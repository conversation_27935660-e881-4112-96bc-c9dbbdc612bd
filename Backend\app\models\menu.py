from pydantic import BaseModel, Field, field_validator
from typing import List, Optional
from datetime import datetime
from enum import Enum
from app.constants.allergens import VALID_ALLERGENS

class MenuCategory(str, Enum):
    APPETIZER = "appetizer"
    MAIN_COURSE = "main_course"
    DESSERT = "dessert"
    BEVERAGE = "beverage"
    SPECIAL = "special"
    SIDE = "side"

class MenuItemBase(BaseModel):
    name: str
    price: float
    category: MenuCategory
    description: Optional[str] = None
    image_url: Optional[str] = None
    available: bool = True
    ingredients: Optional[List[str]] = None
    allergens: Optional[List[str]] = None

    @field_validator('allergens')
    @classmethod
    def validate_allergens(cls, v):
        if v is not None:
            for allergen in v:
                if allergen not in VALID_ALLERGENS:
                    raise ValueError(f'Invalid allergen: {allergen}. Must be one of: {", ".join(VALID_ALLERGENS)}')
        return v

class MenuItemCreate(MenuItemBase):
    restaurant_id: str

class MenuItemUpdate(BaseModel):
    name: Optional[str] = None
    price: Optional[float] = None
    category: Optional[MenuCategory] = None
    description: Optional[str] = None
    image_url: Optional[str] = None
    available: Optional[bool] = None
    ingredients: Optional[List[str]] = None
    allergens: Optional[List[str]] = None

    @field_validator('allergens')
    @classmethod
    def validate_allergens(cls, v):
        if v is not None:
            for allergen in v:
                if allergen not in VALID_ALLERGENS:
                    raise ValueError(f'Invalid allergen: {allergen}. Must be one of: {", ".join(VALID_ALLERGENS)}')
        return v

class MenuItem(MenuItemBase):
    id: str
    restaurant_id: str
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)

class MenuItemResponse(BaseModel):
    success: bool
    message: str
    menu_item: Optional[MenuItem] = None
