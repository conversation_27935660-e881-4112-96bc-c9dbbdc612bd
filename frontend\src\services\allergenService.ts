// API base URL - using Vite environment variables and our consolidated FastAPI backend
// In development, use proxy paths; in production, use full URLs
const API_BASE_URL = import.meta.env.DEV
  ? '' // Use proxy in development (Vite will proxy to backend)
  : (import.meta.env.VITE_API_URL || 'http://localhost:5001');

// TypeScript interfaces for API responses
export interface AllergenData {
  allergens: string[];
  descriptions: Record<string, string>;
}

export interface MenuItem {
  id: string;
  name: string;
  price: number;
  category: string;
  description?: string;
  image_url?: string;
  available: boolean;
  ingredients?: string[];
  allergens?: string[];
  created_at: string;
  updated_at: string;
}

export interface InventoryItem {
  id: string;
  name: string;
  quantity: number;
  unit: string;
  reorder_level: number;
  price_per_unit: number;
  category?: string;
  supplier?: string;
  allergens?: string[];
  last_restocked?: string;
  created_at: string;
  updated_at: string;
}

export interface ApiError {
  detail: string;
  status_code?: number;
}

class AllergenService {
  private async fetchWithErrorHandling<T>(url: string): Promise<T> {
    try {
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          // Add authentication headers if needed
          // 'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorData: ApiError = await response.json().catch(() => ({
          detail: `HTTP ${response.status}: ${response.statusText}`,
          status_code: response.status
        }));
        throw new Error(errorData.detail || `Request failed with status ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('An unexpected error occurred');
    }
  }

  /**
   * Fetch valid allergens and their descriptions from the menu endpoint
   */
  async getValidAllergens(): Promise<AllergenData> {
    return this.fetchWithErrorHandling<AllergenData>(`${API_BASE_URL}/api/menu/allergens`);
  }

  /**
   * Fetch valid allergens from the inventory endpoint
   */
  async getInventoryAllergens(): Promise<AllergenData> {
    return this.fetchWithErrorHandling<AllergenData>(`${API_BASE_URL}/api/inventory/allergens`);
  }

  /**
   * Filter menu items by allergens
   * @param allergens Array of allergen names to filter by
   * @param category Optional category filter
   * @param available Optional availability filter
   */
  async filterMenuByAllergens(
    allergens: string[],
    category?: string,
    available?: boolean
  ): Promise<MenuItem[]> {
    const params = new URLSearchParams();
    
    if (allergens.length > 0) {
      params.append('allergens', allergens.join(','));
    }
    if (category) {
      params.append('category', category);
    }
    if (available !== undefined) {
      params.append('available', available.toString());
    }

    const url = `${API_BASE_URL}/api/menu${params.toString() ? `?${params.toString()}` : ''}`;
    return this.fetchWithErrorHandling<MenuItem[]>(url);
  }

  /**
   * Filter inventory items by allergens
   * @param allergens Array of allergen names to filter by
   * @param category Optional category filter
   */
  async filterInventoryByAllergens(
    allergens: string[],
    category?: string
  ): Promise<InventoryItem[]> {
    const params = new URLSearchParams();
    
    if (allergens.length > 0) {
      params.append('allergens', allergens.join(','));
    }
    if (category) {
      params.append('category', category);
    }

    const url = `${API_BASE_URL}/api/inventory${params.toString() ? `?${params.toString()}` : ''}`;
    return this.fetchWithErrorHandling<InventoryItem[]>(url);
  }

  /**
   * Get all menu items
   */
  async getAllMenuItems(): Promise<MenuItem[]> {
    return this.fetchWithErrorHandling<MenuItem[]>(`${API_BASE_URL}/api/menu`);
  }

  /**
   * Get all inventory items
   */
  async getAllInventoryItems(): Promise<InventoryItem[]> {
    return this.fetchWithErrorHandling<InventoryItem[]>(`${API_BASE_URL}/api/inventory`);
  }

  /**
   * Validate allergens against the backend's valid allergen list
   * @param allergens Array of allergen names to validate
   */
  async validateAllergens(allergens: string[]): Promise<{ valid: boolean; invalidAllergens: string[] }> {
    try {
      const { allergens: validAllergens } = await this.getValidAllergens();
      const invalidAllergens = allergens.filter(
        allergen => !validAllergens.includes(allergen.toLowerCase())
      );
      
      return {
        valid: invalidAllergens.length === 0,
        invalidAllergens
      };
    } catch (error) {
      console.error('Failed to validate allergens:', error);
      return { valid: false, invalidAllergens: allergens };
    }
  }

  /**
   * Search menu items by name and optionally filter by allergens
   * @param searchTerm Search term for item names
   * @param allergens Optional allergen filter
   */
  async searchMenuItems(searchTerm: string, allergens?: string[]): Promise<MenuItem[]> {
    try {
      const allItems = await this.getAllMenuItems();
      let filteredItems = allItems.filter(item =>
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (item.description && item.description.toLowerCase().includes(searchTerm.toLowerCase()))
      );

      if (allergens && allergens.length > 0) {
        filteredItems = filteredItems.filter(item =>
          item.allergens && allergens.some(allergen =>
            item.allergens!.includes(allergen)
          )
        );
      }

      return filteredItems;
    } catch (error) {
      console.error('Failed to search menu items:', error);
      throw error;
    }
  }

  /**
   * Search inventory items by name and optionally filter by allergens
   * @param searchTerm Search term for item names
   * @param allergens Optional allergen filter
   */
  async searchInventoryItems(searchTerm: string, allergens?: string[]): Promise<InventoryItem[]> {
    try {
      const allItems = await this.getAllInventoryItems();
      let filteredItems = allItems.filter(item =>
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (item.category && item.category.toLowerCase().includes(searchTerm.toLowerCase()))
      );

      if (allergens && allergens.length > 0) {
        filteredItems = filteredItems.filter(item =>
          item.allergens && allergens.some(allergen =>
            item.allergens!.includes(allergen)
          )
        );
      }

      return filteredItems;
    } catch (error) {
      console.error('Failed to search inventory items:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const allergenService = new AllergenService();
