from pydantic import BaseModel, Field, field_validator
from typing import List, Optional, Dict, Any
from datetime import datetime, date
from enum import Enum

class DiscountType(str, Enum):
    PERCENTAGE = "percentage"
    FIXED_AMOUNT = "fixed_amount"
    BUY_X_GET_Y = "buy_x_get_y"
    FREE_ITEM = "free_item"

class DiscountScope(str, Enum):
    ORDER_TOTAL = "order_total"
    SPECIFIC_ITEMS = "specific_items"
    CATEGORY = "category"
    MINIMUM_SPEND = "minimum_spend"

class DiscountStatus(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    EXPIRED = "expired"
    SCHEDULED = "scheduled"

class PromoCodeBase(BaseModel):
    code: str
    name: str
    description: Optional[str] = None
    discount_type: DiscountType
    discount_value: float  # percentage (0-100) or fixed amount
    scope: DiscountScope
    minimum_spend: Optional[float] = None
    maximum_discount: Optional[float] = None
    applicable_items: Optional[List[str]] = None  # menu item IDs
    applicable_categories: Optional[List[str]] = None
    start_date: datetime
    end_date: datetime
    usage_limit: Optional[int] = None  # total usage limit
    usage_limit_per_customer: Optional[int] = None
    is_stackable: bool = False
    is_active: bool = True

    @field_validator('discount_value')
    @classmethod
    def validate_discount_value(cls, v, info):
        if info.data.get('discount_type') == DiscountType.PERCENTAGE:
            if v < 0 or v > 100:
                raise ValueError('Percentage discount must be between 0 and 100')
        elif v < 0:
            raise ValueError('Discount value must be positive')
        return v

    @field_validator('code')
    @classmethod
    def validate_code(cls, v):
        if len(v) < 3:
            raise ValueError('Promo code must be at least 3 characters long')
        return v.upper()

class PromoCodeCreate(PromoCodeBase):
    pass

class PromoCodeUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    discount_type: Optional[DiscountType] = None
    discount_value: Optional[float] = None
    scope: Optional[DiscountScope] = None
    minimum_spend: Optional[float] = None
    maximum_discount: Optional[float] = None
    applicable_items: Optional[List[str]] = None
    applicable_categories: Optional[List[str]] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    usage_limit: Optional[int] = None
    usage_limit_per_customer: Optional[int] = None
    is_stackable: Optional[bool] = None
    is_active: Optional[bool] = None

class PromoCode(PromoCodeBase):
    id: str
    usage_count: int = 0
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)

class DiscountApplication(BaseModel):
    promo_code_id: str
    promo_code: str
    discount_amount: float
    applied_to_items: Optional[List[str]] = None
    applied_at: datetime = Field(default_factory=datetime.now)

class PromoCodeUsage(BaseModel):
    id: str
    promo_code_id: str
    order_id: str
    customer_id: Optional[str] = None
    discount_amount: float
    used_at: datetime = Field(default_factory=datetime.now)

# Request models for API endpoints
class PromoCodeValidationRequest(BaseModel):
    code: str
    order_total: float
    order_items: List[dict]
    customer_id: Optional[str] = None

class DiscountApplicationRequest(BaseModel):
    order_id: str
    promo_code: str
    customer_id: Optional[str] = None

class DiscountRemovalRequest(BaseModel):
    order_id: str
    promo_code: str

class DiscountValidationResult(BaseModel):
    is_valid: bool
    error_message: Optional[str] = None
    discount_amount: float = 0.0
    applicable_items: Optional[List[str]] = None

class CampaignBase(BaseModel):
    name: str
    description: Optional[str] = None
    campaign_type: str = "promotion"  # promotion, loyalty, seasonal
    start_date: datetime
    end_date: datetime
    target_audience: Optional[str] = None
    budget: Optional[float] = None
    promo_codes: Optional[List[str]] = None  # list of promo code IDs
    is_active: bool = True

class CampaignCreate(CampaignBase):
    pass

class Campaign(CampaignBase):
    id: str
    total_usage: int = 0
    total_discount_given: float = 0.0
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
