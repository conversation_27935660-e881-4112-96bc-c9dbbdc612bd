import logger from "@/utils/logger";
import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { toast } from "@/components/ui/sonner";
import { fetchCSV } from "@/utils/csvParser";

// Define restaurant types
export interface Restaurant {
  id: string;
  code: string;
  name: string;
  logo?: string;
  address: string;
  phone: string;
  email: string;
  vatRate: number;
  currency: string;
  isActive: boolean;
}

// Define context type
interface RestaurantContextType {
  currentRestaurant: Restaurant | null;
  isRestaurantAuthenticated: boolean;
  authenticateRestaurant: (id: string, rememberRestaurant: boolean) => Promise<boolean>;
  logoutRestaurant: () => void;
  availableRestaurants: Restaurant[];
}

// Mock restaurant data
const MOCK_RESTAURANTS: Restaurant[] = [
  {
    id: "1",
    code: "GK001",
    name: "The Gourmet Kitchen",
    logo: "/logos/gourmet-kitchen.png",
    address: "123 Main Street, Anytown, UK",
    phone: "(*************",
    email: "<EMAIL>",
    vatRate: 20,
    currency: "GBP",
    isActive: true
  },
  {
    id: "2",
    code: "PP002",
    name: "Pasta Paradise",
    logo: "/logos/pasta-paradise.png",
    address: "456 Elm Avenue, Somewhere, UK",
    phone: "(*************",
    email: "<EMAIL>",
    vatRate: 20,
    currency: "GBP",
    isActive: true
  },
  {
    id: "3",
    code: "SS003",
    name: "Sushi Sensation",
    logo: "/logos/sushi-sensation.png",
    address: "789 Oak Boulevard, Elsewhere, UK",
    phone: "(*************",
    email: "<EMAIL>",
    vatRate: 20,
    currency: "GBP",
    isActive: true
  }
];

// Create context with default values
const RestaurantContext = createContext<RestaurantContextType>({
  currentRestaurant: null,
  isRestaurantAuthenticated: false,
  authenticateRestaurant: async () => false,
  logoutRestaurant: () => {},
  availableRestaurants: []
});

interface RestaurantProviderProps {
  children: ReactNode;
}

export const RestaurantProvider = ({ children }: RestaurantProviderProps) => {
  const [currentRestaurant, setCurrentRestaurant] = useState<Restaurant | null>(null);
  const [isRestaurantAuthenticated, setIsRestaurantAuthenticated] = useState<boolean>(false);
  const [availableRestaurants, setAvailableRestaurants] = useState<Restaurant[]>([]);

  // Initialize restaurant data from CSV file, localStorage, or use mock data
  useEffect(() => {
    const loadRestaurantData = async () => {
      try {
        // Load restaurants from CSV
        const restaurants = await fetchCSV<Restaurant>('/data/restaurants.csv');
        setAvailableRestaurants(restaurants);

        // Check for existing restaurant session
        const storedRestaurantId = localStorage.getItem('restaurantId');
        const rememberRestaurant = localStorage.getItem('rememberRestaurant') === 'true';

        if (storedRestaurantId && rememberRestaurant) {
          authenticateRestaurant(storedRestaurantId, true);
        }
      } catch (error) {
        logger.logError(error, 'restaurant data load', 'RestaurantContext');
        // Fallback to mock data
        setAvailableRestaurants(MOCK_RESTAURANTS);
        toast.error('Failed to load restaurant data');
      }
    };

    loadRestaurantData();
  }, []);

  // Authenticate restaurant
  const authenticateRestaurant = async (id: string, rememberRestaurant: boolean): Promise<boolean> => {
    try {
      // Find restaurant with matching ID
      const foundRestaurant = availableRestaurants.find(restaurant => restaurant.id === id);

      if (!foundRestaurant) {
        toast.error("Restaurant not found");
        return false;
      }

      if (!foundRestaurant.isActive) {
        toast.error("Restaurant account is inactive");
        return false;
      }

      // Update state
      setCurrentRestaurant(foundRestaurant);
      setIsRestaurantAuthenticated(true);

      // Store in localStorage if remember is checked
      if (rememberRestaurant) {
        localStorage.setItem('restaurantId', foundRestaurant.id);
        localStorage.setItem('rememberRestaurant', 'true');
      } else {
        // Use session storage for temporary storage
        sessionStorage.setItem('restaurantId', foundRestaurant.id);
        localStorage.removeItem('restaurantId');
        localStorage.removeItem('rememberRestaurant');
      }

      toast.success(`Welcome to ${foundRestaurant.name}`);
      return true;
    } catch (error) {
      toast.error("Failed to authenticate restaurant");
      return false;
    }
  };

  // Logout restaurant
  const logoutRestaurant = () => {
    setCurrentRestaurant(null);
    setIsRestaurantAuthenticated(false);
    localStorage.removeItem('restaurantId');
    localStorage.removeItem('rememberRestaurant');
    sessionStorage.removeItem('restaurantId');
    toast.info("Logged out successfully");
  };

  return (
    <RestaurantContext.Provider
      value={{
        currentRestaurant,
        isRestaurantAuthenticated,
        authenticateRestaurant,
        logoutRestaurant,
        availableRestaurants
      }}
    >
      {children}
    </RestaurantContext.Provider>
  );
};

// Custom hook for using restaurant context
export const useRestaurant = () => useContext(RestaurantContext);
