#!/usr/bin/env python3
"""
Simple database test script
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.getcwd())

def test_step_by_step():
    """Test importing database modules step by step"""
    print("Testing database imports step by step...")
    
    try:
        print("Step 1: Testing database module...")
        from Database.database import engine, SessionLocal, Base, test_connection
        print("✅ Database module imported successfully")
        
        print("Step 2: Testing connection...")
        result = test_connection()
        print(f"✅ Connection test: {result}")
        
        print("Step 3: Testing base models...")
        from Database.models.base import BaseModel, TimestampMixin
        print("✅ Base models imported successfully")
        
        print("Step 4: Testing restaurant models...")
        from Database.models.restaurants import Restaurant
        print("✅ Restaurant model imported successfully")
        
        print("Step 5: Creating tables...")
        Base.metadata.create_all(bind=engine)
        print("✅ Tables created successfully")
        
        print("Step 6: Checking created tables...")
        from sqlalchemy import inspect
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        print(f"✅ Created {len(tables)} tables: {tables}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_step_by_step()
    if success:
        print("\n🎉 Database test completed successfully!")
    else:
        print("\n💥 Database test failed!")
        sys.exit(1)
