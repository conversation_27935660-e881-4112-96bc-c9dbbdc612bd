/**
 * React Query integration hooks for RestroManage store
 * Provides efficient data fetching, caching, and synchronization with Zustand store
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { analyticsApi } from '@/services/api';
import { orderApi, userApi, restaurantApi } from '@/services/apiService';
import { notificationApi } from '@/services/notificationService';
import { useRestroManageStore } from '@/store';
import logger from '@/utils/logger';
import { NotificationType, NotificationPriority } from '@/types/notification';

// Dashboard Queries
export const useDashboardData = () => {
  const { currentRestaurant } = useAuth();
  const restaurantId = currentRestaurant?.id;

  return useQuery({
    queryKey: ['dashboard', restaurantId],
    queryFn: async () => {
      if (!restaurantId) throw new Error('No restaurant ID');
      
      const [dashboardData, recentOrders] = await Promise.all([
        analyticsApi.getDashboardData(restaurantId),
        orderApi.getOrders(restaurantId, 'pending,preparing,ready'),
      ]);

      return {
        metrics: {
          todayRevenue: dashboardData.todayRevenue || 0,
          todayCustomers: dashboardData.todayCustomers || 0,
          inventoryItems: dashboardData.inventoryItems || 0,
          staffOnDuty: dashboardData.staffOnDuty || 0,
          ordersToday: dashboardData.ordersToday || 0,
          averageOrderValue: dashboardData.averageOrderValue || 0,
          customerSatisfaction: dashboardData.customerSatisfaction || 0,
          tableOccupancy: dashboardData.tableOccupancy || 0,
        },
        recentOrders: recentOrders.slice(0, 10),
        quickStats: [
          {
            id: 'revenue',
            label: 'Today\'s Revenue',
            value: `£${dashboardData.todayRevenue?.toFixed(2) || '0.00'}`,
            change: dashboardData.revenueChange || 0,
            trend: (dashboardData.revenueChange || 0) > 0 ? 'up' : (dashboardData.revenueChange || 0) < 0 ? 'down' : 'stable',
            icon: 'DollarSign',
          },
          {
            id: 'orders',
            label: 'Orders Today',
            value: dashboardData.ordersToday || 0,
            change: dashboardData.ordersChange || 0,
            trend: (dashboardData.ordersChange || 0) > 0 ? 'up' : (dashboardData.ordersChange || 0) < 0 ? 'down' : 'stable',
            icon: 'ShoppingBag',
          },
          {
            id: 'customers',
            label: 'Customers Served',
            value: dashboardData.todayCustomers || 0,
            change: dashboardData.customersChange || 0,
            trend: (dashboardData.customersChange || 0) > 0 ? 'up' : (dashboardData.customersChange || 0) < 0 ? 'down' : 'stable',
            icon: 'Users',
          },
          {
            id: 'staff',
            label: 'Staff On Duty',
            value: dashboardData.staffOnDuty || 0,
            change: 0,
            trend: 'stable',
            icon: 'UserCheck',
          },
        ],
      };
    },
    enabled: !!restaurantId,
    staleTime: 30000, // 30 seconds
    refetchInterval: 60000, // 1 minute
  });
};

// Analytics Queries
export const useAnalyticsData = (dateRange: { from: Date | null; to: Date | null }, period: string = 'daily') => {
  const { currentRestaurant } = useAuth();
  const restaurantId = currentRestaurant?.id;

  return useQuery({
    queryKey: ['analytics', restaurantId, dateRange, period],
    queryFn: async () => {
      if (!restaurantId || !dateRange.from || !dateRange.to) {
        throw new Error('Missing required parameters');
      }

      const dateFrom = dateRange.from.toISOString().split('T')[0];
      const dateTo = dateRange.to.toISOString().split('T')[0];

      const [
        salesData,
        revenueData,
        popularItems,
        categoryPerformance,
        peakHoursData,
        profitMarginData,
        forecastData,
      ] = await Promise.allSettled([
        analyticsApi.getSalesData(dateFrom, dateTo, restaurantId),
        analyticsApi.getRevenueData(period as 'weekly' | 'monthly', restaurantId),
        analyticsApi.getPopularItems(10, restaurantId),
        analyticsApi.getCategoryPerformance(restaurantId, 30),
        analyticsApi.getPeakHoursAnalysis(restaurantId, 30),
        analyticsApi.getProfitMarginAnalysis(restaurantId, 30),
        analyticsApi.getForecastData(restaurantId),
      ]);

      return {
        salesData: salesData.status === 'fulfilled' ? salesData.value : [],
        revenueData: revenueData.status === 'fulfilled' ? revenueData.value : [],
        popularItems: popularItems.status === 'fulfilled' ? popularItems.value : [],
        categoryPerformance: categoryPerformance.status === 'fulfilled' ? categoryPerformance.value : [],
        peakHoursData: peakHoursData.status === 'fulfilled' ? peakHoursData.value : [],
        profitMarginData: profitMarginData.status === 'fulfilled' ? profitMarginData.value : [],
        forecastData: forecastData.status === 'fulfilled' ? forecastData.value : [],
      };
    },
    enabled: !!restaurantId && !!dateRange.from && !!dateRange.to,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Staff Queries
export const useStaffData = () => {
  const { currentRestaurant } = useAuth();
  const restaurantId = currentRestaurant?.id;

  return useQuery({
    queryKey: ['staff', restaurantId],
    queryFn: async () => {
      if (!restaurantId) throw new Error('No restaurant ID');
      
      const staffData = await userApi.getUsers(restaurantId);
      
      return staffData.map((user: any) => ({
        id: user.id,
        name: user.name,
        email: user.email,
        phone: user.phone || '',
        role: user.role,
        position: user.position || user.role,
        status: user.status || 'active',
        hireDate: user.hireDate || new Date().toISOString().split('T')[0],
        performance: user.performance || 100,
        metrics: {
          sales: user.metrics?.sales || 0,
          tablesTurned: user.metrics?.tablesTurned || 0,
          customerRating: user.metrics?.customerRating || 5.0,
          ordersProcessed: user.metrics?.ordersProcessed || 0,
          averageServiceTime: user.metrics?.averageServiceTime || 0,
        },
        schedule: {
          id: `schedule_${user.id}`,
          staffId: user.id,
          shifts: [],
          availableDays: user.availableDays || ['mon', 'tue', 'wed', 'thu', 'fri'],
          assignedHours: user.assignedHours || 40,
          overtimeHours: 0,
        },
        permissions: [],
        pin: user.pin,
      }));
    },
    enabled: !!restaurantId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Orders Queries
export const useOrdersData = (status?: string) => {
  const { currentRestaurant } = useAuth();
  const restaurantId = currentRestaurant?.id;

  return useQuery({
    queryKey: ['orders', restaurantId, status],
    queryFn: async () => {
      if (!restaurantId) throw new Error('No restaurant ID');
      
      const orders = await orderApi.getOrders(restaurantId, status);
      
      return orders.map((order: any) => ({
        id: order.id,
        tableId: order.table_id,
        customerId: order.customer_id,
        items: order.items?.map((item: any) => ({
          id: item.id,
          menuItemId: item.menu_item_id,
          name: item.name,
          quantity: item.quantity,
          price: item.price,
          modifications: item.modifications || [],
          notes: item.notes,
        })) || [],
        status: order.status,
        totalAmount: order.total_amount,
        tax: order.tax || 0,
        discount: order.discount || 0,
        paymentMethod: order.payment_method,
        paymentStatus: order.payment_status || 'pending',
        timestamp: new Date(order.created_at),
        estimatedTime: order.estimated_time,
        notes: order.notes,
        staffId: order.staff_id,
      }));
    },
    enabled: !!restaurantId,
    staleTime: 30000, // 30 seconds
    refetchInterval: 60000, // 1 minute for active orders
  });
};

// Settings Queries
export const useRestaurantSettings = () => {
  const { currentRestaurant } = useAuth();
  const restaurantId = currentRestaurant?.id;

  return useQuery({
    queryKey: ['restaurant-settings', restaurantId],
    queryFn: async () => {
      if (!restaurantId) throw new Error('No restaurant ID');
      
      const restaurantData = await restaurantApi.getRestaurant(restaurantId);
      
      return {
        id: restaurantData.id,
        name: restaurantData.name,
        logo: restaurantData.logo,
        address: restaurantData.address,
        phone: restaurantData.phone,
        email: restaurantData.email,
        vatRate: restaurantData.vatRate || 20,
        currency: restaurantData.currency || 'GBP',
        timezone: restaurantData.timezone || 'Europe/London',
        operatingHours: restaurantData.operatingHours || {},
        tableConfiguration: restaurantData.tableConfiguration || [],
        paymentMethods: restaurantData.paymentMethods || [],
        integrations: restaurantData.integrations || [],
      };
    },
    enabled: !!restaurantId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Notification Queries
export const useNotificationsData = (filters?: {
  type?: NotificationType;
  priority?: NotificationPriority;
  is_read?: boolean;
  limit?: number;
}) => {
  const { currentRestaurant } = useAuth();
  const restaurantId = currentRestaurant?.id;

  return useQuery({
    queryKey: ['notifications', restaurantId, filters],
    queryFn: async () => {
      if (!restaurantId) throw new Error('No restaurant ID');

      const notifications = await notificationApi.getNotifications(restaurantId, filters);

      logger.debug(
        `Retrieved ${notifications.length} notifications for restaurant ${restaurantId}`,
        'useNotificationsData',
        { restaurantId, count: notifications.length, filters }
      );

      return notifications;
    },
    enabled: !!restaurantId,
    staleTime: 1 * 60 * 1000, // 1 minute
    refetchInterval: 2 * 60 * 1000, // Refetch every 2 minutes for real-time updates
  });
};

export const useNotificationStats = () => {
  const { currentRestaurant } = useAuth();
  const restaurantId = currentRestaurant?.id;

  return useQuery({
    queryKey: ['notification-stats', restaurantId],
    queryFn: async () => {
      if (!restaurantId) throw new Error('No restaurant ID');

      const stats = await notificationApi.getNotificationStats(restaurantId);

      logger.debug(
        `Retrieved notification stats for restaurant ${restaurantId}`,
        'useNotificationStats',
        { restaurantId, stats }
      );

      return stats;
    },
    enabled: !!restaurantId,
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 1 * 60 * 1000, // Refetch every minute
  });
};

export const useUnreadNotifications = () => {
  const { currentRestaurant } = useAuth();
  const restaurantId = currentRestaurant?.id;

  return useQuery({
    queryKey: ['notifications', restaurantId, { is_read: false }],
    queryFn: async () => {
      if (!restaurantId) throw new Error('No restaurant ID');

      const notifications = await notificationApi.getNotifications(restaurantId, {
        is_read: false,
        limit: 50
      });

      logger.debug(
        `Retrieved ${notifications.length} unread notifications for restaurant ${restaurantId}`,
        'useUnreadNotifications',
        { restaurantId, count: notifications.length }
      );

      return notifications;
    },
    enabled: !!restaurantId,
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 1 * 60 * 1000, // Refetch every minute for real-time updates
  });
};

// Mutations
export const useCreateOrder = () => {
  const queryClient = useQueryClient();
  const { currentRestaurant } = useAuth();
  const restaurantId = currentRestaurant?.id;

  return useMutation({
    mutationFn: async (orderData: any) => {
      if (!restaurantId) throw new Error('No restaurant ID');
      
      return await orderApi.createOrder({
        restaurant_id: restaurantId,
        ...orderData,
      });
    },
    onSuccess: () => {
      // Invalidate and refetch orders
      queryClient.invalidateQueries({ queryKey: ['orders', restaurantId] });
      queryClient.invalidateQueries({ queryKey: ['dashboard', restaurantId] });
    },
    onError: (error) => {
      logger.error('Failed to create order', 'useCreateOrder', { error });
    },
  });
};

export const useUpdateOrder = () => {
  const queryClient = useQueryClient();
  const { currentRestaurant } = useAuth();
  const restaurantId = currentRestaurant?.id;

  return useMutation({
    mutationFn: async ({ orderId, updates }: { orderId: string; updates: any }) => {
      return await orderApi.updateOrder(orderId, updates);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['orders', restaurantId] });
      queryClient.invalidateQueries({ queryKey: ['dashboard', restaurantId] });
    },
    onError: (error) => {
      logger.error('Failed to update order', 'useUpdateOrder', { error });
    },
  });
};

export const useCreateStaffMember = () => {
  const queryClient = useQueryClient();
  const { currentRestaurant } = useAuth();
  const restaurantId = currentRestaurant?.id;

  return useMutation({
    mutationFn: async (staffData: any) => {
      if (!restaurantId) throw new Error('No restaurant ID');
      
      return await userApi.createUser({
        restaurant_id: restaurantId,
        ...staffData,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['staff', restaurantId] });
    },
    onError: (error) => {
      logger.error('Failed to create staff member', 'useCreateStaffMember', { error });
    },
  });
};

export const useUpdateRestaurantSettings = () => {
  const queryClient = useQueryClient();
  const { currentRestaurant } = useAuth();
  const restaurantId = currentRestaurant?.id;

  return useMutation({
    mutationFn: async (settings: any) => {
      if (!restaurantId) throw new Error('No restaurant ID');
      
      return await restaurantApi.updateRestaurant(restaurantId, settings);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['restaurant-settings', restaurantId] });
    },
    onError: (error) => {
      logger.error('Failed to update restaurant settings', 'useUpdateRestaurantSettings', { error });
    },
  });
};

// Notification Mutations
export const useCreateNotification = () => {
  const queryClient = useQueryClient();
  const { currentRestaurant } = useAuth();
  const restaurantId = currentRestaurant?.id;

  return useMutation({
    mutationFn: async (notificationData: {
      title: string;
      message: string;
      type?: NotificationType;
      priority?: NotificationPriority;
      link?: string;
      relatedId?: string;
    }) => {
      if (!restaurantId) throw new Error('No restaurant ID');

      return await notificationApi.createNotification(restaurantId, {
        title: notificationData.title,
        message: notificationData.message,
        type: notificationData.type || 'info',
        priority: notificationData.priority || 'medium',
        link: notificationData.link,
        relatedId: notificationData.relatedId,
      });
    },
    onSuccess: () => {
      // Invalidate and refetch notifications
      queryClient.invalidateQueries({ queryKey: ['notifications', restaurantId] });
      queryClient.invalidateQueries({ queryKey: ['notification-stats', restaurantId] });
    },
    onError: (error) => {
      logger.error('Failed to create notification', 'useCreateNotification', { error });
    },
  });
};

export const useMarkNotificationsAsRead = () => {
  const queryClient = useQueryClient();
  const { currentRestaurant } = useAuth();
  const restaurantId = currentRestaurant?.id;

  return useMutation({
    mutationFn: async (notificationIds: string[]) => {
      if (!restaurantId) throw new Error('No restaurant ID');

      return await notificationApi.markAsRead(restaurantId, notificationIds);
    },
    onSuccess: () => {
      // Invalidate and refetch notifications
      queryClient.invalidateQueries({ queryKey: ['notifications', restaurantId] });
      queryClient.invalidateQueries({ queryKey: ['notification-stats', restaurantId] });
    },
    onError: (error) => {
      logger.error('Failed to mark notifications as read', 'useMarkNotificationsAsRead', { error });
    },
  });
};

export const useMarkAllNotificationsAsRead = () => {
  const queryClient = useQueryClient();
  const { currentRestaurant } = useAuth();
  const restaurantId = currentRestaurant?.id;

  return useMutation({
    mutationFn: async () => {
      if (!restaurantId) throw new Error('No restaurant ID');

      return await notificationApi.markAllAsRead(restaurantId);
    },
    onSuccess: () => {
      // Invalidate and refetch notifications
      queryClient.invalidateQueries({ queryKey: ['notifications', restaurantId] });
      queryClient.invalidateQueries({ queryKey: ['notification-stats', restaurantId] });
    },
    onError: (error) => {
      logger.error('Failed to mark all notifications as read', 'useMarkAllNotificationsAsRead', { error });
    },
  });
};

export const useDeleteNotification = () => {
  const queryClient = useQueryClient();
  const { currentRestaurant } = useAuth();
  const restaurantId = currentRestaurant?.id;

  return useMutation({
    mutationFn: async (notificationId: string) => {
      if (!restaurantId) throw new Error('No restaurant ID');

      return await notificationApi.deleteNotification(restaurantId, notificationId);
    },
    onSuccess: () => {
      // Invalidate and refetch notifications
      queryClient.invalidateQueries({ queryKey: ['notifications', restaurantId] });
      queryClient.invalidateQueries({ queryKey: ['notification-stats', restaurantId] });
    },
    onError: (error) => {
      logger.error('Failed to delete notification', 'useDeleteNotification', { error });
    },
  });
};

export const useDeleteAllNotifications = () => {
  const queryClient = useQueryClient();
  const { currentRestaurant } = useAuth();
  const restaurantId = currentRestaurant?.id;

  return useMutation({
    mutationFn: async () => {
      if (!restaurantId) throw new Error('No restaurant ID');

      return await notificationApi.deleteAllNotifications(restaurantId);
    },
    onSuccess: () => {
      // Invalidate and refetch notifications
      queryClient.invalidateQueries({ queryKey: ['notifications', restaurantId] });
      queryClient.invalidateQueries({ queryKey: ['notification-stats', restaurantId] });
    },
    onError: (error) => {
      logger.error('Failed to delete all notifications', 'useDeleteAllNotifications', { error });
    },
  });
};

// Store synchronization hooks
export const useStoreSync = () => {
  const { currentRestaurant } = useAuth();
  const restaurantId = currentRestaurant?.id;
  const queryClient = useQueryClient();

  const syncAllData = async () => {
    if (!restaurantId) return;

    try {
      logger.info('Syncing all store data', 'useStoreSync', { restaurantId });

      await queryClient.invalidateQueries({ queryKey: ['dashboard', restaurantId] });
      await queryClient.invalidateQueries({ queryKey: ['analytics', restaurantId] });
      await queryClient.invalidateQueries({ queryKey: ['staff', restaurantId] });
      await queryClient.invalidateQueries({ queryKey: ['orders', restaurantId] });
      await queryClient.invalidateQueries({ queryKey: ['notifications', restaurantId] });
      await queryClient.invalidateQueries({ queryKey: ['notification-stats', restaurantId] });
      await queryClient.invalidateQueries({ queryKey: ['restaurant-settings', restaurantId] });

      logger.info('Store data sync completed', 'useStoreSync', { restaurantId });
    } catch (error) {
      logger.error('Failed to sync store data', 'useStoreSync', { error, restaurantId });
      throw error;
    }
  };

  return { syncAllData };
};
