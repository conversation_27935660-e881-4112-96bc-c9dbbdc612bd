"""
Enhanced AI Router for RestroManage
Provides intelligent database-driven AI assistant capabilities with function calling,
conversational memory, and restaurant-specific insights.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field
from datetime import datetime

from app.services.enhanced_ai_service import enhanced_ai_service
from app.services.ai_memory_service import ai_memory_service
from app.utils.auth import get_current_active_user, get_current_restaurant_id
from app.utils.logging_config import logger

router = APIRouter(prefix="/ai/enhanced", tags=["Enhanced AI Assistant"])


class IntelligentQueryRequest(BaseModel):
    """Request model for intelligent AI queries"""
    query: str = Field(..., description="Natural language query about restaurant operations")
    session_id: Optional[str] = Field(None, description="Conversation session ID for context")
    include_context: bool = Field(True, description="Whether to include conversation context")
    context: Optional[Dict[str, Any]] = Field(None, description="Additional context for the query")


class IntelligentQueryResponse(BaseModel):
    """Response model for intelligent AI queries"""
    success: bool
    response: str
    ai_enabled: bool
    session_id: str
    function_calls: Optional[List[Dict[str, Any]]] = None
    restaurant_id: str
    timestamp: str
    error: Optional[str] = None


class SessionRequest(BaseModel):
    """Request model for creating a new session"""
    initial_context: Optional[Dict[str, Any]] = Field(None, description="Initial context for the session")


class SessionResponse(BaseModel):
    """Response model for session operations"""
    success: bool
    session_id: str
    message: str
    error: Optional[str] = None


@router.post("/query", response_model=IntelligentQueryResponse)
async def intelligent_query(
    request: IntelligentQueryRequest,
    current_user = Depends(get_current_active_user),
    restaurant_id: str = Depends(get_current_restaurant_id)
):
    """
    Process an intelligent query with function calling and database integration.
    The AI can dynamically query restaurant data to provide informed responses.
    """
    try:
        logger.info("Processing intelligent AI query", "EnhancedAI", {
            "user_id": current_user.id,
            "restaurant_id": restaurant_id,
            "query_length": len(request.query),
            "has_session": bool(request.session_id)
        })
        
        # Get or create session
        session_id = request.session_id
        if not session_id:
            session_id = await ai_memory_service.create_session(
                restaurant_id=restaurant_id,
                user_id=current_user.id,
                initial_context=request.context
            )
        
        # Add user message to conversation history
        await ai_memory_service.add_message(
            session_id=session_id,
            role="user",
            content=request.query,
            metadata={"user_id": current_user.id}
        )
        
        # Get conversation context if requested
        conversation_context = []
        if request.include_context and await ai_memory_service.is_enabled():
            conversation_context = await ai_memory_service.get_conversation_context(session_id)
        
        # Process the query with enhanced AI
        ai_response = await enhanced_ai_service.process_intelligent_query(
            query=request.query,
            restaurant_id=restaurant_id,
            context={
                "conversation_history": conversation_context,
                "user_context": request.context or {},
                "user_id": current_user.id
            }
        )
        
        # Add AI response to conversation history
        await ai_memory_service.add_message(
            session_id=session_id,
            role="assistant",
            content=ai_response.get("response", ""),
            function_calls=ai_response.get("function_calls"),
            metadata={"ai_model": "enhanced_ai_service"}
        )
        
        return IntelligentQueryResponse(
            success=ai_response.get("success", False),
            response=ai_response.get("response", ""),
            ai_enabled=ai_response.get("ai_enabled", False),
            session_id=session_id,
            function_calls=ai_response.get("function_calls"),
            restaurant_id=restaurant_id,
            timestamp=ai_response.get("timestamp", datetime.now().isoformat()),
            error=ai_response.get("error")
        )
        
    except Exception as e:
        logger.error("Error processing intelligent query", "EnhancedAI", {
            "user_id": current_user.id,
            "restaurant_id": restaurant_id,
            "error": str(e)
        })
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process intelligent query: {str(e)}"
        )


@router.post("/session", response_model=SessionResponse)
async def create_session(
    request: SessionRequest,
    current_user = Depends(get_current_active_user),
    restaurant_id: str = Depends(get_current_restaurant_id)
):
    """Create a new conversation session for the AI assistant."""
    try:
        session_id = await ai_memory_service.create_session(
            restaurant_id=restaurant_id,
            user_id=current_user.id,
            initial_context=request.initial_context
        )
        
        return SessionResponse(
            success=True,
            session_id=session_id,
            message="Session created successfully"
        )
        
    except Exception as e:
        logger.error("Error creating AI session", "EnhancedAI", {
            "user_id": current_user.id,
            "restaurant_id": restaurant_id,
            "error": str(e)
        })
        
        return SessionResponse(
            success=False,
            session_id="",
            message="Failed to create session",
            error=str(e)
        )


@router.get("/session/{session_id}")
async def get_session(
    session_id: str,
    current_user = Depends(get_current_active_user),
    restaurant_id: str = Depends(get_current_restaurant_id)
):
    """Get conversation session details."""
    try:
        session = await ai_memory_service.get_session(session_id)
        
        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Session not found"
            )
        
        # Verify session belongs to current restaurant
        if session.restaurant_id != restaurant_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied to this session"
            )
        
        return {
            "success": True,
            "session": {
                "session_id": session.session_id,
                "restaurant_id": session.restaurant_id,
                "user_id": session.user_id,
                "created_at": session.created_at.isoformat(),
                "last_activity": session.last_activity.isoformat(),
                "message_count": len(session.messages),
                "context": session.context
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error getting AI session", "EnhancedAI", {
            "session_id": session_id,
            "user_id": current_user.id,
            "error": str(e)
        })
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get session: {str(e)}"
        )


@router.delete("/session/{session_id}")
async def delete_session(
    session_id: str,
    current_user = Depends(get_current_active_user),
    restaurant_id: str = Depends(get_current_restaurant_id)
):
    """Delete a conversation session."""
    try:
        # Verify session exists and belongs to current restaurant
        session = await ai_memory_service.get_session(session_id)
        
        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Session not found"
            )
        
        if session.restaurant_id != restaurant_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied to this session"
            )
        
        # Delete session
        success = await ai_memory_service.delete_session(session_id)
        
        return {
            "success": success,
            "message": "Session deleted successfully" if success else "Failed to delete session"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error deleting AI session", "EnhancedAI", {
            "session_id": session_id,
            "user_id": current_user.id,
            "error": str(e)
        })
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete session: {str(e)}"
        )


@router.get("/summary")
async def get_restaurant_summary(
    current_user = Depends(get_current_active_user),
    restaurant_id: str = Depends(get_current_restaurant_id)
):
    """Get AI-generated comprehensive restaurant summary."""
    try:
        summary = await enhanced_ai_service.get_restaurant_summary(restaurant_id)
        
        return {
            "success": summary.get("success", False),
            "summary": summary.get("summary", ""),
            "data": summary.get("data", {}),
            "restaurant_id": restaurant_id,
            "timestamp": summary.get("timestamp", datetime.now().isoformat()),
            "error": summary.get("error")
        }
        
    except Exception as e:
        logger.error("Error getting restaurant summary", "EnhancedAI", {
            "user_id": current_user.id,
            "restaurant_id": restaurant_id,
            "error": str(e)
        })
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get restaurant summary: {str(e)}"
        )


@router.get("/status")
async def get_enhanced_ai_status():
    """Get enhanced AI service status and capabilities."""
    try:
        enhanced_ai_enabled = enhanced_ai_service.is_enabled()
        memory_service_enabled = await ai_memory_service.is_enabled()

        return {
            "enhanced_ai_enabled": enhanced_ai_enabled,
            "memory_service_enabled": memory_service_enabled,
            "capabilities": [
                "intelligent_queries",
                "function_calling",
                "database_integration",
                "conversational_memory",
                "restaurant_summaries"
            ] if enhanced_ai_enabled else [],
            "memory_stats": await ai_memory_service.get_memory_stats(),
            "message": "Enhanced AI assistant is ready" if enhanced_ai_enabled else "Enhanced AI assistant is disabled"
        }
        
    except Exception as e:
        logger.error("Error getting enhanced AI status", "EnhancedAI", {
            "error": str(e)
        })
        
        return {
            "enhanced_ai_enabled": False,
            "memory_service_enabled": False,
            "capabilities": [],
            "error": str(e),
            "message": "Error checking AI status"
        }
