import { 
  <PERSON>, 
  <PERSON><PERSON>onte<PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON>Title 
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";

interface TableStatus {
  id: string;
  number: string;
  capacity: number;
  status: "available" | "occupied" | "reserved" | "cleaning";
  occupiedSince?: string;
  waitTime?: string;
  reservation?: {
    time: string;
    name: string;
    guests: number;
  };
}

interface TableStatusCardProps {
  tables: TableStatus[];
}

const TableStatusCard = ({ tables }: TableStatusCardProps) => {
  // Calculate statistics
  const totalTables = tables.length;
  const occupiedTables = tables.filter(t => t.status === "occupied").length;
  const reservedTables = tables.filter(t => t.status === "reserved").length;
  const availableTables = tables.filter(t => t.status === "available").length;
  
  const occupancyRate = Math.round((occupiedTables / totalTables) * 100);
  
  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "available":
        return "bg-green-100 text-green-800";
      case "occupied":
        return "bg-blue-100 text-blue-800";
      case "reserved":
        return "bg-yellow-100 text-yellow-800";
      case "cleaning":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };
  
  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg font-medium">Table Status</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="mb-4">
          <div className="flex justify-between mb-1">
            <span className="text-sm font-medium">Occupancy Rate</span>
            <span className="text-sm font-medium">{occupancyRate}%</span>
          </div>
          <Progress value={occupancyRate} className="h-2" />
        </div>
        
        <div className="grid grid-cols-4 gap-2 mb-4 text-center">
          <div>
            <div className="text-2xl font-bold">{totalTables}</div>
            <div className="text-xs text-muted-foreground">Total</div>
          </div>
          <div>
            <div className="text-2xl font-bold">{occupiedTables}</div>
            <div className="text-xs text-muted-foreground">Occupied</div>
          </div>
          <div>
            <div className="text-2xl font-bold">{reservedTables}</div>
            <div className="text-xs text-muted-foreground">Reserved</div>
          </div>
          <div>
            <div className="text-2xl font-bold">{availableTables}</div>
            <div className="text-xs text-muted-foreground">Available</div>
          </div>
        </div>
        
        <div className="space-y-3 max-h-[220px] overflow-auto pr-1">
          {tables.map((table) => (
            <div key={table.id} className="flex items-center justify-between p-2 bg-muted rounded-md">
              <div>
                <div className="font-medium">{table.number}</div>
                <div className="text-xs text-muted-foreground">{table.capacity} seats</div>
              </div>
              <div className="text-right">
                <Badge className={getStatusColor(table.status)}>
                  {table.status.charAt(0).toUpperCase() + table.status.slice(1)}
                </Badge>
                {table.status === "occupied" && table.waitTime && (
                  <div className="text-xs mt-1">{table.waitTime} wait</div>
                )}
                {table.status === "reserved" && table.reservation && (
                  <div className="text-xs mt-1">{table.reservation.time}</div>
                )}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default TableStatusCard;
