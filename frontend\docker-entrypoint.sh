#!/bin/sh

# Docker entrypoint script for React frontend
# This script allows runtime environment variable injection

# Default values
REACT_APP_API_URL=${REACT_APP_API_URL:-http://localhost:5001}
REACT_APP_ENVIRONMENT=${REACT_APP_ENVIRONMENT:-production}

# Create runtime config file
cat > /usr/share/nginx/html/config.js << EOF
window.ENV = {
  REACT_APP_API_URL: '${REACT_APP_API_URL}',
  REACT_APP_ENVIRONMENT: '${REACT_APP_ENVIRONMENT}'
};
EOF

# Execute the main command
exec "$@"
