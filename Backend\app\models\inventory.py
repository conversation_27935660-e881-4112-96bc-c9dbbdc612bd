from pydantic import BaseModel, Field, field_validator
from typing import List, Optional
from datetime import datetime
from app.constants.allergens import VALID_ALLERGENS

class InventoryItemBase(BaseModel):
    name: str
    quantity: float
    unit: str
    reorder_level: float
    price_per_unit: float
    category: Optional[str] = None
    supplier: Optional[str] = None
    allergens: Optional[List[str]] = None

    @field_validator('allergens')
    @classmethod
    def validate_allergens(cls, v):
        if v is not None:
            for allergen in v:
                if allergen not in VALID_ALLERGENS:
                    raise ValueError(f'Invalid allergen: {allergen}. Must be one of: {", ".join(VALID_ALLERGENS)}')
        return v

class InventoryItemCreate(InventoryItemBase):
    pass

class InventoryItem(InventoryItemBase):
    id: str
    last_restocked: Optional[datetime] = None
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
