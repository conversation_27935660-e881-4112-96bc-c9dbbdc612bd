"""
Functional AI Service for RestroManage
Lightweight implementation using functional programming principles
Optimized for performance with Gemini 1.5 Flash model
"""

import os
import asyncio
from typing import Dict, Any, Optional, Callable, Tuple, Union
from functools import partial, reduce
from dataclasses import dataclass, replace
from datetime import datetime
import google.generativeai as genai
from app.utils.logging_config import logger


# Immutable data structures using dataclasses
@dataclass(frozen=True)
class AIConfig:
    """Immutable AI configuration"""
    api_key: str
    model_name: str = "gemini-1.5-flash"
    temperature: float = 0.7
    max_tokens: int = 800
    timeout: int = 10
    enabled: bool = False


@dataclass(frozen=True)
class AIRequest:
    """Immutable AI request structure"""
    query: str
    timestamp: datetime
    request_id: str
    context: Optional[Dict[str, Any]] = None


@dataclass(frozen=True)
class AIResponse:
    """Immutable AI response structure"""
    content: str
    success: bool
    timestamp: datetime
    request_id: str
    model_used: str
    error: Optional[str] = None
    cached: bool = False


# Pure functions for configuration
def create_config() -> AIConfig:
    """Pure function to create AI configuration from environment"""
    api_key = os.getenv("GOOGLE_API_KEY", "")
    model_name = os.getenv("GEMINI_MODEL", "gemini-1.5-flash")
    temperature = float(os.getenv("GOOGLE_AI_TEMPERATURE", "0.7"))
    max_tokens = int(os.getenv("GOOGLE_AI_MAX_TOKENS", "800"))
    timeout = int(os.getenv("AI_TIMEOUT", "10"))
    
    enabled = bool(api_key and api_key != "your-google-api-key-here")
    
    return AIConfig(
        api_key=api_key,
        model_name=model_name,
        temperature=temperature,
        max_tokens=max_tokens,
        timeout=timeout,
        enabled=enabled
    )


def validate_config(config: AIConfig) -> Tuple[bool, Optional[str]]:
    """Pure function to validate AI configuration"""
    if not config.api_key:
        return False, "API key not configured"
    
    if config.api_key == "your-google-api-key-here":
        return False, "Default API key placeholder detected"
    
    if config.temperature < 0 or config.temperature > 2:
        return False, "Temperature must be between 0 and 2"
    
    if config.max_tokens < 1 or config.max_tokens > 2048:
        return False, "Max tokens must be between 1 and 2048"
    
    return True, None


# Pure functions for request processing
def create_request(query: str, request_id: str, context: Optional[Dict[str, Any]] = None) -> AIRequest:
    """Pure function to create AI request"""
    return AIRequest(
        query=query.strip(),
        timestamp=datetime.now(),
        request_id=request_id,
        context=context or {}
    )


def validate_request(request: AIRequest) -> Tuple[bool, Optional[str]]:
    """Pure function to validate AI request"""
    if not request.query:
        return False, "Query cannot be empty"
    
    if len(request.query) > 2000:
        return False, "Query too long (max 2000 characters)"
    
    return True, None


def sanitize_query(query: str) -> str:
    """Pure function to sanitize user query"""
    # Remove potentially harmful content
    sanitized = query.strip()
    
    # Basic sanitization - remove excessive whitespace
    sanitized = ' '.join(sanitized.split())
    
    # Truncate if too long
    if len(sanitized) > 2000:
        sanitized = sanitized[:2000]
    
    return sanitized


# Pure functions for response handling
def create_success_response(content: str, request: AIRequest, model_used: str, cached: bool = False) -> AIResponse:
    """Pure function to create successful AI response"""
    return AIResponse(
        content=content.strip(),
        success=True,
        timestamp=datetime.now(),
        request_id=request.request_id,
        model_used=model_used,
        cached=cached
    )


def create_error_response(error: str, request: AIRequest, model_used: str = "none") -> AIResponse:
    """Pure function to create error AI response"""
    return AIResponse(
        content="I'm sorry, I encountered an error. Please try again.",
        success=False,
        timestamp=datetime.now(),
        request_id=request.request_id,
        model_used=model_used,
        error=error
    )


def create_fallback_response(request: AIRequest) -> AIResponse:
    """Pure function to create fallback response when AI is unavailable"""
    query_lower = request.query.lower()
    
    # Simple keyword-based responses
    if any(word in query_lower for word in ["hello", "hi", "hey"]):
        content = "Hello! I'm your AI assistant. How can I help you today?"
    elif any(word in query_lower for word in ["how are you", "how do you do"]):
        content = "I'm doing well, thank you for asking! I'm here to help with any questions you might have."
    elif any(word in query_lower for word in ["what can you do", "help", "capabilities"]):
        content = "I'm an AI assistant that can help answer questions, provide information, and have conversations on various topics. What would you like to know?"
    elif any(word in query_lower for word in ["thank", "thanks"]):
        content = "You're welcome! Is there anything else I can help you with?"
    elif any(word in query_lower for word in ["bye", "goodbye", "see you"]):
        content = "Goodbye! Feel free to ask me anything anytime."
    else:
        content = "I'm here to help! Feel free to ask me anything - I can discuss various topics, answer questions, or just have a friendly conversation."
    
    return AIResponse(
        content=content,
        success=True,
        timestamp=datetime.now(),
        request_id=request.request_id,
        model_used="fallback",
        cached=False
    )


# Higher-order functions for composition
def with_logging(func: Callable) -> Callable:
    """Higher-order function to add logging to any function"""
    async def wrapper(*args, **kwargs):
        func_name = func.__name__
        logger.debug(f"Calling {func_name}", "FunctionalAI")
        
        try:
            result = await func(*args, **kwargs)
            logger.debug(f"{func_name} completed successfully", "FunctionalAI")
            return result
        except Exception as e:
            logger.error(f"{func_name} failed", "FunctionalAI", {"error": str(e)})
            raise
    
    return wrapper


def with_timeout(timeout_seconds: int):
    """Higher-order function to add timeout to async functions"""
    def decorator(func: Callable) -> Callable:
        async def wrapper(*args, **kwargs):
            try:
                return await asyncio.wait_for(func(*args, **kwargs), timeout=timeout_seconds)
            except asyncio.TimeoutError:
                raise Exception(f"Operation timed out after {timeout_seconds} seconds")
        return wrapper
    return decorator


def with_retry(max_attempts: int = 2, delay: float = 1.0):
    """Higher-order function to add retry logic"""
    def decorator(func: Callable) -> Callable:
        async def wrapper(*args, **kwargs):
            last_exception = None

            for attempt in range(max_attempts):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_attempts - 1:
                        await asyncio.sleep(delay)
                        logger.warning(f"Retry attempt {attempt + 1} for {func.__name__}", "FunctionalAI")

            raise last_exception
        return wrapper
    return decorator


# Core AI functions
async def initialize_ai_model(config: AIConfig) -> Optional[genai.GenerativeModel]:
    """Initialize Gemini model with configuration"""
    if not config.enabled:
        return None

    try:
        genai.configure(api_key=config.api_key)
        model = genai.GenerativeModel(config.model_name)
        logger.info("AI model initialized successfully", "FunctionalAI", {
            "model": config.model_name,
            "temperature": config.temperature,
            "max_tokens": config.max_tokens
        })
        return model
    except Exception as e:
        logger.error("Failed to initialize AI model", "FunctionalAI", {"error": str(e)})
        return None


def create_simple_prompt(query: str) -> str:
    """Pure function to create a simple conversational prompt"""
    return f"""You are a helpful and friendly AI assistant. Answer this question in a conversational and helpful way:

Question: {query}

Provide a clear, informative response. Be helpful, friendly, and engaging. Keep your response concise and focused."""


@with_logging
@with_retry(max_attempts=2, delay=1.0)
async def call_gemini_api(model: genai.GenerativeModel, prompt: str, config: AIConfig) -> str:
    """Call Gemini API with proper error handling"""
    if not model:
        raise Exception("AI model not initialized")

    try:
        response = model.generate_content(
            prompt,
            generation_config=genai.types.GenerationConfig(
                temperature=config.temperature,
                max_output_tokens=config.max_tokens
            )
        )

        if not response.text:
            raise Exception("Empty response from AI model")

        return response.text.strip()
    except Exception as e:
        logger.error("Gemini API call failed", "FunctionalAI", {"error": str(e)})
        raise


# Function composition for the main AI pipeline
def compose(*functions):
    """Compose multiple functions into a single function"""
    return reduce(lambda f, g: lambda x: g(f(x)), functions, lambda x: x)


async def process_ai_request(request: AIRequest, config: AIConfig, model: Optional[genai.GenerativeModel]) -> AIResponse:
    """Main AI request processing pipeline using functional composition"""

    # Validate request
    is_valid, error = validate_request(request)
    if not is_valid:
        return create_error_response(error, request)

    # If AI is not enabled, return fallback response
    if not config.enabled or not model:
        logger.info("AI not enabled, using fallback response", "FunctionalAI")
        return create_fallback_response(request)

    try:
        # Create prompt
        prompt = create_simple_prompt(request.query)

        # Call AI with timeout
        ai_call_with_timeout = with_timeout(config.timeout)(call_gemini_api)
        content = await ai_call_with_timeout(model, prompt, config)

        # Create successful response
        return create_success_response(content, request, config.model_name)

    except Exception as e:
        logger.error("AI request processing failed", "FunctionalAI", {
            "error": str(e),
            "request_id": request.request_id
        })
        return create_error_response(str(e), request, config.model_name)


# Simple in-memory cache for responses (functional approach)
class ResponseCache:
    """Simple functional cache implementation"""

    def __init__(self, max_size: int = 100, ttl_seconds: int = 300):
        self._cache: Dict[str, Tuple[AIResponse, datetime]] = {}
        self._max_size = max_size
        self._ttl_seconds = ttl_seconds

    def _is_expired(self, timestamp: datetime) -> bool:
        """Check if cache entry is expired"""
        return (datetime.now() - timestamp).total_seconds() > self._ttl_seconds

    def _cleanup_expired(self) -> None:
        """Remove expired entries"""
        current_time = datetime.now()
        expired_keys = [
            key for key, (_, timestamp) in self._cache.items()
            if self._is_expired(timestamp)
        ]
        for key in expired_keys:
            del self._cache[key]

    def _evict_oldest(self) -> None:
        """Evict oldest entry if cache is full"""
        if len(self._cache) >= self._max_size:
            oldest_key = min(self._cache.keys(), key=lambda k: self._cache[k][1])
            del self._cache[oldest_key]

    def get(self, query: str) -> Optional[AIResponse]:
        """Get cached response for query"""
        self._cleanup_expired()

        cache_key = query.lower().strip()
        if cache_key in self._cache:
            response, _ = self._cache[cache_key]
            # Return a new response with cached flag
            return replace(response, cached=True, timestamp=datetime.now())

        return None

    def put(self, query: str, response: AIResponse) -> None:
        """Cache response for query"""
        self._cleanup_expired()
        self._evict_oldest()

        cache_key = query.lower().strip()
        self._cache[cache_key] = (response, datetime.now())

    def clear(self) -> None:
        """Clear all cached responses"""
        self._cache.clear()


# Global instances (initialized lazily)
_config: Optional[AIConfig] = None
_model: Optional[genai.GenerativeModel] = None
_cache: Optional[ResponseCache] = None


async def get_or_create_config() -> AIConfig:
    """Get or create AI configuration (always fresh from environment)"""
    global _config
    # Always create fresh config to pick up environment changes
    _config = create_config()
    is_valid, error = validate_config(_config)
    if not is_valid:
        logger.warning(f"AI configuration invalid: {error}", "FunctionalAI")
    return _config


async def get_or_create_model() -> Optional[genai.GenerativeModel]:
    """Get or create AI model (always fresh to pick up config changes)"""
    global _model
    # Always recreate model to pick up configuration changes
    config = await get_or_create_config()
    _model = await initialize_ai_model(config)
    return _model


def get_or_create_cache() -> ResponseCache:
    """Get or create response cache (lazy initialization)"""
    global _cache
    if _cache is None:
        _cache = ResponseCache(max_size=50, ttl_seconds=300)  # 5 minutes TTL
    return _cache


# Public API functions
async def chat_with_ai(query: str, request_id: Optional[str] = None) -> Dict[str, Any]:
    """
    Main public function for chatting with AI
    Returns a dictionary compatible with the existing API
    """
    import uuid

    if not request_id:
        request_id = str(uuid.uuid4())

    # Sanitize input
    sanitized_query = sanitize_query(query)

    # Create request
    request = create_request(sanitized_query, request_id)

    # Check cache first
    cache = get_or_create_cache()
    cached_response = cache.get(sanitized_query)

    if cached_response:
        logger.info("Returning cached AI response", "FunctionalAI", {
            "request_id": request_id,
            "cached": True
        })
        return {
            "success": cached_response.success,
            "response": cached_response.content,
            "ai_enabled": True,
            "cached": True,
            "timestamp": cached_response.timestamp.isoformat()
        }

    # Get configuration and model
    config = await get_or_create_config()
    model = await get_or_create_model()

    # Process request
    response = await process_ai_request(request, config, model)

    # Cache successful responses
    if response.success and not response.error:
        cache.put(sanitized_query, response)

    # Log the interaction
    logger.info("AI chat completed", "FunctionalAI", {
        "request_id": request_id,
        "success": response.success,
        "model_used": response.model_used,
        "cached": response.cached,
        "query_length": len(sanitized_query),
        "response_length": len(response.content)
    })

    return {
        "success": response.success,
        "response": response.content,
        "ai_enabled": config.enabled,
        "cached": response.cached,
        "timestamp": response.timestamp.isoformat(),
        "error": response.error
    }


async def get_ai_status() -> Dict[str, Any]:
    """Get AI service status"""
    config = await get_or_create_config()

    return {
        "ai_enabled": config.enabled,
        "model": config.model_name if config.enabled else None,
        "features": ["general_conversation"] if config.enabled else [],
        "message": "AI assistant is ready" if config.enabled else "AI assistant is disabled - check API key configuration"
    }


async def reload_ai_configuration() -> Dict[str, Any]:
    """Reload AI configuration from environment"""
    global _config, _model, _cache

    # Clear existing instances
    _config = None
    _model = None
    if _cache:
        _cache.clear()

    # Recreate configuration
    config = await get_or_create_config()
    model = await get_or_create_model()

    logger.info("AI configuration reloaded", "FunctionalAI", {
        "enabled": config.enabled,
        "model": config.model_name
    })

    return {
        "success": True,
        "ai_enabled": config.enabled,
        "model": config.model_name,
        "message": "Configuration reloaded successfully"
    }


# Utility functions for testing and debugging
def get_cache_stats() -> Dict[str, Any]:
    """Get cache statistics"""
    cache = get_or_create_cache()
    return {
        "cache_size": len(cache._cache),
        "max_size": cache._max_size,
        "ttl_seconds": cache._ttl_seconds
    }


def clear_cache() -> Dict[str, Any]:
    """Clear the response cache"""
    cache = get_or_create_cache()
    cache.clear()
    return {"success": True, "message": "Cache cleared"}


# Export main functions
__all__ = [
    "chat_with_ai",
    "get_ai_status",
    "reload_ai_configuration",
    "get_cache_stats",
    "clear_cache"
]
