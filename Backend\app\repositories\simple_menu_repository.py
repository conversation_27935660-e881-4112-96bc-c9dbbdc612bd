"""
Simplified menu repository using raw SQL queries.
This bypasses SQLAlchemy ORM issues and works directly with the database.
"""

import aiosqlite
from typing import List, Dict, Any, Optional
from pathlib import Path
import json

class SimpleMenuRepository:
    """Simple menu repository using raw SQL"""
    
    def __init__(self):
        self.db_path = Path("restro_manage.db")
    
    async def get_all(
        self,
        restaurant_id: Optional[str] = None,
        category: Optional[str] = None,
        available: Optional[bool] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Get all menu items with optional filtering"""
        try:
            async with aiosqlite.connect(str(self.db_path)) as db:
                # Build query with filters
                query = "SELECT * FROM menu_items WHERE 1=1"
                params = []
                
                if restaurant_id:
                    query += " AND restaurant_id = ?"
                    params.append(restaurant_id)
                
                if category:
                    query += " AND category = ?"
                    params.append(category)
                
                if available is not None:
                    query += " AND available = ?"
                    params.append(available)
                
                query += " ORDER BY name LIMIT ? OFFSET ?"
                params.extend([limit, skip])
                
                cursor = await db.execute(query, params)
                rows = await cursor.fetchall()
                
                # Get column names
                columns = [description[0] for description in cursor.description]
                
                # Convert to dictionaries
                items = []
                for row in rows:
                    item = dict(zip(columns, row))
                    
                    # Parse JSON fields
                    if item.get('ingredients'):
                        try:
                            item['ingredients'] = json.loads(item['ingredients'])
                        except (json.JSONDecodeError, TypeError):
                            item['ingredients'] = []
                    
                    if item.get('allergens'):
                        try:
                            item['allergens'] = json.loads(item['allergens'])
                        except (json.JSONDecodeError, TypeError):
                            item['allergens'] = []
                    
                    items.append(item)
                
                return items
                
        except Exception as e:
            print(f"Error getting menu items: {e}")
            return []
    
    async def get_by_id(self, item_id: str) -> Optional[Dict[str, Any]]:
        """Get a menu item by ID"""
        try:
            async with aiosqlite.connect(str(self.db_path)) as db:
                cursor = await db.execute("SELECT * FROM menu_items WHERE id = ?", (item_id,))
                row = await cursor.fetchone()
                
                if not row:
                    return None
                
                # Get column names
                columns = [description[0] for description in cursor.description]
                item = dict(zip(columns, row))
                
                # Parse JSON fields
                if item.get('ingredients'):
                    try:
                        item['ingredients'] = json.loads(item['ingredients'])
                    except (json.JSONDecodeError, TypeError):
                        item['ingredients'] = []
                
                if item.get('allergens'):
                    try:
                        item['allergens'] = json.loads(item['allergens'])
                    except (json.JSONDecodeError, TypeError):
                        item['allergens'] = []
                
                return item
                
        except Exception as e:
            print(f"Error getting menu item {item_id}: {e}")
            return None
    
    async def create(self, item_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new menu item"""
        try:
            async with aiosqlite.connect(str(self.db_path)) as db:
                # Generate ID if not provided
                if 'id' not in item_data:
                    import uuid
                    item_data['id'] = str(uuid.uuid4())
                
                # Convert JSON fields to strings
                if 'ingredients' in item_data and isinstance(item_data['ingredients'], list):
                    item_data['ingredients'] = json.dumps(item_data['ingredients'])
                
                if 'allergens' in item_data and isinstance(item_data['allergens'], list):
                    item_data['allergens'] = json.dumps(item_data['allergens'])
                
                # Add timestamps
                from datetime import datetime
                now = datetime.now().isoformat()
                item_data['created_at'] = now
                item_data['updated_at'] = now
                
                # Insert into database
                columns = list(item_data.keys())
                placeholders = ', '.join(['?' for _ in columns])
                query = f"INSERT INTO menu_items ({', '.join(columns)}) VALUES ({placeholders})"
                
                await db.execute(query, list(item_data.values()))
                await db.commit()
                
                return await self.get_by_id(item_data['id'])
                
        except Exception as e:
            print(f"Error creating menu item: {e}")
            raise
    
    async def update(self, item_id: str, item_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Update a menu item"""
        try:
            async with aiosqlite.connect(str(self.db_path)) as db:
                # Convert JSON fields to strings
                if 'ingredients' in item_data and isinstance(item_data['ingredients'], list):
                    item_data['ingredients'] = json.dumps(item_data['ingredients'])
                
                if 'allergens' in item_data and isinstance(item_data['allergens'], list):
                    item_data['allergens'] = json.dumps(item_data['allergens'])
                
                # Add updated timestamp
                from datetime import datetime
                item_data['updated_at'] = datetime.now().isoformat()
                
                # Build update query
                set_clauses = [f"{key} = ?" for key in item_data.keys()]
                query = f"UPDATE menu_items SET {', '.join(set_clauses)} WHERE id = ?"
                params = list(item_data.values()) + [item_id]
                
                await db.execute(query, params)
                await db.commit()
                
                return await self.get_by_id(item_id)
                
        except Exception as e:
            print(f"Error updating menu item {item_id}: {e}")
            return None
    
    async def delete(self, item_id: str) -> bool:
        """Delete a menu item"""
        try:
            async with aiosqlite.connect(str(self.db_path)) as db:
                await db.execute("DELETE FROM menu_items WHERE id = ?", (item_id,))
                await db.commit()
                return True
                
        except Exception as e:
            print(f"Error deleting menu item {item_id}: {e}")
            return False

# Create global instance
simple_menu_repository = SimpleMenuRepository()
