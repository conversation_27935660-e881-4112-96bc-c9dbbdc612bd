from fastapi import APIRouter, Depends, HTTPException, status
from typing import List
from app.models.staff import Staff, StaffCreate
from app.utils.storage import get_all, get_by_id, create, update, delete
from app.utils.auth import get_current_active_user, check_manager_role

router = APIRouter(prefix="/staff", tags=["Staff"])

@router.get("/", response_model=List[Staff])
async def get_staff_members(current_user = Depends(get_current_active_user)):
    """Get all staff members"""
    # Check if user has manager or admin role
    check_manager_role(current_user)
    
    return get_all("staff")

@router.get("/{staff_id}", response_model=Staff)
async def get_staff_member(
    staff_id: str,
    current_user = Depends(get_current_active_user)
):
    """Get a staff member by ID"""
    # Check if user has manager or admin role
    check_manager_role(current_user)
    
    staff = get_by_id("staff", staff_id)
    if not staff:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Staff member not found"
        )
    return staff

@router.post("/", response_model=Staff)
async def create_staff_member(
    staff: StaffCreate,
    current_user = Depends(get_current_active_user)
):
    """Create a new staff member"""
    # Check if user has manager or admin role
    check_manager_role(current_user)
    
    return create("staff", staff.dict())

@router.put("/{staff_id}", response_model=Staff)
async def update_staff_member(
    staff_id: str,
    staff: StaffCreate,
    current_user = Depends(get_current_active_user)
):
    """Update a staff member"""
    # Check if user has manager or admin role
    check_manager_role(current_user)
    
    # Check if staff exists
    existing_staff = get_by_id("staff", staff_id)
    if not existing_staff:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Staff member not found"
        )
    
    updated_staff = update("staff", staff_id, staff.dict())
    return updated_staff

@router.delete("/{staff_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_staff_member(
    staff_id: str,
    current_user = Depends(get_current_active_user)
):
    """Delete a staff member"""
    # Check if user has manager or admin role
    check_manager_role(current_user)
    
    # Check if staff exists
    existing_staff = get_by_id("staff", staff_id)
    if not existing_staff:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Staff member not found"
        )
    
    success = delete("staff", staff_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete staff member"
        )
