#!/usr/bin/env python3
"""
Railway startup script for RestroManage Backend
Python-based alternative to avoid shell permission issues
"""

import os
import sys
import time
import subprocess
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def wait_for_database():
    """Wait for database to be ready"""
    logger.info("⏳ Waiting for database connection...")
    
    database_url = os.getenv('DATABASE_URL')
    if not database_url:
        logger.error("❌ DATABASE_URL not set")
        sys.exit(1)
    
    try:
        from sqlalchemy import create_engine, text
        engine = create_engine(database_url)
        
        max_retries = 30
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                with engine.connect() as conn:
                    conn.execute(text('SELECT 1'))
                logger.info("✅ Database connection successful")
                return True
            except Exception as e:
                retry_count += 1
                logger.info(f"⏳ Database not ready (attempt {retry_count}/{max_retries}): {e}")
                time.sleep(2)
        
        logger.error("❌ Database connection failed after maximum retries")
        sys.exit(1)
        
    except ImportError:
        logger.warning("⚠️ SQLAlchemy not available, skipping database check")
        return True

def run_migrations():
    """Run database migrations"""
    logger.info("🔄 Running database migrations...")
    
    try:
        # Try the new database structure first
        from app.database import init_database
        if init_database():
            logger.info("✅ Database initialization completed")
        else:
            logger.warning("⚠️ Database initialization had issues, continuing...")
    except ImportError:
        try:
            # Fallback to old structure
            from Database.migrations import initialize_database
            if initialize_database():
                logger.info("✅ Database initialization completed")
            else:
                logger.warning("⚠️ Database initialization had issues, continuing...")
        except Exception as e:
            logger.warning(f"⚠️ Migration error: {e}, continuing...")
    except Exception as e:
        logger.warning(f"⚠️ Migration error: {e}, continuing...")

def setup_directories():
    """Create necessary directories"""
    logger.info("📁 Setting up upload directories...")
    
    directories = [
        "/app/uploads/logos",
        "/app/uploads/data", 
        "/app/logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        logger.info(f"Created directory: {directory}")

def determine_app_module():
    """Determine the correct app module to use"""
    if os.path.exists("app/api.py"):
        return "app.api:app"
    elif os.path.exists("main.py"):
        return "main:app"
    else:
        return "app:app"

def start_server():
    """Start the gunicorn server"""
    host = "0.0.0.0"
    port = os.getenv('PORT', '8000')
    workers = os.getenv('WORKERS', '4')
    log_level = os.getenv('LOG_LEVEL', 'info')
    
    app_module = determine_app_module()
    
    logger.info(f"🌐 Starting server on {host}:{port}")
    logger.info(f"🚀 Using app module: {app_module}")
    
    cmd = [
        "gunicorn", app_module,
        "--worker-class", "uvicorn.workers.UvicornWorker",
        "--workers", workers,
        "--bind", f"{host}:{port}",
        "--timeout", "120",
        "--keep-alive", "2",
        "--max-requests", "1000",
        "--max-requests-jitter", "100",
        "--access-logfile", "-",
        "--error-logfile", "-",
        "--log-level", log_level
    ]
    
    try:
        subprocess.exec(cmd)
    except AttributeError:
        # Python < 3.3 fallback
        os.execvp("gunicorn", cmd)

def main():
    """Main startup function"""
    logger.info("🚀 Starting RestroManage Backend on Railway...")
    
    # Wait for database
    wait_for_database()
    
    # Run migrations
    run_migrations()
    
    # Setup directories
    setup_directories()
    
    # Start server
    start_server()

if __name__ == "__main__":
    main()
