# Table Repository - Database operations for tables
# This replaces the JSON storage system with proper database queries

from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
import uuid
from datetime import datetime

from Database.models.tables import Table as TableModel
from app.database import get_db_session_context

class TableRepository:
    """Repository for table database operations"""
    
    def __init__(self):
        self.model = TableModel

    async def get_all(
        self, 
        restaurant_id: Optional[str] = None,
        status: Optional[str] = None,
        location: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Get all tables with optional filtering"""
        async with get_db_session_context() as session:
            query = select(self.model)
            
            # Build filter conditions
            conditions = []
            
            if restaurant_id:
                conditions.append(self.model.restaurant_id == restaurant_id)
            
            if status:
                conditions.append(self.model.status == status)
            
            if location:
                conditions.append(self.model.location.ilike(f"%{location}%"))
            
            # Apply all conditions
            if conditions:
                query = query.where(and_(*conditions))
            
            # Apply pagination
            query = query.offset(skip).limit(limit)
            
            # Execute query
            result = await session.execute(query)
            tables = result.scalars().all()
            
            # Convert to dictionaries
            return [self._model_to_dict(table) for table in tables]

    async def get_by_id(self, table_id: str) -> Optional[Dict[str, Any]]:
        """Get a table by ID"""
        async with get_db_session_context() as session:
            query = select(self.model).where(self.model.id == table_id)
            result = await session.execute(query)
            table = result.scalar_one_or_none()
            
            if table:
                return self._model_to_dict(table)
            return None

    async def create(self, table_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new table"""
        async with get_db_session_context() as session:
            # Generate ID if not provided
            if "id" not in table_data:
                table_data["id"] = str(uuid.uuid4())
            
            # Set timestamps
            now = datetime.utcnow()
            table_data["created_at"] = now
            table_data["updated_at"] = now
            
            # Set default status if not provided
            if "status" not in table_data:
                table_data["status"] = "available"
            
            # Create model instance
            table = self.model(**table_data)
            
            # Add to session and commit
            session.add(table)
            await session.commit()
            await session.refresh(table)
            
            return self._model_to_dict(table)

    async def update(self, table_id: str, table_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Update a table"""
        async with get_db_session_context() as session:
            # Get existing table
            query = select(self.model).where(self.model.id == table_id)
            result = await session.execute(query)
            table = result.scalar_one_or_none()
            
            if not table:
                return None
            
            # Update fields
            for key, value in table_data.items():
                if hasattr(table, key) and key != "id":  # Don't update ID
                    setattr(table, key, value)
            
            # Update timestamp
            table.updated_at = datetime.utcnow()
            
            # Commit changes
            await session.commit()
            await session.refresh(table)
            
            return self._model_to_dict(table)

    async def delete(self, table_id: str) -> bool:
        """Delete a table"""
        async with get_db_session_context() as session:
            # Get existing table
            query = select(self.model).where(self.model.id == table_id)
            result = await session.execute(query)
            table = result.scalar_one_or_none()
            
            if not table:
                return False
            
            # Delete table
            await session.delete(table)
            await session.commit()
            
            return True

    async def get_by_restaurant(self, restaurant_id: str) -> List[Dict[str, Any]]:
        """Get all tables for a specific restaurant"""
        return await self.get_all(restaurant_id=restaurant_id)

    async def get_available_tables(self, restaurant_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get only available tables"""
        return await self.get_all(restaurant_id=restaurant_id, status="available")

    async def get_by_status(self, status: str, restaurant_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get tables by status"""
        return await self.get_all(restaurant_id=restaurant_id, status=status)

    async def update_status(self, table_id: str, status: str) -> Optional[Dict[str, Any]]:
        """Update table status"""
        return await self.update(table_id, {"status": status})

    async def get_by_number(self, restaurant_id: str, table_number: int) -> Optional[Dict[str, Any]]:
        """Get table by restaurant and table number"""
        async with get_db_session_context() as session:
            query = select(self.model).where(
                and_(
                    self.model.restaurant_id == restaurant_id,
                    self.model.number == table_number
                )
            )
            result = await session.execute(query)
            table = result.scalar_one_or_none()
            
            if table:
                return self._model_to_dict(table)
            return None

    def _model_to_dict(self, table: TableModel) -> Dict[str, Any]:
        """Convert SQLAlchemy model to dictionary"""
        return {
            "id": table.id,
            "restaurant_id": table.restaurant_id,
            "number": table.number,
            "capacity": table.capacity,
            "location": table.location,
            "status": table.status,
            "qr_code": table.qr_code,
            "notes": table.notes,
            "created_at": table.created_at.isoformat() if table.created_at else None,
            "updated_at": table.updated_at.isoformat() if table.updated_at else None
        }

# Global repository instance
table_repository = TableRepository()
