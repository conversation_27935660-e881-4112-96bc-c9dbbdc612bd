import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Clock } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  TimeEntry,
  getStaffTimeEntries,
  getStaffTimeEntriesForWeek,
  getStaffTimeEntriesForMonth,
  getCurrentWeek,
  getCurrentMonth
} from "@/services/timeEntryService";

interface StaffHoursTrackingProps {
  staffId: string;
  staffName: string;
}

const StaffHoursTracking = ({ staffId, staffName }: StaffHoursTrackingProps) => {
  const [timeEntries, setTimeEntries] = useState<TimeEntry[]>([]);
  const [selectedPeriod, setSelectedPeriod] = useState<"week" | "month">("week");
  const [selectedWeek, setSelectedWeek] = useState<string>(getCurrentWeekString());
  const [selectedMonth, setSelectedMonth] = useState<string>(getCurrentMonthString());

  // Get current week in YYYY-Www format
  function getCurrentWeekString(): string {
    const { year, week } = getCurrentWeek();
    return `${year}-W${week.toString().padStart(2, '0')}`;
  }

  // Get current month in YYYY-MM format
  function getCurrentMonthString(): string {
    const { year, month } = getCurrentMonth();
    return `${year}-${month.toString().padStart(2, '0')}`;
  }

  // Load time entries when component mounts or when period/selection changes
  useEffect(() => {
    let entries: TimeEntry[] = [];
    
    if (selectedPeriod === "week") {
      const [year, weekNum] = selectedWeek.split('-W');
      entries = getStaffTimeEntriesForWeek(
        staffId, 
        parseInt(year), 
        parseInt(weekNum)
      );
    } else {
      const [year, month] = selectedMonth.split('-');
      entries = getStaffTimeEntriesForMonth(
        staffId, 
        parseInt(year), 
        parseInt(month)
      );
    }
    
    // If no real entries exist yet, generate some mock data
    if (entries.length === 0) {
      entries = generateMockTimeEntries(staffId);
    }
    
    setTimeEntries(entries);
  }, [staffId, selectedPeriod, selectedWeek, selectedMonth]);

  // Generate mock time entries for the past 3 months
  function generateMockTimeEntries(staffId: string): TimeEntry[] {
    const entries: TimeEntry[] = [];
    const now = new Date();
    const startDate = new Date(now);
    startDate.setMonth(now.getMonth() - 3);

    let currentDate = new Date(startDate);
    let id = 1;

    while (currentDate <= now) {
      // Skip weekends for some staff members to make it more realistic
      const day = currentDate.getDay();
      if (parseInt(staffId) % 2 === 0 && (day === 0 || day === 6)) {
        currentDate.setDate(currentDate.getDate() + 1);
        continue;
      }

      // Random clock in time between 8:00 and 10:00
      const clockInHour = 8 + Math.floor(Math.random() * 2);
      const clockInMinute = Math.floor(Math.random() * 30);
      const clockIn = `${clockInHour.toString().padStart(2, '0')}:${clockInMinute.toString().padStart(2, '0')}`;

      // Random shift length between 6 and 9 hours
      const shiftLength = 6 + Math.floor(Math.random() * 3);
      
      // Random break time between 30 and 60 minutes
      const breakTime = 30 + Math.floor(Math.random() * 31);

      // Calculate clock out time
      const clockOutHour = clockInHour + shiftLength;
      const clockOutMinute = clockInMinute;
      const clockOut = `${clockOutHour.toString().padStart(2, '0')}:${clockOutMinute.toString().padStart(2, '0')}`;

      // Calculate total hours (shift length minus break time in hours)
      const totalHours = shiftLength - (breakTime / 60);

      entries.push({
        id: id.toString(),
        staffId,
        date: currentDate.toISOString().split('T')[0],
        clockIn,
        clockOut,
        breakStart: null,
        breakEnd: null,
        breakTime,
        totalHours
      });

      id++;
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return entries;
  }

  // Filter entries based on selected period
  const filteredEntries = timeEntries.filter(entry => {
    if (selectedPeriod === "week") {
      const entryDate = new Date(entry.date);
      const weekYear = entryDate.getFullYear();
      const onejan = new Date(weekYear, 0, 1);
      const weekNum = Math.ceil(((entryDate.getTime() - onejan.getTime()) / 86400000 + onejan.getDay() + 1) / 7);
      const entryWeek = `${weekYear}-W${weekNum.toString().padStart(2, '0')}`;
      return entryWeek === selectedWeek;
    } else {
      return entry.date.startsWith(selectedMonth);
    }
  });

  // Calculate total hours for the selected period
  const totalHours = filteredEntries.reduce((sum, entry) => sum + entry.totalHours, 0);
  
  // Generate available weeks for selection
  const getAvailableWeeks = () => {
    const weeks: { label: string, value: string }[] = [];
    const now = new Date();
    const startDate = new Date(now);
    startDate.setMonth(now.getMonth() - 3);

    let currentDate = new Date(startDate);

    while (currentDate <= now) {
      const weekYear = currentDate.getFullYear();
      const onejan = new Date(weekYear, 0, 1);
      const weekNum = Math.ceil(((currentDate.getTime() - onejan.getTime()) / 86400000 + onejan.getDay() + 1) / 7);
      const weekValue = `${weekYear}-W${weekNum.toString().padStart(2, '0')}`;
      
      // Format the week label (e.g., "Week 23, 2023 (Jun 5 - Jun 11)")
      const weekStart = new Date(currentDate);
      weekStart.setDate(currentDate.getDate() - currentDate.getDay() + 1);
      const weekEnd = new Date(weekStart);
      weekEnd.setDate(weekStart.getDate() + 6);
      
      const formatDate = (date: Date) => {
        const month = date.toLocaleString('default', { month: 'short' });
        return `${month} ${date.getDate()}`;
      };
      
      const weekLabel = `Week ${weekNum}, ${weekYear} (${formatDate(weekStart)} - ${formatDate(weekEnd)})`;
      
      if (!weeks.some(w => w.value === weekValue)) {
        weeks.push({ label: weekLabel, value: weekValue });
      }
      
      currentDate.setDate(currentDate.getDate() + 7);
    }
    
    return weeks.reverse(); // Most recent first
  };
  
  // Generate available months for selection
  const getAvailableMonths = () => {
    const months: { label: string, value: string }[] = [];
    const now = new Date();
    const startDate = new Date(now);
    startDate.setMonth(now.getMonth() - 11); // Last 12 months
    
    let currentDate = new Date(startDate);
    
    while (currentDate <= now) {
      const year = currentDate.getFullYear();
      const month = currentDate.getMonth() + 1;
      const monthValue = `${year}-${month.toString().padStart(2, '0')}`;
      const monthLabel = currentDate.toLocaleString('default', { month: 'long', year: 'numeric' });
      
      months.push({ label: monthLabel, value: monthValue });
      
      currentDate.setMonth(currentDate.getMonth() + 1);
    }
    
    return months.reverse(); // Most recent first
  };

  // Format time (HH:MM)
  const formatTime = (time: string | null) => {
    return time || '--:--';
  };

  // Format date (e.g., Mon, Jun 5, 2023)
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      weekday: 'short', 
      month: 'short', 
      day: 'numeric', 
      year: 'numeric' 
    });
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-medium">Hours Tracking</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs value={selectedPeriod} onValueChange={(value: "week" | "month") => setSelectedPeriod(value)}>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
            <TabsList>
              <TabsTrigger value="week">Weekly</TabsTrigger>
              <TabsTrigger value="month">Monthly</TabsTrigger>
            </TabsList>
            
            <div className="w-full sm:w-64">
              {selectedPeriod === "week" ? (
                <Select value={selectedWeek} onValueChange={setSelectedWeek}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select week" />
                  </SelectTrigger>
                  <SelectContent>
                    {getAvailableWeeks().map((week) => (
                      <SelectItem key={week.value} value={week.value}>
                        {week.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ) : (
                <Select value={selectedMonth} onValueChange={setSelectedMonth}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select month" />
                  </SelectTrigger>
                  <SelectContent>
                    {getAvailableMonths().map((month) => (
                      <SelectItem key={month.value} value={month.value}>
                        {month.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </div>
          </div>

          <div className="bg-muted p-4 rounded-lg mb-4">
            <div className="flex items-center justify-between">
              <div>
                <Label className="text-sm text-muted-foreground">Total Hours</Label>
                <div className="text-2xl font-bold">{totalHours.toFixed(1)}h</div>
              </div>
              <div>
                <Label className="text-sm text-muted-foreground">Period</Label>
                <div className="font-medium">
                  {selectedPeriod === "week" 
                    ? getAvailableWeeks().find(w => w.value === selectedWeek)?.label
                    : getAvailableMonths().find(m => m.value === selectedMonth)?.label}
                </div>
              </div>
            </div>
          </div>

          <TabsContent value="week" className="m-0">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Clock In</TableHead>
                  <TableHead>Clock Out</TableHead>
                  <TableHead>Break</TableHead>
                  <TableHead className="text-right">Hours</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredEntries.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-4 text-muted-foreground">
                      No time entries for this week
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredEntries.map((entry) => (
                    <TableRow key={entry.id}>
                      <TableCell>{formatDate(entry.date)}</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Clock className="h-3 w-3 mr-1 text-muted-foreground" />
                          {formatTime(entry.clockIn)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Clock className="h-3 w-3 mr-1 text-muted-foreground" />
                          {formatTime(entry.clockOut)}
                        </div>
                      </TableCell>
                      <TableCell>{entry.breakTime} min</TableCell>
                      <TableCell className="text-right font-medium">{entry.totalHours.toFixed(1)}h</TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TabsContent>

          <TabsContent value="month" className="m-0">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Clock In</TableHead>
                  <TableHead>Clock Out</TableHead>
                  <TableHead>Break</TableHead>
                  <TableHead className="text-right">Hours</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredEntries.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-4 text-muted-foreground">
                      No time entries for this month
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredEntries.map((entry) => (
                    <TableRow key={entry.id}>
                      <TableCell>{formatDate(entry.date)}</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Clock className="h-3 w-3 mr-1 text-muted-foreground" />
                          {formatTime(entry.clockIn)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Clock className="h-3 w-3 mr-1 text-muted-foreground" />
                          {formatTime(entry.clockOut)}
                        </div>
                      </TableCell>
                      <TableCell>{entry.breakTime} min</TableCell>
                      <TableCell className="text-right font-medium">{entry.totalHours.toFixed(1)}h</TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default StaffHoursTracking;
