import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Table, 
  ShoppingBag, 
  Settings, 
  Users, 
  Clock,
  UtensilsCrossed,
  ArrowRight
} from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { useStaffPIN } from "@/contexts/StaffPINContext";
import { useSubscriptionAccess } from "@/hooks/useSubscriptionAccess";

interface EPOSLandingPageProps {
  onSelectDineIn: () => void;
  onSelectTakeaway: () => void;
  onSelectTableManagement: () => void;
}

const EPOSLandingPage: React.FC<EPOSLandingPageProps> = ({
  onSelectDineIn,
  onSelectTakeaway,
  onSelectTableManagement
}) => {
  const { currentRestaurant } = useAuth();
  const { activeStaff } = useStaffPIN();
  const { hasFeatureAccess, currentPlanName } = useSubscriptionAccess();

  const getCurrentTime = () => {
    return new Date().toLocaleTimeString('en-GB', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  };

  const getCurrentDate = () => {
    return new Date().toLocaleDateString('en-GB', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-background dark:from-blue-950/20 dark:to-background p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header Section */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <UtensilsCrossed className="w-8 h-8 text-blue-600 dark:text-blue-400" />
            <h1 className="text-4xl font-bold text-foreground">EPOS System</h1>
          </div>
          <p className="text-xl text-muted-foreground mb-2">
            Welcome to {currentRestaurant?.name || 'Your Restaurant'}
          </p>
          <div className="flex items-center justify-center gap-4 text-sm text-muted-foreground">
            <span>{getCurrentDate()}</span>
            <span>•</span>
            <span>{getCurrentTime()}</span>
            <span>•</span>
            <Badge variant="outline" className="text-blue-600 dark:text-blue-400 border-blue-200 dark:border-blue-800">
              {currentPlanName} Plan
            </Badge>
          </div>
        </div>

        {/* Staff Info */}
        {activeStaff && (
          <div className="bg-card rounded-lg shadow-sm border p-4 mb-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Users className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                <div>
                  <p className="font-medium text-card-foreground">
                    Logged in as: {activeStaff.name}
                  </p>
                  <p className="text-sm text-muted-foreground capitalize">
                    Role: {activeStaff.role}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Clock className="w-4 h-4" />
                <span>Session active</span>
              </div>
            </div>
          </div>
        )}

        {/* Main Action Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {/* Dine-In Card */}
          <Card className="group hover:shadow-lg transition-all duration-200 cursor-pointer border-2 hover:border-blue-200 dark:hover:border-blue-800">
            <CardHeader className="text-center pb-4">
              <div className="mx-auto w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mb-4 group-hover:bg-blue-200 dark:group-hover:bg-blue-900/50 transition-colors">
                <Table className="w-8 h-8 text-blue-600 dark:text-blue-400" />
              </div>
              <CardTitle className="text-xl text-card-foreground">Dine-In Service</CardTitle>
            </CardHeader>
            <CardContent className="text-center space-y-4">
              <p className="text-muted-foreground">
                Manage restaurant table orders, seating arrangements, and in-house dining experiences.
              </p>
              <div className="space-y-2 text-sm text-muted-foreground">
                <div className="flex items-center justify-center gap-2">
                  <span>•</span>
                  <span>Table selection & management</span>
                </div>
                <div className="flex items-center justify-center gap-2">
                  <span>•</span>
                  <span>Party size & allergen tracking</span>
                </div>
                <div className="flex items-center justify-center gap-2">
                  <span>•</span>
                  <span>Full menu ordering system</span>
                </div>
              </div>
              <Button
                onClick={onSelectDineIn}
                className="w-full group-hover:bg-blue-700 dark:group-hover:bg-blue-600 transition-colors"
                size="lg"
              >
                Start Dine-In Service
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </CardContent>
          </Card>

          {/* Takeaway Card */}
          <Card className="group hover:shadow-lg transition-all duration-200 cursor-pointer border-2 hover:border-green-200 dark:hover:border-green-800">
            <CardHeader className="text-center pb-4">
              <div className="mx-auto w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mb-4 group-hover:bg-green-200 dark:group-hover:bg-green-900/50 transition-colors">
                <ShoppingBag className="w-8 h-8 text-green-600 dark:text-green-400" />
              </div>
              <CardTitle className="text-xl text-card-foreground">Takeaway Orders</CardTitle>
            </CardHeader>
            <CardContent className="text-center space-y-4">
              <p className="text-muted-foreground">
                Process pickup and delivery orders with streamlined workflow and timing management.
              </p>
              <div className="space-y-2 text-sm text-muted-foreground">
                <div className="flex items-center justify-center gap-2">
                  <span>•</span>
                  <span>Quick order processing</span>
                </div>
                <div className="flex items-center justify-center gap-2">
                  <span>•</span>
                  <span>Customer contact management</span>
                </div>
                <div className="flex items-center justify-center gap-2">
                  <span>•</span>
                  <span>Pickup time estimation</span>
                </div>
              </div>
              <Button
                onClick={onSelectTakeaway}
                className="w-full bg-green-600 hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-600 transition-colors"
                size="lg"
              >
                Start Takeaway Service
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </CardContent>
          </Card>

          {/* Table Management Card */}
          <Card className="group hover:shadow-lg transition-all duration-200 cursor-pointer border-2 hover:border-purple-200 dark:hover:border-purple-800">
            <CardHeader className="text-center pb-4">
              <div className="mx-auto w-16 h-16 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mb-4 group-hover:bg-purple-200 dark:group-hover:bg-purple-900/50 transition-colors">
                <Settings className="w-8 h-8 text-purple-600 dark:text-purple-400" />
              </div>
              <CardTitle className="text-xl text-card-foreground">Table Management</CardTitle>
            </CardHeader>
            <CardContent className="text-center space-y-4">
              <p className="text-muted-foreground">
                Comprehensive table status management and takeaway order queue oversight.
              </p>
              <div className="space-y-2 text-sm text-muted-foreground">
                <div className="flex items-center justify-center gap-2">
                  <span>•</span>
                  <span>Table status & cleaning</span>
                </div>
                <div className="flex items-center justify-center gap-2">
                  <span>•</span>
                  <span>Order queue management</span>
                </div>
                <div className="flex items-center justify-center gap-2">
                  <span>•</span>
                  <span>Real-time status updates</span>
                </div>
              </div>
              <Button
                onClick={onSelectTableManagement}
                className="w-full bg-purple-600 hover:bg-purple-700 dark:bg-purple-700 dark:hover:bg-purple-600 transition-colors"
                size="lg"
              >
                Open Table Management
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Quick Stats or Additional Info */}
        <div className="bg-card rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-semibold text-card-foreground mb-4">Quick Access Tips</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-muted-foreground">
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-blue-500 dark:bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
              <div>
                <p className="font-medium text-card-foreground">Dine-In Service</p>
                <p>Best for managing seated customers with full table service experience.</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-green-500 dark:bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
              <div>
                <p className="font-medium text-card-foreground">Takeaway Orders</p>
                <p>Optimized for quick order processing and customer pickup coordination.</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-purple-500 dark:bg-purple-400 rounded-full mt-2 flex-shrink-0"></div>
              <div>
                <p className="font-medium text-card-foreground">Table Management</p>
                <p>Comprehensive oversight tool for restaurant operations and status tracking.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EPOSLandingPage;
