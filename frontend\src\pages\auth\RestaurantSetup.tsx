import { updateUserSetupData } from "@/services/mockDataService";
import { useAuth } from "@/contexts/AuthContext";
import apiService, { handleApiError } from "@/services/apiService";
import logger from "@/utils/logger";
import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Slider } from "@/components/ui/slider";
import { Alert, AlertDescription } from "@/components/ui/alert";

import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import {
  Clock,
  Users,
  Utensils,
  DollarSign,
  Settings,
  CheckCircle,
  ArrowRight,
  ArrowLeft,
  Save,
  AlertCircle
} from "lucide-react";
import LogoUpload from "@/components/auth/LogoUpload";
import TableConfiguration from "@/components/auth/TableConfiguration";
import {
  RestaurantSetupData,
  TableConfiguration as TableConfig,
  CUISINE_TYPES,
  RestaurantRegistrationData
} from "@/types/restaurantSetup";

const RestaurantSetup: React.FC = () => {
  // Initialize component logging
  logger.setComponent("RestaurantSetup");
  logger.info("Component initialized", "RestaurantSetup");
  const navigate = useNavigate();
  const { refreshRestaurantData } = useAuth();
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [generatedCredentials, setGeneratedCredentials] = useState<{ restaurantCode: string; ownerPin: string } | null>(null);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [registrationData, setRegistrationData] = useState<RestaurantRegistrationData | null>(null);
  const [backendAvailable, setBackendAvailable] = useState<boolean | null>(null);

  const [setupData, setSetupData] = useState<RestaurantSetupData>({
    useDefaultLogo: true,
    logoPosition: "center",
    totalTables: 10,
    tables: [],
    defaultTableCapacity: 4,
    tableNamingSystem: "numbers",
    operatingHours: [
      { day: "monday", isOpen: true, openTime: "09:00", closeTime: "22:00" },
      { day: "tuesday", isOpen: true, openTime: "09:00", closeTime: "22:00" },
      { day: "wednesday", isOpen: true, openTime: "09:00", closeTime: "22:00" },
      { day: "thursday", isOpen: true, openTime: "09:00", closeTime: "22:00" },
      { day: "friday", isOpen: true, openTime: "09:00", closeTime: "23:00" },
      { day: "saturday", isOpen: true, openTime: "10:00", closeTime: "23:00" },
      { day: "sunday", isOpen: true, openTime: "10:00", closeTime: "21:00" },
    ],
    cuisineTypes: [],
    priceRange: {
      min: 15,
      max: 45,
      currency: "GBP",
    },
    averageServiceTime: 60,
    totalSeatingCapacity: 0,
    estimatedDailyCovers: 0,
  });

  // Load registration data and check backend connectivity on component mount
  useEffect(() => {
    const storedData = localStorage.getItem('registrationData');
    if (storedData) {
      setRegistrationData(JSON.parse(storedData));
    } else {
      // Redirect back to registration if no data found
      navigate('/auth/register');
    }

    // Check backend connectivity
    const checkBackend = async () => {
      const isAvailable = await apiService.checkBackendConnectivity();
      setBackendAvailable(isAvailable);

      if (!isAvailable) {
        logger.warn('Backend unavailable during restaurant setup', 'RestaurantSetup');
      }
    };

    checkBackend();
  }, [navigate]);

  const updateSetupData = (field: string, value: any) => {
    logger.userAction(`setup field update: ${field}`, "RestaurantSetup", { field, hasValue: !!value });
    setSetupData(prev => ({ ...prev, [field]: value }));

    // Clear error when user makes changes
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  const updateOperatingHours = (day: string, field: string, value: any) => {
    logger.userAction(`operating hours update: ${day}.${field}`, "RestaurantSetup", { day, field, value });
    setSetupData(prev => ({
      ...prev,
      operatingHours: prev.operatingHours.map(hours =>
        hours.day === day ? { ...hours, [field]: value } : hours
      ),
    }));
  };

  const handleTablesChange = (tables: TableConfig[], totalCapacity: number) => {
    logger.dataOperation("update", "table configuration", "RestaurantSetup", { tableCount: tables.length, totalCapacity });
    setSetupData(prev => ({
      ...prev,
      tables,
      totalTables: tables.length,
      totalSeatingCapacity: totalCapacity,
      estimatedDailyCovers: Math.round(totalCapacity * 2.5), // Estimate 2.5 turns per day
    }));
  };

  const handleLogoChange = (file: File | null, url: string | null) => {
    logger.userAction("logo change", "RestaurantSetup", { hasFile: !!file, hasUrl: !!url });
    setSetupData(prev => ({
      ...prev,
      logo: file || undefined,
      logoUrl: url || undefined,
      useDefaultLogo: !file,
    }));
  };

  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {};

    if (step === 2) {
      if (setupData.tables.length === 0) {
        newErrors.tables = "Please configure at least one table";
      }
      if (setupData.totalSeatingCapacity < 4) {
        newErrors.capacity = "Minimum seating capacity is 4";
      }
    }

    if (step === 3) {
      if (setupData.cuisineTypes.length === 0) {
        newErrors.cuisineTypes = "Please select at least one cuisine type";
      }
      if (setupData.priceRange.min >= setupData.priceRange.max) {
        newErrors.priceRange = "Maximum price must be higher than minimum price";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => {
        const nextStep = Math.min(3, prev + 1);
        logger.userAction(`step navigation: ${prev} → ${nextStep}`, "RestaurantSetup");
        return nextStep;
      });
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => {
      const prevStep = Math.max(1, prev - 1);
      logger.userAction(`step navigation: ${prev} → ${prevStep}`, "RestaurantSetup");
      return prevStep;
    });
  };

  const handleSubmit = async () => {
    if (!validateStep(3)) return;

    setIsLoading(true);
    try {
      if (!registrationData) {
        throw new Error("Registration data not found");
      }

      let result: {
        success: boolean;
        credentials?: { restaurantCode: string; ownerPin: string };
        restaurant?: any;
        user?: any;
      };

      // Try backend registration first if available
      if (backendAvailable === true) {
        try {
          logger.info('Attempting backend restaurant registration', 'RestaurantSetup', {
            email: registrationData.email
          });

          // Generate unique restaurant code
          const codeResponse = await apiService.restaurant.generateCode(registrationData.restaurantName);
          const restaurantCode = codeResponse.code;

          // Generate owner PIN
          const ownerPin = Math.floor(1000 + Math.random() * 9000).toString();

          const credentials = { restaurantCode, ownerPin };

          // Transform registration data to match backend expectations
          const backendRegistrationData = {
            ...registrationData,
            agreeToTerms: registrationData.acceptTerms, // Map acceptTerms to agreeToTerms
          };

          // Remove the frontend-specific field
          delete (backendRegistrationData as any).acceptTerms;
          delete (backendRegistrationData as any).acceptPrivacy;

          // Transform setup data to match backend expectations
          const backendSetupData = {
            ...setupData,
            // Ensure tables are in the correct format for backend
            tables: setupData.tables.map((table, index) => ({
              id: table.id,
              name: `Table ${table.number || index + 1}`,
              capacity: table.capacity,
              location: "Main Floor"
            }))
          };

          // Remove any frontend-specific fields from setup data
          delete (backendSetupData as any).logo; // File objects can't be serialized

          // Log the data being sent for debugging
          console.log('Sending registration data to backend:', {
            registrationData: backendRegistrationData,
            setupData: backendSetupData,
            credentials
          });

          // Register with backend
          const backendResult = await apiService.restaurant.register(
            backendRegistrationData as any,
            backendSetupData as any,
            credentials
          );

          if (backendResult.success) {
            result = {
              success: true,
              credentials,
              restaurant: backendResult.restaurant,
              user: backendResult.user
            };
            logger.dataOperation("complete", "backend restaurant registration", "RestaurantSetup", {
              email: registrationData.email,
              restaurantCode: credentials.restaurantCode
            });
          } else {
            throw new Error("Backend registration failed");
          }
        } catch (apiError) {
          const errorInfo = handleApiError(apiError);
          logger.logError(apiError, "backend restaurant registration", "RestaurantSetup");

          // Log detailed error information for debugging
          console.error('Registration API Error Details:', {
            error: apiError,
            errorInfo,
            email: registrationData.email
          });

          if (errorInfo.isConnectivityError) {
            // Backend became unavailable, fall back to localStorage
            logger.warn('Backend became unavailable during setup, falling back to localStorage', 'RestaurantSetup');
            setBackendAvailable(false);

            // Fall through to localStorage fallback
            result = await updateUserSetupData(registrationData.email, setupData);
          } else {
            // Other API error - provide more specific error message
            throw new Error(`Registration failed: ${errorInfo.message}`);
          }
        }
      } else {
        // Use localStorage fallback
        logger.info('Using localStorage fallback for restaurant setup', 'RestaurantSetup');
        result = await updateUserSetupData(registrationData.email, setupData);
      }

      if (!result.success) {
        throw new Error("Failed to complete restaurant setup");
      }

      logger.dataOperation("complete", "restaurant setup", "RestaurantSetup", {
        email: registrationData.email,
        restaurantCode: result.credentials?.restaurantCode,
        method: backendAvailable ? "backend" : "localStorage"
      });

      // Store the generated credentials and prepare registration result
      if (result.credentials) {
        setGeneratedCredentials(result.credentials);

        // Refresh restaurant data in AuthContext to make new restaurant immediately available
        await refreshRestaurantData();

        logger.info("Restaurant data refreshed after registration", "RestaurantSetup", {
          restaurantCode: result.credentials.restaurantCode
        });

        // Prepare complete registration data for success page
        const registrationResult = {
          restaurant: {
            id: result.restaurant?.id || 'temp-id',
            name: registrationData.restaurantName,
            code: result.credentials.restaurantCode,
            email: registrationData.email,
            phone: registrationData.phone,
            address: `${registrationData.address.street}, ${registrationData.address.city}, ${registrationData.address.zipCode}`,
            ownerName: registrationData.ownerName
          },
          user: {
            id: result.user?.id || 'temp-user-id',
            name: registrationData.ownerName,
            email: registrationData.email,
            role: 'admin',
            pin: result.credentials.ownerPin
          },
          credentials: result.credentials
        };

        // Store registration result for success page
        localStorage.setItem("registrationResult", JSON.stringify(registrationResult));
        localStorage.removeItem("registrationData"); // Clean up temporary data

        // Navigate directly to success page
        navigate('/auth/registration-success', {
          state: { registrationData: registrationResult }
        });
        return;
      }

      // Store complete profile for immediate access (fallback)
      const completeProfile = {
        registrationData,
        setupData,
        isSetupComplete: true,
        createdAt: new Date().toISOString(),
        status: "active",
        credentials: result.credentials,
      };

      localStorage.setItem("restaurantProfile", JSON.stringify(completeProfile));
    } catch (error) {
      logger.logError(error, "restaurant setup", "RestaurantSetup");
      console.error('Setup Error:', error);

      // Provide more specific error message
      const errorMessage = error instanceof Error ? error.message : "Setup failed. Please try again.";
      setErrors({ submit: errorMessage });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle success modal close and navigation
  const handleSuccessModalClose = () => {
    setShowSuccessModal(false);
    // Navigate to login page instead of dashboard
    navigate("/restaurant-login");
  };

  const stepTitles = [
    "Logo & Branding",
    "Table Configuration",
    "Business Operations"
  ];

  const totalSteps = 3;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-4xl">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Setup Your Restaurant</h1>
          <p className="text-gray-600">
            Welcome to Promith! Let's configure your restaurant for optimal management.
          </p>
          {registrationData && (
            <p className="text-sm text-gray-500 mt-2">
              Setting up: <strong>{registrationData.restaurantName}</strong>
            </p>
          )}
        </div>

        {/* Progress Indicator */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            {[1, 2, 3].map((step) => (
              <div key={step} className="flex items-center">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium ${
                  step <= currentStep
                    ? "bg-blue-600 text-white"
                    : "bg-gray-200 text-gray-500"
                }`}>
                  {step < currentStep ? <CheckCircle className="w-5 h-5" /> : step}
                </div>
                {step < totalSteps && (
                  <div className={`flex-1 h-1 mx-4 ${
                    step < currentStep ? "bg-blue-600" : "bg-gray-200"
                  }`} />
                )}
              </div>
            ))}
          </div>
          <div className="text-center">
            <span className="text-sm text-gray-600">
              Step {currentStep} of {totalSteps}: {stepTitles[currentStep - 1]}
            </span>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {currentStep === 1 && <Settings className="h-5 w-5" />}
              {currentStep === 2 && <Users className="h-5 w-5" />}
              {currentStep === 3 && <Utensils className="h-5 w-5" />}
              {stepTitles[currentStep - 1]}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {/* Step 1: Logo & Branding */}
            {currentStep === 1 && (
              <div className="space-y-6">
                <LogoUpload
                  onLogoChange={handleLogoChange}
                  currentLogo={setupData.logoUrl}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="logoPosition">Logo Position</Label>
                    <Select
                      value={setupData.logoPosition}
                      onValueChange={(value: any) => updateSetupData("logoPosition", value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="left">Left Aligned</SelectItem>
                        <SelectItem value="center">Center Aligned</SelectItem>
                        <SelectItem value="right">Right Aligned</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="useDefaultLogo"
                      checked={setupData.useDefaultLogo}
                      onCheckedChange={(checked) => updateSetupData("useDefaultLogo", checked)}
                    />
                    <Label htmlFor="useDefaultLogo" className="text-sm">
                      Use default Promith logo if no custom logo is uploaded
                    </Label>
                  </div>
                </div>

                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Your logo will appear on receipts, reports, and the staff interface.
                    You can change it later in the settings.
                  </AlertDescription>
                </Alert>
              </div>
            )}

            {/* Step 2: Table Configuration */}
            {currentStep === 2 && (
              <div className="space-y-6">
                <TableConfiguration
                  onTablesChange={handleTablesChange}
                  initialTables={setupData.tables}
                />

                {errors.tables && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{errors.tables}</AlertDescription>
                  </Alert>
                )}

                {errors.capacity && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{errors.capacity}</AlertDescription>
                  </Alert>
                )}
              </div>
            )}

            {/* Step 3: Business Operations */}
            {currentStep === 3 && (
              <div className="space-y-8">
                {/* Operating Hours */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Clock className="h-5 w-5" />
                      Operating Hours
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {setupData.operatingHours.map((hours) => (
                        <div key={hours.day} className="grid grid-cols-1 md:grid-cols-4 gap-4 items-center">
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              checked={hours.isOpen}
                              onCheckedChange={(checked) => updateOperatingHours(hours.day, "isOpen", checked)}
                            />
                            <Label className="capitalize font-medium">{hours.day}</Label>
                          </div>

                          {hours.isOpen && (
                            <>
                              <div>
                                <Label className="text-sm">Open Time</Label>
                                <Input
                                  type="time"
                                  value={hours.openTime}
                                  onChange={(e) => updateOperatingHours(hours.day, "openTime", e.target.value)}
                                />
                              </div>

                              <div>
                                <Label className="text-sm">Close Time</Label>
                                <Input
                                  type="time"
                                  value={hours.closeTime}
                                  onChange={(e) => updateOperatingHours(hours.day, "closeTime", e.target.value)}
                                />
                              </div>

                              <div className="text-sm text-gray-500">
                                {hours.openTime} - {hours.closeTime}
                              </div>
                            </>
                          )}

                          {!hours.isOpen && (
                            <div className="md:col-span-3 text-sm text-gray-500">Closed</div>
                          )}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Cuisine Types */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Utensils className="h-5 w-5" />
                      Cuisine Types
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <Label>Select the types of cuisine you serve (multiple selection allowed):</Label>
                      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                        {CUISINE_TYPES.map((cuisine) => (
                          <div key={cuisine.id} className="flex items-center space-x-2">
                            <Checkbox
                              id={cuisine.id}
                              checked={setupData.cuisineTypes.includes(cuisine.id)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  updateSetupData("cuisineTypes", [...setupData.cuisineTypes, cuisine.id]);
                                } else {
                                  updateSetupData("cuisineTypes", setupData.cuisineTypes.filter(id => id !== cuisine.id));
                                }
                              }}
                            />
                            <Label htmlFor={cuisine.id} className="text-sm cursor-pointer">
                              {cuisine.name}
                            </Label>
                          </div>
                        ))}
                      </div>

                      {setupData.cuisineTypes.length > 0 && (
                        <div className="flex flex-wrap gap-2 mt-4">
                          {setupData.cuisineTypes.map((cuisineId) => {
                            const cuisine = CUISINE_TYPES.find(c => c.id === cuisineId);
                            return cuisine ? (
                              <Badge key={cuisineId} variant="secondary">
                                {cuisine.name}
                              </Badge>
                            ) : null;
                          })}
                        </div>
                      )}

                      {errors.cuisineTypes && (
                        <p className="text-sm text-red-600">{errors.cuisineTypes}</p>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Price Range */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <DollarSign className="h-5 w-5" />
                      Price Range
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      <div>
                        <Label>Average price range per person (£)</Label>
                        <div className="mt-4 space-y-4">
                          <div className="px-4">
                            <Slider
                              value={[setupData.priceRange.min, setupData.priceRange.max]}
                              onValueChange={([min, max]) => {
                                updateSetupData("priceRange", {
                                  ...setupData.priceRange,
                                  min,
                                  max,
                                });
                              }}
                              max={100}
                              min={5}
                              step={5}
                              className="w-full"
                            />
                          </div>

                          <div className="flex justify-between text-sm text-gray-600">
                            <span>£{setupData.priceRange.min}</span>
                            <span>£{setupData.priceRange.max}</span>
                          </div>

                          <div className="text-center">
                            <Badge variant="outline" className="text-lg px-4 py-2">
                              £{setupData.priceRange.min} - £{setupData.priceRange.max} per person
                            </Badge>
                          </div>
                        </div>

                        {errors.priceRange && (
                          <p className="text-sm text-red-600 mt-2">{errors.priceRange}</p>
                        )}
                      </div>

                      <div>
                        <Label htmlFor="serviceTime">Average Service Time (minutes)</Label>
                        <Input
                          id="serviceTime"
                          type="number"
                          min="30"
                          max="180"
                          value={setupData.averageServiceTime}
                          onChange={(e) => updateSetupData("averageServiceTime", parseInt(e.target.value) || 60)}
                          className="mt-1"
                        />
                        <p className="text-sm text-gray-500 mt-1">
                          How long does an average customer stay at your restaurant?
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Summary */}
                <Card>
                  <CardHeader>
                    <CardTitle>Setup Summary</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-600">{setupData.totalTables}</div>
                        <div className="text-sm text-gray-500">Tables</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">{setupData.totalSeatingCapacity}</div>
                        <div className="text-sm text-gray-500">Total Seats</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-purple-600">{setupData.estimatedDailyCovers}</div>
                        <div className="text-sm text-gray-500">Est. Daily Covers</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Error Display */}
            {errors.submit && (
              <Alert variant="destructive" className="mt-6">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{errors.submit}</AlertDescription>
              </Alert>
            )}

            {/* Navigation Buttons */}
            <div className="flex justify-between mt-8">
              <Button
                type="button"
                variant="outline"
                onClick={handlePrevious}
                disabled={currentStep === 1}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Previous
              </Button>

              {currentStep < totalSteps ? (
                <Button type="button" onClick={handleNext}>
                  Next
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              ) : (
                <Button onClick={handleSubmit} disabled={isLoading}>
                  {isLoading ? (
                    "Setting up..."
                  ) : (
                    <>
                      <Save className="w-4 h-4 mr-2" />
                      Complete Setup
                    </>
                  )}
                </Button>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center mt-6">
          <p className="text-sm text-gray-600">
            Need help? Contact our support team for assistance with setup.
          </p>
        </div>
      </div>

      {/* Success Modal */}
      <Dialog open={showSuccessModal} onOpenChange={setShowSuccessModal}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-green-600">
              <CheckCircle className="h-6 w-6" />
              Setup Complete!
            </DialogTitle>
            <DialogDescription>
              Your restaurant has been successfully set up and is ready to use.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h3 className="font-semibold text-green-800 mb-2">Your Restaurant Credentials</h3>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-green-700">Restaurant Code:</span>
                  <code className="bg-green-100 px-2 py-1 rounded text-green-800 font-mono">
                    {generatedCredentials?.restaurantCode}
                  </code>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-green-700">Owner PIN:</span>
                  <code className="bg-green-100 px-2 py-1 rounded text-green-800 font-mono">
                    {generatedCredentials?.ownerPin}
                  </code>
                </div>
              </div>
            </div>

            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <strong>Important:</strong> Save these credentials securely. You'll need the restaurant code to log in to your restaurant, and the owner PIN for administrative access.
              </AlertDescription>
            </Alert>

            <div className="text-sm text-gray-600">
              <p><strong>Next Steps:</strong></p>
              <ol className="list-decimal list-inside mt-2 space-y-1">
                <li>Use your restaurant code to log in to your restaurant</li>
                <li>Use your owner PIN to access the admin dashboard</li>
                <li>Start managing your restaurant operations</li>
              </ol>
            </div>
          </div>

          <div className="flex justify-end gap-3 mt-6">
            <Button onClick={handleSuccessModalClose} className="bg-green-600 hover:bg-green-700">
              Continue to Login
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default RestaurantSetup;
