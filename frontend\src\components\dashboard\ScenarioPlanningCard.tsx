import { useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>itle,
  CardDescription
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer, 
  Legend
} from 'recharts';
import { Slider } from "@/components/ui/slider";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  TrendingUp, 
  TrendingDown, 
  Percent, 
  Users, 
  DollarSign,
  Save,
  RefreshCw
} from "lucide-react";

interface ScenarioPlanningCardProps {
  baseRevenue?: number;
  baseCustomers?: number;
}

// Custom tooltip component
const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-background border rounded p-2 shadow-sm">
        <p className="font-medium">{label}%</p>
        {payload.map((entry: any, index: number) => (
          <p key={`item-${index}`} style={{ color: entry.color }}>
            {entry.name}: £{entry.value.toLocaleString()}
          </p>
        ))}
      </div>
    );
  }

  return null;
};

const ScenarioPlanningCard = ({ 
  baseRevenue = 10000, 
  baseCustomers = 500 
}: ScenarioPlanningCardProps) => {
  // State for scenario parameters
  const [priceChange, setPriceChange] = useState(0);
  const [customerChange, setCustomerChange] = useState(0);
  const [costChange, setCostChange] = useState(0);
  const [scenarioName, setScenarioName] = useState("New Scenario");
  
  // Calculate scenario outcomes
  const calculateScenarioData = () => {
    // Base values
    const baseCosts = baseRevenue * 0.6; // Assume 60% costs
    const baseProfit = baseRevenue - baseCosts;
    
    // Generate data points from -20% to +20% for the selected parameter
    const data = [];
    
    for (let i = -20; i <= 20; i += 5) {
      // Calculate revenue based on price and customer changes
      const adjustedCustomers = baseCustomers * (1 + (customerChange + i) / 100);
      const adjustedPrice = 1 + (priceChange + i) / 100;
      const adjustedRevenue = baseRevenue * adjustedPrice * (adjustedCustomers / baseCustomers);
      
      // Calculate costs with cost change
      const adjustedCosts = baseCosts * (1 + (costChange + i) / 100);
      
      // Calculate profit
      const adjustedProfit = adjustedRevenue - adjustedCosts;
      
      data.push({
        change: i,
        revenue: adjustedRevenue,
        profit: adjustedProfit,
        costs: adjustedCosts
      });
    }
    
    return data;
  };
  
  const scenarioData = calculateScenarioData();
  
  // Calculate summary metrics
  const baselineScenario = scenarioData.find(d => d.change === 0) || { 
    revenue: baseRevenue, 
    profit: baseRevenue * 0.4, 
    costs: baseRevenue * 0.6 
  };
  
  const revenueChange = ((baselineScenario.revenue - baseRevenue) / baseRevenue) * 100;
  const profitChange = ((baselineScenario.profit - (baseRevenue * 0.4)) / (baseRevenue * 0.4)) * 100;
  
  return (
    <Card className="col-span-2">
      <CardHeader>
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2">
          <div>
            <CardTitle>Scenario Planning</CardTitle>
            <CardDescription>
              Model different business scenarios and their financial impact
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Input 
              value={scenarioName}
              onChange={(e) => setScenarioName(e.target.value)}
              className="w-[200px]"
              placeholder="Scenario name"
            />
            <Button variant="outline" size="icon">
              <Save className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="parameters">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="parameters">Scenario Parameters</TabsTrigger>
            <TabsTrigger value="results">Scenario Results</TabsTrigger>
          </TabsList>
          
          <TabsContent value="parameters" className="space-y-6 py-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <Label>Price Change</Label>
                  <div className="flex items-center text-sm font-medium">
                    {priceChange > 0 ? (
                      <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                    ) : priceChange < 0 ? (
                      <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
                    ) : (
                      <Percent className="h-4 w-4 text-muted-foreground mr-1" />
                    )}
                    {priceChange > 0 ? '+' : ''}{priceChange}%
                  </div>
                </div>
                <Slider
                  value={[priceChange]}
                  min={-20}
                  max={20}
                  step={1}
                  onValueChange={(value) => setPriceChange(value[0])}
                />
                <div className="text-sm text-muted-foreground">
                  Adjust menu prices up or down
                </div>
              </div>
              
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <Label>Customer Volume</Label>
                  <div className="flex items-center text-sm font-medium">
                    {customerChange > 0 ? (
                      <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                    ) : customerChange < 0 ? (
                      <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
                    ) : (
                      <Users className="h-4 w-4 text-muted-foreground mr-1" />
                    )}
                    {customerChange > 0 ? '+' : ''}{customerChange}%
                  </div>
                </div>
                <Slider
                  value={[customerChange]}
                  min={-20}
                  max={20}
                  step={1}
                  onValueChange={(value) => setCustomerChange(value[0])}
                />
                <div className="text-sm text-muted-foreground">
                  Estimate changes in customer volume
                </div>
              </div>
              
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <Label>Cost Change</Label>
                  <div className="flex items-center text-sm font-medium">
                    {costChange > 0 ? (
                      <TrendingUp className="h-4 w-4 text-red-500 mr-1" />
                    ) : costChange < 0 ? (
                      <TrendingDown className="h-4 w-4 text-green-500 mr-1" />
                    ) : (
                      <DollarSign className="h-4 w-4 text-muted-foreground mr-1" />
                    )}
                    {costChange > 0 ? '+' : ''}{costChange}%
                  </div>
                </div>
                <Slider
                  value={[costChange]}
                  min={-20}
                  max={20}
                  step={1}
                  onValueChange={(value) => setCostChange(value[0])}
                />
                <div className="text-sm text-muted-foreground">
                  Adjust operational costs
                </div>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="text-sm font-medium">Revenue Impact</div>
                    <div className={`text-sm font-medium ${revenueChange > 0 ? 'text-green-500' : revenueChange < 0 ? 'text-red-500' : ''}`}>
                      {revenueChange > 0 ? '+' : ''}{revenueChange.toFixed(1)}%
                    </div>
                  </div>
                  <div className="text-2xl font-bold mt-2">
                    £{baselineScenario.revenue.toLocaleString(undefined, { maximumFractionDigits: 0 })}
                  </div>
                  <div className="text-sm text-muted-foreground mt-1">
                    vs £{baseRevenue.toLocaleString(undefined, { maximumFractionDigits: 0 })} baseline
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="text-sm font-medium">Profit Impact</div>
                    <div className={`text-sm font-medium ${profitChange > 0 ? 'text-green-500' : profitChange < 0 ? 'text-red-500' : ''}`}>
                      {profitChange > 0 ? '+' : ''}{profitChange.toFixed(1)}%
                    </div>
                  </div>
                  <div className="text-2xl font-bold mt-2">
                    £{baselineScenario.profit.toLocaleString(undefined, { maximumFractionDigits: 0 })}
                  </div>
                  <div className="text-sm text-muted-foreground mt-1">
                    vs £{(baseRevenue * 0.4).toLocaleString(undefined, { maximumFractionDigits: 0 })} baseline
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="text-sm font-medium">Profit Margin</div>
                    <div className={`text-sm font-medium ${
                      (baselineScenario.profit / baselineScenario.revenue) > 0.4 ? 'text-green-500' : 
                      (baselineScenario.profit / baselineScenario.revenue) < 0.4 ? 'text-red-500' : ''
                    }`}>
                      {((baselineScenario.profit / baselineScenario.revenue) * 100).toFixed(1)}%
                    </div>
                  </div>
                  <div className="text-2xl font-bold mt-2">
                    {((baselineScenario.profit / baselineScenario.revenue) * 100).toFixed(1)}%
                  </div>
                  <div className="text-sm text-muted-foreground mt-1">
                    vs 40.0% baseline
                  </div>
                </CardContent>
              </Card>
            </div>
            
            <div className="flex justify-end mt-4">
              <Button variant="outline" size="sm" className="mr-2">
                <RefreshCw className="h-4 w-4 mr-2" />
                Reset
              </Button>
              <Button size="sm">
                Apply Scenario
              </Button>
            </div>
          </TabsContent>
          
          <TabsContent value="results" className="py-4">
            <div className="h-[350px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={scenarioData}
                  margin={{ top: 10, right: 10, left: 10, bottom: 20 }}
                >
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis
                    dataKey="change"
                    tickFormatter={(value) => `${value > 0 ? '+' : ''}${value}%`}
                    tick={{ fontSize: 12 }}
                    tickLine={false}
                    axisLine={false}
                  />
                  <YAxis
                    tickFormatter={(value) => `£${(value / 1000).toFixed(0)}k`}
                    tick={{ fontSize: 12 }}
                    tickLine={false}
                    axisLine={false}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="revenue"
                    name="Revenue"
                    stroke="#3b82f6"
                    strokeWidth={2}
                    dot={{ r: 4 }}
                    activeDot={{ r: 6 }}
                  />
                  <Line
                    type="monotone"
                    dataKey="profit"
                    name="Profit"
                    stroke="#10b981"
                    strokeWidth={2}
                    dot={{ r: 4 }}
                    activeDot={{ r: 6 }}
                  />
                  <Line
                    type="monotone"
                    dataKey="costs"
                    name="Costs"
                    stroke="#f43f5e"
                    strokeWidth={2}
                    dot={{ r: 4 }}
                    activeDot={{ r: 6 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
            
            <div className="mt-4 text-sm text-muted-foreground">
              <p>This chart shows how changes in the selected parameters affect your financial outcomes. The x-axis represents percentage change, and the y-axis shows the resulting financial values.</p>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default ScenarioPlanningCard;