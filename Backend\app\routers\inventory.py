from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import List, Optional
from datetime import datetime
from app.models.inventory import InventoryItem, InventoryItemCreate
from app.utils.storage import get_all, get_by_id, create, update, delete, query
from app.utils.auth import get_current_active_user, check_manager_role
from app.constants.allergens import VALID_ALLERGENS, ALLERGEN_DESCRIPTIONS

router = APIRouter(prefix="/inventory", tags=["Inventory"])

@router.get("/", response_model=List[InventoryItem])
async def get_inventory_items(
    allergens: Optional[str] = Query(None, description="Filter by allergens (comma-separated)"),
    category: Optional[str] = Query(None, description="Filter by category"),
    current_user = Depends(get_current_active_user)
):
    """Get all inventory items with optional filtering"""
    items = get_all("inventory")

    # Filter by allergens if provided
    if allergens:
        allergen_list = [a.strip().lower() for a in allergens.split(",")]
        filtered_items = []
        for item in items:
            item_allergens = [a.lower() for a in (item.get("allergens") or [])]
            if any(allergen in item_allergens for allergen in allergen_list):
                filtered_items.append(item)
        items = filtered_items

    # Filter by category if provided
    if category:
        items = [item for item in items if item.get("category", "").lower() == category.lower()]

    return items

@router.get("/allergens")
async def get_valid_allergens():
    """Get list of valid allergens with descriptions"""
    return {
        "allergens": VALID_ALLERGENS,
        "descriptions": ALLERGEN_DESCRIPTIONS
    }

@router.get("/{item_id}", response_model=InventoryItem)
async def get_inventory_item(
    item_id: str,
    current_user = Depends(get_current_active_user)
):
    """Get an inventory item by ID"""
    item = get_by_id("inventory", item_id)
    if not item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Inventory item not found"
        )
    return item

@router.post("/", response_model=InventoryItem)
async def create_inventory_item(
    item: InventoryItemCreate,
    current_user = Depends(get_current_active_user)
):
    """Create a new inventory item"""
    # Check if user has manager or admin role
    check_manager_role(current_user)

    # Create item dict
    item_dict = item.model_dump()
    item_dict["last_restocked"] = datetime.now().isoformat()

    return create("inventory", item_dict)

@router.put("/{item_id}", response_model=InventoryItem)
async def update_inventory_item(
    item_id: str,
    item: InventoryItemCreate,
    current_user = Depends(get_current_active_user)
):
    """Update an inventory item"""
    # Check if user has manager or admin role
    check_manager_role(current_user)

    # Check if item exists
    existing_item = get_by_id("inventory", item_id)
    if not existing_item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Inventory item not found"
        )

    updated_item = update("inventory", item_id, item.model_dump())
    return updated_item

@router.put("/{item_id}/restock", response_model=InventoryItem)
async def restock_inventory_item(
    item_id: str,
    quantity: float,
    current_user = Depends(get_current_active_user)
):
    """Restock an inventory item"""
    # Check if item exists
    item = get_by_id("inventory", item_id)
    if not item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Inventory item not found"
        )

    # Update quantity and last_restocked
    new_quantity = item["quantity"] + quantity
    updates = {
        "quantity": new_quantity,
        "last_restocked": datetime.now().isoformat()
    }

    updated_item = update("inventory", item_id, updates)
    return updated_item

@router.delete("/{item_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_inventory_item(
    item_id: str,
    current_user = Depends(get_current_active_user)
):
    """Delete an inventory item"""
    # Check if user has manager or admin role
    check_manager_role(current_user)

    # Check if item exists
    existing_item = get_by_id("inventory", item_id)
    if not existing_item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Inventory item not found"
        )

    success = delete("inventory", item_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete inventory item"
        )
