
@media (max-width: 768px) {
  #root {
    max-width: 100%;
    margin: 0;
    padding: 0;
  }
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

/* Responsive utilities */
.scroll-hidden {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.scroll-hidden::-webkit-scrollbar {
  display: none;
}

/* Mobile adaptations */
@media (max-width: 640px) {
  .mobile-full-width {
    width: 100% !important;
    max-width: 100% !important;
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .mobile-stack {
    flex-direction: column !important;
  }
  
  .mobile-p-4 {
    padding: 1rem !important;
  }
  
  .mobile-text-sm {
    font-size: 0.875rem !important;
    line-height: 1.25rem !important;
  }
  
  .mobile-text-base {
    font-size: 1rem !important;
    line-height: 1.5rem !important;
  }
}
