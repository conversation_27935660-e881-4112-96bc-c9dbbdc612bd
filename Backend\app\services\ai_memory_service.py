"""
AI Conversational Memory Service for RestroManage
Provides Redis-based conversation context persistence and session management
for maintaining context across multiple AI interactions.
"""

import json
from typing import Dict, Any, List, Optional
from datetime import datetime
import redis.asyncio as aioredis
from dataclasses import dataclass
from app.utils.logging_config import logger
from app.config import settings


@dataclass
class ConversationMessage:
    """Represents a single message in a conversation"""
    role: str  # 'user' or 'assistant'
    content: str
    timestamp: datetime
    function_calls: Optional[List[Dict[str, Any]]] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class ConversationSession:
    """Represents a conversation session"""
    session_id: str
    restaurant_id: str
    user_id: Optional[str]
    messages: List[ConversationMessage]
    created_at: datetime
    last_activity: datetime
    context: Dict[str, Any]


class AIMemoryService:
    """Service for managing AI conversation memory with Redis"""

    def __init__(self):
        self.redis_url = settings.REDIS_URL
        self.redis_client = None
        self.session_ttl = 3600 * 24  # 24 hours
        self.max_messages_per_session = 50
        self.enabled = False
        self._initialization_task = None
        self._initialized = False

        # Don't initialize Redis connection here - do it lazily when needed
    
    async def _ensure_initialized(self):
        """Ensure Redis connection is initialized (lazy initialization)"""
        if self._initialized:
            return

        if self._initialization_task is None:
            self._initialization_task = self._initialize_redis()

        await self._initialization_task

    async def _initialize_redis(self):
        """Initialize Redis connection"""
        if self._initialized:
            return

        try:
            if self.redis_url:
                self.redis_client = aioredis.from_url(
                    self.redis_url,
                    encoding="utf-8",
                    decode_responses=True
                )

                # Test connection
                await self.redis_client.ping()
                self.enabled = True

                logger.info("AI Memory Service initialized successfully", "AIMemory", {
                    "redis_url": self.redis_url.split('@')[-1],  # Hide credentials
                    "session_ttl": self.session_ttl,
                    "max_messages": self.max_messages_per_session
                })
            else:
                logger.warning("Redis URL not configured. AI memory will be disabled.", "AIMemory")

        except Exception as e:
            self.enabled = False
            logger.error("Failed to initialize AI Memory Service", "AIMemory", {"error": str(e)})
        finally:
            self._initialized = True
    
    async def is_enabled(self) -> bool:
        """Check if memory service is enabled"""
        await self._ensure_initialized()
        return self.enabled and self.redis_client is not None
    
    def _session_key(self, session_id: str) -> str:
        """Generate Redis key for session"""
        return f"ai_session:{session_id}"
    
    def _user_sessions_key(self, restaurant_id: str, user_id: str) -> str:
        """Generate Redis key for user's sessions list"""
        return f"ai_user_sessions:{restaurant_id}:{user_id}"
    
    async def create_session(
        self, 
        restaurant_id: str, 
        user_id: Optional[str] = None,
        initial_context: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Create a new conversation session.

        Args:
            restaurant_id: Restaurant ID for multi-tenant isolation
            user_id: Optional user ID
            initial_context: Initial context for the session

        Returns:
            Session ID
        """
        await self._ensure_initialized()
        if not await self.is_enabled():
            return f"local_session_{datetime.now().timestamp()}"
        
        try:
            import uuid
            session_id = str(uuid.uuid4())
            
            session = ConversationSession(
                session_id=session_id,
                restaurant_id=restaurant_id,
                user_id=user_id,
                messages=[],
                created_at=datetime.now(),
                last_activity=datetime.now(),
                context=initial_context or {}
            )
            
            # Store session in Redis
            session_data = {
                "session_id": session.session_id,
                "restaurant_id": session.restaurant_id,
                "user_id": session.user_id,
                "messages": [],
                "created_at": session.created_at.isoformat(),
                "last_activity": session.last_activity.isoformat(),
                "context": json.dumps(session.context)
            }
            
            await self.redis_client.hset(
                self._session_key(session_id),
                mapping=session_data
            )
            
            # Set TTL
            await self.redis_client.expire(
                self._session_key(session_id),
                self.session_ttl
            )
            
            # Add to user's sessions list if user_id provided
            if user_id:
                await self.redis_client.sadd(
                    self._user_sessions_key(restaurant_id, user_id),
                    session_id
                )
                await self.redis_client.expire(
                    self._user_sessions_key(restaurant_id, user_id),
                    self.session_ttl
                )
            
            logger.info("Created new AI conversation session", "AIMemory", {
                "session_id": session_id,
                "restaurant_id": restaurant_id,
                "user_id": user_id
            })
            
            return session_id
            
        except Exception as e:
            logger.error("Error creating conversation session", "AIMemory", {
                "restaurant_id": restaurant_id,
                "user_id": user_id,
                "error": str(e)
            })
            return f"local_session_{datetime.now().timestamp()}"
    
    async def add_message(
        self,
        session_id: str,
        role: str,
        content: str,
        function_calls: Optional[List[Dict[str, Any]]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Add a message to a conversation session.

        Args:
            session_id: Session ID
            role: Message role ('user' or 'assistant')
            content: Message content
            function_calls: Optional function calls made
            metadata: Optional metadata

        Returns:
            Success status
        """
        await self._ensure_initialized()
        if not await self.is_enabled():
            return False
        
        try:
            # Get current session
            session_data = await self.redis_client.hgetall(self._session_key(session_id))
            
            if not session_data:
                logger.warning("Session not found", "AIMemory", {"session_id": session_id})
                return False
            
            # Parse existing messages
            messages = json.loads(session_data.get("messages", "[]"))
            
            # Create new message
            message = {
                "role": role,
                "content": content,
                "timestamp": datetime.now().isoformat(),
                "function_calls": function_calls,
                "metadata": metadata
            }
            
            # Add message and maintain max limit
            messages.append(message)
            if len(messages) > self.max_messages_per_session:
                # Remove oldest messages, keeping the first one (system context)
                messages = messages[:1] + messages[-(self.max_messages_per_session-1):]
            
            # Update session
            await self.redis_client.hset(
                self._session_key(session_id),
                mapping={
                    "messages": json.dumps(messages),
                    "last_activity": datetime.now().isoformat()
                }
            )
            
            # Refresh TTL
            await self.redis_client.expire(
                self._session_key(session_id),
                self.session_ttl
            )
            
            logger.debug("Added message to conversation", "AIMemory", {
                "session_id": session_id,
                "role": role,
                "content_length": len(content),
                "total_messages": len(messages)
            })
            
            return True
            
        except Exception as e:
            logger.error("Error adding message to session", "AIMemory", {
                "session_id": session_id,
                "role": role,
                "error": str(e)
            })
            return False
    
    async def get_session(self, session_id: str) -> Optional[ConversationSession]:
        """
        Get a conversation session by ID.

        Args:
            session_id: Session ID

        Returns:
            ConversationSession or None if not found
        """
        await self._ensure_initialized()
        if not await self.is_enabled():
            return None
        
        try:
            session_data = await self.redis_client.hgetall(self._session_key(session_id))
            
            if not session_data:
                return None
            
            # Parse messages
            messages_data = json.loads(session_data.get("messages", "[]"))
            messages = [
                ConversationMessage(
                    role=msg["role"],
                    content=msg["content"],
                    timestamp=datetime.fromisoformat(msg["timestamp"]),
                    function_calls=msg.get("function_calls"),
                    metadata=msg.get("metadata")
                )
                for msg in messages_data
            ]
            
            # Create session object
            session = ConversationSession(
                session_id=session_data["session_id"],
                restaurant_id=session_data["restaurant_id"],
                user_id=session_data.get("user_id"),
                messages=messages,
                created_at=datetime.fromisoformat(session_data["created_at"]),
                last_activity=datetime.fromisoformat(session_data["last_activity"]),
                context=json.loads(session_data.get("context", "{}"))
            )
            
            return session
            
        except Exception as e:
            logger.error("Error getting conversation session", "AIMemory", {
                "session_id": session_id,
                "error": str(e)
            })
            return None


    async def get_conversation_context(self, session_id: str) -> List[Dict[str, Any]]:
        """
        Get conversation context for AI model.

        Args:
            session_id: Session ID

        Returns:
            List of messages formatted for AI model
        """
        session = await self.get_session(session_id)

        if not session:
            return []

        # Format messages for AI model
        context = []
        for message in session.messages:
            context.append({
                "role": message.role,
                "content": message.content,
                "timestamp": message.timestamp.isoformat()
            })

        return context

    async def update_session_context(
        self,
        session_id: str,
        context_updates: Dict[str, Any]
    ) -> bool:
        """
        Update session context.

        Args:
            session_id: Session ID
            context_updates: Context updates to merge

        Returns:
            Success status
        """
        await self._ensure_initialized()
        if not await self.is_enabled():
            return False

        try:
            session_data = await self.redis_client.hgetall(self._session_key(session_id))

            if not session_data:
                return False

            # Merge context
            current_context = json.loads(session_data.get("context", "{}"))
            current_context.update(context_updates)

            # Update session
            await self.redis_client.hset(
                self._session_key(session_id),
                mapping={
                    "context": json.dumps(current_context),
                    "last_activity": datetime.now().isoformat()
                }
            )

            return True

        except Exception as e:
            logger.error("Error updating session context", "AIMemory", {
                "session_id": session_id,
                "error": str(e)
            })
            return False

    async def get_user_sessions(
        self,
        restaurant_id: str,
        user_id: str,
        limit: int = 10
    ) -> List[str]:
        """
        Get user's recent sessions.

        Args:
            restaurant_id: Restaurant ID
            user_id: User ID
            limit: Maximum number of sessions to return

        Returns:
            List of session IDs
        """
        await self._ensure_initialized()
        if not await self.is_enabled():
            return []

        try:
            sessions = await self.redis_client.smembers(
                self._user_sessions_key(restaurant_id, user_id)
            )

            # Sort by last activity (would need additional data structure for this)
            # For now, return as is
            return list(sessions)[:limit]

        except Exception as e:
            logger.error("Error getting user sessions", "AIMemory", {
                "restaurant_id": restaurant_id,
                "user_id": user_id,
                "error": str(e)
            })
            return []

    async def delete_session(self, session_id: str) -> bool:
        """
        Delete a conversation session.

        Args:
            session_id: Session ID

        Returns:
            Success status
        """
        await self._ensure_initialized()
        if not await self.is_enabled():
            return False

        try:
            # Get session data first to clean up user sessions
            session_data = await self.redis_client.hgetall(self._session_key(session_id))

            if session_data:
                restaurant_id = session_data.get("restaurant_id")
                user_id = session_data.get("user_id")

                # Remove from user sessions if applicable
                if restaurant_id and user_id:
                    await self.redis_client.srem(
                        self._user_sessions_key(restaurant_id, user_id),
                        session_id
                    )

            # Delete session
            result = await self.redis_client.delete(self._session_key(session_id))

            logger.info("Deleted conversation session", "AIMemory", {
                "session_id": session_id,
                "success": bool(result)
            })

            return bool(result)

        except Exception as e:
            logger.error("Error deleting session", "AIMemory", {
                "session_id": session_id,
                "error": str(e)
            })
            return False

    async def cleanup_expired_sessions(self) -> int:
        """
        Clean up expired sessions (maintenance task).

        Returns:
            Number of sessions cleaned up
        """
        await self._ensure_initialized()
        if not await self.is_enabled():
            return 0

        try:
            # This is a simplified cleanup - in production, you might want
            # to use Redis SCAN for better performance
            pattern = "ai_session:*"
            keys = await self.redis_client.keys(pattern)

            cleaned_count = 0
            for key in keys:
                ttl = await self.redis_client.ttl(key)
                if ttl == -1:  # No TTL set
                    await self.redis_client.expire(key, self.session_ttl)
                elif ttl == -2:  # Key doesn't exist
                    cleaned_count += 1

            logger.info("Cleaned up expired AI sessions", "AIMemory", {
                "cleaned_count": cleaned_count,
                "total_checked": len(keys)
            })

            return cleaned_count

        except Exception as e:
            logger.error("Error cleaning up expired sessions", "AIMemory", {
                "error": str(e)
            })
            return 0

    async def get_memory_stats(self) -> Dict[str, Any]:
        """
        Get memory service statistics.

        Returns:
            Dictionary with memory statistics
        """
        await self._ensure_initialized()
        if not await self.is_enabled():
            return {
                "enabled": False,
                "error": "Memory service not enabled"
            }

        try:
            # Count sessions
            session_keys = await self.redis_client.keys("ai_session:*")
            user_session_keys = await self.redis_client.keys("ai_user_sessions:*")

            # Get Redis info
            redis_info = await self.redis_client.info("memory")

            return {
                "enabled": True,
                "total_sessions": len(session_keys),
                "total_user_session_lists": len(user_session_keys),
                "session_ttl_seconds": self.session_ttl,
                "max_messages_per_session": self.max_messages_per_session,
                "redis_memory_used": redis_info.get("used_memory_human", "unknown"),
                "redis_connected_clients": redis_info.get("connected_clients", 0)
            }

        except Exception as e:
            logger.error("Error getting memory stats", "AIMemory", {
                "error": str(e)
            })
            return {
                "enabled": True,
                "error": str(e)
            }


# Global instance
ai_memory_service = AIMemoryService()
