"""
Analytics Service for RestroManage
Provides database-driven analytics with multi-tenant support
"""

from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, desc, text
import logging

from app.database import get_db_session_context
from app.models.database_models import Order, InventoryItem

logger = logging.getLogger(__name__)

class AnalyticsService:
    """Service for generating analytics data from the database"""
    
    def __init__(self):
        self.cache_ttl = 900  # 15 minutes cache TTL
    
    async def get_sales_data(
        self,
        restaurant_id: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Get sales data for a restaurant within date range"""
        try:
            async with get_db_session_context() as session:
                # Use raw SQL to avoid model relationship issues
                sql_query = """
                    SELECT
                        DATE(created_at) as date,
                        SUM(total) as sales,
                        COUNT(id) as orders
                    FROM orders
                    WHERE restaurant_id = :restaurant_id
                    AND status != 'cancelled'
                """

                params = {"restaurant_id": restaurant_id}

                if start_date:
                    sql_query += " AND created_at >= :start_date"
                    params["start_date"] = start_date
                if end_date:
                    sql_query += " AND created_at <= :end_date"
                    params["end_date"] = end_date

                sql_query += " GROUP BY DATE(created_at) ORDER BY DATE(created_at)"

                result = await session.execute(text(sql_query), params)
                sales_data = []

                for row in result:
                    sales_data.append({
                        "date": row.date,
                        "sales": float(row.sales or 0),
                        "orders": int(row.orders or 0)
                    })

                # If no data, generate last 30 days with zeros
                if not sales_data and not start_date:
                    sales_data = await self._generate_empty_sales_data()

                return sorted(sales_data, key=lambda x: x['date'])

        except Exception as e:
            logger.error(f"Error getting sales data for restaurant {restaurant_id}: {e}")
            return await self._generate_empty_sales_data()
    
    async def get_popular_items(
        self,
        restaurant_id: str,
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """Get most popular menu items for a restaurant"""
        try:
            async with get_db_session_context() as session:
                # Use raw SQL to get popular items
                sql_query = """
                    SELECT
                        mi.id,
                        mi.name,
                        mi.category,
                        mi.price,
                        SUM(oi.quantity) as order_count
                    FROM order_items oi
                    JOIN menu_items mi ON oi.menu_item_id = mi.id
                    JOIN orders o ON oi.order_id = o.id
                    WHERE o.restaurant_id = :restaurant_id
                    AND o.status != 'cancelled'
                    GROUP BY mi.id, mi.name, mi.category, mi.price
                    ORDER BY order_count DESC
                    LIMIT :limit
                """

                result = await session.execute(text(sql_query), {
                    "restaurant_id": restaurant_id,
                    "limit": limit
                })

                popular_items = []
                items_data = result.fetchall()
                total_orders = sum(item.order_count for item in items_data) if items_data else 0

                for item in items_data:
                    percentage = round((item.order_count / total_orders) * 100) if total_orders > 0 else 0
                    popular_items.append({
                        "id": item.id,
                        "name": item.name,
                        "category": item.category,
                        "price": float(item.price),
                        "orderCount": int(item.order_count),
                        "percentageOfSales": percentage,
                        "trend": 5  # TODO: Calculate actual trend from historical data
                    })

                return popular_items

        except Exception as e:
            logger.error(f"Error getting popular items for restaurant {restaurant_id}: {e}")
            return []
    
    async def get_revenue_data(
        self, 
        restaurant_id: str, 
        period_type: str = "weekly"
    ) -> List[Dict[str, Any]]:
        """Get revenue data by period for a restaurant"""
        try:
            async with get_db_session_context() as session:
                if period_type == "weekly":
                    # Group by week
                    query = select(
                        func.strftime('%Y-W%W', Order.created_at).label('period'),
                        func.sum(Order.total).label('revenue')
                    ).where(
                        and_(
                            Order.restaurant_id == restaurant_id,
                            Order.status != 'cancelled'
                        )
                    ).group_by(func.strftime('%Y-W%W', Order.created_at))
                else:
                    # Group by month
                    query = select(
                        func.strftime('%Y-%m', Order.created_at).label('period'),
                        func.sum(Order.total).label('revenue')
                    ).where(
                        and_(
                            Order.restaurant_id == restaurant_id,
                            Order.status != 'cancelled'
                        )
                    ).group_by(func.strftime('%Y-%m', Order.created_at))
                
                result = await session.execute(query)
                revenue_data = []
                
                for row in result:
                    revenue_data.append({
                        "period": row.period,
                        "revenue": float(row.revenue or 0)
                    })
                
                return revenue_data
                
        except Exception as e:
            logger.error(f"Error getting revenue data for restaurant {restaurant_id}: {e}")
            return []
    
    async def _generate_empty_sales_data(self) -> List[Dict[str, Any]]:
        """Generate empty sales data for last 30 days"""
        sales_data = []
        today = datetime.now()

        for i in range(30, 0, -1):
            date = today - timedelta(days=i)
            sales_data.append({
                "date": date.strftime("%Y-%m-%d"),
                "sales": 0.0,
                "orders": 0
            })

        return sales_data

    async def get_dashboard_metrics(
        self,
        restaurant_id: str,
        date_range: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """Get dashboard metrics for a restaurant"""
        try:
            async with get_db_session_context() as session:
                # Get recent sales data
                sales_data = await self.get_sales_data(restaurant_id)
                recent_sales = sales_data[-7:] if sales_data else []

                # Get revenue data
                weekly_revenue = await self.get_revenue_data(restaurant_id, "weekly")
                monthly_revenue = await self.get_revenue_data(restaurant_id, "monthly")

                # Generate forecast data (simplified for now)
                forecast_data = await self._generate_forecast_data(restaurant_id)

                return {
                    "daily_sales": recent_sales,
                    "weekly_revenue": weekly_revenue[-4:],  # Last 4 weeks
                    "monthly_revenue": monthly_revenue[-6:],  # Last 6 months
                    "forecast": forecast_data
                }

        except Exception as e:
            logger.error(f"Error getting dashboard metrics for restaurant {restaurant_id}: {e}")
            return {
                "daily_sales": [],
                "weekly_revenue": [],
                "monthly_revenue": [],
                "forecast": []
            }

    async def _generate_forecast_data(self, restaurant_id: str) -> List[Dict[str, Any]]:
        """Generate advanced forecast data based on historical trends and patterns"""
        try:
            async with get_db_session_context() as session:
                # Get comprehensive historical data for better forecasting
                historical_query = text("""
                    SELECT
                        DATE(created_at) as date,
                        strftime('%w', created_at) as weekday,
                        strftime('%H', created_at) as hour,
                        SUM(total) as daily_revenue,
                        COUNT(id) as daily_orders,
                        AVG(total) as avg_order_value
                    FROM orders
                    WHERE restaurant_id = :restaurant_id
                    AND status != 'cancelled'
                    AND created_at >= date('now', '-90 days')
                    GROUP BY DATE(created_at)
                    ORDER BY date
                """)

                result = await session.execute(historical_query, {"restaurant_id": restaurant_id})
                historical_data = result.fetchall()

                if not historical_data:
                    return await self._generate_fallback_forecast()

                # Calculate advanced metrics
                weekday_patterns = self._analyze_weekday_patterns(historical_data)
                seasonal_trends = self._analyze_seasonal_trends(historical_data)
                growth_rate = self._calculate_growth_rate(historical_data)

                forecast_data = []
                today = datetime.now()

                for i in range(1, 8):  # Next 7 days
                    date = today + timedelta(days=i)
                    weekday = date.weekday()
                    day_name = date.strftime("%A")

                    # Apply advanced forecasting
                    base_revenue = weekday_patterns.get(weekday, {}).get('avg_revenue', 1500)
                    base_orders = weekday_patterns.get(weekday, {}).get('avg_orders', 15)

                    # Apply seasonal adjustment
                    seasonal_factor = seasonal_trends.get(date.month, 1.0)

                    # Apply growth trend
                    growth_factor = 1 + (growth_rate * (i / 365))  # Project growth

                    # Add confidence based on data quality
                    confidence = min(95, 70 + len(historical_data))

                    projected_revenue = base_revenue * seasonal_factor * growth_factor
                    projected_orders = int(base_orders * seasonal_factor * growth_factor)

                    forecast_data.append({
                        "day": day_name,
                        "customers": projected_orders,
                        "projectedRevenue": round(projected_revenue, 2),
                        "confidence": confidence,
                        "seasonalFactor": round(seasonal_factor, 2),
                        "growthFactor": round(growth_factor, 3)
                    })

                return forecast_data

        except Exception as e:
            logger.error(f"Error generating advanced forecast data for restaurant {restaurant_id}: {e}")
            return await self._generate_fallback_forecast()

    def _analyze_weekday_patterns(self, historical_data) -> Dict[int, Dict[str, float]]:
        """Analyze patterns by weekday"""
        weekday_stats = {}

        for row in historical_data:
            weekday = int(row.weekday)
            if weekday not in weekday_stats:
                weekday_stats[weekday] = {'revenues': [], 'orders': []}

            weekday_stats[weekday]['revenues'].append(float(row.daily_revenue or 0))
            weekday_stats[weekday]['orders'].append(int(row.daily_orders or 0))

        # Calculate averages
        patterns = {}
        for weekday, stats in weekday_stats.items():
            if stats['revenues']:
                patterns[weekday] = {
                    'avg_revenue': sum(stats['revenues']) / len(stats['revenues']),
                    'avg_orders': sum(stats['orders']) / len(stats['orders'])
                }

        return patterns

    def _analyze_seasonal_trends(self, historical_data) -> Dict[int, float]:
        """Analyze seasonal trends by month"""
        monthly_stats = {}

        for row in historical_data:
            date_obj = datetime.strptime(row.date, '%Y-%m-%d')
            month = date_obj.month

            if month not in monthly_stats:
                monthly_stats[month] = []

            monthly_stats[month].append(float(row.daily_revenue or 0))

        # Calculate seasonal factors relative to average
        all_revenues = [rev for month_revs in monthly_stats.values() for rev in month_revs]
        overall_avg = sum(all_revenues) / len(all_revenues) if all_revenues else 1500

        seasonal_factors = {}
        for month, revenues in monthly_stats.items():
            if revenues:
                month_avg = sum(revenues) / len(revenues)
                seasonal_factors[month] = month_avg / overall_avg

        return seasonal_factors

    def _calculate_growth_rate(self, historical_data) -> float:
        """Calculate growth rate from historical data"""
        if len(historical_data) < 14:  # Need at least 2 weeks of data
            return 0.0

        # Compare first and last weeks
        first_week = historical_data[:7]
        last_week = historical_data[-7:]

        first_week_avg = sum(float(row.daily_revenue or 0) for row in first_week) / len(first_week)
        last_week_avg = sum(float(row.daily_revenue or 0) for row in last_week) / len(last_week)

        if first_week_avg > 0:
            # Annualized growth rate
            weeks_diff = len(historical_data) / 7
            growth_rate = ((last_week_avg / first_week_avg) ** (52 / weeks_diff)) - 1
            return max(-0.5, min(0.5, growth_rate))  # Cap between -50% and +50%

        return 0.0

    async def _generate_fallback_forecast(self) -> List[Dict[str, Any]]:
        """Generate fallback forecast when no historical data is available"""
        forecast_data = []
        today = datetime.now()

        for i in range(1, 8):
            date = today + timedelta(days=i)
            weekday = date.weekday()
            day_name = date.strftime("%A")

            # Simple weekday-based forecast
            if weekday >= 5:  # Weekend
                revenue = 2000
                orders = 25
            else:
                revenue = 1200 + (weekday * 100)
                orders = 15 + weekday

            forecast_data.append({
                "day": day_name,
                "customers": orders,
                "projectedRevenue": revenue,
                "confidence": 60
            })

        return forecast_data

    async def get_inventory_forecast(self, restaurant_id: str) -> List[Dict[str, Any]]:
        """Get inventory usage forecast for a restaurant"""
        try:
            async with get_db_session_context() as session:
                # Get inventory items for the restaurant
                query = select(InventoryItem).where(InventoryItem.restaurant_id == restaurant_id)
                result = await session.execute(query)
                inventory_items = result.scalars().all()

                forecast_recommendations = []

                for item in inventory_items:
                    # Simple forecast based on current stock and reorder level
                    needs_reorder = item.quantity <= item.reorder_level

                    forecast_recommendations.append({
                        "id": item.id,
                        "name": item.name,
                        "currentQuantity": item.quantity,
                        "unit": item.unit,
                        "forecastUsage": 0,  # TODO: Calculate based on historical usage
                        "projectedRemaining": item.quantity,
                        "reorderLevel": item.reorder_level,
                        "needsReorder": needs_reorder,
                        "recommendedOrderQuantity": item.reorder_level * 2 if needs_reorder else 0,
                        "estimatedCost": round((item.reorder_level * 2 * item.price_per_unit), 2) if needs_reorder else 0
                    })

                return forecast_recommendations

        except Exception as e:
            logger.error(f"Error getting inventory forecast for restaurant {restaurant_id}: {e}")
            return []

    async def get_category_performance(self, restaurant_id: str, days: int = 30) -> List[Dict[str, Any]]:
        """Get performance metrics by menu category"""
        try:
            async with get_db_session_context() as session:
                sql_query = text("""
                    SELECT
                        mi.category,
                        COUNT(DISTINCT oi.order_id) as order_count,
                        SUM(oi.quantity) as total_quantity,
                        SUM(oi.subtotal) as total_revenue,
                        AVG(oi.price) as avg_price,
                        COUNT(DISTINCT mi.id) as unique_items
                    FROM order_items oi
                    JOIN menu_items mi ON oi.menu_item_id = mi.id
                    JOIN orders o ON oi.order_id = o.id
                    WHERE o.restaurant_id = :restaurant_id
                    AND o.status != 'cancelled'
                    AND o.created_at >= date('now', '-' || :days || ' days')
                    GROUP BY mi.category
                    ORDER BY total_revenue DESC
                """)

                result = await session.execute(sql_query, {
                    "restaurant_id": restaurant_id,
                    "days": days
                })

                category_performance = []
                for row in result:
                    category_performance.append({
                        "category": row.category,
                        "orderCount": int(row.order_count),
                        "totalQuantity": int(row.total_quantity),
                        "totalRevenue": float(row.total_revenue),
                        "avgPrice": float(row.avg_price),
                        "uniqueItems": int(row.unique_items),
                        "avgOrderValue": float(row.total_revenue) / int(row.order_count) if row.order_count > 0 else 0
                    })

                return category_performance

        except Exception as e:
            logger.error(f"Error getting category performance for restaurant {restaurant_id}: {e}")
            return []

    async def get_peak_hours_analysis(self, restaurant_id: str, days: int = 30) -> List[Dict[str, Any]]:
        """Get peak hours analysis with category breakdown"""
        try:
            async with get_db_session_context() as session:
                sql_query = text("""
                    SELECT
                        strftime('%H', o.created_at) as hour,
                        mi.category,
                        COUNT(o.id) as order_count,
                        SUM(o.total) as revenue,
                        SUM(oi.quantity) as items_sold
                    FROM orders o
                    JOIN order_items oi ON o.id = oi.order_id
                    JOIN menu_items mi ON oi.menu_item_id = mi.id
                    WHERE o.restaurant_id = :restaurant_id
                    AND o.status != 'cancelled'
                    AND o.created_at >= date('now', '-' || :days || ' days')
                    GROUP BY strftime('%H', o.created_at), mi.category
                    ORDER BY hour, revenue DESC
                """)

                result = await session.execute(sql_query, {
                    "restaurant_id": restaurant_id,
                    "days": days
                })

                # Group by hour
                hourly_data = {}
                for row in result:
                    hour = int(row.hour)
                    if hour not in hourly_data:
                        hourly_data[hour] = {
                            "hour": hour,
                            "totalOrders": 0,
                            "totalRevenue": 0,
                            "categories": {}
                        }

                    hourly_data[hour]["totalOrders"] += int(row.order_count)
                    hourly_data[hour]["totalRevenue"] += float(row.revenue)
                    hourly_data[hour]["categories"][row.category] = {
                        "orderCount": int(row.order_count),
                        "revenue": float(row.revenue),
                        "itemsSold": int(row.items_sold)
                    }

                return list(hourly_data.values())

        except Exception as e:
            logger.error(f"Error getting peak hours analysis for restaurant {restaurant_id}: {e}")
            return []

    async def get_profit_margin_analysis(self, restaurant_id: str, days: int = 30) -> List[Dict[str, Any]]:
        """Get profit margin analysis by category (simplified - assumes 60% average margin)"""
        try:
            category_performance = await self.get_category_performance(restaurant_id, days)

            # Add profit calculations (simplified)
            for category in category_performance:
                revenue = category["totalRevenue"]
                # Estimate costs based on category type
                if "Beverages" in category["category"]:
                    cost_ratio = 0.15  # 85% margin
                elif "Desserts" in category["category"]:
                    cost_ratio = 0.20  # 80% margin
                elif "Appetizers" in category["category"]:
                    cost_ratio = 0.25  # 75% margin
                elif "Pasta" in category["category"]:
                    cost_ratio = 0.35  # 65% margin
                elif "Main" in category["category"]:
                    cost_ratio = 0.40  # 60% margin
                else:
                    cost_ratio = 0.30  # 70% margin default

                estimated_cost = revenue * cost_ratio
                profit = revenue - estimated_cost
                margin_percentage = (profit / revenue * 100) if revenue > 0 else 0

                category.update({
                    "estimatedCost": round(estimated_cost, 2),
                    "estimatedProfit": round(profit, 2),
                    "marginPercentage": round(margin_percentage, 1)
                })

            return category_performance

        except Exception as e:
            logger.error(f"Error getting profit margin analysis for restaurant {restaurant_id}: {e}")
            return []
