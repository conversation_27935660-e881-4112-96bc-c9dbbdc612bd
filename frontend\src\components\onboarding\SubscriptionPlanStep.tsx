import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { 
  Crown, 
  Check, 
  Star, 
  Zap,
  Settings,
  TrendingUp,
  Users,
  BarChart3,
  Brain,
  Smartphone
} from 'lucide-react';
import { 
  SUBSCRIPTION_PLANS, 
  ADD_ON_FEATURES, 
  calculateCustomizedPlanPrice,
  type SubscriptionPlan,
  type AddOnFeature 
} from '@/config/subscriptionPlans';

interface SubscriptionData {
  selectedPlan: string;
  customizedFeatures: string[];
  billingInterval: 'monthly' | 'yearly';
}

interface SubscriptionPlanStepProps {
  data: SubscriptionData;
  updateData: (data: Partial<SubscriptionData>) => void;
}

export const SubscriptionPlanStep: React.FC<SubscriptionPlanStepProps> = ({
  data,
  updateData
}) => {
  const [selectedPlan, setSelectedPlan] = useState(data.selectedPlan || 'basic');
  const [customizedFeatures, setCustomizedFeatures] = useState<string[]>(
    data.customizedFeatures || []
  );

  const handlePlanSelect = (planId: string) => {
    setSelectedPlan(planId);
    updateData({ selectedPlan: planId });
  };

  const handleAddOnToggle = (addOnId: string, enabled: boolean) => {
    const newFeatures = enabled
      ? [...customizedFeatures, addOnId]
      : customizedFeatures.filter(id => id !== addOnId);
    
    setCustomizedFeatures(newFeatures);
    updateData({ customizedFeatures: newFeatures });
  };

  const getFeatureIcon = (featureId: string) => {
    const iconMap: Record<string, React.ComponentType<any>> = {
      'llm-ai-integration': Brain,
      'advanced-epos': Smartphone,
      'table-turnover-analytics': BarChart3,
      'advanced-promotions': TrendingUp,
      'task-management': Users,
      'multi-location': Settings
    };
    return iconMap[featureId] || Check;
  };

  const renderPlanCard = (plan: SubscriptionPlan) => {
    const isSelected = selectedPlan === plan.id;
    const isCustomized = plan.id === 'customized';
    const displayPrice = isCustomized 
      ? calculateCustomizedPlanPrice(customizedFeatures)
      : plan.price;

    return (
      <Card 
        key={plan.id}
        className={`relative cursor-pointer transition-all duration-200 ${
          isSelected 
            ? 'ring-2 ring-blue-500 border-blue-500 shadow-lg' 
            : 'border-gray-200 hover:border-gray-300'
        } ${plan.popular ? 'border-blue-300' : ''}`}
        onClick={() => handlePlanSelect(plan.id)}
      >
        {plan.popular && (
          <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
            <Badge className="bg-blue-600 text-white px-3 py-1">
              <Star className="h-3 w-3 mr-1" />
              Most Popular
            </Badge>
          </div>
        )}
        
        <CardHeader className="text-center pb-4">
          <div className="flex items-center justify-center mb-2">
            {plan.id === 'basic' && <Users className="h-6 w-6 text-blue-600" />}
            {plan.id === 'pro' && <Crown className="h-6 w-6 text-purple-600" />}
            {plan.id === 'customized' && <Settings className="h-6 w-6 text-green-600" />}
          </div>
          <CardTitle className="text-xl">{plan.name}</CardTitle>
          <div className="text-3xl font-bold text-gray-900">
            £{displayPrice}
            <span className="text-sm font-normal text-gray-500">/month</span>
          </div>
          <p className="text-sm text-gray-600">{plan.description}</p>
        </CardHeader>
        
        <CardContent>
          <div className="space-y-3">
            {plan.id === 'pro' ? (
              <div className="text-center py-4">
                <div className="flex items-center justify-center text-green-600 mb-2">
                  <Check className="h-5 w-5 mr-2" />
                  <span className="font-medium">All Features Included</span>
                </div>
                <p className="text-sm text-gray-600">
                  Full access to every feature in RestroManage
                </p>
              </div>
            ) : (
              <div className="space-y-2">
                <h4 className="font-medium text-sm text-gray-900">Included Features:</h4>
                <ul className="space-y-1">
                  {plan.features.slice(0, 4).map((feature) => (
                    <li key={feature} className="flex items-center text-sm text-gray-600">
                      <Check className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                      {feature.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </li>
                  ))}
                  {plan.features.length > 4 && (
                    <li className="text-sm text-gray-500">
                      +{plan.features.length - 4} more features
                    </li>
                  )}
                </ul>
              </div>
            )}
            
            {isSelected && (
              <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                <div className="flex items-center text-blue-700">
                  <Check className="h-4 w-4 mr-2" />
                  <span className="text-sm font-medium">Selected Plan</span>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  const renderCustomizedAddOns = () => {
    if (selectedPlan !== 'customized') return null;

    const categorizedAddOns = ADD_ON_FEATURES.reduce((acc, addOn) => {
      if (!acc[addOn.category]) acc[addOn.category] = [];
      acc[addOn.category].push(addOn);
      return acc;
    }, {} as Record<string, AddOnFeature[]>);

    return (
      <Card className="mt-6">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Zap className="h-5 w-5 mr-2" />
            Customize Your Plan
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {Object.entries(categorizedAddOns).map(([category, addOns]) => (
              <div key={category}>
                <h4 className="font-medium text-sm text-gray-900 mb-3 capitalize">
                  {category} Features
                </h4>
                <div className="space-y-3">
                  {addOns.map((addOn) => {
                    const isEnabled = customizedFeatures.includes(addOn.id);
                    const IconComponent = getFeatureIcon(addOn.id);
                    
                    return (
                      <div 
                        key={addOn.id}
                        className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50"
                      >
                        <div className="flex items-center space-x-3">
                          <IconComponent className="h-5 w-5 text-gray-600" />
                          <div>
                            <Label className="font-medium">{addOn.name}</Label>
                            <p className="text-sm text-gray-600">{addOn.description}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-3">
                          <span className="text-sm font-medium">
                            +£{addOn.price}/month
                          </span>
                          <Switch
                            checked={isEnabled}
                            onCheckedChange={(checked) => handleAddOnToggle(addOn.id, checked)}
                          />
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            ))}
            
            {customizedFeatures.length > 0 && (
              <div className="mt-4 p-4 bg-green-50 rounded-lg">
                <div className="flex items-center justify-between">
                  <span className="font-medium text-green-800">
                    Total Monthly Cost:
                  </span>
                  <span className="text-xl font-bold text-green-800">
                    £{calculateCustomizedPlanPrice(customizedFeatures)}
                  </span>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Crown className="h-5 w-5 mr-2" />
            Choose Your Subscription Plan
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {SUBSCRIPTION_PLANS.map(renderPlanCard)}
          </div>
        </CardContent>
      </Card>

      {renderCustomizedAddOns()}

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            <TrendingUp className="h-5 w-5 text-blue-600 mt-0.5" />
          </div>
          <div>
            <h4 className="font-medium text-blue-900">14-Day Free Trial</h4>
            <p className="text-sm text-blue-700 mt-1">
              Start with a free trial on any plan. No credit card required. 
              You can change or cancel your plan anytime during the trial period.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
