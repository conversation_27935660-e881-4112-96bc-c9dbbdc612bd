import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON>Header, Di<PERSON>T<PERSON>le, <PERSON><PERSON>Footer, DialogDescription } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { toast } from "@/components/ui/sonner";
import { format, isSameDay } from "date-fns";
import { Calendar as CalendarIcon, Plus, X, User } from "lucide-react";

// Define interfaces
interface StaffMember {
  id: string;
  name: string;
  role: string;
  position: string;
  status?: 'active' | 'inactive';
  availableDays?: string[];
}

interface StaffUnavailability {
  id: string;
  staffId: string;
  date: Date;
  type: 'holiday' | 'sick' | 'personal' | 'other';
  notes?: string;
}

interface StaffCalendarProps {
  staffData: StaffMember[];
  onUnavailabilityChange?: (unavailabilities: StaffUnavailability[]) => void;
}

const StaffCalendar = ({ staffData, onUnavailabilityChange }: StaffCalendarProps) => {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [isAddUnavailabilityOpen, setIsAddUnavailabilityOpen] = useState(false);
  const [isViewUnavailabilityOpen, setIsViewUnavailabilityOpen] = useState(false);
  const [unavailabilities, setUnavailabilities] = useState<StaffUnavailability[]>([]);
  const [selectedStaffId, setSelectedStaffId] = useState<string>("");
  const [unavailabilityType, setUnavailabilityType] = useState<'holiday' | 'sick' | 'personal' | 'other'>('holiday');
  const [notes, setNotes] = useState<string>("");
  const [selectedUnavailability, setSelectedUnavailability] = useState<StaffUnavailability | null>(null);
  const [filteredStaffId, setFilteredStaffId] = useState<string>("all");

  // Function to add unavailability
  const handleAddUnavailability = () => {
    if (!selectedDate || !selectedStaffId) {
      toast.error("Please select a date and staff member");
      return;
    }

    // Check if there's already an unavailability for this staff on this date
    const existingUnavailability = unavailabilities.find(
      u => u.staffId === selectedStaffId && isSameDay(new Date(u.date), selectedDate)
    );

    if (existingUnavailability) {
      toast.error("This staff member is already marked as unavailable on this date");
      return;
    }

    const newUnavailability: StaffUnavailability = {
      id: `unavail-${Date.now()}`,
      staffId: selectedStaffId,
      date: selectedDate,
      type: unavailabilityType,
      notes: notes.trim() || undefined
    };

    const updatedUnavailabilities = [...unavailabilities, newUnavailability];
    setUnavailabilities(updatedUnavailabilities);
    
    // Notify parent component if callback is provided
    if (onUnavailabilityChange) {
      onUnavailabilityChange(updatedUnavailabilities);
    }

    // Reset form and close dialog
    setSelectedStaffId("");
    setUnavailabilityType('holiday');
    setNotes("");
    setIsAddUnavailabilityOpen(false);
    
    // Show success message
    const staffName = staffData.find(s => s.id === selectedStaffId)?.name || "Staff member";
    toast.success(`${staffName} marked as unavailable on ${format(selectedDate, "MMMM d, yyyy")}`);
  };

  // Function to remove unavailability
  const handleRemoveUnavailability = (unavailabilityId: string) => {
    const unavailabilityToRemove = unavailabilities.find(u => u.id === unavailabilityId);
    if (!unavailabilityToRemove) return;

    const updatedUnavailabilities = unavailabilities.filter(u => u.id !== unavailabilityId);
    setUnavailabilities(updatedUnavailabilities);
    
    // Notify parent component if callback is provided
    if (onUnavailabilityChange) {
      onUnavailabilityChange(updatedUnavailabilities);
    }

    // Close dialog
    setIsViewUnavailabilityOpen(false);
    setSelectedUnavailability(null);
    
    // Show success message
    const staffName = staffData.find(s => s.id === unavailabilityToRemove.staffId)?.name || "Staff member";
    toast.success(`Unavailability removed for ${staffName}`);
  };

  // Function to handle date selection
  const handleDateSelect = (date: Date | undefined) => {
    setSelectedDate(date);
    
    if (date) {
      // Check if there are any unavailabilities on this date
      const unavailabilitiesOnDate = unavailabilities.filter(u => 
        isSameDay(new Date(u.date), date)
      );
      
      if (unavailabilitiesOnDate.length > 0) {
        // If there's only one unavailability, show it directly
        if (unavailabilitiesOnDate.length === 1) {
          setSelectedUnavailability(unavailabilitiesOnDate[0]);
          setIsViewUnavailabilityOpen(true);
        } else {
          // If there are multiple, just open the add dialog where they can select a staff
          setIsAddUnavailabilityOpen(true);
        }
      } else {
        // No unavailabilities on this date, open add dialog
        setIsAddUnavailabilityOpen(true);
      }
    }
  };

  // Function to get unavailable staff for a date
  const getUnavailableStaffForDate = (date: Date) => {
    return unavailabilities
      .filter(u => isSameDay(new Date(u.date), date))
      .map(u => staffData.find(s => s.id === u.staffId)?.name || "Unknown");
  };

  // Function to render day contents in the calendar
  const renderDayContents = (day: Date) => {
    const unavailableStaff = getUnavailableStaffForDate(day);
    
    return (
      <div className="relative w-full h-full">
        <div>{day.getDate()}</div>
        {unavailableStaff.length > 0 && (
          <div className="absolute bottom-0 right-0">
            <Badge variant="destructive" className="text-[10px] h-4 px-1">
              {unavailableStaff.length}
            </Badge>
          </div>
        )}
      </div>
    );
  };

  // Filter unavailabilities based on selected staff
  const filteredUnavailabilities = filteredStaffId === "all" 
    ? unavailabilities 
    : unavailabilities.filter(u => u.staffId === filteredStaffId);

  // Group unavailabilities by date for the list view
  const groupedUnavailabilities = filteredUnavailabilities.reduce((acc, curr) => {
    const dateStr = format(new Date(curr.date), "yyyy-MM-dd");
    if (!acc[dateStr]) {
      acc[dateStr] = [];
    }
    acc[dateStr].push(curr);
    return acc;
  }, {} as Record<string, StaffUnavailability[]>);

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-lg font-medium">Staff Calendar</CardTitle>
              <CardDescription>
                Manage staff holidays and unavailable days
              </CardDescription>
            </div>
            <Button onClick={() => setIsAddUnavailabilityOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Unavailability
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={handleDateSelect}
                className="rounded-md border"
                components={{
                  DayContent: ({ date }) => renderDayContents(date)
                }}
              />
            </div>
            <div>
              <div className="mb-4">
                <Label htmlFor="staff-filter">Filter by Staff</Label>
                <Select value={filteredStaffId} onValueChange={setFilteredStaffId}>
                  <SelectTrigger id="staff-filter">
                    <SelectValue placeholder="Select staff member" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Staff</SelectItem>
                    {staffData.map(staff => (
                      <SelectItem key={staff.id} value={staff.id}>
                        {staff.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-4 max-h-[400px] overflow-y-auto">
                <h3 className="font-medium">Upcoming Unavailability</h3>
                {Object.keys(groupedUnavailabilities).length === 0 ? (
                  <p className="text-muted-foreground text-sm">No unavailability records found</p>
                ) : (
                  Object.entries(groupedUnavailabilities)
                    .sort(([dateA], [dateB]) => new Date(dateA).getTime() - new Date(dateB).getTime())
                    .map(([dateStr, items]) => (
                      <div key={dateStr} className="border rounded-md p-3">
                        <div className="font-medium mb-2 flex items-center">
                          <CalendarIcon className="h-4 w-4 mr-2" />
                          {format(new Date(dateStr), "MMMM d, yyyy")}
                        </div>
                        <div className="space-y-2">
                          {items.map(item => {
                            const staff = staffData.find(s => s.id === item.staffId);
                            return (
                              <div 
                                key={item.id} 
                                className="flex justify-between items-center text-sm p-2 bg-muted rounded-md cursor-pointer hover:bg-muted/80"
                                onClick={() => {
                                  setSelectedUnavailability(item);
                                  setIsViewUnavailabilityOpen(true);
                                }}
                              >
                                <div className="flex items-center">
                                  <User className="h-4 w-4 mr-2" />
                                  <span>{staff?.name || "Unknown"}</span>
                                </div>
                                <Badge variant={
                                  item.type === 'holiday' ? "default" :
                                  item.type === 'sick' ? "destructive" :
                                  item.type === 'personal' ? "secondary" : "outline"
                                }>
                                  {item.type}
                                </Badge>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    ))
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Add Unavailability Dialog */}
      <Dialog open={isAddUnavailabilityOpen} onOpenChange={setIsAddUnavailabilityOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Staff Unavailability</DialogTitle>
            <DialogDescription>
              Mark a staff member as unavailable for a specific date
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="date">Date</Label>
              <div className="flex items-center border rounded-md p-2">
                <CalendarIcon className="h-4 w-4 mr-2" />
                {selectedDate ? format(selectedDate, "MMMM d, yyyy") : "Select a date"}
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="staff">Staff Member</Label>
              <Select value={selectedStaffId} onValueChange={setSelectedStaffId}>
                <SelectTrigger id="staff">
                  <SelectValue placeholder="Select staff member" />
                </SelectTrigger>
                <SelectContent>
                  {staffData.map(staff => (
                    <SelectItem key={staff.id} value={staff.id}>
                      {staff.name} ({staff.position})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="type">Unavailability Type</Label>
              <Select 
                value={unavailabilityType} 
                onValueChange={(value) => setUnavailabilityType(value as any)}
              >
                <SelectTrigger id="type">
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="holiday">Holiday</SelectItem>
                  <SelectItem value="sick">Sick Leave</SelectItem>
                  <SelectItem value="personal">Personal Leave</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="notes">Notes (Optional)</Label>
              <Textarea
                id="notes"
                placeholder="Add any additional notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddUnavailabilityOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddUnavailability}>
              Save
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* View Unavailability Dialog */}
      <Dialog open={isViewUnavailabilityOpen} onOpenChange={setIsViewUnavailabilityOpen}>
        <DialogContent>
          {selectedUnavailability && (
            <>
              <DialogHeader>
                <DialogTitle>Staff Unavailability</DialogTitle>
                <DialogDescription>
                  Details for {format(new Date(selectedUnavailability.date), "MMMM d, yyyy")}
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label>Staff Member</Label>
                  <div className="font-medium">
                    {staffData.find(s => s.id === selectedUnavailability.staffId)?.name || "Unknown"}
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label>Type</Label>
                  <div>
                    <Badge variant={
                      selectedUnavailability.type === 'holiday' ? "default" :
                      selectedUnavailability.type === 'sick' ? "destructive" :
                      selectedUnavailability.type === 'personal' ? "secondary" : "outline"
                    }>
                      {selectedUnavailability.type}
                    </Badge>
                  </div>
                </div>
                
                {selectedUnavailability.notes && (
                  <div className="space-y-2">
                    <Label>Notes</Label>
                    <div className="text-sm text-muted-foreground border rounded-md p-2">
                      {selectedUnavailability.notes}
                    </div>
                  </div>
                )}
              </div>
              
              <DialogFooter>
                <Button 
                  variant="destructive" 
                  onClick={() => handleRemoveUnavailability(selectedUnavailability.id)}
                >
                  <X className="h-4 w-4 mr-2" />
                  Remove Unavailability
                </Button>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default StaffCalendar;