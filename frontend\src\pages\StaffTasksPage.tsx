import { useEffect, useState } from "react";
import Layout from "@/components/layout/Layout";
import { useAuth } from "@/contexts/AuthContext";
import SimpleThingsToDoTab from "@/components/staff/SimpleThingsToDoTab";

const StaffTasksPage = () => {
  const { user, allUsers } = useAuth();
  const [staffData, setStaffData] = useState<any[]>([]);

  useEffect(() => {
    // Set up staff data for the SimpleThingsToDoTab component
    if (user) {
      // If we have a current user, make sure they're first in the list
      const currentUser = {
        id: user.id,
        name: user.name,
        position: user.position || "Staff",
      };
      
      // Add other staff members
      const otherStaff = allUsers
        .filter(u => u.id !== user.id)
        .map(u => ({
          id: u.id,
          name: u.name,
          position: u.position || "Staff",
        }));
      
      setStaffData([currentUser, ...otherStaff]);
    } else {
      // Fallback if no user is logged in
      setStaffData(
        allUsers.map(u => ({
          id: u.id,
          name: u.name,
          position: u.position || "Staff",
        }))
      );
    }
  }, [user, allUsers]);

  return (
    <Layout title="Staff Tasks" requiredRoles={["staff", "waiter", "chef", "hostess", "bartender", "admin", "manager"]}>
      <div className="space-y-6">
        <h1 className="text-2xl font-bold">Tasks</h1>
        <p className="text-muted-foreground">
          View and complete tasks assigned to you or available for any staff member.
        </p>
        
        {staffData.length > 0 ? (
          <SimpleThingsToDoTab staffData={staffData} />
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            Loading staff data...
          </div>
        )}
      </div>
    </Layout>
  );
};

export default StaffTasksPage;
