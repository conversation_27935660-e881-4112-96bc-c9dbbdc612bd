"""
Simplified table repository using raw SQL queries.
This bypasses SQLAlchemy ORM issues and works directly with the database.
"""

import aiosqlite
from typing import List, Dict, Any, Optional
from pathlib import Path

class SimpleTableRepository:
    """Simple table repository using raw SQL"""
    
    def __init__(self):
        self.db_path = Path("restro_manage.db")
    
    async def get_all(
        self,
        restaurant_id: Optional[str] = None,
        status: Optional[str] = None,
        location: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Get all tables with optional filtering"""
        try:
            async with aiosqlite.connect(str(self.db_path)) as db:
                # Build query with filters
                query = "SELECT * FROM tables WHERE 1=1"
                params = []
                
                if restaurant_id:
                    query += " AND restaurant_id = ?"
                    params.append(restaurant_id)
                
                if status:
                    query += " AND status = ?"
                    params.append(status)
                
                if location:
                    query += " AND location = ?"
                    params.append(location)
                
                query += " ORDER BY number LIMIT ? OFFSET ?"
                params.extend([limit, skip])
                
                cursor = await db.execute(query, params)
                rows = await cursor.fetchall()
                
                # Get column names
                columns = [description[0] for description in cursor.description]
                
                # Convert to dictionaries
                tables = []
                for row in rows:
                    table = dict(zip(columns, row))
                    tables.append(table)
                
                return tables
                
        except Exception as e:
            print(f"Error getting tables: {e}")
            return []
    
    async def get_by_id(self, table_id: str) -> Optional[Dict[str, Any]]:
        """Get a table by ID"""
        try:
            async with aiosqlite.connect(str(self.db_path)) as db:
                cursor = await db.execute("SELECT * FROM tables WHERE id = ?", (table_id,))
                row = await cursor.fetchone()
                
                if not row:
                    return None
                
                # Get column names
                columns = [description[0] for description in cursor.description]
                table = dict(zip(columns, row))
                
                return table
                
        except Exception as e:
            print(f"Error getting table {table_id}: {e}")
            return None
    
    async def get_by_number(self, restaurant_id: str, table_number: int) -> Optional[Dict[str, Any]]:
        """Get a table by restaurant ID and table number"""
        try:
            async with aiosqlite.connect(str(self.db_path)) as db:
                cursor = await db.execute(
                    "SELECT * FROM tables WHERE restaurant_id = ? AND number = ?",
                    (restaurant_id, table_number)
                )
                row = await cursor.fetchone()
                
                if not row:
                    return None
                
                # Get column names
                columns = [description[0] for description in cursor.description]
                table = dict(zip(columns, row))
                
                return table
                
        except Exception as e:
            print(f"Error getting table by number: {e}")
            return None
    
    async def get_by_status(self, status: str, restaurant_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get tables by status"""
        try:
            async with aiosqlite.connect(str(self.db_path)) as db:
                if restaurant_id:
                    cursor = await db.execute(
                        "SELECT * FROM tables WHERE status = ? AND restaurant_id = ? ORDER BY number",
                        (status, restaurant_id)
                    )
                else:
                    cursor = await db.execute(
                        "SELECT * FROM tables WHERE status = ? ORDER BY number",
                        (status,)
                    )
                
                rows = await cursor.fetchall()
                
                # Get column names
                columns = [description[0] for description in cursor.description]
                
                # Convert to dictionaries
                tables = []
                for row in rows:
                    table = dict(zip(columns, row))
                    tables.append(table)
                
                return tables
                
        except Exception as e:
            print(f"Error getting tables by status: {e}")
            return []
    
    async def create(self, table_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new table"""
        try:
            async with aiosqlite.connect(str(self.db_path)) as db:
                # Generate ID if not provided
                if 'id' not in table_data:
                    import uuid
                    table_data['id'] = str(uuid.uuid4())
                
                # Add timestamps
                from datetime import datetime
                now = datetime.now().isoformat()
                table_data['created_at'] = now
                table_data['updated_at'] = now
                
                # Insert into database
                columns = list(table_data.keys())
                placeholders = ', '.join(['?' for _ in columns])
                query = f"INSERT INTO tables ({', '.join(columns)}) VALUES ({placeholders})"
                
                await db.execute(query, list(table_data.values()))
                await db.commit()
                
                return await self.get_by_id(table_data['id'])
                
        except Exception as e:
            print(f"Error creating table: {e}")
            raise
    
    async def update(self, table_id: str, table_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Update a table"""
        try:
            async with aiosqlite.connect(str(self.db_path)) as db:
                # Add updated timestamp
                from datetime import datetime
                table_data['updated_at'] = datetime.now().isoformat()
                
                # Build update query
                set_clauses = [f"{key} = ?" for key in table_data.keys()]
                query = f"UPDATE tables SET {', '.join(set_clauses)} WHERE id = ?"
                params = list(table_data.values()) + [table_id]
                
                await db.execute(query, params)
                await db.commit()
                
                return await self.get_by_id(table_id)
                
        except Exception as e:
            print(f"Error updating table {table_id}: {e}")
            return None
    
    async def delete(self, table_id: str) -> bool:
        """Delete a table"""
        try:
            async with aiosqlite.connect(str(self.db_path)) as db:
                await db.execute("DELETE FROM tables WHERE id = ?", (table_id,))
                await db.commit()
                return True
                
        except Exception as e:
            print(f"Error deleting table {table_id}: {e}")
            return False

# Create global instance
simple_table_repository = SimpleTableRepository()
