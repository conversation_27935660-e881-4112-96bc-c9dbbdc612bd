export interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  currency: string;
  interval: 'monthly' | 'yearly';
  description: string;
  features: string[];
  excludedFeatures?: string[];
  popular?: boolean;
  trialDays?: number;
}

export interface AddOnFeature {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  category: 'ai' | 'analytics' | 'management' | 'epos';
}

// Core feature definitions
export const FEATURE_DEFINITIONS = {
  // Core Features (included in all plans)
  'dashboard': 'Main dashboard and overview',

  // Basic Plan Features (£29/month)
  'analytics': 'Basic sales and performance analytics',
  'inventory': 'Basic inventory tracking',
  'staff-management': 'Staff profiles and basic scheduling',
  'menu-management': 'Menu creation and management',
  'forecasting': 'Sales and demand forecasting',
  'staff-scheduling': 'Advanced staff scheduling and time tracking',
  'order-management': 'Basic order processing and management',

  // Pro Plan Features (£69/month - includes all features)
  'basic-epos': 'Basic point-of-sale functionality',
  'advanced-epos': 'Advanced point-of-sale features',
  'llm-ai-integration': 'AI-powered insights and recommendations',
  'table-turnover-analytics': 'Detailed table performance analytics',
  'advanced-promotions': 'Complex promotion and discount management',
  'task-management': 'Task assignment and workflow management',
  'multi-location': 'Multi-restaurant management',
  'api-access': 'API access for integrations',
  'custom-reports': 'Custom reporting and data exports',
  'loyalty-programs': 'Customer loyalty program management',
  'online-ordering': 'Online ordering integration',
  'delivery-management': 'Delivery tracking and management',
  'financial-reporting': 'Advanced financial reporting',
  'crm-integration': 'Customer relationship management',
  'pos-integration': 'Third-party POS system integration'
} as const;

export type FeatureKey = keyof typeof FEATURE_DEFINITIONS;

// Subscription plans configuration
export const SUBSCRIPTION_PLANS: SubscriptionPlan[] = [
  {
    id: 'basic',
    name: 'Basic Plan',
    price: 29,
    currency: 'GBP',
    interval: 'monthly',
    description: 'Perfect for small restaurants getting started',
    trialDays: 14,
    features: [
      'dashboard',
      'analytics',
      'inventory',
      'staff-management',
      'menu-management',
      'forecasting',
      'staff-scheduling',
      'order-management'
    ],
    excludedFeatures: [
      'basic-epos',
      'advanced-epos',
      'llm-ai-integration',
      'table-turnover-analytics',
      'advanced-promotions',
      'task-management',
      'multi-location',
      'api-access',
      'custom-reports',
      'loyalty-programs',
      'online-ordering',
      'delivery-management',
      'financial-reporting',
      'crm-integration',
      'pos-integration'
    ]
  },
  {
    id: 'pro',
    name: 'Pro Plan',
    price: 69,
    currency: 'GBP',
    interval: 'monthly',
    description: 'Full access to all features for growing restaurants',
    popular: true,
    trialDays: 14,
    features: ['all'], // Special case: all features included
    excludedFeatures: []
  },
  {
    id: 'customized',
    name: 'Customized Plan',
    price: 29, // Base price
    currency: 'GBP',
    interval: 'monthly',
    description: 'Tailored plan with add-on features',
    trialDays: 14,
    features: [
      'dashboard',
      'analytics',
      'inventory',
      'staff-management',
      'menu-management',
      'forecasting',
      'staff-scheduling',
      'order-management'
    ],
    excludedFeatures: [] // Determined by selected add-ons
  }
];

// Add-on features for customized plan
export const ADD_ON_FEATURES: AddOnFeature[] = [
  {
    id: 'basic-epos',
    name: 'Basic EPOS',
    description: 'Basic point-of-sale functionality',
    price: 15,
    currency: 'GBP',
    category: 'epos'
  },
  {
    id: 'advanced-epos',
    name: 'Advanced EPOS',
    description: 'Advanced point-of-sale features and customizations',
    price: 25,
    currency: 'GBP',
    category: 'epos'
  },
  {
    id: 'llm-ai-integration',
    name: 'AI Integration',
    description: 'AI-powered insights, recommendations, and automation',
    price: 10,
    currency: 'GBP',
    category: 'ai'
  },
  {
    id: 'table-turnover-analytics',
    name: 'Table Analytics',
    description: 'Detailed table performance and turnover analytics',
    price: 3,
    currency: 'GBP',
    category: 'analytics'
  },
  {
    id: 'advanced-promotions',
    name: 'Advanced Promotions',
    description: 'Complex promotion rules and discount management',
    price: 5,
    currency: 'GBP',
    category: 'management'
  },
  {
    id: 'task-management',
    name: 'Task Management',
    description: 'Task assignment, workflow management, and team collaboration',
    price: 3,
    currency: 'GBP',
    category: 'management'
  },
  {
    id: 'multi-location',
    name: 'Multi-Location',
    description: 'Manage multiple restaurant locations from one dashboard',
    price: 15,
    currency: 'GBP',
    category: 'management'
  },
  {
    id: 'api-access',
    name: 'API Access',
    description: 'Full API access for custom integrations',
    price: 8,
    currency: 'GBP',
    category: 'epos'
  },
  {
    id: 'custom-reports',
    name: 'Custom Reports',
    description: 'Create custom reports and data exports',
    price: 5,
    currency: 'GBP',
    category: 'analytics'
  }
];

// Helper functions
export const getPlanById = (planId: string): SubscriptionPlan | undefined => {
  return SUBSCRIPTION_PLANS.find(plan => plan.id === planId);
};

export const getAddOnById = (addOnId: string): AddOnFeature | undefined => {
  return ADD_ON_FEATURES.find(addOn => addOn.id === addOnId);
};

export const calculateCustomizedPlanPrice = (selectedAddOns: string[]): number => {
  const basePlan = getPlanById('customized');
  if (!basePlan) return 0;
  
  const addOnPrice = selectedAddOns.reduce((total, addOnId) => {
    const addOn = getAddOnById(addOnId);
    return total + (addOn?.price || 0);
  }, 0);
  
  return basePlan.price + addOnPrice;
};

export const getAvailableFeatures = (
  planId: string,
  customizedFeatures?: string[]
): string[] => {
  // PRO PLAN: Direct access to all features - no complex logic
  if (planId === 'pro') {
    console.log('✅ PRO PLAN DETECTED: Granting access to ALL features');
    return Object.keys(FEATURE_DEFINITIONS);
  }

  const plan = getPlanById(planId);
  if (!plan) {
    console.error('❌ No plan found for planId:', planId);
    return [];
  }

  // CUSTOMIZED PLAN: Combine base features with add-ons
  if (planId === 'customized' && customizedFeatures) {
    const combinedFeatures = [...plan.features, ...customizedFeatures];
    console.log('🔧 Customized plan features:', combinedFeatures.length);
    return combinedFeatures;
  }

  // BASIC PLAN: Return standard features
  return plan.features;
};

export const hasFeatureAccess = (
  feature: string,
  planId: string,
  customizedFeatures?: string[]
): boolean => {
  // 🔓 TEMPORARY SUBSCRIPTION DISABLING
  // Import feature flag to check if subscription gating is enabled
  const { isSubscriptionGatingEnabled } = require('./featureFlags');

  // If subscription gating is disabled, grant access to ALL features for ALL plans
  if (!isSubscriptionGatingEnabled()) {
    return true;
  }

  // 🔒 ORIGINAL SUBSCRIPTION LOGIC (Preserved for future re-enablement)
  // BULLETPROOF PRO PLAN LOGIC: Multiple checks to ensure reliability

  // Check 1: Direct planId comparison
  if (planId === 'pro') {
    return true;
  }

  // Check 2: Case-insensitive comparison
  if (planId?.toLowerCase() === 'pro') {
    return true;
  }

  // Check 3: String contains 'pro'
  if (typeof planId === 'string' && planId.includes('pro')) {
    return true;
  }

  // For all other plans, check against available features
  const availableFeatures = getAvailableFeatures(planId, customizedFeatures);
  const hasAccess = availableFeatures.includes(feature);

  console.log(`${hasAccess ? '✅' : '❌'} ${planId?.toUpperCase() || 'UNKNOWN'} PLAN: ${feature} = ${hasAccess}`);

  return hasAccess;
};

// Comprehensive test function for Pro plan access
export const testProPlanAccess = () => {
  console.log('🧪 TESTING BULLETPROOF PRO PLAN LOGIC:');
  console.log('='.repeat(50));

  // All 13 required menu features for Pro Plan
  const allMenuFeatures = [
    'dashboard',
    'analytics',
    'inventory',
    'table-turnover-analytics',  // ❌ Previously missing
    'basic-epos',               // ❌ Previously missing
    'menu-management',
    'advanced-promotions',      // ❌ Previously missing
    'task-management',          // ❌ Previously missing
    'notifications',
    'staff-management',
    'staff-scheduling',
    'llm-ai-integration',       // ❌ Previously missing
    'settings'
  ];

  console.log('🎯 Testing Pro Plan Access (Should be ALL ✅):');
  let proPlanSuccessCount = 0;
  allMenuFeatures.forEach((feature, index) => {
    const hasAccess = hasFeatureAccess(feature, 'pro');
    const status = hasAccess ? '✅ GRANTED' : '❌ DENIED';
    console.log(`  ${index + 1}. ${feature}: ${status}`);
    if (hasAccess) proPlanSuccessCount++;
  });

  console.log(`\n📊 Pro Plan Results: ${proPlanSuccessCount}/${allMenuFeatures.length} features granted`);

  if (proPlanSuccessCount === allMenuFeatures.length) {
    console.log('🎉 SUCCESS: Pro Plan has access to ALL 13 menu features!');
  } else {
    console.error(`🚨 FAILURE: Pro Plan missing ${allMenuFeatures.length - proPlanSuccessCount} features!`);
  }

  console.log('\n📋 Testing Basic Plan Access (Should be LIMITED):');
  let basicPlanSuccessCount = 0;
  allMenuFeatures.forEach((feature, index) => {
    const hasAccess = hasFeatureAccess(feature, 'basic');
    const status = hasAccess ? '✅ GRANTED' : '❌ DENIED';
    console.log(`  ${index + 1}. ${feature}: ${status}`);
    if (hasAccess) basicPlanSuccessCount++;
  });

  console.log(`\n📊 Basic Plan Results: ${basicPlanSuccessCount}/${allMenuFeatures.length} features granted`);
  console.log('='.repeat(50));

  return {
    proPlanSuccess: proPlanSuccessCount === allMenuFeatures.length,
    basicPlanLimited: basicPlanSuccessCount < allMenuFeatures.length,
    proPlanCount: proPlanSuccessCount,
    basicPlanCount: basicPlanSuccessCount,
    totalFeatures: allMenuFeatures.length
  };
};

// Specific validation for all restaurants with subscription gating disabled
export const validateUniversalAccess = () => {
  // Validation function disabled for production
};

// Make test functions available in development
if (process.env.NODE_ENV === 'development' && typeof window !== 'undefined') {
  (window as any).testProPlanAccess = testProPlanAccess;
  (window as any).validateUniversalAccess = validateUniversalAccess;
}
