#!/usr/bin/env python3
"""
<PERSON>ript to create sample orders for analytics testing.
This will populate the database with orders to make analytics data visible.
"""

import sqlite3
import uuid
import random
from datetime import datetime, timedelta
import json

def get_restaurant_data():
    """Get restaurant and menu data from the database"""
    conn = sqlite3.connect("restro_manage.db")
    cursor = conn.cursor()
    
    # Get first restaurant
    cursor.execute("SELECT id, name FROM restaurants LIMIT 1;")
    restaurant = cursor.fetchone()
    
    if not restaurant:
        print("❌ No restaurants found in database")
        return None, None, None
    
    restaurant_id, restaurant_name = restaurant
    
    # Get menu items for this restaurant
    cursor.execute("SELECT id, name, price FROM menu_items WHERE restaurant_id = ? LIMIT 20;", (restaurant_id,))
    menu_items = cursor.fetchall()
    
    # Get tables for this restaurant
    cursor.execute("SELECT id, number FROM tables WHERE restaurant_id = ? LIMIT 10;", (restaurant_id,))
    tables = cursor.fetchall()
    
    conn.close()
    
    return restaurant_id, menu_items, tables

def create_sample_orders(restaurant_id, menu_items, tables, num_orders=50):
    """Create sample orders for the past 30 days"""
    conn = sqlite3.connect("restro_manage.db")
    cursor = conn.cursor()
    
    created_orders = 0
    total_revenue = 0
    
    # Create orders for the past 30 days
    for i in range(num_orders):
        # Random date in the past 30 days
        days_ago = random.randint(0, 30)
        order_date = datetime.now() - timedelta(days=days_ago)
        
        # Random time during business hours (11 AM to 10 PM)
        hour = random.randint(11, 22)
        minute = random.randint(0, 59)
        order_datetime = order_date.replace(hour=hour, minute=minute, second=0, microsecond=0)
        
        # Random table
        table = random.choice(tables)
        table_id, _ = table
        
        # Create order
        order_id = str(uuid.uuid4())
        
        # Random number of items (1-5)
        num_items = random.randint(1, 5)
        selected_items = random.sample(menu_items, min(num_items, len(menu_items)))
        
        # Calculate total
        order_total = 0
        order_items = []
        
        for item in selected_items:
            item_id, item_name, item_price = item
            quantity = random.randint(1, 3)
            item_total = float(item_price) * quantity
            order_total += item_total
            
            order_items.append({
                "id": str(uuid.uuid4()),
                "menu_item_id": item_id,
                "name": item_name,
                "quantity": quantity,
                "price": float(item_price),
                "total": item_total
            })
        
        # Add tax (20%)
        tax_amount = order_total * 0.20
        final_total = order_total + tax_amount
        
        # Random status (mostly completed)
        status_options = ["completed", "completed", "completed", "completed", "cancelled"]
        status = random.choice(status_options)
        
        if status == "cancelled":
            final_total = 0
        
        # Insert order
        cursor.execute("""
            INSERT INTO orders (
                id, restaurant_id, table_id, status,
                subtotal, tax_amount, total, payment_status,
                created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            order_id,
            restaurant_id,
            table_id,
            status,
            order_total,
            tax_amount,
            final_total,
            "paid" if status == "completed" else "pending",
            order_datetime.isoformat(),
            order_datetime.isoformat()
        ))
        
        # Insert order items
        for item in order_items:
            cursor.execute("""
                INSERT INTO order_items (
                    id, order_id, menu_item_id, name, quantity, price, subtotal, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                item["id"],
                order_id,
                item["menu_item_id"],
                item["name"],
                item["quantity"],
                item["price"],
                item["total"],
                order_datetime.isoformat(),
                order_datetime.isoformat()
            ))
        
        if status == "completed":
            total_revenue += final_total
            created_orders += 1
    
    conn.commit()
    conn.close()
    
    return created_orders, total_revenue

def main():
    """Main function to create sample orders"""
    print("🍽️  Creating Sample Orders for Analytics")
    print("=" * 50)
    
    # Get restaurant data
    restaurant_id, menu_items, tables = get_restaurant_data()
    
    if not restaurant_id:
        return False
    
    print(f"📊 Found restaurant with {len(menu_items)} menu items and {len(tables)} tables")
    
    # Check if orders already exist
    conn = sqlite3.connect("restro_manage.db")
    cursor = conn.cursor()
    cursor.execute("SELECT COUNT(*) FROM orders WHERE restaurant_id = ?", (restaurant_id,))
    existing_orders = cursor.fetchone()[0]
    conn.close()
    
    if existing_orders > 0:
        print(f"⚠️  Restaurant already has {existing_orders} orders")
        response = input("Do you want to create more orders? (y/N): ")
        if response.lower() != 'y':
            print("Skipping order creation")
            return True
    
    # Create sample orders
    print("🔄 Creating sample orders...")
    created_orders, total_revenue = create_sample_orders(restaurant_id, menu_items, tables, 100)
    
    print(f"✅ Created {created_orders} completed orders")
    print(f"💰 Total revenue: £{total_revenue:.2f}")
    print("🎉 Sample orders created successfully!")
    
    return True

if __name__ == "__main__":
    main()
