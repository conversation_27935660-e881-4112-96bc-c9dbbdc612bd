import React from 'react';
import { useSubscriptionAccess } from '@/hooks/useSubscriptionAccess';
import { UpgradePrompt } from './UpgradePrompt';
import { type FeatureKey } from '@/config/subscriptionPlans';
import { isSubscriptionGatingEnabled } from '@/config/featureFlags';
import logger from '@/utils/logger';

interface FeatureGateProps {
  feature: FeatureKey;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showUpgradePrompt?: boolean;
  silent?: boolean; // If true, just hide content without showing upgrade prompt
}

export const FeatureGate: React.FC<FeatureGateProps> = ({
  feature,
  children,
  fallback,
  showUpgradePrompt = false, // Changed default to false - hide rather than show
  silent = true // Changed default to true - hide rather than show
}) => {
  const { hasFeatureAccess, getUpgradePrompt } = useSubscriptionAccess();

  // 🔓 BYPASS FEATURE GATE WHEN SUBSCRIPTION GATING IS DISABLED
  // If subscription gating is disabled globally, always show the content
  if (!isSubscriptionGatingEnabled()) {
    return <>{children}</>;
  }

  const hasAccess = hasFeatureAccess(feature);

  // Log feature access attempts for analytics
  React.useEffect(() => {
    if (!hasAccess) {
      logger.userAction('feature access denied', 'FeatureGate', {
        feature,
        reason: 'subscription restriction'
      });
    }
  }, [hasAccess, feature]);

  if (hasAccess) {
    return <>{children}</>;
  }

  // If access denied, show appropriate fallback
  if (silent) {
    return null;
  }

  if (fallback) {
    return <>{fallback}</>;
  }

  if (showUpgradePrompt) {
    const upgradeInfo = getUpgradePrompt(feature);
    return <UpgradePrompt {...upgradeInfo} />;
  }

  return null;
};

export default FeatureGate;
