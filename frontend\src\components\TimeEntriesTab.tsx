import logger from "@/utils/logger";
import React, { useEffect } from "react";
import { StaffMember, TimeEntry } from "@/types";
import { Plus, Pencil, Clock } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "@/components/ui/sonner";

interface TimeEntriesTabProps {
  timeEntries: TimeEntry[];
  staffData: StaffMember[];
  selectedStaff: string | null;
  setSelectedStaff: (id: string) => void;
  setIsAddTimeEntryDialogOpen: (open: boolean) => void;
  setCurrentTimeEntry: (entry: TimeEntry) => void;
  setIsEditTimeEntryDialogOpen: (open: boolean) => void;
  handleCompleteTimeEntry: (entry: TimeEntry) => void;
  getStaffMember: (id: string) => StaffMember | undefined;
}

const TimeEntriesTab: React.FC<TimeEntriesTabProps> = ({
  timeEntries,
  staffData,
  selectedStaff,
  setSelectedStaff,
  setIsAddTimeEntryDialogOpen,
  setCurrentTimeEntry,
  setIsEditTimeEntryDialogOpen,
  handleCompleteTimeEntry,
  getStaffMember
}) => {

  // Add logging to help debug the component
  useEffect(() => {
    logger.debug("Component rendered", "TimeEntriesTab", {
      staffCount: staffData.length,
      entriesCount: timeEntries.length,
      selectedStaff
    });

    // Show toast for debugging
    toast.info(`Time Entries Tab loaded with ${timeEntries?.length || 0} entries`);
  }, [timeEntries, staffData, selectedStaff]);
  // Create a simplified version to debug the issue
  try {
    return (
      <Card className="p-6">
        <h2 className="text-2xl font-bold mb-2">Time Entry Management</h2>
        <p className="text-muted-foreground mb-6">View and manage staff clock in/out times</p>

        <div className="flex justify-between items-center mb-6">
          <div>
            <Label className="mb-1 block">Filter by Staff</Label>
            <div className="w-[200px]">
              <select
                className="w-full border rounded-md px-3 py-2 bg-background text-foreground"
                value={selectedStaff || ""}
                onChange={(e) => setSelectedStaff(e.target.value)}
              >
                <option value="">All Staff</option>
                {staffData && staffData.map(staff => (
                  <option key={staff.id} value={staff.id}>{staff.name}</option>
                ))}
              </select>
            </div>
          </div>
          <Button
            onClick={() => setIsAddTimeEntryDialogOpen(true)}
          >
            <Plus className="h-4 w-4 mr-2" /> Add Time Entry
          </Button>
        </div>

        <div className="text-xs text-muted-foreground mb-4">
          Debug: Time entries count: {timeEntries ? timeEntries.length : 'undefined'}
        </div>

        {!timeEntries ? (
          <div className="text-center py-8 text-muted-foreground">Error loading time entries</div>
        ) : timeEntries.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">No time entries found</div>
        ) : (
          <div className="space-y-4">
            {timeEntries
              .filter(entry => !selectedStaff || entry.staffId === selectedStaff)
              .map((entry) => {
                const staff = getStaffMember(entry.staffId);
                return (
                  <div key={entry.id} className="border rounded-md p-4 flex justify-between items-center">
                    <div>
                      <div className="font-medium">{staff?.name || "Unknown"}</div>
                      <div className="text-sm text-muted-foreground">
                        {entry.date} | {entry.clockIn} - {entry.clockOut || "In Progress"}
                      </div>
                      {entry.breakStart && (
                        <div className="text-sm text-muted-foreground">
                          Break: {entry.breakStart} - {entry.breakEnd || "In Progress"}
                        </div>
                      )}
                      <div className="mt-1">
                        <span className="inline-block px-2 py-1 text-xs rounded-full bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-100">
                          {entry.status}
                        </span>
                        {entry.totalHours !== null && (
                          <span className="ml-2 text-sm">{entry.totalHours}h</span>
                        )}
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setCurrentTimeEntry(entry);
                          setIsEditTimeEntryDialogOpen(true);
                        }}
                      >
                        <Pencil className="h-4 w-4 mr-2" /> Edit
                      </Button>
                      {entry.status === "active" && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleCompleteTimeEntry(entry)}
                        >
                          <Clock className="h-4 w-4 mr-2" /> Clock Out
                        </Button>
                      )}
                    </div>
                  </div>
                );
              })}
          </div>
        )}
      </Card>
    );
  } catch (error) {
    logger.logError(error, "TimeEntriesTab render", "TimeEntriesTab");
    toast.error(`Error in Time Entries Tab: ${error.message}`);

    // Fallback UI in case of error
    return (
      <Card className="p-6">
        <h2 className="text-2xl font-bold mb-2">Time Entry Management</h2>
        <div className="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-100 p-4 rounded-md">
          <p>There was an error loading the time entries. Please try again later.</p>
          <p className="text-xs mt-2">Error details: {error.message}</p>
        </div>
        <Button className="mt-4" onClick={() => window.location.reload()}>
          Reload Page
        </Button>
      </Card>
    );
  }
};

export default TimeEntriesTab;
