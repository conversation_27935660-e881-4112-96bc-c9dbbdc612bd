import { useState } from "react";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer, 
  Legend,
  LineChart,
  Line,
  AreaChart,
  Area,
  ScatterChart,
  Scatter,
  ZAxis
} from 'recharts';
import { ForecastData } from "./ForecastCard";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { 
  Download,
  Info,
  Users,
  Clock,
  Calendar
} from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";

interface CustomerTrafficCardProps {
  data?: ForecastData[];
  title?: string;
}

// Custom tooltip component
const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-background border rounded p-2 shadow-sm">
        <p className="font-medium">{label}</p>
        {payload.map((entry: any, index: number) => (
          <p key={`item-${index}`} style={{ color: entry.color }}>
            {entry.name}: {entry.value.toLocaleString()} customers
          </p>
        ))}
      </div>
    );
  }

  return null;
};

// Custom tooltip for hourly data
const HourlyTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-background border rounded p-2 shadow-sm">
        <p className="font-medium">{label}:00</p>
        {payload.map((entry: any, index: number) => (
          <p key={`item-${index}`} style={{ color: entry.color }}>
            {entry.name}: {entry.value.toLocaleString()} customers
          </p>
        ))}
      </div>
    );
  }

  return null;
};

const CustomerTrafficCard = ({ data = [], title = "Customer Traffic Predictions" }: CustomerTrafficCardProps) => {
  const [selectedDay, setSelectedDay] = useState<string>(data.length > 0 ? data[0].day : "");
  const [viewType, setViewType] = useState<"daily" | "hourly">("daily");
  
  // Get traffic data for the selected day
  const dayData = data.find(d => d.day === selectedDay);
  
  // Get hourly traffic data for the selected day
  const hourlyData = dayData?.trafficByHour || [];
  
  // Format hourly data for chart
  const formattedHourlyData = hourlyData.map(h => ({
    hour: h.hour,
    traffic: h.traffic
  }));
  
  // Fill in missing hours (8am to 10pm)
  const completeHourlyData = Array.from({ length: 15 }, (_, i) => i + 8).map(hour => {
    const existingData = formattedHourlyData.find(h => h.hour === hour);
    return existingData || { hour, traffic: 0 };
  });
  
  // Get peak hour
  const peakHour = completeHourlyData.reduce((max, current) => 
    current.traffic > max.traffic ? current : max, completeHourlyData[0]);
  
  // Get daily traffic data
  const dailyTrafficData = data.map(d => ({
    day: d.day,
    traffic: d.customers
  }));
  
  // Get peak day
  const peakDay = dailyTrafficData.reduce((max, current) => 
    current.traffic > max.traffic ? current : max, dailyTrafficData[0]);
  
  // Calculate average daily traffic
  const averageDailyTraffic = Math.round(
    dailyTrafficData.reduce((sum, day) => sum + day.traffic, 0) / dailyTrafficData.length
  );

  return (
    <Card className="col-span-full">
      <CardHeader>
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2">
          <div className="flex items-center gap-2">
            <div>
              <CardTitle>{title}</CardTitle>
              <CardDescription>
                Predict customer traffic patterns by day and time
              </CardDescription>
            </div>
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <Info className="h-4 w-4" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80">
                <div className="space-y-2">
                  <h4 className="font-medium">About Traffic Predictions</h4>
                  <p className="text-sm text-muted-foreground">
                    These predictions analyze historical customer visit patterns, reservation data, 
                    and external factors like weather and local events to forecast traffic.
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Use this data to optimize staffing levels and kitchen preparation.
                  </p>
                </div>
              </PopoverContent>
            </Popover>
          </div>
          <div className="flex items-center gap-2">
            <Select
              value={viewType}
              onValueChange={(value) => setViewType(value as "daily" | "hourly")}
            >
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder="View" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="daily">Daily View</SelectItem>
                <SelectItem value="hourly">Hourly View</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" size="icon">
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="overview">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="patterns">Traffic Patterns</TabsTrigger>
            <TabsTrigger value="heatmap">Day/Time Heatmap</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm font-medium">Peak Day</div>
                      <div className="text-2xl font-bold mt-1">{peakDay.day}</div>
                    </div>
                    <Calendar className="h-8 w-8 text-blue-500" />
                  </div>
                  <div className="text-sm text-muted-foreground mt-2">
                    {peakDay.traffic} expected customers
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm font-medium">Peak Hour</div>
                      <div className="text-2xl font-bold mt-1">{peakHour.hour}:00</div>
                    </div>
                    <Clock className="h-8 w-8 text-purple-500" />
                  </div>
                  <div className="text-sm text-muted-foreground mt-2">
                    {peakHour.traffic} customers per hour
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm font-medium">Average Daily Traffic</div>
                      <div className="text-2xl font-bold mt-1">{averageDailyTraffic}</div>
                    </div>
                    <Users className="h-8 w-8 text-green-500" />
                  </div>
                  <div className="text-sm text-muted-foreground mt-2">
                    Customers per day
                  </div>
                </CardContent>
              </Card>
            </div>
            
            <div className="h-[300px] mt-4">
              {viewType === "daily" ? (
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={dailyTrafficData}
                    margin={{ top: 10, right: 10, left: 10, bottom: 20 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" vertical={false} />
                    <XAxis
                      dataKey="day"
                      tick={{ fontSize: 12 }}
                      tickLine={false}
                      axisLine={false}
                    />
                    <YAxis
                      tick={{ fontSize: 12 }}
                      tickLine={false}
                      axisLine={false}
                    />
                    <Tooltip content={<CustomTooltip />} />
                    <Bar
                      dataKey="traffic"
                      name="Customers"
                      fill="#8b5cf6"
                      radius={[4, 4, 0, 0]}
                      barSize={30}
                    />
                  </BarChart>
                </ResponsiveContainer>
              ) : (
                <div>
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-sm font-medium">Hourly Traffic for {selectedDay || "Selected Day"}</h3>
                    <Select
                      value={selectedDay}
                      onValueChange={setSelectedDay}
                    >
                      <SelectTrigger className="w-[150px]">
                        <SelectValue placeholder="Select day" />
                      </SelectTrigger>
                      <SelectContent>
                        {data.map((day) => (
                          <SelectItem key={day.day} value={day.day}>
                            {day.day}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart
                      data={completeHourlyData}
                      margin={{ top: 10, right: 10, left: 10, bottom: 20 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" vertical={false} />
                      <XAxis
                        dataKey="hour"
                        tickFormatter={(value) => `${value}:00`}
                        tick={{ fontSize: 12 }}
                        tickLine={false}
                        axisLine={false}
                      />
                      <YAxis
                        tick={{ fontSize: 12 }}
                        tickLine={false}
                        axisLine={false}
                      />
                      <Tooltip content={<HourlyTooltip />} />
                      <Area
                        type="monotone"
                        dataKey="traffic"
                        name="Customers"
                        stroke="#8b5cf6"
                        fill="#8b5cf680"
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              )}
            </div>
          </TabsContent>
          
          <TabsContent value="patterns" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base">Daily Traffic Pattern</CardTitle>
                </CardHeader>
                <CardContent className="h-[250px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={dailyTrafficData}
                      margin={{ top: 10, right: 10, left: 10, bottom: 20 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" vertical={false} />
                      <XAxis
                        dataKey="day"
                        tick={{ fontSize: 12 }}
                        tickLine={false}
                        axisLine={false}
                      />
                      <YAxis
                        tick={{ fontSize: 12 }}
                        tickLine={false}
                        axisLine={false}
                      />
                      <Tooltip content={<CustomTooltip />} />
                      <Line
                        type="monotone"
                        dataKey="traffic"
                        name="Customers"
                        stroke="#8b5cf6"
                        strokeWidth={2}
                        dot={{ r: 4 }}
                        activeDot={{ r: 6 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base">Hourly Traffic Pattern</CardTitle>
                </CardHeader>
                <CardContent className="h-[250px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart
                      data={completeHourlyData}
                      margin={{ top: 10, right: 10, left: 10, bottom: 20 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" vertical={false} />
                      <XAxis
                        dataKey="hour"
                        tickFormatter={(value) => `${value}:00`}
                        tick={{ fontSize: 12 }}
                        tickLine={false}
                        axisLine={false}
                      />
                      <YAxis
                        tick={{ fontSize: 12 }}
                        tickLine={false}
                        axisLine={false}
                      />
                      <Tooltip content={<HourlyTooltip />} />
                      <Area
                        type="monotone"
                        dataKey="traffic"
                        name="Customers"
                        stroke="#f59e0b"
                        fill="#f59e0b80"
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>
            
            <div className="grid grid-cols-3 gap-4 mt-4">
              <HoverCard>
                <HoverCardTrigger asChild>
                  <Card className="cursor-help">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="text-sm font-medium">Lunch Rush</div>
                          <div className="text-2xl font-bold mt-1">12:00 - 14:00</div>
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {Math.round(peakHour.traffic * 0.8)} customers
                        </Badge>
                      </div>
                    </CardContent>
                  </Card>
                </HoverCardTrigger>
                <HoverCardContent className="w-80">
                  <div className="space-y-2">
                    <h4 className="font-medium">Lunch Rush Details</h4>
                    <p className="text-sm text-muted-foreground">
                      Typically sees {Math.round(peakHour.traffic * 0.8)} customers during this period.
                      Recommend {Math.ceil(Math.round(peakHour.traffic * 0.8) / 15)} servers and
                      {Math.ceil(Math.round(peakHour.traffic * 0.8) / 25)} kitchen staff.
                    </p>
                  </div>
                </HoverCardContent>
              </HoverCard>
              
              <HoverCard>
                <HoverCardTrigger asChild>
                  <Card className="cursor-help">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="text-sm font-medium">Dinner Rush</div>
                          <div className="text-2xl font-bold mt-1">18:00 - 21:00</div>
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {peakHour.traffic} customers
                        </Badge>
                      </div>
                    </CardContent>
                  </Card>
                </HoverCardTrigger>
                <HoverCardContent className="w-80">
                  <div className="space-y-2">
                    <h4 className="font-medium">Dinner Rush Details</h4>
                    <p className="text-sm text-muted-foreground">
                      Peak traffic time with {peakHour.traffic} customers.
                      Recommend {Math.ceil(peakHour.traffic / 12)} servers and
                      {Math.ceil(peakHour.traffic / 20)} kitchen staff.
                    </p>
                  </div>
                </HoverCardContent>
              </HoverCard>
              
              <HoverCard>
                <HoverCardTrigger asChild>
                  <Card className="cursor-help">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="text-sm font-medium">Quiet Hours</div>
                          <div className="text-2xl font-bold mt-1">14:00 - 17:00</div>
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {Math.round(peakHour.traffic * 0.4)} customers
                        </Badge>
                      </div>
                    </CardContent>
                  </Card>
                </HoverCardTrigger>
                <HoverCardContent className="w-80">
                  <div className="space-y-2">
                    <h4 className="font-medium">Quiet Hours Details</h4>
                    <p className="text-sm text-muted-foreground">
                      Lowest traffic period with approximately {Math.round(peakHour.traffic * 0.4)} customers.
                      Good time for staff breaks and prep work for dinner service.
                    </p>
                  </div>
                </HoverCardContent>
              </HoverCard>
            </div>
          </TabsContent>
          
          <TabsContent value="heatmap" className="space-y-4">
            <div className="h-[400px] mt-4">
              <ResponsiveContainer width="100%" height="100%">
                <ScatterChart
                  margin={{ top: 20, right: 20, bottom: 20, left: 20 }}
                >
                  <CartesianGrid />
                  <XAxis 
                    type="category" 
                    dataKey="hour" 
                    name="Hour" 
                    tickFormatter={(value) => `${value}:00`}
                    allowDuplicatedCategory={false} 
                  />
                  <YAxis 
                    type="category" 
                    dataKey="day" 
                    name="Day" 
                    allowDuplicatedCategory={false} 
                  />
                  <ZAxis 
                    type="number" 
                    dataKey="traffic" 
                    range={[100, 1000]} 
                    name="Traffic" 
                  />
                  <Tooltip cursor={{ strokeDasharray: '3 3' }} />
                  <Scatter 
                    name="Traffic Heatmap" 
                    data={data.flatMap(day => 
                      (day.trafficByHour || []).map(hour => ({
                        day: day.day,
                        hour: hour.hour,
                        traffic: hour.traffic
                      }))
                    )} 
                    fill="#8884d8" 
                  />
                </ScatterChart>
              </ResponsiveContainer>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default CustomerTrafficCard;
