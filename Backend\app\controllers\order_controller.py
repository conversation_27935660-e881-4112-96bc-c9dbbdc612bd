"""
Order controller implementing business logic for order operations.
Handles order CRUD operations, status management, and processing.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime

from app.controllers.base import BaseController
from app.utils.storage_async import get_all_async, get_by_id_async, create_async, update_async, delete_async, query_async
from app.utils.logging_config import logger

class OrderController(BaseController):
    """Controller for order business logic and operations"""
    
    def __init__(self):
        super().__init__()
        self.cache_prefix = "order"
        self.default_cache_ttl = 180  # 3 minutes for order data (more dynamic)
    
    async def get_orders(
        self,
        restaurant_id: Optional[str] = None,
        status: Optional[str] = None,
        table_id: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Get orders with filtering and caching"""
        # Validate pagination
        skip, limit = self.validate_pagination(skip, limit)
        
        # Create cache key based on filters
        filter_key = f"{restaurant_id}_{status}_{table_id}_{skip}_{limit}"
        cache_key = f"{self.cache_prefix}_filtered_{hash(filter_key)}"
        
        # Check cache first
        cached_result = self.get_cached_result(cache_key)
        if cached_result:
            return cached_result
        
        # Fetch orders with filters
        orders = await self.handle_async_operation(
            get_all_async,
            "orders",
            error_message="Failed to fetch orders"
        )
        
        # Apply filters
        filtered_orders = self._apply_filters(orders, restaurant_id, status, table_id)
        
        # Apply pagination
        paginated_orders = filtered_orders[skip:skip + limit]
        
        # Cache the result
        self.cache_result(cache_key, paginated_orders, self.default_cache_ttl)
        
        logger.info(
            f"Retrieved {len(paginated_orders)} orders",
            "OrderController",
            {"total_filtered": len(filtered_orders), "restaurant_id": restaurant_id}
        )
        
        return paginated_orders
    
    def _apply_filters(
        self,
        orders: List[Dict[str, Any]],
        restaurant_id: Optional[str] = None,
        status: Optional[str] = None,
        table_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Apply filters to orders"""
        filtered_orders = orders.copy()
        
        if restaurant_id:
            filtered_orders = [
                order for order in filtered_orders
                if order.get("restaurant_id") == restaurant_id
            ]
        
        if status:
            filtered_orders = [
                order for order in filtered_orders
                if order.get("status") == status
            ]
        
        if table_id:
            filtered_orders = [
                order for order in filtered_orders
                if order.get("table_id") == table_id
            ]
        
        return filtered_orders
    
    async def get_order_by_id(self, order_id: str) -> Optional[Dict[str, Any]]:
        """Get order by ID with caching"""
        cache_key = f"{self.cache_prefix}_{order_id}"
        cached_result = self.get_cached_result(cache_key)
        
        if cached_result:
            return cached_result
        
        order = await self.handle_async_operation(
            get_by_id_async,
            "orders",
            order_id,
            error_message=f"Failed to fetch order {order_id}"
        )
        
        if order:
            self.cache_result(cache_key, order, self.default_cache_ttl)
            logger.info(f"Retrieved order: {order_id}", "OrderController")
        
        return order
    
    async def create_order(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new order"""
        # Add timestamps
        order_data["created_at"] = datetime.now().isoformat()
        order_data["updated_at"] = datetime.now().isoformat()
        order_data["status"] = order_data.get("status", "pending")
        
        created_order = await self.handle_async_operation(
            create_async,
            "orders",
            order_data,
            error_message="Failed to create order"
        )
        
        # Invalidate relevant caches
        self.invalidate_cache_pattern(self.cache_prefix)
        
        logger.info(
            f"Created order: {created_order.get('id')}",
            "OrderController",
            {"restaurant_id": created_order.get("restaurant_id")}
        )
        
        return created_order
    
    async def update_order_status(self, order_id: str, status: str) -> Dict[str, Any]:
        """Update order status"""
        existing_order = await self.get_order_by_id(order_id)
        if not existing_order:
            raise self.handle_not_found("Order", order_id)
        
        update_data = {
            "status": status,
            "updated_at": datetime.now().isoformat()
        }
        
        # Add completion timestamp if order is completed
        if status in ["completed", "delivered"]:
            update_data["completed_at"] = datetime.now().isoformat()
        
        updated_order = await self.handle_async_operation(
            update_async,
            "orders",
            order_id,
            update_data,
            error_message=f"Failed to update order status {order_id}"
        )
        
        # Invalidate relevant caches
        self.invalidate_cache(f"{self.cache_prefix}_{order_id}")
        self.invalidate_cache_pattern(f"{self.cache_prefix}_filtered")
        
        logger.info(
            f"Updated order status: {order_id} -> {status}",
            "OrderController"
        )
        
        return updated_order
