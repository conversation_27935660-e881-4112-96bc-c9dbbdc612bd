import { useState } from "react";
import Layout from "@/components/layout/Layout";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "@/components/ui/sonner";
import {
  Search,
  Plus,
  Pencil,
  Trash2,
  Save,
  X,
  Filter,
  SlidersHorizontal
} from "lucide-react";
import InventoryIngredientSelector from "@/components/menu/InventoryIngredientSelector";
import { AllergenSelector, AllergenBadgeList } from "@/components/allergens";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

// Define types
interface InventoryIngredient {
  inventoryItemId: string;
  name: string;
  quantity: number;
  unit: string;
}

interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  image?: string;
  isAvailable: boolean;
  allergens?: string[];
  ingredients?: string[];
  inventoryIngredients?: InventoryIngredient[];
  nutritionalInfo?: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  };
}

interface Category {
  id: string;
  name: string;
}

interface InventoryItem {
  id: string;
  name: string;
  category: string;
  stock: number;
  unit: string;
  reorderLevel: number;
  cost: number;
  expiryDate?: string;
  isLow: boolean;
  isExpiring: boolean;
}

const MenuManagement = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [activeCategory, setActiveCategory] = useState("all");
  const [isAddItemOpen, setIsAddItemOpen] = useState(false);
  const [isEditItemOpen, setIsEditItemOpen] = useState(false);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [currentItem, setCurrentItem] = useState<MenuItem | null>(null);
  const [newItem, setNewItem] = useState<Omit<MenuItem, "id">>({
    name: "",
    description: "",
    price: 0,
    category: "",
    isAvailable: true,
    allergens: [],
    ingredients: [],
    inventoryIngredients: []
  });

  // Mock inventory data
  const inventoryItems: InventoryItem[] = [
    {
      id: "1",
      name: "Tomatoes",
      category: "Vegetables",
      stock: 15,
      unit: "kg",
      reorderLevel: 10,
      cost: 3.99,
      isLow: false,
      isExpiring: false
    },
    {
      id: "2",
      name: "Chicken Breast",
      category: "Meat",
      stock: 20,
      unit: "kg",
      reorderLevel: 8,
      cost: 12.50,
      isLow: false,
      isExpiring: false
    },
    {
      id: "3",
      name: "Olive Oil",
      category: "Grocery",
      stock: 10,
      unit: "liters",
      reorderLevel: 4,
      cost: 18.75,
      isLow: false,
      isExpiring: false
    },
    {
      id: "4",
      name: "Flour",
      category: "Grocery",
      stock: 25,
      unit: "kg",
      reorderLevel: 10,
      cost: 2.50,
      isLow: false,
      isExpiring: false
    },
    {
      id: "5",
      name: "Mozzarella Cheese",
      category: "Dairy",
      stock: 12,
      unit: "kg",
      reorderLevel: 5,
      cost: 8.99,
      isLow: false,
      isExpiring: false
    },
    {
      id: "6",
      name: "Pepperoni",
      category: "Meat",
      stock: 8,
      unit: "kg",
      reorderLevel: 3,
      cost: 15.99,
      isLow: false,
      isExpiring: false
    },
    {
      id: "7",
      name: "Basil",
      category: "Herbs",
      stock: 2,
      unit: "kg",
      reorderLevel: 1,
      cost: 5.99,
      isLow: false,
      isExpiring: false
    }
  ];

  // Mock menu data
  const [menuItems, setMenuItems] = useState<MenuItem[]>([
    {
      id: "1",
      name: "Classic Margherita Pizza",
      description: "Fresh mozzarella, tomato sauce, basil",
      price: 14.99,
      category: "pizza",
      isAvailable: true,
      allergens: ["gluten", "dairy"],
      ingredients: ["flour", "tomatoes", "mozzarella", "basil", "olive oil"],
      inventoryIngredients: [
        { inventoryItemId: "4", name: "Flour", quantity: 250, unit: "g" },
        { inventoryItemId: "1", name: "Tomatoes", quantity: 150, unit: "g" },
        { inventoryItemId: "5", name: "Mozzarella Cheese", quantity: 120, unit: "g" },
        { inventoryItemId: "7", name: "Basil", quantity: 10, unit: "g" },
        { inventoryItemId: "3", name: "Olive Oil", quantity: 15, unit: "ml" }
      ]
    },
    {
      id: "2",
      name: "Pepperoni Pizza",
      description: "Tomato sauce, mozzarella, pepperoni",
      price: 16.99,
      category: "pizza",
      isAvailable: true,
      allergens: ["gluten", "dairy"],
      ingredients: ["flour", "tomatoes", "mozzarella", "pepperoni"],
      inventoryIngredients: [
        { inventoryItemId: "4", name: "Flour", quantity: 250, unit: "g" },
        { inventoryItemId: "1", name: "Tomatoes", quantity: 150, unit: "g" },
        { inventoryItemId: "5", name: "Mozzarella Cheese", quantity: 100, unit: "g" },
        { inventoryItemId: "6", name: "Pepperoni", quantity: 80, unit: "g" }
      ]
    },
    {
      id: "3",
      name: "Vegetable Supreme Pizza",
      description: "Bell peppers, onions, mushrooms, olives",
      price: 15.99,
      category: "pizza",
      isAvailable: true,
      allergens: ["gluten", "dairy"],
      ingredients: ["flour", "tomatoes", "mozzarella", "bell peppers", "onions", "mushrooms", "olives"]
    },
    {
      id: "4",
      name: "Spaghetti Carbonara",
      description: "Pancetta, egg, parmesan, black pepper",
      price: 18.99,
      category: "pasta",
      isAvailable: true,
      allergens: ["gluten", "dairy", "eggs"],
      ingredients: ["pasta", "pancetta", "eggs", "parmesan", "black pepper"]
    },
    {
      id: "5",
      name: "Fettuccine Alfredo",
      description: "Creamy parmesan sauce with garlic",
      price: 17.99,
      category: "pasta",
      isAvailable: true,
      allergens: ["gluten", "dairy"],
      ingredients: ["pasta", "butter", "cream", "parmesan", "garlic"]
    },
    {
      id: "6",
      name: "Caesar Salad",
      description: "Romaine lettuce, croutons, parmesan",
      price: 12.99,
      category: "salad",
      isAvailable: true,
      allergens: ["gluten", "dairy", "eggs"],
      ingredients: ["romaine lettuce", "croutons", "parmesan", "caesar dressing"]
    },
    {
      id: "7",
      name: "Tiramisu",
      description: "Coffee-soaked ladyfingers with mascarpone",
      price: 8.99,
      category: "dessert",
      isAvailable: true,
      allergens: ["gluten", "dairy", "eggs"],
      ingredients: ["ladyfingers", "coffee", "mascarpone", "cocoa powder"]
    },
    {
      id: "8",
      name: "Coca-Cola",
      description: "Classic soda",
      price: 2.99,
      category: "drinks",
      isAvailable: true,
      ingredients: ["carbonated water", "sugar", "caramel color", "phosphoric acid", "natural flavors", "caffeine"]
    }
  ]);

  // Categories
  const categories: Category[] = [
    { id: "all", name: "All Items" },
    { id: "pizza", name: "Pizza" },
    { id: "pasta", name: "Pasta" },
    { id: "salad", name: "Salads" },
    { id: "appetizer", name: "Appetizers" },
    { id: "dessert", name: "Desserts" },
    { id: "drinks", name: "Drinks" }
  ];

  // Filter menu items based on search and category
  const filteredItems = menuItems.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = activeCategory === "all" || item.category === activeCategory;
    return matchesSearch && matchesCategory;
  });

  // Add new menu item
  const handleAddItem = () => {
    if (!newItem.name || !newItem.category || newItem.price <= 0) {
      toast.error("Please fill in all required fields");
      return;
    }

    const id = (menuItems.length + 1).toString();
    const itemToAdd = {
      ...newItem,
      id
    };

    setMenuItems([...menuItems, itemToAdd]);
    setIsAddItemOpen(false);
    setNewItem({
      name: "",
      description: "",
      price: 0,
      category: "",
      isAvailable: true,
      allergens: [],
      ingredients: []
    });
    toast.success("Menu item added successfully");
  };

  // Edit menu item
  const handleEditItem = () => {
    if (!currentItem) return;

    if (!currentItem.name || !currentItem.category || currentItem.price <= 0) {
      toast.error("Please fill in all required fields");
      return;
    }

    setMenuItems(menuItems.map(item =>
      item.id === currentItem.id ? currentItem : item
    ));
    setIsEditItemOpen(false);
    setCurrentItem(null);
    toast.success("Menu item updated successfully");
  };

  // Delete menu item
  const handleDeleteItem = () => {
    if (!currentItem) return;

    setMenuItems(menuItems.filter(item => item.id !== currentItem.id));
    setIsDeleteConfirmOpen(false);
    setCurrentItem(null);
    toast.success("Menu item deleted successfully");
  };

  // Toggle item availability
  const toggleItemAvailability = (id: string) => {
    setMenuItems(menuItems.map(item =>
      item.id === id ? { ...item, isAvailable: !item.isAvailable } : item
    ));
    toast.success("Item availability updated");
  };

  return (
    <Layout title="Menu Management" requiredRoles={["admin", "manager"]}>
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex flex-col sm:flex-row gap-4 flex-1">
            <div className="relative w-full sm:w-64">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search menu items..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Select value={activeCategory} onValueChange={setActiveCategory}>
              <SelectTrigger className="w-full sm:w-40">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                {categories.map(category => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <Button onClick={() => setIsAddItemOpen(true)}>
            <Plus className="mr-2 h-4 w-4" /> Add Menu Item
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Menu Items</CardTitle>
            <CardDescription>
              Manage your restaurant's menu items
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Allergens</TableHead>
                  <TableHead>Price</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredItems.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                      No menu items found
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredItems.map(item => (
                    <TableRow key={item.id}>
                      <TableCell>
                        <div className="font-medium">{item.name}</div>
                        <div className="text-sm text-muted-foreground">{item.description}</div>
                        {item.inventoryIngredients && item.inventoryIngredients.length > 0 && (
                          <div className="mt-1 text-xs text-muted-foreground">
                            <span className="font-medium">Average usage per item:</span>{" "}
                            {item.inventoryIngredients.map((ing, i) => (
                              <span key={ing.inventoryItemId}>
                                {ing.name} ({ing.quantity}{ing.unit})
                                {i < item.inventoryIngredients!.length - 1 ? ", " : ""}
                              </span>
                            ))}
                          </div>
                        )}
                      </TableCell>
                      <TableCell>{item.category}</TableCell>
                      <TableCell>£{item.price.toFixed(2)}</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <div
                            className={`h-2.5 w-2.5 rounded-full mr-2 ${
                              item.isAvailable ? "bg-green-500" : "bg-red-500"
                            }`}
                          />
                          <span>{item.isAvailable ? "Available" : "Unavailable"}</span>
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => toggleItemAvailability(item.id)}
                            title={item.isAvailable ? "Mark as unavailable" : "Mark as available"}
                          >
                            <SlidersHorizontal className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => {
                              setCurrentItem(item);
                              setIsEditItemOpen(true);
                            }}
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => {
                              setCurrentItem(item);
                              setIsDeleteConfirmOpen(true);
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>

      {/* Add Menu Item Dialog */}
      <Dialog open={isAddItemOpen} onOpenChange={setIsAddItemOpen}>
        <DialogContent className="sm:max-w-[650px] max-h-[90vh] flex flex-col">
          <DialogHeader>
            <DialogTitle>Add Menu Item</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4 overflow-y-auto flex-1 pr-2">
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="name">Name *</Label>
                <Input
                  id="name"
                  value={newItem.name}
                  onChange={(e) => setNewItem({ ...newItem, name: e.target.value })}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="category">Category *</Label>
                <Select
                  value={newItem.category}
                  onValueChange={(value) => setNewItem({ ...newItem, category: value })}
                >
                  <SelectTrigger id="category">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.filter(c => c.id !== "all").map(category => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="description">Description</Label>
              <Input
                id="description"
                value={newItem.description}
                onChange={(e) => setNewItem({ ...newItem, description: e.target.value })}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="price">Price *</Label>
                <Input
                  id="price"
                  type="number"
                  min="0"
                  step="0.01"
                  value={newItem.price}
                  onChange={(e) => setNewItem({ ...newItem, price: parseFloat(e.target.value) })}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="availability">Availability</Label>
                <Select
                  value={newItem.isAvailable ? "available" : "unavailable"}
                  onValueChange={(value) => setNewItem({ ...newItem, isAvailable: value === "available" })}
                >
                  <SelectTrigger id="availability">
                    <SelectValue placeholder="Select availability" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="available">Available</SelectItem>
                    <SelectItem value="unavailable">Unavailable</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="border-t pt-4 mt-2">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium">Inventory Ingredients</h3>
                <p className="text-sm text-muted-foreground">Track average ingredient quantities (g/ml) used in this item</p>
              </div>
              <InventoryIngredientSelector
                inventoryItems={inventoryItems}
                selectedIngredients={newItem.inventoryIngredients || []}
                onChange={(ingredients) => setNewItem({ ...newItem, inventoryIngredients: ingredients })}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddItemOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddItem}>
              Add Item
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Menu Item Dialog */}
      <Dialog open={isEditItemOpen} onOpenChange={setIsEditItemOpen}>
        <DialogContent className="sm:max-w-[650px] max-h-[90vh] flex flex-col">
          <DialogHeader>
            <DialogTitle>Edit Menu Item</DialogTitle>
          </DialogHeader>
          {currentItem && (
            <div className="grid gap-4 py-4 overflow-y-auto flex-1 pr-2">
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="edit-name">Name *</Label>
                  <Input
                    id="edit-name"
                    value={currentItem.name}
                    onChange={(e) => setCurrentItem({ ...currentItem, name: e.target.value })}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="edit-category">Category *</Label>
                  <Select
                    value={currentItem.category}
                    onValueChange={(value) => setCurrentItem({ ...currentItem, category: value })}
                  >
                    <SelectTrigger id="edit-category">
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.filter(c => c.id !== "all").map(category => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-description">Description</Label>
                <Input
                  id="edit-description"
                  value={currentItem.description}
                  onChange={(e) => setCurrentItem({ ...currentItem, description: e.target.value })}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="edit-price">Price *</Label>
                  <Input
                    id="edit-price"
                    type="number"
                    min="0"
                    step="0.01"
                    value={currentItem.price}
                    onChange={(e) => setCurrentItem({ ...currentItem, price: parseFloat(e.target.value) })}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="edit-availability">Availability</Label>
                  <Select
                    value={currentItem.isAvailable ? "available" : "unavailable"}
                    onValueChange={(value) => setCurrentItem({ ...currentItem, isAvailable: value === "available" })}
                  >
                    <SelectTrigger id="edit-availability">
                      <SelectValue placeholder="Select availability" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="available">Available</SelectItem>
                      <SelectItem value="unavailable">Unavailable</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="border-t pt-4 mt-2">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium">Inventory Ingredients</h3>
                  <p className="text-sm text-muted-foreground">Track average ingredient quantities (g/ml) used in this item</p>
                </div>
                <InventoryIngredientSelector
                  inventoryItems={inventoryItems}
                  selectedIngredients={currentItem.inventoryIngredients || []}
                  onChange={(ingredients) => setCurrentItem({ ...currentItem, inventoryIngredients: ingredients })}
                />
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditItemOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleEditItem}>
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteConfirmOpen} onOpenChange={setIsDeleteConfirmOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>Are you sure you want to delete "{currentItem?.name}"? This action cannot be undone.</p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteConfirmOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteItem}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Layout>
  );
};

export default MenuManagement;
