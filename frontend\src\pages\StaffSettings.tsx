import React, { useState, useEffect } from "react";
import Layout from "@/components/layout/Layout";
import { useTheme } from "next-themes";
import { toast } from "@/components/ui/sonner";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { 
  Settings as SettingsIcon, 
  Bell, 
  User, 
  Clock, 
  Calendar,
  Moon,
  Sun,
  Monitor
} from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";

const StaffSettings = () => {
  const [activeTab, setActiveTab] = useState("appearance");
  const { theme, setTheme } = useTheme();
  const { user } = useAuth();
  
  // Staff personal settings
  const [personalSettings, setPersonalSettings] = useState({
    theme: "system",
    notifications: {
      enableShiftReminders: true,
      enableScheduleChanges: true,
      enableBreakReminders: true,
      reminderTime: 30 // minutes before shift
    },
    preferences: {
      defaultPage: "dashboard",
      clockInReminder: true,
      showBreakTimer: true
    },
    availability: {
      monday: { available: true, startTime: "09:00", endTime: "17:00" },
      tuesday: { available: true, startTime: "09:00", endTime: "17:00" },
      wednesday: { available: true, startTime: "09:00", endTime: "17:00" },
      thursday: { available: true, startTime: "09:00", endTime: "17:00" },
      friday: { available: true, startTime: "09:00", endTime: "17:00" },
      saturday: { available: false, startTime: "09:00", endTime: "17:00" },
      sunday: { available: false, startTime: "09:00", endTime: "17:00" }
    }
  });

  // Sync theme from provider
  useEffect(() => {
    if (theme) {
      setPersonalSettings(prev => ({
        ...prev,
        theme: theme
      }));
    }
  }, [theme]);

  // Handle theme change
  const handleThemeChange = (value) => {
    setPersonalSettings({...personalSettings, theme: value});
    setTheme(value);
    toast.success(`Theme changed to ${value}`);
  };

  // Handle notification settings change
  const handleNotificationChange = (key, value) => {
    setPersonalSettings({
      ...personalSettings,
      notifications: {
        ...personalSettings.notifications,
        [key]: value
      }
    });
  };

  // Handle preferences change
  const handlePreferenceChange = (key, value) => {
    setPersonalSettings({
      ...personalSettings,
      preferences: {
        ...personalSettings.preferences,
        [key]: value
      }
    });
  };

  // Save settings
  const saveSettings = () => {
    // In a real app, this would save to a database or localStorage
    toast.success("Your settings have been saved");
  };

  return (
    <Layout title="Staff Settings" requiredRoles={["staff", "waiter", "chef", "hostess", "bartender"]}>
      <div className="p-4">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="appearance">
              <Moon className="h-4 w-4 mr-2" />
              Appearance
            </TabsTrigger>
            <TabsTrigger value="notifications">
              <Bell className="h-4 w-4 mr-2" />
              Notifications
            </TabsTrigger>
            <TabsTrigger value="preferences">
              <User className="h-4 w-4 mr-2" />
              Preferences
            </TabsTrigger>
            <TabsTrigger value="availability">
              <Calendar className="h-4 w-4 mr-2" />
              Availability
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="appearance">
            <Card>
              <CardHeader>
                <CardTitle>Appearance Settings</CardTitle>
                <CardDescription>Customize how the application looks</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid gap-2">
                    <Label htmlFor="theme">Theme</Label>
                    <div className="grid grid-cols-3 gap-2">
                      <Button 
                        variant={personalSettings.theme === "light" ? "default" : "outline"} 
                        className="flex flex-col items-center justify-center h-24 gap-2"
                        onClick={() => handleThemeChange("light")}
                      >
                        <Sun className="h-8 w-8" />
                        <span>Light</span>
                      </Button>
                      <Button 
                        variant={personalSettings.theme === "dark" ? "default" : "outline"} 
                        className="flex flex-col items-center justify-center h-24 gap-2"
                        onClick={() => handleThemeChange("dark")}
                      >
                        <Moon className="h-8 w-8" />
                        <span>Dark</span>
                      </Button>
                      <Button 
                        variant={personalSettings.theme === "system" ? "default" : "outline"} 
                        className="flex flex-col items-center justify-center h-24 gap-2"
                        onClick={() => handleThemeChange("system")}
                      >
                        <Monitor className="h-8 w-8" />
                        <span>System</span>
                      </Button>
                    </div>
                  </div>
                  
                  <div>
                    <p className="text-sm text-muted-foreground">Current theme: {theme}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="notifications">
            <Card>
              <CardHeader>
                <CardTitle>Notification Settings</CardTitle>
                <CardDescription>Manage your notification preferences</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="enableShiftReminders">Shift Reminders</Label>
                      <p className="text-sm text-muted-foreground">
                        Receive notifications before your shifts
                      </p>
                    </div>
                    <Switch
                      id="enableShiftReminders"
                      checked={personalSettings.notifications.enableShiftReminders}
                      onCheckedChange={(checked) => handleNotificationChange('enableShiftReminders', checked)}
                    />
                  </div>
                  
                  <Separator />
                  
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="enableScheduleChanges">Schedule Changes</Label>
                      <p className="text-sm text-muted-foreground">
                        Receive notifications when your schedule changes
                      </p>
                    </div>
                    <Switch
                      id="enableScheduleChanges"
                      checked={personalSettings.notifications.enableScheduleChanges}
                      onCheckedChange={(checked) => handleNotificationChange('enableScheduleChanges', checked)}
                    />
                  </div>
                  
                  <Separator />
                  
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="enableBreakReminders">Break Reminders</Label>
                      <p className="text-sm text-muted-foreground">
                        Receive notifications for scheduled breaks
                      </p>
                    </div>
                    <Switch
                      id="enableBreakReminders"
                      checked={personalSettings.notifications.enableBreakReminders}
                      onCheckedChange={(checked) => handleNotificationChange('enableBreakReminders', checked)}
                    />
                  </div>
                  
                  <Separator />
                  
                  <div className="grid gap-2">
                    <Label htmlFor="reminderTime">Reminder Time (minutes before shift)</Label>
                    <Input
                      id="reminderTime"
                      type="number"
                      min="5"
                      max="120"
                      value={personalSettings.notifications.reminderTime}
                      onChange={(e) => handleNotificationChange('reminderTime', parseInt(e.target.value) || 30)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="preferences">
            <Card>
              <CardHeader>
                <CardTitle>Preferences</CardTitle>
                <CardDescription>Customize your work preferences</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid gap-2">
                    <Label htmlFor="defaultPage">Default Page</Label>
                    <Select
                      value={personalSettings.preferences.defaultPage}
                      onValueChange={(value) => handlePreferenceChange('defaultPage', value)}
                    >
                      <SelectTrigger id="defaultPage">
                        <SelectValue placeholder="Select default page" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="dashboard">Dashboard</SelectItem>
                        <SelectItem value="epos">EPOS</SelectItem>
                        <SelectItem value="inventory">Inventory</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <Separator />
                  
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="clockInReminder">Clock In Reminder</Label>
                      <p className="text-sm text-muted-foreground">
                        Show a reminder to clock in when you log in
                      </p>
                    </div>
                    <Switch
                      id="clockInReminder"
                      checked={personalSettings.preferences.clockInReminder}
                      onCheckedChange={(checked) => handlePreferenceChange('clockInReminder', checked)}
                    />
                  </div>
                  
                  <Separator />
                  
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="showBreakTimer">Show Break Timer</Label>
                      <p className="text-sm text-muted-foreground">
                        Show a countdown timer during breaks
                      </p>
                    </div>
                    <Switch
                      id="showBreakTimer"
                      checked={personalSettings.preferences.showBreakTimer}
                      onCheckedChange={(checked) => handlePreferenceChange('showBreakTimer', checked)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="availability">
            <Card>
              <CardHeader>
                <CardTitle>Availability</CardTitle>
                <CardDescription>Set your weekly availability for scheduling</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  This information will be used by managers when creating the schedule. 
                  It does not guarantee you will be scheduled during these times.
                </p>
                
                <div className="space-y-4">
                  {/* This would be a more complex component with time selectors for each day */}
                  <p className="text-sm font-medium">Coming soon: Availability settings</p>
                  
                  <p className="text-xs text-muted-foreground">
                    In the full implementation, you would be able to set your availability 
                    for each day of the week with specific time ranges.
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
        
        <div className="mt-4 flex justify-end">
          <Button onClick={saveSettings}>
            Save Settings
          </Button>
        </div>
      </div>
    </Layout>
  );
};

export default StaffSettings;
