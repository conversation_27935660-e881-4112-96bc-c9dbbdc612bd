"""
Debug script to check memory storage state
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.utils.storage import storage as memory_storage

def test_memory():
    """Test memory storage state"""
    try:
        print("Checking memory storage state...")
        
        print(f"Memory storage keys: {list(memory_storage.keys())}")
        
        restaurants = memory_storage.get("restaurants", [])
        print(f"Restaurants in memory: {len(restaurants)}")
        
        users = memory_storage.get("restaurant_users", [])
        print(f"Restaurant users in memory: {len(users)}")
        
        if restaurants:
            print(f"First restaurant in memory: {restaurants[0].get('name')}")
        
        print("\n✅ Memory storage check completed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Memory storage check failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_memory()
    sys.exit(0 if success else 1)
