import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, <PERSON><PERSON>Title, DialogFooter } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { toast } from '@/components/ui/sonner';
import { Trash2, Tag, X } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { getAuthHeaders } from '@/utils/authUtils';

interface PromoCode {
  id: string;
  code: string;
  name: string;
  description?: string;
  discount_type: 'percentage' | 'fixed_amount';
  discount_value: number;
  scope: string;
  minimum_spend?: number;
  maximum_discount?: number;
  start_date: string;
  end_date: string;
  usage_limit?: number;
  usage_count: number;
  is_active: boolean;
}

interface DiscountApplication {
  promo_code: string;
  discount_amount: number;
  applied_to_items?: string[];
}

interface DiscountManagerProps {
  isOpen: boolean;
  onClose: () => void;
  orderTotal: number;
  orderItems: any[];
  appliedDiscounts: DiscountApplication[];
  onDiscountApplied: (discount: DiscountApplication) => void;
  onDiscountRemoved: (promoCode: string) => void;
}

const DiscountManager: React.FC<DiscountManagerProps> = ({
  isOpen,
  onClose,
  orderTotal,
  orderItems,
  appliedDiscounts,
  onDiscountApplied,
  onDiscountRemoved
}) => {
  const { user } = useAuth();
  const [promoCode, setPromoCode] = useState('');
  const [selectedPromoCode, setSelectedPromoCode] = useState('');
  const [customDiscountPercent, setCustomDiscountPercent] = useState('');
  const [isValidating, setIsValidating] = useState(false);
  const [availablePromoCodes, setAvailablePromoCodes] = useState<PromoCode[]>([]);
  const [activeTab, setActiveTab] = useState('dropdown');

  useEffect(() => {
    fetchAvailablePromoCodes();
  }, []);

  const fetchAvailablePromoCodes = async () => {
    try {
      // Try the public active promo codes endpoint first (no auth required)
      const response = await fetch('/api/discounts/promo-codes/active');
      if (response.ok) {
        const promoCodes = await response.json();
        setAvailablePromoCodes(promoCodes);
        console.log('✅ Successfully fetched promo codes from public endpoint:', promoCodes.length);
      } else if (response.status === 401) {
        // Fallback to authenticated endpoint
        console.log('🔄 Public endpoint failed, trying authenticated endpoint...');
        const authResponse = await fetch('/api/discounts/promo-codes', {
          headers: getAuthHeaders(user)
        });
        if (authResponse.ok) {
          const promoCodes = await authResponse.json();
          // Filter active promo codes
          const activePromoCodes = promoCodes.filter((code: PromoCode) =>
            code.is_active && new Date(code.end_date) > new Date()
          );
          setAvailablePromoCodes(activePromoCodes);
          console.log('✅ Successfully fetched promo codes from authenticated endpoint:', activePromoCodes.length);
        } else {
          console.log('❌ Both endpoints failed, using fallback codes');
          setAvailablePromoCodes(getFallbackPromoCodes());
        }
      } else {
        console.log('❌ Public endpoint failed with status:', response.status, 'using fallback codes');
        setAvailablePromoCodes(getFallbackPromoCodes());
      }
    } catch (error) {
      console.error('Failed to fetch promo codes:', error);
      console.log('❌ Network error, using fallback codes');
      setAvailablePromoCodes(getFallbackPromoCodes());
    }
  };

  const getFallbackPromoCodes = (): PromoCode[] => {
    return [
      {
        id: 'fallback-1',
        code: 'SAVE10',
        name: '10% Off Total Order',
        description: 'Get 10% off your entire order',
        discount_type: 'percentage',
        discount_value: 10,
        scope: 'order_total',
        minimum_spend: 20,
        maximum_discount: 50,
        start_date: new Date().toISOString(),
        end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        usage_limit: 100,
        usage_count: 0,
        is_active: true
      },
      {
        id: 'fallback-2',
        code: 'WELCOME5',
        name: '£5 Off Welcome Offer',
        description: 'Get £5 off your first order',
        discount_type: 'fixed_amount',
        discount_value: 5,
        scope: 'order_total',
        minimum_spend: 25,
        maximum_discount: 75,
        start_date: new Date().toISOString(),
        end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        usage_limit: 50,
        usage_count: 0,
        is_active: true
      },
      {
        id: 'fallback-3',
        code: 'HAPPYHOUR',
        name: 'Happy Hour 15% Off',
        description: '15% off during happy hour',
        discount_type: 'percentage',
        discount_value: 15,
        scope: 'order_total',
        minimum_spend: 15,
        start_date: new Date().toISOString(),
        end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        usage_limit: 200,
        usage_count: 0,
        is_active: true
      }
    ];
  };

  const validateAndApplyPromoCode = async () => {
    if (!promoCode.trim()) {
      toast.error('Please enter a promo code');
      return;
    }

    // Check if promo code is already applied
    if (appliedDiscounts.some(discount => discount.promo_code === promoCode.toUpperCase())) {
      toast.error('This promo code is already applied');
      return;
    }

    setIsValidating(true);

    try {
      const response = await fetch('/api/discounts/validate-promo-code', {
        method: 'POST',
        headers: getAuthHeaders(user),
        body: JSON.stringify({
          code: promoCode,
          order_total: orderTotal,
          order_items: orderItems
        }),
      });

      const result = await response.json();
      console.log('Promo code validation response:', { response: response.status, result });

      if (response.ok && result.is_valid) {
        const discountApplication: DiscountApplication = {
          promo_code: promoCode.toUpperCase(),
          discount_amount: result.discount_amount,
          applied_to_items: result.applicable_items
        };

        onDiscountApplied(discountApplication);
        setPromoCode('');
        toast.success(`Discount applied! Saved £${result.discount_amount.toFixed(2)}`);
      } else if (response.status === 401) {
        toast.error('Authentication required. Please log in again.');
      } else {
        const errorMsg = result.error_message || result.detail || 'Invalid promo code';
        console.log('Promo code validation failed:', errorMsg);
        toast.error(errorMsg);
      }
    } catch (error) {
      console.error('Error validating promo code:', error);
      toast.error('Failed to validate promo code');
    } finally {
      setIsValidating(false);
    }
  };

  const removeDiscount = (promoCode: string) => {
    onDiscountRemoved(promoCode);
    toast.success('Discount removed');
  };

  const applyQuickPromoCode = (code: string) => {
    setPromoCode(code);
    setTimeout(() => validateAndApplyPromoCode(), 100);
  };

  const applySelectedPromoCode = async () => {
    if (!selectedPromoCode) {
      toast.error('Please select a promo code');
      return;
    }

    const selectedCode = availablePromoCodes.find(code => code.id === selectedPromoCode);
    if (selectedCode) {
      setPromoCode(selectedCode.code);
      setTimeout(() => validateAndApplyPromoCode(), 100);
    }
  };

  const applyCustomDiscount = async () => {
    const discountPercent = parseFloat(customDiscountPercent);
    const maxDiscountPercent = 40; // Maximum discount limit

    if (!customDiscountPercent || discountPercent <= 0 || discountPercent > 100) {
      toast.error('Please enter a valid discount percentage (1-100)');
      return;
    }

    // Check maximum discount limit
    if (discountPercent > maxDiscountPercent) {
      toast.error(`Discount cannot exceed ${maxDiscountPercent}%. Please contact a manager for higher discounts.`);
      return;
    }

    const discountAmount = orderTotal * (discountPercent / 100);
    const customDiscount: DiscountApplication = {
      promo_code: `MANAGER_${discountPercent}%`,
      discount_amount: discountAmount,
      applied_to_items: undefined
    };

    onDiscountApplied(customDiscount);
    setCustomDiscountPercent('');
    toast.success(`Manager discount of ${customDiscountPercent}% applied!`);
  };

  const totalDiscount = appliedDiscounts.reduce((sum, discount) => sum + discount.discount_amount, 0);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto" aria-describedby="discount-description">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">Apply Discount</DialogTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </Button>
        </DialogHeader>

        <div id="discount-description" className="sr-only">
          Apply discounts and promo codes to your order. Choose from available codes, enter manually, or apply manager discounts.
        </div>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="dropdown">Select Code</TabsTrigger>
              <TabsTrigger value="manual">Manual Entry</TabsTrigger>
              <TabsTrigger value="custom">Manager Discount</TabsTrigger>
            </TabsList>

            <div className="mt-6">
              {/* Dropdown Selection Tab */}
              <TabsContent value="dropdown" className="space-y-4">
                <div className="space-y-3">
                  <Label htmlFor="promo-select" className="text-sm font-medium">
                    Select Available Promo Code
                  </Label>
                  <Select value={selectedPromoCode} onValueChange={setSelectedPromoCode}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder={
                        availablePromoCodes.length === 0
                          ? "No promo codes available..."
                          : "Choose a promo code..."
                      } />
                    </SelectTrigger>
                    <SelectContent>
                      {availablePromoCodes.length === 0 ? (
                        <div className="p-4 text-center text-sm text-muted-foreground">
                          No active promo codes available
                        </div>
                      ) : (
                        availablePromoCodes.map((code) => (
                          <SelectItem key={code.id} value={code.id}>
                            <div className="flex items-center justify-between w-full">
                              <div className="flex items-center gap-2">
                                <Badge variant="outline" className="text-xs">
                                  {code.code}
                                </Badge>
                                <span className="font-medium">{code.name}</span>
                              </div>
                              <div className="text-right ml-4">
                                <span className="text-sm font-bold text-green-600">
                                  {code.discount_type === 'percentage'
                                    ? `${code.discount_value}% OFF`
                                    : `£${code.discount_value} OFF`
                                  }
                                </span>
                              </div>
                            </div>
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>

                  {selectedPromoCode && (
                    <div className="p-3 bg-blue-50 dark:bg-blue-900/30 rounded-md border border-blue-200 dark:border-blue-700">
                      {(() => {
                        const selectedCode = availablePromoCodes.find(code => code.id === selectedPromoCode);
                        return selectedCode ? (
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <span className="font-medium text-foreground">{selectedCode.name}</span>
                              <Badge variant="secondary">{selectedCode.code}</Badge>
                            </div>
                            <p className="text-sm text-muted-foreground">{selectedCode.description}</p>
                            {selectedCode.minimum_spend && (
                              <p className="text-xs text-muted-foreground">
                                Minimum spend: £{selectedCode.minimum_spend}
                              </p>
                            )}
                            <div className="flex items-center gap-2 text-sm">
                              <span className="text-foreground">Discount:</span>
                              <span className="font-bold text-green-600 dark:text-green-400">
                                {selectedCode.discount_type === 'percentage'
                                  ? `${selectedCode.discount_value}% OFF`
                                  : `£${selectedCode.discount_value} OFF`
                                }
                              </span>
                            </div>
                          </div>
                        ) : null;
                      })()}
                    </div>
                  )}

                  <Button
                    onClick={applySelectedPromoCode}
                    disabled={!selectedPromoCode || isValidating || availablePromoCodes.length === 0}
                    className="w-full"
                  >
                    {isValidating ? 'Applying...' :
                     availablePromoCodes.length === 0 ? 'No Codes Available' :
                     'Apply Selected Code'}
                  </Button>
                </div>
              </TabsContent>

              {/* Manual Entry Tab */}
              <TabsContent value="manual" className="space-y-4">
                <div className="space-y-3">
                  <Label htmlFor="promo-code" className="text-sm font-medium">
                    Enter Promo Code Manually
                  </Label>
                  <div className="flex gap-2">
                    <Input
                      id="promo-code"
                      value={promoCode}
                      onChange={(e) => setPromoCode(e.target.value.toUpperCase())}
                      placeholder="Enter code..."
                      className="flex-1"
                      onKeyDown={(e) => e.key === 'Enter' && validateAndApplyPromoCode()}
                    />
                    <Button
                      onClick={validateAndApplyPromoCode}
                      disabled={isValidating || !promoCode.trim()}
                      size="sm"
                    >
                      {isValidating ? 'Validating...' : 'Apply'}
                    </Button>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Enter any valid promo code, including special codes not listed above.
                  </p>
                </div>
              </TabsContent>

              {/* Manager Custom Discount Tab */}
              <TabsContent value="custom" className="space-y-4">
                <div className="space-y-3">
                  <Label htmlFor="custom-discount" className="text-sm font-medium">
                    Manager Discount (Percentage)
                  </Label>
                  <div className="flex gap-2">
                    <Input
                      id="custom-discount"
                      type="number"
                      min="1"
                      max="40"
                      step="1"
                      value={customDiscountPercent}
                      onChange={(e) => setCustomDiscountPercent(e.target.value)}
                      placeholder="Enter percentage (1-40)"
                      className="flex-1"
                    />
                    <Button
                      onClick={applyCustomDiscount}
                      disabled={!customDiscountPercent || parseFloat(customDiscountPercent) <= 0}
                      size="sm"
                    >
                      Apply
                    </Button>
                  </div>
                  <div className="p-3 bg-yellow-50 dark:bg-yellow-900/30 rounded-md border border-yellow-200 dark:border-yellow-700">
                    <p className="text-xs text-yellow-800 dark:text-yellow-200">
                      <strong>Manager Override:</strong> This allows applying a custom percentage discount.
                      <br />
                      <strong>Maximum Limit:</strong> 40% - Higher discounts require senior manager approval.
                    </p>
                    {customDiscountPercent && parseFloat(customDiscountPercent) > 0 && (
                      <div className="mt-2">
                        <p className="text-sm font-medium text-yellow-900 dark:text-yellow-100">
                          Discount Amount: £{(orderTotal * (parseFloat(customDiscountPercent) / 100)).toFixed(2)}
                        </p>
                        {parseFloat(customDiscountPercent) > 40 && (
                          <p className="text-xs text-red-600 dark:text-red-400 font-medium mt-1">
                            ⚠️ Exceeds maximum limit of 40%
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </TabsContent>
            </div>
          </Tabs>

          {/* Applied Discounts Section */}
          {appliedDiscounts.length > 0 && (
            <div className="mt-6 space-y-3">
              <Separator />
              <div>
                <Label className="text-sm font-medium">Applied Discounts</Label>
                <div className="mt-2 space-y-2">
                  {appliedDiscounts.map((discount, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/30 rounded-md border border-green-200 dark:border-green-700">
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary" className="bg-green-100 dark:bg-green-900/50 text-green-800 dark:text-green-200">
                          {discount.promo_code}
                        </Badge>
                        <span className="text-sm font-medium text-green-700 dark:text-green-300">
                          -£{discount.discount_amount.toFixed(2)}
                        </span>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeDiscount(discount.promo_code)}
                        className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/30"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}
                </div>
                <div className="mt-3 pt-3 border-t border-green-200 dark:border-green-700">
                  <div className="flex justify-between items-center font-semibold">
                    <span className="text-foreground">Total Discount:</span>
                    <span className="text-green-600 dark:text-green-400 text-lg">-£{totalDiscount.toFixed(2)}</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
  );
};

export default DiscountManager;
