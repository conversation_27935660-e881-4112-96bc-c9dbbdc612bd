"""
Debug script to test storage_async and database service
"""

import asyncio
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.utils.storage_async import get_all_async
from app.services.database_service import db_service

async def test_storage():
    """Test storage operations"""
    try:
        print("Testing storage_async...")
        
        # Test storage_async
        print("\n1. Testing storage_async.get_all_async('restaurants')...")
        restaurants = await get_all_async("restaurants")
        print(f"Found {len(restaurants)} restaurants via storage_async")
        
        # Test database service directly
        print("\n2. Testing db_service.get_all('restaurants') directly...")
        restaurants_direct = await db_service.get_all("restaurants")
        print(f"Found {len(restaurants_direct)} restaurants via db_service")
        
        if restaurants_direct:
            print(f"First restaurant: {restaurants_direct[0].get('name')} (Code: {restaurants_direct[0].get('code')})")
        
        print("\n✅ Storage test completed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Storage test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_storage())
    sys.exit(0 if success else 1)
