"""
Split Bills MVC router implementing EPOS split bill and payment functionality.
Uses SplitBillController for business logic and provides RESTful API endpoints.
"""

from fastapi import APIRouter, HTTPException, status, Depends, Query
from typing import List, Optional
from pydantic import BaseModel

from app.controllers.split_bill_controller import SplitBillController
from app.utils.logging_config import logger

router = APIRouter(prefix="/split-bills", tags=["Split Bills (MVC)"])

# Pydantic models for request/response
class SplitDetail(BaseModel):
    amount: float
    customer_name: Optional[str] = None
    customer_email: Optional[str] = None
    items: Optional[List[str]] = None

class SplitBillCreate(BaseModel):
    order_id: str
    split_details: List[SplitDetail]
    split_type: str = "equal"  # "equal", "custom", "by_item"

class PaymentDetails(BaseModel):
    amount: float
    payment_method: str  # "cash", "card", "digital_wallet", "bank_transfer"
    transaction_id: Optional[str] = None

class PaymentRequest(BaseModel):
    split_bill_id: str
    portion_number: int
    payment_details: PaymentDetails

# Dependency injection for controller
async def get_split_bill_controller() -> SplitBillController:
    """Dependency injection for split bill controller"""
    return SplitBillController()

@router.get("/")
async def get_split_bills(
    order_id: Optional[str] = Query(None, description="Filter by order ID"),
    restaurant_id: Optional[str] = Query(None, description="Filter by restaurant ID"),
    status: Optional[str] = Query(None, description="Filter by status"),
    skip: int = Query(0, ge=0, description="Number of split bills to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of split bills to return"),
    controller: SplitBillController = Depends(get_split_bill_controller)
):
    """
    Get split bills with filtering.
    
    This endpoint supports EPOS functionality by providing:
    - Split bill status tracking
    - Order-specific split bills
    - Restaurant-wide split bill management
    """
    try:
        split_bills = await controller.get_split_bills(
            order_id=order_id,
            restaurant_id=restaurant_id,
            status=status,
            skip=skip,
            limit=limit
        )
        return {
            "success": True,
            "data": split_bills,
            "count": len(split_bills),
            "filters": {
                "order_id": order_id,
                "restaurant_id": restaurant_id,
                "status": status
            }
        }
    except Exception as e:
        logger.error(f"Failed to get split bills: {e}", "SplitBillsRouter")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve split bills"
        )

@router.post("/")
async def create_split_bill(
    split_bill_data: SplitBillCreate,
    controller: SplitBillController = Depends(get_split_bill_controller)
):
    """
    Create a split bill - Critical EPOS endpoint.
    
    This endpoint enables customers to split their bill:
    - Validates split amounts against order total
    - Creates individual payment portions
    - Supports different split types (equal, custom, by item)
    - Generates payment tracking for each portion
    """
    try:
        result = await controller.create_split_bill(
            order_id=split_bill_data.order_id,
            split_details=[detail.model_dump() for detail in split_bill_data.split_details],
            split_type=split_bill_data.split_type
        )
        return {
            "success": True,
            "message": "Split bill created successfully",
            "data": result
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error creating split bill: {e}", "SplitBillsRouter")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create split bill"
        )

@router.get("/{split_bill_id}")
async def get_split_bill(
    split_bill_id: str,
    controller: SplitBillController = Depends(get_split_bill_controller)
):
    """Get a specific split bill with all portions"""
    split_bill = await controller.get_split_bill_by_id(split_bill_id)
    if not split_bill:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Split bill not found"
        )
    return {
        "success": True,
        "data": split_bill
    }

@router.post("/payment")
async def process_payment(
    payment_request: PaymentRequest,
    controller: SplitBillController = Depends(get_split_bill_controller)
):
    """
    Process payment for split bill portion - Critical EPOS endpoint.
    
    This endpoint handles individual payments within a split bill:
    - Validates payment amount against portion amount
    - Processes payment through specified method
    - Updates portion status to paid
    - Checks if entire split bill is completed
    - Updates order status when all portions are paid
    """
    try:
        result = await controller.process_payment(
            split_bill_id=payment_request.split_bill_id,
            portion_number=payment_request.portion_number,
            payment_details=payment_request.payment_details.model_dump()
        )
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error processing payment: {e}", "SplitBillsRouter")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process payment"
        )

@router.get("/{split_bill_id}/status")
async def get_payment_status(
    split_bill_id: str,
    controller: SplitBillController = Depends(get_split_bill_controller)
):
    """
    Get payment status for split bill - EPOS monitoring endpoint.
    
    This endpoint provides real-time payment tracking:
    - Shows which portions are paid/pending
    - Calculates completion percentage
    - Displays remaining amount
    - Provides customer-specific payment status
    """
    try:
        status_data = await controller.get_payment_status(split_bill_id)
        return {
            "success": True,
            "data": status_data
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get payment status: {e}", "SplitBillsRouter")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve payment status"
        )

@router.delete("/{split_bill_id}")
async def cancel_split_bill(
    split_bill_id: str,
    reason: Optional[str] = Query(None, description="Cancellation reason"),
    controller: SplitBillController = Depends(get_split_bill_controller)
):
    """Cancel a split bill if no payments have been made"""
    try:
        result = await controller.cancel_split_bill(split_bill_id, reason)
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error cancelling split bill: {e}", "SplitBillsRouter")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to cancel split bill"
        )

@router.get("/analytics/{restaurant_id}")
async def get_split_bill_analytics(
    restaurant_id: str,
    controller: SplitBillController = Depends(get_split_bill_controller)
):
    """
    Get split bill analytics for restaurant dashboard.
    
    Provides insights for restaurant management:
    - Split bill usage patterns
    - Completion rates
    - Average portions per bill
    - Payment method preferences
    """
    try:
        analytics_data = await controller.get_split_bill_analytics(restaurant_id)
        return {
            "success": True,
            "data": analytics_data
        }
    except Exception as e:
        logger.error(f"Failed to get split bill analytics: {e}", "SplitBillsRouter")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve split bill analytics"
        )

# Quick status check for EPOS integration
@router.get("/{split_bill_id}/quick-status")
async def get_quick_status(
    split_bill_id: str,
    controller: SplitBillController = Depends(get_split_bill_controller)
):
    """
    Quick status check for EPOS integration.
    
    Simplified endpoint for real-time status updates in EPOS interface.
    Returns essential status information without full details.
    """
    try:
        split_bill = await controller.get_split_bill_by_id(split_bill_id)
        if not split_bill:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Split bill not found"
            )
        
        portions = split_bill.get("portions", [])
        total_portions = len(portions)
        paid_portions = len([p for p in portions if p.get("status") == "paid"])
        
        return {
            "split_bill_id": split_bill_id,
            "status": split_bill.get("status"),
            "total_portions": total_portions,
            "paid_portions": paid_portions,
            "completion_percentage": round((paid_portions / total_portions) * 100, 2) if total_portions > 0 else 0,
            "is_completed": split_bill.get("status") == "completed"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get quick status: {e}", "SplitBillsRouter")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve status"
        )

# Bulk payment processing for efficiency
@router.post("/bulk-payment")
async def process_bulk_payment(
    payments: List[PaymentRequest],
    controller: SplitBillController = Depends(get_split_bill_controller)
):
    """
    Process multiple payments at once.
    
    Useful for scenarios where multiple portions of a split bill
    are paid simultaneously (e.g., group payment processing).
    """
    try:
        results = []
        for payment_request in payments:
            try:
                result = await controller.process_payment(
                    split_bill_id=payment_request.split_bill_id,
                    portion_number=payment_request.portion_number,
                    payment_details=payment_request.payment_details.model_dump()
                )
                results.append({
                    "split_bill_id": payment_request.split_bill_id,
                    "portion_number": payment_request.portion_number,
                    "success": True,
                    "result": result
                })
            except Exception as e:
                results.append({
                    "split_bill_id": payment_request.split_bill_id,
                    "portion_number": payment_request.portion_number,
                    "success": False,
                    "error": str(e)
                })
        
        successful_payments = len([r for r in results if r["success"]])
        
        return {
            "success": True,
            "message": f"Processed {successful_payments}/{len(payments)} payments successfully",
            "results": results,
            "summary": {
                "total_payments": len(payments),
                "successful_payments": successful_payments,
                "failed_payments": len(payments) - successful_payments
            }
        }
    except Exception as e:
        logger.error(f"Failed to process bulk payment: {e}", "SplitBillsRouter")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process bulk payment"
        )
