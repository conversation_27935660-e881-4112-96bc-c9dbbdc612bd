import json
import os
from datetime import datetime
from typing import Dict, Any, List, Union, Callable

# In-memory storage
storage = {
    "users": [],
    "menu_items": [],
    "orders": [],
    "staff": [],
    "tables": [],
    "inventory": [],
    "restaurants": [],
    "restaurant_users": [],
    "promo_codes": [],
    "promo_code_usage": [],
    "campaigns": [],
    "split_bills": [],
    "gift_cards": [],
    "feature_access_logs": []
}

# File path for persistence
DATA_FILE = "data.json"

def initialize_storage():
    """Initialize the storage with data from file if it exists"""
    if os.path.exists(DATA_FILE):
        try:
            with open(DATA_FILE, "r") as f:
                data = json.load(f)
                # Load existing data structure
                if "restaurants" in data:
                    storage["restaurants"] = data["restaurants"]
                if "users" in data:
                    # Map users to restaurant_users for compatibility
                    storage["restaurant_users"] = data["users"]
                    storage["users"] = data["users"]  # Keep both for backward compatibility

                # Load other collections if they exist
                for key in ["menu_items", "orders", "staff", "tables", "inventory", "promo_codes", "promo_code_usage", "campaigns", "split_bills", "gift_cards"]:
                    if key in data:
                        storage[key] = data[key]

            print(f"Loaded data from {DATA_FILE}")
            print(f"Loaded {len(storage['restaurants'])} restaurants and {len(storage['restaurant_users'])} users")
        except Exception as e:
            print(f"Error loading data: {e}")
            # Initialize with sample data if loading fails
            _initialize_sample_data()
            save_storage()
    else:
        # Initialize with some sample data
        _initialize_sample_data()
        save_storage()
        print("Initialized with sample data")

def save_storage():
    """Save the current storage to file"""
    try:
        with open(DATA_FILE, "w") as f:
            # Prepare data in the expected format for data.json
            output_data = {
                "restaurants": storage.get("restaurants", []),
                "users": storage.get("restaurant_users", []),  # Use restaurant_users as the main users collection
                "menu_items": storage.get("menu_items", []),
                "orders": storage.get("orders", []),
                "staff": storage.get("staff", []),
                "tables": storage.get("tables", []),
                "inventory": storage.get("inventory", []),
                "promo_codes": storage.get("promo_codes", []),
                "promo_code_usage": storage.get("promo_code_usage", []),
                "campaigns": storage.get("campaigns", []),
                "split_bills": storage.get("split_bills", []),
                "gift_cards": storage.get("gift_cards", [])
            }

            # Convert datetime objects to strings for JSON serialization
            data = _prepare_data_for_serialization(output_data)
            json.dump(data, f, indent=2)
        print(f"Saved data to {DATA_FILE}")
        print(f"Saved {len(output_data['restaurants'])} restaurants and {len(output_data['users'])} users")
    except Exception as e:
        print(f"Error saving data: {e}")

def _prepare_data_for_serialization(data):
    """Convert datetime objects to strings for JSON serialization"""
    if isinstance(data, dict):
        return {k: _prepare_data_for_serialization(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [_prepare_data_for_serialization(item) for item in data]
    elif isinstance(data, datetime):
        return data.isoformat()
    else:
        return data

def _initialize_sample_data():
    """Initialize the storage with sample data"""
    # Sample users
    storage["users"] = [
        {
            "id": "user1",
            "username": "admin",
            "email": "<EMAIL>",
            "full_name": "Admin User",
            "role": "admin",
            "is_active": True,
            "password_hash": "$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",  # "password"
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        },
        {
            "id": "user2",
            "username": "manager",
            "email": "<EMAIL>",
            "full_name": "Manager User",
            "role": "manager",
            "is_active": True,
            "password_hash": "$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",  # "password"
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
    ]

    # Sample menu items
    storage["menu_items"] = [
        {
            "id": "item1",
            "name": "Classic Burger",
            "price": 9.99,
            "category": "main_course",
            "description": "Juicy beef patty with lettuce, tomato, and special sauce",
            "image_url": "https://example.com/burger.jpg",
            "available": True,
            "ingredients": ["beef", "lettuce", "tomato", "bun", "sauce"],
            "allergens": ["gluten", "dairy"],
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        },
        {
            "id": "item2",
            "name": "Caesar Salad",
            "price": 7.99,
            "category": "appetizer",
            "description": "Fresh romaine lettuce with Caesar dressing and croutons",
            "image_url": "https://example.com/salad.jpg",
            "available": True,
            "ingredients": ["romaine", "croutons", "parmesan", "dressing"],
            "allergens": ["dairy", "gluten"],
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        },
        {
            "id": "item3",
            "name": "Chocolate Cake",
            "price": 5.99,
            "category": "dessert",
            "description": "Rich chocolate cake with ganache frosting",
            "image_url": "https://example.com/cake.jpg",
            "available": True,
            "ingredients": ["flour", "sugar", "chocolate", "butter"],
            "allergens": ["gluten", "dairy", "eggs"],
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        },
        {
            "id": "item4",
            "name": "Grilled Salmon",
            "price": 18.99,
            "category": "main_course",
            "description": "Fresh Atlantic salmon with lemon and herbs",
            "image_url": "https://example.com/salmon.jpg",
            "available": True,
            "ingredients": ["salmon", "lemon", "herbs", "olive oil"],
            "allergens": ["fish"],
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        },
        {
            "id": "item5",
            "name": "Pad Thai",
            "price": 12.99,
            "category": "main_course",
            "description": "Traditional Thai stir-fried noodles with shrimp",
            "image_url": "https://example.com/padthai.jpg",
            "available": True,
            "ingredients": ["rice noodles", "shrimp", "bean sprouts", "peanuts"],
            "allergens": ["shellfish", "nuts", "soy"],
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        },
        {
            "id": "item6",
            "name": "Vegetable Stir Fry",
            "price": 10.99,
            "category": "main_course",
            "description": "Fresh vegetables stir-fried with sesame oil",
            "image_url": "https://example.com/stirfry.jpg",
            "available": True,
            "ingredients": ["mixed vegetables", "sesame oil", "soy sauce"],
            "allergens": ["sesame", "soy"],
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
    ]

    # Sample staff
    storage["staff"] = [
        {
            "id": "staff1",
            "name": "John Smith",
            "role": "Chef",
            "position": "back_of_house",
            "email": "<EMAIL>",
            "phone": "555-1234",
            "hourly_rate": 20.0,
            "status": "active",
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        },
        {
            "id": "staff2",
            "name": "Jane Doe",
            "role": "Waiter",
            "position": "front_of_house",
            "email": "<EMAIL>",
            "phone": "555-5678",
            "hourly_rate": 15.0,
            "status": "active",
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
    ]

    # Sample tables
    storage["tables"] = [
        {
            "id": "table1",
            "number": 1,
            "capacity": 4,
            "location": "Main Floor",
            "status": "available",
            "current_order_id": None,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        },
        {
            "id": "table2",
            "number": 2,
            "capacity": 2,
            "location": "Main Floor",
            "status": "available",
            "current_order_id": None,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        },
        {
            "id": "table3",
            "number": 3,
            "capacity": 6,
            "location": "Patio",
            "status": "available",
            "current_order_id": None,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
    ]

    # Sample inventory
    storage["inventory"] = [
        {
            "id": "inv1",
            "name": "Beef Patty",
            "quantity": 50,
            "unit": "piece",
            "reorder_level": 10,
            "price_per_unit": 2.0,
            "category": "meat",
            "supplier": "Local Butcher",
            "allergens": [],
            "last_restocked": datetime.now().isoformat(),
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        },
        {
            "id": "inv2",
            "name": "Lettuce",
            "quantity": 10,
            "unit": "kg",
            "reorder_level": 2,
            "price_per_unit": 3.0,
            "category": "vegetable",
            "supplier": "Fresh Farms",
            "allergens": [],
            "last_restocked": datetime.now().isoformat(),
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        },
        {
            "id": "inv3",
            "name": "Flour",
            "quantity": 25,
            "unit": "kg",
            "reorder_level": 5,
            "price_per_unit": 2.5,
            "category": "baking",
            "supplier": "Grain Co",
            "allergens": ["gluten"],
            "last_restocked": datetime.now().isoformat(),
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        },
        {
            "id": "inv4",
            "name": "Mozzarella Cheese",
            "quantity": 15,
            "unit": "kg",
            "reorder_level": 3,
            "price_per_unit": 8.0,
            "category": "dairy",
            "supplier": "Dairy Fresh",
            "allergens": ["dairy"],
            "last_restocked": datetime.now().isoformat(),
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        },
        {
            "id": "inv5",
            "name": "Salmon Fillet",
            "quantity": 20,
            "unit": "kg",
            "reorder_level": 5,
            "price_per_unit": 15.0,
            "category": "seafood",
            "supplier": "Ocean Fresh",
            "allergens": ["fish"],
            "last_restocked": datetime.now().isoformat(),
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        },
        {
            "id": "inv6",
            "name": "Eggs",
            "quantity": 100,
            "unit": "piece",
            "reorder_level": 20,
            "price_per_unit": 0.3,
            "category": "dairy",
            "supplier": "Farm Fresh",
            "allergens": ["eggs"],
            "last_restocked": datetime.now().isoformat(),
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        },
        {
            "id": "inv7",
            "name": "Almonds",
            "quantity": 5,
            "unit": "kg",
            "reorder_level": 1,
            "price_per_unit": 12.0,
            "category": "nuts",
            "supplier": "Nut Co",
            "allergens": ["nuts"],
            "last_restocked": datetime.now().isoformat(),
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        },
        {
            "id": "inv8",
            "name": "Soy Sauce",
            "quantity": 8,
            "unit": "liters",
            "reorder_level": 2,
            "price_per_unit": 4.5,
            "category": "condiments",
            "supplier": "Asian Foods",
            "allergens": ["soy", "gluten"],
            "last_restocked": datetime.now().isoformat(),
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
    ]

    # Sample orders
    storage["orders"] = [
        {
            "id": "order1",
            "table_id": "table1",
            "items": [
                {
                    "id": "orderitem1",
                    "menu_item_id": "item1",
                    "name": "Classic Burger",
                    "quantity": 2,
                    "price": 9.99,
                    "special_instructions": "No onions",
                    "subtotal": 19.98
                },
                {
                    "id": "orderitem2",
                    "menu_item_id": "item3",
                    "name": "Chocolate Cake",
                    "quantity": 1,
                    "price": 5.99,
                    "special_instructions": "",
                    "subtotal": 5.99
                }
            ],
            "customer_name": "Customer 1",
            "special_instructions": "",
            "status": "completed",
            "total": 25.97,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "completed_at": datetime.now().isoformat()
        }
    ]

    # Sample restaurants
    storage["restaurants"] = [
        {
            "id": "restaurant1",
            "name": "The Gourmet Kitchen",
            "code": "GK001",
            "logo": "/logos/gourmet-kitchen.png",
            "address": "123 Main Street, Anytown, UK",
            "phone": "(*************",
            "email": "<EMAIL>",
            "vatRate": 20.0,
            "currency": "GBP",
            "isActive": True,
            "ownerName": "John Smith",
            "businessLicenseNumber": "BL-001",
            "restaurantType": "restaurant",
            "password": "gourmet123",
            "createdAt": datetime.now().isoformat(),
            "updatedAt": datetime.now().isoformat()
        },
        {
            "id": "restaurant2",
            "name": "Pasta Paradise",
            "code": "PP002",
            "logo": "/logos/pasta-paradise.png",
            "address": "456 Elm Avenue, Somewhere, UK",
            "phone": "(*************",
            "email": "<EMAIL>",
            "vatRate": 20.0,
            "currency": "GBP",
            "isActive": True,
            "ownerName": "Maria Rossi",
            "businessLicenseNumber": "BL-002",
            "restaurantType": "restaurant",
            "password": "pasta123",
            "createdAt": datetime.now().isoformat(),
            "updatedAt": datetime.now().isoformat()
        }
    ]

    # Sample restaurant users
    storage["restaurant_users"] = [
        {
            "id": "ruser1",
            "name": "Michael Rodriguez",
            "email": "<EMAIL>",
            "phone": "(*************",
            "restaurant_id": "restaurant1",
            "restaurant_name": "The Gourmet Kitchen",
            "role": "waiter",
            "position": "Head Waiter",
            "pin": "1234",
            "status": "active",
            "hireDate": "2022-03-15",
            "performance": 92,
            "accessLevel": "limited"
        },
        {
            "id": "ruser2",
            "name": "Jennifer Smith",
            "email": "<EMAIL>",
            "phone": "(*************",
            "restaurant_id": "restaurant1",
            "restaurant_name": "The Gourmet Kitchen",
            "role": "waiter",
            "position": "Waiter",
            "pin": "2345",
            "status": "active",
            "hireDate": "2022-05-20",
            "performance": 78,
            "accessLevel": "limited"
        },
        {
            "id": "ruser3",
            "name": "Robert Johnson",
            "email": "<EMAIL>",
            "phone": "(*************",
            "restaurant_id": "restaurant1",
            "restaurant_name": "The Gourmet Kitchen",
            "role": "manager",
            "position": "Floor Manager",
            "pin": "5678",
            "status": "active",
            "hireDate": "2021-06-15",
            "performance": 95,
            "accessLevel": "full"
        }
    ]

# Helper functions for CRUD operations
def get_all(collection: str) -> List[Dict[str, Any]]:
    """Get all items from a collection"""
    return storage.get(collection, [])

def get_by_id(collection: str, item_id: str) -> Dict[str, Any]:
    """Get an item by ID from a collection"""
    items = storage.get(collection, [])
    for item in items:
        if item.get("id") == item_id:
            return item
    return None

def create(collection: str, item: Dict[str, Any]) -> Dict[str, Any]:
    """Create a new item in a collection"""
    items = storage.get(collection, [])

    # Generate ID if not provided
    if "id" not in item:
        item["id"] = f"{collection[:-1]}{len(items) + 1}"

    # Add timestamps
    if "created_at" not in item:
        item["created_at"] = datetime.now().isoformat()
    if "updated_at" not in item:
        item["updated_at"] = datetime.now().isoformat()

    items.append(item)
    storage[collection] = items
    save_storage()
    return item

def update(collection: str, item_id: str, updates: Dict[str, Any]) -> Dict[str, Any]:
    """Update an item in a collection"""
    items = storage.get(collection, [])
    for i, item in enumerate(items):
        if item.get("id") == item_id:
            # Update the item
            updates["updated_at"] = datetime.now().isoformat()
            items[i] = {**item, **updates}
            storage[collection] = items
            save_storage()
            return items[i]
    return None

def delete(collection: str, item_id: str) -> bool:
    """Delete an item from a collection"""
    items = storage.get(collection, [])
    for i, item in enumerate(items):
        if item.get("id") == item_id:
            items.pop(i)
            storage[collection] = items
            save_storage()
            return True
    return False

def query(collection: str, filters: Union[Dict[str, Any], Callable]) -> List[Dict[str, Any]]:
    """Query items from a collection with filters"""
    items = storage.get(collection, [])
    results = []

    for item in items:
        if callable(filters):
            # Use callable filter
            if filters(item):
                results.append(item)
        else:
            # Use dictionary filters
            match = True
            for key, value in filters.items():
                if key not in item or item[key] != value:
                    match = False
                    break
            if match:
                results.append(item)

    return results

def load_mock_data(data_type: str) -> Union[List[Dict[str, Any]], Dict[str, Any]]:
    """Load mock data for a specific type (for LLM context)"""
    # Map data types to storage collections
    data_mapping = {
        "restaurants": "restaurants",
        "inventory": "inventory",
        "sales": "orders",  # Use orders as sales data
        "staff": "restaurant_users",  # Use restaurant_users as staff data
        "menu": "menu_items",
        "tables": "tables"
    }

    collection = data_mapping.get(data_type, data_type)
    return storage.get(collection, [])
