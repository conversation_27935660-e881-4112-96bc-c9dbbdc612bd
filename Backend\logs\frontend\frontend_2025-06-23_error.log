{"timestamp": "2025-06-23T14:20:17.838Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750688200489_zrt8kxlow", "url": "http://localhost:5175/admin/epos", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx?t=1750688415569:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-23T14:20:30.874Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750688200489_zrt8kxlow", "url": "http://localhost:5175/admin/epos", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx?t=1750688415569:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-23T14:20:30.910Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750688200489_zrt8kxlow", "url": "http://localhost:5175/admin/epos", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx?t=1750688415569:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-23T14:21:08.245Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750688200489_zrt8kxlow", "url": "http://localhost:5175/admin/epos", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts?t=1750688465354:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx?t=1750688465354:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-23T14:21:16.455Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750688475378_xudj4x2hs", "url": "http://localhost:5175/", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts?t=1750688465354:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx?t=1750688465354:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-23T14:21:16.520Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750688475378_xudj4x2hs", "url": "http://localhost:5175/", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts?t=1750688465354:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx?t=1750688465354:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-23T14:22:46.155Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750688475378_xudj4x2hs", "url": "http://localhost:5175/admin/analytics", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-23T14:22:46.564Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750688200489_zrt8kxlow", "url": "http://localhost:5175/admin/epos", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-23T14:22:46.667Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750688200489_zrt8kxlow", "url": "http://localhost:5175/admin/epos", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-23T14:22:46.690Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750688475378_xudj4x2hs", "url": "http://localhost:5175/admin/analytics", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-23T14:23:43.578Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750688622708_qnj8ej8jd", "url": "http://localhost:5175/", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-23T14:23:43.639Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750688622708_qnj8ej8jd", "url": "http://localhost:5175/", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
