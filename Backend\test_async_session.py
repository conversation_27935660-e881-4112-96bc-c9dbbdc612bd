"""
Test script to debug async session issues
"""

import asyncio
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.database import AsyncSessionLocal, engine
from app.models.database_models import Restaurant
from sqlalchemy import select, text, func

async def test_async_session():
    """Test async session directly"""
    try:
        print("Testing async session...")
        
        async with AsyncSessionLocal() as session:
            print(f"Session created: {session}")
            print(f"Engine URL: {engine.url}")
            
            # Test basic connection
            print("\n1. Testing basic connection...")
            result = await session.execute(text("SELECT 1"))
            print(f"Basic query result: {result.scalar()}")
            
            # Test table existence
            print("\n2. Testing table existence...")
            result = await session.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='restaurants'"))
            table_exists = result.scalar()
            print(f"Restaurants table exists: {table_exists is not None}")
            
            # Test raw count
            print("\n3. Testing raw count...")
            result = await session.execute(text("SELECT COUNT(*) FROM restaurants"))
            raw_count = result.scalar()
            print(f"Raw count from restaurants table: {raw_count}")
            
            # Test SQLAlchemy count
            print("\n4. Testing SQLAlchemy count...")
            result = await session.execute(select(func.count(Restaurant.id)))
            sqlalchemy_count = result.scalar()
            print(f"SQLAlchemy count: {sqlalchemy_count}")
            
            # Test SQLAlchemy select
            print("\n5. Testing SQLAlchemy select...")
            result = await session.execute(select(Restaurant).limit(3))
            restaurants = result.scalars().all()
            print(f"SQLAlchemy select returned {len(restaurants)} restaurants")
            
            for restaurant in restaurants:
                print(f"  - {restaurant.name} (ID: {restaurant.id})")
            
            # Test raw select
            print("\n6. Testing raw select...")
            result = await session.execute(text("SELECT id, name, code FROM restaurants LIMIT 3"))
            raw_restaurants = result.fetchall()
            print(f"Raw select returned {len(raw_restaurants)} restaurants")
            
            for restaurant in raw_restaurants:
                print(f"  - {restaurant[1]} (ID: {restaurant[0]}, Code: {restaurant[2]})")
        
        print("\n✅ Async session test completed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Async session test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_async_session())
    sys.exit(0 if success else 1)
