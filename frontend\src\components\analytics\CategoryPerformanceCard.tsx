import {
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON>er,
  <PERSON>Title
} from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON><PERSON>, <PERSON>, Cell } from 'recharts';
import { TrendingUp, TrendingDown, DollarSign, ShoppingBag } from "lucide-react";

interface CategoryData {
  category: string;
  orderCount: number;
  totalQuantity: number;
  totalRevenue: number;
  avgPrice: number;
  uniqueItems: number;
  avgOrderValue: number;
  estimatedProfit?: number;
  marginPercentage?: number;
}

interface CategoryPerformanceCardProps {
  data: CategoryData[];
  title?: string;
}

const CategoryPerformanceCard = ({ data, title = "Category Performance" }: CategoryPerformanceCardProps) => {
  // Colors for different categories
  const categoryColors = [
    '#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6', '#06b6d4'
  ];

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  // Custom tooltip for charts
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border rounded-md shadow-md p-3">
          <p className="font-medium">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={`item-${index}`} style={{ color: entry.color }}>
              {entry.name}: {
                entry.name.includes('Revenue') || entry.name.includes('Profit') 
                  ? formatCurrency(entry.value)
                  : entry.value.toLocaleString()
              }
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  // Prepare data for pie chart
  const pieData = data.map((item, index) => ({
    name: item.category,
    value: item.totalRevenue,
    fill: categoryColors[index % categoryColors.length]
  }));

  // Calculate totals
  const totalRevenue = data.reduce((sum, item) => sum + item.totalRevenue, 0);
  const totalOrders = data.reduce((sum, item) => sum + item.orderCount, 0);
  const totalProfit = data.reduce((sum, item) => sum + (item.estimatedProfit || 0), 0);

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg font-medium">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        {data.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No category data available
          </div>
        ) : (
          <Tabs defaultValue="overview">
            <TabsList className="mb-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="revenue">Revenue</TabsTrigger>
              <TabsTrigger value="performance">Performance</TabsTrigger>
              <TabsTrigger value="profitability">Profitability</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              {/* Summary Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-3 bg-muted rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{data.length}</div>
                  <div className="text-sm text-muted-foreground">Categories</div>
                </div>
                <div className="text-center p-3 bg-muted rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{formatCurrency(totalRevenue)}</div>
                  <div className="text-sm text-muted-foreground">Total Revenue</div>
                </div>
                <div className="text-center p-3 bg-muted rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">{totalOrders}</div>
                  <div className="text-sm text-muted-foreground">Total Orders</div>
                </div>
                <div className="text-center p-3 bg-muted rounded-lg">
                  <div className="text-2xl font-bold text-orange-600">{formatCurrency(totalRevenue / totalOrders)}</div>
                  <div className="text-sm text-muted-foreground">Avg Order</div>
                </div>
              </div>

              {/* Category List */}
              <div className="space-y-3">
                {data.map((category, index) => (
                  <div key={category.category} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div 
                        className="w-4 h-4 rounded-full"
                        style={{ backgroundColor: categoryColors[index % categoryColors.length] }}
                      />
                      <div>
                        <div className="font-medium">{category.category}</div>
                        <div className="text-sm text-muted-foreground">
                          {category.uniqueItems} items • {category.totalQuantity} sold
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-green-600">{formatCurrency(category.totalRevenue)}</div>
                      <div className="text-sm text-muted-foreground">{category.orderCount} orders</div>
                    </div>
                  </div>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="revenue" className="h-[400px]">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 h-full">
                {/* Bar Chart */}
                <div>
                  <h4 className="text-sm font-medium mb-2">Revenue by Category</h4>
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={data} margin={{ top: 10, right: 10, left: 10, bottom: 20 }}>
                      <CartesianGrid strokeDasharray="3 3" vertical={false} />
                      <XAxis 
                        dataKey="category" 
                        tick={{ fontSize: 10 }}
                        angle={-45}
                        textAnchor="end"
                        height={60}
                      />
                      <YAxis 
                        tickFormatter={(value) => `£${value}`}
                        tick={{ fontSize: 10 }}
                      />
                      <Tooltip content={<CustomTooltip />} />
                      <Bar 
                        dataKey="totalRevenue" 
                        name="Revenue"
                        fill="#3b82f6" 
                        radius={[4, 4, 0, 0]}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </div>

                {/* Pie Chart */}
                <div>
                  <h4 className="text-sm font-medium mb-2">Revenue Distribution</h4>
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={pieData}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        dataKey="value"
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        labelLine={false}
                      >
                        {pieData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.fill} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="performance" className="h-[400px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={data} margin={{ top: 10, right: 10, left: 10, bottom: 60 }}>
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis 
                    dataKey="category" 
                    tick={{ fontSize: 10 }}
                    angle={-45}
                    textAnchor="end"
                    height={80}
                  />
                  <YAxis tick={{ fontSize: 10 }} />
                  <Tooltip content={<CustomTooltip />} />
                  <Bar dataKey="orderCount" name="Orders" fill="#10b981" radius={[2, 2, 0, 0]} />
                  <Bar dataKey="totalQuantity" name="Items Sold" fill="#f59e0b" radius={[2, 2, 0, 0]} />
                </BarChart>
              </ResponsiveContainer>
            </TabsContent>

            <TabsContent value="profitability" className="space-y-4">
              {data.some(item => item.estimatedProfit !== undefined) ? (
                <>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-3 bg-muted rounded-lg">
                      <div className="text-2xl font-bold text-green-600">{formatCurrency(totalProfit)}</div>
                      <div className="text-sm text-muted-foreground">Total Estimated Profit</div>
                    </div>
                    <div className="text-center p-3 bg-muted rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">
                        {((totalProfit / totalRevenue) * 100).toFixed(1)}%
                      </div>
                      <div className="text-sm text-muted-foreground">Overall Margin</div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    {data
                      .filter(item => item.estimatedProfit !== undefined)
                      .sort((a, b) => (b.marginPercentage || 0) - (a.marginPercentage || 0))
                      .map((category, index) => (
                        <div key={category.category} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                          <div className="flex items-center space-x-3">
                            <div 
                              className="w-4 h-4 rounded-full"
                              style={{ backgroundColor: categoryColors[index % categoryColors.length] }}
                            />
                            <div>
                              <div className="font-medium">{category.category}</div>
                              <div className="text-sm text-muted-foreground">
                                Revenue: {formatCurrency(category.totalRevenue)}
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="font-bold text-green-600">
                              {formatCurrency(category.estimatedProfit || 0)}
                            </div>
                            <div className="text-sm flex items-center">
                              {(category.marginPercentage || 0) >= 70 ? (
                                <TrendingUp className="h-3 w-3 text-green-600 mr-1" />
                              ) : (
                                <TrendingDown className="h-3 w-3 text-orange-600 mr-1" />
                              )}
                              {category.marginPercentage?.toFixed(1)}% margin
                            </div>
                          </div>
                        </div>
                      ))}
                  </div>
                </>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  Profitability data not available
                </div>
              )}
            </TabsContent>
          </Tabs>
        )}
      </CardContent>
    </Card>
  );
};

export default CategoryPerformanceCard;
