/**
 * Main Zustand store for RestroManage application
 * Combines all tab-specific state slices with restaurant ID isolation
 */

import { create } from 'zustand';
import { devtools, persist, subscribeWithSelector } from 'zustand/middleware';
import { createDashboardSlice, DashboardSlice } from './slices/dashboardSlice';
import { createAnalyticsSlice, AnalyticsSlice } from './slices/analyticsSlice';
import { createStaffSlice, StaffSlice } from './slices/staffSlice';
import { createInventorySlice, InventorySlice } from './slices/inventorySlice';
import { createOrdersSlice, OrdersSlice } from './slices/ordersSlice';
import { createSettingsSlice, SettingsSlice } from './slices/settingsSlice';
import logger from '@/utils/logger';

// Combined store interface
export interface RestroManageStore extends 
  DashboardSlice,
  AnalyticsSlice,
  StaffSlice,
  InventorySlice,
  OrdersSlice,
  SettingsSlice {
  
  // Global store state
  currentRestaurantId: string | null;
  isInitialized: boolean;
  lastSyncTime: Date | null;
  
  // Global actions
  setCurrentRestaurant: (restaurantId: string) => void;
  initializeStore: (restaurantId: string) => Promise<void>;
  resetStore: () => void;
  syncWithBackend: () => Promise<void>;
  clearRestaurantData: (restaurantId: string) => void;
}

// Create the main store
export const useRestroManageStore = create<RestroManageStore>()(
  devtools(
    persist(
      subscribeWithSelector((set, get, api) => ({
          // Global state
          currentRestaurantId: null,
          isInitialized: false,
          lastSyncTime: null,

          // Global actions
          setCurrentRestaurant: (restaurantId: string) => {
            set((state) => ({
              ...state,
              currentRestaurantId: restaurantId,
            }));
            logger.info('Restaurant context changed', 'Store', { restaurantId });
          },

          initializeStore: async (restaurantId: string) => {
            try {
              set((state) => ({
                ...state,
                currentRestaurantId: restaurantId,
                isInitialized: false,
              }));

              logger.info('Initializing store for restaurant', 'Store', { restaurantId });

              // Initialize all slices
              const state = get();
              await Promise.all([
                state.initializeDashboard(restaurantId),
                state.initializeAnalytics(restaurantId),
                state.initializeStaff(restaurantId),
                state.initializeInventory(restaurantId),
                state.initializeOrders(restaurantId),
                state.initializeSettings(restaurantId),
              ]);

              set((state) => ({
                ...state,
                isInitialized: true,
                lastSyncTime: new Date(),
              }));

              logger.info('Store initialized successfully', 'Store', { restaurantId });
            } catch (error) {
              logger.error('Failed to initialize store', 'Store', { error, restaurantId });
              throw error;
            }
          },

          resetStore: () => {
            const state = get();

            // Reset all slices
            state.resetDashboard();
            state.resetAnalytics();
            state.resetStaff();
            state.resetInventory();
            state.resetOrders();
            state.resetSettings();

            // Reset global state
            set((state) => ({
              ...state,
              currentRestaurantId: null,
              isInitialized: false,
              lastSyncTime: null,
            }));

            logger.info('Store reset completed', 'Store');
          },

          syncWithBackend: async () => {
            const restaurantId = get().currentRestaurantId;
            if (!restaurantId) {
              logger.warn('Cannot sync without restaurant ID', 'Store');
              return;
            }

            try {
              logger.info('Starting backend sync', 'Store', { restaurantId });

              const state = get();
              await Promise.all([
                state.syncDashboard(),
                state.syncAnalytics(),
                state.syncStaff(),
                state.syncInventory(),
                state.syncOrders(),
                state.syncSettings(),
              ]);

              set((state) => ({
                ...state,
                lastSyncTime: new Date(),
              }));

              logger.info('Backend sync completed', 'Store', { restaurantId });
            } catch (error) {
              logger.error('Backend sync failed', 'Store', { error, restaurantId });
              throw error;
            }
          },

          clearRestaurantData: (restaurantId: string) => {
            const state = get();

            // Clear data for specific restaurant
            state.clearDashboardData(restaurantId);
            state.clearAnalyticsData(restaurantId);
            state.clearStaffData(restaurantId);
            state.clearInventoryData(restaurantId);
            state.clearOrdersData(restaurantId);
            state.clearSettingsData(restaurantId);

            logger.info('Restaurant data cleared', 'Store', { restaurantId });
          },

          // Include all slice implementations
          ...createDashboardSlice(set, get, api),
          ...createAnalyticsSlice(set, get, api),
          ...createStaffSlice(set, get, api),
          ...createInventorySlice(set, get, api),
          ...createOrdersSlice(set, get, api),
          ...createSettingsSlice(set, get, api),
        })),
      {
        name: 'restro-manage-store',
        partialize: (state) => ({
          // Only persist essential data, not loading states
          currentRestaurantId: state.currentRestaurantId,
          dashboardMetrics: state.dashboardMetrics,
          analyticsFilters: state.analyticsFilters,
          staffFilters: state.staffFilters,
          inventoryFilters: state.inventoryFilters,
          ordersFilters: state.ordersFilters,
          userPreferences: state.userPreferences,
          restaurantSettings: state.restaurantSettings,
        }),
        version: 1,
        migrate: (persistedState: any, version: number) => {
          // Handle store migrations if needed
          if (version === 0) {
            // Migration logic for version 0 to 1
            logger.info('Migrating store from version 0 to 1', 'Store');
          }
          return persistedState;
        },
      }
    ),
    {
      name: 'RestroManage Store',
      enabled: import.meta.env.DEV, // Only enable devtools in development
    }
  )
);

// Selector hooks for better performance
export const useDashboardState = () => useRestroManageStore((state) => ({
  metrics: state.dashboardMetrics,
  recentOrders: state.recentOrders,
  alerts: state.dashboardAlerts,
  quickStats: state.quickStats,
  isLoading: state.isDashboardLoading,
  error: state.dashboardError,
  isRealTimeEnabled: state.isRealTimeEnabled,
}));

export const useAnalyticsState = () => useRestroManageStore((state) => ({
  data: state.analyticsData,
  filters: state.analyticsFilters,
  isLoading: state.isAnalyticsLoading,
  error: state.analyticsError,
  cachedData: state.analyticsCachedData,
  chartConfigs: state.chartConfigs,
}));

export const useStaffState = () => useRestroManageStore((state) => ({
  members: state.staffMembers,
  schedules: state.staffSchedules,
  activeStaff: state.activeStaff,
  selectedMember: state.selectedStaffMember,
  isLoading: state.isStaffLoading,
  error: state.staffError,
  filters: state.staffFilters,
  scheduleView: state.scheduleView,
}));

export const useInventoryState = () => useRestroManageStore((state) => ({
  items: state.inventoryItems,
  transactions: state.inventoryTransactions,
  suppliers: state.suppliers,
  alerts: state.inventoryAlerts,
  isLoading: state.isInventoryLoading,
  error: state.inventoryError,
  filters: state.inventoryFilters,
  forecasts: state.inventoryForecasts,
}));

export const useOrdersState = () => useRestroManageStore((state) => ({
  activeOrders: state.activeOrders,
  orderHistory: state.orderHistory,
  queue: state.ordersQueue,
  selectedOrder: state.selectedOrder,
  isLoading: state.isOrdersLoading,
  error: state.ordersError,
  filters: state.ordersFilters,
  paymentStates: state.paymentStates,
}));

export const useSettingsState = () => useRestroManageStore((state) => ({
  restaurant: state.restaurantSettings,
  userPreferences: state.userPreferences,
  systemSettings: state.systemSettings,
  isLoading: state.isSettingsLoading,
  error: state.settingsError,
  isDirty: state.isSettingsDirty,
  validationErrors: state.validationErrors,
}));

// Action hooks
export const useDashboardActions = () => useRestroManageStore((state) => ({
  initializeDashboard: state.initializeDashboard,
  refreshMetrics: state.refreshDashboardMetrics,
  toggleRealTime: state.toggleRealTime,
  addAlert: state.addDashboardAlert,
  clearAlerts: state.clearDashboardAlerts,
  syncDashboard: state.syncDashboard,
}));

export const useAnalyticsActions = () => useRestroManageStore((state) => ({
  initializeAnalytics: state.initializeAnalytics,
  updateFilters: state.updateAnalyticsFilters,
  refreshData: state.refreshAnalyticsData,
  exportData: state.exportAnalyticsData,
  syncAnalytics: state.syncAnalytics,
}));

export const useStaffActions = () => useRestroManageStore((state) => ({
  initializeStaff: state.initializeStaff,
  addStaffMember: state.addStaffMember,
  updateStaffMember: state.updateStaffMember,
  removeStaffMember: state.removeStaffMember,
  updateSchedule: state.updateStaffSchedule,
  setSelectedMember: state.setSelectedStaffMember,
  syncStaff: state.syncStaff,
}));

export const useInventoryActions = () => useRestroManageStore((state) => ({
  initializeInventory: state.initializeInventory,
  addInventoryItem: state.addInventoryItem,
  updateInventoryItem: state.updateInventoryItem,
  removeInventoryItem: state.removeInventoryItem,
  addTransaction: state.addInventoryTransaction,
  updateFilters: state.updateInventoryFilters,
  syncInventory: state.syncInventory,
}));

export const useOrdersActions = () => useRestroManageStore((state) => ({
  initializeOrders: state.initializeOrders,
  createOrder: state.createOrder,
  updateOrder: state.updateOrder,
  cancelOrder: state.cancelOrder,
  processPayment: state.processPayment,
  setSelectedOrder: state.setSelectedOrder,
  syncOrders: state.syncOrders,
}));

export const useSettingsActions = () => useRestroManageStore((state) => ({
  initializeSettings: state.initializeSettings,
  updateRestaurantSettings: state.updateRestaurantSettings,
  updateUserPreferences: state.updateUserPreferences,
  updateSystemSettings: state.updateSystemSettings,
  saveSettings: state.saveSettings,
  resetSettings: state.resetSettingsToDefault,
  syncSettings: state.syncSettings,
}));

// Global store actions
export const useStoreActions = () => useRestroManageStore((state) => ({
  setCurrentRestaurant: state.setCurrentRestaurant,
  initializeStore: state.initializeStore,
  resetStore: state.resetStore,
  syncWithBackend: state.syncWithBackend,
  clearRestaurantData: state.clearRestaurantData,
}));

// Store state selectors
export const useStoreState = () => useRestroManageStore((state) => ({
  currentRestaurantId: state.currentRestaurantId,
  isInitialized: state.isInitialized,
  lastSyncTime: state.lastSyncTime,
}));

export default useRestroManageStore;
