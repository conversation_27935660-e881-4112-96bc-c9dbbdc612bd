import logger from "@/utils/logger";

import { useState, useMemo } from "react";
import { Link, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import {
  BarChart3,
  Calendar,
  ClipboardList,
  Home,
  Settings,
  Users,
  ShoppingBasket,
  ChefHat,
  Bell,
  Table,
  CreditCard,
  X,
  CheckSquare,
  MessageSquare,
  Tag
} from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";
import { But<PERSON> } from "@/components/ui/button";
import { useSubscriptionAccess } from "@/hooks/useSubscriptionAccess";
import { useAuth } from "@/contexts/AuthContext";
import { type FeatureKey } from "@/config/subscriptionPlans";
import { isSubscriptionGatingEnabled, isDebugLoggingEnabled } from "@/config/featureFlags";

interface MenuItem {
  icon: React.ReactNode;
  title: string;
  to: string;
  roles: string[];
  hasNotification?: boolean;
  requiredFeature?: FeatureKey; // Feature required to access this menu item
}

interface SidebarItemProps {
  icon: React.ReactNode;
  title: string;
  to: string;
  isActive: boolean;
  hasNotification?: boolean;
}

const SidebarItem = ({ icon, title, to, isActive, hasNotification }: SidebarItemProps) => (
  <Link
    to={to}
    className={cn(
      "flex items-center gap-3 px-3 py-2 rounded-md transition-colors",
      isActive
        ? "bg-sidebar-accent text-sidebar-accent-foreground"
        : "text-sidebar-foreground hover:bg-sidebar-accent/50 hover:text-sidebar-accent-foreground"
    )}
  >
    <div className="relative">
      {icon}
      {hasNotification && (
        <span className="absolute -top-1 -right-1 w-2 h-2 bg-restaurant-secondary rounded-full" />
      )}
    </div>
    <span>{title}</span>
  </Link>
);

interface SidebarProps {
  userRole: string;
  onCloseMobile?: () => void;
}

const Sidebar = ({ userRole, onCloseMobile }: SidebarProps) => {
  // Initialize component logging
  logger.setComponent("Sidebar");
  logger.info("Component initialized", "Sidebar");
  const location = useLocation();
  const isMobile = useIsMobile();
  const { hasFeatureAccess } = useSubscriptionAccess();
  const { currentRestaurant, user } = useAuth();

  logger.debug('Rendering sidebar', 'Sidebar', {
    userRole,
    currentRestaurant: currentRestaurant?.name,
    subscriptionPlan: currentRestaurant?.subscriptionPlan,
    user: user?.name,
    userId: user?.id,
    userPosition: user?.position
  });
  logger.debug('Current path', 'Sidebar', { path: location.pathname });

  // Check if we have a user logged in
  if (!user) {
    logger.warn('No user logged in, defaulting to staff role', 'Sidebar');
  } else {
    logger.debug('User is logged in', 'Sidebar', {
      userName: user.name,
      userRole: user.role,
      passedRole: userRole
    });
  }

  const isActive = (path: string) => location.pathname === path;

  // Admin menu items with feature requirements
  const adminMenuItems: MenuItem[] = [
    { icon: <Home size={20} />, title: "Dashboard", to: "/admin", roles: ["admin", "manager"] },
    { icon: <BarChart3 size={20} />, title: "Analytics", to: "/admin/analytics", roles: ["admin", "manager"], requiredFeature: "analytics" },
    { icon: <ShoppingBasket size={20} />, title: "Inventory", to: "/admin/inventory", hasNotification: true, roles: ["admin", "manager"], requiredFeature: "inventory" },
    { icon: <Table size={20} />, title: "Table Turnover", to: "/admin/table-turnover", roles: ["admin", "manager"], requiredFeature: "table-turnover-analytics" },
    { icon: <CreditCard size={20} />, title: "EPOS", to: "/admin/epos", roles: ["admin", "manager"], requiredFeature: "basic-epos" },
    { icon: <ChefHat size={20} />, title: "Menu", to: "/admin/menu", roles: ["admin", "manager"], requiredFeature: "menu-management" },
    { icon: <Tag size={20} />, title: "Discounts", to: "/admin/discounts", roles: ["admin", "manager"], requiredFeature: "advanced-promotions" },
    { icon: <CheckSquare size={20} />, title: "Tasks", to: "/admin/tasks", roles: ["admin", "manager"], requiredFeature: "task-management" },
    { icon: <Bell size={20} />, title: "Notifications", to: "/admin/notifications", hasNotification: true, roles: ["admin", "manager"] },
    { icon: <Users size={20} />, title: "Staff", to: "/admin/staff", roles: ["admin", "manager"], requiredFeature: "staff-management" },
    { icon: <Calendar size={20} />, title: "Schedule", to: "/admin/schedule", roles: ["admin", "manager"], requiredFeature: "staff-scheduling" },
    { icon: <MessageSquare size={20} />, title: "Assistant", to: "/admin/assistant", roles: ["admin", "manager"], requiredFeature: "llm-ai-integration" },
    { icon: <Settings size={20} />, title: "Settings", to: "/admin/settings", roles: ["admin", "manager"] },
  ];

  // Staff menu items (limited access) with feature requirements
  const staffMenuItems: MenuItem[] = [
    { icon: <Home size={20} />, title: "Dashboard", to: "/staff-dashboard", roles: ["staff", "waiter", "chef", "hostess", "bartender"] },
    { icon: <CreditCard size={20} />, title: "EPOS", to: "/admin/epos", roles: ["staff", "waiter", "chef", "hostess", "bartender"], requiredFeature: "basic-epos" },
    { icon: <ShoppingBasket size={20} />, title: "Inventory", to: "/admin/inventory", hasNotification: true, roles: ["staff", "waiter", "chef", "hostess", "bartender"], requiredFeature: "inventory" },
    { icon: <MessageSquare size={20} />, title: "Assistant", to: "/staff/assistant", roles: ["staff", "waiter", "chef", "hostess", "bartender"], requiredFeature: "llm-ai-integration" },
    { icon: <Settings size={20} />, title: "Settings", to: "/staff/settings", roles: ["staff", "waiter", "chef", "hostess", "bartender"] },
  ];

  // Performance optimization: Memoize menu filtering
  const filterMenuItemsByAccess = useMemo(() => {
    return (items: MenuItem[]) => {
      const DEBUG_MENU = process.env.NODE_ENV === 'development' &&
        (typeof window !== 'undefined' && window.localStorage.getItem('menu_debug') !== 'false');

      // Debug menu filtering disabled for production

      const filteredItems = items.filter((item) => {
        // 🔓 TEMPORARY SUBSCRIPTION DISABLING
        // If subscription gating is disabled, show ALL menu items
        if (!isSubscriptionGatingEnabled()) {
          return true;
        }

        // 🔒 ORIGINAL SUBSCRIPTION LOGIC (Preserved for future re-enablement)
        // If no feature requirement, always show
        if (!item.requiredFeature) {
          logger.debug('Menu item has no feature requirement, showing', 'Sidebar', {
            title: item.title,
            to: item.to
          });
          return true;
        }

        // Check if user has access to the required feature
        const hasAccess = hasFeatureAccess(item.requiredFeature);

        logger.debug('Menu item feature access check', 'Sidebar', {
          title: item.title,
          to: item.to,
          requiredFeature: item.requiredFeature,
          hasAccess,
          currentRestaurant: currentRestaurant?.name,
          subscriptionPlan: currentRestaurant?.subscriptionPlan
        });

        return hasAccess;
      });

      // Menu filtering completed

      return filteredItems;
    };
  }, [hasFeatureAccess, currentRestaurant]);

  // Select menu items based on user role
  const lowerRole = userRole.toLowerCase();
  const isAdminOrManager = lowerRole === "admin" || lowerRole === "manager";
  const baseMenuItems = isAdminOrManager ? adminMenuItems : staffMenuItems;

  // Filter menu items based on subscription access
  const menuItems = filterMenuItemsByAccess(baseMenuItems);

  // Performance optimization: Only log in debug mode
  const DEBUG_MENU_RESULTS = process.env.NODE_ENV === 'development' &&
    (typeof window !== 'undefined' && window.localStorage.getItem('menu_results_debug') !== 'false');

  // Menu filtering results disabled for production

  logger.debug('Menu selection', 'Sidebar', { userRole, menuType: isAdminOrManager ? 'admin' : 'staff' });

  logger.debug('Menu selection logic', 'Sidebar', {
    userRole,
    lowerRole,
    isAdminOrManager,
    menuType: isAdminOrManager ? 'admin' : 'staff',
    baseMenuCount: baseMenuItems.length,
    filteredMenuCount: menuItems.length,
    currentRestaurant: currentRestaurant?.name,
    subscriptionPlan: currentRestaurant?.subscriptionPlan,
    user: user?.name
  });

  return (
    <div className="h-screen bg-sidebar-default flex flex-col bg-sidebar-background border-r border-sidebar-border">
      <div className="flex flex-col h-full">
        <div className="p-4 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <ChefHat size={24} className="text-restaurant-secondary" />
            <h1 className="text-lg font-bold text-sidebar-foreground">
              Promith
            </h1>
          </div>
          {isMobile && onCloseMobile && (
            <Button
              variant="ghost"
              size="sm"
              className="text-sidebar-foreground -mr-1"
              onClick={onCloseMobile}
              aria-label="Close sidebar"
            >
              <X className="h-5 w-5" />
            </Button>
          )}
        </div>

        <div className="flex-1 overflow-auto py-2 px-3 space-y-1">
          {menuItems.map((item) => (
            <SidebarItem
              key={item.to}
              icon={item.icon}
              title={item.title}
              to={item.to}
              isActive={isActive(item.to)}
              hasNotification={item.hasNotification}
            />
          ))}
        </div>

        <div className="p-4 mt-auto border-t border-sidebar-border">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 rounded-full bg-sidebar-accent flex items-center justify-center">
              <Users size={16} className="text-sidebar-accent-foreground" />
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-sidebar-foreground">
                {isAdminOrManager ? "Restaurant Admin" : "Staff Member"}
              </p>
              <p className="text-xs text-sidebar-foreground/70">
                {isAdminOrManager ? "Full Access" : "Limited Access"}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
