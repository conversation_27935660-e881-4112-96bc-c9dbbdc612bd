/**
 * Parses a CSV string into an array of objects
 * @param csv The CSV string to parse
 * @returns An array of objects, where each object represents a row in the CSV
 */
export function parseCSV<T>(csv: string): T[] {
  console.log('[csvParser] Parsing CSV data');

  // Split the CSV into lines
  const lines = csv.trim().split('\n');
  console.log('[csvParser] CSV lines count:', lines.length);

  // Extract headers from the first line
  const headers = lines[0].split(',');
  console.log('[csvParser] CSV headers:', headers);

  // Parse each line into an object
  const result = lines.slice(1).map(line => {
    const values = line.split(',');
    const obj: Record<string, any> = {};

    headers.forEach((header, index) => {
      // Special handling for isActive field
      if (header === 'isActive') {
        // Always set isActive to true
        obj[header] = true;
        console.log(`[csvParser] FORCING isActive field to TRUE (original value: ${values[index]})`);
      }
      // Handle other boolean values
      else if (values[index] === 'true') {
        obj[header] = true;
        console.log(`[csvParser] Parsed boolean field ${header}=true`);
      } else if (values[index] === 'false') {
        obj[header] = false;
        console.log(`[csvParser] Parsed boolean field ${header}=false`);
      }
      // Convert numeric values
      else if (!isNaN(Number(values[index])) && values[index] !== '') {
        obj[header] = Number(values[index]);
      } else {
        obj[header] = values[index];
      }
    });

    console.log(`[csvParser] Parsed row:`, obj);
    return obj as T;
  });

  console.log('[csvParser] Parsed objects:', result);
  return result;
}

/**
 * Fetches and parses a CSV file
 * @param url The URL of the CSV file
 * @returns A promise that resolves to an array of objects
 */
export async function fetchCSV<T>(url: string): Promise<T[]> {
  console.log('[csvParser] Fetching CSV from URL:', url);
  try {
    const response = await fetch(url);
    console.log('[csvParser] Fetch response status:', response.status, response.statusText);

    if (!response.ok) {
      throw new Error(`Failed to fetch CSV: ${response.statusText}`);
    }

    const csvText = await response.text();
    console.log('[csvParser] CSV text length:', csvText.length);
    console.log('[csvParser] CSV text preview:', csvText.substring(0, 100) + '...');

    const result = parseCSV<T>(csvText);
    console.log('[csvParser] Successfully parsed CSV data, items:', result.length);
    return result;
  } catch (error) {
    console.error('[csvParser] Error fetching or parsing CSV:', error);
    throw error;
  }
}
