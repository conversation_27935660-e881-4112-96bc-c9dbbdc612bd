"""
Staff controller implementing business logic for staff operations.
Handles staff CRUD operations, authentication, and performance tracking.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime

from app.controllers.base import BaseController
from app.utils.storage_async import get_all_async, get_by_id_async, create_async, update_async, delete_async, query_async
from app.utils.logging_config import logger

class StaffController(BaseController):
    """Controller for staff business logic and operations"""
    
    def __init__(self):
        super().__init__()
        self.cache_prefix = "staff"
        self.default_cache_ttl = 600  # 10 minutes for staff data
    
    async def get_staff_members(
        self,
        restaurant_id: Optional[str] = None,
        role: Optional[str] = None,
        status: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Get staff members with filtering and caching"""
        # Validate pagination
        skip, limit = self.validate_pagination(skip, limit)
        
        # Create cache key based on filters
        filter_key = f"{restaurant_id}_{role}_{status}_{skip}_{limit}"
        cache_key = f"{self.cache_prefix}_filtered_{hash(filter_key)}"
        
        # Check cache first
        cached_result = self.get_cached_result(cache_key)
        if cached_result:
            return cached_result
        
        # Fetch staff members
        staff_members = await self.handle_async_operation(
            get_all_async,
            "restaurant_users",
            error_message="Failed to fetch staff members"
        )
        
        # Apply filters
        filtered_staff = self._apply_filters(staff_members, restaurant_id, role, status)
        
        # Apply pagination
        paginated_staff = filtered_staff[skip:skip + limit]
        
        # Cache the result
        self.cache_result(cache_key, paginated_staff, self.default_cache_ttl)
        
        logger.info(
            f"Retrieved {len(paginated_staff)} staff members",
            "StaffController",
            {"total_filtered": len(filtered_staff), "restaurant_id": restaurant_id}
        )
        
        return paginated_staff
    
    def _apply_filters(
        self,
        staff_members: List[Dict[str, Any]],
        restaurant_id: Optional[str] = None,
        role: Optional[str] = None,
        status: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Apply filters to staff members"""
        filtered_staff = staff_members.copy()
        
        if restaurant_id:
            filtered_staff = [
                member for member in filtered_staff
                if member.get("restaurant_id") == restaurant_id
            ]
        
        if role:
            filtered_staff = [
                member for member in filtered_staff
                if member.get("role") == role
            ]
        
        if status:
            filtered_staff = [
                member for member in filtered_staff
                if member.get("status") == status
            ]
        
        return filtered_staff
    
    async def get_staff_member_by_id(self, staff_id: str) -> Optional[Dict[str, Any]]:
        """Get staff member by ID with caching"""
        cache_key = f"{self.cache_prefix}_{staff_id}"
        cached_result = self.get_cached_result(cache_key)
        
        if cached_result:
            return cached_result
        
        staff_member = await self.handle_async_operation(
            get_by_id_async,
            "restaurant_users",
            staff_id,
            error_message=f"Failed to fetch staff member {staff_id}"
        )
        
        if staff_member:
            self.cache_result(cache_key, staff_member, self.default_cache_ttl)
            logger.info(f"Retrieved staff member: {staff_member.get('name', 'Unknown')}", "StaffController")
        
        return staff_member
    
    async def verify_staff_pin(self, restaurant_id: str, pin: str) -> Optional[Dict[str, Any]]:
        """Verify staff PIN for authentication"""
        # Check restaurant-specific users first
        users = await self.handle_async_operation(
            query_async,
            "restaurant_users",
            lambda u: (
                u.get("restaurant_id") == restaurant_id and
                u.get("pin") == pin and
                u.get("status") == "active"
            ),
            error_message="Failed to verify staff PIN"
        )
        
        # If no restaurant-specific user found, check system admin users
        if not users:
            admin_users = await self.handle_async_operation(
                query_async,
                "restaurant_users",
                lambda u: (
                    u.get("restaurant_id") == "0" and
                    u.get("pin") == pin and
                    u.get("status") == "active"
                ),
                error_message="Failed to verify admin PIN"
            )
            users = admin_users
        
        if users:
            user = users[0]
            logger.info(
                f"Staff PIN verification successful",
                "StaffController",
                {
                    "restaurant_id": restaurant_id,
                    "user_name": user.get("name"),
                    "user_role": user.get("role")
                }
            )
            return user
        
        logger.warning(
            f"Invalid PIN for restaurant {restaurant_id}",
            "StaffController"
        )
        return None
    
    async def create_staff_member(self, staff_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new staff member"""
        # Check if email already exists
        existing_user = await self.handle_async_operation(
            query_async,
            "restaurant_users",
            lambda u: u.get("email") == staff_data.get("email"),
            error_message="Failed to check existing email"
        )
        
        if existing_user:
            raise self.handle_validation_error(
                "Email already registered",
                {"email": staff_data.get("email")}
            )
        
        # Add timestamps and default values
        staff_data["created_at"] = datetime.now().isoformat()
        staff_data["updated_at"] = datetime.now().isoformat()
        staff_data["status"] = staff_data.get("status", "active")
        
        created_staff = await self.handle_async_operation(
            create_async,
            "restaurant_users",
            staff_data,
            error_message="Failed to create staff member"
        )
        
        # Invalidate relevant caches
        self.invalidate_cache_pattern(self.cache_prefix)
        
        logger.info(
            f"Created staff member: {created_staff.get('name', 'Unknown')}",
            "StaffController",
            {"staff_id": created_staff.get("id"), "restaurant_id": created_staff.get("restaurant_id")}
        )
        
        return created_staff
    
    async def update_staff_member(
        self,
        staff_id: str,
        staff_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Update an existing staff member"""
        existing_staff = await self.get_staff_member_by_id(staff_id)
        if not existing_staff:
            raise self.handle_not_found("Staff member", staff_id)
        
        # Add update timestamp
        staff_data["updated_at"] = datetime.now().isoformat()
        
        updated_staff = await self.handle_async_operation(
            update_async,
            "restaurant_users",
            staff_id,
            staff_data,
            error_message=f"Failed to update staff member {staff_id}"
        )
        
        # Invalidate relevant caches
        self.invalidate_cache(f"{self.cache_prefix}_{staff_id}")
        self.invalidate_cache_pattern(f"{self.cache_prefix}_filtered")
        
        logger.info(
            f"Updated staff member: {updated_staff.get('name', 'Unknown')}",
            "StaffController",
            {"staff_id": staff_id}
        )
        
        return updated_staff
    
    async def deactivate_staff_member(self, staff_id: str) -> Dict[str, Any]:
        """Deactivate a staff member (soft delete)"""
        existing_staff = await self.get_staff_member_by_id(staff_id)
        if not existing_staff:
            raise self.handle_not_found("Staff member", staff_id)
        
        update_data = {
            "status": "inactive",
            "deactivated_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
        
        await self.handle_async_operation(
            update_async,
            "restaurant_users",
            staff_id,
            update_data,
            error_message=f"Failed to deactivate staff member {staff_id}"
        )
        
        # Invalidate relevant caches
        self.invalidate_cache(f"{self.cache_prefix}_{staff_id}")
        self.invalidate_cache_pattern(f"{self.cache_prefix}_filtered")
        
        logger.info(
            f"Deactivated staff member: {existing_staff.get('name', 'Unknown')}",
            "StaffController",
            {"staff_id": staff_id}
        )
        
        return {
            "success": True,
            "message": "Staff member deactivated successfully",
            "staff_id": staff_id,
            "staff_name": existing_staff.get("name")
        }
