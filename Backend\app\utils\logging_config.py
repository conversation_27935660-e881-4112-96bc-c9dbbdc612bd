"""
Logging configuration for RestroManage FastAPI Backend
Provides centralized logging with file output and console output
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional

# Create logs directory if it doesn't exist
LOGS_DIR = Path(__file__).parent.parent.parent / "logs"
LOGS_DIR.mkdir(exist_ok=True)

# Log file path
LOG_FILE = LOGS_DIR / "logs.log"

class ColoredFormatter(logging.Formatter):
    """Custom formatter with colors for console output"""
    
    # ANSI color codes
    COLORS = {
        'DEBUG': '\033[36m',    # Cyan
        'INFO': '\033[32m',     # Green
        'WARNING': '\033[33m',  # Yellow
        'ERROR': '\033[31m',    # Red
        'CRITICAL': '\033[35m', # Magenta
        'RESET': '\033[0m'      # Reset
    }
    
    def format(self, record):
        # Add color to levelname for console output
        if hasattr(record, 'use_color') and record.use_color:
            color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
            record.levelname = f"{color}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)

class RestroManageLogger:
    """Centralized logger for RestroManage backend"""
    
    def __init__(self):
        self.logger = logging.getLogger("RestroManage")
        self.logger.setLevel(logging.DEBUG)
        
        # Prevent duplicate handlers
        if not self.logger.handlers:
            self._setup_handlers()
    
    def _setup_handlers(self):
        """Setup file and console handlers"""
        
        # File handler - detailed logging
        file_handler = logging.handlers.RotatingFileHandler(
            LOG_FILE,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        
        # File formatter - detailed
        file_formatter = logging.Formatter(
            '%(asctime)s | %(levelname)-8s | %(name)s | %(funcName)s:%(lineno)d | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(file_formatter)
        
        # Console handler - less verbose
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        
        # Console formatter - colored and concise
        console_formatter = ColoredFormatter(
            '%(asctime)s | %(levelname)-8s | %(message)s',
            datefmt='%H:%M:%S'
        )
        console_handler.setFormatter(console_formatter)
        
        # Add handlers
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def debug(self, message: str, component: str = "", extra_data: dict = None):
        """Log debug message"""
        self._log(logging.DEBUG, message, component, extra_data)
    
    def info(self, message: str, component: str = "", extra_data: dict = None):
        """Log info message"""
        self._log(logging.INFO, message, component, extra_data)
    
    def warning(self, message: str, component: str = "", extra_data: dict = None):
        """Log warning message"""
        self._log(logging.WARNING, message, component, extra_data)
    
    def error(self, message: str, component: str = "", extra_data: dict = None):
        """Log error message"""
        self._log(logging.ERROR, message, component, extra_data)
    
    def critical(self, message: str, component: str = "", extra_data: dict = None):
        """Log critical message"""
        self._log(logging.CRITICAL, message, component, extra_data)
    
    def _log(self, level: int, message: str, component: str = "", extra_data: dict = None):
        """Internal logging method"""
        # Format message with component
        if component:
            formatted_message = f"[{component}] {message}"
        else:
            formatted_message = message
        
        # Add extra data if provided
        if extra_data:
            formatted_message += f" | Data: {extra_data}"
        
        # Create log record with color flag for console
        record = self.logger.makeRecord(
            self.logger.name, level, "", 0, formatted_message, (), None
        )
        record.use_color = True
        
        # Log to file (without color)
        file_record = self.logger.makeRecord(
            self.logger.name, level, "", 0, formatted_message, (), None
        )
        file_record.use_color = False
        
        # Send to handlers
        for handler in self.logger.handlers:
            if isinstance(handler, logging.handlers.RotatingFileHandler):
                handler.handle(file_record)
            else:
                handler.handle(record)
    
    def log_ai_model_usage(self, model_name: str, operation: str, success: bool = True, extra_info: dict = None):
        """Specific logging for AI model usage"""
        status = "SUCCESS" if success else "FAILED"
        message = f"AI Model Usage - {model_name} | Operation: {operation} | Status: {status}"
        
        if extra_info:
            message += f" | Info: {extra_info}"
        
        if success:
            self.info(message, "GoogleAI")
        else:
            self.error(message, "GoogleAI")
    
    def log_api_request(self, endpoint: str, method: str, status_code: int, response_time: float = None):
        """Log API requests"""
        message = f"API {method} {endpoint} | Status: {status_code}"
        
        if response_time:
            message += f" | Time: {response_time:.3f}s"
        
        if status_code < 400:
            self.info(message, "API")
        elif status_code < 500:
            self.warning(message, "API")
        else:
            self.error(message, "API")
    
    def log_database_operation(self, operation: str, table: str, success: bool = True, extra_info: dict = None):
        """Log database operations"""
        status = "SUCCESS" if success else "FAILED"
        message = f"DB {operation.upper()} on {table} | Status: {status}"
        
        if extra_info:
            message += f" | Info: {extra_info}"
        
        if success:
            self.debug(message, "Database")
        else:
            self.error(message, "Database")
    
    def log_authentication(self, action: str, user_id: str = None, success: bool = True):
        """Log authentication events"""
        status = "SUCCESS" if success else "FAILED"
        message = f"Auth {action.upper()} | Status: {status}"
        
        if user_id:
            message += f" | User: {user_id}"
        
        if success:
            self.info(message, "Auth")
        else:
            self.warning(message, "Auth")
    
    def log_startup(self, component: str, version: str = None):
        """Log application startup"""
        message = f"{component} starting up"
        if version:
            message += f" | Version: {version}"
        
        self.info(message, "Startup")
    
    def log_shutdown(self, component: str):
        """Log application shutdown"""
        self.info(f"{component} shutting down", "Shutdown")

# Create global logger instance
logger = RestroManageLogger()

# Convenience functions for direct import
def log_ai_model_usage(model_name: str, operation: str, success: bool = True, extra_info: dict = None):
    """Log AI model usage"""
    logger.log_ai_model_usage(model_name, operation, success, extra_info)

def log_api_request(endpoint: str, method: str, status_code: int, response_time: float = None):
    """Log API request"""
    logger.log_api_request(endpoint, method, status_code, response_time)

def log_database_operation(operation: str, table: str, success: bool = True, extra_info: dict = None):
    """Log database operation"""
    logger.log_database_operation(operation, table, success, extra_info)

def log_authentication(action: str, user_id: str = None, success: bool = True):
    """Log authentication event"""
    logger.log_authentication(action, user_id, success)

def log_startup(component: str, version: str = None):
    """Log startup event"""
    logger.log_startup(component, version)

def log_shutdown(component: str):
    """Log shutdown event"""
    logger.log_shutdown(component)

# Export logger instance
__all__ = [
    'logger', 
    'log_ai_model_usage', 
    'log_api_request', 
    'log_database_operation',
    'log_authentication',
    'log_startup',
    'log_shutdown'
]
