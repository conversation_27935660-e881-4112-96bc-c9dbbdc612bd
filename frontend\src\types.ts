// Staff member interface
export interface StaffMember {
  id: string;
  name: string;
  role: string;
  position: string;
  email: string;
  phone: string;
  status: "active" | "inactive" | "on-leave";
  hireDate: string;
  pin?: string; // Made optional to match usage in components
  assignedHours?: number;
  availableDays?: string[];
  workStatus?: "clocked-in" | "clocked-out" | "on-break";
  clockInHistory?: {
    date: string;
    clockIn: string;
    clockOut?: string;
    breakStart?: string;
    breakEnd?: string;
  }[];
  // Additional properties for compatibility
  performance?: number;
  metrics?: {
    sales: number;
    tablesTurned: number;
    customerRating: number;
  };
  avatar?: string;
  totalHours?: number;
  percentOfIdeal?: number;
}

// Time entry interface
export interface TimeEntry {
  id: string;
  staffId: string;
  date: string;
  clockIn: string;
  clockOut: string | null;
  breakStart: string | null;
  breakEnd: string | null;
  totalHours: number | null;
  status: "active" | "completed";
}

// Table status interface
export interface TableStatus {
  id: string;
  number: string;
  capacity: number;
  status: "available" | "occupied" | "reserved" | "cleaning";
  occupiedSince?: string;
  waitTime?: string;
  reservation?: {
    time: string;
    name: string;
    guests: number;
  };
}

// Date range interface
export interface DateRange {
  from: Date;
  to: Date;
}

// Forecast shift interface
export interface ForecastShift {
  id: string;
  staffId: string;
  staffName: string;
  role: string;
  day: string;
  date: string;
  startTime: string;
  endTime: string;
  forecastBased: boolean;
  assignedStaff?: StaffMember[]; // Added missing property
}

// Restaurant context type
export interface RestaurantContextType {
  currentRestaurant: Restaurant | null;
  restaurant?: Restaurant | null; // Added for compatibility
  isRestaurantAuthenticated: boolean;
  isAuthenticated?: boolean; // Added for compatibility
  isLoading?: boolean; // Added for compatibility
  authenticateRestaurant: (id: string, rememberRestaurant: boolean) => Promise<boolean>;
  logoutRestaurant: () => void;
  logout?: () => void; // Added for compatibility
  availableRestaurants: Restaurant[];
}

// Restaurant interface
export interface Restaurant {
  id: string;
  code: string;
  name: string;
  logo: string;
  address: string;
  phone: string;
  email: string;
  vatRate: number;
  currency: string;
  isActive: boolean;
  hasData?: boolean;
  subscriptionPlan: "basic" | "pro" | "customized";
  subscriptionStatus: "active" | "trial" | "expired" | "cancelled";
  subscriptionExpiresAt?: string;
  customizedFeatures?: string[];
  billingInfo?: {
    monthlyPrice: number;
    currency: string;
    nextBillingDate: string;
  };
}
