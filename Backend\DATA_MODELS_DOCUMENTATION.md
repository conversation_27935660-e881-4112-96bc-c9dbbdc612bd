# RestroManage Data Models Documentation

## Overview

This document provides comprehensive documentation for all data models used in the RestroManage application. The models are organized by domain and include proper validation, type definitions, and relationships.

## Model Architecture

### Base Models (`app/models/base.py`)

#### Core Base Classes
- **BaseModel**: Foundation class with common configuration
- **IDMixin**: Provides unique identifier functionality
- **TimestampMixin**: Adds created_at and updated_at fields
- **SoftDeleteMixin**: Enables soft deletion capability
- **StatusMixin**: Adds is_active status field
- **MetadataMixin**: Provides metadata and tagging support
- **AuditMixin**: Adds audit trail functionality
- **LocationMixin**: Geographic location support
- **ContactMixin**: Contact information fields
- **PricingMixin**: Currency and tax rate support

#### Common Enums
- **Priority**: LOW, MEDIUM, HIGH, URGENT
- **Status**: ACTIVE, INACTIVE, PENDING, COMPLETED, CANCELLED, ARCHIVED
- **OperatingStatus**: OPEN, CLOSED, BUSY, MAINTENANCE

### Authentication Models (`app/models/auth.py`)

#### User Management
- **User**: Core user model with authentication
- **UserCreate**: User creation request
- **UserUpdate**: User update request
- **UserRole**: User role enumeration
- **Token**: JWT token model
- **TokenData**: Token payload data

### Restaurant Models (`app/models/restaurants.py`)

#### Restaurant Configuration
- **Restaurant**: Main restaurant entity
- **RestaurantCreate**: Restaurant creation request
- **RestaurantUpdate**: Restaurant update request
- **RestaurantSettings**: Configuration settings
- **OperatingHours**: Business hours configuration

### Menu Models (`app/models/menu.py`)

#### Menu Management
- **MenuItem**: Individual menu item
- **MenuItemCreate**: Menu item creation
- **MenuItemUpdate**: Menu item updates
- **MenuCategory**: Menu categorization
- **AllergenInfo**: Allergen information
- **NutritionalInfo**: Nutritional data
- **MenuSection**: Menu organization

#### Allergen Support
```python
ALLERGEN_TYPES = [
    "nuts", "dairy", "gluten", "shellfish", 
    "eggs", "soy", "fish", "sesame"
]
```

### Order Models (`app/models/orders.py`)

#### Order Processing
- **Order**: Main order entity
- **OrderCreate**: Order creation request
- **OrderUpdate**: Order modifications
- **OrderItem**: Individual order items
- **OrderStatus**: Order state enumeration
- **PaymentStatus**: Payment state tracking
- **DiscountApplication**: Applied discounts

#### Order Lifecycle
```
PENDING → PREPARING → READY → DELIVERED → COMPLETED
                  ↓
               CANCELLED
```

### Table Models (`app/models/tables.py`)

#### Table Management
- **Table**: Table entity with enhanced features
- **TableCreate**: Table creation
- **TableUpdate**: Table modifications
- **TableStatus**: Table state enumeration
- **TableLocation**: Location categorization
- **TableReservation**: Reservation management

#### Table Statuses
- **AVAILABLE**: Ready for seating
- **OCCUPIED**: Currently in use
- **RESERVED**: Reserved for future use
- **CLEANING**: Being cleaned
- **OUT_OF_SERVICE**: Temporarily unavailable

### Inventory Models (`app/models/inventory.py`)

#### Stock Management
- **InventoryItem**: Stock item tracking
- **InventoryCreate**: Item creation
- **InventoryUpdate**: Stock updates
- **StockLevel**: Quantity tracking
- **SupplierInfo**: Supplier management
- **StockAlert**: Low stock notifications

### Staff Models (`app/models/staff.py`)

#### Staff Management
- **StaffMember**: Employee information
- **StaffCreate**: Staff creation
- **StaffUpdate**: Staff updates
- **StaffRole**: Role definitions
- **Schedule**: Work scheduling
- **TimeEntry**: Time tracking
- **Performance**: Performance metrics

#### Staff Roles
- **ADMIN**: Full system access
- **MANAGER**: Management functions
- **WAITER**: Service staff
- **CHEF**: Kitchen staff
- **HOSTESS**: Front of house
- **BARTENDER**: Bar operations

### Discount Models (`app/models/discounts.py`)

#### Promotion Management
- **PromoCode**: Discount codes
- **PromoCodeCreate**: Code creation
- **PromoCodeUpdate**: Code modifications
- **Campaign**: Marketing campaigns
- **DiscountApplication**: Applied discounts
- **PromoCodeUsage**: Usage tracking

#### Discount Types
- **PERCENTAGE**: Percentage-based discounts
- **FIXED_AMOUNT**: Fixed amount discounts
- **BUY_X_GET_Y**: Quantity-based offers
- **FREE_ITEM**: Complimentary items

### Split Bill Models (`app/models/split_bills.py`)

#### Bill Splitting
- **SplitBill**: Main split bill entity
- **SplitBillCreate**: Split creation
- **SplitPortion**: Individual portions
- **PaymentRequest**: Payment processing
- **PaymentResponse**: Payment results
- **ReceiptData**: Receipt generation

#### Split Types
- **EQUAL_SPLIT**: Equal division
- **BY_ITEMS**: Item-based splitting
- **CUSTOM_AMOUNTS**: Custom amounts
- **PERCENTAGE_SPLIT**: Percentage-based

### AI Models (`app/models/ai_models.py`)

#### AI Integration
- **AIRequest**: AI service requests
- **AIResponse**: AI service responses
- **PromoSuggestion**: AI-generated promo codes
- **CustomerInsight**: Customer analysis
- **SalesPrediction**: Sales forecasting
- **MenuOptimization**: Menu recommendations

#### AI Providers
- **GOOGLE**: Google Generative AI
- **OPENAI**: OpenAI GPT models
- **ANTHROPIC**: Claude models
- **LOCAL**: Local AI models
- **MOCK**: Testing/development

### Analytics Models (`app/models/analytics.py`)

#### Reporting & Analytics
- **SalesReport**: Sales analytics
- **CustomerAnalytics**: Customer insights
- **PerformanceMetrics**: KPI tracking
- **RevenueAnalytics**: Revenue analysis
- **InventoryAnalytics**: Stock analytics

## Model Relationships

### Core Relationships
```
Restaurant
├── Menu Items
├── Tables
├── Staff Members
├── Orders
│   ├── Order Items
│   ├── Applied Discounts
│   └── Split Bills
├── Promo Codes
│   ├── Campaigns
│   └── Usage Records
└── Inventory Items
```

### Data Flow
```
Order Creation:
Customer → Table → Menu Items → Order → Payment → Completion

Discount Application:
Promo Code → Validation → Application → Usage Tracking

Split Bill Process:
Order → Split Creation → Portion Payments → Completion
```

## Validation Rules

### Common Validations
- **Email**: RFC 5322 compliant format
- **Phone**: Minimum 10 digits
- **Currency**: ISO 4217 codes (GBP, USD, EUR, etc.)
- **Percentages**: 0-100 range
- **Prices**: Positive values only
- **Dates**: ISO 8601 format

### Business Rules
- **Orders**: Must have at least one item
- **Discounts**: Cannot exceed order total
- **Split Bills**: Portions must sum to order total
- **Tables**: Capacity must be positive
- **Staff**: Unique email addresses
- **Inventory**: Stock levels cannot be negative

## Database Schema

### Primary Keys
All models use UUID v4 for primary keys to ensure uniqueness across distributed systems.

### Indexes
- **Orders**: table_id, created_at, status
- **Menu Items**: category, is_active, allergens
- **Promo Codes**: code, is_active, end_date
- **Staff**: email, role, is_active
- **Tables**: status, location, capacity

### Foreign Keys
- **Orders** → **Tables** (table_id)
- **Order Items** → **Menu Items** (menu_item_id)
- **Promo Usage** → **Promo Codes** (promo_code_id)
- **Split Bills** → **Orders** (original_order_id)

## API Response Formats

### Success Response
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": { ... }
}
```

### Error Response
```json
{
  "success": false,
  "error": "Error description",
  "details": { ... },
  "error_code": "ERROR_CODE"
}
```

### Paginated Response
```json
{
  "items": [...],
  "total": 100,
  "page": 1,
  "per_page": 10,
  "pages": 10,
  "has_next": true,
  "has_prev": false
}
```

## Model Extensions

### Custom Fields
Models support custom metadata fields for extensibility:
```python
metadata: Optional[Dict[str, Any]] = None
tags: Optional[List[str]] = None
```

### Audit Trail
All models include audit information:
```python
created_by: Optional[str] = None
updated_by: Optional[str] = None
version: int = 1
```

### Soft Deletion
Models support soft deletion:
```python
is_deleted: bool = False
deleted_at: Optional[datetime] = None
```

## Performance Considerations

### Indexing Strategy
- Index frequently queried fields
- Composite indexes for complex queries
- Partial indexes for filtered queries

### Data Archival
- Archive old orders after 2 years
- Compress historical analytics data
- Maintain audit logs for compliance

### Caching
- Cache menu items (1 hour TTL)
- Cache table status (5 minutes TTL)
- Cache promo codes (30 minutes TTL)

## Security Considerations

### Data Protection
- PII fields are marked for encryption
- Sensitive data uses field-level encryption
- Audit logs track all data access

### Access Control
- Role-based field access
- Row-level security for multi-tenant data
- API rate limiting per user role

## Migration Strategy

### Schema Changes
- Use Alembic for database migrations
- Backward-compatible changes preferred
- Data migration scripts for breaking changes

### Version Management
- Semantic versioning for model changes
- API versioning for breaking changes
- Deprecation notices for removed fields

---

This documentation is maintained alongside the codebase and should be updated with any model changes.
