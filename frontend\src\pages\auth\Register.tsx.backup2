import React, { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  User, 
  Mail, 
  Lock, 
  Building, 
  Phone, 
  MapPin, 
  FileText, 
  AlertCircle,
  ArrowRight,
  ArrowLeft,
  Check
} from "lucide-react";
import LogoUpload from "@/components/auth/LogoUpload";
import { RestaurantRegistrationData, RESTAURANT_TYPES } from "@/types/restaurantSetup";

const Register: React.FC = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const [formData, setFormData] = useState<RestaurantRegistrationData>({
    restaurantName: "",
    ownerName: "",
    email: "",
    password: "",
    confirmPassword: "",
    restaurantType: "restaurant",
    businessLicenseNumber: "",
    phone: "",
    address: {
      street: "",
      city: "",
      state: "",
      zipCode: "",
      country: "United Kingdom",
    },
    acceptTerms: false,
    acceptPrivacy: false,
  });

  const updateFormData = (field: string, value: any) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent as keyof RestaurantRegistrationData],
          [child]: value,
        },
      }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {};

    if (step === 1) {
      if (!formData.restaurantName.trim()) newErrors.restaurantName = "Restaurant name is required";
      if (!formData.ownerName.trim()) newErrors.ownerName = "Owner name is required";
      if (!formData.email.trim()) newErrors.email = "Email is required";
      else if (!/\S+@\S+\.\S+/.test(formData.email)) newErrors.email = "Invalid email format";
      if (!formData.password) newErrors.password = "Password is required";
      else if (formData.password.length < 8) newErrors.password = "Password must be at least 8 characters";
      if (formData.password !== formData.confirmPassword) newErrors.confirmPassword = "Passwords do not match";
    }

    if (step === 2) {
      if (!formData.restaurantType) newErrors.restaurantType = "Restaurant type is required";
      if (!formData.businessLicenseNumber.trim()) newErrors.businessLicenseNumber = "Business license number is required";
      if (!formData.phone.trim()) newErrors.phone = "Phone number is required";
      if (!formData.address.street.trim()) newErrors["address.street"] = "Street address is required";
      if (!formData.address.city.trim()) newErrors["address.city"] = "City is required";
      if (!formData.address.zipCode.trim()) newErrors["address.zipCode"] = "Postal code is required";
    }

    if (step === 3) {
      if (!formData.acceptTerms) newErrors.acceptTerms = "You must accept the terms of service";
      if (!formData.acceptPrivacy) newErrors.acceptPrivacy = "You must accept the privacy policy";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(3, prev + 1));
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(1, prev - 1));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateStep(3)) return;

    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Store registration data in localStorage for now
      localStorage.setItem('registrationData', JSON.stringify(formData));
      
      // Navigate to restaurant setup
      navigate('/auth/restaurant-setup');
    } catch (error) {
      setErrors({ submit: "Registration failed. Please try again." });
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogoChange = (file: File | null, url: string | null) => {
    setFormData(prev => ({
      ...prev,
      logo: file || undefined,
      logoUrl: url || undefined,
    }));
  };

  const stepTitles = [
    "Account Information",
    "Business Details", 
    "Logo & Legal"
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-2xl">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Join Promith</h1>
          <p className="text-gray-600">Create your restaurant management account</p>
        </div>

        {/* Progress Indicator */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            {[1, 2, 3].map((step) => (
              <div key={step} className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  step <= currentStep 
                    ? "bg-blue-600 text-white" 
                    : "bg-gray-200 text-gray-500"
                }`}>
                  {step < currentStep ? <Check className="w-4 h-4" /> : step}
                </div>
                {step < 3 && (
                  <div className={`flex-1 h-1 mx-4 ${
                    step < currentStep ? "bg-blue-600" : "bg-gray-200"
                  }`} />
                )}
              </div>
            ))}
          </div>
          <div className="text-center">
            <span className="text-sm text-gray-600">
              Step {currentStep} of 3: {stepTitles[currentStep - 1]}
            </span>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>{stepTitles[currentStep - 1]}</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit}>
              {/* Step 1: Account Information */}
              {currentStep === 1 && (
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="restaurantName">Restaurant Name *</Label>
                      <div className="relative">
                        <Building className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="restaurantName"
                          type="text"
                          placeholder="Your Restaurant Name"
                          value={formData.restaurantName}
                          onChange={(e) => updateFormData("restaurantName", e.target.value)}
                          className="pl-10"
                        />
                      </div>
                      {errors.restaurantName && (
                        <p className="text-sm text-red-600 mt-1">{errors.restaurantName}</p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="ownerName">Owner Name *</Label>
                      <div className="relative">
                        <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="ownerName"
                          type="text"
                          placeholder="Your Full Name"
                          value={formData.ownerName}
                          onChange={(e) => updateFormData("ownerName", e.target.value)}
                          className="pl-10"
                        />
                      </div>
                      {errors.ownerName && (
                        <p className="text-sm text-red-600 mt-1">{errors.ownerName}</p>
                      )}
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="email">Email Address *</Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="email"
                        type="email"
                        placeholder="<EMAIL>"
                        value={formData.email}
                        onChange={(e) => updateFormData("email", e.target.value)}
                        className="pl-10"
                      />
                    </div>
                    {errors.email && (
                      <p className="text-sm text-red-600 mt-1">{errors.email}</p>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="password">Password *</Label>
                      <div className="relative">
                        <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="password"
                          type="password"
                          placeholder="Create a strong password"
                          value={formData.password}
                          onChange={(e) => updateFormData("password", e.target.value)}
                          className="pl-10"
                        />
                      </div>
                      {errors.password && (
                        <p className="text-sm text-red-600 mt-1">{errors.password}</p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="confirmPassword">Confirm Password *</Label>
                      <div className="relative">
                        <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="confirmPassword"
                          type="password"
                          placeholder="Confirm your password"
                          value={formData.confirmPassword}
                          onChange={(e) => updateFormData("confirmPassword", e.target.value)}
                          className="pl-10"
                        />
                      </div>
                      {errors.confirmPassword && (
                        <p className="text-sm text-red-600 mt-1">{errors.confirmPassword}</p>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {/* Step 2: Business Details */}
              {currentStep === 2 && (
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="restaurantType">Restaurant Type *</Label>
                      <Select value={formData.restaurantType} onValueChange={(value: any) => updateFormData("restaurantType", value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select restaurant type" />
                        </SelectTrigger>
                        <SelectContent>
                          {RESTAURANT_TYPES.map((type) => (
                            <SelectItem key={type.value} value={type.value}>
                              <div>
                                <div className="font-medium">{type.label}</div>
                                <div className="text-sm text-gray-500">{type.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {errors.restaurantType && (
                        <p className="text-sm text-red-600 mt-1">{errors.restaurantType}</p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="businessLicense">Business License Number *</Label>
                      <div className="relative">
                        <FileText className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="businessLicense"
                          type="text"
                          placeholder="License/Registration Number"
                          value={formData.businessLicenseNumber}
                          onChange={(e) => updateFormData("businessLicenseNumber", e.target.value)}
                          className="pl-10"
                        />
                      </div>
                      {errors.businessLicenseNumber && (
                        <p className="text-sm text-red-600 mt-1">{errors.businessLicenseNumber}</p>
                      )}
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="phone">Phone Number *</Label>
                    <div className="relative">
                      <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="phone"
                        type="tel"
                        placeholder="+44 20 1234 5678"
                        value={formData.phone}
                        onChange={(e) => updateFormData("phone", e.target.value)}
                        className="pl-10"
                      />
                    </div>
                    {errors.phone && (
                      <p className="text-sm text-red-600 mt-1">{errors.phone}</p>
                    )}
                  </div>

                  <div className="space-y-4">
                    <Label className="text-base font-medium">Business Address *</Label>
                    
                    <div>
                      <Label htmlFor="street">Street Address</Label>
                      <div className="relative">
                        <MapPin className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="street"
                          type="text"
                          placeholder="123 High Street"
                          value={formData.address.street}
                          onChange={(e) => updateFormData("address.street", e.target.value)}
                          className="pl-10"
                        />
                      </div>
                      {errors["address.street"] && (
                        <p className="text-sm text-red-600 mt-1">{errors["address.street"]}</p>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <Label htmlFor="city">City</Label>
                        <Input
                          id="city"
                          type="text"
                          placeholder="London"
                          value={formData.address.city}
                          onChange={(e) => updateFormData("address.city", e.target.value)}
                        />
                        {errors["address.city"] && (
                          <p className="text-sm text-red-600 mt-1">{errors["address.city"]}</p>
                        )}
                      </div>

                      <div>
                        <Label htmlFor="state">County/State</Label>
                        <Input
                          id="state"
                          type="text"
                          placeholder="Greater London"
                          value={formData.address.state}
                          onChange={(e) => updateFormData("address.state", e.target.value)}
                        />
                      </div>

                      <div>
                        <Label htmlFor="zipCode">Postal Code</Label>
                        <Input
                          id="zipCode"
                          type="text"
                          placeholder="SW1A 1AA"
                          value={formData.address.zipCode}
                          onChange={(e) => updateFormData("address.zipCode", e.target.value)}
                        />
                        {errors["address.zipCode"] && (
                          <p className="text-sm text-red-600 mt-1">{errors["address.zipCode"]}</p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Step 3: Logo & Legal */}
              {currentStep === 3 && (
                <div className="space-y-6">
                  <LogoUpload
                    onLogoChange={handleLogoChange}
                    currentLogo={formData.logoUrl}
                  />

                  <div className="space-y-4">
                    <div className="flex items-start space-x-3">
                      <Checkbox
                        id="acceptTerms"
                        checked={formData.acceptTerms}
                        onCheckedChange={(checked) => updateFormData("acceptTerms", checked)}
                      />
                      <div className="text-sm">
                        <Label htmlFor="acceptTerms" className="cursor-pointer">
                          I agree to the{" "}
                          <Link to="/terms" className="text-blue-600 hover:underline">
                            Terms of Service
                          </Link>
                        </Label>
                        {errors.acceptTerms && (
                          <p className="text-red-600 mt-1">{errors.acceptTerms}</p>
                        )}
                      </div>
                    </div>

                    <div className="flex items-start space-x-3">
                      <Checkbox
                        id="acceptPrivacy"
                        checked={formData.acceptPrivacy}
                        onCheckedChange={(checked) => updateFormData("acceptPrivacy", checked)}
                      />
                      <div className="text-sm">
                        <Label htmlFor="acceptPrivacy" className="cursor-pointer">
                          I agree to the{" "}
                          <Link to="/privacy" className="text-blue-600 hover:underline">
                            Privacy Policy
                          </Link>
                        </Label>
                        {errors.acceptPrivacy && (
                          <p className="text-red-600 mt-1">{errors.acceptPrivacy}</p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Error Display */}
              {errors.submit && (
                <Alert variant="destructive" className="mt-6">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{errors.submit}</AlertDescription>
                </Alert>
              )}

              {/* Navigation Buttons */}
              <div className="flex justify-between mt-8">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handlePrevious}
                  disabled={currentStep === 1}
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Previous
                </Button>

                {currentStep < 3 ? (
                  <Button type="button" onClick={handleNext}>
                    Next
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                ) : (
                  <Button type="submit" disabled={isLoading}>
                    {isLoading ? "Creating Account..." : "Create Account"}
                  </Button>
                )}
              </div>
            </form>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center mt-6">
          <p className="text-sm text-gray-600">
            Already have an account?{" "}
            <Link to="/login" className="text-blue-600 hover:underline">
              Sign in here
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default Register;
