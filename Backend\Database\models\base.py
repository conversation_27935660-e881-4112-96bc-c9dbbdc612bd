"""
Base SQLAlchemy models and mixins for RestroManage application.
Provides common functionality and patterns for all database models.
"""

import uuid
from datetime import datetime, timezone
from sqlalchemy import Column, String, DateTime, Boolean, Text, JSON
from sqlalchemy.sql import func

# Import Base from database module to avoid circular imports
from ..database import Base

class BaseModel(Base):
    """
    Abstract base model with common fields and functionality.
    All models should inherit from this class.
    """
    __abstract__ = True
    
    # Primary key with UUID
    id = Column(
        String(36), 
        primary_key=True, 
        default=lambda: str(uuid.uuid4()),
        index=True
    )

class TimestampMixin:
    """
    Mixin for models that need timestamp tracking.
    Provides created_at and updated_at fields.
    """
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        index=True
    )
    
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        index=True
    )

class SoftDeleteMixin:
    """
    Mixin for models that support soft deletion.
    Provides is_deleted and deleted_at fields.
    """
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)
    deleted_at = Column(DateTime(timezone=True), nullable=True)
    
    def soft_delete(self):
        """Mark record as deleted without removing from database"""
        self.is_deleted = True
        self.deleted_at = datetime.now(timezone.utc)

    def restore(self):
        """Restore a soft-deleted record"""
        self.is_deleted = False
        self.deleted_at = None

class StatusMixin:
    """
    Mixin for models that have an active/inactive status.
    """
    is_active = Column(Boolean, default=True, nullable=False, index=True)

class MetadataMixin:
    """
    Mixin for models that need to store additional metadata.
    """
    metadata = Column(JSON, nullable=True)
    notes = Column(Text, nullable=True)

class AuditMixin:
    """
    Mixin for models that need audit trail information.
    """
    created_by = Column(String(36), nullable=True, index=True)
    updated_by = Column(String(36), nullable=True, index=True)
    version = Column(String(10), default="1.0", nullable=False)

class LocationMixin:
    """
    Mixin for models that have location information.
    """
    latitude = Column(String(20), nullable=True)
    longitude = Column(String(20), nullable=True)
    address = Column(Text, nullable=True)
    city = Column(String(100), nullable=True)
    country = Column(String(100), nullable=True)
    postal_code = Column(String(20), nullable=True)

class ContactMixin:
    """
    Mixin for models that have contact information.
    """
    email = Column(String(255), nullable=True, index=True)
    phone = Column(String(20), nullable=True)
    website = Column(String(255), nullable=True)

# Full model combining all mixins
class FullModel(BaseModel, TimestampMixin, StatusMixin, SoftDeleteMixin, AuditMixin):
    """
    Full model with all common mixins.
    Use this as base for models that need all functionality.
    """
    __abstract__ = True
