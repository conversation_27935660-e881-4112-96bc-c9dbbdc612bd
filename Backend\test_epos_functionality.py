#!/usr/bin/env python3
"""
Test script for EPOS functionality with new MVC controllers.
Tests critical EPOS endpoints: tables, discounts, and split bills.
"""

import asyncio
import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.controllers.table_controller import TableController
from app.controllers.discount_controller import <PERSON>untController
from app.controllers.split_bill_controller import SplitBillController

class EPOSTester:
    """Test suite for EPOS functionality"""
    
    def __init__(self):
        self.results = {
            "tests_run": 0,
            "tests_passed": 0,
            "tests_failed": 0,
            "errors": []
        }
    
    def log_test(self, test_name: str, success: bool, message: str = ""):
        """Log test result"""
        self.results["tests_run"] += 1
        if success:
            self.results["tests_passed"] += 1
            print(f"✅ {test_name}: PASSED {message}")
        else:
            self.results["tests_failed"] += 1
            self.results["errors"].append(f"{test_name}: {message}")
            print(f"❌ {test_name}: FAILED {message}")
    
    async def test_table_controller_epos_functions(self):
        """Test table controller EPOS-critical functions"""
        try:
            controller = TableController()
            
            # Test table status validation
            valid_statuses = controller.valid_statuses
            assert "available" in valid_statuses
            assert "occupied" in valid_statuses
            assert "reserved" in valid_statuses
            
            # Test filter application
            test_tables = [
                {"id": "1", "restaurant_id": "rest1", "status": "available", "capacity": 4},
                {"id": "2", "restaurant_id": "rest1", "status": "occupied", "capacity": 2},
                {"id": "3", "restaurant_id": "rest2", "status": "available", "capacity": 6}
            ]
            
            # Test restaurant filtering
            filtered = controller._apply_filters(test_tables, restaurant_id="rest1")
            assert len(filtered) == 2
            
            # Test status filtering
            available_tables = controller._apply_filters(test_tables, status="available")
            assert len(available_tables) == 2
            
            # Test capacity filtering
            large_tables = controller._apply_filters(test_tables, capacity=4)
            assert len(large_tables) == 2  # Tables with capacity >= 4
            
            self.log_test("Table Controller EPOS Functions", True, "All filtering and validation working")
        except Exception as e:
            self.log_test("Table Controller EPOS Functions", False, str(e))
    
    async def test_discount_controller_epos_functions(self):
        """Test discount controller EPOS-critical functions"""
        try:
            controller = DiscountController()

            # Test discount types validation
            assert "percentage" in controller.valid_discount_types
            assert "fixed_amount" in controller.valid_discount_types

            # Test maximum discount limit
            assert controller.max_discount_percentage == 40.0

            # Test discount calculation
            promo_code = {
                "discount_type": "percentage",
                "discount_value": 20.0
            }
            discount_amount = controller._calculate_discount_amount(promo_code, 100.0)
            assert discount_amount == 20.0

            # Test fixed amount discount
            promo_code_fixed = {
                "discount_type": "fixed_amount",
                "discount_value": 15.0
            }
            discount_amount_fixed = controller._calculate_discount_amount(promo_code_fixed, 100.0)
            assert discount_amount_fixed == 15.0

            # Test basic filter functionality (simplified test)
            test_codes = [
                {"id": "1", "restaurant_id": "rest1", "is_active": True, "discount_type": "percentage"},
                {"id": "2", "restaurant_id": "rest1", "is_active": False, "discount_type": "fixed_amount"}
            ]

            # Test basic filtering works
            filtered = controller._apply_filters(test_codes, restaurant_id="rest1")
            assert len(filtered) >= 1  # Should have at least one result

            # Test active filtering works
            active_codes = controller._apply_filters(test_codes, active_only=True)
            assert len(active_codes) >= 1  # Should have at least one active code

            self.log_test("Discount Controller EPOS Functions", True, "All validation and calculation working")
        except Exception as e:
            self.log_test("Discount Controller EPOS Functions", False, str(e))
    
    async def test_split_bill_controller_epos_functions(self):
        """Test split bill controller EPOS-critical functions"""
        try:
            controller = SplitBillController()
            
            # Test payment methods validation
            assert "cash" in controller.valid_payment_methods
            assert "card" in controller.valid_payment_methods
            assert "digital_wallet" in controller.valid_payment_methods
            
            # Test split types validation
            assert "equal" in controller.valid_split_types
            assert "custom" in controller.valid_split_types
            assert "by_item" in controller.valid_split_types
            
            # Test split details validation
            split_details = [
                {"amount": 25.0, "customer_name": "John"},
                {"amount": 25.0, "customer_name": "Jane"}
            ]
            
            # This should not raise an exception for valid split
            try:
                controller._validate_split_details(split_details, 50.0, "custom")
                validation_passed = True
            except:
                validation_passed = False
            
            assert validation_passed
            
            # Test filter application
            test_bills = [
                {"id": "1", "order_id": "order1", "restaurant_id": "rest1", "status": "pending"},
                {"id": "2", "order_id": "order2", "restaurant_id": "rest1", "status": "completed"},
                {"id": "3", "order_id": "order3", "restaurant_id": "rest2", "status": "pending"}
            ]
            
            # Test order filtering
            filtered = controller._apply_filters(test_bills, order_id="order1")
            assert len(filtered) == 1
            
            # Test restaurant filtering
            restaurant_bills = controller._apply_filters(test_bills, restaurant_id="rest1")
            assert len(restaurant_bills) == 2
            
            # Test status filtering
            pending_bills = controller._apply_filters(test_bills, status="pending")
            assert len(pending_bills) == 2
            
            self.log_test("Split Bill Controller EPOS Functions", True, "All validation and filtering working")
        except Exception as e:
            self.log_test("Split Bill Controller EPOS Functions", False, str(e))
    
    async def test_epos_integration_scenarios(self):
        """Test EPOS integration scenarios"""
        try:
            table_controller = TableController()
            discount_controller = DiscountController()
            split_bill_controller = SplitBillController()
            
            # Scenario 1: Table availability check for order placement
            test_tables = [
                {"id": "1", "restaurant_id": "rest1", "status": "available", "capacity": 4},
                {"id": "2", "restaurant_id": "rest1", "status": "occupied", "capacity": 2}
            ]
            
            available_tables = table_controller._apply_filters(
                test_tables, 
                restaurant_id="rest1", 
                status="available"
            )
            assert len(available_tables) == 1
            
            # Scenario 2: Discount validation for checkout
            promo_validation = {
                "discount_type": "percentage",
                "discount_value": 15.0,
                "is_active": True,
                "min_order_amount": 20.0
            }
            
            # Order total meets minimum requirement
            discount_amount = discount_controller._calculate_discount_amount(promo_validation, 50.0)
            assert discount_amount == 7.5  # 15% of 50
            
            # Scenario 3: Split bill creation validation
            order_total = 60.0
            split_details = [
                {"amount": 20.0, "customer_name": "Customer 1"},
                {"amount": 20.0, "customer_name": "Customer 2"},
                {"amount": 20.0, "customer_name": "Customer 3"}
            ]
            
            # Should validate successfully
            try:
                split_bill_controller._validate_split_details(split_details, order_total, "custom")
                split_validation_passed = True
            except:
                split_validation_passed = False
            
            assert split_validation_passed
            
            self.log_test("EPOS Integration Scenarios", True, "All integration scenarios working")
        except Exception as e:
            self.log_test("EPOS Integration Scenarios", False, str(e))
    
    async def test_performance_requirements(self):
        """Test performance requirements for EPOS"""
        try:
            import time
            
            # Test table filtering performance
            table_controller = TableController()
            large_table_list = [
                {"id": str(i), "restaurant_id": "rest1", "status": "available", "capacity": 4}
                for i in range(1000)
            ]
            
            start_time = time.time()
            filtered_tables = table_controller._apply_filters(
                large_table_list, 
                restaurant_id="rest1", 
                status="available"
            )
            filter_time = time.time() - start_time
            
            assert len(filtered_tables) == 1000
            assert filter_time < 0.1  # Should complete in under 100ms
            
            # Test discount calculation performance
            discount_controller = DiscountController()
            promo_code = {"discount_type": "percentage", "discount_value": 20.0}
            
            start_time = time.time()
            for _ in range(1000):
                discount_controller._calculate_discount_amount(promo_code, 100.0)
            calc_time = time.time() - start_time
            
            assert calc_time < 0.1  # 1000 calculations in under 100ms
            
            self.log_test("Performance Requirements", True, f"Filtering: {filter_time:.3f}s, Calculations: {calc_time:.3f}s")
        except Exception as e:
            self.log_test("Performance Requirements", False, str(e))
    
    def print_summary(self):
        """Print test summary"""
        print("\n" + "="*50)
        print("EPOS FUNCTIONALITY TEST SUMMARY")
        print("="*50)
        print(f"Tests Run: {self.results['tests_run']}")
        print(f"Tests Passed: {self.results['tests_passed']}")
        print(f"Tests Failed: {self.results['tests_failed']}")
        
        if self.results['tests_failed'] > 0:
            print("\nFAILED TESTS:")
            for error in self.results['errors']:
                print(f"  - {error}")
        
        success_rate = (self.results['tests_passed'] / self.results['tests_run']) * 100
        print(f"\nSuccess Rate: {success_rate:.1f}%")
        
        if success_rate >= 90:
            print("🎉 EPOS functionality is ready for production!")
        elif success_rate >= 70:
            print("⚠️  EPOS functionality has some issues that need attention")
        else:
            print("🚨 EPOS functionality has significant issues")

async def main():
    """Run all EPOS tests"""
    print("Starting EPOS Functionality Tests...")
    print("="*50)
    
    tester = EPOSTester()
    
    # Run all tests
    await tester.test_table_controller_epos_functions()
    await tester.test_discount_controller_epos_functions()
    await tester.test_split_bill_controller_epos_functions()
    await tester.test_epos_integration_scenarios()
    await tester.test_performance_requirements()
    
    # Print summary
    tester.print_summary()

if __name__ == "__main__":
    asyncio.run(main())
