import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { toast } from "@/components/ui/sonner";
import { ChefHat, Store } from "lucide-react";
import { useRestaurant } from "@/contexts/RestaurantContext";

const RestaurantSelect = () => {
  const navigate = useNavigate();
  const { availableRestaurants, authenticateRestaurant } = useRestaurant();
  const [selectedRestaurantId, setSelectedRestaurantId] = useState<string>("");
  const [rememberRestaurant, setRememberRestaurant] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const handleRestaurantSelect = (id: string) => {
    setSelectedRestaurantId(id);
  };

  const handleContinue = async () => {
    if (!selectedRestaurantId) {
      toast.error("Please select a restaurant");
      return;
    }

    setIsLoading(true);
    try {
      const success = await authenticateRestaurant(selectedRestaurantId, rememberRestaurant);
      if (success) {
        navigate('/staff-pin');
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-background p-4">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <div className="inline-flex items-center gap-2">
            <ChefHat size={36} className="text-primary" />
            <span className="text-2xl font-bold">Promith</span>
          </div>
          <h1 className="text-3xl font-bold mt-6 mb-2">Select Restaurant</h1>
          <p className="text-muted-foreground">
            Choose your restaurant to continue
          </p>
        </div>

        <div className="space-y-4">
          {availableRestaurants.length === 0 ? (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-8 text-muted-foreground">
                  No restaurants available
                </div>
              </CardContent>
            </Card>
          ) : (
            availableRestaurants.map((restaurant) => (
              <Card 
                key={restaurant.id}
                className={`cursor-pointer transition-colors ${
                  selectedRestaurantId === restaurant.id 
                    ? 'border-primary bg-primary/5' 
                    : 'hover:bg-muted/50'
                }`}
                onClick={() => handleRestaurantSelect(restaurant.id)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center gap-4">
                    <div className="h-12 w-12 rounded-md bg-primary/10 flex items-center justify-center">
                      {restaurant.logo ? (
                        <img 
                          src={restaurant.logo} 
                          alt={restaurant.name} 
                          className="h-10 w-10 object-contain"
                        />
                      ) : (
                        <Store className="h-6 w-6 text-primary" />
                      )}
                    </div>
                    <div>
                      <h3 className="font-medium">{restaurant.name}</h3>
                      <p className="text-sm text-muted-foreground">{restaurant.address}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>

        <div className="mt-6 flex items-center space-x-2">
          <Checkbox 
            id="remember" 
            checked={rememberRestaurant}
            onCheckedChange={(checked) => setRememberRestaurant(checked === true)}
          />
          <Label htmlFor="remember" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
            Remember restaurant on this device
          </Label>
        </div>

        <Button 
          className="w-full mt-4" 
          onClick={handleContinue}
          disabled={!selectedRestaurantId || isLoading}
        >
          {isLoading ? "Loading..." : "Continue"}
        </Button>
      </div>
    </div>
  );
};

export default RestaurantSelect;
