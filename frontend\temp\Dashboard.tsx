import { useState, useEffect } from "react";
import Layout from "@/components/layout/Layout";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/contexts/AuthContext";
import { useNavigate } from "react-router-dom";
import {
  BarChart3,
  Users,
  ShoppingBasket,
  Calendar,
  LogOut,
  Settings,
  FileText,
  CreditCard
} from "lucide-react";
import StaffHoursSummary from "@/components/admin/StaffHoursSummary";

// Mock staff data
const mockStaffData = [
  {
    id: "1",
    name: "<PERSON>",
    role: "waiter",
    position: "Head Waiter",
    email: "<EMAIL>",
    phone: "(*************",
    status: "active",
    hireDate: "2022-03-15",
    performance: 92,
    assignedHours: 35,
    availableDays: ["mon", "tue", "wed", "thu", "fri"]
  },
  {
    id: "2",
    name: "<PERSON>",
    role: "waiter",
    position: "Waiter",
    email: "<EMAIL>",
    phone: "(*************",
    status: "active",
    hireDate: "2022-05-20",
    performance: 78,
    assignedHours: 25,
    availableDays: ["mon", "wed", "fri", "sat"]
  },
  {
    id: "3",
    name: "David Chen",
    role: "waiter",
    position: "Waiter",
    email: "<EMAIL>",
    phone: "(*************",
    status: "on-leave",
    hireDate: "2022-08-10",
    performance: 65,
    assignedHours: 30,
    availableDays: ["thu", "fri", "sat", "sun"]
  },
  {
    id: "4",
    name: "Maria Lopez",
    role: "chef",
    position: "Head Chef",
    email: "<EMAIL>",
    phone: "(*************",
    status: "active",
    hireDate: "2021-11-05",
    performance: 88,
    assignedHours: 40,
    availableDays: ["wed", "thu", "fri", "sat", "sun"]
  },
  {
    id: "5",
    name: "Robert Johnson",
    role: "manager",
    position: "Floor Manager",
    email: "<EMAIL>",
    phone: "(*************",
    status: "active",
    hireDate: "2021-06-15",
    performance: 95,
    assignedHours: 40,
    availableDays: ["mon", "tue", "wed", "thu", "fri"]
  }
];

const Dashboard = () => {
  const navigate = useNavigate();
  const { user, logout, currentRestaurant } = useAuth();

  // Handle logout
  const handleLogout = () => {
    logout();
    navigate('/');
  };

  return (
    <Layout title="Admin Dashboard" requiredRoles={["admin", "manager"]} requiresFullAccess>
      <div className="space-y-6">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Today's Revenue
              </CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">$3,245.80</div>
              <p className="text-xs text-muted-foreground">
                +12% from yesterday
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Customers Today
              </CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">142</div>
              <p className="text-xs text-muted-foreground">
                +8% from yesterday
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Inventory Items
              </CardTitle>
              <ShoppingBasket className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">245</div>
              <p className="text-xs text-muted-foreground">
                -3% from last week
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Staff on Duty
              </CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">8</div>
              <p className="text-xs text-muted-foreground">
                2 more than scheduled
              </p>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          <Card className="col-span-1 md:col-span-2 lg:col-span-1">
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="grid gap-4">
              <Button
                className="w-full justify-start"
                variant="outline"
                onClick={() => navigate('/admin/epos')}
              >
                <CreditCard className="mr-2 h-4 w-4" />
                Open EPOS System
              </Button>
              <Button
                className="w-full justify-start"
                variant="outline"
                onClick={() => navigate('/admin/inventory')}
              >
                <ShoppingBasket className="mr-2 h-4 w-4" />
                Check Inventory
              </Button>
              <Button
                className="w-full justify-start"
                variant="outline"
                onClick={() => navigate('/admin/staff')}
              >
                <Users className="mr-2 h-4 w-4" />
                Manage Staff
              </Button>
              <Button
                className="w-full justify-start"
                variant="outline"
                onClick={() => navigate('/admin/reports')}
              >
                <FileText className="mr-2 h-4 w-4" />
                View Reports
              </Button>
              <Button
                className="w-full justify-start"
                variant="outline"
                onClick={() => navigate('/admin/settings')}
              >
                <Settings className="mr-2 h-4 w-4" />
                Settings
              </Button>
              <Button
                className="w-full justify-start"
                variant="outline"
                onClick={handleLogout}
              >
                <LogOut className="mr-2 h-4 w-4" />
                Logout
              </Button>
            </CardContent>
          </Card>

          {/* Staff Hours Summary */}
          <StaffHoursSummary staffData={mockStaffData} />
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Restaurant Information</CardTitle>
            </CardHeader>
            <CardContent>
              {currentRestaurant && (
                <div className="space-y-4">
                  <div className="flex items-center space-x-4">
                    {currentRestaurant.logo ? (
                      <img
                        src={currentRestaurant.logo}
                        alt={currentRestaurant.name}
                        className="h-16 w-16 rounded-md object-contain"
                      />
                    ) : (
                      <div className="h-16 w-16 rounded-md bg-muted flex items-center justify-center">
                        <ShoppingBasket className="h-8 w-8 text-muted-foreground" />
                      </div>
                    )}
                    <div>
                      <h3 className="text-lg font-medium">{currentRestaurant.name}</h3>
                      <p className="text-sm text-muted-foreground">{currentRestaurant.address}</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium">Contact</p>
                      <p className="text-sm text-muted-foreground">{currentRestaurant.phone}</p>
                      <p className="text-sm text-muted-foreground">{currentRestaurant.email}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Tax Rate</p>
                      <p className="text-sm text-muted-foreground">{currentRestaurant.taxRate}%</p>
                      <p className="text-sm font-medium mt-2">Currency</p>
                      <p className="text-sm text-muted-foreground">{currentRestaurant.currency}</p>
                    </div>
                  </div>
                </div>
              )}

              {user && (
                <div className="mt-6 pt-6 border-t">
                  <p className="text-sm font-medium">Logged in as</p>
                  <div className="flex items-center mt-2">
                    <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center mr-3">
                      <Users className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <p className="font-medium">{user.name}</p>
                      <p className="text-sm text-muted-foreground">{user.position}</p>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </Layout>
  );
};

export default Dashboard;
