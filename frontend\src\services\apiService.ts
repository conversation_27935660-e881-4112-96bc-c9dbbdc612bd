import logger from "@/utils/logger";
import { RestaurantRegistrationData, RestaurantSetupData } from "@/types/restaurantSetup";

// API Configuration using environment variables
// In development, use proxy paths; in production, use full URLs
const API_BASE_URL = import.meta.env.DEV
  ? '/api'  // Use proxy in development (Vite will proxy to backend)
  : `${import.meta.env.VITE_API_URL || "http://localhost:5001"}/api`; // Direct URL in production
const TIMEOUT_MS = 10000; // 10 seconds

// API Response Types
interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
}

interface RestaurantLoginResponse {
  success: boolean;
  message: string;
  restaurant?: any;
}

interface RegistrationResponse {
  success: boolean;
  message: string;
  restaurant?: any;
  user?: any;
  credentials?: {
    restaurantCode: string;
    ownerPin: string;
  };
}

// Backend connectivity check
export const checkBackendConnectivity = async (): Promise<boolean> => {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

    // Use the proxy path for health check in development
    const healthUrl = import.meta.env.DEV
      ? '/health'  // Use proxy in development (Vite will proxy to backend)
      : `${API_BASE_URL.replace('/api', '')}/health`; // Direct URL in production

    logger.debug('Checking backend connectivity', 'ApiService', { url: healthUrl });

    const response = await fetch(healthUrl, {
      method: 'GET',
      signal: controller.signal,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    clearTimeout(timeoutId);

    if (response.ok) {
      logger.info('Backend connectivity check successful', 'ApiService', { url: healthUrl });
      return true;
    } else {
      logger.warn('Backend health check failed', 'ApiService', { status: response.status, url: healthUrl });
      return false;
    }
  } catch (error) {
    logger.warn('Backend connectivity check failed', 'ApiService', { error: error.message });
    return false;
  }
};

// Generic API request function with error handling
export const apiRequest = async <T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> => {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), TIMEOUT_MS);

  try {
    const url = `${API_BASE_URL}${endpoint}`;

    // Prepare headers with authentication
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    // Add authentication token if available
    const token = localStorage.getItem('access_token');
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    } else {
      // For development, use mock token for AI endpoints
      if (endpoint.includes('/ai/')) {
        headers['Authorization'] = 'Bearer dev-mock-token-admin';
      }
    }

    // Add restaurant ID if available
    const restaurantId = localStorage.getItem('restaurant_id');
    if (restaurantId) {
      headers['X-Restaurant-ID'] = restaurantId;
    }

    const defaultOptions: RequestInit = {
      headers: {
        ...headers,
        ...(options.headers || {})
      },
      signal: controller.signal,
    };

    const response = await fetch(url, { ...defaultOptions, ...options });
    clearTimeout(timeoutId);

    if (!response.ok) {
      let errorMessage = `HTTP ${response.status}`;
      let errorDetails = null;

      try {
        const errorData = await response.json();
        errorDetails = errorData;
        errorMessage = errorData.detail || errorData.message || JSON.stringify(errorData);
      } catch (e) {
        errorMessage = `HTTP ${response.status} - ${response.statusText}`;
      }

      // Enhanced error logging
      logger.error('API Request Failed', 'ApiService', {
        url,
        method: options.method || 'GET',
        status: response.status,
        statusText: response.statusText,
        errorMessage,
        errorDetails,
        headers: Object.fromEntries(response.headers.entries()),
        requestHeaders: headers,
        endpoint
      });

      throw new Error(errorMessage);
    }

    return await response.json();
  } catch (error) {
    clearTimeout(timeoutId);

    // Enhanced error logging for network issues
    if (error.name === 'AbortError') {
      logger.error('API Request Timeout', 'ApiService', {
        url,
        endpoint,
        timeout: TIMEOUT_MS,
        method: options.method || 'GET',
        restaurantId: headers['X-Restaurant-ID']
      });
      throw new Error(`Request timeout (${TIMEOUT_MS}ms) - backend may be unavailable`);
    }

    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      logger.error('API Network Error', 'ApiService', {
        url,
        endpoint,
        error: error.message,
        method: options.method || 'GET',
        restaurantId: headers['X-Restaurant-ID'],
        possibleCause: 'Backend server may be down or proxy configuration issue'
      });
      throw new Error('Network error - unable to connect to backend server');
    }

    // Log any other unexpected errors
    logger.error('API Request Unexpected Error', 'ApiService', {
      url,
      endpoint,
      error: error.message,
      errorName: error.name,
      method: options.method || 'GET',
      restaurantId: headers['X-Restaurant-ID']
    });

    throw error;
  }
};

// Restaurant API functions
export const restaurantApi = {
  // Get all restaurants
  getRestaurants: async () => {
    logger.debug('Fetching all restaurants', 'ApiService');
    return await apiRequest('/restaurants');
  },

  // Get restaurant by ID
  getRestaurant: async (id: string) => {
    logger.debug('Fetching restaurant by ID', 'ApiService', { id });
    return await apiRequest(`/restaurants/${id}`);
  },

  // Get restaurant by code
  getRestaurantByCode: async (code: string) => {
    logger.debug('Fetching restaurant by code', 'ApiService', { code });
    return await apiRequest(`/restaurants/code/${code}`);
  },

  // Update restaurant
  updateRestaurant: async (id: string, updateData: any) => {
    logger.debug('Updating restaurant', 'ApiService', { id, updateData });
    return await apiRequest(`/restaurants/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updateData),
    });
  },

  // Restaurant login
  login: async (code: string, password: string): Promise<RestaurantLoginResponse> => {
    logger.debug('Restaurant login attempt', 'ApiService', { code });

    try {
      const response = await apiRequest<RestaurantLoginResponse>('/restaurants/auth/login', {
        method: 'POST',
        body: JSON.stringify({ code, password }),
      });

      logger.authentication('restaurant login', 'success', 'ApiService', { code });
      return response;
    } catch (error) {
      logger.authentication('restaurant login', 'failure', 'ApiService', { code, error: error.message });
      throw error;
    }
  },

  // Generate unique restaurant code
  generateCode: async (name: string): Promise<{ code: string }> => {
    logger.debug('Generating restaurant code', 'ApiService', { name });
    return await apiRequest('/restaurants/auth/generate-code', {
      method: 'POST',
      body: JSON.stringify({ name }),
    });
  },

  // Complete restaurant registration
  register: async (
    registrationData: RestaurantRegistrationData,
    setupData: RestaurantSetupData,
    credentials: { restaurantCode: string; ownerPin: string }
  ): Promise<RegistrationResponse> => {
    logger.debug('Registering new restaurant', 'ApiService', {
      restaurantName: registrationData.restaurantName,
      code: credentials.restaurantCode
    });

    try {
      const response = await apiRequest<RegistrationResponse>('/restaurants/register', {
        method: 'POST',
        body: JSON.stringify({
          registrationData,
          setupData,
          credentials,
        }),
      });

      logger.dataOperation('create', 'restaurant registration', 'ApiService', {
        restaurantName: registrationData.restaurantName,
        code: credentials.restaurantCode,
        success: response.success
      });

      return response;
    } catch (error) {
      logger.logError(error, 'restaurant registration', 'ApiService');
      throw error;
    }
  },
};

// User API functions
export const userApi = {
  // Get users for a restaurant
  getRestaurantUsers: async (restaurantId: string) => {
    logger.debug('Fetching restaurant users', 'ApiService', { restaurantId });
    return await apiRequest(`/restaurants/${restaurantId}/users`);
  },

  // Create new user for restaurant
  createUser: async (restaurantId: string, userData: any) => {
    logger.debug('Creating restaurant user', 'ApiService', { restaurantId });
    return await apiRequest(`/restaurants/${restaurantId}/users`, {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  },

  // Verify staff PIN for restaurant
  verifyStaffPin: async (restaurantId: string, pin: string) => {
    logger.debug('Verifying staff PIN', 'ApiService', { restaurantId, pin: '****' });
    return await apiRequest(`/restaurants/${restaurantId}/staff/verify-pin`, {
      method: 'POST',
      body: JSON.stringify({ pin }),
    });
  },
};

// Error handling utility
export const handleApiError = (error: any, fallbackMessage: string = 'An error occurred') => {
  if (error.message?.includes('timeout') || error.message?.includes('unavailable')) {
    return {
      isConnectivityError: true,
      message: 'Backend server is unavailable. Please check your connection and try again.',
      originalError: error.message
    };
  }

  return {
    isConnectivityError: false,
    message: error.message || fallbackMessage,
    originalError: error.message
  };
};

// Order API functions
export const orderApi = {
  // Create new order
  createOrder: async (orderData: any) => {
    logger.debug('Creating new order', 'ApiService', { tableId: orderData.table_id });
    return await apiRequest('/orders', {
      method: 'POST',
      body: JSON.stringify(orderData),
    });
  },

  // Get orders for a restaurant
  getOrders: async (restaurantId?: string, status?: string, tableId?: string) => {
    const params = new URLSearchParams();
    if (restaurantId) params.append('restaurant_id', restaurantId);
    if (status) params.append('status', status);
    if (tableId) params.append('table_id', tableId);

    const queryString = params.toString() ? `?${params.toString()}` : '';
    logger.debug('Fetching orders', 'ApiService', { restaurantId, status, tableId });
    return await apiRequest(`/orders${queryString}`);
  },

  // Update order status
  updateOrderStatus: async (orderId: string, status: string) => {
    logger.debug('Updating order status', 'ApiService', { orderId, status });
    return await apiRequest(`/orders/${orderId}/status`, {
      method: 'PATCH',
      body: JSON.stringify({ status }),
    });
  },

  // Get order by ID
  getOrder: async (orderId: string) => {
    logger.debug('Fetching order by ID', 'ApiService', { orderId });
    return await apiRequest(`/orders/${orderId}`);
  },
};

// Menu API functions
export const menuApi = {
  // Get menu items
  getMenuItems: async (restaurantId?: string, category?: string, allergens?: string[]) => {
    const params = new URLSearchParams();
    if (restaurantId) params.append('restaurant_id', restaurantId);
    if (category) params.append('category', category);
    if (allergens && allergens.length > 0) params.append('allergens', allergens.join(','));

    const queryString = params.toString() ? `?${params.toString()}` : '';
    logger.debug('Fetching menu items', 'ApiService', { restaurantId, category, allergens });
    return await apiRequest(`/menu${queryString}`);
  },

  // Get menu item by ID
  getMenuItem: async (itemId: string) => {
    logger.debug('Fetching menu item by ID', 'ApiService', { itemId });
    return await apiRequest(`/menu/${itemId}`);
  },
};

// Export default service object
const apiService = {
  checkBackendConnectivity,
  apiRequest,
  restaurant: restaurantApi,
  user: userApi,
  order: orderApi,
  menu: menuApi,
  handleApiError,
};

export default apiService;
