import { useState } from "react";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
  LineChart,
  Line,
  AreaChart,
  Area,
  ReferenceLine
} from 'recharts';
import { ForecastData } from "./ForecastCard";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import {
  Download,
  Info,
  TrendingUp,
  BarChart3,
  Percent
} from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Slider } from "@/components/ui/slider";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface RevenueProjectionCardProps {
  data?: ForecastData[];
  title?: string;
  historicalRevenue?: {
    month: string;
    revenue: number;
    growth: number;
  }[];
}

// Custom tooltip component
const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-background border rounded p-2 shadow-sm">
        <p className="font-medium">{label}</p>
        {payload.map((entry: any, index: number) => (
          <p key={`item-${index}`} style={{ color: entry.color }}>
            {entry.name}: {entry.dataKey.includes('revenue') || entry.dataKey.includes('Revenue') ? '£' : ''}
            {entry.value.toLocaleString()}
            {entry.dataKey.includes('growth') || entry.dataKey.includes('Growth') ? '%' : ''}
          </p>
        ))}
      </div>
    );
  }

  return null;
};

// Mock historical revenue data
const defaultHistoricalRevenue = [
  { month: 'Jan', revenue: 85000, growth: 5.2 },
  { month: 'Feb', revenue: 82000, growth: 4.8 },
  { month: 'Mar', revenue: 90000, growth: 6.5 },
  { month: 'Apr', revenue: 95000, growth: 7.2 },
  { month: 'May', revenue: 100000, growth: 8.0 },
  { month: 'Jun', revenue: 110000, growth: 9.5 },
  { month: 'Jul', revenue: 115000, growth: 10.2 },
  { month: 'Aug', revenue: 112000, growth: 9.8 },
  { month: 'Sep', revenue: 105000, growth: 8.5 },
  { month: 'Oct', revenue: 98000, growth: 7.5 },
  { month: 'Nov', revenue: 92000, growth: 6.8 },
  { month: 'Dec', revenue: 108000, growth: 9.0 }
];

const RevenueProjectionCard = ({
  data = [],
  title = "Revenue Projections",
  historicalRevenue = defaultHistoricalRevenue
}: RevenueProjectionCardProps) => {
  const [timeframe, setTimeframe] = useState<"6m" | "1y" | "3y">("1y");
  const [growthRate, setGrowthRate] = useState<number>(8);

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  // Calculate average monthly revenue
  const averageMonthlyRevenue = historicalRevenue.reduce((sum, month) => sum + month.revenue, 0) / historicalRevenue.length;

  // Calculate average growth rate
  const averageGrowthRate = historicalRevenue.reduce((sum, month) => sum + month.growth, 0) / historicalRevenue.length;

  // Generate projection data
  const generateProjectionData = () => {
    const projectionMonths = timeframe === "6m" ? 6 : timeframe === "1y" ? 12 : 36;
    const projectionData = [];

    // Start with the last month's revenue
    let currentRevenue = historicalRevenue[historicalRevenue.length - 1].revenue;

    // Get current month and year
    const currentDate = new Date();
    let currentMonth = currentDate.getMonth();
    let currentYear = currentDate.getFullYear();

    for (let i = 0; i < projectionMonths; i++) {
      // Calculate projected revenue with the selected growth rate
      currentRevenue = currentRevenue * (1 + (growthRate / 100));

      // Increment month and year
      currentMonth++;
      if (currentMonth > 11) {
        currentMonth = 0;
        currentYear++;
      }

      // Get month name
      const monthName = new Date(currentYear, currentMonth, 1).toLocaleString('default', { month: 'short' });

      projectionData.push({
        month: `${monthName} ${currentYear}`,
        projectedRevenue: Math.round(currentRevenue),
        growthRate: growthRate
      });
    }

    return projectionData;
  };

  const projectionData = generateProjectionData();

  // Calculate total projected revenue
  const totalProjectedRevenue = projectionData.reduce((sum, month) => sum + month.projectedRevenue, 0);

  // Calculate projected annual revenue
  const projectedAnnualRevenue = averageMonthlyRevenue * 12 * (1 + (growthRate / 100));

  return (
    <Card className="col-span-full">
      <CardHeader>
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2">
          <div className="flex items-center gap-2">
            <div>
              <CardTitle>{title}</CardTitle>
              <CardDescription>
                Revenue projections based on current growth patterns
              </CardDescription>
            </div>
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <Info className="h-4 w-4" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80">
                <div className="space-y-2">
                  <h4 className="font-medium">About Revenue Projections</h4>
                  <p className="text-sm text-muted-foreground">
                    These projections are based on historical revenue data and current growth trends.
                    Adjust the growth rate slider to see different scenarios based on your business expectations.
                  </p>
                  <p className="text-sm text-muted-foreground">
                    The default growth rate is calculated from your historical performance.
                  </p>
                </div>
              </PopoverContent>
            </Popover>
          </div>
          <div className="flex items-center gap-2">
            <Select
              value={timeframe}
              onValueChange={(value) => setTimeframe(value as "6m" | "1y" | "3y")}
            >
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder="Timeframe" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="6m">6 Months</SelectItem>
                <SelectItem value="1y">1 Year</SelectItem>
                <SelectItem value="3y">3 Years</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" size="icon">
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="projections">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="projections">Projections</TabsTrigger>
            <TabsTrigger value="scenarios">Scenarios</TabsTrigger>
            <TabsTrigger value="breakdown">Monthly Breakdown</TabsTrigger>
          </TabsList>

          <TabsContent value="projections" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm font-medium">Projected Revenue</div>
                      <div className="text-2xl font-bold mt-1">{formatCurrency(totalProjectedRevenue)}</div>
                    </div>
                    <span className="h-8 w-8 text-blue-500 flex items-center justify-center text-2xl font-bold">£</span>
                  </div>
                  <div className="text-sm text-muted-foreground mt-2">
                    For the next {timeframe === "6m" ? "6 months" : timeframe === "1y" ? "year" : "3 years"}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm font-medium">Growth Rate</div>
                      <div className="text-2xl font-bold mt-1">{growthRate.toFixed(1)}%</div>
                    </div>
                    <TrendingUp className="h-8 w-8 text-green-500" />
                  </div>
                  <div className="text-sm text-muted-foreground mt-2">
                    Projected monthly growth
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm font-medium">Annual Revenue</div>
                      <div className="text-2xl font-bold mt-1">{formatCurrency(projectedAnnualRevenue)}</div>
                    </div>
                    <BarChart3 className="h-8 w-8 text-purple-500" />
                  </div>
                  <div className="text-sm text-muted-foreground mt-2">
                    Projected yearly total
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="mt-6">
              <div className="flex items-center justify-between mb-2">
                <div className="text-sm font-medium">Adjust Growth Rate: {growthRate.toFixed(1)}%</div>
                <Badge variant={growthRate > averageGrowthRate ? "default" : "outline"} className="text-xs">
                  {growthRate > averageGrowthRate + 2 ? 'Optimistic' :
                   growthRate < averageGrowthRate - 2 ? 'Conservative' : 'Realistic'}
                </Badge>
              </div>
              <Slider
                defaultValue={[growthRate]}
                min={0}
                max={20}
                step={0.5}
                onValueChange={(value) => setGrowthRate(value[0])}
                className="mb-6"
              />
            </div>

            <div className="h-[300px] mt-4">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart
                  data={projectionData}
                  margin={{ top: 10, right: 10, left: 10, bottom: 20 }}
                >
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis
                    dataKey="month"
                    tick={{ fontSize: 12 }}
                    tickLine={false}
                    axisLine={false}
                  />
                  <YAxis
                    tickFormatter={(value) => `£${value/1000}k`}
                    tick={{ fontSize: 12 }}
                    tickLine={false}
                    axisLine={false}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                  <Area
                    type="monotone"
                    dataKey="projectedRevenue"
                    name="Projected Revenue"
                    stroke="#3b82f6"
                    fill="#3b82f680"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
          </TabsContent>

          <TabsContent value="scenarios" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base">Conservative</CardTitle>
                  <CardDescription>5% Growth Rate</CardDescription>
                </CardHeader>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold">
                    {formatCurrency(averageMonthlyRevenue * 12 * (1 + (5 / 100)))}
                  </div>
                  <div className="text-sm text-muted-foreground mt-2">
                    Annual revenue projection
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base">Realistic</CardTitle>
                  <CardDescription>{averageGrowthRate.toFixed(1)}% Growth Rate</CardDescription>
                </CardHeader>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold">
                    {formatCurrency(averageMonthlyRevenue * 12 * (1 + (averageGrowthRate / 100)))}
                  </div>
                  <div className="text-sm text-muted-foreground mt-2">
                    Annual revenue projection
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base">Optimistic</CardTitle>
                  <CardDescription>12% Growth Rate</CardDescription>
                </CardHeader>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold">
                    {formatCurrency(averageMonthlyRevenue * 12 * (1 + (12 / 100)))}
                  </div>
                  <div className="text-sm text-muted-foreground mt-2">
                    Annual revenue projection
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="h-[300px] mt-4">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  margin={{ top: 10, right: 10, left: 10, bottom: 20 }}
                >
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis
                    dataKey="month"
                    tick={{ fontSize: 12 }}
                    tickLine={false}
                    axisLine={false}
                    allowDuplicatedCategory={false}
                  />
                  <YAxis
                    tickFormatter={(value) => `£${value/1000}k`}
                    tick={{ fontSize: 12 }}
                    tickLine={false}
                    axisLine={false}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                  <Line
                    data={generateProjectionData().map(item => ({ ...item, month: item.month }))}
                    type="monotone"
                    dataKey="projectedRevenue"
                    name="Current Projection"
                    stroke="#3b82f6"
                    strokeWidth={2}
                    dot={{ r: 4 }}
                    activeDot={{ r: 6 }}
                  />
                  <Line
                    data={Array.from({ length: timeframe === "6m" ? 6 : timeframe === "1y" ? 12 : 36 }).map((_, i) => {
                      const date = new Date();
                      date.setMonth(date.getMonth() + i + 1);
                      return {
                        month: date.toLocaleString('default', { month: 'short' }) + ' ' + date.getFullYear(),
                        conservativeRevenue: Math.round(historicalRevenue[historicalRevenue.length - 1].revenue * Math.pow(1 + (5 / 100), i + 1))
                      };
                    })}
                    type="monotone"
                    dataKey="conservativeRevenue"
                    name="Conservative"
                    stroke="#94a3b8"
                    strokeWidth={2}
                    strokeDasharray="5 5"
                    dot={false}
                  />
                  <Line
                    data={Array.from({ length: timeframe === "6m" ? 6 : timeframe === "1y" ? 12 : 36 }).map((_, i) => {
                      const date = new Date();
                      date.setMonth(date.getMonth() + i + 1);
                      return {
                        month: date.toLocaleString('default', { month: 'short' }) + ' ' + date.getFullYear(),
                        optimisticRevenue: Math.round(historicalRevenue[historicalRevenue.length - 1].revenue * Math.pow(1 + (12 / 100), i + 1))
                      };
                    })}
                    type="monotone"
                    dataKey="optimisticRevenue"
                    name="Optimistic"
                    stroke="#10b981"
                    strokeWidth={2}
                    strokeDasharray="5 5"
                    dot={false}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </TabsContent>

          <TabsContent value="breakdown" className="space-y-4">
            <div className="rounded-md border mt-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Month</TableHead>
                    <TableHead>Projected Revenue</TableHead>
                    <TableHead>Growth Rate</TableHead>
                    <TableHead>vs. Previous Year</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {projectionData.slice(0, 12).map((month, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">{month.month}</TableCell>
                      <TableCell>{formatCurrency(month.projectedRevenue)}</TableCell>
                      <TableCell>
                        <Badge variant="outline" className="text-xs">
                          {month.growthRate.toFixed(1)}%
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                          {(month.projectedRevenue / historicalRevenue[index % 12].revenue * 100 - 100).toFixed(1)}%
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default RevenueProjectionCard;
