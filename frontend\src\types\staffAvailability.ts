// Staff availability types

export type UnavailabilityType = 'holiday' | 'sick' | 'personal' | 'training' | 'other';

export interface TimeOffRequest {
  id: string;
  staffId: string;
  startDate: string; // ISO date string
  endDate: string; // ISO date string
  type: UnavailabilityType;
  status: 'pending' | 'approved' | 'rejected';
  reason: string;
  isFullDay: boolean;
  startTime?: string; // Time string (HH:mm) for partial day requests
  endTime?: string; // Time string (HH:mm) for partial day requests
  notes?: string;
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
  approvedBy?: string; // Manager ID
}

export interface StaffUnavailability {
  id: string;
  staffId: string;
  date: string; // ISO date string
  type: UnavailabilityType;
  reason: string;
  isFullDay: boolean;
  startTime?: string; // Time string (HH:mm) for partial day unavailability
  endTime?: string; // Time string (HH:mm) for partial day unavailability
  requestId?: string; // Reference to the TimeOffRequest if applicable
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
}

export interface SchedulingConflict {
  id: string;
  type: 'unavailability' | 'double-booking' | 'overtime' | 'understaffed';
  staffId: string;
  staffName: string;
  shiftId: string;
  date: string; // ISO date string
  description: string;
  severity: 'low' | 'medium' | 'high';
  resolved?: boolean;
  unavailabilityId?: string;
  createdAt: string; // ISO date string
}

export interface StaffAvailabilitySettings {
  staffId: string;
  assignedHours: number;
  availableDays: string[]; // e.g., ['mon', 'tue', 'wed', 'thu', 'fri']
  timePreferences: {
    morning: boolean;
    evening: boolean;
  };
}
