import React, { useState, useEffect } from "react";
import Layout from "@/components/layout/Layout";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Search,
  Plus,
  Pencil,
  Trash2,
  CheckCircle,
  Clock,
  Calendar,
  AlertCircle,
  ClipboardList,
  Brush,
  Scissors,
  RefreshCw,
  MoreHorizontal,
  Copy
} from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "@/components/ui/sonner";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import TaskList from "@/components/admin/TaskList";
import {
  Task,
  getAllTasks,
  getTasksByCategory,
  addTask,
  updateTask,
  deleteTask,
  markTaskAsCompleted,
  markTaskAsInProgress,
  initializeMockTasks
} from "@/services/taskService";

const TaskManagement = () => {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [filteredTasks, setFilteredTasks] = useState<Task[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  const [selectedPriority, setSelectedPriority] = useState<string>("all");
  const [isAddTaskDialogOpen, setIsAddTaskDialogOpen] = useState(false);
  const [isEditTaskDialogOpen, setIsEditTaskDialogOpen] = useState(false);
  const [isDeleteTaskDialogOpen, setIsDeleteTaskDialogOpen] = useState(false);

  const [currentTask, setCurrentTask] = useState<Task | null>(null);
  const [newTask, setNewTask] = useState<Partial<Task>>({
    title: "",
    description: "",
    category: "cleaning",
    assignedTo: "",
    assignedBy: "1", // Assuming admin ID is 1
    status: "pending",
    priority: "medium",
    dueDate: new Date(new Date().setDate(new Date().getDate() + 1)).toISOString().split('T')[0],
    completedAt: null
  });

  // Mock staff data
  const staffData = [
    {
      id: "1",
      name: "Michael Rodriguez",
      role: "waiter",
      position: "Head Waiter",
    },
    {
      id: "2",
      name: "Jennifer Smith",
      role: "waiter",
      position: "Waiter",
    },
    {
      id: "3",
      name: "David Chen",
      role: "waiter",
      position: "Waiter",
    },
    {
      id: "4",
      name: "Maria Lopez",
      role: "chef",
      position: "Head Chef",
    },
    {
      id: "5",
      name: "Robert Johnson",
      role: "manager",
      position: "Floor Manager",
    }
  ];



  // Load tasks on component mount
  useEffect(() => {
    loadTasks();
  }, []);

  // Filter tasks when search query or filters change
  useEffect(() => {
    filterTasks();
  }, [tasks, searchQuery, selectedCategory, selectedStatus, selectedPriority]);

  // Load all tasks from localStorage
  const loadTasks = () => {
    // Initialize mock tasks if needed
    initializeMockTasks(staffData);

    // Get all tasks
    const allTasks = getAllTasks();
    setTasks(allTasks);
  };

  // Filter tasks based on search query and selected filters
  const filterTasks = () => {
    let filtered = [...tasks];

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(task =>
        task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        task.description.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Filter by category
    if (selectedCategory !== "all") {
      filtered = filtered.filter(task => task.category === selectedCategory);
    }

    // Filter by status
    if (selectedStatus !== "all") {
      filtered = filtered.filter(task => task.status === selectedStatus);
    }

    // Filter by priority
    if (selectedPriority !== "all") {
      filtered = filtered.filter(task => task.priority === selectedPriority);
    }

    setFilteredTasks(filtered);
  };

  // Get staff member by ID
  const getStaffMember = (id: string) => {
    return staffData.find(staff => staff.id === id);
  };

  // Get category icon
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "cleaning":
        return <Brush className="h-4 w-4" />;
      case "cutting":
        return <Scissors className="h-4 w-4" />;
      case "refilling":
        return <RefreshCw className="h-4 w-4" />;
      default:
        return <ClipboardList className="h-4 w-4" />;
    }
  };

  // Get priority badge variant
  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "high":
        return <div className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">High</div>;
      case "medium":
        return <div className="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">Medium</div>;
      case "low":
        return <div className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Low</div>;
      default:
        return <div className="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">Unknown</div>;
    }
  };

  // Get status badge variant
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return <div className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Completed</div>;
      case "in-progress":
        return <div className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">In Progress</div>;
      case "pending":
        return <div className="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">Pending</div>;
      default:
        return <div className="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">Unknown</div>;
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Handle adding new task
  const handleAddTask = () => {
    // Validate required fields
    if (!newTask.title || !newTask.dueDate) {
      toast.error("Please fill in all required fields");
      return;
    }

    // Add the task
    const addedTask = addTask(newTask as Omit<Task, 'id' | 'createdAt'>);

    // Update local state
    setTasks([...tasks, addedTask]);

    // Reset form and close dialog
    setNewTask({
      title: "",
      description: "",
      category: "cleaning",
      assignedTo: "",
      assignedBy: "1",
      status: "pending",
      priority: "medium",
      dueDate: new Date(new Date().setDate(new Date().getDate() + 1)).toISOString().split('T')[0],
      completedAt: null
    });
    setIsAddTaskDialogOpen(false);

    // Show success message
    toast.success("Task added successfully");
  };

  // Handle editing task
  const handleEditTask = () => {
    // Validate required fields
    if (!currentTask || !currentTask.title || !currentTask.dueDate) {
      toast.error("Please fill in all required fields");
      return;
    }

    // Update the task
    updateTask(currentTask);

    // Update local state
    setTasks(tasks.map(task => task.id === currentTask.id ? currentTask : task));

    // Reset and close dialog
    setCurrentTask(null);
    setIsEditTaskDialogOpen(false);

    // Show success message
    toast.success("Task updated successfully");
  };

  // Handle deleting task
  const handleDeleteTask = () => {
    if (!currentTask) return;

    // Delete the task
    deleteTask(currentTask.id);

    // Update local state
    setTasks(tasks.filter(task => task.id !== currentTask.id));

    // Reset and close dialog
    setCurrentTask(null);
    setIsDeleteTaskDialogOpen(false);

    // Show success message
    toast.success("Task deleted successfully");
  };

  // Handle marking task as completed
  const handleMarkAsCompleted = (taskId: string) => {
    // For demo purposes, we'll use a random staff member as the completedBy
    const randomStaffIndex = Math.floor(Math.random() * staffData.length);
    const completedBy = staffData[randomStaffIndex].id;
    const completedAt = new Date().toISOString();

    // Mark the task as completed
    markTaskAsCompleted(taskId, completedBy);

    // Update local state
    setTasks(tasks.map(task => {
      if (task.id === taskId) {
        return {
          ...task,
          status: "completed",
          completedAt,
          completedBy
        };
      }
      return task;
    }));

    // Show success message with staff name and time
    const formattedDate = formatDate(completedAt);
    const formattedTime = new Date(completedAt).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });

    toast.success("Task completed successfully", {
      description: `Task completed by staff member at ${formattedTime} on ${formattedDate}`
    });
  };

  // Handle marking task as in-progress
  const handleMarkAsInProgress = (taskId: string) => {
    // For demo purposes, we'll use a random staff member as the assignedTo
    const randomStaffIndex = Math.floor(Math.random() * staffData.length);
    const startedBy = staffData[randomStaffIndex].id;
    const startedAt = new Date().toISOString();

    // Mark the task as in-progress
    markTaskAsInProgress(taskId, startedBy);

    // Update local state
    setTasks(tasks.map(task => {
      if (task.id === taskId) {
        return {
          ...task,
          status: "in-progress",
          assignedTo: startedBy,
          startedAt // Add this to track when the task was started
        };
      }
      return task;
    }));

    // Show success message with staff name and time
    const formattedDate = formatDate(startedAt);
    const formattedTime = new Date(startedAt).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });

    toast.success("Task started", {
      description: `Task started by staff member at ${formattedTime} on ${formattedDate}`
    });
  };



  return (
    <Layout title="Task Management" requiredRoles={["admin"]}>
      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="relative w-full sm:w-72">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search tasks..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className="flex flex-wrap gap-2">
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-[130px]">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="cleaning">Cleaning</SelectItem>
                <SelectItem value="cutting">Cutting</SelectItem>
                <SelectItem value="refilling">Refilling</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-[130px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="in-progress">In Progress</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
              </SelectContent>
            </Select>
            <Select value={selectedPriority} onValueChange={setSelectedPriority}>
              <SelectTrigger className="w-[130px]">
                <SelectValue placeholder="Priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Priorities</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="low">Low</SelectItem>
              </SelectContent>
            </Select>

            <Button onClick={() => setIsAddTaskDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" /> Add Task
            </Button>
          </div>
        </div>

        <Tabs defaultValue="all" className="space-y-4">
          <TabsList>
            <TabsTrigger value="all">All Tasks</TabsTrigger>
            <TabsTrigger value="cleaning">
              <Brush className="h-4 w-4 mr-2" /> Cleaning
            </TabsTrigger>
            <TabsTrigger value="cutting">
              <Scissors className="h-4 w-4 mr-2" /> Cutting
            </TabsTrigger>
            <TabsTrigger value="refilling">
              <RefreshCw className="h-4 w-4 mr-2" /> Refilling
            </TabsTrigger>
            <TabsTrigger value="other">
              <ClipboardList className="h-4 w-4 mr-2" /> Other
            </TabsTrigger>
          </TabsList>

          <TabsContent value="all">
            <TaskList
              tasks={filteredTasks}
              getStaffMember={getStaffMember}
              getCategoryIcon={getCategoryIcon}
              getPriorityBadge={getPriorityBadge}
              getStatusBadge={getStatusBadge}
              formatDate={formatDate}
              setCurrentTask={setCurrentTask}
              setIsEditTaskDialogOpen={setIsEditTaskDialogOpen}
              setIsDeleteTaskDialogOpen={setIsDeleteTaskDialogOpen}
              handleMarkAsCompleted={handleMarkAsCompleted}
              handleMarkAsInProgress={handleMarkAsInProgress}
            />
          </TabsContent>

          <TabsContent value="cleaning">
            <TaskList
              tasks={filteredTasks.filter(task => task.category === "cleaning")}
              getStaffMember={getStaffMember}
              getCategoryIcon={getCategoryIcon}
              getPriorityBadge={getPriorityBadge}
              getStatusBadge={getStatusBadge}
              formatDate={formatDate}
              setCurrentTask={setCurrentTask}
              setIsEditTaskDialogOpen={setIsEditTaskDialogOpen}
              setIsDeleteTaskDialogOpen={setIsDeleteTaskDialogOpen}
              handleMarkAsCompleted={handleMarkAsCompleted}
              handleMarkAsInProgress={handleMarkAsInProgress}
            />
          </TabsContent>

          <TabsContent value="cutting">
            <TaskList
              tasks={filteredTasks.filter(task => task.category === "cutting")}
              getStaffMember={getStaffMember}
              getCategoryIcon={getCategoryIcon}
              getPriorityBadge={getPriorityBadge}
              getStatusBadge={getStatusBadge}
              formatDate={formatDate}
              setCurrentTask={setCurrentTask}
              setIsEditTaskDialogOpen={setIsEditTaskDialogOpen}
              setIsDeleteTaskDialogOpen={setIsDeleteTaskDialogOpen}
              handleMarkAsCompleted={handleMarkAsCompleted}
              handleMarkAsInProgress={handleMarkAsInProgress}
            />
          </TabsContent>

          <TabsContent value="refilling">
            <TaskList
              tasks={filteredTasks.filter(task => task.category === "refilling")}
              getStaffMember={getStaffMember}
              getCategoryIcon={getCategoryIcon}
              getPriorityBadge={getPriorityBadge}
              getStatusBadge={getStatusBadge}
              formatDate={formatDate}
              setCurrentTask={setCurrentTask}
              setIsEditTaskDialogOpen={setIsEditTaskDialogOpen}
              setIsDeleteTaskDialogOpen={setIsDeleteTaskDialogOpen}
              handleMarkAsCompleted={handleMarkAsCompleted}
              handleMarkAsInProgress={handleMarkAsInProgress}
            />
          </TabsContent>

          <TabsContent value="other">
            <TaskList
              tasks={filteredTasks.filter(task => task.category === "other")}
              getStaffMember={getStaffMember}
              getCategoryIcon={getCategoryIcon}
              getPriorityBadge={getPriorityBadge}
              getStatusBadge={getStatusBadge}
              formatDate={formatDate}
              setCurrentTask={setCurrentTask}
              setIsEditTaskDialogOpen={setIsEditTaskDialogOpen}
              setIsDeleteTaskDialogOpen={setIsDeleteTaskDialogOpen}
              handleMarkAsCompleted={handleMarkAsCompleted}
              handleMarkAsInProgress={handleMarkAsInProgress}
            />
          </TabsContent>
        </Tabs>
      </div>

      {/* Add Task Dialog */}
      <Dialog open={isAddTaskDialogOpen} onOpenChange={setIsAddTaskDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Add New Task</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="title">Task Title</Label>
              <Input
                id="title"
                value={newTask.title}
                onChange={(e) => setNewTask({ ...newTask, title: e.target.value })}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={newTask.description}
                onChange={(e) => setNewTask({ ...newTask, description: e.target.value })}
                rows={3}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="category">Category</Label>
                <Select
                  value={newTask.category}
                  onValueChange={(value: "cleaning" | "cutting" | "refilling" | "other") => setNewTask({ ...newTask, category: value })}
                >
                  <SelectTrigger id="category">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="cleaning">Cleaning</SelectItem>
                    <SelectItem value="cutting">Cutting</SelectItem>
                    <SelectItem value="refilling">Refilling</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="assignedTo">Assign To (Optional)</Label>
                <Select
                  value={newTask.assignedTo || "unassigned"}
                  onValueChange={(value) => setNewTask({ ...newTask, assignedTo: value === "unassigned" ? undefined : value })}
                >
                  <SelectTrigger id="assignedTo">
                    <SelectValue placeholder="Select staff (optional)" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="unassigned">Unassigned</SelectItem>
                    {staffData.map((staff) => (
                      <SelectItem key={staff.id} value={staff.id}>
                        {staff.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">Leave unassigned to allow any staff to complete this task</p>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="priority">Priority</Label>
                <Select
                  value={newTask.priority}
                  onValueChange={(value: "low" | "medium" | "high") => setNewTask({ ...newTask, priority: value })}
                >
                  <SelectTrigger id="priority">
                    <SelectValue placeholder="Select priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="dueDate">Due Date</Label>
                <Input
                  id="dueDate"
                  type="date"
                  value={newTask.dueDate}
                  onChange={(e) => setNewTask({ ...newTask, dueDate: e.target.value })}
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddTaskDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleAddTask}>Add Task</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Task Dialog */}
      <Dialog open={isEditTaskDialogOpen} onOpenChange={setIsEditTaskDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit Task</DialogTitle>
          </DialogHeader>
          {currentTask && (
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="edit-title">Task Title</Label>
                <Input
                  id="edit-title"
                  value={currentTask.title}
                  onChange={(e) => setCurrentTask({ ...currentTask, title: e.target.value })}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-description">Description</Label>
                <Textarea
                  id="edit-description"
                  value={currentTask.description}
                  onChange={(e) => setCurrentTask({ ...currentTask, description: e.target.value })}
                  rows={3}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="edit-category">Category</Label>
                  <Select
                    value={currentTask.category}
                    onValueChange={(value: "cleaning" | "cutting" | "refilling" | "other") => setCurrentTask({ ...currentTask, category: value })}
                  >
                    <SelectTrigger id="edit-category">
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="cleaning">Cleaning</SelectItem>
                      <SelectItem value="cutting">Cutting</SelectItem>
                      <SelectItem value="refilling">Refilling</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="edit-assignedTo">Assign To (Optional)</Label>
                  <Select
                    value={currentTask.assignedTo || "unassigned"}
                    onValueChange={(value) => setCurrentTask({ ...currentTask, assignedTo: value === "unassigned" ? undefined : value })}
                  >
                    <SelectTrigger id="edit-assignedTo">
                      <SelectValue placeholder="Select staff (optional)" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="unassigned">Unassigned</SelectItem>
                      {staffData.map((staff) => (
                        <SelectItem key={staff.id} value={staff.id}>
                          {staff.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground">Leave unassigned to allow any staff to complete this task</p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="edit-priority">Priority</Label>
                  <Select
                    value={currentTask.priority}
                    onValueChange={(value: "low" | "medium" | "high") => setCurrentTask({ ...currentTask, priority: value })}
                  >
                    <SelectTrigger id="edit-priority">
                      <SelectValue placeholder="Select priority" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="edit-dueDate">Due Date</Label>
                  <Input
                    id="edit-dueDate"
                    type="date"
                    value={currentTask.dueDate}
                    onChange={(e) => setCurrentTask({ ...currentTask, dueDate: e.target.value })}
                  />
                </div>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-status">Status</Label>
                <Select
                  value={currentTask.status}
                  onValueChange={(value: "pending" | "in-progress" | "completed") => setCurrentTask({ ...currentTask, status: value })}
                >
                  <SelectTrigger id="edit-status">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="in-progress">In Progress</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Task Activity Information */}
              <div className="mt-4 p-3 bg-muted rounded-md space-y-2">
                <h4 className="text-sm font-medium">Task Activity</h4>

                <div className="text-xs text-muted-foreground">
                  <span className="font-medium">Created:</span> {formatDate(currentTask.createdAt)}
                </div>

                {currentTask.startedAt && currentTask.assignedTo && (
                  <div className="text-xs text-muted-foreground">
                    <span className="font-medium">Started by:</span> {getStaffMember(currentTask.assignedTo)?.name || "Unknown"} on {formatDate(currentTask.startedAt)} at {new Date(currentTask.startedAt).toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' })}
                  </div>
                )}

                {currentTask.status === "completed" && currentTask.completedBy && currentTask.completedAt && (
                  <div className="text-xs text-muted-foreground">
                    <span className="font-medium">Completed by:</span> {getStaffMember(currentTask.completedBy)?.name || "Unknown"} on {formatDate(currentTask.completedAt)} at {new Date(currentTask.completedAt).toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' })}
                  </div>
                )}
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditTaskDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleEditTask}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteTaskDialogOpen} onOpenChange={setIsDeleteTaskDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>Are you sure you want to delete this task? This action cannot be undone.</p>
            {currentTask && (
              <div className="mt-2 p-3 bg-muted rounded-md space-y-2">
                <p className="font-medium">{currentTask.title}</p>
                <p className="text-sm text-muted-foreground">{currentTask.description}</p>

                <div className="mt-3 space-y-1">
                  <div className="text-xs text-muted-foreground">
                    <span className="font-medium">Category:</span> {currentTask.category.charAt(0).toUpperCase() + currentTask.category.slice(1)}
                  </div>

                  <div className="text-xs text-muted-foreground">
                    <span className="font-medium">Status:</span> {currentTask.status.charAt(0).toUpperCase() + currentTask.status.slice(1)}
                  </div>

                  <div className="text-xs text-muted-foreground">
                    <span className="font-medium">Created:</span> {formatDate(currentTask.createdAt)}
                  </div>

                  {currentTask.startedAt && currentTask.assignedTo && (
                    <div className="text-xs text-muted-foreground">
                      <span className="font-medium">Started by:</span> {getStaffMember(currentTask.assignedTo)?.name || "Unknown"} on {formatDate(currentTask.startedAt)}
                    </div>
                  )}

                  {currentTask.status === "completed" && currentTask.completedBy && currentTask.completedAt && (
                    <div className="text-xs text-muted-foreground">
                      <span className="font-medium">Completed by:</span> {getStaffMember(currentTask.completedBy)?.name || "Unknown"} on {formatDate(currentTask.completedAt)}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteTaskDialogOpen(false)}>Cancel</Button>
            <Button variant="destructive" onClick={handleDeleteTask}>Delete</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>


    </Layout>
  );
};

export default TaskManagement;
