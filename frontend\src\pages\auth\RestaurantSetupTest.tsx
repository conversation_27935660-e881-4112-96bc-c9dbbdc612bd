import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "@/components/ui/sonner";

const RestaurantSetupTest = () => {
  const navigate = useNavigate();
  const [restaurantName, setRestaurantName] = useState("");
  const [restaurantCode, setRestaurantCode] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!restaurantName || !restaurantCode) {
      toast.error("Please fill in all fields");
      return;
    }
    
    setIsSubmitting(true);
    
    // Simulate API call
    setTimeout(() => {
      toast.success("Restaurant setup completed successfully!");
      navigate("/restaurant-login");
      setIsSubmitting(false);
    }, 1500);
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-2xl">Restaurant Setup (Test)</CardTitle>
          <CardDescription>
            This is a simplified test version of the restaurant setup form
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Restaurant Name</Label>
              <Input 
                id="name" 
                placeholder="The Gourmet Kitchen" 
                value={restaurantName}
                onChange={(e) => setRestaurantName(e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="code">Restaurant Code</Label>
              <Input 
                id="code" 
                placeholder="GK001" 
                value={restaurantCode}
                onChange={(e) => setRestaurantCode(e.target.value)}
              />
              <p className="text-sm text-muted-foreground">
                A unique code for your restaurant (used for login)
              </p>
            </div>
            
            <Button type="submit" className="w-full" disabled={isSubmitting}>
              {isSubmitting ? "Setting up..." : "Complete Setup"}
            </Button>
          </form>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={() => navigate("/")}>
            Back to Login
          </Button>
          <Button variant="outline" onClick={() => navigate("/restaurant-setup")}>
            Full Setup Form
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default RestaurantSetupTest;
