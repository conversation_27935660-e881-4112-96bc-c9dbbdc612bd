import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import {
  BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer,
  PieChart, Pie, Cell, LineChart, Line, AreaChart, Area, ComposedChart
} from "recharts";
import {
  Calendar as CalendarIcon, Download, DollarSign, Users, ShoppingBag,
  TrendingUp, Clock, Utensils, AlertTriangle, BarChart2, Pie<PERSON><PERSON> as PieChartIcon
} from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { toast } from "@/components/ui/sonner";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { DateRange } from "react-day-picker";

const ReportsSection = () => {
  const [reportType, setReportType] = useState("sales");
  const [dateRange, setDateRange] = useState<{ from: Date; to: Date }>({
    from: new Date(new Date().setDate(new Date().getDate() - 30)),
    to: new Date(),
  });
  const [timeFrame, setTimeFrame] = useState<"daily" | "weekly" | "monthly">("daily");
  const [showUpsellItems, setShowUpsellItems] = useState(false);

  // Mock data for reports - Daily/Weekly/Monthly Revenue
  const dailyRevenueData = [
    { name: "Mon", revenue: 4000, lastPeriod: 3800 },
    { name: "Tue", revenue: 3000, lastPeriod: 3200 },
    { name: "Wed", revenue: 5000, lastPeriod: 4800 },
    { name: "Thu", revenue: 2780, lastPeriod: 2500 },
    { name: "Fri", revenue: 6890, lastPeriod: 6500 },
    { name: "Sat", revenue: 8390, lastPeriod: 7900 },
    { name: "Sun", revenue: 3490, lastPeriod: 3200 }
  ];

  const weeklyRevenueData = [
    { name: "Week 1", revenue: 24500, lastPeriod: 22000 },
    { name: "Week 2", revenue: 28700, lastPeriod: 26500 },
    { name: "Week 3", revenue: 31200, lastPeriod: 29800 },
    { name: "Week 4", revenue: 26800, lastPeriod: 27500 }
  ];

  const monthlyRevenueData = [
    { name: "Jan", revenue: 105000, lastPeriod: 98000 },
    { name: "Feb", revenue: 95000, lastPeriod: 92000 },
    { name: "Mar", revenue: 118000, lastPeriod: 110000 },
    { name: "Apr", revenue: 112000, lastPeriod: 105000 },
    { name: "May", revenue: 125000, lastPeriod: 115000 },
    { name: "Jun", revenue: 132000, lastPeriod: 122000 }
  ];

  // Top and Least Selling Items
  const topSellingItems = [
    { name: "Grilled Salmon", orders: 78, revenue: 1560 },
    { name: "Margherita Pizza", orders: 65, revenue: 1105 },
    { name: "Chocolate Lava Cake", orders: 52, revenue: 520 },
    { name: "Beef Burger", orders: 48, revenue: 720 },
    { name: "Caesar Salad", orders: 45, revenue: 585 }
  ];

  const leastSellingItems = [
    { name: "Vegetable Soup", orders: 12, revenue: 120 },
    { name: "Mushroom Risotto", orders: 15, revenue: 225 },
    { name: "Seafood Paella", orders: 18, revenue: 450 },
    { name: "Eggplant Parmesan", orders: 20, revenue: 300 },
    { name: "Spinach Quiche", orders: 22, revenue: 264 }
  ];

  // Revenue by Category
  const categoryData = [
    { name: "Main Course", value: 45, revenue: 22500 },
    { name: "Appetizers", value: 20, revenue: 10000 },
    { name: "Desserts", value: 15, revenue: 7500 },
    { name: "Beverages", value: 20, revenue: 10000 }
  ];

  // Revenue by Channel
  const channelData = [
    { name: "Dine-in", value: 60, revenue: 30000 },
    { name: "Takeaway", value: 15, revenue: 7500 },
    { name: "Delivery", value: 20, revenue: 10000 },
    { name: "Online", value: 5, revenue: 2500 }
  ];

  // Staff Sales Performance
  const staffSalesData = [
    { name: "Michael", sales: 12500, tables: 48, rating: 4.8, upsells: 32 },
    { name: "Jennifer", sales: 9800, tables: 38, rating: 4.5, upsells: 24 },
    { name: "David", sales: 7500, tables: 30, rating: 3.9, upsells: 15 },
    { name: "Maria", sales: 11000, tables: 42, rating: 4.6, upsells: 28 },
    { name: "Robert", sales: 13200, tables: 50, rating: 4.9, upsells: 35 }
  ];

  // Customer Metrics
  const avgSpendData = [
    { name: "Breakfast (7-11)", spend: 15.50, customers: 120 },
    { name: "Lunch (11-3)", spend: 22.75, customers: 250 },
    { name: "Afternoon (3-6)", spend: 18.25, customers: 85 },
    { name: "Dinner (6-10)", spend: 35.80, customers: 220 },
    { name: "Late Night (10+)", spend: 28.50, customers: 65 }
  ];

  // Table Turnover Rate
  const tableTurnoverData = [
    { name: "Mon", turnover: 42, utilization: 78 },
    { name: "Tue", turnover: 45, utilization: 72 },
    { name: "Wed", turnover: 38, utilization: 85 },
    { name: "Thu", turnover: 40, utilization: 80 },
    { name: "Fri", turnover: 35, utilization: 92 },
    { name: "Sat", turnover: 32, utilization: 95 },
    { name: "Sun", turnover: 36, utilization: 88 }
  ];

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  // Get revenue data based on selected timeframe
  const getRevenueData = () => {
    switch (timeFrame) {
      case "daily":
        return dailyRevenueData;
      case "weekly":
        return weeklyRevenueData;
      case "monthly":
        return monthlyRevenueData;
      default:
        return dailyRevenueData;
    }
  };

  const handleExportReport = () => {
    toast.success("Report exported successfully");
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <Select defaultValue="sales" onValueChange={setReportType}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Report Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="sales">Revenue Reports</SelectItem>
              <SelectItem value="items">Item Performance</SelectItem>
              <SelectItem value="categories">Revenue by Category</SelectItem>
              <SelectItem value="channels">Revenue by Channel</SelectItem>
              <SelectItem value="staff">Staff Performance</SelectItem>
              <SelectItem value="customers">Customer Metrics</SelectItem>
              <SelectItem value="tables">Table Turnover</SelectItem>
              <SelectItem value="inventory">Inventory & Waste</SelectItem>
              <SelectItem value="financial">Financial Performance</SelectItem>
            </SelectContent>
          </Select>

          {reportType === "sales" && (
            <Select defaultValue="daily" onValueChange={(value) => setTimeFrame(value as "daily" | "weekly" | "monthly")}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Time Period" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="daily">Daily</SelectItem>
                <SelectItem value="weekly">Weekly</SelectItem>
                <SelectItem value="monthly">Monthly</SelectItem>
              </SelectContent>
            </Select>
          )}

          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "w-[280px] justify-start text-left font-normal",
                  !dateRange && "text-muted-foreground"
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {dateRange?.from ? (
                  dateRange.to ? (
                    <>
                      {format(dateRange.from, "LLL dd, y")} - {format(dateRange.to, "LLL dd, y")}
                    </>
                  ) : (
                    format(dateRange.from, "LLL dd, y")
                  )
                ) : (
                  <span>Pick a date range</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                initialFocus
                mode="range"
                defaultMonth={dateRange?.from}
                selected={dateRange}
                onSelect={(newDateRange: DateRange | undefined) => {
                  if (newDateRange?.from && newDateRange?.to) {
                    setDateRange({ from: newDateRange.from, to: newDateRange.to });
                  }
                }}
                numberOfMonths={2}
              />
            </PopoverContent>
          </Popover>
        </div>

        <div className="flex gap-2">
          {reportType === "staff" && (
            <div className="flex items-center space-x-2">
              <Switch
                id="upsell-mode"
                checked={showUpsellItems}
                onCheckedChange={setShowUpsellItems}
              />
              <Label htmlFor="upsell-mode">Show Upselling</Label>
            </div>
          )}
          <Button onClick={handleExportReport}>
            <Download className="mr-2 h-4 w-4" /> Export Report
          </Button>
        </div>
      </div>

      <Tabs value={reportType} onValueChange={setReportType}>
        <TabsList className="mb-4">
          <TabsTrigger value="sales">Revenue</TabsTrigger>
          <TabsTrigger value="items">Items</TabsTrigger>
          <TabsTrigger value="categories">Categories</TabsTrigger>
          <TabsTrigger value="channels">Channels</TabsTrigger>
          <TabsTrigger value="staff">Staff</TabsTrigger>
          <TabsTrigger value="customers">Customers</TabsTrigger>
          <TabsTrigger value="tables">Tables</TabsTrigger>
          <TabsTrigger value="inventory">Inventory</TabsTrigger>
          <TabsTrigger value="financial">Financial</TabsTrigger>
        </TabsList>

        <TabsContent value="sales" className="space-y-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="text-lg font-medium">
                {timeFrame === "daily" ? "Daily" : timeFrame === "weekly" ? "Weekly" : "Monthly"} Revenue
              </CardTitle>
              <Badge variant="outline" className="ml-2">
                {timeFrame === "daily" ? "Last 7 days" : timeFrame === "weekly" ? "Last 4 weeks" : "Last 6 months"}
              </Badge>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={getRevenueData()}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis tickFormatter={(value) => `£${value.toLocaleString()}`} />
                    <Tooltip formatter={(value) => `£${value.toLocaleString()}`} />
                    <Legend />
                    <Bar dataKey="revenue" fill="#3b82f6" name="Current Period" />
                    <Bar dataKey="lastPeriod" fill="#93c5fd" name="Previous Period" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-medium">Revenue Trends</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={monthlyRevenueData}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis tickFormatter={(value) => `£${(value/1000).toFixed(0)}k`} />
                      <Tooltip formatter={(value) => `£${value.toLocaleString()}`} />
                      <Legend />
                      <Line type="monotone" dataKey="revenue" stroke="#3b82f6" name="Revenue" strokeWidth={2} />
                      <Line type="monotone" dataKey="lastPeriod" stroke="#93c5fd" name="Previous Year" strokeWidth={2} strokeDasharray="5 5" />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-medium">Revenue Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-muted p-4 rounded-lg">
                      <div className="text-sm text-muted-foreground">Total Revenue</div>
                      <div className="text-2xl font-bold">£132,000</div>
                      <div className="text-xs text-green-600 mt-1">↑ 8.2% vs last period</div>
                    </div>
                    <div className="bg-muted p-4 rounded-lg">
                      <div className="text-sm text-muted-foreground">Average Daily</div>
                      <div className="text-2xl font-bold">£4,400</div>
                      <div className="text-xs text-green-600 mt-1">↑ 5.7% vs last period</div>
                    </div>
                    <div className="bg-muted p-4 rounded-lg">
                      <div className="text-sm text-muted-foreground">Orders</div>
                      <div className="text-2xl font-bold">1,842</div>
                      <div className="text-xs text-green-600 mt-1">↑ 3.2% vs last period</div>
                    </div>
                    <div className="bg-muted p-4 rounded-lg">
                      <div className="text-sm text-muted-foreground">Avg. Order Value</div>
                      <div className="text-2xl font-bold">£71.66</div>
                      <div className="text-xs text-green-600 mt-1">↑ 4.8% vs last period</div>
                    </div>
                  </div>

                  <div className="pt-4">
                    <div className="text-sm font-medium mb-2">Revenue Breakdown</div>
                    <ul className="space-y-2">
                      <li className="flex justify-between">
                        <span>Food</span>
                        <span className="font-medium">£92,400 (70%)</span>
                      </li>
                      <li className="flex justify-between">
                        <span>Beverages</span>
                        <span className="font-medium">£26,400 (20%)</span>
                      </li>
                      <li className="flex justify-between">
                        <span>Other</span>
                        <span className="font-medium">£13,200 (10%)</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="items" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-medium">Top-Selling Items</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={topSellingItems}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                      layout="vertical"
                    >
                      <CartesianGrid strokeDasharray="3 3" horizontal={true} vertical={false} />
                      <XAxis type="number" />
                      <YAxis type="category" dataKey="name" width={100} />
                      <Tooltip formatter={(value) => value} />
                      <Legend />
                      <Bar dataKey="orders" fill="#3b82f6" name="Orders" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
                <div className="mt-4">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-2">Item</th>
                        <th className="text-right py-2">Orders</th>
                        <th className="text-right py-2">Revenue</th>
                      </tr>
                    </thead>
                    <tbody>
                      {topSellingItems.map((item, index) => (
                        <tr key={index} className="border-b">
                          <td className="py-2">{item.name}</td>
                          <td className="text-right py-2">{item.orders}</td>
                          <td className="text-right py-2">£{item.revenue.toLocaleString()}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-medium">Least-Selling Items</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={leastSellingItems}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                      layout="vertical"
                    >
                      <CartesianGrid strokeDasharray="3 3" horizontal={true} vertical={false} />
                      <XAxis type="number" />
                      <YAxis type="category" dataKey="name" width={100} />
                      <Tooltip formatter={(value) => value} />
                      <Legend />
                      <Bar dataKey="orders" fill="#ef4444" name="Orders" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
                <div className="mt-4">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-2">Item</th>
                        <th className="text-right py-2">Orders</th>
                        <th className="text-right py-2">Revenue</th>
                      </tr>
                    </thead>
                    <tbody>
                      {leastSellingItems.map((item, index) => (
                        <tr key={index} className="border-b">
                          <td className="py-2">{item.name}</td>
                          <td className="text-right py-2">{item.orders}</td>
                          <td className="text-right py-2">£{item.revenue.toLocaleString()}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="categories" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-medium">Revenue by Category</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={categoryData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, value }) => `${name}: ${value}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {categoryData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
                <div className="space-y-4">
                  {categoryData.map((category, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                      <div className="flex items-center gap-3">
                        <div
                          className="w-4 h-4 rounded-full"
                          style={{ backgroundColor: COLORS[index % COLORS.length] }}
                        />
                        <span className="font-medium">{category.name}</span>
                      </div>
                      <div className="text-right">
                        <div className="font-bold">£{category.revenue.toLocaleString()}</div>
                        <div className="text-sm text-muted-foreground">{category.value}%</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="channels" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-medium">Revenue by Channel</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={channelData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis tickFormatter={(value) => `£${(value/1000).toFixed(0)}k`} />
                      <Tooltip formatter={(value) => `£${value.toLocaleString()}`} />
                      <Bar dataKey="revenue" fill="#3b82f6" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
                <div className="space-y-4">
                  {channelData.map((channel, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                      <span className="font-medium">{channel.name}</span>
                      <div className="text-right">
                        <div className="font-bold">£{channel.revenue.toLocaleString()}</div>
                        <div className="text-sm text-muted-foreground">{channel.value}%</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="staff" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-medium">Staff Performance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={staffSalesData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis tickFormatter={(value) => `£${(value/1000).toFixed(0)}k`} />
                    <Tooltip formatter={(value) => `£${value.toLocaleString()}`} />
                    <Bar dataKey="sales" fill="#3b82f6" name="Sales" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
              <div className="mt-4">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-2">Staff Member</th>
                      <th className="text-right py-2">Sales</th>
                      <th className="text-right py-2">Tables</th>
                      <th className="text-right py-2">Rating</th>
                      <th className="text-right py-2">Upsells</th>
                    </tr>
                  </thead>
                  <tbody>
                    {staffSalesData.map((staff, index) => (
                      <tr key={index} className="border-b">
                        <td className="py-2">{staff.name}</td>
                        <td className="text-right py-2">£{staff.sales.toLocaleString()}</td>
                        <td className="text-right py-2">{staff.tables}</td>
                        <td className="text-right py-2">{staff.rating}</td>
                        <td className="text-right py-2">{staff.upsells}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="customers" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-medium">Customer Spending by Time</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={avgSpendData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis tickFormatter={(value) => `£${value}`} />
                    <Tooltip formatter={(value) => `£${value}`} />
                    <Bar dataKey="spend" fill="#3b82f6" name="Avg Spend" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
              <div className="mt-4">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-2">Time Period</th>
                      <th className="text-right py-2">Avg Spend</th>
                      <th className="text-right py-2">Customers</th>
                    </tr>
                  </thead>
                  <tbody>
                    {avgSpendData.map((period, index) => (
                      <tr key={index} className="border-b">
                        <td className="py-2">{period.name}</td>
                        <td className="text-right py-2">£{period.spend.toFixed(2)}</td>
                        <td className="text-right py-2">{period.customers}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tables" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-medium">Table Turnover & Utilization</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <ComposedChart data={tableTurnoverData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip />
                    <Legend />
                    <Bar yAxisId="left" dataKey="turnover" fill="#3b82f6" name="Turnover (min)" />
                    <Line yAxisId="right" type="monotone" dataKey="utilization" stroke="#f59e0b" name="Utilization %" />
                  </ComposedChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="inventory" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-medium">Inventory Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="text-sm text-muted-foreground">
                  Current stock levels and waste percentages by category
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {[
                    { name: "Vegetables", stock: 35, reorder: 10, waste: 3.2 },
                    { name: "Meat", stock: 28, reorder: 15, waste: 2.5 },
                    { name: "Dairy", stock: 42, reorder: 20, waste: 4.8 },
                    { name: "Grains", stock: 50, reorder: 25, waste: 1.5 },
                    { name: "Beverages", stock: 38, reorder: 15, waste: 0.8 }
                  ].map((item, index) => (
                    <div key={index} className="p-4 bg-muted rounded-lg">
                      <div className="font-medium">{item.name}</div>
                      <div className="text-sm text-muted-foreground mt-1">
                        Stock: {item.stock}% | Reorder: {item.reorder}%
                      </div>
                      <div className="text-sm mt-1">
                        <span className={`${item.waste > 3 ? 'text-red-600' : 'text-green-600'}`}>
                          Waste: {item.waste}%
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="financial" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-medium">Financial Performance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="p-4 bg-muted rounded-lg">
                  <div className="text-sm text-muted-foreground">Revenue</div>
                  <div className="text-2xl font-bold">£132,000</div>
                  <div className="text-xs text-green-600 mt-1">↑ 8.2%</div>
                </div>
                <div className="p-4 bg-muted rounded-lg">
                  <div className="text-sm text-muted-foreground">COGS</div>
                  <div className="text-2xl font-bold">£38,280</div>
                  <div className="text-xs text-muted-foreground mt-1">29%</div>
                </div>
                <div className="p-4 bg-muted rounded-lg">
                  <div className="text-sm text-muted-foreground">Labor</div>
                  <div className="text-2xl font-bold">£31,680</div>
                  <div className="text-xs text-muted-foreground mt-1">24%</div>
                </div>
                <div className="p-4 bg-muted rounded-lg">
                  <div className="text-sm text-muted-foreground">Profit</div>
                  <div className="text-2xl font-bold">£34,320</div>
                  <div className="text-xs text-green-600 mt-1">26%</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ReportsSection;
