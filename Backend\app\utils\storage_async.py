"""
Async wrapper for storage operations that maintains compatibility with existing code.
This module provides both async and sync interfaces to the database.
"""

import asyncio
from typing import Dict, Any, List, Union, Callable, Optional
from functools import wraps
import logging

from app.services.database_service import db_service
from app.utils.storage import storage as memory_storage, save_storage as save_memory_storage

logger = logging.getLogger(__name__)

# Flag to determine whether to use database or memory storage
USE_DATABASE = True

def async_to_sync(async_func):
    """Decorator to convert async function to sync for backward compatibility"""
    @wraps(async_func)
    def wrapper(*args, **kwargs):
        try:
            # Try to get existing event loop
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If loop is running, create a new task
                return asyncio.create_task(async_func(*args, **kwargs))
            else:
                # If no loop is running, run the coroutine
                return loop.run_until_complete(async_func(*args, **kwargs))
        except RuntimeError:
            # No event loop exists, create a new one
            return asyncio.run(async_func(*args, **kwargs))
    return wrapper

class StorageService:
    """Unified storage service that can use either database or memory storage"""
    
    def __init__(self):
        self.use_database = USE_DATABASE
    
    async def get_all_async(self, collection: str) -> List[Dict[str, Any]]:
        """Get all items from a collection (async)"""
        if self.use_database:
            try:
                result = await db_service.get_all(collection)
                logger.info(f"Successfully retrieved {len(result)} items from {collection} via database")
                return result
            except Exception as e:
                logger.error(f"Database error in get_all({collection}): {e}")
                # Fallback to memory storage
                fallback_result = memory_storage.get(collection, [])
                logger.warning(f"Falling back to memory storage for {collection}, found {len(fallback_result)} items")
                return fallback_result
        else:
            return memory_storage.get(collection, [])
    
    async def get_by_id_async(self, collection: str, item_id: str) -> Optional[Dict[str, Any]]:
        """Get an item by ID from a collection (async)"""
        if self.use_database:
            try:
                return await db_service.get_by_id(collection, item_id)
            except Exception as e:
                logger.error(f"Database error in get_by_id({collection}, {item_id}): {e}")
                # Fallback to memory storage
                items = memory_storage.get(collection, [])
                for item in items:
                    if item.get("id") == item_id:
                        return item
                return None
        else:
            items = memory_storage.get(collection, [])
            for item in items:
                if item.get("id") == item_id:
                    return item
            return None
    
    async def create_async(self, collection: str, item: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new item in a collection (async)"""
        if self.use_database:
            try:
                return await db_service.create(collection, item)
            except Exception as e:
                logger.error(f"Database error in create({collection}): {e}")
                # Fallback to memory storage
                return self._create_memory(collection, item)
        else:
            return self._create_memory(collection, item)
    
    async def update_async(self, collection: str, item_id: str, updates: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Update an item in a collection (async)"""
        if self.use_database:
            try:
                return await db_service.update(collection, item_id, updates)
            except Exception as e:
                logger.error(f"Database error in update({collection}, {item_id}): {e}")
                # Fallback to memory storage
                return self._update_memory(collection, item_id, updates)
        else:
            return self._update_memory(collection, item_id, updates)
    
    async def delete_async(self, collection: str, item_id: str) -> bool:
        """Delete an item from a collection (async)"""
        if self.use_database:
            try:
                return await db_service.delete(collection, item_id)
            except Exception as e:
                logger.error(f"Database error in delete({collection}, {item_id}): {e}")
                # Fallback to memory storage
                return self._delete_memory(collection, item_id)
        else:
            return self._delete_memory(collection, item_id)
    
    async def query_async(self, collection: str, filters: Union[Dict[str, Any], Callable]) -> List[Dict[str, Any]]:
        """Query items from a collection with filters (async)"""
        if self.use_database:
            try:
                return await db_service.query(collection, filters)
            except Exception as e:
                logger.error(f"Database error in query({collection}): {e}")
                # Fallback to memory storage
                return self._query_memory(collection, filters)
        else:
            return self._query_memory(collection, filters)
    
    # Memory storage fallback methods
    def _create_memory(self, collection: str, item: Dict[str, Any]) -> Dict[str, Any]:
        """Create item in memory storage"""
        from datetime import datetime
        
        items = memory_storage.get(collection, [])
        
        # Generate ID if not provided
        if "id" not in item:
            item["id"] = f"{collection[:-1]}{len(items) + 1}"
        
        # Add timestamps
        if "created_at" not in item:
            item["created_at"] = datetime.now().isoformat()
        if "updated_at" not in item:
            item["updated_at"] = datetime.now().isoformat()
        
        items.append(item)
        memory_storage[collection] = items
        save_memory_storage()
        return item
    
    def _update_memory(self, collection: str, item_id: str, updates: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Update item in memory storage"""
        from datetime import datetime
        
        items = memory_storage.get(collection, [])
        for i, item in enumerate(items):
            if item.get("id") == item_id:
                updates["updated_at"] = datetime.now().isoformat()
                items[i] = {**item, **updates}
                memory_storage[collection] = items
                save_memory_storage()
                return items[i]
        return None
    
    def _delete_memory(self, collection: str, item_id: str) -> bool:
        """Delete item from memory storage"""
        items = memory_storage.get(collection, [])
        for i, item in enumerate(items):
            if item.get("id") == item_id:
                items.pop(i)
                memory_storage[collection] = items
                save_memory_storage()
                return True
        return False
    
    def _query_memory(self, collection: str, filters: Union[Dict[str, Any], Callable]) -> List[Dict[str, Any]]:
        """Query items from memory storage"""
        items = memory_storage.get(collection, [])
        results = []
        
        for item in items:
            if callable(filters):
                if filters(item):
                    results.append(item)
            else:
                match = True
                for key, value in filters.items():
                    if key not in item or item[key] != value:
                        match = False
                        break
                if match:
                    results.append(item)
        
        return results
    
    # Sync wrapper methods for backward compatibility
    @async_to_sync
    async def get_all(self, collection: str) -> List[Dict[str, Any]]:
        """Get all items from a collection (sync wrapper)"""
        return await self.get_all_async(collection)
    
    @async_to_sync
    async def get_by_id(self, collection: str, item_id: str) -> Optional[Dict[str, Any]]:
        """Get an item by ID from a collection (sync wrapper)"""
        return await self.get_by_id_async(collection, item_id)
    
    @async_to_sync
    async def create(self, collection: str, item: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new item in a collection (sync wrapper)"""
        return await self.create_async(collection, item)
    
    @async_to_sync
    async def update(self, collection: str, item_id: str, updates: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Update an item in a collection (sync wrapper)"""
        return await self.update_async(collection, item_id, updates)
    
    @async_to_sync
    async def delete(self, collection: str, item_id: str) -> bool:
        """Delete an item from a collection (sync wrapper)"""
        return await self.delete_async(collection, item_id)
    
    @async_to_sync
    async def query(self, collection: str, filters: Union[Dict[str, Any], Callable]) -> List[Dict[str, Any]]:
        """Query items from a collection with filters (sync wrapper)"""
        return await self.query_async(collection, filters)

# Global storage service instance
storage_service = StorageService()

# Export functions for backward compatibility
get_all = storage_service.get_all
get_by_id = storage_service.get_by_id
create = storage_service.create
update = storage_service.update
delete = storage_service.delete
query = storage_service.query

# Export async versions
get_all_async = storage_service.get_all_async
get_by_id_async = storage_service.get_by_id_async
create_async = storage_service.create_async
update_async = storage_service.update_async
delete_async = storage_service.delete_async
query_async = storage_service.query_async
