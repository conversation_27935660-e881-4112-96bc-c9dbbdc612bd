"""
Inventory models for RestroManage database.
Corresponds to app/models/inventory.py Pydantic models.
"""

from sqlalchemy import Column, String, Boolean, Float, Text, JSON, ForeignKey, Integer, DateTime
from sqlalchemy.orm import relationship
from .base import BaseModel, TimestampMixin, StatusMixin, SoftDeleteMixin

class InventoryItem(BaseModel, TimestampMixin, StatusMixin, SoftDeleteMixin):
    """
    Inventory item model for stock management.
    Corresponds to InventoryItem Pydantic model in app/models/inventory.py
    """
    __tablename__ = "inventory_items"
    
    # Restaurant association
    restaurant_id = Column(String(36), ForeignKey("restaurants.id"), nullable=False, index=True)
    
    # Basic information
    name = Column(String(255), nullable=False, index=True)
    sku = Column(String(100), nullable=True, unique=True, index=True)
    barcode = Column(String(100), nullable=True, unique=True, index=True)
    
    # Quantity and units
    quantity = Column(Float, nullable=False, default=0.0)
    unit = Column(String(20), nullable=False)  # kg, lbs, pieces, liters, etc.
    reorder_level = Column(Float, nullable=False)
    maximum_stock = Column(Float, nullable=True)
    
    # Pricing
    price_per_unit = Column(Float, nullable=False)
    cost_per_unit = Column(Float, nullable=True)
    last_purchase_price = Column(Float, nullable=True)
    
    # Categorization
    category = Column(String(100), nullable=True, index=True)
    subcategory = Column(String(100), nullable=True)
    brand = Column(String(100), nullable=True)
    
    # Supplier information
    supplier = Column(String(255), nullable=True, index=True)
    supplier_id = Column(String(36), nullable=True, index=True)
    supplier_product_code = Column(String(100), nullable=True)
    
    # Storage and handling
    storage_location = Column(String(100), nullable=True)
    storage_temperature = Column(String(50), nullable=True)  # frozen, refrigerated, room_temp
    storage_requirements = Column(Text, nullable=True)
    
    # Expiration and freshness
    expiration_date = Column(DateTime(timezone=True), nullable=True, index=True)
    shelf_life_days = Column(Integer, nullable=True)
    batch_number = Column(String(100), nullable=True)
    
    # Allergens and dietary information
    allergens = Column(JSON, nullable=True)  # List of allergens
    is_organic = Column(Boolean, default=False, nullable=False)
    is_gluten_free = Column(Boolean, default=False, nullable=False)
    is_vegan = Column(Boolean, default=False, nullable=False)
    is_halal = Column(Boolean, default=False, nullable=False)
    is_kosher = Column(Boolean, default=False, nullable=False)
    
    # Tracking and alerts
    low_stock_alert = Column(Boolean, default=True, nullable=False)
    expiration_alert_days = Column(Integer, default=7, nullable=False)
    auto_reorder = Column(Boolean, default=False, nullable=False)
    auto_reorder_quantity = Column(Float, nullable=True)
    
    # Usage tracking
    last_restocked = Column(DateTime(timezone=True), nullable=True)
    last_used = Column(DateTime(timezone=True), nullable=True)
    average_daily_usage = Column(Float, nullable=True)
    
    # Quality and condition
    quality_grade = Column(String(10), nullable=True)  # A, B, C
    condition = Column(String(20), default="good", nullable=False)
    # Conditions: excellent, good, fair, poor, expired
    
    # Menu item associations
    used_in_items = Column(JSON, nullable=True)  # List of menu item IDs
    
    # Notes and descriptions
    description = Column(Text, nullable=True)
    handling_instructions = Column(Text, nullable=True)
    notes = Column(Text, nullable=True)
    
    # Relationships
    restaurant = relationship("Restaurant", back_populates="inventory_items")
    transactions = relationship("InventoryTransaction", back_populates="inventory_item", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<InventoryItem(id={self.id}, name={self.name}, quantity={self.quantity} {self.unit})>"

class InventoryTransaction(BaseModel, TimestampMixin):
    """
    Inventory transaction model for tracking stock movements.
    """
    __tablename__ = "inventory_transactions"
    
    # Inventory item association
    inventory_item_id = Column(String(36), ForeignKey("inventory_items.id"), nullable=False, index=True)
    restaurant_id = Column(String(36), ForeignKey("restaurants.id"), nullable=False, index=True)
    
    # Transaction details
    transaction_type = Column(String(20), nullable=False, index=True)
    # Types: purchase, usage, waste, adjustment, transfer, return
    
    # Quantity changes
    quantity_before = Column(Float, nullable=False)
    quantity_change = Column(Float, nullable=False)  # Positive for additions, negative for usage
    quantity_after = Column(Float, nullable=False)
    unit = Column(String(20), nullable=False)
    
    # Financial information
    unit_cost = Column(Float, nullable=True)
    total_cost = Column(Float, nullable=True)
    
    # Reference information
    reference_type = Column(String(20), nullable=True)  # order, purchase, adjustment
    reference_id = Column(String(36), nullable=True, index=True)  # ID of related record
    
    # Purchase/supplier information
    supplier = Column(String(255), nullable=True)
    purchase_order_number = Column(String(100), nullable=True)
    invoice_number = Column(String(100), nullable=True)
    delivery_date = Column(DateTime(timezone=True), nullable=True)
    
    # Batch and expiration tracking
    batch_number = Column(String(100), nullable=True)
    expiration_date = Column(DateTime(timezone=True), nullable=True)
    
    # Staff and approval
    performed_by = Column(String(36), ForeignKey("users.id"), nullable=True, index=True)
    approved_by = Column(String(36), ForeignKey("users.id"), nullable=True, index=True)
    
    # Reason and notes
    reason = Column(String(255), nullable=True)
    notes = Column(Text, nullable=True)
    
    # Quality information
    quality_grade = Column(String(10), nullable=True)
    condition_notes = Column(Text, nullable=True)
    
    # Waste tracking
    waste_reason = Column(String(100), nullable=True)  # expired, damaged, spoiled, etc.
    waste_category = Column(String(50), nullable=True)  # food_waste, packaging, etc.
    
    # Relationships
    inventory_item = relationship("InventoryItem", back_populates="transactions")
    restaurant = relationship("Restaurant")
    performed_by_user = relationship("User", foreign_keys=[performed_by])
    approved_by_user = relationship("User", foreign_keys=[approved_by])
    
    def __repr__(self):
        return f"<InventoryTransaction(id={self.id}, type={self.transaction_type}, quantity_change={self.quantity_change})>"
