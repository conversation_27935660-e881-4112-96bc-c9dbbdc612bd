from typing import List, Optional, Dict, Any
from datetime import datetime
from app.models.discounts import (
    PromoCode, DiscountType, DiscountScope, DiscountValidationResult,
    DiscountApplication, PromoCodeUsage
)
from app.models.orders import Order
from app.utils.storage import get_all, get_by_id, create, update, query

class DiscountService:
    """Service for handling discount calculations and promo code validation"""
    
    @staticmethod
    def validate_promo_code(
        code: str,
        order_total: float,
        order_items: List[dict],
        customer_id: Optional[str] = None
    ) -> DiscountValidationResult:
        """Validate a promo code and calculate discount amount"""
        
        # Find promo code
        promo_codes = query("promo_codes", lambda x: x.get("code") == code.upper())
        if not promo_codes:
            return DiscountValidationResult(
                is_valid=False,
                error_message="Invalid promo code"
            )
        
        promo_code = promo_codes[0]
        now = datetime.now()
        
        # Check if promo code is active
        if not promo_code.get("is_active", True):
            return DiscountValidationResult(
                is_valid=False,
                error_message="Promo code is not active"
            )
        
        # Check date validity
        start_date_str = promo_code["start_date"].replace("Z", "+00:00")
        end_date_str = promo_code["end_date"].replace("Z", "+00:00")
        start_date = datetime.fromisoformat(start_date_str).replace(tzinfo=None)
        end_date = datetime.fromisoformat(end_date_str).replace(tzinfo=None)

        if now < start_date:
            return DiscountValidationResult(
                is_valid=False,
                error_message="Promo code is not yet valid"
            )

        if now > end_date:
            return DiscountValidationResult(
                is_valid=False,
                error_message="Promo code has expired"
            )
        
        # Check usage limits
        usage_count = promo_code.get("usage_count", 0)
        usage_limit = promo_code.get("usage_limit")
        
        if usage_limit and usage_count >= usage_limit:
            return DiscountValidationResult(
                is_valid=False,
                error_message="Promo code usage limit exceeded"
            )
        
        # Check per-customer usage limit
        if customer_id and promo_code.get("usage_limit_per_customer"):
            customer_usage = len(query(
                "promo_code_usage",
                lambda x: x.get("promo_code_id") == promo_code["id"] and 
                         x.get("customer_id") == customer_id
            ))
            
            if customer_usage >= promo_code["usage_limit_per_customer"]:
                return DiscountValidationResult(
                    is_valid=False,
                    error_message="Customer usage limit exceeded for this promo code"
                )
        
        # Check minimum spend
        minimum_spend = promo_code.get("minimum_spend")
        if minimum_spend and order_total < minimum_spend:
            return DiscountValidationResult(
                is_valid=False,
                error_message=f"Minimum spend of £{minimum_spend:.2f} required"
            )
        
        # Calculate discount
        discount_result = DiscountService._calculate_discount(
            promo_code, order_total, order_items
        )
        
        return discount_result
    
    @staticmethod
    def _calculate_discount(
        promo_code: Dict[str, Any],
        order_total: float,
        order_items: List[dict]
    ) -> DiscountValidationResult:
        """Calculate the discount amount based on promo code rules"""
        
        discount_type = promo_code["discount_type"]
        discount_value = promo_code["discount_value"]
        scope = promo_code["scope"]
        
        applicable_items = []
        discount_amount = 0.0
        
        if scope == DiscountScope.ORDER_TOTAL:
            # Apply to entire order
            if discount_type == DiscountType.PERCENTAGE:
                discount_amount = order_total * (discount_value / 100)
            elif discount_type == DiscountType.FIXED_AMOUNT:
                discount_amount = min(discount_value, order_total)
            
        elif scope == DiscountScope.SPECIFIC_ITEMS:
            # Apply to specific items
            applicable_item_ids = promo_code.get("applicable_items", [])
            applicable_total = sum(
                item.subtotal for item in order_items 
                if item.menu_item_id in applicable_item_ids
            )
            
            if applicable_total > 0:
                if discount_type == DiscountType.PERCENTAGE:
                    discount_amount = applicable_total * (discount_value / 100)
                elif discount_type == DiscountType.FIXED_AMOUNT:
                    discount_amount = min(discount_value, applicable_total)
                
                applicable_items = [
                    item.menu_item_id for item in order_items 
                    if item.menu_item_id in applicable_item_ids
                ]
        
        elif scope == DiscountScope.CATEGORY:
            # Apply to specific categories
            applicable_categories = promo_code.get("applicable_categories", [])
            # Note: This would require menu item category lookup
            # For now, we'll implement basic logic
            applicable_total = order_total  # Simplified
            
            if discount_type == DiscountType.PERCENTAGE:
                discount_amount = applicable_total * (discount_value / 100)
            elif discount_type == DiscountType.FIXED_AMOUNT:
                discount_amount = min(discount_value, applicable_total)
        
        # Apply maximum discount limit
        maximum_discount = promo_code.get("maximum_discount")
        if maximum_discount:
            discount_amount = min(discount_amount, maximum_discount)
        
        # Ensure discount doesn't exceed order total
        discount_amount = min(discount_amount, order_total)
        
        return DiscountValidationResult(
            is_valid=True,
            discount_amount=discount_amount,
            applicable_items=applicable_items if applicable_items else None
        )
    
    @staticmethod
    def apply_discount_to_order(
        order_id: str,
        promo_code: str,
        customer_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Apply a discount to an order and update totals"""
        
        # Get order
        order = get_by_id("orders", order_id)
        if not order:
            raise ValueError("Order not found")
        
        # Validate promo code
        validation_result = DiscountService.validate_promo_code(
            promo_code, order["total"], order["items"], customer_id
        )
        
        if not validation_result.is_valid:
            raise ValueError(validation_result.error_message)
        
        # Get promo code details
        promo_codes = query("promo_codes", lambda x: x.get("code") == promo_code.upper())
        promo_code_obj = promo_codes[0]
        
        # Create discount application
        discount_application = DiscountApplication(
            promo_code_id=promo_code_obj["id"],
            promo_code=promo_code.upper(),
            discount_amount=validation_result.discount_amount,
            applied_to_items=validation_result.applicable_items
        )
        
        # Update order with discount
        applied_discounts = order.get("applied_discounts", [])
        applied_discounts.append(discount_application.model_dump())
        
        new_total = order["total"] - validation_result.discount_amount
        
        updated_order = update("orders", order_id, {
            "applied_discounts": applied_discounts,
            "discount_amount": order.get("discount_amount", 0) + validation_result.discount_amount,
            "total": new_total
        })
        
        # Record usage
        usage_record = PromoCodeUsage(
            id=f"usage_{datetime.now().timestamp()}",
            promo_code_id=promo_code_obj["id"],
            order_id=order_id,
            customer_id=customer_id,
            discount_amount=validation_result.discount_amount
        )
        
        create("promo_code_usage", usage_record.model_dump())
        
        # Update promo code usage count
        update("promo_codes", promo_code_obj["id"], {
            "usage_count": promo_code_obj.get("usage_count", 0) + 1
        })
        
        return {
            "success": True,
            "discount_amount": validation_result.discount_amount,
            "new_total": new_total,
            "order": updated_order
        }
    
    @staticmethod
    def remove_discount_from_order(order_id: str, promo_code: str) -> Dict[str, Any]:
        """Remove a discount from an order"""
        
        order = get_by_id("orders", order_id)
        if not order:
            raise ValueError("Order not found")
        
        applied_discounts = order.get("applied_discounts", [])
        discount_to_remove = None
        
        for discount in applied_discounts:
            if discount["promo_code"] == promo_code.upper():
                discount_to_remove = discount
                break
        
        if not discount_to_remove:
            raise ValueError("Discount not found on this order")
        
        # Remove discount from list
        applied_discounts.remove(discount_to_remove)
        
        # Recalculate totals
        new_discount_amount = order.get("discount_amount", 0) - discount_to_remove["discount_amount"]
        new_total = order["total"] + discount_to_remove["discount_amount"]
        
        updated_order = update("orders", order_id, {
            "applied_discounts": applied_discounts,
            "discount_amount": new_discount_amount,
            "total": new_total
        })
        
        return {
            "success": True,
            "removed_discount": discount_to_remove["discount_amount"],
            "new_total": new_total,
            "order": updated_order
        }
