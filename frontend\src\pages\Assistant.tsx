import { useState, useRef, useEffect } from "react";
import Layout from "@/components/layout/Layout";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, <PERSON><PERSON><PERSON><PERSON>, CardFooter, CardDescription } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Bot, Send, BarChart, ShoppingCart, Calendar, AlertTriangle, Clock, TrendingUp, CheckCircle, AlertCircle } from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { Separator } from "@/components/ui/separator";
import { toast } from "@/components/ui/sonner";
import { useAuth } from "@/contexts/AuthContext";
import aiAssistantService, { AIQueryResponse, AIStatus } from "@/services/aiAssistantService";
import logger from "@/utils/logger";

interface Message {
  id: string;
  role: "user" | "assistant";
  content: string;
  timestamp: Date;
  data?: any;
  provider?: string;
  error?: boolean;
}

const Assistant = () => {
  const { user } = useAuth();
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "welcome",
      role: "assistant",
      content: `Hello! I'm your restaurant management assistant for ${user?.restaurant_name || "your restaurant"}. How can I help you today?`,
      timestamp: new Date(),
    },
  ]);
  const [inputValue, setInputValue] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [aiStatus, setAiStatus] = useState<AIStatus | null>(null);
  const [isConfigured, setIsConfigured] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Focus input on load and check LLM configuration
  useEffect(() => {
    setTimeout(() => {
      inputRef.current?.focus();
    }, 100);

    // Check AI service configuration
    checkAIConfiguration();
  }, []);

  const checkAIConfiguration = async () => {
    try {
      const status = await aiAssistantService.getStatus();
      setAiStatus(status);
      setIsConfigured(status?.ai_enabled || false);

      if (status && !status.ai_enabled) {
        toast.info("AI assistant is in fallback mode. Configure Google API key for full functionality.");
      }
    } catch (error) {
      logger.error('Failed to check AI configuration', 'Assistant', { error: error.message });
      setIsConfigured(false);
    }
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: "user",
      content: inputValue,
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);
    const currentQuery = inputValue;
    setInputValue("");
    setIsLoading(true);

    try {
      logger.info('Sending AI query', 'Assistant', {
        query: currentQuery.substring(0, 50) + '...',
        restaurant_id: user?.restaurant_id
      });

      // Use the AI assistant service
      const response: AIQueryResponse = await aiAssistantService.processQuery(
        currentQuery,
        user?.restaurant_id || "1"
      );

      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: "assistant",
        content: response.response,
        timestamp: new Date(),
        provider: response.ai_enabled ? "Google AI" : "Fallback",
        error: !response.success
      };

      setMessages((prev) => [...prev, assistantMessage]);

      if (response.success) {
        logger.info('AI query successful', 'Assistant', {
          ai_enabled: response.ai_enabled
        });
      } else {
        logger.warn('AI query failed', 'Assistant', { error: response.error });
        toast.error("Assistant response failed. Please try again.");
      }
      
    } catch (error) {
      logger.error('Error querying AI assistant', 'Assistant', { error: error.message });

      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: "assistant",
        content: "I'm sorry, I encountered an error processing your request. Please try again later.",
        timestamp: new Date(),
        error: true
      };

      setMessages((prev) => [...prev, errorMessage]);
      toast.error("Failed to get assistant response. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const renderMessageContent = (message: Message) => {
    return (
      <div className="whitespace-pre-wrap">
        {message.content.split("\n").map((line, i) => (
          <div key={i}>{line}</div>
        ))}
        
        {message.data && message.data.topItems && (
          <div className="mt-2 space-y-1">
            <Badge variant="outline" className="mb-1">Top Items</Badge>
            {message.data.topItems.map((item: any, i: number) => (
              <div key={i} className="flex justify-between px-2 py-1 bg-muted/50 rounded-sm">
                <span>{item.name}</span>
                <Badge variant="secondary">{item.orders} orders</Badge>
              </div>
            ))}
          </div>
        )}
        
        {message.data && message.data.bottomItems && (
          <div className="mt-2 space-y-1">
            <Badge variant="outline" className="mb-1">Least Popular Items</Badge>
            {message.data.bottomItems.map((item: any, i: number) => (
              <div key={i} className="flex justify-between px-2 py-1 bg-muted/50 rounded-sm">
                <span>{item.name}</span>
                <Badge variant="secondary">{item.orders} orders</Badge>
              </div>
            ))}
          </div>
        )}
        
        {message.data && message.data.lowStockItems && (
          <div className="mt-2 space-y-1">
            <Badge variant="outline" className="mb-1">Low Stock Items</Badge>
            {message.data.lowStockItems.map((item: any, i: number) => (
              <div key={i} className="flex justify-between px-2 py-1 bg-muted/50 rounded-sm">
                <span>{item.name}</span>
                <Badge variant="secondary">{item.quantity} {item.unit}</Badge>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

  return (
    <Layout title="AI Assistant">
      <div className="container mx-auto py-6">
        <div className="flex flex-col lg:flex-row gap-6">
          <div className="lg:w-3/4">
            <Card className="h-[calc(100vh-12rem)]">
              <CardHeader className="pb-3">
                <div className="flex items-center">
                  <Avatar className="h-10 w-10 mr-3">
                    <AvatarFallback>AI</AvatarFallback>
                    <AvatarImage src="/ai-assistant.png" />
                  </Avatar>
                  <div className="flex-1">
                    <CardTitle className="text-xl">AI Assistant</CardTitle>
                    <CardDescription>
                      Powered by Google AI
                    </CardDescription>
                    {aiStatus && (
                      <div className="mt-1">
                        <Badge variant={isConfigured ? "default" : "secondary"} className="text-xs">
                          {isConfigured ? "Google AI" : "Fallback Mode"}
                        </Badge>
                      </div>
                    )}
                  </div>
                  {aiStatus && !isConfigured && (
                    <div className="flex items-center gap-1 text-amber-600">
                      <AlertCircle className="h-4 w-4" />
                      <span className="text-xs">Fallback Mode</span>
                    </div>
                  )}
                </div>
              </CardHeader>
              
              <Separator />
              
              <CardContent className="p-0 flex-1 flex flex-col h-[calc(100%-10rem)]">
                <ScrollArea className="flex-1 p-4">
                  <div className="space-y-4">
                    {messages.map((message) => (
                      <div
                        key={message.id}
                        className={`flex ${
                          message.role === "user" ? "justify-end" : "justify-start"
                        }`}
                      >
                        <div
                          className={`max-w-[85%] rounded-lg p-3 ${
                            message.role === "user"
                              ? "bg-primary text-primary-foreground"
                              : message.error
                              ? "bg-red-50 border border-red-200"
                              : "bg-gradient-to-r from-emerald-50 to-green-50 border border-emerald-200 text-emerald-800 shadow-sm"
                          }`}
                        >
                          {renderMessageContent(message)}
                          <div
                            className={`text-xs mt-1 flex items-center justify-between ${
                              message.role === "user"
                                ? "text-primary-foreground/70"
                                : "text-muted-foreground"
                            }`}
                          >
                            <span>
                              {message.timestamp.toLocaleTimeString([], {
                                hour: "2-digit",
                                minute: "2-digit",
                              })}
                            </span>
                            {message.role === "assistant" && message.provider && (
                              <div className="flex items-center gap-1">
                                {message.error ? (
                                  <AlertCircle className="h-3 w-3 text-red-500" />
                                ) : (
                                  <CheckCircle className="h-3 w-3 text-green-500" />
                                )}
                                <span className="text-xs">
                                  {message.provider}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                    {isLoading && (
                      <div className="flex justify-start">
                        <div className="max-w-[85%] rounded-lg p-3 bg-gradient-to-r from-emerald-50 to-green-50 border border-emerald-200 shadow-sm">
                          <div className="flex space-x-2">
                            <div className="w-2 h-2 rounded-full bg-emerald-400 animate-bounce" style={{ animationDelay: "0ms" }}></div>
                            <div className="w-2 h-2 rounded-full bg-emerald-400 animate-bounce" style={{ animationDelay: "150ms" }}></div>
                            <div className="w-2 h-2 rounded-full bg-emerald-400 animate-bounce" style={{ animationDelay: "300ms" }}></div>
                          </div>
                        </div>
                      </div>
                    )}
                    <div ref={messagesEndRef} />
                  </div>
                </ScrollArea>
              </CardContent>
              
              <Separator />
              
              <CardFooter className="p-4">
                <div className="flex w-full items-center space-x-2">
                  <Input
                    ref={inputRef}
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    onKeyDown={handleKeyDown}
                    placeholder="Ask me anything about your restaurant..."
                    className="flex-1"
                    disabled={isLoading}
                  />
                  <Button
                    onClick={handleSendMessage}
                    disabled={!inputValue.trim() || isLoading}
                  >
                    <Send className="mr-2 h-4 w-4" />
                    Send
                  </Button>
                </div>
              </CardFooter>
            </Card>
          </div>
          
          <div className="lg:w-1/4 space-y-6">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Quick Questions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {aiAssistantService.generateSuggestions(user?.restaurant_name).slice(0, 6).map((suggestion, i) => (
                  <Button 
                    key={i}
                    variant="outline" 
                    className="w-full justify-start h-auto py-2 px-3 text-left"
                    onClick={() => {
                      setInputValue(suggestion);
                      setTimeout(() => {
                        handleSendMessage();
                      }, 100);
                    }}
                  >
                    {suggestion}
                  </Button>
                ))}
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Insights</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Card className="p-3 bg-muted/50">
                  <div className="flex items-start">
                    <AlertTriangle className="h-5 w-5 mr-2 text-amber-500 mt-0.5" />
                    <div>
                      <div className="font-medium">Low Stock Alert</div>
                      <p className="text-sm text-muted-foreground">3 items are below par level</p>
                    </div>
                  </div>
                </Card>
                
                <Card className="p-3 bg-muted/50">
                  <div className="flex items-start">
                    <TrendingUp className="h-5 w-5 mr-2 text-green-500 mt-0.5" />
                    <div>
                      <div className="font-medium">Sales Trend</div>
                      <p className="text-sm text-muted-foreground">Up 12% from last week</p>
                    </div>
                  </div>
                </Card>
                
                <Card className="p-3 bg-muted/50">
                  <div className="flex items-start">
                    <Clock className="h-5 w-5 mr-2 text-blue-500 mt-0.5" />
                    <div>
                      <div className="font-medium">Peak Hours</div>
                      <p className="text-sm text-muted-foreground">Friday 6-9 PM is busiest</p>
                    </div>
                  </div>
                </Card>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button variant="outline" className="w-full justify-start">
                  <ShoppingCart className="mr-2 h-4 w-4" />
                  Generate Order
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Calendar className="mr-2 h-4 w-4" />
                  Staff Schedule
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <BarChart className="mr-2 h-4 w-4" />
                  View Analytics
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Assistant;