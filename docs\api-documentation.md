# API Documentation

## 🌐 Base URL
- **Development**: `http://localhost:5002` (Backend Direct) or `http://localhost:5175` (Through Proxy)
- **Production**: `https://your-domain.com`

## 🔐 Authentication
Most endpoints require authentication via JWT tokens in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

## 📋 Core API Endpoints

### Health & Status

#### `GET /health`
Basic health check endpoint.

**Response**:
```json
{
  "status": "healthy",
  "timestamp": "2025-07-17T12:00:00Z",
  "version": "1.0.0",
  "environment": "development"
}
```

#### `GET /health/detailed`
Comprehensive health check with dependency status.

**Response**:
```json
{
  "status": "healthy",
  "timestamp": "2025-07-17T12:00:00Z",
  "version": "1.0.0",
  "environment": "development",
  "response_time_ms": 45.2,
  "checks": {
    "database": {"status": "healthy", "response_time_ms": 12.3},
    "redis": {"status": "healthy", "response_time_ms": 8.1},
    "system": {"status": "healthy", "cpu_usage_percent": 15.2}
  }
}
```

### Restaurants

#### `GET /api/restaurants`
Get all restaurants with pagination.

**Query Parameters**:
- `skip` (int, default: 0): Number of restaurants to skip
- `limit` (int, default: 100): Maximum number of restaurants to return

**Response**:
```json
[
  {
    "id": "uuid",
    "name": "Restaurant Name",
    "code": "REST001",
    "address": "123 Main St, City, ZIP",
    "phone": "+**********",
    "email": "<EMAIL>",
    "is_active": true,
    "created_at": "2025-07-17T12:00:00Z"
  }
]
```

#### `POST /api/restaurants`
Create a new restaurant.

**Request Body**:
```json
{
  "name": "New Restaurant",
  "address": "456 Oak Ave, City, ZIP",
  "phone": "+**********",
  "email": "<EMAIL>",
  "owner_name": "John Doe",
  "business_license_number": "BL123456"
}
```

#### `GET /api/restaurants/{restaurant_id}`
Get a specific restaurant by ID.

#### `PUT /api/restaurants/{restaurant_id}`
Update a restaurant.

#### `DELETE /api/restaurants/{restaurant_id}`
Delete a restaurant.

### Notifications

#### `GET /api/notifications`
Get notifications for a restaurant.

**Query Parameters**:
- `restaurant_id` (string, required): Restaurant identifier
- `limit` (int, default: 50): Maximum number of notifications
- `is_read` (bool, optional): Filter by read status
- `type` (string, optional): Filter by notification type
- `priority` (string, optional): Filter by priority level

**Response**:
```json
{
  "notifications": [
    {
      "id": "uuid",
      "restaurant_id": "uuid",
      "title": "New Order",
      "message": "Order #123 has been placed",
      "type": "order",
      "priority": "high",
      "is_read": false,
      "created_at": "2025-07-17T12:00:00Z"
    }
  ],
  "total": 25,
  "unread_count": 5
}
```

#### `GET /api/notifications/stats`
Get notification statistics.

**Query Parameters**:
- `restaurant_id` (string, required): Restaurant identifier

**Response**:
```json
{
  "total_notifications": 100,
  "unread_count": 15,
  "by_type": {
    "order": 45,
    "inventory": 20,
    "staff": 10,
    "system": 25
  },
  "by_priority": {
    "high": 5,
    "medium": 30,
    "low": 65
  }
}
```

#### `POST /api/notifications`
Create a new notification.

**Request Body**:
```json
{
  "restaurant_id": "uuid",
  "title": "Notification Title",
  "message": "Notification message content",
  "type": "order",
  "priority": "medium",
  "metadata": {}
}
```

#### `PATCH /api/notifications/{notification_id}/read`
Mark a notification as read.

### Menu Management

#### `GET /api/menu`
Get menu items for a restaurant.

#### `POST /api/menu`
Create a new menu item.

#### `PUT /api/menu/{item_id}`
Update a menu item.

#### `DELETE /api/menu/{item_id}`
Delete a menu item.

### Orders

#### `GET /api/orders`
Get orders for a restaurant.

#### `POST /api/orders`
Create a new order.

#### `GET /api/orders/{order_id}`
Get a specific order.

#### `PUT /api/orders/{order_id}`
Update an order.

### Staff Management

#### `GET /api/staff`
Get staff members.

#### `POST /api/staff`
Add a new staff member.

#### `PUT /api/staff/{staff_id}`
Update staff member.

### Tables

#### `GET /api/tables`
Get restaurant tables.

#### `POST /api/tables`
Create a new table.

### Inventory

#### `GET /api/inventory`
Get inventory items.

#### `POST /api/inventory`
Add inventory item.

### Analytics

#### `GET /api/analytics/sales`
Get sales analytics.

#### `GET /api/analytics/performance`
Get performance metrics.

## 🔧 Error Responses

### Standard Error Format
```json
{
  "detail": "Error message",
  "status_code": 400,
  "timestamp": "2025-07-17T12:00:00Z"
}
```

### Common HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `500` - Internal Server Error

## 📝 Request/Response Examples

### Authentication Example
```bash
# Login
curl -X POST http://localhost:5002/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}'

# Use token
curl -X GET http://localhost:5002/api/restaurants \
  -H "Authorization: Bearer <your-token>"
```

### Notification Example
```bash
# Get notifications
curl "http://localhost:5002/api/notifications?restaurant_id=123&limit=10&is_read=false"

# Get notification stats
curl "http://localhost:5002/api/notifications/stats?restaurant_id=123"
```

---

**Note**: Replace `localhost:5002` with your actual backend URL. For development through Vite proxy, use `localhost:5175`.
