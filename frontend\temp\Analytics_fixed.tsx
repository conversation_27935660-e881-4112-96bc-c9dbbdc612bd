import { useState } from "react";
import Layout from "@/components/layout/Layout";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import DateRangeSelector from "@/components/dashboard/DateRangeSelector";
import StatCard from "@/components/dashboard/StatCard";
import CustomerFeedbackCard from "@/components/dashboard/CustomerFeedbackCard";
import ForecastCard, { ForecastData } from "@/components/dashboard/ForecastCard";
import PopularItemsCard from "@/components/dashboard/PopularItemsCard";

import { TrendingUp, DollarSign, Users, ShoppingBag } from "lucide-react";

// Mock customer reviews data
const mockReviews = [
  {
    id: "1",
    customerName: "<PERSON>",
    rating: 5,
    comment: "Excellent food and service! Will definitely come back.",
    date: "2023-06-15"
  },
  {
    id: "2",
    customerName: "<PERSON>",
    rating: 4,
    comment: "Great atmosphere and delicious food. Service was a bit slow.",
    date: "2023-06-14"
  },
  {
    id: "3",
    customerName: "<PERSON> Brown",
    rating: 5,
    comment: "The best Italian food in town! Highly recommend the pasta dishes.",
    date: "2023-06-12"
  },
  {
    id: "4",
    customerName: "Emily Davis",
    rating: 3,
    comment: "Food was good but portions were small for the price.",
    date: "2023-06-10"
  },
  {
    id: "5",
    customerName: "<PERSON>",
    rating: 4,
    comment: "Lovely ambiance and great wine selection. Will return!",
    date: "2023-06-08"
  }
];

// Mock popular menu items
const mockMenuItems = [
  {
    id: "1",
    name: "Margherita Pizza",
    category: "Pizza",
    price: 12.99,
    orderCount: 145,
    percentageOfSales: 18,
    trend: 5
  },
  {
    id: "2",
    name: "Spaghetti Carbonara",
    category: "Pasta",
    price: 14.99,
    orderCount: 120,
    percentageOfSales: 15,
    trend: 8
  },
  {
    id: "3",
    name: "Chicken Parmesan",
    category: "Main Course",
    price: 16.99,
    orderCount: 95,
    percentageOfSales: 12,
    trend: -3
  },
  {
    id: "4",
    name: "Tiramisu",
    category: "Dessert",
    price: 7.99,
    orderCount: 85,
    percentageOfSales: 10,
    trend: 12
  },
  {
    id: "5",
    name: "Caesar Salad",
    category: "Salad",
    price: 9.99,
    orderCount: 75,
    percentageOfSales: 9,
    trend: -2
  }
];

const Analytics = () => {
  const [dateRange, setDateRange] = useState<{ from: Date; to: Date }>({
    from: new Date(new Date().setDate(new Date().getDate() - 7)),
    to: new Date(),
  });

  // Sales data with correct ForecastData structure
  const salesData: ForecastData[] = [
    {
      day: "Monday",
      actualRevenue: 1200,
      projectedRevenue: 1100,
      customers: 45
    },
    {
      day: "Tuesday",
      actualRevenue: 1350,
      projectedRevenue: 1300,
      customers: 52
    },
    {
      day: "Wednesday",
      actualRevenue: 1500,
      projectedRevenue: 1450,
      customers: 61
    },
    {
      day: "Thursday",
      actualRevenue: 1700,
      projectedRevenue: 1600,
      customers: 70
    },
    {
      day: "Friday",
      actualRevenue: 2100,
      projectedRevenue: 2000,
      customers: 85
    },
    {
      day: "Saturday",
      actualRevenue: 2400,
      projectedRevenue: 2500,
      customers: 98
    },
    {
      day: "Sunday",
      actualRevenue: 1900,
      projectedRevenue: 1800,
      customers: 76
    }
  ];

  // Future forecast with correct ForecastData structure
  const forecastData: ForecastData[] = [
    {
      day: "Next Monday",
      actualRevenue: 0,
      projectedRevenue: 1150,
      customers: 47
    },
    {
      day: "Next Tuesday",
      actualRevenue: 0,
      projectedRevenue: 1380,
      customers: 54
    },
    {
      day: "Next Wednesday",
      actualRevenue: 0,
      projectedRevenue: 1520,
      customers: 63
    },
    {
      day: "Next Thursday",
      actualRevenue: 0,
      projectedRevenue: 1750,
      customers: 72
    },
    {
      day: "Next Friday",
      actualRevenue: 0,
      projectedRevenue: 2200,
      customers: 90
    },
    {
      day: "Next Saturday",
      actualRevenue: 0,
      projectedRevenue: 2600,
      customers: 105
    },
    {
      day: "Next Sunday",
      actualRevenue: 0,
      projectedRevenue: 2000,
      customers: 80
    }
  ];

  const handleDateRangeChange = (range: { from: Date; to: Date }) => {
    setDateRange(range);
    // In a real app, you would fetch new data based on the date range
    console.log("Date range changed:", range);
  };

  return (
    <Layout title="Analytics" requiredRoles={["admin"]}>
      <div className="flex flex-col gap-4">
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-bold">Analytics Dashboard</h2>
          <DateRangeSelector onRangeChange={handleDateRangeChange} />
        </div>

        <Tabs defaultValue="overview" className="w-full">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="reports">Reports</TabsTrigger>
            <TabsTrigger value="forecasts">Forecasts</TabsTrigger>
          </TabsList>
          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <StatCard
                title="Total Revenue"
                value="$24,500"
                icon={<DollarSign className="h-5 w-5" />}
                trend={{ value: 12, isPositive: true }}
                className="bg-card"
              />
              <StatCard
                title="New Customers"
                value="320"
                icon={<Users className="h-5 w-5" />}
                trend={{ value: 8, isPositive: true }}
                className="bg-card"
              />
              <StatCard
                title="Orders Placed"
                value="1,250"
                icon={<ShoppingBag className="h-5 w-5" />}
                trend={{ value: 3, isPositive: false }}
                className="bg-card"
              />
              <StatCard
                title="Website Visits"
                value="15,000"
                icon={<TrendingUp className="h-5 w-5" />}
                trend={{ value: 5, isPositive: true }}
                className="bg-card"
              />
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <ForecastCard data={salesData} title="Sales Performance" />
              <CustomerFeedbackCard reviews={mockReviews} />
            </div>
          </TabsContent>

          <TabsContent value="reports" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Detailed Reports</CardTitle>
              </CardHeader>
              <CardContent>
                <p>Detailed reports and data visualizations will be available here.</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="forecasts" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <ForecastCard data={forecastData} title="Future Forecast" />
              <PopularItemsCard items={mockMenuItems} />
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </Layout>
  );
};

export default Analytics;
