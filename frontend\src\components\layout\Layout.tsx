import logger from "@/utils/logger";

import { ReactNode, useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { Navigate } from "react-router-dom";
import Sidebar from "./Sidebar";
import Header from "./Header";
import { useIsMobile } from "@/hooks/use-mobile";
import { Menu } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/contexts/AuthContext";

interface LayoutProps {
  children: ReactNode;
  title: string;
  requiredRoles?: Array<string>;
}

const Layout = ({ children, title, requiredRoles = ["admin", "staff", "manager"] }: LayoutProps) => {
  // Initialize component logging
  logger.setComponent("Layout");
  logger.info("Component initialized", "Layout");
  const { user, isAuthenticated, currentRestaurant, isRestaurantAuthenticated } = useAuth();
  const isMobile = useIsMobile();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  logger.debug('Rendering layout', 'Layout', { title });
  logger.debug('Authentication state', 'Layout', { isAuthenticated, user: user?.name, userRole: user?.role });
  logger.debug('Required roles check', 'Layout', { requiredRoles });

  // Redirect to staff login if not authenticated
  if (!isAuthenticated || !user) {
    logger.authentication('layout access', 'failure', 'Layout', { reason: 'not authenticated' });
    return <Navigate to="/staff-login" replace />;
  }

  // Redirect to appropriate dashboard if user doesn't have required role
  // More flexible role checking - similar to ProtectedRoute
  const hasRequiredRole = requiredRoles.some(allowedRole => {
    const lowerAllowedRole = allowedRole.toLowerCase().trim();
    const lowerUserRole = user.role.toLowerCase().trim();

    // Direct match
    if (lowerAllowedRole === lowerUserRole) {
      return true;
    }

    // Check if 'staff' is allowed and user has a staff-like role
    if (lowerAllowedRole === 'staff' &&
        ['waiter', 'chef', 'hostess', 'bartender', 'staff'].includes(lowerUserRole)) {
      return true;
    }

    // Check if 'admin' is allowed and user has an admin-like role
    if (lowerAllowedRole === 'admin' &&
        ['admin', 'manager'].includes(lowerUserRole)) {
      return true;
    }

    return false;
  });

  logger.debug('Enhanced role check', 'Layout', {
    userRole: user.role,
    requiredRoles,
    hasRequiredRole,
    path: window.location.pathname
  });

  if (requiredRoles.length > 0 && !hasRequiredRole) {
    logger.authentication('role access', 'failure', 'Layout', { userRole: user.role, requiredRoles });

    // Avoid circular redirects by checking the current path
    const currentPath = window.location.pathname;
    logger.debug('Current path check', 'Layout', { currentPath });

    if (currentPath === '/admin' || currentPath === '/staff-dashboard') {
      logger.debug('Dashboard loop prevention', 'Layout');
      // Already on a dashboard, don't redirect to avoid loops
      return null;
    }

    // Redirect based on role - case insensitive check
    const lowerUserRole = user.role.toLowerCase();
    if (lowerUserRole === 'admin' || lowerUserRole === 'manager') {
      logger.userAction('redirect admin/manager to dashboard', 'Layout');
      return <Navigate to="/admin" replace />;
    } else {
      logger.userAction('redirect staff to dashboard', 'Layout');
      return <Navigate to="/staff-dashboard" replace />;
    }
  }

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };
  
  // Close sidebar when clicking outside on mobile
  const handleContentClick = () => {
    if (isMobile && sidebarOpen) {
      setSidebarOpen(false);
    }
  };

  if (!isMounted) {
    // Return a loading state or empty fragment until the component is mounted
    // This prevents a flash of UI before we determine mobile status
    return <div className="min-h-screen bg-background"></div>;
  }

  return (
    <div className="flex h-screen overflow-hidden bg-background">
      {/* Mobile overlay - only visible when sidebar is open on mobile */}
      {isMobile && sidebarOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black/50 backdrop-blur-sm transition-opacity"
          onClick={toggleSidebar}
          aria-hidden="true"
        />
      )}
      
      {/* Sidebar */}
      <div
        className={cn(
          "fixed inset-y-0 left-0 z-50 w-64 transform transition-transform duration-300 ease-in-out",
          isMobile ? (sidebarOpen ? "translate-x-0" : "-translate-x-full") : "translate-x-0"
        )}
      >
        {(() => {
          const userRole = user?.role || "staff";

          logger.debug('Passing userRole to Sidebar', 'Layout', {
            userRole,
            userName: user?.name,
            userId: user?.id,
            userExists: !!user
          });
          return <Sidebar userRole={userRole} onCloseMobile={isMobile ? () => setSidebarOpen(false) : undefined} />;
        })()}
      </div>

      {/* Main content */}
      <div 
        className={cn(
          "flex-1 flex flex-col transition-all duration-300 ease-in-out",
          isMobile ? "ml-0" : "ml-64"
        )}
        onClick={handleContentClick}
      >
        <Header title={title}>
          {isMobile && (
            <Button
              variant="outline"
              size="icon"
              className="mr-2"
              onClick={toggleSidebar}
              aria-label="Toggle sidebar"
            >
              <Menu className="h-4 w-4" />
            </Button>
          )}
        </Header>
        <main className="flex-1 overflow-auto p-4 md:p-6 lg:p-8">
          <div className="mb-4 md:mb-6">
            <h1 className="text-xl md:text-2xl lg:text-3xl font-bold tracking-tight">{title}</h1>
          </div>
          {children}
        </main>
      </div>
    </div>
  );
};

export default Layout;
