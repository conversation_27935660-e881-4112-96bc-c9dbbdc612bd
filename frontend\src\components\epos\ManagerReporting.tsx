import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>T<PERSON>le, DialogFooter } from "@/components/ui/dialog";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  Clock, 
  PoundSterling,
  Table,
  ChefHat,
  Calendar,
  Download,
  Printer,
  RefreshCw
} from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { useStaffPIN } from "@/contexts/StaffPINContext";
import { toast } from "@/components/ui/sonner";
import apiService from "@/services/apiService";
import logger from "@/utils/logger";

interface DailyReport {
  date: string;
  total_orders: number;
  total_revenue: number;
  avg_order_value: number;
  dine_in_orders: number;
  takeaway_orders: number;
  avg_table_turnover: number;
  peak_hours: string[];
  staff_performance: StaffPerformance[];
  hourly_breakdown: HourlyBreakdown[];
  payment_methods: PaymentMethodBreakdown[];
}

interface StaffPerformance {
  staff_id: string;
  staff_name: string;
  orders_served: number;
  total_sales: number;
  avg_service_time: number;
  customer_satisfaction: number;
}

interface HourlyBreakdown {
  hour: string;
  orders: number;
  revenue: number;
  avg_wait_time: number;
}

interface PaymentMethodBreakdown {
  method: string;
  count: number;
  total: number;
  percentage: number;
}

interface ManagerReportingProps {
  isOpen: boolean;
  onClose: () => void;
}

const ManagerReporting: React.FC<ManagerReportingProps> = ({ isOpen, onClose }) => {
  const { currentRestaurant } = useAuth();
  const { activeStaff } = useStaffPIN();
  const [dailyReport, setDailyReport] = useState<DailyReport | null>(null);
  const [loading, setLoading] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);

  // Load report when dialog opens
  useEffect(() => {
    if (isOpen && currentRestaurant) {
      loadDailyReport();
    }
  }, [isOpen, currentRestaurant, selectedDate]);

  const loadDailyReport = async () => {
    if (!currentRestaurant) return;
    
    setLoading(true);
    try {
      logger.debug('Loading daily report', 'ManagerReporting', { 
        restaurantId: currentRestaurant.id,
        date: selectedDate
      });

      // Try to load from API first, fallback to mock data
      try {
        const response = await apiService.apiRequest(
          `/mvc/reports/daily?restaurant_id=${currentRestaurant.id}&date=${selectedDate}`
        );
        
        const typedResponse = response as { success: boolean; data?: any };
        if (typedResponse.success && typedResponse.data) {
          setDailyReport(typedResponse.data);
          logger.info('Daily report loaded from API', 'ManagerReporting');
        } else {
          throw new Error('Invalid API response');
        }
      } catch (apiError) {
        logger.warn('API failed, using mock daily report', 'ManagerReporting', { 
          error: apiError.message 
        });
        
        // Fallback to mock data
        const mockReport = generateMockDailyReport();
        setDailyReport(mockReport);
      }
    } catch (error) {
      logger.error('Failed to load daily report', 'ManagerReporting', { error: error.message });
      toast.error('Failed to load daily report');
    } finally {
      setLoading(false);
    }
  };

  const generateMockDailyReport = (): DailyReport => {
    const today = new Date(selectedDate);
    const isToday = selectedDate === new Date().toISOString().split('T')[0];
    
    // Generate hourly breakdown
    const hourlyBreakdown: HourlyBreakdown[] = [];
    for (let hour = 8; hour <= 22; hour++) {
      const orders = Math.floor(Math.random() * 15) + 5;
      const revenue = orders * (15 + Math.random() * 20);
      hourlyBreakdown.push({
        hour: `${hour}:00`,
        orders,
        revenue: Math.round(revenue * 100) / 100,
        avg_wait_time: Math.floor(Math.random() * 20) + 10
      });
    }

    const totalOrders = hourlyBreakdown.reduce((sum, h) => sum + h.orders, 0);
    const totalRevenue = hourlyBreakdown.reduce((sum, h) => sum + h.revenue, 0);

    return {
      date: selectedDate,
      total_orders: totalOrders,
      total_revenue: Math.round(totalRevenue * 100) / 100,
      avg_order_value: Math.round((totalRevenue / totalOrders) * 100) / 100,
      dine_in_orders: Math.floor(totalOrders * 0.7),
      takeaway_orders: Math.floor(totalOrders * 0.3),
      avg_table_turnover: 2.3,
      peak_hours: ['12:00-14:00', '18:00-20:00'],
      staff_performance: [
        {
          staff_id: 'staff_001',
          staff_name: 'Alice Johnson',
          orders_served: Math.floor(totalOrders * 0.4),
          total_sales: Math.round(totalRevenue * 0.4 * 100) / 100,
          avg_service_time: 45,
          customer_satisfaction: 4.8
        },
        {
          staff_id: 'staff_002',
          staff_name: 'Bob Smith',
          orders_served: Math.floor(totalOrders * 0.35),
          total_sales: Math.round(totalRevenue * 0.35 * 100) / 100,
          avg_service_time: 52,
          customer_satisfaction: 4.6
        },
        {
          staff_id: 'staff_003',
          staff_name: 'Carol Davis',
          orders_served: Math.floor(totalOrders * 0.25),
          total_sales: Math.round(totalRevenue * 0.25 * 100) / 100,
          avg_service_time: 48,
          customer_satisfaction: 4.7
        }
      ],
      hourly_breakdown: hourlyBreakdown,
      payment_methods: [
        {
          method: 'Card',
          count: Math.floor(totalOrders * 0.75),
          total: Math.round(totalRevenue * 0.75 * 100) / 100,
          percentage: 75
        },
        {
          method: 'Cash',
          count: Math.floor(totalOrders * 0.25),
          total: Math.round(totalRevenue * 0.25 * 100) / 100,
          percentage: 25
        }
      ]
    };
  };

  const exportReport = () => {
    if (!dailyReport) return;
    
    const reportData = {
      restaurant: currentRestaurant?.name,
      date: dailyReport.date,
      summary: {
        total_orders: dailyReport.total_orders,
        total_revenue: dailyReport.total_revenue,
        avg_order_value: dailyReport.avg_order_value
      },
      staff_performance: dailyReport.staff_performance,
      hourly_breakdown: dailyReport.hourly_breakdown
    };

    const dataStr = JSON.stringify(reportData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `daily-report-${dailyReport.date}.json`;
    link.click();
    
    URL.revokeObjectURL(url);
    toast.success('Report exported successfully');
  };

  const printReport = () => {
    window.print();
    toast.success('Report sent to printer');
  };

  if (!dailyReport && !loading) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <BarChart3 className="w-5 h-5" />
            Manager Daily Report - {selectedDate}
          </DialogTitle>
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-2 text-gray-400" />
              <p className="text-gray-500">Loading daily report...</p>
            </div>
          </div>
        ) : dailyReport ? (
          <Tabs defaultValue="overview" className="h-[70vh]">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="hourly">Hourly Breakdown</TabsTrigger>
              <TabsTrigger value="staff">Staff Performance</TabsTrigger>
              <TabsTrigger value="payments">Payment Methods</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="h-[calc(100%-60px)]">
              <ScrollArea className="h-full">
                <div className="space-y-6 p-4">
                  {/* Key Metrics */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <Card>
                      <CardContent className="p-4 text-center">
                        <div className="text-2xl font-bold text-blue-600">{dailyReport.total_orders}</div>
                        <div className="text-sm text-gray-600">Total Orders</div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-4 text-center">
                        <div className="text-2xl font-bold text-green-600">£{dailyReport.total_revenue.toFixed(2)}</div>
                        <div className="text-sm text-gray-600">Total Revenue</div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-4 text-center">
                        <div className="text-2xl font-bold text-purple-600">£{dailyReport.avg_order_value.toFixed(2)}</div>
                        <div className="text-sm text-gray-600">Avg Order Value</div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-4 text-center">
                        <div className="text-2xl font-bold text-orange-600">{dailyReport.avg_table_turnover}</div>
                        <div className="text-sm text-gray-600">Table Turnover</div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Order Types */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Order Distribution</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="text-center">
                          <div className="text-xl font-bold text-blue-600">{dailyReport.dine_in_orders}</div>
                          <div className="text-sm text-gray-600">Dine-In Orders</div>
                          <div className="text-xs text-gray-500">
                            {Math.round((dailyReport.dine_in_orders / dailyReport.total_orders) * 100)}%
                          </div>
                        </div>
                        <div className="text-center">
                          <div className="text-xl font-bold text-green-600">{dailyReport.takeaway_orders}</div>
                          <div className="text-sm text-gray-600">Takeaway Orders</div>
                          <div className="text-xs text-gray-500">
                            {Math.round((dailyReport.takeaway_orders / dailyReport.total_orders) * 100)}%
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Peak Hours */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Peak Hours</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex gap-2">
                        {dailyReport.peak_hours.map((hour, index) => (
                          <Badge key={index} className="bg-orange-100 text-orange-800">
                            {hour}
                          </Badge>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </ScrollArea>
            </TabsContent>

            <TabsContent value="hourly" className="h-[calc(100%-60px)]">
              <ScrollArea className="h-full">
                <div className="p-4">
                  <div className="space-y-3">
                    {dailyReport.hourly_breakdown.map((hour, index) => (
                      <Card key={index}>
                        <CardContent className="p-4">
                          <div className="grid grid-cols-4 gap-4 text-center">
                            <div>
                              <div className="font-bold">{hour.hour}</div>
                              <div className="text-xs text-gray-500">Time</div>
                            </div>
                            <div>
                              <div className="font-bold text-blue-600">{hour.orders}</div>
                              <div className="text-xs text-gray-500">Orders</div>
                            </div>
                            <div>
                              <div className="font-bold text-green-600">£{hour.revenue.toFixed(2)}</div>
                              <div className="text-xs text-gray-500">Revenue</div>
                            </div>
                            <div>
                              <div className="font-bold text-orange-600">{hour.avg_wait_time}m</div>
                              <div className="text-xs text-gray-500">Avg Wait</div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              </ScrollArea>
            </TabsContent>

            <TabsContent value="staff" className="h-[calc(100%-60px)]">
              <ScrollArea className="h-full">
                <div className="p-4 space-y-4">
                  {dailyReport.staff_performance.map((staff, index) => (
                    <Card key={index}>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-2">
                            <ChefHat className="w-5 h-5" />
                            <span className="font-medium">{staff.staff_name}</span>
                          </div>
                          <Badge className="bg-yellow-100 text-yellow-800">
                            ⭐ {staff.customer_satisfaction}/5
                          </Badge>
                        </div>
                        <div className="grid grid-cols-3 gap-4 text-center">
                          <div>
                            <div className="font-bold text-blue-600">{staff.orders_served}</div>
                            <div className="text-xs text-gray-500">Orders Served</div>
                          </div>
                          <div>
                            <div className="font-bold text-green-600">£{staff.total_sales.toFixed(2)}</div>
                            <div className="text-xs text-gray-500">Total Sales</div>
                          </div>
                          <div>
                            <div className="font-bold text-orange-600">{staff.avg_service_time}m</div>
                            <div className="text-xs text-gray-500">Avg Service Time</div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>

            <TabsContent value="payments" className="h-[calc(100%-60px)]">
              <ScrollArea className="h-full">
                <div className="p-4 space-y-4">
                  {dailyReport.payment_methods.map((method, index) => (
                    <Card key={index}>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between mb-2">
                          <span className="font-medium">{method.method}</span>
                          <Badge className="bg-blue-100 text-blue-800">
                            {method.percentage}%
                          </Badge>
                        </div>
                        <div className="grid grid-cols-2 gap-4 text-center">
                          <div>
                            <div className="font-bold text-blue-600">{method.count}</div>
                            <div className="text-xs text-gray-500">Transactions</div>
                          </div>
                          <div>
                            <div className="font-bold text-green-600">£{method.total.toFixed(2)}</div>
                            <div className="text-xs text-gray-500">Total Amount</div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>
          </Tabs>
        ) : null}

        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={exportReport} disabled={!dailyReport}>
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" onClick={printReport} disabled={!dailyReport}>
            <Printer className="w-4 h-4 mr-2" />
            Print
          </Button>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ManagerReporting;
