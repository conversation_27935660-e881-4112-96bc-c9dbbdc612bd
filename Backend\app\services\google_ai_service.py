"""
Google Generative AI Service for RestroManage
Provides AI-powered features for restaurant management including:
- Promo code suggestions
- Customer insights
- Menu optimization
- Sales predictions
"""

import os
import json
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import google.generativeai as genai
from app.utils.storage import get_all, query
from app.utils.logging_config import logger, log_ai_model_usage

class GoogleAIService:
    """Service for Google Generative AI integration"""
    
    def __init__(self):
        self.api_key = os.getenv("GOOGLE_API_KEY")
        self.model_name = os.getenv("GEMINI_MODEL", "gemini-1.5-flash")
        self.temperature = float(os.getenv("GOOGLE_AI_TEMPERATURE", "0.7"))
        self.max_tokens = int(os.getenv("GOOGLE_AI_MAX_TOKENS", "1000"))

        logger.info("Initializing Google AI Service", "GoogleAI", {
            "model": self.model_name,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "api_key_configured": bool(self.api_key and self.api_key != "your-google-api-key-here")
        })

        # Initialize Google AI service if API key is configured
        if self.api_key and self.api_key != "your-google-api-key-here":
            try:
                genai.configure(api_key=self.api_key)
                self.model = genai.GenerativeModel(self.model_name)
                self.enabled = True
                log_ai_model_usage(self.model_name, "initialization", True, {
                    "temperature": self.temperature,
                    "max_tokens": self.max_tokens
                })
                logger.info("Google AI Service initialized successfully", "GoogleAI")
            except Exception as e:
                self.enabled = False
                logger.error("Failed to initialize Google AI Service", "GoogleAI", {"error": str(e)})
                log_ai_model_usage(self.model_name, "initialization", False, {"error": str(e)})
        else:
            self.enabled = False
            logger.warning("Google AI API key not configured. AI features will be disabled.", "GoogleAI")
    
    def is_enabled(self) -> bool:
        """Check if Google AI is properly configured"""
        return self.enabled

    def reload_configuration(self):
        """Reload configuration from environment variables"""
        self.api_key = os.getenv("GOOGLE_API_KEY")
        self.model_name = os.getenv("GEMINI_MODEL", "gemini-1.5-flash")
        self.temperature = float(os.getenv("GOOGLE_AI_TEMPERATURE", "0.7"))
        self.max_tokens = int(os.getenv("GOOGLE_AI_MAX_TOKENS", "1000"))
        # Trigger auto-reload by adding this comment

        logger.info("Reloading Google AI Service configuration", "GoogleAI", {
            "model": self.model_name,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "api_key_configured": bool(self.api_key and self.api_key != "your-google-api-key-here")
        })

        # Reinitialize Google AI service if API key is configured
        if self.api_key and self.api_key != "your-google-api-key-here":
            try:
                genai.configure(api_key=self.api_key)
                self.model = genai.GenerativeModel(self.model_name)
                self.enabled = True
                log_ai_model_usage(self.model_name, "reinitialization", True, {
                    "temperature": self.temperature,
                    "max_tokens": self.max_tokens
                })
                logger.info("Google AI Service reinitialized successfully", "GoogleAI")
            except Exception as e:
                self.enabled = False
                logger.error("Failed to reinitialize Google AI Service", "GoogleAI", {"error": str(e)})
                log_ai_model_usage(self.model_name, "reinitialization", False, {"error": str(e)})
        else:
            self.enabled = False
            logger.warning("Google AI API key not configured during reload. AI features will be disabled.", "GoogleAI")
    
    async def generate_promo_code_suggestions(
        self,
        business_context: Dict[str, Any],
        target_audience: str = "general",
        campaign_goal: str = "increase_sales"
    ) -> List[Dict[str, Any]]:
        """Generate AI-powered promo code suggestions"""

        logger.info("Generating promo code suggestions", "GoogleAI", {
            "target_audience": target_audience,
            "campaign_goal": campaign_goal,
            "ai_enabled": self.enabled
        })

        if not self.enabled:
            logger.warning("AI not enabled, using fallback promo suggestions", "GoogleAI")
            return self._get_fallback_promo_suggestions()

        try:
            # Get current promo codes for context
            existing_codes = get_all("promo_codes")
            existing_code_names = [code.get("code", "") for code in existing_codes]
            
            # Get recent orders for sales context
            orders = get_all("orders")
            recent_orders = [order for order in orders if self._is_recent_order(order)]
            
            prompt = f"""
            As a restaurant marketing expert, suggest 5 creative promo codes for a restaurant with the following context:
            
            Business Context:
            - Restaurant Type: {business_context.get('type', 'casual dining')}
            - Average Order Value: £{business_context.get('avg_order_value', 25)}
            - Peak Hours: {business_context.get('peak_hours', '12-2pm, 6-9pm')}
            - Target Audience: {target_audience}
            - Campaign Goal: {campaign_goal}
            
            Recent Performance:
            - Recent Orders: {len(recent_orders)}
            - Current Active Codes: {len(existing_codes)}
            
            Existing Codes to Avoid: {', '.join(existing_code_names[:10])}
            
            Please suggest 5 promo codes with the following JSON format:
            {{
                "suggestions": [
                    {{
                        "code": "UNIQUE_CODE",
                        "name": "Descriptive Name",
                        "description": "Customer-facing description",
                        "discount_type": "percentage" or "fixed_amount",
                        "discount_value": number,
                        "minimum_spend": number or null,
                        "target_audience": "description",
                        "reasoning": "Why this code would be effective"
                    }}
                ]
            }}
            
            Make the codes creative, memorable, and aligned with current trends. Consider seasonal relevance and restaurant industry best practices.
            """
            
            log_ai_model_usage(self.model_name, "promo_code_generation", True, {
                "target_audience": target_audience,
                "campaign_goal": campaign_goal
            })

            response = self.model.generate_content(prompt)

            # Parse the JSON response
            try:
                result = json.loads(response.text)
                suggestions = result.get("suggestions", [])
                logger.info("Promo code suggestions generated successfully", "GoogleAI", {
                    "suggestions_count": len(suggestions)
                })
                return suggestions
            except json.JSONDecodeError:
                logger.warning("Failed to parse JSON response, using text parsing", "GoogleAI")
                # If JSON parsing fails, extract suggestions manually
                return self._parse_text_suggestions(response.text)

        except Exception as e:
            error_details = {
                "error": str(e),
                "error_type": type(e).__name__,
                "target_audience": target_audience,
                "campaign_goal": campaign_goal
            }
            logger.error("Error generating promo code suggestions", "GoogleAI", error_details)
            log_ai_model_usage(self.model_name, "promo_code_generation", False, error_details)
            return self._get_fallback_promo_suggestions()
    
    async def analyze_customer_behavior(
        self, 
        customer_data: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Analyze customer behavior patterns using AI"""
        
        if not self.enabled:
            return self._get_fallback_customer_analysis()
        
        try:
            # Prepare customer data summary
            total_customers = len(customer_data)
            orders = get_all("orders")
            promo_usage = get_all("promo_code_usage")
            
            prompt = f"""
            Analyze the following restaurant customer data and provide insights:
            
            Customer Data Summary:
            - Total Customers: {total_customers}
            - Total Orders: {len(orders)}
            - Promo Code Usage: {len(promo_usage)}
            
            Please provide analysis in the following JSON format:
            {{
                "customer_segments": [
                    {{
                        "segment_name": "High Value Customers",
                        "percentage": 15,
                        "characteristics": ["description"],
                        "recommendations": ["action items"]
                    }}
                ],
                "behavioral_patterns": {{
                    "peak_ordering_times": ["time ranges"],
                    "popular_items": ["item categories"],
                    "discount_sensitivity": "high/medium/low"
                }},
                "recommendations": {{
                    "marketing_strategies": ["strategies"],
                    "promo_targeting": ["targeting approaches"],
                    "retention_tactics": ["retention methods"]
                }},
                "insights": ["key insights about customer behavior"]
            }}
            
            Focus on actionable insights that can improve customer retention and increase sales.
            """
            
            response = self.model.generate_content(prompt)
            
            try:
                return json.loads(response.text)
            except json.JSONDecodeError:
                return self._parse_text_analysis(response.text)
                
        except Exception as e:
            print(f"Error analyzing customer behavior: {e}")
            return self._get_fallback_customer_analysis()
    
    async def optimize_menu_pricing(
        self, 
        menu_items: List[Dict[str, Any]],
        cost_data: Dict[str, float]
    ) -> Dict[str, Any]:
        """Provide AI-powered menu pricing optimization suggestions"""
        
        if not self.enabled:
            return self._get_fallback_pricing_suggestions()
        
        try:
            # Get order data to understand item popularity
            orders = get_all("orders")
            item_popularity = self._calculate_item_popularity(orders)
            
            prompt = f"""
            As a restaurant pricing expert, analyze the following menu and provide pricing optimization suggestions:
            
            Menu Items: {len(menu_items)}
            Cost Data Available: {len(cost_data)} items
            Order History: {len(orders)} orders
            
            Popular Items: {list(item_popularity.keys())[:10]}
            
            Please provide pricing optimization in JSON format:
            {{
                "pricing_recommendations": [
                    {{
                        "item_name": "item name",
                        "current_price": current_price,
                        "suggested_price": suggested_price,
                        "reasoning": "explanation",
                        "expected_impact": "positive/negative/neutral"
                    }}
                ],
                "general_strategies": [
                    "pricing strategy recommendations"
                ],
                "market_insights": {{
                    "price_elasticity": "analysis",
                    "competitive_positioning": "recommendations",
                    "psychological_pricing": "suggestions"
                }}
            }}
            
            Consider factors like cost margins, demand elasticity, and psychological pricing principles.
            """
            
            response = self.model.generate_content(prompt)
            
            try:
                return json.loads(response.text)
            except json.JSONDecodeError:
                return self._parse_text_pricing(response.text)
                
        except Exception as e:
            print(f"Error optimizing menu pricing: {e}")
            return self._get_fallback_pricing_suggestions()
    
    async def predict_sales_trends(
        self, 
        historical_data: List[Dict[str, Any]],
        time_period: str = "next_week"
    ) -> Dict[str, Any]:
        """Predict sales trends using AI analysis"""
        
        if not self.enabled:
            return self._get_fallback_sales_prediction()
        
        try:
            prompt = f"""
            Analyze the following restaurant sales data and predict trends for {time_period}:
            
            Historical Data Points: {len(historical_data)}
            Analysis Period: {time_period}
            
            Please provide sales predictions in JSON format:
            {{
                "predictions": {{
                    "revenue_forecast": {{
                        "amount": estimated_amount,
                        "confidence": "high/medium/low",
                        "factors": ["influencing factors"]
                    }},
                    "order_volume": {{
                        "estimated_orders": number,
                        "peak_times": ["time periods"],
                        "slow_periods": ["time periods"]
                    }}
                }},
                "trends": [
                    "identified trends and patterns"
                ],
                "recommendations": [
                    "actionable recommendations based on predictions"
                ],
                "risk_factors": [
                    "potential challenges or opportunities"
                ]
            }}
            
            Consider seasonal patterns, day-of-week variations, and external factors.
            """
            
            response = self.model.generate_content(prompt)
            
            try:
                return json.loads(response.text)
            except json.JSONDecodeError:
                return self._parse_text_prediction(response.text)
                
        except Exception as e:
            print(f"Error predicting sales trends: {e}")
            return self._get_fallback_sales_prediction()

    async def generate_custom_response(
        self,
        query: str,
        context: Optional[Dict[str, Any]] = None
    ) -> str:
        """Generate a simple AI response for restaurant management queries"""

        logger.info("Generating custom AI response", "GoogleAI", {
            "query_length": len(query),
            "has_context": bool(context),
            "ai_enabled": self.enabled
        })

        if not self.enabled:
            logger.warning("AI not enabled, using fallback response", "GoogleAI")
            return self._get_fallback_response(query)

        try:
            # Simple, general conversational AI prompt
            prompt = f"""
            You are a helpful and friendly AI assistant. Answer this question in a conversational and helpful way:

            Question: {query}

            Provide a clear, informative response. Be helpful, friendly, and engaging.
            """

            log_ai_model_usage(self.model_name, "custom_response", True, {
                "query_length": len(query),
                "has_context": bool(context)
            })

            response = self.model.generate_content(prompt)

            logger.info("Custom AI response generated successfully", "GoogleAI", {
                "response_length": len(response.text)
            })

            return response.text.strip()

        except Exception as e:
            error_details = {
                "error": str(e),
                "error_type": type(e).__name__,
                "query_length": len(query),
                "has_context": bool(context)
            }
            logger.error("Error generating custom response", "GoogleAI", error_details)
            log_ai_model_usage(self.model_name, "custom_response", False, error_details)
            return self._get_fallback_response(query)

    def _get_fallback_response(self, query: str) -> str:
        """Simple fallback response when AI is not available"""
        query_lower = query.lower()

        if "hello" in query_lower or "hi" in query_lower:
            return "Hello! I'm your AI assistant. How can I help you today?"
        elif "how are you" in query_lower:
            return "I'm doing well, thank you for asking! I'm here to help with any questions you might have."
        elif "what can you do" in query_lower or "help" in query_lower:
            return "I'm an AI assistant that can help answer questions, provide information, and have conversations on a wide variety of topics. What would you like to know?"
        elif "weather" in query_lower:
            return "I don't have access to real-time weather data, but I'd be happy to help with other questions!"
        elif "time" in query_lower or "date" in query_lower:
            return "I don't have access to current time information, but I can help with other topics. What else can I assist you with?"
        else:
            return "I'm here to help! Feel free to ask me anything - I can discuss various topics, answer questions, or just have a friendly conversation."
    
    # Helper methods
    def _is_recent_order(self, order: Dict[str, Any]) -> bool:
        """Check if an order is from the last 30 days"""
        try:
            order_date = datetime.fromisoformat(order.get("created_at", ""))
            return order_date > datetime.now() - timedelta(days=30)
        except:
            return False
    
    def _calculate_item_popularity(self, orders: List[Dict[str, Any]]) -> Dict[str, int]:
        """Calculate item popularity from order history"""
        popularity = {}
        for order in orders:
            for item in order.get("items", []):
                item_name = item.get("name", "Unknown")
                popularity[item_name] = popularity.get(item_name, 0) + item.get("quantity", 1)
        return dict(sorted(popularity.items(), key=lambda x: x[1], reverse=True))
    
    # Fallback methods for when AI is not available
    def _get_fallback_promo_suggestions(self) -> List[Dict[str, Any]]:
        """Fallback promo code suggestions when AI is not available"""
        return [
            {
                "code": "SAVE10",
                "name": "10% Off Everything",
                "description": "Get 10% off your entire order",
                "discount_type": "percentage",
                "discount_value": 10,
                "minimum_spend": 20,
                "target_audience": "general",
                "reasoning": "Simple percentage discount appeals to all customers"
            },
            {
                "code": "WELCOME5",
                "name": "Welcome Offer",
                "description": "£5 off your first order",
                "discount_type": "fixed_amount",
                "discount_value": 5,
                "minimum_spend": 15,
                "target_audience": "new customers",
                "reasoning": "Fixed amount discount for new customer acquisition"
            }
        ]
    
    def _get_fallback_customer_analysis(self) -> Dict[str, Any]:
        """Fallback customer analysis when AI is not available"""
        return {
            "customer_segments": [
                {
                    "segment_name": "Regular Customers",
                    "percentage": 60,
                    "characteristics": ["Frequent orders", "Consistent spending"],
                    "recommendations": ["Loyalty program", "Exclusive offers"]
                }
            ],
            "behavioral_patterns": {
                "peak_ordering_times": ["12:00-14:00", "18:00-21:00"],
                "popular_items": ["Main courses", "Beverages"],
                "discount_sensitivity": "medium"
            },
            "recommendations": {
                "marketing_strategies": ["Email campaigns", "Social media engagement"],
                "promo_targeting": ["Time-based offers", "Category-specific discounts"],
                "retention_tactics": ["Loyalty points", "Birthday offers"]
            },
            "insights": ["Customers prefer percentage discounts", "Weekend orders are higher"]
        }
    
    def _get_fallback_pricing_suggestions(self) -> Dict[str, Any]:
        """Fallback pricing suggestions when AI is not available"""
        return {
            "pricing_recommendations": [],
            "general_strategies": [
                "Use psychological pricing (£9.99 vs £10.00)",
                "Bundle popular items together",
                "Implement dynamic pricing for peak hours"
            ],
            "market_insights": {
                "price_elasticity": "Monitor customer response to price changes",
                "competitive_positioning": "Research competitor pricing regularly",
                "psychological_pricing": "End prices with 9 or 5 for better perception"
            }
        }
    
    def _get_fallback_sales_prediction(self) -> Dict[str, Any]:
        """Fallback sales prediction when AI is not available"""
        return {
            "predictions": {
                "revenue_forecast": {
                    "amount": 0,
                    "confidence": "low",
                    "factors": ["Historical average", "Seasonal patterns"]
                },
                "order_volume": {
                    "estimated_orders": 0,
                    "peak_times": ["12:00-14:00", "18:00-21:00"],
                    "slow_periods": ["15:00-17:00", "22:00-11:00"]
                }
            },
            "trends": ["Weekend sales typically higher", "Lunch rush consistent"],
            "recommendations": ["Track daily patterns", "Monitor seasonal changes"],
            "risk_factors": ["Weather dependency", "Local events impact"]
        }
    
    def _parse_text_suggestions(self, text: str) -> List[Dict[str, Any]]:
        """Parse text response when JSON parsing fails"""
        # Simple fallback parsing - in production, implement more robust parsing
        return self._get_fallback_promo_suggestions()
    
    def _parse_text_analysis(self, text: str) -> Dict[str, Any]:
        """Parse text response for customer analysis"""
        return self._get_fallback_customer_analysis()
    
    def _parse_text_pricing(self, text: str) -> Dict[str, Any]:
        """Parse text response for pricing suggestions"""
        return self._get_fallback_pricing_suggestions()
    
    def _parse_text_prediction(self, text: str) -> Dict[str, Any]:
        """Parse text response for sales predictions"""
        return self._get_fallback_sales_prediction()

# Global instance
google_ai_service = GoogleAIService()
