from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime
from enum import Enum

class TableStatus(str, Enum):
    AVAILABLE = "available"
    OCCUPIED = "occupied"
    RESERVED = "reserved"
    CLEANING = "cleaning"
    OUT_OF_SERVICE = "out_of_service"

class TableLocation(str, Enum):
    INDOOR = "indoor"
    OUTDOOR = "outdoor"
    PATIO = "patio"
    BAR = "bar"
    PRIVATE_ROOM = "private_room"
    MAIN_FLOOR = "Main Floor"  # Match database values
    VIP_SECTION = "VIP Section"  # Match database values

class TableBase(BaseModel):
    number: int
    capacity: int
    location: Optional[str] = "indoor"
    section: Optional[str] = None  # e.g., "Main Dining", "Terrace", "VIP"
    shape: Optional[str] = "round"  # round, square, rectangular
    notes: Optional[str] = None

class TableCreate(TableBase):
    pass

class TableUpdate(BaseModel):
    number: Optional[int] = None
    capacity: Optional[int] = None
    location: Optional[str] = None
    section: Optional[str] = None
    shape: Optional[str] = None
    notes: Optional[str] = None

class Table(TableBase):
    id: str
    status: TableStatus = TableStatus.AVAILABLE
    current_order_id: Optional[str] = None
    reserved_until: Optional[datetime] = None
    last_cleaned: Optional[datetime] = None
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
