"""
Analytics models for RestroManage database.
Corresponds to app/models/analytics.py Pydantic models.
"""

from sqlalchemy import Column, String, Boolean, Float, Text, JSON, ForeignKey, Integer, DateTime, Date
from sqlalchemy.orm import relationship
from .base import BaseModel, TimestampMixin, StatusMixin

class SalesRecord(BaseModel, TimestampMixin):
    """
    Sales record model for analytics and reporting.
    Corresponds to SalesData Pydantic model in app/models/analytics.py
    """
    __tablename__ = "sales_records"
    
    # Restaurant association
    restaurant_id = Column(String(36), ForeignKey("restaurants.id"), nullable=False, index=True)
    
    # Time period
    record_date = Column(Date, nullable=False, index=True)
    record_hour = Column(Integer, nullable=True, index=True)  # 0-23 for hourly data
    
    # Sales metrics
    total_sales = Column(Float, nullable=False, default=0.0)
    total_orders = Column(Integer, nullable=False, default=0)
    average_order_value = Column(Float, nullable=True)
    
    # Customer metrics
    total_customers = Column(Integer, nullable=False, default=0)
    new_customers = Column(Integer, nullable=False, default=0)
    returning_customers = Column(Integer, nullable=False, default=0)
    
    # Order breakdown
    dine_in_orders = Column(Integer, nullable=False, default=0)
    takeaway_orders = Column(Integer, nullable=False, default=0)
    delivery_orders = Column(Integer, nullable=False, default=0)
    
    # Revenue breakdown
    food_revenue = Column(Float, nullable=False, default=0.0)
    beverage_revenue = Column(Float, nullable=False, default=0.0)
    tax_collected = Column(Float, nullable=False, default=0.0)
    tips_collected = Column(Float, nullable=False, default=0.0)
    
    # Discounts and promotions
    total_discounts = Column(Float, nullable=False, default=0.0)
    promo_code_usage = Column(Integer, nullable=False, default=0)
    discount_percentage = Column(Float, nullable=True)
    
    # Payment methods
    cash_payments = Column(Float, nullable=False, default=0.0)
    card_payments = Column(Float, nullable=False, default=0.0)
    digital_payments = Column(Float, nullable=False, default=0.0)
    
    # Operational metrics
    table_turnover_rate = Column(Float, nullable=True)
    average_service_time = Column(Float, nullable=True)  # minutes
    peak_hour_sales = Column(Float, nullable=True)
    
    # Staff performance
    staff_count = Column(Integer, nullable=True)
    sales_per_staff = Column(Float, nullable=True)
    
    # Popular items
    top_selling_items = Column(JSON, nullable=True)  # List of item IDs and quantities
    category_breakdown = Column(JSON, nullable=True)  # Sales by category
    
    # Weather and external factors
    weather_condition = Column(String(50), nullable=True)
    temperature = Column(Float, nullable=True)
    special_events = Column(JSON, nullable=True)  # List of events affecting sales
    
    # Relationships
    restaurant = relationship("Restaurant")
    
    def __repr__(self):
        return f"<SalesRecord(id={self.id}, date={self.record_date}, total_sales={self.total_sales})>"

class CustomerAnalytics(BaseModel, TimestampMixin):
    """
    Customer analytics model for customer behavior tracking.
    Corresponds to CustomerData Pydantic model in app/models/analytics.py
    """
    __tablename__ = "customer_analytics"
    
    # Restaurant association
    restaurant_id = Column(String(36), ForeignKey("restaurants.id"), nullable=False, index=True)
    
    # Time period
    analysis_date = Column(Date, nullable=False, index=True)
    analysis_period = Column(String(20), nullable=False)  # daily, weekly, monthly
    
    # Customer demographics
    total_unique_customers = Column(Integer, nullable=False, default=0)
    new_customers = Column(Integer, nullable=False, default=0)
    returning_customers = Column(Integer, nullable=False, default=0)
    loyal_customers = Column(Integer, nullable=False, default=0)  # 5+ visits
    
    # Visit patterns
    average_visits_per_customer = Column(Float, nullable=True)
    average_time_between_visits = Column(Float, nullable=True)  # days
    customer_retention_rate = Column(Float, nullable=True)  # percentage
    
    # Spending behavior
    average_spend_per_customer = Column(Float, nullable=True)
    average_spend_per_visit = Column(Float, nullable=True)
    customer_lifetime_value = Column(Float, nullable=True)
    
    # Order preferences
    average_party_size = Column(Float, nullable=True)
    preferred_dining_times = Column(JSON, nullable=True)  # Hour distribution
    preferred_order_types = Column(JSON, nullable=True)   # dine_in, takeaway, etc.
    
    # Popular choices
    popular_menu_items = Column(JSON, nullable=True)
    popular_categories = Column(JSON, nullable=True)
    dietary_preferences = Column(JSON, nullable=True)
    
    # Satisfaction metrics
    average_rating = Column(Float, nullable=True)
    positive_feedback_count = Column(Integer, nullable=False, default=0)
    negative_feedback_count = Column(Integer, nullable=False, default=0)
    complaint_rate = Column(Float, nullable=True)
    
    # Promotional response
    promo_code_usage_rate = Column(Float, nullable=True)
    discount_sensitivity = Column(Float, nullable=True)
    campaign_response_rate = Column(Float, nullable=True)
    
    # Churn analysis
    at_risk_customers = Column(Integer, nullable=False, default=0)
    churned_customers = Column(Integer, nullable=False, default=0)
    churn_rate = Column(Float, nullable=True)
    
    # Segmentation
    customer_segments = Column(JSON, nullable=True)  # Segment distribution
    high_value_customers = Column(Integer, nullable=False, default=0)
    frequent_customers = Column(Integer, nullable=False, default=0)
    
    # Acquisition channels
    acquisition_sources = Column(JSON, nullable=True)
    referral_rate = Column(Float, nullable=True)
    social_media_influence = Column(Float, nullable=True)
    
    # Relationships
    restaurant = relationship("Restaurant")
    
    def __repr__(self):
        return f"<CustomerAnalytics(id={self.id}, date={self.analysis_date}, total_customers={self.total_unique_customers})>"
