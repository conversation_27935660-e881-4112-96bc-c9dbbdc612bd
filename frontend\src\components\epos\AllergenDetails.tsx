import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { AlertTriangle } from "lucide-react";
import { toast } from "@/components/ui/sonner";

interface AllergenDetailsProps {
  isOpen: boolean;
  tableNumber: number;
  onConfirm: (allergens: string[]) => void;
  onBack: () => void;
  onClose?: () => void;
}

const commonAllergens = [
  { id: "dairy", label: "Dairy/Lactose", description: "Milk, cheese, yogurt, butter" },
  { id: "gluten", label: "Gluten", description: "Wheat, barley, rye, oats" },
  { id: "nuts", label: "Nuts/Tree Nuts", description: "Almonds, walnuts, pecans, etc." },
  { id: "peanuts", label: "Peanuts", description: "Peanuts and peanut products" },
  { id: "shellfish", label: "Shellfish", description: "Shrimp, crab, lobster, etc." },
  { id: "fish", label: "Fish", description: "All fish and fish products" },
  { id: "eggs", label: "Eggs", description: "Chicken eggs and egg products" },
  { id: "soy", label: "Soy", description: "Soybeans and soy products" },
  { id: "sesame", label: "Sesame", description: "Sesame seeds and products" },
  { id: "mustard", label: "Mustard", description: "Mustard seeds and products" },
];

const AllergenDetails: React.FC<AllergenDetailsProps> = ({
  isOpen,
  tableNumber,
  onConfirm,
  onBack,
  onClose
}) => {
  const [selectedAllergens, setSelectedAllergens] = useState<string[]>([]);
  const [customAllergen, setCustomAllergen] = useState("");

  const handleAllergenToggle = (allergenId: string) => {
    setSelectedAllergens(prev => 
      prev.includes(allergenId)
        ? prev.filter(id => id !== allergenId)
        : [...prev, allergenId]
    );
  };

  const handleConfirm = () => {
    let finalAllergens = [...selectedAllergens];
    
    // Add custom allergen if provided
    if (customAllergen.trim()) {
      finalAllergens.push(`custom:${customAllergen.trim()}`);
    }

    if (finalAllergens.length === 0) {
      toast.error("Please select at least one allergen or go back to select 'No Allergies'");
      return;
    }

    onConfirm(finalAllergens);
    setSelectedAllergens([]);
    setCustomAllergen("");
  };

  const handleBack = () => {
    setSelectedAllergens([]);
    setCustomAllergen("");
    onBack();
  };

  const handleOpenChange = (open: boolean) => {
    if (!open && onClose) {
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-2xl max-h-[80vh] overflow-y-auto [&>button]:hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="w-5 h-5 text-red-500" />
            Allergen Details - Table {tableNumber}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            Please select all applicable allergies and dietary restrictions for this table:
          </p>

          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-start gap-2">
              <AlertTriangle className="w-4 h-4 text-red-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-red-800">
                <p className="font-medium">Critical Information:</p>
                <p>This information will be shared with the kitchen staff to ensure safe food preparation.</p>
              </div>
            </div>
          </div>

          {/* Common Allergens */}
          <div className="space-y-3">
            <Label className="text-base font-semibold">Common Allergens:</Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {commonAllergens.map((allergen) => (
                <div key={allergen.id} className="flex items-start space-x-3 p-3 border rounded-lg hover:bg-gray-50">
                  <Checkbox
                    id={allergen.id}
                    checked={selectedAllergens.includes(allergen.id)}
                    onCheckedChange={() => handleAllergenToggle(allergen.id)}
                    className="mt-1"
                  />
                  <div className="flex-1 min-w-0">
                    <Label
                      htmlFor={allergen.id}
                      className="text-sm font-medium cursor-pointer"
                    >
                      {allergen.label}
                    </Label>
                    <p className="text-xs text-muted-foreground mt-1">
                      {allergen.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Custom Allergen */}
          <div className="space-y-2">
            <Label htmlFor="custom-allergen" className="text-base font-semibold">
              Other Allergies/Restrictions:
            </Label>
            <Input
              id="custom-allergen"
              value={customAllergen}
              onChange={(e) => setCustomAllergen(e.target.value)}
              placeholder="Please specify any other allergies or dietary restrictions..."
              className="w-full"
            />
          </div>

          {/* Selected Summary */}
          {(selectedAllergens.length > 0 || customAllergen.trim()) && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <Label className="text-sm font-medium text-blue-800">Selected Allergens:</Label>
              <div className="mt-2 flex flex-wrap gap-2">
                {selectedAllergens.map(id => {
                  const allergen = commonAllergens.find(a => a.id === id);
                  return allergen ? (
                    <span key={id} className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                      {allergen.label}
                    </span>
                  ) : null;
                })}
                {customAllergen.trim() && (
                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                    {customAllergen.trim()}
                  </span>
                )}
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleBack}>
            Back
          </Button>
          <Button 
            onClick={handleConfirm}
            disabled={selectedAllergens.length === 0 && !customAllergen.trim()}
            className="bg-red-600 hover:bg-red-700"
          >
            Confirm Allergens
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AllergenDetails;
