import React from "react";
import { useState, useEffect } from "react";
import { format, startOfWeek, endOfWeek, eachDayOfInterval, addDays, isSameDay } from "date-fns";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { StaffUnavailability } from "@/types/staffAvailability";
import { getUnavailabilityForWeek, getUnavailabilityColor } from "@/services/staffAvailabilityService";
import AvailabilityLegend from "./AvailabilityLegend";

interface AvailabilityCalendarWeekViewProps {
  staffData: { id: string; name: string }[];
  onDateSelect?: (date: Date) => void;
}

const AvailabilityCalendarWeekView: React.FC<AvailabilityCalendarWeekViewProps> = ({
  staffData,
  onDateSelect,
}) => {
  const [currentWeekStart, setCurrentWeekStart] = useState<Date>(
    startOfWeek(new Date(), { weekStartsOn: 1 })
  );
  const [unavailability, setUnavailability] = useState<StaffUnavailability[]>([]);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);

  const currentWeekEnd = endOfWeek(currentWeekStart, { weekStartsOn: 1 });
  const weekDays = eachDayOfInterval({
    start: currentWeekStart,
    end: currentWeekEnd
  });

  useEffect(() => {
    const weekUnavailability = getUnavailabilityForWeek(currentWeekStart);
    setUnavailability(weekUnavailability);
  }, [currentWeekStart]);

  const handlePreviousWeek = () => {
    setCurrentWeekStart(prevWeekStart => addDays(prevWeekStart, -7));
  };

  const handleNextWeek = () => {
    setCurrentWeekStart(prevWeekStart => addDays(prevWeekStart, 7));
  };

  const handleDateClick = (date: Date) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const selectedDateOnly = new Date(date);
    selectedDateOnly.setHours(0, 0, 0, 0);
    
    if (selectedDateOnly >= today) {
      setSelectedDate(date);
      onDateSelect?.(date);
    }
  };

  const getUnavailabilityForDay = (date: Date) => {
    const dateString = format(date, 'yyyy-MM-dd');
    return unavailability.filter(u => u.date.startsWith(dateString));
  };

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-medium">Weekly Staff Availability</CardTitle>
          <div className="flex items-center gap-1">
            <Button variant="outline" size="icon" onClick={handlePreviousWeek}>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <span className="text-sm font-medium min-w-[120px] text-center">
              {format(currentWeekStart, 'MMM dd')} - {format(currentWeekEnd, 'MMM dd, yyyy')}
            </span>
            <Button variant="outline" size="icon" onClick={handleNextWeek}>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-7 gap-2">
            {weekDays.map((day) => {
              const dayUnavailability = getUnavailabilityForDay(day);
              const isSelected = selectedDate ? isSameDay(day, selectedDate) : false;

              const today = new Date();
              today.setHours(0, 0, 0, 0);
              const dayOnly = new Date(day);
              dayOnly.setHours(0, 0, 0, 0);
              const isPastDate = dayOnly < today;

              const countByType: Record<string, number> = {};
              dayUnavailability.forEach(u => {
                countByType[u.type] = (countByType[u.type] || 0) + 1;
              });

              return (
                <div 
                  key={day.toISOString()}
                  className={`p-2 border rounded-md ${isPastDate ? "cursor-not-allowed opacity-50 bg-gray-100" : "cursor-pointer"} ${
                    isSelected ? 'border-primary bg-primary/10' : 'hover:bg-accent'
                  }`}
                  onClick={() => !isPastDate && handleDateClick(day)}
                >
                  <div className="text-center">
                    <div className="font-medium">{format(day, 'EEE')}</div>
                    <div className="text-sm">{format(day, 'dd/MM')}</div>
                  </div>

                  {Object.entries(countByType).length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2 justify-center">
                      {Object.entries(countByType).map(([type, count]) => (
                        <Badge
                          key={type}
                          variant="secondary"
                          className="text-xs px-1 py-0"
                          style={{ backgroundColor: getUnavailabilityColor(type as any) }}
                        >
                          {count}
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>
              );
            })}
          </div>

          <AvailabilityLegend />
        </div>
      </CardContent>
    </Card>
  );
};

export default AvailabilityCalendarWeekView;
