import React, { useState, useRef, useCallback } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Upload, X, Image, AlertCircle, Check } from "lucide-react";
import { DEFAULT_LOGO_CONFIG, LogoUploadConfig } from "@/types/restaurantSetup";

interface LogoUploadProps {
  onLogoChange: (file: File | null, url: string | null) => void;
  currentLogo?: string | null;
  config?: LogoUploadConfig;
  className?: string;
}

const LogoUpload: React.FC<LogoUploadProps> = ({
  onLogoChange,
  currentLogo,
  config = DEFAULT_LOGO_CONFIG,
  className = "",
}) => {
  const [dragActive, setDragActive] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(currentLogo || null);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateFile = (file: File): string | null => {
    // Check file size
    if (file.size > config.maxSize) {
      return `File size must be less than ${(config.maxSize / (1024 * 1024)).toFixed(1)}MB`;
    }

    // Check file format
    if (!config.allowedFormats.includes(file.type)) {
      return `File must be one of: ${config.allowedFormats.map(f => f.split('/')[1].toUpperCase()).join(', ')}`;
    }

    return null;
  };

  const handleFile = useCallback(async (file: File) => {
    setUploadError(null);
    setIsUploading(true);

    const validationError = validateFile(file);
    if (validationError) {
      setUploadError(validationError);
      setIsUploading(false);
      return;
    }

    try {
      // Create preview URL
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
      
      // Call parent callback
      onLogoChange(file, url);
      
    } catch (error) {
      setUploadError("Failed to process image. Please try again.");
    } finally {
      setIsUploading(false);
    }
  }, [onLogoChange, config]);

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFile(e.dataTransfer.files[0]);
    }
  }, [handleFile]);

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFile(e.target.files[0]);
    }
  };

  const removeLogo = () => {
    setPreviewUrl(null);
    setUploadError(null);
    onLogoChange(null, null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Image className="h-5 w-5" />
          Restaurant Logo
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Upload Area */}
        <div
          className={`relative border-2 border-dashed rounded-lg p-6 transition-colors ${
            dragActive
              ? "border-primary bg-primary/5"
              : "border-gray-300 hover:border-gray-400"
          } ${isUploading ? "opacity-50 pointer-events-none" : ""}`}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          {previewUrl ? (
            // Preview Area
            <div className="text-center space-y-4">
              <div className="relative inline-block">
                <img
                  src={previewUrl}
                  alt="Logo preview"
                  className="max-w-32 max-h-32 object-contain mx-auto rounded-lg border"
                />
                <Button
                  type="button"
                  variant="destructive"
                  size="sm"
                  className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                  onClick={removeLogo}
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-center gap-2 text-green-600">
                  <Check className="h-4 w-4" />
                  <span className="text-sm font-medium">Logo uploaded successfully</span>
                </div>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={openFileDialog}
                >
                  Change Logo
                </Button>
              </div>
            </div>
          ) : (
            // Upload Area
            <div className="text-center space-y-4">
              <div className="mx-auto w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                <Upload className="h-6 w-6 text-gray-400" />
              </div>
              <div className="space-y-2">
                <h3 className="text-lg font-medium">Upload Restaurant Logo</h3>
                <p className="text-sm text-gray-500">
                  Drag and drop your logo here, or click to browse
                </p>
                <div className="flex flex-wrap justify-center gap-2">
                  {config.allowedFormats.map((format) => (
                    <Badge key={format} variant="secondary" className="text-xs">
                      {format.split('/')[1].toUpperCase()}
                    </Badge>
                  ))}
                </div>
                <p className="text-xs text-gray-400">
                  Max size: {(config.maxSize / (1024 * 1024)).toFixed(1)}MB
                </p>
              </div>
              <Button
                type="button"
                variant="outline"
                onClick={openFileDialog}
                disabled={isUploading}
              >
                {isUploading ? "Uploading..." : "Choose File"}
              </Button>
            </div>
          )}

          {/* Hidden file input */}
          <input
            ref={fileInputRef}
            type="file"
            className="hidden"
            accept={config.allowedFormats.join(",")}
            onChange={handleFileInput}
          />
        </div>

        {/* Error Display */}
        {uploadError && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{uploadError}</AlertDescription>
          </Alert>
        )}

        {/* Recommendations */}
        <div className="text-xs text-gray-500 space-y-1">
          <p>• Recommended dimensions: {config.recommendedDimensions.width}x{config.recommendedDimensions.height}px</p>
          <p>• Square logos work best for consistent display</p>
          <p>• High contrast logos are more readable</p>
        </div>
      </CardContent>
    </Card>
  );
};

export default LogoUpload;
