"""
Forecasting models for RestroManage database.
Corresponds to app/models/forecasting.py Pydantic models.
"""

from sqlalchemy import Column, String, Boolean, Float, Text, JSON, ForeignKey, Integer, DateTime, Date
from sqlalchemy.orm import relationship
from .base import BaseModel, TimestampMixin, StatusMixin

class ForecastData(BaseModel, TimestampMixin, StatusMixin):
    """
    Forecast data model for predictive analytics.
    Corresponds to ForecastData Pydantic model in app/models/forecasting.py
    """
    __tablename__ = "forecast_data"
    
    # Restaurant association
    restaurant_id = Column(String(36), ForeignKey("restaurants.id"), nullable=False, index=True)
    
    # Forecast identification
    forecast_type = Column(String(50), nullable=False, index=True)
    # Types: sales, customer_traffic, inventory_demand, staff_scheduling, seasonal_trends
    forecast_period = Column(String(20), nullable=False, index=True)
    # Periods: hourly, daily, weekly, monthly, quarterly, yearly
    
    # Time range
    forecast_date = Column(Date, nullable=False, index=True)
    forecast_start = Column(DateTime(timezone=True), nullable=False, index=True)
    forecast_end = Column(DateTime(timezone=True), nullable=False, index=True)
    
    # Model information
    model_name = Column(String(100), nullable=False)
    model_version = Column(String(20), nullable=False)
    algorithm_used = Column(String(50), nullable=False)
    # Algorithms: linear_regression, arima, lstm, prophet, ensemble
    
    # Forecast metrics
    predicted_value = Column(Float, nullable=False)
    confidence_interval_lower = Column(Float, nullable=True)
    confidence_interval_upper = Column(Float, nullable=True)
    confidence_level = Column(Float, default=0.95, nullable=False)
    
    # Accuracy metrics
    accuracy_score = Column(Float, nullable=True)
    mean_absolute_error = Column(Float, nullable=True)
    root_mean_square_error = Column(Float, nullable=True)
    
    # Input data summary
    training_data_points = Column(Integer, nullable=False)
    training_period_start = Column(DateTime(timezone=True), nullable=False)
    training_period_end = Column(DateTime(timezone=True), nullable=False)
    
    # Seasonal and trend components
    trend_component = Column(Float, nullable=True)
    seasonal_component = Column(Float, nullable=True)
    cyclical_component = Column(Float, nullable=True)
    irregular_component = Column(Float, nullable=True)
    
    # External factors considered
    weather_factor = Column(Float, nullable=True)
    holiday_factor = Column(Float, nullable=True)
    event_factor = Column(Float, nullable=True)
    economic_factor = Column(Float, nullable=True)
    
    # Detailed predictions (JSON format)
    hourly_breakdown = Column(JSON, nullable=True)
    daily_breakdown = Column(JSON, nullable=True)
    category_breakdown = Column(JSON, nullable=True)
    
    # Actual vs predicted (for completed forecasts)
    actual_value = Column(Float, nullable=True)
    prediction_error = Column(Float, nullable=True)
    error_percentage = Column(Float, nullable=True)
    
    # Business impact
    recommended_actions = Column(JSON, nullable=True)
    risk_factors = Column(JSON, nullable=True)
    opportunity_indicators = Column(JSON, nullable=True)
    
    # Model parameters
    model_parameters = Column(JSON, nullable=True)
    feature_importance = Column(JSON, nullable=True)
    
    # Status and validation
    is_validated = Column(Boolean, default=False, nullable=False)
    validation_date = Column(DateTime(timezone=True), nullable=True)
    validation_notes = Column(Text, nullable=True)
    
    # Relationships
    restaurant = relationship("Restaurant")
    
    def __repr__(self):
        return f"<ForecastData(id={self.id}, type={self.forecast_type}, predicted_value={self.predicted_value})>"

class TrendAnalysis(BaseModel, TimestampMixin, StatusMixin):
    """
    Trend analysis model for identifying patterns and trends.
    Corresponds to TrendAnalysis Pydantic model in app/models/forecasting.py
    """
    __tablename__ = "trend_analysis"
    
    # Restaurant association
    restaurant_id = Column(String(36), ForeignKey("restaurants.id"), nullable=False, index=True)
    
    # Analysis identification
    analysis_type = Column(String(50), nullable=False, index=True)
    # Types: sales_trend, customer_trend, menu_performance, seasonal_pattern
    analysis_period = Column(String(20), nullable=False, index=True)
    # Periods: weekly, monthly, quarterly, yearly
    
    # Time range analyzed
    analysis_start = Column(Date, nullable=False, index=True)
    analysis_end = Column(Date, nullable=False, index=True)
    data_points_analyzed = Column(Integer, nullable=False)
    
    # Trend direction and strength
    trend_direction = Column(String(20), nullable=False)  # increasing, decreasing, stable, volatile
    trend_strength = Column(Float, nullable=False)        # 0.0 to 1.0
    trend_significance = Column(Float, nullable=False)    # p-value or confidence score
    
    # Statistical measures
    slope = Column(Float, nullable=True)                  # Rate of change
    correlation_coefficient = Column(Float, nullable=True)
    r_squared = Column(Float, nullable=True)
    
    # Trend components
    linear_trend = Column(Float, nullable=True)
    seasonal_trend = Column(JSON, nullable=True)          # Seasonal patterns
    cyclical_trend = Column(JSON, nullable=True)          # Cyclical patterns
    
    # Peak and trough analysis
    peak_values = Column(JSON, nullable=True)             # Identified peaks
    trough_values = Column(JSON, nullable=True)           # Identified troughs
    volatility_measure = Column(Float, nullable=True)
    
    # Growth metrics
    growth_rate = Column(Float, nullable=True)            # Percentage growth
    compound_annual_growth_rate = Column(Float, nullable=True)
    period_over_period_change = Column(Float, nullable=True)
    
    # Comparative analysis
    industry_benchmark = Column(Float, nullable=True)
    performance_vs_benchmark = Column(Float, nullable=True)
    percentile_ranking = Column(Float, nullable=True)
    
    # Anomaly detection
    anomalies_detected = Column(JSON, nullable=True)      # List of anomalous data points
    anomaly_score = Column(Float, nullable=True)
    outlier_count = Column(Integer, default=0, nullable=False)
    
    # Forecasting implications
    short_term_outlook = Column(String(20), nullable=True)  # positive, negative, neutral
    medium_term_outlook = Column(String(20), nullable=True)
    long_term_outlook = Column(String(20), nullable=True)
    
    # Business insights
    key_insights = Column(JSON, nullable=True)
    recommended_actions = Column(JSON, nullable=True)
    risk_assessment = Column(JSON, nullable=True)
    
    # Segmentation analysis
    segment_trends = Column(JSON, nullable=True)          # Trends by customer segment
    product_trends = Column(JSON, nullable=True)          # Trends by product category
    geographic_trends = Column(JSON, nullable=True)       # Trends by location
    
    # External factor correlation
    weather_correlation = Column(Float, nullable=True)
    holiday_impact = Column(Float, nullable=True)
    economic_correlation = Column(Float, nullable=True)
    marketing_impact = Column(Float, nullable=True)
    
    # Confidence and reliability
    confidence_score = Column(Float, nullable=False)
    data_quality_score = Column(Float, nullable=False)
    reliability_rating = Column(String(10), nullable=False)  # high, medium, low
    
    # Relationships
    restaurant = relationship("Restaurant")
    
    def __repr__(self):
        return f"<TrendAnalysis(id={self.id}, type={self.analysis_type}, trend_direction={self.trend_direction})>"
