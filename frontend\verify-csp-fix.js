// CSP Fix Verification Script for RestroManage-v0
// Run this script in the browser console to verify the CSP fix

console.log('🛡️ CSP Fix Verification Script');
console.log('================================');

// Track CSP violations
let cspViolations = [];
document.addEventListener('securitypolicyviolation', (e) => {
    cspViolations.push({
        blockedURI: e.blockedURI,
        violatedDirective: e.violatedDirective,
        originalPolicy: e.originalPolicy,
        timestamp: new Date().toISOString()
    });
    console.warn('🚨 CSP Violation:', e);
});

// Test functions
const tests = {
    async testFrontendConnection() {
        console.log('\n📡 Testing Frontend Connection...');
        try {
            const response = await fetch('http://localhost:5174/');
            if (response.ok) {
                console.log('✅ Frontend (5174): Connected successfully');
                return true;
            } else {
                console.log(`❌ Frontend (5174): HTTP ${response.status}`);
                return false;
            }
        } catch (error) {
            console.log(`❌ Frontend (5174): ${error.message}`);
            return false;
        }
    },

    async testBackendConnection() {
        console.log('\n🔧 Testing Backend Connection...');
        try {
            const response = await fetch('http://localhost:5001/health');
            if (response.ok) {
                const data = await response.json();
                console.log('✅ Backend (5001): Connected successfully');
                console.log('📊 Backend Status:', data);
                return true;
            } else {
                console.log(`❌ Backend (5001): HTTP ${response.status}`);
                return false;
            }
        } catch (error) {
            console.log(`❌ Backend (5001): ${error.message}`);
            return false;
        }
    },

    async testAPIProxy() {
        console.log('\n🔄 Testing API Proxy...');
        try {
            const response = await fetch('/api/restaurants');
            if (response.ok) {
                console.log('✅ API Proxy: Working correctly');
                return true;
            } else {
                console.log(`❌ API Proxy: HTTP ${response.status}`);
                return false;
            }
        } catch (error) {
            console.log(`❌ API Proxy: ${error.message}`);
            return false;
        }
    },

    async testWebSocketConnection() {
        console.log('\n🔌 Testing WebSocket Connection...');
        return new Promise((resolve) => {
            try {
                const ws = new WebSocket('ws://localhost:5174');
                
                ws.onopen = () => {
                    console.log('✅ WebSocket: Connected successfully');
                    ws.close();
                    resolve(true);
                };
                
                ws.onerror = (error) => {
                    console.log('❌ WebSocket: Connection failed');
                    resolve(false);
                };
                
                // Timeout after 3 seconds
                setTimeout(() => {
                    if (ws.readyState === WebSocket.CONNECTING) {
                        ws.close();
                        console.log('⏰ WebSocket: Connection timeout');
                        resolve(false);
                    }
                }, 3000);
            } catch (error) {
                console.log(`❌ WebSocket: ${error.message}`);
                resolve(false);
            }
        });
    },

    checkCSPViolations() {
        console.log('\n🛡️ Checking CSP Violations...');
        if (cspViolations.length === 0) {
            console.log('✅ CSP: No violations detected');
            return true;
        } else {
            console.log(`❌ CSP: ${cspViolations.length} violations detected:`);
            cspViolations.forEach((violation, index) => {
                console.log(`   ${index + 1}. ${violation.violatedDirective}: ${violation.blockedURI}`);
            });
            return false;
        }
    },

    async simulateCompleteSetup() {
        console.log('\n🏗️ Simulating Complete Setup Process...');
        
        // Test the backend connectivity check that happens during setup
        try {
            const connectivityCheck = await fetch('http://localhost:5001/health');
            if (connectivityCheck.ok) {
                console.log('✅ Setup Simulation: Backend connectivity check passed');
                
                // Test a typical API call that would happen during setup
                const apiTest = await fetch('/api/restaurants');
                if (apiTest.ok || apiTest.status === 404) { // 404 is OK if no restaurants exist
                    console.log('✅ Setup Simulation: API calls working');
                    return true;
                } else {
                    console.log(`❌ Setup Simulation: API call failed with ${apiTest.status}`);
                    return false;
                }
            } else {
                console.log(`❌ Setup Simulation: Backend connectivity failed with ${connectivityCheck.status}`);
                return false;
            }
        } catch (error) {
            console.log(`❌ Setup Simulation: ${error.message}`);
            return false;
        }
    }
};

// Main verification function
async function verifyCSPFix() {
    console.log('\n🚀 Starting CSP Fix Verification...');
    console.log('Time:', new Date().toLocaleString());
    
    const results = {
        frontend: await tests.testFrontendConnection(),
        backend: await tests.testBackendConnection(),
        apiProxy: await tests.testAPIProxy(),
        websocket: await tests.testWebSocketConnection(),
        setupSimulation: await tests.simulateCompleteSetup()
    };
    
    // Wait a moment for any CSP violations to be detected
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    results.csp = tests.checkCSPViolations();
    
    console.log('\n📊 VERIFICATION SUMMARY');
    console.log('=======================');
    
    const allPassed = Object.values(results).every(result => result === true);
    
    Object.entries(results).forEach(([test, passed]) => {
        const status = passed ? '✅ PASS' : '❌ FAIL';
        const testName = test.charAt(0).toUpperCase() + test.slice(1);
        console.log(`${status} - ${testName}`);
    });
    
    console.log('\n' + '='.repeat(50));
    
    if (allPassed) {
        console.log('🎉 ALL TESTS PASSED!');
        console.log('✅ The CSP fix is working correctly');
        console.log('✅ Complete Setup functionality should work without CSP violations');
        console.log('\n📝 Next Steps:');
        console.log('1. Navigate to: http://localhost:5174/auth/register');
        console.log('2. Complete the registration form');
        console.log('3. Click "Complete Setup" button');
        console.log('4. Verify no CSP violations appear in console');
    } else {
        console.log('⚠️ SOME TESTS FAILED');
        console.log('❌ Please check the failed tests above');
        console.log('\n🔧 Troubleshooting:');
        console.log('1. Ensure both servers are running:');
        console.log('   - Frontend: http://localhost:5174');
        console.log('   - Backend: http://localhost:5001');
        console.log('2. Check for any error messages in the console');
        console.log('3. Verify the CSP configuration in index.html and vite.config.ts');
    }
    
    return allPassed;
}

// Export for manual use
window.verifyCSPFix = verifyCSPFix;
window.cspTests = tests;

// Auto-run verification
console.log('🔄 Auto-running verification in 2 seconds...');
setTimeout(verifyCSPFix, 2000);
