{"root": ["./src/app.tsx", "./src/main.tsx", "./src/types.ts", "./src/vite-env.d.ts", "./src/components/approuter.tsx", "./src/components/logviewer.tsx", "./src/components/timeentriestab.tsx", "./src/components/admin/aipromosuggestions.tsx", "./src/components/admin/admindiscountmanagement.tsx", "./src/components/admin/promocodemanagement.tsx", "./src/components/admin/restaurantmanagement.tsx", "./src/components/admin/simpletimeentryeditor.tsx", "./src/components/admin/staffhourssummary.tsx", "./src/components/admin/stafftimeentries.tsx", "./src/components/admin/tasklist.tsx", "./src/components/admin/timeentryeditor.tsx", "./src/components/admin/timeentryeditor_debug.tsx", "./src/components/ai/minimalaichat.tsx", "./src/components/ai/simpleaiwrapper.tsx", "./src/components/ai/ultraminimalchat.tsx", "./src/components/allergens/allergenbadge.tsx", "./src/components/allergens/allergenselector.tsx", "./src/components/allergens/index.ts", "./src/components/analytics/categoryperformancecard.tsx", "./src/components/analytics/peakhoursanalysiscard.tsx", "./src/components/analytics/reportssection.tsx", "./src/components/analytics/index.tsx", "./src/components/analytics/scheduling/laborcostanalysis.tsx", "./src/components/analytics/scheduling/staffscheduleanalytics.tsx", "./src/components/analytics/scheduling/staffutilizationchart.tsx", "./src/components/analytics/scheduling/availability/availabilitycalendarmonthview.tsx", "./src/components/analytics/scheduling/availability/availabilitycalendarweekview.tsx", "./src/components/analytics/scheduling/availability/availabilitylegend.tsx", "./src/components/analytics/scheduling/availability/schedulingconflictnotification.tsx", "./src/components/analytics/scheduling/availability/staffavailabilitycalendar.tsx", "./src/components/analytics/scheduling/availability/timeoffrequestform.tsx", "./src/components/assistant/virtualassistant.tsx", "./src/components/auth/initializingloader.tsx", "./src/components/auth/logoupload.tsx", "./src/components/auth/protectedroute.tsx", "./src/components/auth/tableconfiguration.tsx", "./src/components/dashboard/advancedforecast.tsx", "./src/components/dashboard/customerfeedbackcard.tsx", "./src/components/dashboard/customertrafficcard.tsx", "./src/components/dashboard/daterangeselector.tsx", "./src/components/dashboard/forecastcard.tsx", "./src/components/dashboard/ingredientforecastcard.tsx", "./src/components/dashboard/inventoryalertcard.tsx", "./src/components/dashboard/popularitemscard.tsx", "./src/components/dashboard/revenueprojectioncard.tsx", "./src/components/dashboard/salespredictioncard.tsx", "./src/components/dashboard/scenarioplanningcard.tsx", "./src/components/dashboard/seasonaltrendcard.tsx", "./src/components/dashboard/staffperformancecard.tsx", "./src/components/dashboard/statcard.tsx", "./src/components/dashboard/tablestatuscard.tsx", "./src/components/debug/authdebug.tsx", "./src/components/epos/allergendetails.tsx", "./src/components/epos/allergendisplay.tsx", "./src/components/epos/allergeninfocollection.tsx", "./src/components/epos/cartitemoptions.tsx", "./src/components/epos/discountmanager.tsx", "./src/components/epos/eposlandingpage.tsx", "./src/components/epos/enhancedtableselection.tsx", "./src/components/epos/enhancedtablestatus.tsx", "./src/components/epos/foodinfomodal.tsx", "./src/components/epos/managerreporting.tsx", "./src/components/epos/ordertypeselection.tsx", "./src/components/epos/partysizeinput.tsx", "./src/components/epos/paymentprocessing.tsx", "./src/components/epos/receiptprinter.tsx", "./src/components/epos/splitbillmanager.tsx", "./src/components/epos/staffnotificationsystem.tsx", "./src/components/epos/tablemanagement.tsx", "./src/components/epos/tableselection.tsx", "./src/components/inventory/fileimportdialog.tsx", "./src/components/inventory/forecastedinventory.tsx", "./src/components/inventory/inventoryforecastdashboard.tsx", "./src/components/layout/header.tsx", "./src/components/layout/layout.tsx", "./src/components/layout/sidebar.tsx", "./src/components/marketing/marketinglayout.tsx", "./src/components/menu/inventoryingredientselector.tsx", "./src/components/notifications/notificationdropdown.tsx", "./src/components/notifications/notificationitem.tsx", "./src/components/onboarding/onboardingsteps.tsx", "./src/components/onboarding/subscriptionplanstep.tsx", "./src/components/schedule/forecastbasedscheduler.tsx", "./src/components/staff/enhancedstafftimeclock.tsx", "./src/components/staff/enhancedstafftimeclock_updated.tsx", "./src/components/staff/pinmanagement.tsx", "./src/components/staff/simplethingstodotab.tsx", "./src/components/staff/staffavailabilitysettings.tsx", "./src/components/staff/staffcalendar.tsx", "./src/components/staff/staffhourstab.tsx", "./src/components/staff/staffhourstab_updated.tsx", "./src/components/staff/staffhourstracking.tsx", "./src/components/staff/staffhourstracking_fixed.tsx", "./src/components/staff/staffhourstracking_updated.tsx", "./src/components/staff/staffperformancedetails.tsx", "./src/components/staff/staffschedule.tsx", "./src/components/staff/staffsettings.tsx", "./src/components/staff/stafftimeclock.tsx", "./src/components/staff/testsettings.tsx", "./src/components/staff/thingstodotab.tsx", "./src/components/subscription/featuregate.tsx", "./src/components/subscription/upgradeprompt.tsx", "./src/components/ui/accordion.tsx", "./src/components/ui/alert-dialog.tsx", "./src/components/ui/alert.tsx", "./src/components/ui/aspect-ratio.tsx", "./src/components/ui/avatar.tsx", "./src/components/ui/badge.tsx", "./src/components/ui/breadcrumb.tsx", "./src/components/ui/button.tsx", "./src/components/ui/calendar.tsx", "./src/components/ui/card.tsx", "./src/components/ui/carousel.tsx", "./src/components/ui/chart.tsx", "./src/components/ui/checkbox.tsx", "./src/components/ui/collapsible.tsx", "./src/components/ui/command.tsx", "./src/components/ui/context-menu.tsx", "./src/components/ui/dialog.tsx", "./src/components/ui/drawer.tsx", "./src/components/ui/dropdown-menu.tsx", "./src/components/ui/form.tsx", "./src/components/ui/hover-card.tsx", "./src/components/ui/input-otp.tsx", "./src/components/ui/input.tsx", "./src/components/ui/label.tsx", "./src/components/ui/menubar.tsx", "./src/components/ui/navigation-menu.tsx", "./src/components/ui/pagination.tsx", "./src/components/ui/popover.tsx", "./src/components/ui/progress.tsx", "./src/components/ui/radio-group.tsx", "./src/components/ui/resizable.tsx", "./src/components/ui/scroll-area.tsx", "./src/components/ui/select.tsx", "./src/components/ui/separator.tsx", "./src/components/ui/sheet.tsx", "./src/components/ui/sidebar.tsx", "./src/components/ui/skeleton.tsx", "./src/components/ui/slider.tsx", "./src/components/ui/sonner.tsx", "./src/components/ui/switch.tsx", "./src/components/ui/table.tsx", "./src/components/ui/tabs.tsx", "./src/components/ui/textarea.tsx", "./src/components/ui/toast.tsx", "./src/components/ui/toaster.tsx", "./src/components/ui/toggle-group.tsx", "./src/components/ui/toggle.tsx", "./src/components/ui/tooltip.tsx", "./src/components/ui/use-toast.ts", "./src/config/featureflags.ts", "./src/config/subscriptionplans.ts", "./src/contexts/authcontext.tsx", "./src/contexts/notificationcontext.tsx", "./src/contexts/restaurantcontext.tsx", "./src/contexts/staffpincontext.tsx", "./src/hooks/use-mobile.tsx", "./src/hooks/use-toast.ts", "./src/hooks/usesubscriptionapi.tsx", "./src/hooks/usesubscriptionaccess.tsx", "./src/lib/utils.ts", "./src/pages/admindashboard.tsx", "./src/pages/analytics.tsx", "./src/pages/assistant.tsx", "./src/pages/dashboard.tsx", "./src/pages/epos.tsx", "./src/pages/importdialog.tsx", "./src/pages/index.tsx", "./src/pages/inventory.tsx", "./src/pages/lightweightassistant.tsx", "./src/pages/notfound.tsx", "./src/pages/schedule.tsx", "./src/pages/settings.tsx", "./src/pages/signup.tsx", "./src/pages/staff.tsx", "./src/pages/staffdashboard.tsx", "./src/pages/staffdetails.tsx", "./src/pages/staffsettings.tsx", "./src/pages/stafftaskspage.tsx", "./src/pages/tableturnover.tsx", "./src/pages/admin/dashboard.tsx", "./src/pages/admin/discountmanagement.tsx", "./src/pages/admin/menumanagement.tsx", "./src/pages/admin/taskmanagement.tsx", "./src/pages/auth/login.tsx", "./src/pages/auth/register.tsx", "./src/pages/auth/registrationsuccess.tsx", "./src/pages/auth/restaurantlogin.tsx", "./src/pages/auth/restaurantselect.tsx", "./src/pages/auth/restaurantsetup.tsx", "./src/pages/auth/restaurantsetuptest.tsx", "./src/pages/auth/restaurentsetuptest.tsx", "./src/pages/auth/stafflogin.tsx", "./src/pages/auth/staffpin.tsx", "./src/pages/customer/menu.tsx", "./src/pages/customer/reservation.tsx", "./src/pages/dashboard/restaurantdashboard.tsx", "./src/pages/marketing/contact.tsx", "./src/pages/marketing/features.tsx", "./src/pages/marketing/home.tsx", "./src/pages/marketing/pricing.tsx", "./src/pages/onboarding/newrestaurantonboarding.tsx", "./src/services/aiassistantservice.ts", "./src/services/allergenservice.ts", "./src/services/api.ts", "./src/services/apiservice.ts", "./src/services/authservice.ts", "./src/services/clockstatusservice.ts", "./src/services/dataservice.ts", "./src/services/filestorageservice.ts", "./src/services/forecastscheduleservice.ts", "./src/services/inventoryforecastservice.ts", "./src/services/lightweightai.ts", "./src/services/mockdataservice.ts", "./src/services/mocktimeentries.ts", "./src/services/notificationservice.ts", "./src/services/staffavailabilityservice.ts", "./src/services/tablesessionservice.ts", "./src/services/taskservice.ts", "./src/services/timeentryservice.ts", "./src/types/inventoryforecast.ts", "./src/types/notification.ts", "./src/types/restaurantsetup.ts", "./src/types/staffavailability.ts", "./src/utils/authutils.ts", "./src/utils/csvparser.ts", "./src/utils/fileupload.ts", "./src/utils/logger.ts", "./src/utils/notificationutils.ts"], "version": "5.6.3"}