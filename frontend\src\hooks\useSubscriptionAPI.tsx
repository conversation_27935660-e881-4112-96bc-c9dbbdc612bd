// New API-based Subscription Hook - Replaces useSubscriptionAccess

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { useCallback, useMemo } from 'react';
import logger from '@/utils/logger';

// API Types
interface SubscriptionResponse {
  restaurant_id: string;
  plan_id: string;
  plan_name: string;
  status: string;
  starts_at: string;
  expires_at: string;
  trial_ends_at?: string;
  custom_features: string[];
  days_remaining: number;
  is_trial: boolean;
  is_active: boolean;
}

interface FeatureAccessResponse {
  restaurant_id: string;
  plan_id: string;
  available_features: string[];
  total_features: number;
  access_level: string;
}

interface FeatureCheckResponse {
  feature_id: string;
  has_access: boolean;
  reason: string;
  plan_id: string;
  timestamp: string;
}

// API Client
class SubscriptionAPI {
  private baseURL = import.meta.env.DEV
    ? '' // Use proxy in development (Vite will proxy to backend)
    : (import.meta.env.VITE_API_URL || 'http://localhost:5001');

  async getSubscription(restaurantId: string): Promise<SubscriptionResponse> {
    const response = await fetch(`${this.baseURL}/api/subscription/restaurants/${restaurantId}/subscription`);
    if (!response.ok) {
      throw new Error(`Failed to fetch subscription: ${response.statusText}`);
    }
    return response.json();
  }

  async getFeatures(restaurantId: string): Promise<FeatureAccessResponse> {
    const response = await fetch(`${this.baseURL}/api/subscription/restaurants/${restaurantId}/features`);
    if (!response.ok) {
      throw new Error(`Failed to fetch features: ${response.statusText}`);
    }
    return response.json();
  }

  async checkFeatureAccess(restaurantId: string, featureId: string): Promise<FeatureCheckResponse> {
    const response = await fetch(`${this.baseURL}/api/subscription/check-access`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        restaurant_id: restaurantId,
        feature_id: featureId
      })
    });
    if (!response.ok) {
      throw new Error(`Failed to check feature access: ${response.statusText}`);
    }
    return response.json();
  }

  async bulkCheckAccess(restaurantId: string, featureIds: string[]): Promise<Record<string, boolean>> {
    const response = await fetch(`${this.baseURL}/api/subscription/bulk/check-access`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        restaurant_id: restaurantId,
        feature_ids: featureIds
      })
    });
    if (!response.ok) {
      throw new Error(`Failed to bulk check access: ${response.statusText}`);
    }
    const data = await response.json();
    return data.access_results;
  }
}

const subscriptionAPI = new SubscriptionAPI();

// Main Hook
export const useSubscriptionAPI = () => {
  const { currentRestaurant } = useAuth();
  const queryClient = useQueryClient();

  const restaurantId = currentRestaurant?.id;

  // Query: Get subscription details
  const {
    data: subscriptionInfo,
    isLoading: isLoadingSubscription,
    error: subscriptionError
  } = useQuery({
    queryKey: ['subscription', restaurantId],
    queryFn: () => subscriptionAPI.getSubscription(restaurantId!),
    enabled: !!restaurantId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (renamed from cacheTime in newer versions)
    retry: 3,

  });

  // Query: Get available features
  const {
    data: featuresInfo,
    isLoading: isLoadingFeatures,
    error: featuresError
  } = useQuery({
    queryKey: ['features', restaurantId],
    queryFn: () => subscriptionAPI.getFeatures(restaurantId!),
    enabled: !!restaurantId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (renamed from cacheTime in newer versions)
    retry: 3,

  });

  // Mutation: Check individual feature access
  const featureCheckMutation = useMutation({
    mutationFn: ({ featureId }: { featureId: string }) => 
      subscriptionAPI.checkFeatureAccess(restaurantId!, featureId),
    onError: (error) => {
      logger.error('Failed to check feature access', 'useSubscriptionAPI', { error });
    }
  });

  // Optimized feature access check with caching
  const hasFeatureAccess = useCallback((featureId: string): boolean => {
    if (!featuresInfo) {
      // Fallback: basic access during loading
      const basicFeatures = ['dashboard', 'analytics', 'inventory', 'staff-management', 'settings'];
      return basicFeatures.includes(featureId);
    }

    const hasAccess = featuresInfo.available_features.includes(featureId);
    
    // Log access check for debugging
    logger.debug('Feature access check', 'useSubscriptionAPI', {
      featureId,
      hasAccess,
      planId: featuresInfo.plan_id,
      accessLevel: featuresInfo.access_level
    });

    return hasAccess;
  }, [featuresInfo]);

  // Bulk feature access check for menu rendering
  const checkMultipleFeatures = useCallback(async (featureIds: string[]) => {
    if (!restaurantId) return {};
    
    try {
      return await subscriptionAPI.bulkCheckAccess(restaurantId, featureIds);
    } catch (error) {
      logger.error('Bulk feature check failed', 'useSubscriptionAPI', { error });
      return {};
    }
  }, [restaurantId]);

  // Computed values
  const isProPlan = useMemo(() => subscriptionInfo?.plan_id === 'pro', [subscriptionInfo]);
  const isBasicPlan = useMemo(() => subscriptionInfo?.plan_id === 'basic', [subscriptionInfo]);
  const isTrialExpired = useMemo(() => 
    subscriptionInfo?.status === 'trial' && !subscriptionInfo?.is_active, 
    [subscriptionInfo]
  );

  // Refresh functions
  const refreshSubscription = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: ['subscription', restaurantId] });
    queryClient.invalidateQueries({ queryKey: ['features', restaurantId] });
  }, [queryClient, restaurantId]);

  // Loading states
  const isLoading = isLoadingSubscription || isLoadingFeatures;
  const hasError = !!subscriptionError || !!featuresError;

  return {
    // Data
    subscriptionInfo,
    featuresInfo,
    
    // Computed states
    isProPlan,
    isBasicPlan,
    isTrialExpired,
    
    // Functions
    hasFeatureAccess,
    checkMultipleFeatures,
    refreshSubscription,
    
    // Loading states
    isLoading,
    hasError,
    
    // Raw errors for debugging
    subscriptionError,
    featuresError,
    
    // Mutation for individual checks
    checkFeatureAccess: featureCheckMutation.mutateAsync
  };
};

// Fallback hook for backward compatibility during migration
export const useSubscriptionAccess = () => {
  const apiHook = useSubscriptionAPI();
  
  // Map new API structure to old hook interface
  return {
    subscriptionInfo: apiHook.subscriptionInfo ? {
      planId: apiHook.subscriptionInfo.plan_id,
      planName: apiHook.subscriptionInfo.plan_name,
      subscriptionStatus: apiHook.subscriptionInfo.status,
      isTrialExpired: apiHook.isTrialExpired,
      daysUntilExpiry: apiHook.subscriptionInfo.days_remaining,
      customizedFeatures: apiHook.subscriptionInfo.custom_features
    } : null,
    
    hasFeatureAccess: apiHook.hasFeatureAccess,
    isLoading: apiHook.isLoading,
    error: apiHook.hasError
  };
};
