import { User } from '@/contexts/AuthContext';
import { getAuthHeaders, storeUser, clearUserSession, isTokenExpiringSoon } from '@/utils/authUtils';

// API Configuration - use proxy in development, direct URL in production
const API_BASE_URL = import.meta.env.DEV
  ? '/api' // Use proxy in development (Vite will proxy to backend)
  : `${import.meta.env.VITE_API_URL || 'http://localhost:5001'}/api`;

interface LoginRequest {
  username: string;
  password: string;
}

interface TokenResponse {
  access_token: string;
  token_type: string;
  expires_at: number;
  user: User;
}

/**
 * Authenticate user with backend and get JW<PERSON> token
 */
export const authenticateWithBackend = async (
  username: string, 
  password: string
): Promise<{ success: boolean; user?: User; message: string }> => {
  try {
    // Create form data for OAuth2 token endpoint
    const formData = new FormData();
    formData.append('username', username);
    formData.append('password', password);

    const response = await fetch(`${API_BASE_URL}/auth/token`, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json();
      return {
        success: false,
        message: errorData.detail || 'Authentication failed'
      };
    }

    const tokenData: TokenResponse = await response.json();
    
    // Store user with token
    const userWithToken: User = {
      ...tokenData.user,
      access_token: tokenData.access_token,
      token_type: tokenData.token_type,
      expires_at: tokenData.expires_at
    };

    storeUser(userWithToken);

    return {
      success: true,
      user: userWithToken,
      message: 'Authentication successful'
    };
  } catch (error) {
    console.error('Backend authentication error:', error);
    return {
      success: false,
      message: 'Failed to connect to authentication server'
    };
  }
};

/**
 * Create a default user for PIN-based login (for EPOS system)
 */
export const createDefaultUserForPin = async (
  pin: string,
  userData: Partial<User>
): Promise<{ success: boolean; user?: User; message: string }> => {
  try {
    // For PIN-based login, create a temporary token using a default admin user
    // This is a fallback for when users login with PIN in the EPOS system
    const defaultCredentials = {
      username: 'admin',
      password: 'admin123' // This should be configured properly in production
    };

    const authResult = await authenticateWithBackend(
      defaultCredentials.username, 
      defaultCredentials.password
    );

    if (authResult.success && authResult.user) {
      // Override user data with PIN user information but keep the token
      const pinUser: User = {
        ...authResult.user,
        ...userData,
        id: userData.id || authResult.user.id,
        name: userData.name || authResult.user.name,
        role: userData.role || authResult.user.role,
        pin: pin
      };

      storeUser(pinUser);

      return {
        success: true,
        user: pinUser,
        message: 'PIN authentication successful'
      };
    }

    return authResult;
  } catch (error) {
    console.error('PIN authentication error:', error);
    return {
      success: false,
      message: 'PIN authentication failed'
    };
  }
};

/**
 * Validate token with backend
 */
export const validateTokenWithBackend = async (user: User): Promise<boolean> => {
  try {
    if (!user.access_token) {
      console.warn('No access token available for validation');
      return false;
    }

    const response = await fetch(`${API_BASE_URL}/auth/validate-token`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${user.access_token}`,
        'Content-Type': 'application/json',
      },
    });

    if (response.ok) {
      console.log('Token validation successful');
      return true;
    } else {
      console.warn('Token validation failed:', response.status);
      return false;
    }
  } catch (error) {
    console.error('Token validation error:', error);
    return false;
  }
};

/**
 * Refresh user token
 */
export const refreshUserToken = async (user: User): Promise<User | null> => {
  try {
    console.log('Attempting to refresh user token');

    const response = await fetch(`${API_BASE_URL}/auth/refresh`, {
      method: 'POST',
      headers: getAuthHeaders(user),
    });

    if (!response.ok) {
      console.warn('Token refresh failed:', response.status, response.statusText);
      clearUserSession();
      return null;
    }

    const tokenData: TokenResponse = await response.json();

    const refreshedUser: User = {
      ...user,
      access_token: tokenData.access_token,
      expires_at: tokenData.expires_at
    };

    console.log('Token refreshed successfully');
    storeUser(refreshedUser);
    return refreshedUser;
  } catch (error) {
    console.error('Token refresh error:', error);
    clearUserSession();
    return null;
  }
};

/**
 * Make authenticated API request with automatic token refresh
 */
export const makeAuthenticatedRequest = async (
  url: string,
  options: RequestInit = {},
  user: User
): Promise<Response> => {
  // Check if token needs refresh
  if (isTokenExpiringSoon(user)) {
    console.log('Token expiring soon, attempting refresh');
    const refreshedUser = await refreshUserToken(user);
    if (!refreshedUser) {
      throw new Error('Failed to refresh token');
    }
    user = refreshedUser;
  }

  // Make the request with current token
  const response = await fetch(url, {
    ...options,
    headers: {
      ...options.headers,
      ...getAuthHeaders(user),
    },
  });

  // If we get 401, try to refresh token once
  if (response.status === 401) {
    console.log('Received 401, attempting token refresh');
    const refreshedUser = await refreshUserToken(user);
    if (!refreshedUser) {
      throw new Error('Authentication failed');
    }

    // Retry the request with new token
    return fetch(url, {
      ...options,
      headers: {
        ...options.headers,
        ...getAuthHeaders(refreshedUser),
      },
    });
  }

  return response;
};

/**
 * Logout user and clear session
 */
export const logoutUser = async (user?: User): Promise<void> => {
  try {
    if (user?.access_token) {
      await fetch(`${API_BASE_URL}/auth/logout`, {
        method: 'POST',
        headers: getAuthHeaders(user),
      });
    }
  } catch (error) {
    console.error('Logout error:', error);
  } finally {
    clearUserSession();
  }
};
