// Define the time entry interface
export interface TimeEntry {
  id: string;
  staffId: string;
  date: string;
  clockIn: string | null;
  clockOut: string | null;
  breakStart: string | null;
  breakEnd: string | null;
  breakTime: number; // in minutes
  totalHours: number;
}

// Define the clock status type
export type ClockStatus = 'clocked_in' | 'clocked_out' | 'on_break';

// Define the staff time tracking interface
export interface StaffTimeTracking {
  clockStatus: ClockStatus;
  lastClockIn: string | null;
  lastClockOut: string | null;
  breakStart: string | null;
  breakEnd: string | null;
  totalHoursToday: number;
  totalBreakTime: number;
}

// Storage keys
const TIME_ENTRIES_KEY = 'timeEntries';
const ACTIVE_CLOCK_INS_KEY = 'activeClockIns';

// Get all time entries from localStorage
export const getAllTimeEntries = (): TimeEntry[] => {
  const entriesJson = localStorage.getItem(TIME_ENTRIES_KEY);
  return entriesJson ? JSON.parse(entriesJson) : [];
};

// Get time entries for a specific staff member
export const getStaffTimeEntries = (staffId: string): TimeEntry[] => {
  const allEntries = getAllTimeEntries();
  return allEntries.filter(entry => entry.staffId === staffId);
};

// Get time entries for a specific staff member within a date range
export const getStaffTimeEntriesInRange = (
  staffId: string,
  startDate: Date,
  endDate: Date
): TimeEntry[] => {
  const staffEntries = getStaffTimeEntries(staffId);
  return staffEntries.filter(entry => {
    const entryDate = new Date(entry.date);
    return entryDate >= startDate && entryDate <= endDate;
  });
};

// Get time entries for a specific week
export const getStaffTimeEntriesForWeek = (
  staffId: string,
  weekYear: number,
  weekNum: number
): TimeEntry[] => {
  // Calculate the start and end dates for the week
  const januaryFirst = new Date(weekYear, 0, 1);
  const daysOffset = (weekNum - 1) * 7;

  // Calculate the first day of the week (Monday)
  const firstDayOfWeek = new Date(januaryFirst);
  firstDayOfWeek.setDate(januaryFirst.getDate() + daysOffset - januaryFirst.getDay() + 1);

  // Calculate the last day of the week (Sunday)
  const lastDayOfWeek = new Date(firstDayOfWeek);
  lastDayOfWeek.setDate(firstDayOfWeek.getDate() + 6);

  return getStaffTimeEntriesInRange(staffId, firstDayOfWeek, lastDayOfWeek);
};

// Get time entries for a specific month
export const getStaffTimeEntriesForMonth = (
  staffId: string,
  year: number,
  month: number // 1-12
): TimeEntry[] => {
  // Calculate the start and end dates for the month
  const firstDayOfMonth = new Date(year, month - 1, 1);
  const lastDayOfMonth = new Date(year, month, 0);

  return getStaffTimeEntriesInRange(staffId, firstDayOfMonth, lastDayOfMonth);
};

// Add a new time entry
export const addTimeEntry = (entry: TimeEntry): void => {
  const allEntries = getAllTimeEntries();

  // Generate a unique ID if not provided
  if (!entry.id) {
    entry.id = Date.now().toString();
  }

  allEntries.push(entry);
  localStorage.setItem(TIME_ENTRIES_KEY, JSON.stringify(allEntries));
};

// Update an existing time entry
export const updateTimeEntry = (updatedEntry: TimeEntry): void => {
  const allEntries = getAllTimeEntries();
  const index = allEntries.findIndex(entry => entry.id === updatedEntry.id);

  if (index !== -1) {
    allEntries[index] = updatedEntry;
    localStorage.setItem(TIME_ENTRIES_KEY, JSON.stringify(allEntries));
  }
};

// Delete a time entry
export const deleteTimeEntry = (entryId: string): void => {
  const allEntries = getAllTimeEntries();
  const filteredEntries = allEntries.filter(entry => entry.id !== entryId);
  localStorage.setItem(TIME_ENTRIES_KEY, JSON.stringify(filteredEntries));
};

// Get today's time entry for a staff member
export const getTodayTimeEntry = (staffId: string): TimeEntry | null => {
  const today = new Date().toISOString().split('T')[0];
  const staffEntries = getStaffTimeEntries(staffId);
  return staffEntries.find(entry => entry.date === today) || null;
};

// Create or update today's time entry based on clock actions
export const updateTimeEntryFromClockAction = (
  staffId: string,
  timeTracking: StaffTimeTracking
): TimeEntry => {
  const today = new Date().toISOString().split('T')[0];
  let todayEntry = getTodayTimeEntry(staffId);

  // Calculate break time in minutes
  let breakTimeMinutes = 0;
  if (timeTracking.breakStart && timeTracking.breakEnd) {
    const breakStartTime = new Date(timeTracking.breakStart).getTime();
    const breakEndTime = new Date(timeTracking.breakEnd).getTime();
    breakTimeMinutes = Math.round((breakEndTime - breakStartTime) / (1000 * 60));
  } else if (timeTracking.totalBreakTime > 0) {
    breakTimeMinutes = Math.round(timeTracking.totalBreakTime * 60);
  }

  // Format clock times (HH:MM)
  const formatTimeFromISOString = (isoString: string | null): string | null => {
    if (!isoString) return null;
    const date = new Date(isoString);
    return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
  };

  const clockInTime = formatTimeFromISOString(timeTracking.lastClockIn);
  const clockOutTime = formatTimeFromISOString(timeTracking.lastClockOut);

  if (!todayEntry) {
    // Create a new entry for today
    todayEntry = {
      id: `${staffId}_${today}`,
      staffId,
      date: today,
      clockIn: clockInTime,
      clockOut: clockOutTime,
      breakStart: formatTimeFromISOString(timeTracking.breakStart),
      breakEnd: formatTimeFromISOString(timeTracking.breakEnd),
      breakTime: breakTimeMinutes,
      totalHours: timeTracking.totalHoursToday
    };
    addTimeEntry(todayEntry);
  } else {
    // Update existing entry
    todayEntry = {
      ...todayEntry,
      clockIn: clockInTime || todayEntry.clockIn,
      clockOut: clockOutTime || todayEntry.clockOut,
      breakStart: formatTimeFromISOString(timeTracking.breakStart) || todayEntry.breakStart,
      breakEnd: formatTimeFromISOString(timeTracking.breakEnd) || todayEntry.breakEnd,
      breakTime: breakTimeMinutes || todayEntry.breakTime,
      totalHours: timeTracking.totalHoursToday
    };
    updateTimeEntry(todayEntry);
  }

  return todayEntry;
};

// Get the current week number
export const getCurrentWeek = (): { year: number, week: number } => {
  const now = new Date();
  const onejan = new Date(now.getFullYear(), 0, 1);
  const weekNum = Math.ceil(((now.getTime() - onejan.getTime()) / 86400000 + onejan.getDay() + 1) / 7);
  return { year: now.getFullYear(), week: weekNum };
};

// Get the current month
export const getCurrentMonth = (): { year: number, month: number } => {
  const now = new Date();
  return { year: now.getFullYear(), month: now.getMonth() + 1 };
};

// Calculate total hours for a staff member in a specific week
export const getStaffTotalHoursForWeek = (
  staffId: string,
  weekYear: number,
  weekNum: number
): number => {
  const entries = getStaffTimeEntriesForWeek(staffId, weekYear, weekNum);
  return entries.reduce((total, entry) => total + entry.totalHours, 0);
};

// Calculate total hours for a staff member in a specific month
export const getStaffTotalHoursForMonth = (
  staffId: string,
  year: number,
  month: number
): number => {
  const entries = getStaffTimeEntriesForMonth(staffId, year, month);
  return entries.reduce((total, entry) => total + entry.totalHours, 0);
};

// Get all active clock-ins
export const getActiveClockIns = (): Record<string, string> => {
  const activeClockInsJson = localStorage.getItem(ACTIVE_CLOCK_INS_KEY);
  return activeClockInsJson ? JSON.parse(activeClockInsJson) : {};
};

// Save active clock-in
export const saveActiveClockIn = (staffId: string, clockInTime: string): void => {
  const activeClockIns = getActiveClockIns();
  activeClockIns[staffId] = clockInTime;
  localStorage.setItem(ACTIVE_CLOCK_INS_KEY, JSON.stringify(activeClockIns));
};

// Remove active clock-in
export const removeActiveClockIn = (staffId: string): void => {
  const activeClockIns = getActiveClockIns();
  delete activeClockIns[staffId];
  localStorage.setItem(ACTIVE_CLOCK_INS_KEY, JSON.stringify(activeClockIns));
};
