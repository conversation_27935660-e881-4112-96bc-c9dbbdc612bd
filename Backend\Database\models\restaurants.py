"""
Restaurant models for RestroManage database.
Corresponds to app/models/restaurants.py Pydantic models.
"""

from sqlalchemy import Column, String, Boolean, DateTime, Text, Float, Integer, JSON, ForeignKey
from sqlalchemy.orm import relationship
from .base import BaseModel, TimestampMixin, StatusMixin, SoftDeleteMixin, ContactMixin, LocationMixin

class Restaurant(BaseModel, TimestampMixin, StatusMixin, SoftDeleteMixin, ContactMixin, LocationMixin):
    """
    Restaurant model for restaurant management.
    Corresponds to Restaurant Pydantic model in app/models/restaurants.py
    """
    __tablename__ = "restaurants"
    
    # Basic restaurant information
    name = Column(String(255), nullable=False, index=True)
    code = Column(String(20), unique=True, nullable=False, index=True)
    logo_url = Column(String(500), nullable=True)
    
    # Business information
    owner_name = Column(String(255), nullable=False)
    business_license_number = Column(String(100), nullable=False, unique=True)
    restaurant_type = Column(String(50), default="restaurant")  # restaurant, cafe, bar, etc.
    
    # Financial settings
    vat_rate = Column(Float, default=20.0, nullable=False)  # VAT percentage
    currency = Column(String(3), default="GBP", nullable=False)
    
    # Authentication
    password_hash = Column(String(255), nullable=False)
    
    # Operating information
    cuisine_types = Column(JSON, nullable=True)  # List of cuisine types
    price_range = Column(JSON, nullable=True)  # {"min": 10, "max": 50}
    seating_capacity = Column(Integer, nullable=True)
    average_service_time = Column(Integer, nullable=True)  # minutes
    estimated_daily_covers = Column(Integer, nullable=True)
    
    # Operating hours
    operating_hours = Column(JSON, nullable=True)  # Weekly schedule
    
    # Setup and configuration
    setup_completed = Column(Boolean, default=False, nullable=False)
    setup_data = Column(JSON, nullable=True)  # Additional setup information
    onboarding_step = Column(String(50), nullable=True)  # Current onboarding step
    
    # Subscription and billing
    subscription_plan = Column(String(50), default="basic")
    subscription_status = Column(String(20), default="active")
    subscription_expires_at = Column(DateTime(timezone=True), nullable=True)
    
    # Settings and preferences
    settings = Column(JSON, nullable=True)  # Restaurant-specific settings
    features_enabled = Column(JSON, nullable=True)  # List of enabled features
    
    # Analytics and metrics
    total_orders = Column(Integer, default=0)
    total_revenue = Column(Float, default=0.0)
    average_rating = Column(Float, nullable=True)
    
    # Relationships - Enable for proper model relationships
    users = relationship("User", back_populates="restaurant")
    restaurant_users = relationship("RestaurantUser", back_populates="restaurant")
    menu_categories = relationship("MenuCategory", back_populates="restaurant")
    menu_items = relationship("MenuItem", back_populates="restaurant")
    tables = relationship("Table", back_populates="restaurant")
    orders = relationship("Order", back_populates="restaurant")
    promo_codes = relationship("PromoCode", back_populates="restaurant")
    staff = relationship("Staff", back_populates="restaurant")
    inventory_items = relationship("InventoryItem", back_populates="restaurant")
    
    def __repr__(self):
        return f"<Restaurant(id={self.id}, name={self.name}, code={self.code})>"

class RestaurantUser(BaseModel, TimestampMixin, StatusMixin):
    """
    Restaurant-specific user model for staff management.
    Corresponds to RestaurantUser Pydantic model in app/models/restaurants.py
    """
    __tablename__ = "restaurant_users"
    
    # Basic information
    name = Column(String(255), nullable=False)
    email = Column(String(255), nullable=False, index=True)
    phone = Column(String(20), nullable=True)
    
    # Restaurant association
    restaurant_id = Column(String(36), ForeignKey("restaurants.id"), nullable=False, index=True)
    restaurant_name = Column(String(255), nullable=False)  # Denormalized for performance
    
    # Role and position
    role = Column(String(50), nullable=False, index=True)
    position = Column(String(100), nullable=False)
    access_level = Column(String(20), default="limited")  # limited, full
    
    # Authentication
    pin = Column(String(10), nullable=False, index=True)
    
    # Employment details
    hire_date = Column(String(20), nullable=False)  # Stored as string for flexibility
    status = Column(String(20), default="active", nullable=False, index=True)
    performance_score = Column(Integer, default=100)
    
    # Work schedule and preferences
    work_schedule = Column(JSON, nullable=True)
    hourly_rate = Column(Float, nullable=True)
    overtime_rate = Column(Float, nullable=True)
    
    # Permissions and access
    permissions = Column(JSON, nullable=True)  # List of specific permissions
    can_access_pos = Column(Boolean, default=True)
    can_access_admin = Column(Boolean, default=False)
    can_manage_orders = Column(Boolean, default=True)
    can_manage_inventory = Column(Boolean, default=False)
    
    # Training and certifications
    certifications = Column(JSON, nullable=True)
    training_completed = Column(JSON, nullable=True)
    
    # Relationships
    restaurant = relationship("Restaurant", back_populates="restaurant_users")
    
    def __repr__(self):
        return f"<RestaurantUser(id={self.id}, name={self.name}, restaurant_id={self.restaurant_id})>"
