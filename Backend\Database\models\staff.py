"""
Staff models for RestroManage database.
Corresponds to app/models/staff.py Pydantic models.
"""

from sqlalchemy import Column, String, Boolean, Float, Text, JSON, ForeignKey, Integer, DateTime, Time
from sqlalchemy.orm import relationship
from .base import BaseModel, TimestampMixin, StatusMixin, SoftDeleteMixin, ContactMixin

class Staff(BaseModel, TimestampMixin, StatusMixin, SoftDeleteMixin, ContactMixin):
    """
    Staff model for employee management.
    Corresponds to Staff Pydantic model in app/models/staff.py
    """
    __tablename__ = "staff"
    
    # Restaurant association
    restaurant_id = Column(String(36), ForeignKey("restaurants.id"), nullable=False, index=True)
    
    # Basic information
    name = Column(String(255), nullable=False, index=True)
    employee_id = Column(String(50), nullable=True, unique=True, index=True)
    
    # Role and position
    role = Column(String(50), nullable=False, index=True)
    position = Column(String(20), nullable=False, index=True)
    # Positions: front_of_house, back_of_house, management
    department = Column(String(50), nullable=True)
    
    # Employment details
    hire_date = Column(DateTime(timezone=True), nullable=True)
    termination_date = Column(DateTime(timezone=True), nullable=True)
    employment_type = Column(String(20), default="full_time", nullable=False)
    # Types: full_time, part_time, contract, temporary
    
    # Compensation
    hourly_rate = Column(Float, nullable=True)
    salary = Column(Float, nullable=True)
    overtime_rate = Column(Float, nullable=True)
    commission_rate = Column(Float, nullable=True)
    
    # Status and performance
    status = Column(String(20), default="active", nullable=False, index=True)
    # Status: active, inactive, on_leave, terminated
    performance_score = Column(Integer, default=100, nullable=False)
    
    # Skills and certifications
    skills = Column(JSON, nullable=True)  # List of skills
    certifications = Column(JSON, nullable=True)  # List of certifications
    languages = Column(JSON, nullable=True)  # List of languages spoken
    
    # Training and development
    training_completed = Column(JSON, nullable=True)
    training_required = Column(JSON, nullable=True)
    last_training_date = Column(DateTime(timezone=True), nullable=True)
    
    # Work preferences
    preferred_shifts = Column(JSON, nullable=True)
    availability = Column(JSON, nullable=True)  # Weekly availability
    max_hours_per_week = Column(Integer, nullable=True)
    
    # Emergency contact
    emergency_contact_name = Column(String(255), nullable=True)
    emergency_contact_phone = Column(String(20), nullable=True)
    emergency_contact_relationship = Column(String(50), nullable=True)
    
    # Documents and compliance
    tax_id = Column(String(50), nullable=True)
    social_security = Column(String(50), nullable=True)  # Encrypted
    work_permit_status = Column(String(50), nullable=True)
    background_check_status = Column(String(20), nullable=True)
    background_check_date = Column(DateTime(timezone=True), nullable=True)
    
    # Performance metrics
    total_hours_worked = Column(Float, default=0.0, nullable=False)
    total_overtime_hours = Column(Float, default=0.0, nullable=False)
    customer_rating = Column(Float, nullable=True)
    punctuality_score = Column(Float, nullable=True)
    
    # Notes and comments
    manager_notes = Column(Text, nullable=True)
    hr_notes = Column(Text, nullable=True)
    
    # Relationships
    restaurant = relationship("Restaurant", back_populates="staff")
    schedules = relationship("StaffSchedule", back_populates="staff", cascade="all, delete-orphan")
    assigned_orders_chef = relationship("Order", foreign_keys="Order.assigned_chef_id")
    assigned_orders_waiter = relationship("Order", foreign_keys="Order.assigned_waiter_id")
    
    def __repr__(self):
        return f"<Staff(id={self.id}, name={self.name}, role={self.role})>"

class StaffSchedule(BaseModel, TimestampMixin):
    """
    Staff schedule model for shift management.
    """
    __tablename__ = "staff_schedules"
    
    # Staff association
    staff_id = Column(String(36), ForeignKey("staff.id"), nullable=False, index=True)
    restaurant_id = Column(String(36), ForeignKey("restaurants.id"), nullable=False, index=True)
    
    # Schedule details
    schedule_date = Column(DateTime(timezone=True), nullable=False, index=True)
    shift_start = Column(Time, nullable=False)
    shift_end = Column(Time, nullable=False)
    
    # Shift information
    shift_type = Column(String(20), nullable=False)  # morning, afternoon, evening, night
    position = Column(String(50), nullable=False)
    department = Column(String(50), nullable=True)
    
    # Status tracking
    status = Column(String(20), default="scheduled", nullable=False, index=True)
    # Status: scheduled, confirmed, in_progress, completed, cancelled, no_show
    
    # Actual time tracking
    actual_start = Column(DateTime(timezone=True), nullable=True)
    actual_end = Column(DateTime(timezone=True), nullable=True)
    break_start = Column(DateTime(timezone=True), nullable=True)
    break_end = Column(DateTime(timezone=True), nullable=True)
    
    # Hours calculation
    scheduled_hours = Column(Float, nullable=False)
    actual_hours = Column(Float, nullable=True)
    overtime_hours = Column(Float, default=0.0, nullable=False)
    break_duration = Column(Float, default=0.0, nullable=False)  # minutes
    
    # Compensation
    hourly_rate = Column(Float, nullable=True)
    overtime_rate = Column(Float, nullable=True)
    total_pay = Column(Float, nullable=True)
    
    # Notes and comments
    schedule_notes = Column(Text, nullable=True)
    manager_notes = Column(Text, nullable=True)
    staff_notes = Column(Text, nullable=True)
    
    # Approval and management
    approved_by = Column(String(36), nullable=True)
    approved_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    staff = relationship("Staff", back_populates="schedules")
    restaurant = relationship("Restaurant")
    
    def __repr__(self):
        return f"<StaffSchedule(id={self.id}, staff_id={self.staff_id}, schedule_date={self.schedule_date})>"
