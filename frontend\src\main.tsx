import logger from "./utils/logger";
import React from 'react'
import { createRoot } from 'react-dom/client'
import App from './App'
import './index.css'

// Set up CSP violation reporting
document.addEventListener('securitypolicyviolation', (e) => {
  logger.logCSPViolation(e, 'CSP');
});

// Set up global error handling
window.addEventListener('error', (e) => {
  logger.logError(e.error || e, 'Global Error Handler', 'main', {
    filename: e.filename,
    lineno: e.lineno,
    colno: e.colno,
    message: e.message
  });
});

window.addEventListener('unhandledrejection', (e) => {
  logger.logError(e.reason, 'Unhandled Promise Rejection', 'main', {
    promise: e.promise
  });
});

// Add console log for debugging
logger.info('Application starting', 'main');

try {
  const rootElement = document.getElementById("root")
  if (!rootElement) {
    logger.error('[main.tsx] Root element not found!')
  } else {
    logger.info('Root element found, rendering app', 'main');
    createRoot(rootElement).render(
      <React.StrictMode>
        <App />
      </React.StrictMode>
    )
    logger.info('App rendered successfully', 'main');
  }
} catch (error) {
  logger.logError(error, 'React app render', 'main');
}
