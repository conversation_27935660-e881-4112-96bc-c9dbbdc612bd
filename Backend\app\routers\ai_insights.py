from fastapi import APIRouter, Depends, HTTPException, status
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
from app.services.google_ai_service import google_ai_service
from app.utils.storage import get_all, get_by_id
from app.utils.auth import get_current_active_user, check_manager_role
from app.utils.logging_config import logger

router = APIRouter(prefix="/ai", tags=["AI Insights"])

class BusinessContext(BaseModel):
    type: str = "casual dining"
    avg_order_value: float = 25.0
    peak_hours: str = "12-2pm, 6-9pm"
    cuisine_type: Optional[str] = None
    location_type: Optional[str] = None

class PromoCodeSuggestionRequest(BaseModel):
    business_context: BusinessContext
    target_audience: str = "general"
    campaign_goal: str = "increase_sales"

class CustomerAnalysisRequest(BaseModel):
    include_recent_only: bool = True
    analysis_period_days: int = 30

class MenuOptimizationRequest(BaseModel):
    include_cost_analysis: bool = True
    focus_categories: Optional[List[str]] = None

class SalesPredictionRequest(BaseModel):
    time_period: str = "next_week"
    include_external_factors: bool = True

@router.get("/status")
async def get_ai_status():
    """Get AI service status and configuration (public endpoint)"""
    # Don't reload configuration on every status check - only check current state
    ai_enabled = google_ai_service.is_enabled()

    if ai_enabled:
        return {
            "ai_enabled": True,
            "model": google_ai_service.model_name,
            "features": [
                "promo_code_suggestions",
                "customer_behavior_analysis",
                "menu_pricing_optimization",
                "sales_trend_prediction",
                "custom_ai_responses"
            ],
            "message": "AI features are active and ready to use"
        }
    else:
        return {
            "ai_enabled": False,
            "model": None,
            "features": [],
            "message": "AI features are disabled - check API key configuration"
        }

@router.post("/reload-config")
async def reload_ai_configuration(current_user = Depends(get_current_active_user)):
    """Reload AI service configuration (admin only)"""
    check_manager_role(current_user)

    try:
        google_ai_service.reload_configuration()
        return {
            "success": True,
            "message": "AI configuration reloaded successfully",
            "ai_enabled": google_ai_service.is_enabled(),
            "model": google_ai_service.model_name
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to reload AI configuration: {str(e)}"
        )

@router.post("/promo-suggestions")
async def generate_promo_suggestions(
    request: PromoCodeSuggestionRequest,
    current_user = Depends(get_current_active_user)
):
    """Generate AI-powered promo code suggestions"""
    check_manager_role(current_user)
    
    try:
        suggestions = await google_ai_service.generate_promo_code_suggestions(
            business_context=request.business_context.model_dump(),
            target_audience=request.target_audience,
            campaign_goal=request.campaign_goal
        )
        
        return {
            "success": True,
            "suggestions": suggestions,
            "ai_enabled": google_ai_service.is_enabled(),
            "generated_at": "2024-01-01T00:00:00Z"  # In production, use actual timestamp
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate promo suggestions: {str(e)}"
        )

@router.post("/customer-analysis")
async def analyze_customer_behavior(
    request: CustomerAnalysisRequest,
    current_user = Depends(get_current_active_user)
):
    """Analyze customer behavior patterns using AI"""
    check_manager_role(current_user)
    
    try:
        # Get customer data (in production, this would be from a proper database)
        orders = get_all("orders")
        promo_usage = get_all("promo_code_usage")
        
        # Filter recent data if requested
        if request.include_recent_only:
            # In production, implement proper date filtering
            pass
        
        customer_data = []  # Aggregate customer data from orders
        
        analysis = await google_ai_service.analyze_customer_behavior(customer_data)
        
        return {
            "success": True,
            "analysis": analysis,
            "data_points": len(customer_data),
            "ai_enabled": google_ai_service.is_enabled(),
            "analyzed_at": "2024-01-01T00:00:00Z"
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to analyze customer behavior: {str(e)}"
        )

@router.post("/menu-optimization")
async def optimize_menu_pricing(
    request: MenuOptimizationRequest,
    current_user = Depends(get_current_active_user)
):
    """Get AI-powered menu pricing optimization suggestions"""
    check_manager_role(current_user)
    
    try:
        menu_items = get_all("menu_items")
        
        # In production, get actual cost data from inventory or cost management system
        cost_data = {}
        
        optimization = await google_ai_service.optimize_menu_pricing(
            menu_items=menu_items,
            cost_data=cost_data
        )
        
        return {
            "success": True,
            "optimization": optimization,
            "menu_items_analyzed": len(menu_items),
            "ai_enabled": google_ai_service.is_enabled(),
            "optimized_at": "2024-01-01T00:00:00Z"
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to optimize menu pricing: {str(e)}"
        )

@router.post("/sales-prediction")
async def predict_sales_trends(
    request: SalesPredictionRequest,
    current_user = Depends(get_current_active_user)
):
    """Get AI-powered sales trend predictions"""
    check_manager_role(current_user)
    
    try:
        # Get historical sales data
        orders = get_all("orders")
        
        # In production, aggregate and format historical data properly
        historical_data = []
        
        prediction = await google_ai_service.predict_sales_trends(
            historical_data=historical_data,
            time_period=request.time_period
        )
        
        return {
            "success": True,
            "prediction": prediction,
            "historical_data_points": len(historical_data),
            "time_period": request.time_period,
            "ai_enabled": google_ai_service.is_enabled(),
            "predicted_at": "2024-01-01T00:00:00Z"
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to predict sales trends: {str(e)}"
        )

@router.get("/insights/dashboard")
async def get_ai_dashboard_insights(current_user = Depends(get_current_active_user)):
    """Get comprehensive AI insights for dashboard"""
    check_manager_role(current_user)
    
    try:
        # Get basic business context
        orders = get_all("orders")
        menu_items = get_all("menu_items")
        promo_codes = get_all("promo_codes")
        
        # Calculate basic metrics
        total_orders = len(orders)
        avg_order_value = sum(order.get("total", 0) for order in orders) / max(total_orders, 1)
        active_promos = len([p for p in promo_codes if p.get("is_active", False)])
        
        business_context = {
            "type": "casual dining",
            "avg_order_value": avg_order_value,
            "peak_hours": "12-2pm, 6-9pm"
        }
        
        # Get AI insights if available
        insights = {
            "business_metrics": {
                "total_orders": total_orders,
                "avg_order_value": avg_order_value,
                "active_promotions": active_promos,
                "menu_items": len(menu_items)
            },
            "ai_enabled": google_ai_service.is_enabled()
        }
        
        if google_ai_service.is_enabled():
            # Get quick AI insights
            try:
                promo_suggestions = await google_ai_service.generate_promo_code_suggestions(
                    business_context=business_context,
                    target_audience="general",
                    campaign_goal="increase_sales"
                )
                insights["quick_promo_suggestions"] = promo_suggestions[:3]  # Top 3 suggestions
            except:
                insights["quick_promo_suggestions"] = []
        
        return insights
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get AI dashboard insights: {str(e)}"
        )

@router.get("/insights/promo-performance")
async def analyze_promo_performance(current_user = Depends(get_current_active_user)):
    """Analyze promo code performance with AI insights"""
    check_manager_role(current_user)
    
    try:
        promo_codes = get_all("promo_codes")
        promo_usage = get_all("promo_code_usage")
        
        # Calculate performance metrics
        performance_data = []
        for promo in promo_codes:
            usage_count = len([u for u in promo_usage if u.get("promo_code_id") == promo.get("id")])
            total_discount = sum(u.get("discount_amount", 0) for u in promo_usage if u.get("promo_code_id") == promo.get("id"))
            
            performance_data.append({
                "code": promo.get("code"),
                "usage_count": usage_count,
                "total_discount": total_discount,
                "conversion_rate": usage_count / max(promo.get("usage_limit", 1), 1) * 100
            })
        
        # Sort by performance
        performance_data.sort(key=lambda x: x["usage_count"], reverse=True)
        
        return {
            "success": True,
            "performance_data": performance_data,
            "top_performers": performance_data[:5],
            "total_codes": len(promo_codes),
            "total_usage": len(promo_usage),
            "ai_enabled": google_ai_service.is_enabled()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to analyze promo performance: {str(e)}"
        )

@router.post("/insights/custom-analysis")
async def custom_ai_analysis(
    query: str,
    context: Optional[Dict[str, Any]] = None,
    current_user = Depends(get_current_active_user)
):
    """Perform custom AI analysis based on user query"""
    check_manager_role(current_user)

    try:
        # Generate custom AI response
        response = await google_ai_service.generate_custom_response(query, context)

        return {
            "success": True,
            "response": response,
            "ai_enabled": google_ai_service.is_enabled(),
            "query": query
        }
        
    except Exception as e:
        logger.error("Custom AI analysis failed", "AIInsights", {
            "error": str(e),
            "error_type": type(e).__name__,
            "query_length": len(query),
            "user_id": current_user.get("id"),
            "has_context": bool(context)
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to perform custom analysis: {str(e)}"
        )
