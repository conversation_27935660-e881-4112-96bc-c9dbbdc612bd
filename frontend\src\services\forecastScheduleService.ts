import { ForecastData } from "@/components/dashboard/ForecastCard";
import { format, addDays } from "date-fns";

// Interface for staff member
export interface StaffMember {
  id: string;
  name: string;
  role: string;
  position: string;
  status?: 'active' | 'inactive';
  availableDays?: string[];
  assignedHours?: number;
}

// Interface for a shift generated from forecast data
export interface ForecastShift {
  id: string;
  staffId: string;
  staffName: string;
  role: string;
  day: string;
  date: string;
  startTime: string;
  endTime: string;
  forecastBased: boolean;
  assignedStaff?: StaffMember[]; // Added missing property
}

// Interface for staff requirements based on forecast
export interface StaffRequirement {
  day: string;
  date: string;
  totalStaffNeeded: number;
  waiters: number;
  kitchen: number;
  bar: number;
  host: number;
  shifts: {
    morning: number;
    afternoon: number;
    evening: number;
  };
}

/**
 * Calculate staff requirements based on forecast data
 */
export const calculateStaffRequirements = (forecastData: ForecastData[]): StaffRequirement[] => {
  return forecastData.map(forecast => {
    // Calculate staff requirements based on forecast data
    const totalStaffNeeded = forecast.staffNeeded || Math.ceil(forecast.customers / 10);
    
    // Distribute staff across roles
    const waiters = Math.ceil(totalStaffNeeded * 0.4); // 40% waiters
    const kitchen = Math.ceil(totalStaffNeeded * 0.3); // 30% kitchen
    const bar = Math.ceil(totalStaffNeeded * 0.2); // 20% bar
    const host = Math.ceil(totalStaffNeeded * 0.1); // 10% host
    
    // Distribute shifts across the day
    const morning = Math.ceil(totalStaffNeeded * 0.3); // 30% morning
    const afternoon = Math.ceil(totalStaffNeeded * 0.3); // 30% afternoon
    const evening = Math.ceil(totalStaffNeeded * 0.4); // 40% evening
    
    return {
      day: forecast.day,
      date: format(new Date(), "yyyy-MM-dd"), // Placeholder date
      totalStaffNeeded,
      waiters,
      kitchen,
      bar,
      host,
      shifts: {
        morning,
        afternoon,
        evening
      }
    };
  });
};

/**
 * Generate shifts from forecast data
 */
export const generateShiftsFromForecast = (
  requirements: StaffRequirement[],
  staffData: StaffMember[],
  dateRange: { startDate: Date; endDate: Date }
): ForecastShift[] => {
  const shifts: ForecastShift[] = [];
  let shiftId = 1;
  
  // Filter active staff
  const activeStaff = staffData.filter(staff => staff.status !== 'inactive');
  
  requirements.forEach(req => {
    // Map day name to day of week (0-6)
    const dayMap: { [key: string]: number } = {
      "Monday": 1, "Tuesday": 2, "Wednesday": 3, "Thursday": 4, 
      "Friday": 5, "Saturday": 6, "Sunday": 0
    };
    
    // Find the date for this day within the date range
    const dayOfWeek = dayMap[req.day];
    let shiftDate = new Date(dateRange.startDate);
    
    // Adjust to the correct day of the week
    while (shiftDate.getDay() !== dayOfWeek) {
      shiftDate = addDays(shiftDate, 1);
      if (shiftDate > dateRange.endDate) {
        // Skip if the day is outside the date range
        return;
      }
    }
    
    // Format the date
    const formattedDate = format(shiftDate, "yyyy-MM-dd");
    
    // Assign morning shifts
    const morningStaff = assignStaffToShifts(
      activeStaff, 
      req.shifts.morning, 
      req.day, 
      "08:00", 
      "14:00"
    );
    
    // Assign afternoon shifts
    const afternoonStaff = assignStaffToShifts(
      activeStaff.filter(staff => !morningStaff.some(s => s.id === staff.id)), 
      req.shifts.afternoon, 
      req.day, 
      "12:00", 
      "18:00"
    );
    
    // Assign evening shifts
    const eveningStaff = assignStaffToShifts(
      activeStaff.filter(staff => 
        !morningStaff.some(s => s.id === staff.id) && 
        !afternoonStaff.some(s => s.id === staff.id)
      ), 
      req.shifts.evening, 
      req.day, 
      "16:00", 
      "22:00"
    );
    
    // Create shifts for all assigned staff
    [...morningStaff, ...afternoonStaff, ...eveningStaff].forEach(staff => {
      const isMorning = morningStaff.some(s => s.id === staff.id);
      const isAfternoon = afternoonStaff.some(s => s.id === staff.id);
      
      shifts.push({
        id: `forecast-${shiftId++}`,
        staffId: staff.id,
        staffName: staff.name,
        role: staff.role,
        day: req.day,
        date: formattedDate,
        startTime: isMorning ? "08:00" : isAfternoon ? "12:00" : "16:00",
        endTime: isMorning ? "14:00" : isAfternoon ? "18:00" : "22:00",
        forecastBased: true
      });
    });
  });
  
  return shifts;
};

/**
 * Assign staff to shifts
 */
const assignStaffToShifts = (
  availableStaff: StaffMember[],
  count: number,
  day: string,
  startTime: string,
  endTime: string
): StaffMember[] => {
  // Map day name to short code
  const dayMap: { [key: string]: string } = {
    "Monday": "mon", "Tuesday": "tue", "Wednesday": "wed", "Thursday": "thu", 
    "Friday": "fri", "Saturday": "sat", "Sunday": "sun"
  };
  
  const dayCode = dayMap[day].toLowerCase();
  
  // Filter staff by availability for this day
  const availableForDay = availableStaff.filter(staff => 
    !staff.availableDays || staff.availableDays.includes(dayCode)
  );
  
  // If not enough staff available, use any available staff
  const staffToAssign = availableForDay.length >= count 
    ? availableForDay.slice(0, count) 
    : [...availableForDay, ...availableStaff.slice(0, count - availableForDay.length)];
  
  return staffToAssign.slice(0, count);
};

/**
 * Save generated shifts
 */
export const saveGeneratedShifts = (shifts: ForecastShift[]): void => {
  // In a real app, this would save to a database
  console.log("Saving generated shifts:", shifts);
  // For now, we'll just return the shifts to be handled by the parent component
};