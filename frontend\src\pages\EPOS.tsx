import { useState, useEffect } from "react";
import logger from "@/utils/logger";
import Layout from "@/components/layout/Layout";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { toast } from "@/components/ui/sonner";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Search, Plus, Minus, ShoppingCart, CreditCard, Printer, PoundSterling, Trash2, Table, Users, Tag, AlertTriangle, UtensilsCrossed, ShoppingBag, Settings, ArrowRight, BarChart3, Moon, Sun } from "lucide-react";
import { useTheme } from "next-themes";
import PaymentProcessing from "@/components/epos/PaymentProcessing";
import TableSelection from "@/components/epos/TableSelection";
import OrderTypeSelection from "@/components/epos/OrderTypeSelection";
import PartySizeInput from "@/components/epos/PartySizeInput";
import AllergenInfoCollection from "@/components/epos/AllergenInfoCollection";
import AllergenDetails from "@/components/epos/AllergenDetails";
import { AllergenDisplayCompact } from "@/components/epos/AllergenDisplay";
import FoodInfoModal from "@/components/epos/FoodInfoModal";
import CartItemOptions from "@/components/epos/CartItemOptions";
import DiscountManager from "@/components/epos/DiscountManager";
import SplitBillManager from "@/components/epos/SplitBillManager";
import ReceiptPrinter from "@/components/epos/ReceiptPrinter";
import { useStaffPIN } from "@/contexts/StaffPINContext";
import { useAuth } from "@/contexts/AuthContext";
import { useNavigate } from "react-router-dom";
import { Badge } from "@/components/ui/badge";
import { useSubscriptionAccess } from "@/hooks/useSubscriptionAccess";
import { FeatureGate } from "@/components/subscription/FeatureGate";
import { EnhancedTableSelection } from "@/components/epos/EnhancedTableSelection";
import { tableSessionService, TableSession, CartItem } from "@/services/tableSessionService";
import apiService from "@/services/apiService";
import TableManagement from "@/components/epos/TableManagement";
import EPOSLandingPage from "@/components/epos/EPOSLandingPage";
import StaffNotificationSystem from "@/components/epos/StaffNotificationSystem";
import ManagerReporting from "@/components/epos/ManagerReporting";

// Define types
interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  image?: string;
  allergens?: string[];
}

interface AddOn {
  id: string;
  name: string;
  price: number;
}

interface DiscountApplication {
  promo_code: string;
  discount_amount: number;
  applied_to_items?: string[];
}

interface TableInfo {
  id: string;
  area: string;
  number: number;
  type: "table" | "takeout";
  capacity?: number;
  status?: 'available' | 'occupied';
}

interface Category {
  id: string;
  name: string;
}

const EPOS = () => {
  const navigate = useNavigate();
  const { activeStaff } = useStaffPIN();
  const { hasFeatureAccess, currentPlanName } = useSubscriptionAccess();
  const { theme, setTheme } = useTheme();
  const { currentRestaurant } = useAuth();

  // Core EPOS state
  const [searchQuery, setSearchQuery] = useState("");
  const [activeCategory, setActiveCategory] = useState("all");
  const [cart, setCart] = useState<CartItem[]>([]);
  const [isPaymentOpen, setIsPaymentOpen] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState<"card" | "cash">("card");
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  const [paymentComplete, setPaymentComplete] = useState(false);
  const [orderNumber, setOrderNumber] = useState<string>("");

  // EPOS workflow state
  const [showLandingPage, setShowLandingPage] = useState(true);
  const [selectedWorkflow, setSelectedWorkflow] = useState<'dine-in' | 'takeaway' | 'table-management' | null>(null);

  // Staff notification state
  const [isNotificationMinimized, setIsNotificationMinimized] = useState(true);

  // Manager reporting state
  const [isManagerReportingOpen, setIsManagerReportingOpen] = useState(false);

  // Sequential flow state
  const [isOrderTypeSelectionOpen, setIsOrderTypeSelectionOpen] = useState(false);
  const [isTableSelectionOpen, setIsTableSelectionOpen] = useState(false);
  const [isPartySizeInputOpen, setIsPartySizeInputOpen] = useState(false);
  const [isAllergenInfoOpen, setIsAllergenInfoOpen] = useState(false);
  const [isAllergenDetailsOpen, setIsAllergenDetailsOpen] = useState(false);
  const [selectedTable, setSelectedTable] = useState<TableInfo | null>(null);
  const [orderType, setOrderType] = useState<'dine-in' | 'takeaway' | null>(null);
  const [currentSession, setCurrentSession] = useState<TableSession | null>(null);
  const [pendingTableInfo, setPendingTableInfo] = useState<TableInfo | null>(null);
  const [pendingPartySize, setPendingPartySize] = useState<number>(0);

  // Item management state
  const [expandedCartItem, setExpandedCartItem] = useState<string | null>(null);
  const [itemToAdd, setItemToAdd] = useState<MenuItem | null>(null);
  const [isFoodInfoOpen, setIsFoodInfoOpen] = useState(false);
  const [selectedFoodItem, setSelectedFoodItem] = useState<MenuItem | null>(null);
  const [appliedDiscounts, setAppliedDiscounts] = useState<DiscountApplication[]>([]);
  const [expandedItemId, setExpandedItemId] = useState<string | null>(null);
  const [itemAllergenAlerts, setItemAllergenAlerts] = useState<Record<string, boolean>>({});
  const [itemNotes, setItemNotes] = useState<Record<string, string>>({});
  const [showAllergenDialog, setShowAllergenDialog] = useState(false);
  const [allergenDialogItem, setAllergenDialogItem] = useState<MenuItem | null>(null);
  const [selectedAddOns, setSelectedAddOns] = useState<AddOn[]>([]);

  // Additional state
  const [isSplitBill, setIsSplitBill] = useState(false);
  const [splitBillId, setSplitBillId] = useState<string | null>(null);
  const [isReceiptOpen, setIsReceiptOpen] = useState(false);
  const [isDiscountOpen, setIsDiscountOpen] = useState(false);
  const [isTableManagementOpen, setIsTableManagementOpen] = useState(false);

  // Check if staff is logged in
  useEffect(() => {
    console.log("EPOS: Checking active staff:", activeStaff);
    // We'll let the ProtectedRoute handle the redirection instead of doing it here
  }, [activeStaff]);

  // Initialize EPOS flow
  useEffect(() => {
    // Check for existing session
    const existingSession = tableSessionService.getCurrentSession();
    if (existingSession && existingSession.setupComplete) {
      // Resume existing session, skip landing page
      setCurrentSession(existingSession);
      setSelectedTable({
        id: existingSession.tableId,
        area: existingSession.orderType === 'takeaway' ? 'Takeaway' : 'Main Dining',
        number: existingSession.tableNumber,
        type: existingSession.orderType === 'takeaway' ? 'takeout' : 'table'
      });
      setOrderType(existingSession.orderType);
      setCart(existingSession.cartItems);
      setShowLandingPage(false);
      setSelectedWorkflow(existingSession.orderType);
    } else {
      // No session, show landing page
      setShowLandingPage(true);
    }
  }, []);

  // Landing page handlers
  const handleSelectDineIn = () => {
    setSelectedWorkflow('dine-in');
    setOrderType('dine-in');
    setShowLandingPage(false);
    setIsTableSelectionOpen(true);
  };

  const handleSelectTakeaway = () => {
    setSelectedWorkflow('takeaway');
    setOrderType('takeaway');
    setShowLandingPage(false);

    // For takeaway, auto-select takeaway table and proceed to party size input
    const takeawayTable: TableInfo = {
      id: 'takeaway-001',
      area: 'Takeaway',
      number: 1,
      type: 'takeout'
    };
    setSelectedTable(takeawayTable);
    setPendingTableInfo(takeawayTable);
    setIsPartySizeInputOpen(true);
  };

  const handleSelectTableManagement = () => {
    setSelectedWorkflow('table-management');
    setShowLandingPage(false);
    setIsTableManagementOpen(true);
  };

  const handleBackToLanding = () => {
    // Clear current session and return to landing page
    tableSessionService.clearSession();
    setCurrentSession(null);
    setSelectedTable(null);
    setOrderType(null);
    setCart([]);
    setSelectedWorkflow(null);
    setShowLandingPage(true);

    // Close all dialogs
    setIsOrderTypeSelectionOpen(false);
    setIsTableSelectionOpen(false);
    setIsPartySizeInputOpen(false);
    setIsAllergenInfoOpen(false);
    setIsAllergenDetailsOpen(false);
    setIsTableManagementOpen(false);
  };

  // Sequential flow handlers
  const handleOrderTypeSelect = (selectedOrderType: 'dine-in' | 'takeaway') => {
    setOrderType(selectedOrderType);
    setIsOrderTypeSelectionOpen(false);

    if (selectedOrderType === 'takeaway') {
      // For takeaway, auto-select takeaway table and proceed to party size input
      const takeawayTable: TableInfo = {
        id: 'takeaway-001',
        area: 'Takeaway',
        number: 1,
        type: 'takeout'
      };
      setSelectedTable(takeawayTable);
      setPendingTableInfo(takeawayTable);
      setIsPartySizeInputOpen(true);
    } else {
      // For dine-in, open table selection
      setIsTableSelectionOpen(true);
    }
  };

  // Handle table selection (integrated approach)
  const handleTableSelect = (tableInfo: TableInfo) => {
    // Check if table already has a session (setup complete)
    const existingSession = tableSessionService.getSession(tableInfo.id);

    if (existingSession && existingSession.setupComplete) {
      // Table already set up, restore existing session
      setSelectedTable(tableInfo);
      setCurrentSession(existingSession);
      setIsTableSelectionOpen(false);

      // Restore cart items from session
      const sessionCartItems: CartItem[] = existingSession.cartItems || [];
      setCart(sessionCartItems);

      // Initialize allergen alerts and notes state
      const alerts: Record<string, boolean> = {};
      const notes: Record<string, string> = {};
      sessionCartItems.forEach(item => {
        if (item.allergenAlert) alerts[item.id] = true;
        if (item.notes) notes[item.id] = item.notes;
      });
      setItemAllergenAlerts(alerts);
      setItemNotes(notes);

      // If there was an item waiting to be added, add it now
      if (itemToAdd) {
        addItemToCart(itemToAdd);
        setItemToAdd(null);
      }

      const allergenInfo = existingSession.hasAllergens ? " (⚠️ Allergies)" : "";
      toast.success(`Table ${tableInfo.number} resumed (${existingSession.partySize} guests${allergenInfo})`);
    } else {
      // New table, start setup flow
      setPendingTableInfo(tableInfo);
      setIsTableSelectionOpen(false);
      setIsPartySizeInputOpen(true);
    }
  };

  // Handle party size input
  const handlePartySizeConfirm = (partySize: number) => {
    setPendingPartySize(partySize);
    setIsPartySizeInputOpen(false);
    setIsAllergenInfoOpen(true);
  };

  // Handle allergen info collection
  const handleAllergenInfoConfirm = (hasAllergens: boolean) => {
    if (hasAllergens) {
      setIsAllergenInfoOpen(false);
      setIsAllergenDetailsOpen(true);
    } else {
      // No allergens, close dialog and complete setup
      setIsAllergenInfoOpen(false);
      completeTableSetup(false, []);
    }
  };

  // Handle allergen details
  const handleAllergenDetailsConfirm = (allergens: string[]) => {
    setIsAllergenDetailsOpen(false);
    completeTableSetup(true, allergens);
  };

  // Complete table setup
  const completeTableSetup = (hasAllergens: boolean, allergens: string[]) => {
    if (!pendingTableInfo || !pendingPartySize) return;

    // Create session using the integrated service
    const session = tableSessionService.createSession(
      pendingTableInfo.id,
      pendingTableInfo.number,
      pendingPartySize,
      allergens,
      hasAllergens
    );

    setCurrentSession(session);
    setSelectedTable(pendingTableInfo);
    setPendingTableInfo(null);
    setPendingPartySize(0);

    // If there was an item waiting to be added, add it now
    if (itemToAdd) {
      addItemToCart(itemToAdd);
      setItemToAdd(null);
    }

    const allergenInfo = hasAllergens ? " (⚠️ Allergies noted)" : "";
    toast.success(`Table ${pendingTableInfo.number} setup complete (${pendingPartySize} guests${allergenInfo})`);
  };

  // Handle going back in the flow
  const handleAllergenDetailsBack = () => {
    setIsAllergenDetailsOpen(false);
    setIsAllergenInfoOpen(true);
  };

  const handleAllergenInfoBack = () => {
    setIsAllergenInfoOpen(false);
    setIsPartySizeInputOpen(true);
  };

  const handlePartySizeBack = () => {
    setIsPartySizeInputOpen(false);
    if (orderType === "takeaway") {
      setIsOrderTypeSelectionOpen(true);
    } else {
      setIsTableSelectionOpen(true);
    }
  };

  // Mock menu data - using the same structure as in Menu.tsx
  const menuItems: MenuItem[] = [
    {
      id: "1",
      name: "Classic Margherita Pizza",
      description: "Fresh mozzarella, tomato sauce, basil",
      price: 14.99,
      category: "pizza",
      allergens: ["dairy", "gluten"],
    },
    {
      id: "2",
      name: "Pepperoni Pizza",
      description: "Tomato sauce, mozzarella, pepperoni",
      price: 16.99,
      category: "pizza",
      allergens: ["dairy", "gluten"],
    },
    {
      id: "3",
      name: "Vegetable Supreme Pizza",
      description: "Bell peppers, onions, mushrooms, olives",
      price: 15.99,
      category: "pizza",
      allergens: ["dairy", "gluten"],
    },
    {
      id: "4",
      name: "Spaghetti Carbonara",
      description: "Pancetta, egg, parmesan, black pepper",
      price: 18.99,
      category: "pasta",
      allergens: ["dairy", "eggs", "gluten"],
    },
    {
      id: "5",
      name: "Fettuccine Alfredo",
      description: "Creamy parmesan sauce with garlic",
      price: 17.99,
      category: "pasta",
      allergens: ["dairy", "gluten"],
    },
    {
      id: "6",
      name: "Penne Arrabbiata",
      description: "Spicy tomato sauce with garlic and chili",
      price: 16.99,
      category: "pasta",
      allergens: ["gluten"],
    },
    {
      id: "7",
      name: "Caesar Salad",
      description: "Romaine lettuce, croutons, parmesan",
      price: 12.99,
      category: "salad",
      allergens: ["dairy", "gluten", "eggs"],
    },
    {
      id: "8",
      name: "Greek Salad",
      description: "Cucumber, tomato, olives, feta cheese",
      price: 13.99,
      category: "salad",
      allergens: ["dairy"],
    },
    {
      id: "9",
      name: "Tiramisu",
      description: "Coffee-soaked ladyfingers with mascarpone",
      price: 8.99,
      category: "dessert",
      allergens: ["dairy", "eggs", "gluten"],
    },
    {
      id: "10",
      name: "Chocolate Lava Cake",
      description: "Warm chocolate cake with molten center",
      price: 9.99,
      category: "dessert",
      allergens: ["dairy", "eggs", "gluten"],
    },
    {
      id: "11",
      name: "Garlic Bread",
      description: "Toasted bread with garlic butter",
      price: 6.99,
      category: "appetizer",
      allergens: ["dairy", "gluten"],
    },
    {
      id: "12",
      name: "Bruschetta",
      description: "Toasted bread with tomatoes and basil",
      price: 8.99,
      category: "appetizer",
      allergens: ["gluten"],
    },
    {
      id: "13",
      name: "Coca-Cola",
      description: "Classic soda",
      price: 2.99,
      category: "drinks",
      allergens: [],
    },
    {
      id: "14",
      name: "Sparkling Water",
      description: "Refreshing carbonated water",
      price: 2.49,
      category: "drinks",
      allergens: [],
    },
    {
      id: "15",
      name: "House Red Wine",
      description: "Glass of house red wine",
      price: 7.99,
      category: "drinks",
      allergens: [],
    }
  ];

  // Categories
  const categories: Category[] = [
    { id: "all", name: "All Items" },
    { id: "pizza", name: "Pizza" },
    { id: "pasta", name: "Pasta" },
    { id: "salad", name: "Salads" },
    { id: "appetizer", name: "Appetizers" },
    { id: "dessert", name: "Desserts" },
    { id: "drinks", name: "Drinks" }
  ];

  // Available add-ons for different menu items
  const availableAddOns: Record<string, AddOn[]> = {
    "1": [ // Classic Margherita Pizza
      { id: "extra_cheese", name: "Extra Cheese", price: 2.50 },
      { id: "extra_basil", name: "Extra Basil", price: 1.00 },
      { id: "olives", name: "Olives", price: 1.50 },
      { id: "mushrooms", name: "Mushrooms", price: 2.00 }
    ],
    "2": [ // Pepperoni Pizza
      { id: "extra_cheese", name: "Extra Cheese", price: 2.50 },
      { id: "extra_pepperoni", name: "Extra Pepperoni", price: 3.00 },
      { id: "mushrooms", name: "Mushrooms", price: 2.00 },
      { id: "olives", name: "Olives", price: 1.50 }
    ],
    "3": [ // Vegetable Supreme Pizza
      { id: "extra_cheese", name: "Extra Cheese", price: 2.50 },
      { id: "extra_vegetables", name: "Extra Vegetables", price: 2.00 },
      { id: "olives", name: "Olives", price: 1.50 }
    ],
    "4": [ // Spaghetti Carbonara
      { id: "extra_pancetta", name: "Extra Pancetta", price: 3.00 },
      { id: "extra_parmesan", name: "Extra Parmesan", price: 2.00 },
      { id: "garlic_bread", name: "Garlic Bread", price: 3.50 }
    ],
    "5": [ // Fettuccine Alfredo
      { id: "extra_parmesan", name: "Extra Parmesan", price: 2.00 },
      { id: "grilled_chicken", name: "Grilled Chicken", price: 4.00 },
      { id: "garlic_bread", name: "Garlic Bread", price: 3.50 }
    ]
  };

  // Filter menu items based on search and category
  const filteredItems = menuItems.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = activeCategory === "all" || item.category === activeCategory;
    return matchesSearch && matchesCategory;
  });

  // Initiate adding item to cart
  const initiateAddToCart = (item: MenuItem) => {
    // If order type is not selected, this shouldn't happen as the dialog should be open
    if (!orderType) {
      toast.error("Please select order type first");
      return;
    }

    // For takeaway orders, table is already set, add directly to cart
    if (orderType === "takeaway" && currentSession?.setupComplete) {
      addItemToCart(item);
      return;
    }

    // For dine-in orders, check if table setup is complete
    if (orderType === "dine-in" && currentSession?.setupComplete) {
      addItemToCart(item);
      return;
    }

    // If setup is not complete, store the item and guide user through setup
    setItemToAdd(item);

    if (!orderType) {
      setIsOrderTypeSelectionOpen(true);
      toast.info("Please select order type first");
    } else if (!selectedTable) {
      if (orderType === "dine-in") {
        setIsTableSelectionOpen(true);
        toast.info("Please select a table first");
      } else {
        setIsPartySizeInputOpen(true);
        toast.info("Please complete table setup first");
      }
    } else if (!currentSession?.setupComplete) {
      setIsPartySizeInputOpen(true);
      toast.info("Please complete table setup first");
    }
  };

  // Add item to cart
  const addItemToCart = (item: MenuItem) => {
    if (!currentSession) {
      toast.error("No active table session");
      return;
    }

    const cartItem: CartItem = {
      id: item.id,
      name: item.name,
      description: item.description,
      price: item.price,
      category: item.category,
      quantity: 1,
      allergens: item.allergens || [],
      notes: undefined,
      allergenAlert: false
    };

    // Add to session service
    tableSessionService.addCartItem(currentSession.tableId, cartItem);

    // Update local cart state
    const existingItemIndex = cart.findIndex(cartItem => cartItem.id === item.id);
    if (existingItemIndex >= 0) {
      const updatedCart = [...cart];
      updatedCart[existingItemIndex].quantity += 1;
      setCart(updatedCart);
    } else {
      setCart(prevCart => [...prevCart, cartItem]);
    }

    toast.success(`Added ${item.name} to order`);
  };

  // Remove item from cart
  const removeFromCart = (itemId: string) => {
    if (!currentSession) return;

    const existingItemIndex = cart.findIndex(item => item.id === itemId);
    if (existingItemIndex >= 0) {
      const existingItem = cart[existingItemIndex];

      if (existingItem.quantity > 1) {
        // Decrease quantity
        const updatedCart = [...cart];
        updatedCart[existingItemIndex].quantity -= 1;
        setCart(updatedCart);

        // Update session service
        tableSessionService.updateCartItemQuantity(
          currentSession.tableId,
          itemId,
          existingItem.quantity - 1
        );
      } else {
        // Remove item completely
        const updatedCart = cart.filter(item => item.id !== itemId);
        setCart(updatedCart);

        // Update session service
        tableSessionService.removeCartItem(currentSession.tableId, itemId);
      }
    }
  };

  // Update item notes
  const updateItemNotes = (itemId: string, notes: string) => {
    if (!currentSession) return;

    setItemNotes(prev => ({ ...prev, [itemId]: notes }));

    // Update session service
    tableSessionService.updateCartItemNotes(currentSession.tableId, itemId, notes);

    // Update local cart state
    const updatedCart = cart.map(item =>
      item.id === itemId ? { ...item, notes: notes || undefined } : item
    );
    setCart(updatedCart);
  };

  // Toggle allergen alert for item
  const toggleAllergenAlert = (itemId: string) => {
    if (!currentSession) return;

    const newAlertState = !itemAllergenAlerts[itemId];
    setItemAllergenAlerts(prev => ({ ...prev, [itemId]: newAlertState }));

    // Update session service
    tableSessionService.updateCartItemAllergenAlert(currentSession.tableId, itemId, newAlertState);

    // Update local cart state
    const updatedCart = cart.map(item =>
      item.id === itemId ? { ...item, allergenAlert: newAlertState } : item
    );
    setCart(updatedCart);
  };

  // Calculate cart total
  const calculateTotal = () => {
    return cart.reduce((total, item) => total + (item.price * item.quantity), 0);
  };

  // Calculate total with discounts
  const calculateTotalWithDiscounts = () => {
    const subtotal = calculateTotal();
    const totalDiscount = appliedDiscounts.reduce((total, discount) => total + discount.discount_amount, 0);
    return Math.max(0, subtotal - totalDiscount);
  };

  // Handle payment processing
  const handlePayment = async () => {
    if (!currentSession || cart.length === 0) {
      toast.error("No items in cart");
      return;
    }

    setIsProcessingPayment(true);

    try {
      // Create order data for API
      const orderData = {
        table_id: currentSession.tableId,
        customer_name: null, // Customer name not stored in session
        special_instructions: null, // Special instructions not stored in session
        promo_codes: appliedDiscounts.map(d => d.promo_code),
        items: cart.map(item => ({
          menu_item_id: item.id,
          quantity: item.quantity,
          price: item.price,
          special_instructions: item.notes || null
        }))
      };

      // Create order in database
      const createdOrder = await apiService.order.createOrder(orderData);

      // Generate order number from response or fallback
      const newOrderNumber = (createdOrder as any)?.id || `ORD-${Date.now()}`;
      setOrderNumber(newOrderNumber);

      // Update session with order number
      tableSessionService.setOrderNumber(currentSession.tableId, newOrderNumber);

      setPaymentComplete(true);
      setIsProcessingPayment(false);

      toast.success(`Payment successful! Order #${newOrderNumber} created`);

      // Open receipt printer
      setIsReceiptOpen(true);

    } catch (error) {
      setIsProcessingPayment(false);
      console.error("Payment/Order creation failed:", error);

      // Fallback to local order creation if API fails
      const fallbackOrderNumber = `ORD-${Date.now()}`;
      setOrderNumber(fallbackOrderNumber);
      tableSessionService.setOrderNumber(currentSession.tableId, fallbackOrderNumber);

      setPaymentComplete(true);
      setIsProcessingPayment(false);
      setIsReceiptOpen(true);

      toast.error("Order created locally - backend unavailable");
    }
  };

  // Complete order and clear session
  const completeOrder = async () => {
    if (!currentSession) return;

    try {
      // Save completed order to database
      await saveCompletedOrder();

      // For dine-in orders, automatically set table to cleaning status
      if (orderType === 'dine-in' && selectedTable) {
        await automateTableWorkflow();
      }

      // Clear the session
      tableSessionService.clearSession();

      // Reset all state
      setCurrentSession(null);
      setSelectedTable(null);
      setOrderType(null);
      setCart([]);
      setPaymentComplete(false);
      setOrderNumber("");
      setAppliedDiscounts([]);
      setItemAllergenAlerts({});
      setItemNotes({});
      setIsReceiptOpen(false);
      setIsPaymentOpen(false);

      // Start fresh
      setIsOrderTypeSelectionOpen(true);

      toast.success("Order completed successfully!");
    } catch (error) {
      logger.error('Failed to complete order', 'EPOS', { error: error.message });
      toast.error('Failed to complete order properly');
    }
  };

  // Save completed order to database
  const saveCompletedOrder = async () => {
    if (!currentSession || !selectedTable || !activeStaff) return;

    const orderData = {
      order_number: orderNumber,
      restaurant_id: currentRestaurant?.id,
      table_number: selectedTable.number,
      party_size: currentSession.partySize,
      order_type: orderType,
      total_amount: calculateTotalWithDiscounts(),
      payment_method: paymentMethod,
      seated_time: currentSession.createdAt,
      order_time: new Date().toISOString(),
      payment_time: new Date().toISOString(),
      staff_id: activeStaff.id,
      staff_name: activeStaff.name,
      status: 'completed',
      items: cart.map(item => ({
        name: item.name,
        quantity: item.quantity,
        price: item.price,
        allergens: item.allergens || [],
        notes: itemNotes[item.id] || '',
        allergen_alert: itemAllergenAlerts[item.id] || false
      })),
      customer_info: {
        party_size: currentSession.partySize,
        has_allergens: currentSession.hasAllergens,
        allergen_details: currentSession.allergens || []
      },
      applied_discounts: appliedDiscounts,
      split_bill_id: splitBillId
    };

    try {
      const response = await apiService.apiRequest('/mvc/orders', {
        method: 'POST',
        body: JSON.stringify(orderData)
      });

      const apiResponse = response as any;
      if (apiResponse?.success) {
        logger.info('Order saved successfully', 'EPOS', {
          orderId: apiResponse.data?.id,
          orderNumber
        });
      } else {
        throw new Error(apiResponse.message || 'Failed to save order');
      }
    } catch (error) {
      logger.warn('Failed to save order to API, storing locally', 'EPOS', {
        error: error.message
      });
      // Could implement local storage fallback here
    }
  };

  // Automate table workflow after payment
  const automateTableWorkflow = async () => {
    if (!selectedTable || !activeStaff) return;

    try {
      // Set table status to cleaning
      const response = await apiService.apiRequest(`/mvc/tables/${selectedTable.id}/status`, {
        method: 'PATCH',
        body: JSON.stringify({
          status: 'cleaning',
          notes: 'Automatic cleaning required after customer departure',
          updated_by: activeStaff.id,
          automated: true
        })
      });

      const apiResponse = response as any;
      if (apiResponse.success) {
        logger.info('Table automatically set to cleaning status', 'EPOS', {
          tableId: selectedTable.id,
          tableNumber: selectedTable.number
        });

        // Send notification to staff (could be implemented with WebSocket)
        toast.info(`Table ${selectedTable.number} marked for cleaning`);
      } else {
        throw new Error('Failed to update table status');
      }
    } catch (error) {
      logger.warn('Failed to automate table workflow', 'EPOS', {
        error: error.message,
        tableId: selectedTable.id
      });
      // Don't fail the order completion for this
    }
  };

  // Handle food info modal
  const handleFoodInfoClick = (item: MenuItem) => {
    setSelectedFoodItem(item);
    setIsFoodInfoOpen(true);
  };

  // Handle allergen dialog
  const handleAllergenDialogOpen = (item: MenuItem) => {
    setAllergenDialogItem(item);
    setShowAllergenDialog(true);
  };

  const handleAllergenDialogConfirm = () => {
    if (allergenDialogItem) {
      // Add item with allergen considerations
      const itemWithAddOns: MenuItem = {
        ...allergenDialogItem,
        // Add any selected add-ons here if needed
      };

      addItemToCart(itemWithAddOns);
      setShowAllergenDialog(false);
      setAllergenDialogItem(null);
      setSelectedAddOns([]);
    }
  };

  // Clear current session (for testing/reset)
  const clearCurrentSession = () => {
    if (currentSession) {
      tableSessionService.clearSession();
      setCurrentSession(null);
      setSelectedTable(null);
      setOrderType(null);
      setCart([]);
      setIsOrderTypeSelectionOpen(true);
      toast.info("Session cleared");
    }
  };

  // Show landing page if no workflow is selected and no active session
  if (showLandingPage) {
    return (
      <Layout title="EPOS System">
        <EPOSLandingPage
          onSelectDineIn={handleSelectDineIn}
          onSelectTakeaway={handleSelectTakeaway}
          onSelectTableManagement={handleSelectTableManagement}
        />
      </Layout>
    );
  }

  return (
    <Layout title="EPOS System">
      <div className="flex h-screen bg-background">
        {/* Left Panel - Menu */}
        <div className="flex-1 flex flex-col">
          {/* Header */}
          <div className="bg-card border-b border-border p-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-4">
                <h1 className="text-2xl font-bold text-card-foreground">EPOS System</h1>
                {currentPlanName && (
                  <Badge variant="outline" className="bg-blue-50 dark:bg-blue-950/30 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-800">
                    {currentPlanName} Plan
                  </Badge>
                )}
              </div>

              {/* Session Info and Table Management */}
              <div className="flex items-center gap-4">
                {/* Back to Landing Button */}
                {selectedWorkflow && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleBackToLanding}
                    className="flex items-center gap-2"
                  >
                    <ArrowRight className="w-4 h-4 rotate-180" />
                    Back to Menu
                  </Button>
                )}

                {/* Theme Toggle Button */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
                  className="flex items-center gap-2"
                >
                  {theme === 'dark' ? (
                    <Sun className="w-4 h-4" />
                  ) : (
                    <Moon className="w-4 h-4" />
                  )}
                  <span className="hidden md:inline">
                    {theme === 'dark' ? 'Light' : 'Dark'}
                  </span>
                </Button>

                {/* Manager Reporting Button - Only for managers */}
                {activeStaff && (activeStaff.role === 'manager' || activeStaff.role === 'admin') && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsManagerReportingOpen(true)}
                    className="flex items-center gap-2"
                  >
                    <BarChart3 className="w-4 h-4" />
                    Daily Report
                  </Button>
                )}

                {/* Table Management Button */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsTableManagementOpen(true)}
                  className="flex items-center gap-2"
                >
                  <Settings className="w-4 h-4" />
                  Table Management
                </Button>

                {/* Session Info */}
                {currentSession && selectedTable && (
                  <div className="flex items-center gap-4">
                    <div className="text-sm text-muted-foreground">
                      <div className="flex items-center gap-2">
                        <Table className="w-4 h-4" />
                        <span>
                          {orderType === 'takeaway' ? 'Takeaway' : `Table ${selectedTable.number}`}
                        </span>
                      </div>
                      <div className="flex items-center gap-2 mt-1">
                        <Users className="w-4 h-4" />
                        <span>{currentSession.partySize} guests</span>
                        {currentSession.hasAllergens && (
                          <AlertTriangle className="w-4 h-4 text-orange-500 dark:text-orange-400" />
                        )}
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={clearCurrentSession}
                      className="text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                    >
                      Clear Session
                    </Button>
                  </div>
                )}
              </div>
            </div>

            {/* Search and Category Filters */}
            <div className="flex gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search menu items..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                  disabled={!currentSession?.setupComplete}
                />
              </div>
            </div>
          </div>

          {/* Category Tabs */}
          <div className="bg-card border-b border-border">
            <Tabs value={activeCategory} onValueChange={setActiveCategory}>
              <TabsList className="w-full justify-start h-12 bg-transparent border-0 rounded-none">
                {categories.map((category) => (
                  <TabsTrigger
                    key={category.id}
                    value={category.id}
                    className="data-[state=active]:bg-blue-50 dark:data-[state=active]:bg-blue-950/30 data-[state=active]:text-blue-700 dark:data-[state=active]:text-blue-300 data-[state=active]:border-b-2 data-[state=active]:border-blue-500 dark:data-[state=active]:border-blue-400 rounded-none"
                    disabled={!currentSession?.setupComplete}
                  >
                    {category.name}
                  </TabsTrigger>
                ))}
              </TabsList>
            </Tabs>
          </div>

          {/* Menu Items Grid */}
          <div className="flex-1 overflow-y-auto p-4">
            {!currentSession?.setupComplete ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <UtensilsCrossed className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-foreground mb-2">Complete Table Setup</h3>
                  <p className="text-muted-foreground">
                    Please complete the table setup process to start adding items to your order.
                  </p>
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                {filteredItems.map((item) => (
                  <Card key={item.id} className="hover:shadow-md transition-shadow cursor-pointer">
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="font-medium text-card-foreground text-sm leading-tight">
                          {item.name}
                        </h3>
                        <span className="text-lg font-bold text-blue-600 dark:text-blue-400 ml-2">
                          £{item.price.toFixed(2)}
                        </span>
                      </div>

                      <p className="text-xs text-muted-foreground mb-3 line-clamp-2">
                        {item.description}
                      </p>

                      {/* Allergen Display */}
                      <div className="mb-3">
                        <AllergenDisplayCompact allergens={item.allergens || []} />
                      </div>

                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          onClick={() => initiateAddToCart(item)}
                          className="flex-1 bg-blue-600 hover:bg-blue-700"
                          disabled={!currentSession?.setupComplete}
                        >
                          <Plus className="w-4 h-4 mr-1" />
                          Add
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleFoodInfoClick(item)}
                        >
                          Info
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Right Panel - Cart */}
        <div className="w-96 bg-card border-l border-border flex flex-col">
          {/* Cart Header */}
          <div className="p-4 border-b border-border">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-card-foreground">Current Order</h2>
              <div className="flex items-center gap-2">
                <ShoppingCart className="w-5 h-5 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">{cart.length} items</span>
              </div>
            </div>

            {selectedTable && (
              <div className="mt-2 text-sm text-muted-foreground">
                {orderType === 'takeaway' ? 'Takeaway Order' : `Table ${selectedTable.number}`}
                {currentSession && (
                  <span className="ml-2">• {currentSession.partySize} guests</span>
                )}
              </div>
            )}
          </div>

          {/* Cart Items */}
          <div className="flex-1 overflow-y-auto">
            {cart.length === 0 ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <ShoppingCart className="w-12 h-12 text-muted-foreground mx-auto mb-3" />
                  <p className="text-muted-foreground">No items in cart</p>
                </div>
              </div>
            ) : (
              <div className="p-4 space-y-3">
                {cart.map((item, index) => (
                  <div key={`${item.id}-${index}`} className="bg-muted/50 rounded-lg p-3">
                    <div className="flex justify-between items-start mb-2">
                      <div className="flex-1">
                        <h4 className="font-medium text-card-foreground text-sm">{item.name}</h4>
                        <p className="text-xs text-muted-foreground">£{item.price.toFixed(2)} each</p>
                      </div>
                      <div className="flex items-center gap-2 ml-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => removeFromCart(item.id)}
                          className="w-8 h-8 p-0"
                        >
                          <Minus className="w-3 h-3" />
                        </Button>
                        <span className="w-8 text-center text-sm font-medium">
                          {item.quantity}
                        </span>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => addItemToCart({
                            id: item.id,
                            name: item.name,
                            description: item.description || '',
                            price: item.price,
                            category: item.category || 'general',
                            image: (item as any).image,
                            allergens: item.allergens
                          })}
                          className="w-8 h-8 p-0"
                        >
                          <Plus className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>

                    {/* Item Options */}
                    <div className="space-y-2">
                      {/* Allergen Alert Toggle */}
                      {item.allergens && item.allergens.length > 0 && (
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-muted-foreground">Allergen Alert</span>
                          <Button
                            size="sm"
                            variant={itemAllergenAlerts[item.id] ? "default" : "outline"}
                            onClick={() => toggleAllergenAlert(item.id)}
                            className={`h-6 px-2 text-xs ${
                              itemAllergenAlerts[item.id]
                                ? "bg-orange-500 hover:bg-orange-600 dark:bg-orange-600 dark:hover:bg-orange-500 text-white"
                                : "text-orange-600 dark:text-orange-400 border-orange-300 dark:border-orange-700"
                            }`}
                          >
                            <AlertTriangle className="w-3 h-3 mr-1" />
                            {itemAllergenAlerts[item.id] ? "ON" : "OFF"}
                          </Button>
                        </div>
                      )}

                      {/* Notes Input */}
                      <div>
                        <Input
                          placeholder="Add notes..."
                          value={itemNotes[item.id] || ""}
                          onChange={(e) => updateItemNotes(item.id, e.target.value)}
                          className="h-8 text-xs"
                        />
                      </div>
                    </div>

                    <div className="flex justify-between items-center mt-2 pt-2 border-t border-border">
                      <span className="text-xs text-muted-foreground">
                        Subtotal: £{(item.price * item.quantity).toFixed(2)}
                      </span>
                      {item.allergenAlert && (
                        <AlertTriangle className="w-4 h-4 text-orange-500 dark:text-orange-400" />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Cart Footer */}
          {cart.length > 0 && (
            <div className="border-t border-border p-4 space-y-4">
              {/* Totals */}
              <div className="space-y-2">
                <div className="flex justify-between text-sm text-card-foreground">
                  <span>Subtotal:</span>
                  <span>£{calculateTotal().toFixed(2)}</span>
                </div>
                {appliedDiscounts.length > 0 && (
                  <>
                    {appliedDiscounts.map((discount, index) => (
                      <div key={index} className="flex justify-between text-sm text-green-600 dark:text-green-400">
                        <span>{discount.promo_code}:</span>
                        <span>-£{discount.discount_amount.toFixed(2)}</span>
                      </div>
                    ))}
                    <Separator />
                  </>
                )}
                <div className="flex justify-between text-lg font-bold text-card-foreground">
                  <span>Total:</span>
                  <span>£{calculateTotalWithDiscounts().toFixed(2)}</span>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="space-y-2">
                <FeatureGate feature="advanced-epos" fallback={null}>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setIsSplitBill(true)}
                      className="flex-1"
                    >
                      <Tag className="w-4 h-4 mr-1" />
                      Split Bill
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setIsDiscountOpen(true)}
                      className="flex-1"
                    >
                      <Tag className="w-4 h-4 mr-1" />
                      Discount
                    </Button>
                  </div>
                </FeatureGate>

                <Button
                  onClick={() => setIsPaymentOpen(true)}
                  className="w-full bg-green-600 hover:bg-green-700"
                  disabled={cart.length === 0}
                >
                  <CreditCard className="w-4 h-4 mr-2" />
                  Process Payment
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Dialog Components */}

      {/* Order Type Selection Dialog */}
      <OrderTypeSelection
        isOpen={isOrderTypeSelectionOpen}
        onOrderTypeSelect={handleOrderTypeSelect}
        onClose={handleBackToLanding}
      />

      {/* Table Selection Dialog */}
      <TableSelection
        isOpen={isTableSelectionOpen}
        onClose={() => setIsTableSelectionOpen(false)}
        onSelectTable={handleTableSelect}
      />

      {/* Enhanced Table Selection Dialog (for subscription-aware features) */}
      <FeatureGate feature="advanced-epos" fallback={null}>
        <EnhancedTableSelection
          isOpen={isTableSelectionOpen}
          onClose={() => setIsTableSelectionOpen(false)}
          onSelectTable={handleTableSelect}
          orderType={orderType || 'dine-in'}
        />
      </FeatureGate>

      {/* Party Size Input Dialog */}
      <PartySizeInput
        isOpen={isPartySizeInputOpen}
        tableNumber={pendingTableInfo?.number || 0}
        onConfirm={handlePartySizeConfirm}
        onCancel={handlePartySizeBack}
      />

      {/* Allergen Info Collection Dialog */}
      <AllergenInfoCollection
        isOpen={isAllergenInfoOpen}
        tableNumber={pendingTableInfo?.number || 0}
        onConfirm={handleAllergenInfoConfirm}
        onBack={handleAllergenInfoBack}
        onClose={() => setIsAllergenInfoOpen(false)}
      />

      {/* Allergen Details Dialog */}
      <AllergenDetails
        isOpen={isAllergenDetailsOpen}
        tableNumber={pendingTableInfo?.number || 0}
        onConfirm={handleAllergenDetailsConfirm}
        onBack={handleAllergenDetailsBack}
        onClose={() => setIsAllergenDetailsOpen(false)}
      />

      {/* Food Info Modal */}
      <FoodInfoModal
        isOpen={isFoodInfoOpen}
        onClose={() => setIsFoodInfoOpen(false)}
        item={selectedFoodItem}
      />

      {/* Cart Item Options Dialog */}
      <CartItemOptions
        isOpen={showAllergenDialog}
        onClose={() => setShowAllergenDialog(false)}
        item={allergenDialogItem}
        onConfirm={handleAllergenDialogConfirm}
        selectedAddOns={selectedAddOns}
        onAddOnsChange={setSelectedAddOns}
        availableAddOns={allergenDialogItem ? availableAddOns[allergenDialogItem.id] || [] : []}
      />

      {/* Payment Processing Dialog */}
      <PaymentProcessing
        isOpen={isPaymentOpen}
        onClose={() => setIsPaymentOpen(false)}
        cart={cart}
        total={calculateTotalWithDiscounts()}
        paymentMethod={paymentMethod}
        onPaymentMethodChange={setPaymentMethod}
        onProcessPayment={handlePayment}
        isProcessing={isProcessingPayment}
        paymentComplete={paymentComplete}
        orderNumber={orderNumber}
        onComplete={completeOrder}
      />

      {/* Split Bill Manager */}
      <FeatureGate feature="advanced-epos" fallback={null}>
        <SplitBillManager
          isOpen={isSplitBill}
          onClose={() => setIsSplitBill(false)}
          cart={cart}
          onSplitComplete={(splitId) => {
            setSplitBillId(splitId);
            setIsSplitBill(false);
            toast.success("Bill split successfully");
          }}
        />
      </FeatureGate>

      {/* Discount Manager */}
      <FeatureGate feature="advanced-epos" fallback={null}>
        <DiscountManager
          isOpen={isDiscountOpen}
          onClose={() => setIsDiscountOpen(false)}
          orderTotal={calculateTotal()}
          orderItems={cart}
          appliedDiscounts={appliedDiscounts}
          onDiscountApplied={(discount) => {
            setAppliedDiscounts(prev => [...prev, discount]);
            setIsDiscountOpen(false);
            toast.success("Discount applied successfully!");
          }}
          onDiscountRemoved={(promoCode) => {
            setAppliedDiscounts(prev => prev.filter(d => d.promo_code !== promoCode));
            toast.success("Discount removed");
          }}
        />
      </FeatureGate>

      {/* Receipt Printer */}
      <ReceiptPrinter
        isOpen={isReceiptOpen}
        onClose={() => setIsReceiptOpen(false)}
        orderNumber={orderNumber}
        cart={cart}
        total={calculateTotalWithDiscounts()}
        tableInfo={selectedTable}
        sessionInfo={currentSession}
        paymentMethod={paymentMethod}
      />

      {/* Table Management */}
      <TableManagement
        isOpen={isTableManagementOpen}
        onClose={() => setIsTableManagementOpen(false)}
      />



      {/* Staff Notification System - Only show when not on landing page */}
      {!showLandingPage && activeStaff && (
        <StaffNotificationSystem
          isMinimized={isNotificationMinimized}
          onToggleMinimize={() => setIsNotificationMinimized(!isNotificationMinimized)}
        />
      )}

      {/* Manager Reporting */}
      <ManagerReporting
        isOpen={isManagerReportingOpen}
        onClose={() => setIsManagerReportingOpen(false)}
      />
    </Layout>
  );
};

export default EPOS;
