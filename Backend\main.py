import os
import uvicorn
from app.api import app

if __name__ == "__main__":
    # Get configuration from environment variables
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", "5002"))
    reload = os.getenv("RELOAD", "true").lower() == "true"
    workers = int(os.getenv("WORKERS", "1"))

    # For production, use multiple workers and disable reload
    if os.getenv("ENVIRONMENT") == "production":
        port = int(os.getenv("PORT", "8000"))
        reload = False
        workers = int(os.getenv("WORKERS", "4"))

    uvicorn.run(
        "app.api:app",
        host=host,
        port=port,
        reload=reload,
        workers=workers if not reload else 1  # Workers only work without reload
    )
