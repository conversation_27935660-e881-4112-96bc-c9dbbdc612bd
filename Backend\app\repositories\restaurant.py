"""
Restaurant repository for database operations.
Handles all restaurant and restaurant user related database operations.
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func
from sqlalchemy.orm import selectinload

from app.models.database_models import Restaurant, RestaurantUser
from app.repositories.base import BaseRepository
import logging

logger = logging.getLogger(__name__)

class RestaurantRepository(BaseRepository[Restaurant]):
    """Repository for restaurant operations"""
    
    def __init__(self):
        super().__init__(Restaurant)
    
    async def get_by_code(self, db: AsyncSession, code: str) -> Optional[Restaurant]:
        """Get restaurant by unique code"""
        try:
            result = await db.execute(
                select(Restaurant).where(Restaurant.code == code)
            )
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting restaurant by code {code}: {e}")
            raise
    
    async def get_active_restaurants(self, db: AsyncSession) -> List[Restaurant]:
        """Get all active restaurants"""
        try:
            result = await db.execute(
                select(Restaurant)
                .where(Restaurant.is_active == True)
                .order_by(Restaurant.name)
            )
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting active restaurants: {e}")
            raise
    
    async def search_restaurants(self, db: AsyncSession, search_term: str) -> List[Restaurant]:
        """Search restaurants by name, code, or owner name"""
        try:
            search_pattern = f"%{search_term}%"
            result = await db.execute(
                select(Restaurant)
                .where(
                    or_(
                        Restaurant.name.ilike(search_pattern),
                        Restaurant.code.ilike(search_pattern),
                        Restaurant.owner_name.ilike(search_pattern)
                    )
                )
                .order_by(Restaurant.name)
            )
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error searching restaurants with term '{search_term}': {e}")
            raise
    
    async def get_with_users(self, db: AsyncSession, restaurant_id: str) -> Optional[Restaurant]:
        """Get restaurant with all its users loaded"""
        try:
            result = await db.execute(
                select(Restaurant)
                .options(selectinload(Restaurant.users))
                .where(Restaurant.id == restaurant_id)
            )
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting restaurant with users {restaurant_id}: {e}")
            raise
    
    async def verify_credentials(self, db: AsyncSession, code: str, password: str) -> Optional[Restaurant]:
        """Verify restaurant login credentials"""
        try:
            # Note: In production, password should be hashed
            result = await db.execute(
                select(Restaurant)
                .where(
                    and_(
                        Restaurant.code == code,
                        Restaurant.password == password,  # TODO: Hash comparison
                        Restaurant.is_active == True
                    )
                )
            )
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error verifying credentials for restaurant {code}: {e}")
            raise
    
    async def get_restaurant_stats(self, db: AsyncSession, restaurant_id: str) -> Dict[str, Any]:
        """Get basic statistics for a restaurant"""
        try:
            # Get user count
            user_count_result = await db.execute(
                select(func.count(RestaurantUser.id))
                .where(RestaurantUser.restaurant_id == restaurant_id)
            )
            user_count = user_count_result.scalar()
            
            # Get active user count
            active_user_count_result = await db.execute(
                select(func.count(RestaurantUser.id))
                .where(
                    and_(
                        RestaurantUser.restaurant_id == restaurant_id,
                        RestaurantUser.status == "active"
                    )
                )
            )
            active_user_count = active_user_count_result.scalar()
            
            return {
                "total_users": user_count,
                "active_users": active_user_count,
                "inactive_users": user_count - active_user_count
            }
        except Exception as e:
            logger.error(f"Error getting restaurant stats for {restaurant_id}: {e}")
            raise

class RestaurantUserRepository(BaseRepository[RestaurantUser]):
    """Repository for restaurant user operations"""
    
    def __init__(self):
        super().__init__(RestaurantUser)
    
    async def get_by_restaurant(self, db: AsyncSession, restaurant_id: str) -> List[RestaurantUser]:
        """Get all users for a specific restaurant"""
        try:
            result = await db.execute(
                select(RestaurantUser)
                .where(RestaurantUser.restaurant_id == restaurant_id)
                .order_by(RestaurantUser.name)
            )
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting users for restaurant {restaurant_id}: {e}")
            raise
    
    async def get_active_users(self, db: AsyncSession, restaurant_id: str) -> List[RestaurantUser]:
        """Get active users for a specific restaurant"""
        try:
            result = await db.execute(
                select(RestaurantUser)
                .where(
                    and_(
                        RestaurantUser.restaurant_id == restaurant_id,
                        RestaurantUser.status == "active"
                    )
                )
                .order_by(RestaurantUser.name)
            )
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting active users for restaurant {restaurant_id}: {e}")
            raise
    
    async def verify_pin(self, db: AsyncSession, restaurant_id: str, pin: str) -> Optional[RestaurantUser]:
        """Verify user PIN for a specific restaurant"""
        try:
            # Check restaurant-specific users first
            result = await db.execute(
                select(RestaurantUser)
                .where(
                    and_(
                        RestaurantUser.restaurant_id == restaurant_id,
                        RestaurantUser.pin == pin,
                        RestaurantUser.status == "active"
                    )
                )
            )
            user = result.scalar_one_or_none()
            
            # If no restaurant-specific user found, check system admin users (restaurant_id = "0")
            if not user:
                result = await db.execute(
                    select(RestaurantUser)
                    .where(
                        and_(
                            RestaurantUser.restaurant_id == "0",
                            RestaurantUser.pin == pin,
                            RestaurantUser.status == "active"
                        )
                    )
                )
                user = result.scalar_one_or_none()
            
            return user
        except Exception as e:
            logger.error(f"Error verifying PIN for restaurant {restaurant_id}: {e}")
            raise
    
    async def get_by_role(self, db: AsyncSession, restaurant_id: str, role: str) -> List[RestaurantUser]:
        """Get users by role for a specific restaurant"""
        try:
            result = await db.execute(
                select(RestaurantUser)
                .where(
                    and_(
                        RestaurantUser.restaurant_id == restaurant_id,
                        RestaurantUser.role == role
                    )
                )
                .order_by(RestaurantUser.name)
            )
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting users by role {role} for restaurant {restaurant_id}: {e}")
            raise
    
    async def search_users(self, db: AsyncSession, restaurant_id: str, search_term: str) -> List[RestaurantUser]:
        """Search users within a restaurant"""
        try:
            search_pattern = f"%{search_term}%"
            result = await db.execute(
                select(RestaurantUser)
                .where(
                    and_(
                        RestaurantUser.restaurant_id == restaurant_id,
                        or_(
                            RestaurantUser.name.ilike(search_pattern),
                            RestaurantUser.email.ilike(search_pattern),
                            RestaurantUser.role.ilike(search_pattern),
                            RestaurantUser.position.ilike(search_pattern)
                        )
                    )
                )
                .order_by(RestaurantUser.name)
            )
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error searching users in restaurant {restaurant_id} with term '{search_term}': {e}")
            raise
    
    async def get_all_users(self, db: AsyncSession) -> List[RestaurantUser]:
        """Get all restaurant users across all restaurants"""
        try:
            result = await db.execute(
                select(RestaurantUser)
                .order_by(RestaurantUser.restaurant_id, RestaurantUser.name)
            )
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting all restaurant users: {e}")
            raise
    
    async def update_performance(self, db: AsyncSession, user_id: str, performance: int) -> Optional[RestaurantUser]:
        """Update user performance score"""
        try:
            return await self.update(db, user_id, {"performance": performance})
        except Exception as e:
            logger.error(f"Error updating performance for user {user_id}: {e}")
            raise
