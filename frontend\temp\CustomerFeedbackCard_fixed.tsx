import { 
  <PERSON>, 
  Card<PERSON>ontent, 
  <PERSON>Header, 
  CardTitle 
} from "@/components/ui/card";
import { StarIcon } from "lucide-react";

interface CustomerReview {
  id: string;
  customerName: string;
  rating: number;
  comment: string;
  date: string;
}

interface CustomerFeedbackCardProps {
  reviews?: CustomerReview[];
}

// Default empty reviews array
const defaultReviews: CustomerReview[] = [];

const CustomerFeedbackCard = ({ reviews = defaultReviews }: CustomerFeedbackCardProps) => {
  // Sort reviews by date (newest first)
  const sortedReviews = [...reviews].sort((a, b) => 
    new Date(b.date).getTime() - new Date(a.date).getTime()
  );
  
  // Calculate average rating
  const averageRating = reviews.length > 0 
    ? reviews.reduce((acc, review) => acc + review.rating, 0) / reviews.length
    : 0;
  
  // Generate stars for ratings
  const renderStars = (rating: number) => {
    return Array(5).fill(0).map((_, i) => (
      <StarIcon 
        key={i} 
        className={`h-4 w-4 ${i < rating ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'}`} 
      />
    ));
  };
  
  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg font-medium">Customer Feedback</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-1">
            {renderStars(Math.round(averageRating))}
          </div>
          <div className="text-2xl font-bold">{averageRating.toFixed(1)}</div>
        </div>
        
        {reviews.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No customer reviews yet
          </div>
        ) : (
          <div className="space-y-4 max-h-[250px] overflow-auto pr-1">
            {sortedReviews.map((review) => (
              <div key={review.id} className="p-3 bg-muted rounded-lg">
                <div className="flex justify-between mb-1">
                  <div className="font-medium">{review.customerName}</div>
                  <div className="text-xs text-muted-foreground">
                    {new Date(review.date).toLocaleDateString()}
                  </div>
                </div>
                <div className="flex mb-2">
                  {renderStars(review.rating)}
                </div>
                <p className="text-sm text-muted-foreground">{review.comment}</p>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default CustomerFeedbackCard;
