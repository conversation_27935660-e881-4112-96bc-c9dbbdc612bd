<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Credentials Test - RestroManage</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-title {
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 1.1em;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .code {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .credential-item {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
        }
        .credential-item.success {
            background: #d4edda;
            border-left: 4px solid #28a745;
        }
        .credential-item.error {
            background: #f8d7da;
            border-left: 4px solid #dc3545;
        }
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Login Credentials Test - RestroManage</h1>
        
        <div class="status info">
            <strong>Purpose:</strong> Test the updated login page with all restaurant account credentials displayed and verify login functionality.
        </div>

        <div class="test-section">
            <div class="test-title">✅ Login Page Updates Applied</div>
            <div class="grid">
                <div>
                    <h4>1. Enhanced Credentials Display</h4>
                    <div class="code">✅ Renamed "Demo Restaurant Accounts" → "All Restaurant Accounts"
✅ Fetches all restaurants from backend API
✅ Shows restaurant code, password, and owner PIN
✅ Copy-to-clipboard functionality
✅ Auto-fill form functionality</div>
                </div>
                <div>
                    <h4>2. Smart Password Detection</h4>
                    <div class="code">✅ Intelligent password mapping for known restaurants
✅ Pattern-based password detection for test accounts
✅ Fallback to common test passwords
✅ Password visibility toggle</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🧪 Credential Testing</div>
            <button onclick="testAllCredentials()" id="testCredsBtn">Test All Credentials</button>
            <button onclick="fetchRestaurants()" id="fetchBtn">Fetch Restaurants</button>
            <button onclick="clearResults()">Clear Results</button>
            
            <div id="credentialResults"></div>
        </div>

        <div class="test-section">
            <div class="test-title">📋 Manual Test Steps</div>
            <ol>
                <li><strong>Open Login Page:</strong> 
                    <button onclick="window.open('/restaurant-login', '_blank')">Open Restaurant Login</button>
                </li>
                <li><strong>Verify Credentials Display:</strong> Check that "All Restaurant Accounts" section shows all restaurants</li>
                <li><strong>Test Auto-Fill:</strong> Click "Use" button for any restaurant to auto-fill the form</li>
                <li><strong>Test Copy Functions:</strong> Click copy buttons for codes, passwords, and PINs</li>
                <li><strong>Test Login:</strong> Submit the form with auto-filled credentials</li>
                <li><strong>Verify Success:</strong> Should redirect to staff login page</li>
            </ol>
        </div>

        <div class="test-section">
            <div class="test-title">🔗 Quick Navigation</div>
            <button onclick="window.open('/restaurant-login', '_blank')">Restaurant Login</button>
            <button onclick="window.open('/staff-login', '_blank')">Staff Login</button>
            <button onclick="window.open('/auth/register', '_blank')">Registration</button>
            <button onclick="window.open('http://localhost:5001/docs', '_blank')">Backend API</button>
        </div>

        <div class="test-section">
            <div class="test-title">🐛 Debug Console</div>
            <div class="code" id="debugConsole">Ready for testing...</div>
            <button onclick="clearDebugConsole()">Clear Console</button>
        </div>

        <div id="finalStatus" class="status info">
            <strong>Status:</strong> Ready to test. Click "Test All Credentials" to verify login functionality.
        </div>
    </div>

    <script>
        let debugLog = [];
        let restaurants = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            debugLog.push(logEntry);

            const debugConsoleElement = document.getElementById('debugConsole');
            debugConsoleElement.textContent = debugLog.slice(-10).join('\n');

            // Also log to browser console
            if (type === 'error') {
                console.error(message);
            } else {
                console.log(message);
            }
        }

        async function fetchRestaurants() {
            const btn = document.getElementById('fetchBtn');
            btn.disabled = true;
            btn.textContent = 'Fetching...';
            
            try {
                log('🔄 Fetching all restaurants...');
                const response = await fetch('/api/restaurants');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                restaurants = await response.json();
                log(`✅ Fetched ${restaurants.length} restaurants`);
                
                displayRestaurants();
                
            } catch (error) {
                log(`❌ Failed to fetch restaurants: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = 'Fetch Restaurants';
            }
        }

        function displayRestaurants() {
            const resultsDiv = document.getElementById('credentialResults');
            
            if (restaurants.length === 0) {
                resultsDiv.innerHTML = '<div class="warning">No restaurants found. Try fetching first.</div>';
                return;
            }
            
            let html = '<h4>Available Restaurant Accounts:</h4>';
            html += '<div class="grid">';
            
            restaurants.forEach(restaurant => {
                const password = getRestaurantPassword(restaurant);
                const ownerPin = restaurant.setupData?.ownerPin || '1234';
                
                html += `
                    <div class="credential-item">
                        <strong>${restaurant.name}</strong><br>
                        <small>Code: <code>${restaurant.code}</code></small><br>
                        <small>Password: <code>${password}</code></small><br>
                        <small>Owner PIN: <code>${ownerPin}</code></small><br>
                        <small>Email: ${restaurant.email}</small><br>
                        <button onclick="testLogin('${restaurant.code}', '${password}')" style="margin-top: 5px; padding: 5px 10px; font-size: 12px;">
                            Test Login
                        </button>
                    </div>
                `;
            });
            
            html += '</div>';
            resultsDiv.innerHTML = html;
        }

        function getRestaurantPassword(restaurant) {
            const passwordMap = {
                'GK001': 'gourmet123',
                'PP002': 'pasta123',
                'SS003': 'sushi123',
                'TR511': 'testpass123',
                'TEST123': 'testpass123',
                'DTR735': 'testpass123',
                'ITR372': 'testpass123',
                'PU837': 'Pratham@2410'  // Specific password for Pratham-Test restaurant
            };

            if (passwordMap[restaurant.code]) {
                return passwordMap[restaurant.code];
            }

            // Special case for Pratham-Test restaurant
            if (restaurant.code === 'PU837' ||
                restaurant.name?.includes('Pratham') ||
                restaurant.email === '<EMAIL>') {
                return 'Pratham@2410';
            }

            // For newer restaurants (created after 2025-05-26), use testpass123
            if (restaurant.createdAt) {
                const createdDate = new Date(restaurant.createdAt);
                const cutoffDate = new Date('2025-05-26');
                if (createdDate > cutoffDate) {
                    return 'testpass123';
                }
            }

            // For restaurants with test/debug indicators
            if (restaurant.name?.toLowerCase().includes('test') ||
                restaurant.name?.toLowerCase().includes('debug') ||
                restaurant.email?.includes('test') ||
                restaurant.email?.includes('debug')) {
                return 'testpass123';
            }

            // For restaurants with setupData (indicating they went through registration)
            if (restaurant.setupData && Object.keys(restaurant.setupData).length > 0) {
                return 'testpass123';
            }

            return 'testpass123';
        }

        async function testLogin(code, password) {
            log(`🔐 Testing login for ${code}...`);
            
            try {
                const response = await fetch('/api/restaurants/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ code, password })
                });

                if (response.ok) {
                    const data = await response.json();
                    log(`✅ Login successful for ${code}: ${data.restaurant?.name}`);
                    updateCredentialStatus(code, true);
                } else {
                    const errorData = await response.json();
                    log(`❌ Login failed for ${code}: ${errorData.detail}`, 'error');
                    updateCredentialStatus(code, false);
                }
            } catch (error) {
                log(`❌ Login error for ${code}: ${error.message}`, 'error');
                updateCredentialStatus(code, false);
            }
        }

        function updateCredentialStatus(code, success) {
            const credentialItems = document.querySelectorAll('.credential-item');
            credentialItems.forEach(item => {
                if (item.textContent.includes(code)) {
                    item.className = `credential-item ${success ? 'success' : 'error'}`;
                }
            });
        }

        async function testAllCredentials() {
            const btn = document.getElementById('testCredsBtn');
            btn.disabled = true;
            btn.textContent = 'Testing...';
            
            if (restaurants.length === 0) {
                await fetchRestaurants();
            }
            
            log('🚀 Testing all restaurant credentials...');
            
            let successCount = 0;
            let totalCount = restaurants.length;
            
            for (const restaurant of restaurants) {
                const password = getRestaurantPassword(restaurant);
                try {
                    const response = await fetch('/api/restaurants/auth/login', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ code: restaurant.code, password })
                    });

                    if (response.ok) {
                        successCount++;
                        updateCredentialStatus(restaurant.code, true);
                    } else {
                        updateCredentialStatus(restaurant.code, false);
                    }
                } catch (error) {
                    updateCredentialStatus(restaurant.code, false);
                }
                
                // Small delay to avoid overwhelming the server
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            log(`🎯 Testing complete: ${successCount}/${totalCount} successful logins`);
            
            const finalStatusDiv = document.getElementById('finalStatus');
            if (successCount === totalCount) {
                finalStatusDiv.className = 'status success';
                finalStatusDiv.innerHTML = `<strong>🎉 All ${totalCount} credentials work!</strong> The login page is ready for use.`;
            } else {
                finalStatusDiv.className = 'status warning';
                finalStatusDiv.innerHTML = `<strong>⚠️ ${successCount}/${totalCount} credentials work.</strong> Some passwords may need updating.`;
            }
            
            btn.disabled = false;
            btn.textContent = 'Test All Credentials';
        }

        function clearResults() {
            document.getElementById('credentialResults').innerHTML = '';
            document.getElementById('finalStatus').className = 'status info';
            document.getElementById('finalStatus').innerHTML = '<strong>Status:</strong> Ready to test. Click "Test All Credentials" to verify login functionality.';
        }

        function clearDebugConsole() {
            debugLog = [];
            document.getElementById('debugConsole').textContent = 'Console cleared...';
        }

        // Auto-fetch restaurants on page load
        window.addEventListener('load', () => {
            log('🔐 Login Credentials Test Page Loaded');
            log('Frontend: http://localhost:5174');
            log('Backend: http://localhost:5001');
            fetchRestaurants();
        });
    </script>
</body>
</html>
