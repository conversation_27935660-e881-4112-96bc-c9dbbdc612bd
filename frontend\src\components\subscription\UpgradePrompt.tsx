import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Crown, 
  ArrowRight, 
  Star, 
  Lock,
  Zap,
  TrendingUp
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import logger from '@/utils/logger';

interface UpgradePromptProps {
  title: string;
  description: string;
  suggestedPlan: string;
  currentPlan: string;
  featureName: string;
  variant?: 'card' | 'banner' | 'modal';
  size?: 'sm' | 'md' | 'lg';
}

export const UpgradePrompt: React.FC<UpgradePromptProps> = ({
  title,
  description,
  suggestedPlan,
  currentPlan,
  featureName,
  variant = 'card',
  size = 'md'
}) => {
  const navigate = useNavigate();

  const handleUpgradeClick = () => {
    logger.userAction('upgrade prompt clicked', 'UpgradePrompt', {
      featureName,
      currentPlan,
      suggestedPlan
    });
    
    // Navigate to subscription management or upgrade flow
    navigate('/settings/subscription');
  };

  const handleLearnMoreClick = () => {
    logger.userAction('learn more clicked', 'UpgradePrompt', {
      featureName,
      currentPlan,
      suggestedPlan
    });
    
    // Navigate to pricing page or feature comparison
    navigate('/pricing');
  };

  if (variant === 'banner') {
    return (
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4 mb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <Crown className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-900">{title}</h4>
              <p className="text-sm text-gray-600">{description}</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={handleLearnMoreClick}>
              Learn More
            </Button>
            <Button size="sm" onClick={handleUpgradeClick}>
              Upgrade Now
              <ArrowRight className="h-4 w-4 ml-1" />
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (variant === 'modal') {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <Card className="w-full max-w-md mx-4">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
              <Lock className="h-6 w-6 text-blue-600" />
            </div>
            <CardTitle className="text-xl">{title}</CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-gray-600">{description}</p>
            <div className="flex items-center justify-center space-x-2">
              <Badge variant="outline">{currentPlan}</Badge>
              <ArrowRight className="h-4 w-4 text-gray-400" />
              <Badge className="bg-blue-600">{suggestedPlan}</Badge>
            </div>
            <div className="flex space-x-2">
              <Button variant="outline" className="flex-1" onClick={handleLearnMoreClick}>
                Learn More
              </Button>
              <Button className="flex-1" onClick={handleUpgradeClick}>
                Upgrade Now
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Default card variant
  const cardSizeClasses = {
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8'
  };

  return (
    <Card className={`border-2 border-dashed border-gray-300 bg-gray-50 ${cardSizeClasses[size]}`}>
      <CardContent className="text-center space-y-4">
        <div className="mx-auto w-16 h-16 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center">
          <Crown className="h-8 w-8 text-blue-600" />
        </div>
        
        <div className="space-y-2">
          <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
          <p className="text-gray-600 text-sm">{description}</p>
        </div>

        <div className="flex items-center justify-center space-x-2">
          <Badge variant="outline" className="text-xs">
            Current: {currentPlan}
          </Badge>
          <ArrowRight className="h-4 w-4 text-gray-400" />
          <Badge className="bg-blue-600 text-xs">
            <Star className="h-3 w-3 mr-1" />
            {suggestedPlan}
          </Badge>
        </div>

        <div className="space-y-2">
          <Button onClick={handleUpgradeClick} className="w-full">
            <Zap className="h-4 w-4 mr-2" />
            Upgrade to {suggestedPlan}
          </Button>
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={handleLearnMoreClick}
            className="w-full text-xs"
          >
            <TrendingUp className="h-3 w-3 mr-1" />
            Compare Plans & Features
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default UpgradePrompt;
