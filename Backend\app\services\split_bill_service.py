from typing import List, Optional, Dict, Any
from datetime import datetime
import uuid
from app.models.split_bills import (
    SplitBill, SplitBillCreate, SplitType, SplitPortion, SplitBillItem,
    PaymentRequest, PaymentResponse, SplitBillStatus, ReceiptData
)
from app.models.orders import Order, OrderItem
from app.utils.storage import get_all, get_by_id, create, update, query

class SplitBillService:
    """Service for handling split bill functionality"""
    
    @staticmethod
    def create_split_bill(split_bill_data: SplitBillCreate) -> SplitBill:
        """Create a new split bill from an order"""
        
        # Get original order
        original_order = get_by_id("orders", split_bill_data.original_order_id)
        if not original_order:
            raise ValueError("Original order not found")
        
        if original_order.get("is_split_bill", False):
            raise ValueError("Order is already split")
        
        # Calculate split portions based on split type
        portions = SplitBillService._calculate_split_portions(
            original_order, split_bill_data
        )
        
        # Create split bill
        split_bill_dict = split_bill_data.dict()
        split_bill_dict.update({
            "id": f"split_{uuid.uuid4().hex[:8]}",
            "status": SplitBillStatus.PENDING,
            "original_total": original_order["total"],
            "total_paid": 0.0,
            "remaining_balance": original_order["total"],
            "portions": [portion.dict() for portion in portions]
        })
        
        split_bill = create("split_bills", split_bill_dict)
        
        # Update original order
        update("orders", split_bill_data.original_order_id, {
            "is_split_bill": True,
            "split_bill_id": split_bill["id"]
        })
        
        return SplitBill(**split_bill)
    
    @staticmethod
    def _calculate_split_portions(
        order: Dict[str, Any], 
        split_data: SplitBillCreate
    ) -> List[SplitPortion]:
        """Calculate split portions based on split type"""
        
        portions = []
        order_items = order["items"]
        total_amount = order["total"]
        tax_rate = 0.20  # 20% VAT
        
        if split_data.split_type == SplitType.EQUAL_SPLIT:
            # Split equally among all portions
            amount_per_portion = total_amount / split_data.number_of_splits
            
            for i in range(split_data.number_of_splits):
                portion_data = split_data.portions[i] if i < len(split_data.portions) else {}
                
                portion = SplitPortion(
                    id=f"portion_{uuid.uuid4().hex[:8]}",
                    portion_number=i + 1,
                    customer_name=portion_data.get("customer_name"),
                    customer_id=portion_data.get("customer_id"),
                    amount=amount_per_portion,
                    total_amount=amount_per_portion,
                    tax_amount=amount_per_portion * tax_rate / (1 + tax_rate),
                    tip_amount=0.0,
                    discount_amount=0.0,
                    items=[]
                )
                portions.append(portion)
        
        elif split_data.split_type == SplitType.BY_ITEMS:
            # Split by specific items assigned to each person
            for i, portion_data in enumerate(split_data.portions):
                assigned_items = []
                portion_total = 0.0
                
                if portion_data.items:
                    for item_data in portion_data.items:
                        # Find matching order item
                        order_item = next(
                            (item for item in order_items if item["id"] == item_data.order_item_id),
                            None
                        )
                        
                        if order_item:
                            split_item = SplitBillItem(
                                id=f"split_item_{uuid.uuid4().hex[:8]}",
                                order_item_id=item_data.order_item_id,
                                quantity=item_data.quantity,
                                price=item_data.price,
                                item_name=order_item["name"],
                                subtotal=item_data.price * item_data.quantity,
                                customer_name=portion_data.customer_name,
                                customer_id=portion_data.customer_id
                            )
                            assigned_items.append(split_item)
                            portion_total += split_item.subtotal
                
                portion = SplitPortion(
                    id=f"portion_{uuid.uuid4().hex[:8]}",
                    portion_number=i + 1,
                    customer_name=portion_data.customer_name,
                    customer_id=portion_data.customer_id,
                    amount=portion_total,
                    total_amount=portion_total,
                    tax_amount=portion_total * tax_rate / (1 + tax_rate),
                    tip_amount=0.0,
                    discount_amount=0.0,
                    items=assigned_items
                )
                portions.append(portion)
        
        elif split_data.split_type == SplitType.CUSTOM_AMOUNTS:
            # Custom amounts specified for each portion
            for i, portion_data in enumerate(split_data.portions):
                portion = SplitPortion(
                    id=f"portion_{uuid.uuid4().hex[:8]}",
                    portion_number=i + 1,
                    customer_name=portion_data.customer_name,
                    customer_id=portion_data.customer_id,
                    amount=portion_data.amount,
                    total_amount=portion_data.amount,
                    tax_amount=portion_data.amount * tax_rate / (1 + tax_rate),
                    tip_amount=portion_data.tip_amount or 0.0,
                    discount_amount=portion_data.discount_amount or 0.0,
                    items=[]
                )
                portions.append(portion)
        
        elif split_data.split_type == SplitType.PERCENTAGE_SPLIT:
            # Split by percentage
            for i, portion_data in enumerate(split_data.portions):
                percentage = portion_data.amount / 100  # Assuming amount is percentage
                portion_amount = total_amount * percentage
                
                portion = SplitPortion(
                    id=f"portion_{uuid.uuid4().hex[:8]}",
                    portion_number=i + 1,
                    customer_name=portion_data.customer_name,
                    customer_id=portion_data.customer_id,
                    amount=portion_amount,
                    total_amount=portion_amount,
                    tax_amount=portion_amount * tax_rate / (1 + tax_rate),
                    tip_amount=0.0,
                    discount_amount=0.0,
                    items=[]
                )
                portions.append(portion)
        
        return portions
    
    @staticmethod
    def process_payment(payment_request: PaymentRequest) -> PaymentResponse:
        """Process payment for a split bill portion"""
        
        # Get split bill
        split_bill = get_by_id("split_bills", payment_request.split_bill_id)
        if not split_bill:
            raise ValueError("Split bill not found")
        
        # Find the portion
        portion = None
        for p in split_bill["portions"]:
            if p["id"] == payment_request.portion_id:
                portion = p
                break
        
        if not portion:
            raise ValueError("Portion not found")
        
        if portion["payment_status"] == "paid":
            raise ValueError("Portion already paid")
        
        # Validate payment amount
        expected_amount = portion["total_amount"] + portion.get("tip_amount", 0)
        if abs(payment_request.amount - expected_amount) > 0.01:
            raise ValueError(f"Payment amount mismatch. Expected: £{expected_amount:.2f}")
        
        # Generate payment ID and receipt number
        payment_id = f"pay_{uuid.uuid4().hex[:8]}"
        receipt_number = f"R{datetime.now().strftime('%Y%m%d')}{uuid.uuid4().hex[:6].upper()}"
        
        # Update portion payment status
        portion.update({
            "payment_status": "paid",
            "payment_method": payment_request.payment_method,
            "payment_reference": payment_request.payment_reference or payment_id,
            "paid_at": datetime.now().isoformat(),
            "tip_amount": payment_request.tip_amount
        })
        
        # Update split bill totals
        new_total_paid = split_bill["total_paid"] + payment_request.amount
        new_remaining_balance = split_bill["original_total"] - new_total_paid
        
        # Check if all portions are paid
        all_paid = all(p["payment_status"] == "paid" for p in split_bill["portions"])
        new_status = SplitBillStatus.COMPLETED if all_paid else SplitBillStatus.PARTIALLY_PAID
        
        # Update split bill
        update("split_bills", payment_request.split_bill_id, {
            "portions": split_bill["portions"],
            "total_paid": new_total_paid,
            "remaining_balance": new_remaining_balance,
            "status": new_status
        })
        
        # If all portions are paid, update original order
        if all_paid:
            update("orders", split_bill["original_order_id"], {
                "payment_status": "paid",
                "status": "completed",
                "completed_at": datetime.now().isoformat()
            })
        
        return PaymentResponse(
            success=True,
            payment_id=payment_id,
            receipt_number=receipt_number,
            amount_paid=payment_request.amount,
            remaining_balance=new_remaining_balance,
            payment_method=payment_request.payment_method
        )
    
    @staticmethod
    def generate_receipt(split_bill_id: str, portion_id: str) -> ReceiptData:
        """Generate receipt data for a paid portion"""
        
        split_bill = get_by_id("split_bills", split_bill_id)
        if not split_bill:
            raise ValueError("Split bill not found")
        
        portion = None
        for p in split_bill["portions"]:
            if p["id"] == portion_id:
                portion = p
                break
        
        if not portion:
            raise ValueError("Portion not found")
        
        if portion["payment_status"] != "paid":
            raise ValueError("Portion not yet paid")
        
        receipt_number = f"R{datetime.now().strftime('%Y%m%d')}{uuid.uuid4().hex[:6].upper()}"
        
        return ReceiptData(
            receipt_number=receipt_number,
            split_bill_id=split_bill_id,
            portion_id=portion_id,
            customer_name=portion.get("customer_name"),
            items=portion.get("items", []),
            subtotal=portion["amount"],
            tax_amount=portion.get("tax_amount", 0),
            tip_amount=portion.get("tip_amount", 0),
            discount_amount=portion.get("discount_amount", 0),
            total_amount=portion["total_amount"],
            payment_method=portion["payment_method"],
            payment_reference=portion.get("payment_reference")
        )
    
    @staticmethod
    def get_split_bill_summary(split_bill_id: str) -> Dict[str, Any]:
        """Get summary of split bill status"""
        
        split_bill = get_by_id("split_bills", split_bill_id)
        if not split_bill:
            raise ValueError("Split bill not found")
        
        paid_portions = sum(1 for p in split_bill["portions"] if p["payment_status"] == "paid")
        
        return {
            "split_bill_id": split_bill_id,
            "original_order_id": split_bill["original_order_id"],
            "total_amount": split_bill["original_total"],
            "total_paid": split_bill["total_paid"],
            "remaining_balance": split_bill["remaining_balance"],
            "number_of_portions": len(split_bill["portions"]),
            "paid_portions": paid_portions,
            "status": split_bill["status"],
            "portions": split_bill["portions"]
        }
