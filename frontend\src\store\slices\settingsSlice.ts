/**
 * Settings state slice for RestroManage
 * Manages restaurant configuration, user preferences, customizations, and system settings
 */

import { StateCreator } from 'zustand';
import { 
  SettingsState,
  RestaurantSettings,
  UserPreferences,
  SystemSettings,
  BackupSettings,
  SecuritySettings,
  ValidationError,
  OperatingHours,
  TableConfig,
  PaymentMethod,
  Integration
} from '@/types/store';
import { restaurantApi } from '@/services/apiService';
import logger from '@/utils/logger';

export interface SettingsSlice extends SettingsState {
  // State
  restaurantSettings: RestaurantSettings;
  userPreferences: UserPreferences;
  systemSettings: SystemSettings;
  backupSettings: BackupSettings;
  securitySettings: SecuritySettings;
  isSettingsLoading: boolean;
  settingsError: string | null;
  lastSettingsUpdate: Date | null;
  isSettingsDirty: boolean;
  validationErrors: ValidationError[];

  // Actions
  initializeSettings: (restaurantId: string) => Promise<void>;
  updateRestaurantSettings: (updates: Partial<RestaurantSettings>) => void;
  updateUserPreferences: (updates: Partial<UserPreferences>) => void;
  updateSystemSettings: (updates: Partial<SystemSettings>) => void;
  updateBackupSettings: (updates: Partial<BackupSettings>) => void;
  updateSecuritySettings: (updates: Partial<SecuritySettings>) => void;
  addTableConfig: (table: Omit<TableConfig, 'id'>) => void;
  updateTableConfig: (tableId: string, updates: Partial<TableConfig>) => void;
  removeTableConfig: (tableId: string) => void;
  addPaymentMethod: (method: Omit<PaymentMethod, 'id'>) => void;
  updatePaymentMethod: (methodId: string, updates: Partial<PaymentMethod>) => void;
  removePaymentMethod: (methodId: string) => void;
  addIntegration: (integration: Omit<Integration, 'id'>) => void;
  updateIntegration: (integrationId: string, updates: Partial<Integration>) => void;
  removeIntegration: (integrationId: string) => void;
  validateSettings: () => ValidationError[];
  saveSettings: () => Promise<void>;
  resetSettingsToDefault: () => void;
  syncSettings: () => Promise<void>;
  resetSettings: () => void;
  clearSettingsData: (restaurantId: string) => void;
}

const defaultOperatingHours: OperatingHours = {
  monday: { open: '09:00', close: '22:00', isOpen: true },
  tuesday: { open: '09:00', close: '22:00', isOpen: true },
  wednesday: { open: '09:00', close: '22:00', isOpen: true },
  thursday: { open: '09:00', close: '22:00', isOpen: true },
  friday: { open: '09:00', close: '23:00', isOpen: true },
  saturday: { open: '10:00', close: '23:00', isOpen: true },
  sunday: { open: '10:00', close: '21:00', isOpen: true },
};

const defaultTableConfigs: TableConfig[] = [
  { id: '1', number: 1, seats: 2, section: 'Main', status: 'available' },
  { id: '2', number: 2, seats: 4, section: 'Main', status: 'available' },
  { id: '3', number: 3, seats: 6, section: 'Main', status: 'available' },
  { id: '4', number: 4, seats: 2, section: 'Patio', status: 'available' },
  { id: '5', number: 5, seats: 4, section: 'Patio', status: 'available' },
];

const defaultPaymentMethods: PaymentMethod[] = [
  { id: '1', name: 'Cash', type: 'cash', isEnabled: true, processingFee: 0 },
  { id: '2', name: 'Credit Card', type: 'card', isEnabled: true, processingFee: 2.9 },
  { id: '3', name: 'Debit Card', type: 'card', isEnabled: true, processingFee: 1.5 },
  { id: '4', name: 'Apple Pay', type: 'digital', isEnabled: false, processingFee: 2.9 },
  { id: '5', name: 'Google Pay', type: 'digital', isEnabled: false, processingFee: 2.9 },
];

const initialRestaurantSettings: RestaurantSettings = {
  id: '',
  name: '',
  logo: '',
  address: '',
  phone: '',
  email: '',
  vatRate: 20,
  currency: 'GBP',
  timezone: 'Europe/London',
  operatingHours: defaultOperatingHours,
  tableConfiguration: defaultTableConfigs,
  paymentMethods: defaultPaymentMethods,
  integrations: [],
};

const initialUserPreferences: UserPreferences = {
  theme: 'system',
  language: 'en',
  notifications: {
    email: true,
    push: true,
    sms: false,
    inApp: true,
    types: ['orders', 'inventory', 'staff', 'alerts'],
  },
  dashboard: {
    layout: 'default',
    widgets: ['metrics', 'recent-orders', 'alerts', 'quick-stats'],
    refreshInterval: 30000,
    defaultView: 'overview',
  },
  accessibility: {
    fontSize: 'medium',
    highContrast: false,
    reducedMotion: false,
    screenReader: false,
  },
};

const initialSystemSettings: SystemSettings = {
  backupFrequency: 'daily',
  dataRetention: 365,
  maintenanceMode: false,
  debugMode: false,
};

const initialBackupSettings: BackupSettings = {
  autoBackup: true,
  frequency: 'daily',
  retention: 30,
  location: 'cloud',
};

const initialSecuritySettings: SecuritySettings = {
  sessionTimeout: 3600,
  passwordPolicy: {
    minLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSymbols: false,
    expiryDays: 90,
  },
  twoFactorAuth: false,
  ipWhitelist: [],
};

export const createSettingsSlice: StateCreator<
  SettingsSlice,
  [],
  [],
  SettingsSlice
> = (set, get) => ({
  // Initial state
  restaurantSettings: initialRestaurantSettings,
  userPreferences: initialUserPreferences,
  systemSettings: initialSystemSettings,
  backupSettings: initialBackupSettings,
  securitySettings: initialSecuritySettings,
  isSettingsLoading: false,
  settingsError: null,
  lastSettingsUpdate: null,
  isSettingsDirty: false,
  validationErrors: [],

  // Actions
  initializeSettings: async (restaurantId: string) => {
    try {
      set((state) => {
        state.isSettingsLoading = true;
        state.settingsError = null;
      });

      logger.info('Initializing settings', 'SettingsSlice', { restaurantId });

      // Fetch restaurant settings from API
      const restaurantData = await restaurantApi.getRestaurant(restaurantId);

      set((state) => {
        state.restaurantSettings = {
          id: restaurantData.id,
          name: restaurantData.name,
          logo: restaurantData.logo,
          address: restaurantData.address,
          phone: restaurantData.phone,
          email: restaurantData.email,
          vatRate: restaurantData.vatRate || 20,
          currency: restaurantData.currency || 'GBP',
          timezone: restaurantData.timezone || 'Europe/London',
          operatingHours: restaurantData.operatingHours || defaultOperatingHours,
          tableConfiguration: restaurantData.tableConfiguration || defaultTableConfigs,
          paymentMethods: restaurantData.paymentMethods || defaultPaymentMethods,
          integrations: restaurantData.integrations || [],
        };

        // Load user preferences from localStorage
        const savedPreferences = localStorage.getItem(`userPreferences_${restaurantId}`);
        if (savedPreferences) {
          try {
            state.userPreferences = { ...initialUserPreferences, ...JSON.parse(savedPreferences) };
          } catch (error) {
            logger.warn('Failed to parse saved user preferences', 'SettingsSlice', { error });
          }
        }

        state.isSettingsLoading = false;
        state.lastSettingsUpdate = new Date();
        state.isSettingsDirty = false;
      });

      logger.info('Settings initialized successfully', 'SettingsSlice', { restaurantId });
    } catch (error) {
      set((state) => {
        state.isSettingsLoading = false;
        state.settingsError = error instanceof Error ? error.message : 'Failed to initialize settings';
      });

      logger.error('Failed to initialize settings', 'SettingsSlice', { error, restaurantId });
      throw error;
    }
  },

  updateRestaurantSettings: (updates: Partial<RestaurantSettings>) => {
    set((state) => {
      state.restaurantSettings = { ...state.restaurantSettings, ...updates };
      state.isSettingsDirty = true;
      state.validationErrors = get().validateSettings();
    });
  },

  updateUserPreferences: (updates: Partial<UserPreferences>) => {
    set((state) => {
      state.userPreferences = { ...state.userPreferences, ...updates };
      state.isSettingsDirty = true;
    });

    // Save to localStorage immediately
    const restaurantId = get().currentRestaurantId;
    if (restaurantId) {
      localStorage.setItem(`userPreferences_${restaurantId}`, JSON.stringify(get().userPreferences));
    }
  },

  updateSystemSettings: (updates: Partial<SystemSettings>) => {
    set((state) => {
      state.systemSettings = { ...state.systemSettings, ...updates };
      state.isSettingsDirty = true;
    });
  },

  updateBackupSettings: (updates: Partial<BackupSettings>) => {
    set((state) => {
      state.backupSettings = { ...state.backupSettings, ...updates };
      state.isSettingsDirty = true;
    });
  },

  updateSecuritySettings: (updates: Partial<SecuritySettings>) => {
    set((state) => {
      state.securitySettings = { ...state.securitySettings, ...updates };
      state.isSettingsDirty = true;
    });
  },

  addTableConfig: (table: Omit<TableConfig, 'id'>) => {
    const newTable: TableConfig = {
      ...table,
      id: `table_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    };

    set((state) => {
      state.restaurantSettings.tableConfiguration.push(newTable);
      state.isSettingsDirty = true;
    });
  },

  updateTableConfig: (tableId: string, updates: Partial<TableConfig>) => {
    set((state) => {
      const index = state.restaurantSettings.tableConfiguration.findIndex(table => table.id === tableId);
      if (index !== -1) {
        state.restaurantSettings.tableConfiguration[index] = {
          ...state.restaurantSettings.tableConfiguration[index],
          ...updates,
        };
        state.isSettingsDirty = true;
      }
    });
  },

  removeTableConfig: (tableId: string) => {
    set((state) => {
      state.restaurantSettings.tableConfiguration = state.restaurantSettings.tableConfiguration.filter(
        table => table.id !== tableId
      );
      state.isSettingsDirty = true;
    });
  },

  addPaymentMethod: (method: Omit<PaymentMethod, 'id'>) => {
    const newMethod: PaymentMethod = {
      ...method,
      id: `payment_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    };

    set((state) => {
      state.restaurantSettings.paymentMethods.push(newMethod);
      state.isSettingsDirty = true;
    });
  },

  updatePaymentMethod: (methodId: string, updates: Partial<PaymentMethod>) => {
    set((state) => {
      const index = state.restaurantSettings.paymentMethods.findIndex(method => method.id === methodId);
      if (index !== -1) {
        state.restaurantSettings.paymentMethods[index] = {
          ...state.restaurantSettings.paymentMethods[index],
          ...updates,
        };
        state.isSettingsDirty = true;
      }
    });
  },

  removePaymentMethod: (methodId: string) => {
    set((state) => {
      state.restaurantSettings.paymentMethods = state.restaurantSettings.paymentMethods.filter(
        method => method.id !== methodId
      );
      state.isSettingsDirty = true;
    });
  },

  addIntegration: (integration: Omit<Integration, 'id'>) => {
    const newIntegration: Integration = {
      ...integration,
      id: `integration_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    };

    set((state) => {
      state.restaurantSettings.integrations.push(newIntegration);
      state.isSettingsDirty = true;
    });
  },

  updateIntegration: (integrationId: string, updates: Partial<Integration>) => {
    set((state) => {
      const index = state.restaurantSettings.integrations.findIndex(integration => integration.id === integrationId);
      if (index !== -1) {
        state.restaurantSettings.integrations[index] = {
          ...state.restaurantSettings.integrations[index],
          ...updates,
        };
        state.isSettingsDirty = true;
      }
    });
  },

  removeIntegration: (integrationId: string) => {
    set((state) => {
      state.restaurantSettings.integrations = state.restaurantSettings.integrations.filter(
        integration => integration.id !== integrationId
      );
      state.isSettingsDirty = true;
    });
  },

  validateSettings: (): ValidationError[] => {
    const errors: ValidationError[] = [];
    const settings = get().restaurantSettings;

    // Validate required fields
    if (!settings.name.trim()) {
      errors.push({ field: 'name', message: 'Restaurant name is required', code: 'REQUIRED' });
    }

    if (!settings.email.trim()) {
      errors.push({ field: 'email', message: 'Email is required', code: 'REQUIRED' });
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(settings.email)) {
      errors.push({ field: 'email', message: 'Invalid email format', code: 'INVALID_FORMAT' });
    }

    if (!settings.phone.trim()) {
      errors.push({ field: 'phone', message: 'Phone number is required', code: 'REQUIRED' });
    }

    if (!settings.address.trim()) {
      errors.push({ field: 'address', message: 'Address is required', code: 'REQUIRED' });
    }

    // Validate VAT rate
    if (settings.vatRate < 0 || settings.vatRate > 100) {
      errors.push({ field: 'vatRate', message: 'VAT rate must be between 0 and 100', code: 'INVALID_RANGE' });
    }

    // Validate table configuration
    if (settings.tableConfiguration.length === 0) {
      errors.push({ field: 'tableConfiguration', message: 'At least one table must be configured', code: 'REQUIRED' });
    }

    // Validate payment methods
    const enabledPaymentMethods = settings.paymentMethods.filter(method => method.isEnabled);
    if (enabledPaymentMethods.length === 0) {
      errors.push({ field: 'paymentMethods', message: 'At least one payment method must be enabled', code: 'REQUIRED' });
    }

    return errors;
  },

  saveSettings: async () => {
    const restaurantId = get().currentRestaurantId;
    if (!restaurantId) {
      logger.warn('Cannot save settings without restaurant ID', 'SettingsSlice');
      return;
    }

    try {
      logger.info('Saving settings', 'SettingsSlice', { restaurantId });

      // Validate settings before saving
      const errors = get().validateSettings();
      if (errors.length > 0) {
        set((state) => {
          state.validationErrors = errors;
        });
        throw new Error(`Validation failed: ${errors.map(e => e.message).join(', ')}`);
      }

      // Save restaurant settings via API
      await restaurantApi.updateRestaurant(restaurantId, get().restaurantSettings);

      set((state) => {
        state.isSettingsDirty = false;
        state.lastSettingsUpdate = new Date();
        state.validationErrors = [];
      });

      logger.info('Settings saved successfully', 'SettingsSlice', { restaurantId });
    } catch (error) {
      logger.error('Failed to save settings', 'SettingsSlice', { error, restaurantId });
      throw error;
    }
  },

  resetSettingsToDefault: () => {
    set((state) => {
      state.restaurantSettings = { ...initialRestaurantSettings, id: state.restaurantSettings.id };
      state.userPreferences = initialUserPreferences;
      state.systemSettings = initialSystemSettings;
      state.backupSettings = initialBackupSettings;
      state.securitySettings = initialSecuritySettings;
      state.isSettingsDirty = true;
      state.validationErrors = [];
    });
  },

  syncSettings: async () => {
    const restaurantId = get().currentRestaurantId;
    if (!restaurantId) return;

    await get().initializeSettings(restaurantId);
  },

  resetSettings: () => {
    set((state) => {
      state.restaurantSettings = initialRestaurantSettings;
      state.userPreferences = initialUserPreferences;
      state.systemSettings = initialSystemSettings;
      state.backupSettings = initialBackupSettings;
      state.securitySettings = initialSecuritySettings;
      state.isSettingsLoading = false;
      state.settingsError = null;
      state.lastSettingsUpdate = null;
      state.isSettingsDirty = false;
      state.validationErrors = [];
    });
  },

  clearSettingsData: (restaurantId: string) => {
    set((state) => {
      if (state.currentRestaurantId === restaurantId) {
        state.restaurantSettings = initialRestaurantSettings;
        state.userPreferences = initialUserPreferences;
        state.isSettingsDirty = false;
        state.validationErrors = [];
      }
    });

    // Clear localStorage data for this restaurant
    localStorage.removeItem(`userPreferences_${restaurantId}`);
  },
});
