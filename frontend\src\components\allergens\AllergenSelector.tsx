import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { ChevronDown, X, AlertCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import { allergenService } from "@/services/allergenService";

interface AllergenSelectorProps {
  selectedAllergens: string[];
  onChange: (allergens: string[]) => void;
  className?: string;
  placeholder?: string;
  disabled?: boolean;
}

interface AllergenData {
  allergens: string[];
  descriptions: Record<string, string>;
}

export const AllergenSelector = ({
  selectedAllergens,
  onChange,
  className,
  placeholder = "Select allergens",
  disabled = false
}: AllergenSelectorProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [allergenData, setAllergenData] = useState<AllergenData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch valid allergens from FastAPI backend
  useEffect(() => {
    const fetchAllergens = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await allergenService.getValidAllergens();
        setAllergenData(data);
      } catch (err) {
        console.error("Failed to fetch allergens:", err);
        setError("Failed to load allergens");
        // Fallback to hardcoded allergens if API fails
        setAllergenData({
          allergens: ["nuts", "dairy", "gluten", "shellfish", "eggs", "soy", "fish", "sesame"],
          descriptions: {
            nuts: "Tree nuts (almonds, walnuts, pecans, etc.)",
            dairy: "Milk and dairy products",
            gluten: "Wheat, barley, rye, and other gluten-containing grains",
            shellfish: "Crustaceans and mollusks",
            eggs: "Chicken eggs and egg products",
            soy: "Soybeans and soy products",
            fish: "Fish and fish products",
            sesame: "Sesame seeds and sesame products"
          }
        });
      } finally {
        setLoading(false);
      }
    };

    fetchAllergens();
  }, []);

  const handleAllergenToggle = (allergen: string) => {
    if (disabled) return;
    
    const newAllergens = selectedAllergens.includes(allergen)
      ? selectedAllergens.filter(a => a !== allergen)
      : [...selectedAllergens, allergen];
    
    onChange(newAllergens);
  };

  const handleRemoveAllergen = (allergen: string) => {
    if (disabled) return;
    onChange(selectedAllergens.filter(a => a !== allergen));
  };

  const clearAll = () => {
    if (disabled) return;
    onChange([]);
  };

  if (loading) {
    return (
      <div className={cn("flex items-center space-x-2", className)}>
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900"></div>
        <span className="text-sm text-muted-foreground">Loading allergens...</span>
      </div>
    );
  }

  if (error || !allergenData) {
    return (
      <div className={cn("flex items-center space-x-2 text-red-500", className)}>
        <AlertCircle className="h-4 w-4" />
        <span className="text-sm">{error || "Failed to load allergens"}</span>
      </div>
    );
  }

  return (
    <div className={cn("space-y-2", className)}>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={isOpen}
            className="w-full justify-between"
            disabled={disabled}
          >
            <span className="truncate">
              {selectedAllergens.length === 0
                ? placeholder
                : `${selectedAllergens.length} allergen${selectedAllergens.length === 1 ? '' : 's'} selected`
              }
            </span>
            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <div className="p-4 space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium">Select Allergens</Label>
              {selectedAllergens.length > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearAll}
                  className="h-auto p-0 text-xs text-muted-foreground hover:text-foreground"
                >
                  Clear all
                </Button>
              )}
            </div>
            <div className="space-y-2">
              {allergenData.allergens.map((allergen) => (
                <div key={allergen} className="flex items-start space-x-2">
                  <Checkbox
                    id={allergen}
                    checked={selectedAllergens.includes(allergen)}
                    onCheckedChange={() => handleAllergenToggle(allergen)}
                  />
                  <div className="grid gap-1.5 leading-none">
                    <Label
                      htmlFor={allergen}
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 capitalize cursor-pointer"
                    >
                      {allergen}
                    </Label>
                    {allergenData.descriptions[allergen] && (
                      <p className="text-xs text-muted-foreground">
                        {allergenData.descriptions[allergen]}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </PopoverContent>
      </Popover>

      {/* Selected allergens display */}
      {selectedAllergens.length > 0 && (
        <div className="flex flex-wrap gap-1">
          {selectedAllergens.map((allergen) => (
            <Badge
              key={allergen}
              variant="secondary"
              className="text-xs capitalize"
            >
              {allergen}
              {!disabled && (
                <button
                  onClick={() => handleRemoveAllergen(allergen)}
                  className="ml-1 hover:bg-secondary-foreground/20 rounded-full p-0.5"
                >
                  <X className="h-3 w-3" />
                </button>
              )}
            </Badge>
          ))}
        </div>
      )}
    </div>
  );
};
