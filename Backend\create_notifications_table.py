#!/usr/bin/env python3
"""
Create notifications table migration for RestroManage.
This script adds the notifications table to the existing database.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
import os
from app.utils.logging_config import logger

def create_notifications_table():
    """Create the notifications table with proper indexes"""

    # Get database URL (sync version for migration)
    database_url = os.getenv("DATABASE_URL", "sqlite:///./restro_manage.db")
    if database_url.startswith("sqlite+aiosqlite"):
        database_url = database_url.replace("sqlite+aiosqlite", "sqlite")

    engine = create_engine(database_url)
    
    # SQL to create notifications table
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS notifications (
        id VARCHAR PRIMARY KEY,
        restaurant_id VARCHAR NOT NULL,
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        type VARCHAR(20) NOT NULL DEFAULT 'info',
        priority VARCHAR(10) NOT NULL DEFAULT 'medium',
        is_read BOOLEAN NOT NULL DEFAULT FALSE,
        link VARCHAR(500),
        related_id VARCHAR(255),
        created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (restaurant_id) REFERENCES restaurants(id) ON DELETE CASCADE
    );
    """
    
    # SQL to create indexes
    create_indexes_sql = [
        "CREATE INDEX IF NOT EXISTS idx_notification_restaurant ON notifications(restaurant_id);",
        "CREATE INDEX IF NOT EXISTS idx_notification_type ON notifications(type);",
        "CREATE INDEX IF NOT EXISTS idx_notification_priority ON notifications(priority);",
        "CREATE INDEX IF NOT EXISTS idx_notification_read ON notifications(is_read);",
        "CREATE INDEX IF NOT EXISTS idx_notification_created ON notifications(created_at);",
        "CREATE INDEX IF NOT EXISTS idx_notification_restaurant_read ON notifications(restaurant_id, is_read);"
    ]
    
    try:
        with engine.connect() as connection:
            # Create table
            logger.info("Creating notifications table...", "Migration")
            connection.execute(text(create_table_sql))
            connection.commit()
            logger.info("✅ Notifications table created successfully", "Migration")
            
            # Create indexes
            logger.info("Creating indexes for notifications table...", "Migration")
            for index_sql in create_indexes_sql:
                connection.execute(text(index_sql))
                connection.commit()
            logger.info("✅ All indexes created successfully", "Migration")
            
            # Verify table creation (SQLite compatible)
            result = connection.execute(text("""
                SELECT COUNT(*) as count
                FROM sqlite_master
                WHERE type='table' AND name='notifications'
            """))
            count = result.fetchone()[0]
            
            if count > 0:
                logger.info("✅ Notifications table verified successfully", "Migration")
                
                # Check if we can insert sample data
                sample_notification_sql = """
                INSERT INTO notifications (
                    id, restaurant_id, title, message, type, priority, is_read
                ) VALUES (
                    'sample-notification-' || EXTRACT(EPOCH FROM NOW())::TEXT,
                    (SELECT id FROM restaurants LIMIT 1),
                    'System Notification',
                    'Notification system has been successfully installed and configured.',
                    'info',
                    'low',
                    FALSE
                ) ON CONFLICT (id) DO NOTHING;
                """
                
                try:
                    connection.execute(text(sample_notification_sql))
                    connection.commit()
                    logger.info("✅ Sample notification created successfully", "Migration")
                except Exception as e:
                    logger.warning(f"Could not create sample notification: {str(e)}", "Migration")
                
            else:
                logger.error("❌ Notifications table verification failed", "Migration")
                return False
                
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to create notifications table: {str(e)}", "Migration")
        return False
    finally:
        engine.dispose()

def main():
    """Main migration function"""
    logger.info("Starting notifications table migration...", "Migration")
    
    success = create_notifications_table()
    
    if success:
        logger.info("🎉 Notifications table migration completed successfully!", "Migration")
        print("\n✅ Migration completed successfully!")
        print("📋 Notifications table has been created with proper indexes")
        print("🔗 Foreign key relationship to restaurants table established")
        print("📊 Sample notification created for testing")
        return 0
    else:
        logger.error("❌ Notifications table migration failed!", "Migration")
        print("\n❌ Migration failed!")
        print("Please check the logs for more details")
        return 1

if __name__ == "__main__":
    exit(main())
