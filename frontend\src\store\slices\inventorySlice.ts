/**
 * Inventory state slice for RestroManage
 * Manages current inventory levels, low stock alerts, transaction history, and supplier/product catalog
 */

import { StateCreator } from 'zustand';
import { 
  InventoryState,
  InventoryItem,
  InventoryTransaction,
  InventoryAlert,
  InventoryForecast,
  InventoryFilters,
  Supplier,
  WasteEntry
} from '@/types/store';
import logger from '@/utils/logger';

export interface InventorySlice extends InventoryState {
  // State
  inventoryItems: InventoryItem[];
  inventoryTransactions: InventoryTransaction[];
  suppliers: Supplier[];
  categories: string[];
  inventoryFilters: InventoryFilters;
  inventoryAlerts: InventoryAlert[];
  inventoryForecasts: InventoryForecast[];
  wasteLog: WasteEntry[];
  isInventoryLoading: boolean;
  inventoryError: string | null;
  lastInventoryUpdate: Date | null;

  // Actions
  initializeInventory: (restaurantId: string) => Promise<void>;
  addInventoryItem: (item: Omit<InventoryItem, 'id' | 'lastUpdated'>) => Promise<void>;
  updateInventoryItem: (itemId: string, updates: Partial<InventoryItem>) => Promise<void>;
  removeInventoryItem: (itemId: string) => Promise<void>;
  addInventoryTransaction: (transaction: Omit<InventoryTransaction, 'id' | 'timestamp'>) => Promise<void>;
  updateStock: (itemId: string, quantity: number, type: 'in' | 'out' | 'adjustment', reason: string, staffId: string) => Promise<void>;
  addSupplier: (supplier: Omit<Supplier, 'id'>) => Promise<void>;
  updateSupplier: (supplierId: string, updates: Partial<Supplier>) => Promise<void>;
  removeSupplier: (supplierId: string) => Promise<void>;
  addWasteEntry: (entry: Omit<WasteEntry, 'id' | 'timestamp'>) => Promise<void>;
  updateInventoryFilters: (filters: Partial<InventoryFilters>) => void;
  checkLowStock: () => void;
  checkExpiringItems: () => void;
  generateForecast: (itemId: string, days: number) => Promise<void>;
  syncInventory: () => Promise<void>;
  resetInventory: () => void;
  clearInventoryData: (restaurantId: string) => void;
}

const initialInventoryFilters: InventoryFilters = {
  searchQuery: '',
  dateRange: {
    from: new Date(new Date().setDate(new Date().getDate() - 30)),
    to: new Date(),
  },
  categories: [],
  status: [],
  stockStatus: [],
  suppliers: [],
  expiryStatus: [],
  minStock: 0,
  maxStock: 1000,
};

const defaultCategories = [
  'Meat & Poultry',
  'Seafood',
  'Vegetables',
  'Fruits',
  'Dairy',
  'Grains & Cereals',
  'Spices & Seasonings',
  'Beverages',
  'Frozen Foods',
  'Canned Goods',
  'Cleaning Supplies',
  'Paper Products',
];

export const createInventorySlice: StateCreator<
  InventorySlice,
  [],
  [],
  InventorySlice
> = (set, get) => ({
  // Initial state
  inventoryItems: [],
  inventoryTransactions: [],
  suppliers: [],
  categories: defaultCategories,
  inventoryFilters: initialInventoryFilters,
  inventoryAlerts: [],
  inventoryForecasts: [],
  wasteLog: [],
  isInventoryLoading: false,
  inventoryError: null,
  lastInventoryUpdate: null,

  // Actions
  initializeInventory: async (restaurantId: string) => {
    try {
      set((state) => {
        state.isInventoryLoading = true;
        state.inventoryError = null;
      });

      logger.info('Initializing inventory', 'InventorySlice', { restaurantId });

      // In a real implementation, this would fetch from the API
      // For now, we'll use mock data
      const mockInventoryItems: InventoryItem[] = [
        {
          id: '1',
          name: 'Chicken Breast',
          category: 'Meat & Poultry',
          stock: 25,
          unit: 'kg',
          reorderLevel: 10,
          cost: 8.50,
          supplier: 'Fresh Foods Ltd',
          expiryDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 5 days from now
          allergens: [],
          isLow: false,
          isExpiring: true,
          lastUpdated: new Date(),
        },
        {
          id: '2',
          name: 'Tomatoes',
          category: 'Vegetables',
          stock: 5,
          unit: 'kg',
          reorderLevel: 15,
          cost: 3.20,
          supplier: 'Garden Fresh',
          allergens: [],
          isLow: true,
          isExpiring: false,
          lastUpdated: new Date(),
        },
        {
          id: '3',
          name: 'Mozzarella Cheese',
          category: 'Dairy',
          stock: 12,
          unit: 'kg',
          reorderLevel: 8,
          cost: 12.00,
          supplier: 'Dairy Delights',
          expiryDate: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 10 days from now
          allergens: ['dairy'],
          isLow: false,
          isExpiring: false,
          lastUpdated: new Date(),
        },
      ];

      const mockSuppliers: Supplier[] = [
        {
          id: '1',
          name: 'Fresh Foods Ltd',
          contact: 'John Smith',
          email: '<EMAIL>',
          phone: '+44 20 1234 5678',
          address: '123 Market Street, London, UK',
          rating: 4.5,
          paymentTerms: 'Net 30',
        },
        {
          id: '2',
          name: 'Garden Fresh',
          contact: 'Sarah Johnson',
          email: '<EMAIL>',
          phone: '+44 20 8765 4321',
          address: '456 Farm Road, Surrey, UK',
          rating: 4.2,
          paymentTerms: 'Net 15',
        },
        {
          id: '3',
          name: 'Dairy Delights',
          contact: 'Mike Wilson',
          email: '<EMAIL>',
          phone: '+44 20 5555 1234',
          address: '789 Dairy Lane, Kent, UK',
          rating: 4.8,
          paymentTerms: 'Net 30',
        },
      ];

      set((state) => {
        state.inventoryItems = mockInventoryItems;
        state.suppliers = mockSuppliers;
        state.isInventoryLoading = false;
        state.lastInventoryUpdate = new Date();
      });

      // Check for alerts after initialization
      get().checkLowStock();
      get().checkExpiringItems();

      logger.info('Inventory initialized successfully', 'InventorySlice', { 
        restaurantId, 
        itemCount: mockInventoryItems.length,
        supplierCount: mockSuppliers.length
      });
    } catch (error) {
      set((state) => {
        state.isInventoryLoading = false;
        state.inventoryError = error instanceof Error ? error.message : 'Failed to initialize inventory';
      });

      logger.error('Failed to initialize inventory', 'InventorySlice', { error, restaurantId });
      throw error;
    }
  },

  addInventoryItem: async (item: Omit<InventoryItem, 'id' | 'lastUpdated'>) => {
    try {
      logger.info('Adding inventory item', 'InventorySlice', { name: item.name, category: item.category });

      const newItem: InventoryItem = {
        ...item,
        id: `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        lastUpdated: new Date(),
        isLow: item.stock <= item.reorderLevel,
        isExpiring: item.expiryDate ? isExpiringWithin(item.expiryDate, 7) : false,
      };

      set((state) => {
        state.inventoryItems.push(newItem);
        state.lastInventoryUpdate = new Date();
      });

      // Add transaction for initial stock
      await get().addInventoryTransaction({
        itemId: newItem.id,
        type: 'in',
        quantity: item.stock,
        reason: 'Initial stock',
        staffId: 'system',
        cost: item.cost * item.stock,
      });

      logger.info('Inventory item added successfully', 'InventorySlice', { id: newItem.id, name: item.name });
    } catch (error) {
      logger.error('Failed to add inventory item', 'InventorySlice', { error, item: item.name });
      throw error;
    }
  },

  updateInventoryItem: async (itemId: string, updates: Partial<InventoryItem>) => {
    try {
      logger.info('Updating inventory item', 'InventorySlice', { itemId, updates });

      set((state) => {
        const index = state.inventoryItems.findIndex(item => item.id === itemId);
        if (index !== -1) {
          const updatedItem = { ...state.inventoryItems[index], ...updates, lastUpdated: new Date() };
          
          // Recalculate flags
          updatedItem.isLow = updatedItem.stock <= updatedItem.reorderLevel;
          updatedItem.isExpiring = updatedItem.expiryDate ? isExpiringWithin(updatedItem.expiryDate, 7) : false;
          
          state.inventoryItems[index] = updatedItem;
          state.lastInventoryUpdate = new Date();
        }
      });

      // Check for new alerts
      get().checkLowStock();
      get().checkExpiringItems();

      logger.info('Inventory item updated successfully', 'InventorySlice', { itemId });
    } catch (error) {
      logger.error('Failed to update inventory item', 'InventorySlice', { error, itemId });
      throw error;
    }
  },

  removeInventoryItem: async (itemId: string) => {
    try {
      logger.info('Removing inventory item', 'InventorySlice', { itemId });

      set((state) => {
        state.inventoryItems = state.inventoryItems.filter(item => item.id !== itemId);
        state.inventoryTransactions = state.inventoryTransactions.filter(transaction => transaction.itemId !== itemId);
        state.inventoryAlerts = state.inventoryAlerts.filter(alert => alert.itemId !== itemId);
        state.inventoryForecasts = state.inventoryForecasts.filter(forecast => forecast.itemId !== itemId);
        state.wasteLog = state.wasteLog.filter(entry => entry.itemId !== itemId);
        state.lastInventoryUpdate = new Date();
      });

      logger.info('Inventory item removed successfully', 'InventorySlice', { itemId });
    } catch (error) {
      logger.error('Failed to remove inventory item', 'InventorySlice', { error, itemId });
      throw error;
    }
  },

  addInventoryTransaction: async (transaction: Omit<InventoryTransaction, 'id' | 'timestamp'>) => {
    try {
      const newTransaction: InventoryTransaction = {
        ...transaction,
        id: `transaction_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date(),
      };

      set((state) => {
        state.inventoryTransactions.push(newTransaction);
        state.lastInventoryUpdate = new Date();
      });

      logger.info('Inventory transaction added', 'InventorySlice', { 
        transactionId: newTransaction.id, 
        type: transaction.type,
        quantity: transaction.quantity
      });
    } catch (error) {
      logger.error('Failed to add inventory transaction', 'InventorySlice', { error, transaction });
      throw error;
    }
  },

  updateStock: async (itemId: string, quantity: number, type: 'in' | 'out' | 'adjustment', reason: string, staffId: string) => {
    try {
      logger.info('Updating stock', 'InventorySlice', { itemId, quantity, type, reason });

      const item = get().inventoryItems.find(item => item.id === itemId);
      if (!item) {
        throw new Error(`Item with ID ${itemId} not found`);
      }

      let newStock = item.stock;
      switch (type) {
        case 'in':
          newStock += quantity;
          break;
        case 'out':
          newStock -= quantity;
          break;
        case 'adjustment':
          newStock = quantity; // Set to exact quantity for adjustments
          break;
      }

      // Ensure stock doesn't go negative
      newStock = Math.max(0, newStock);

      // Update the item
      await get().updateInventoryItem(itemId, { stock: newStock });

      // Add transaction record
      await get().addInventoryTransaction({
        itemId,
        type,
        quantity: type === 'adjustment' ? quantity - item.stock : quantity,
        reason,
        staffId,
        cost: type === 'in' ? item.cost * quantity : undefined,
      });

      logger.info('Stock updated successfully', 'InventorySlice', { itemId, newStock });
    } catch (error) {
      logger.error('Failed to update stock', 'InventorySlice', { error, itemId, quantity, type });
      throw error;
    }
  },

  addSupplier: async (supplier: Omit<Supplier, 'id'>) => {
    try {
      logger.info('Adding supplier', 'InventorySlice', { name: supplier.name });

      const newSupplier: Supplier = {
        ...supplier,
        id: `supplier_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      };

      set((state) => {
        state.suppliers.push(newSupplier);
        state.lastInventoryUpdate = new Date();
      });

      logger.info('Supplier added successfully', 'InventorySlice', { id: newSupplier.id, name: supplier.name });
    } catch (error) {
      logger.error('Failed to add supplier', 'InventorySlice', { error, supplier: supplier.name });
      throw error;
    }
  },

  updateSupplier: async (supplierId: string, updates: Partial<Supplier>) => {
    try {
      logger.info('Updating supplier', 'InventorySlice', { supplierId, updates });

      set((state) => {
        const index = state.suppliers.findIndex(supplier => supplier.id === supplierId);
        if (index !== -1) {
          state.suppliers[index] = { ...state.suppliers[index], ...updates };
          state.lastInventoryUpdate = new Date();
        }
      });

      logger.info('Supplier updated successfully', 'InventorySlice', { supplierId });
    } catch (error) {
      logger.error('Failed to update supplier', 'InventorySlice', { error, supplierId });
      throw error;
    }
  },

  removeSupplier: async (supplierId: string) => {
    try {
      logger.info('Removing supplier', 'InventorySlice', { supplierId });

      set((state) => {
        state.suppliers = state.suppliers.filter(supplier => supplier.id !== supplierId);
        state.lastInventoryUpdate = new Date();
      });

      logger.info('Supplier removed successfully', 'InventorySlice', { supplierId });
    } catch (error) {
      logger.error('Failed to remove supplier', 'InventorySlice', { error, supplierId });
      throw error;
    }
  },

  addWasteEntry: async (entry: Omit<WasteEntry, 'id' | 'timestamp'>) => {
    try {
      const newEntry: WasteEntry = {
        ...entry,
        id: `waste_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date(),
      };

      set((state) => {
        state.wasteLog.push(newEntry);
        state.lastInventoryUpdate = new Date();
      });

      // Update stock to reflect waste
      await get().updateStock(entry.itemId, entry.quantity, 'out', `Waste: ${entry.reason}`, entry.staffId);

      logger.info('Waste entry added', 'InventorySlice', { entryId: newEntry.id, quantity: entry.quantity });
    } catch (error) {
      logger.error('Failed to add waste entry', 'InventorySlice', { error, entry });
      throw error;
    }
  },

  updateInventoryFilters: (filters: Partial<InventoryFilters>) => {
    set((state) => {
      state.inventoryFilters = { ...state.inventoryFilters, ...filters };
    });
  },

  checkLowStock: () => {
    const items = get().inventoryItems;
    const lowStockItems = items.filter(item => item.isLow);

    set((state) => {
      // Remove existing low stock alerts
      state.inventoryAlerts = state.inventoryAlerts.filter(alert => alert.type !== 'low-stock');

      // Add new low stock alerts
      lowStockItems.forEach(item => {
        state.inventoryAlerts.push({
          id: `low-stock-${item.id}`,
          type: 'low-stock',
          itemId: item.id,
          message: `${item.name} is running low (${item.stock} ${item.unit} remaining)`,
          severity: 'medium',
          timestamp: new Date(),
        });
      });
    });

    if (lowStockItems.length > 0) {
      logger.warn('Low stock items detected', 'InventorySlice', { count: lowStockItems.length });
    }
  },

  checkExpiringItems: () => {
    const items = get().inventoryItems;
    const expiringItems = items.filter(item => item.isExpiring);

    set((state) => {
      // Remove existing expiring alerts
      state.inventoryAlerts = state.inventoryAlerts.filter(alert => alert.type !== 'expiring');

      // Add new expiring alerts
      expiringItems.forEach(item => {
        state.inventoryAlerts.push({
          id: `expiring-${item.id}`,
          type: 'expiring',
          itemId: item.id,
          message: `${item.name} expires on ${item.expiryDate}`,
          severity: 'high',
          timestamp: new Date(),
        });
      });
    });

    if (expiringItems.length > 0) {
      logger.warn('Expiring items detected', 'InventorySlice', { count: expiringItems.length });
    }
  },

  generateForecast: async (itemId: string, days: number) => {
    try {
      logger.info('Generating forecast', 'InventorySlice', { itemId, days });

      // Simple forecast calculation based on recent usage
      const transactions = get().inventoryTransactions.filter(t => 
        t.itemId === itemId && 
        t.type === 'out' && 
        t.timestamp > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
      );

      const totalUsage = transactions.reduce((sum, t) => sum + t.quantity, 0);
      const dailyAverage = totalUsage / 30;
      const predictedUsage = dailyAverage * days;

      const forecast: InventoryForecast = {
        itemId,
        predictedUsage,
        recommendedOrder: Math.max(0, predictedUsage - get().inventoryItems.find(i => i.id === itemId)?.stock || 0),
        confidence: Math.min(0.9, transactions.length / 10), // Higher confidence with more data points
        period: `${days} days`,
      };

      set((state) => {
        // Remove existing forecast for this item
        state.inventoryForecasts = state.inventoryForecasts.filter(f => f.itemId !== itemId);
        // Add new forecast
        state.inventoryForecasts.push(forecast);
      });

      logger.info('Forecast generated successfully', 'InventorySlice', { itemId, predictedUsage, confidence: forecast.confidence });
    } catch (error) {
      logger.error('Failed to generate forecast', 'InventorySlice', { error, itemId, days });
      throw error;
    }
  },

  syncInventory: async () => {
    const restaurantId = get().currentRestaurantId;
    if (!restaurantId) return;

    await get().initializeInventory(restaurantId);
  },

  resetInventory: () => {
    set((state) => {
      state.inventoryItems = [];
      state.inventoryTransactions = [];
      state.suppliers = [];
      state.inventoryFilters = initialInventoryFilters;
      state.inventoryAlerts = [];
      state.inventoryForecasts = [];
      state.wasteLog = [];
      state.isInventoryLoading = false;
      state.inventoryError = null;
      state.lastInventoryUpdate = null;
    });
  },

  clearInventoryData: (restaurantId: string) => {
    set((state) => {
      if (state.currentRestaurantId === restaurantId) {
        state.inventoryItems = [];
        state.inventoryTransactions = [];
        state.suppliers = [];
        state.inventoryAlerts = [];
        state.inventoryForecasts = [];
        state.wasteLog = [];
      }
    });
  },
});

// Helper function to check if an item is expiring within specified days
function isExpiringWithin(expiryDate: string, days: number): boolean {
  const expiry = new Date(expiryDate);
  const threshold = new Date(Date.now() + days * 24 * 60 * 60 * 1000);
  return expiry <= threshold;
}
