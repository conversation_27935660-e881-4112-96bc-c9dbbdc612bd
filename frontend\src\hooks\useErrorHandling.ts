/**
 * Comprehensive error handling and loading states system for RestroManage
 * Provides consistent error handling, loading states, and retry mechanisms across all tabs
 */

import { useState, useCallback, useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from '@/components/ui/sonner';
import logger from '@/utils/logger';

// Error types
export interface AppError {
  id: string;
  type: 'network' | 'validation' | 'authentication' | 'permission' | 'server' | 'unknown';
  message: string;
  details?: any;
  timestamp: Date;
  context?: string;
  retryable: boolean;
}

export interface LoadingState {
  isLoading: boolean;
  loadingMessage?: string;
  progress?: number;
}

export interface RetryConfig {
  maxRetries: number;
  retryDelay: number;
  backoffMultiplier: number;
}

// Default retry configuration
const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxRetries: 3,
  retryDelay: 1000,
  backoffMultiplier: 2,
};

// Error handling hook
export const useErrorHandling = () => {
  const [errors, setErrors] = useState<AppError[]>([]);
  const [globalLoading, setGlobalLoading] = useState<LoadingState>({ isLoading: false });

  const addError = useCallback((error: Omit<AppError, 'id' | 'timestamp'>) => {
    const appError: AppError = {
      ...error,
      id: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
    };

    setErrors(prev => [appError, ...prev.slice(0, 9)]); // Keep only 10 most recent errors

    // Log error
    logger.error(`${error.type} error: ${error.message}`, error.context || 'ErrorHandling', {
      error: error.details,
      type: error.type,
    });

    // Show toast for user-facing errors
    if (error.type !== 'network' || !error.retryable) {
      toast.error(error.message, {
        description: error.details?.message || 'Please try again or contact support if the problem persists.',
      });
    }

    return appError;
  }, []);

  const removeError = useCallback((errorId: string) => {
    setErrors(prev => prev.filter(error => error.id !== errorId));
  }, []);

  const clearErrors = useCallback(() => {
    setErrors([]);
  }, []);

  const setLoading = useCallback((loading: LoadingState) => {
    setGlobalLoading(loading);
  }, []);

  const handleAsyncOperation = useCallback(async <T>(
    operation: () => Promise<T>,
    context: string,
    retryConfig: Partial<RetryConfig> = {}
  ): Promise<T> => {
    const config = { ...DEFAULT_RETRY_CONFIG, ...retryConfig };
    let lastError: any;

    for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
      try {
        if (attempt > 0) {
          const delay = config.retryDelay * Math.pow(config.backoffMultiplier, attempt - 1);
          await new Promise(resolve => setTimeout(resolve, delay));
        }

        return await operation();
      } catch (error: any) {
        lastError = error;

        const appError = classifyError(error, context);
        
        if (attempt === config.maxRetries || !appError.retryable) {
          addError(appError);
          throw error;
        }

        logger.warn(`Retrying operation (attempt ${attempt + 1}/${config.maxRetries})`, context, {
          error: error.message,
          attempt: attempt + 1,
        });
      }
    }

    throw lastError;
  }, [addError]);

  return {
    errors,
    globalLoading,
    addError,
    removeError,
    clearErrors,
    setLoading,
    handleAsyncOperation,
  };
};

// Loading states hook for specific components
export const useLoadingStates = () => {
  const [loadingStates, setLoadingStates] = useState<Record<string, LoadingState>>({});

  const setComponentLoading = useCallback((component: string, loading: LoadingState) => {
    setLoadingStates(prev => ({
      ...prev,
      [component]: loading,
    }));
  }, []);

  const getComponentLoading = useCallback((component: string): LoadingState => {
    return loadingStates[component] || { isLoading: false };
  }, [loadingStates]);

  const clearComponentLoading = useCallback((component: string) => {
    setLoadingStates(prev => {
      const { [component]: _, ...rest } = prev;
      return rest;
    });
  }, []);

  return {
    loadingStates,
    setComponentLoading,
    getComponentLoading,
    clearComponentLoading,
  };
};

// Data synchronization hook
export const useDataSync = () => {
  const queryClient = useQueryClient();
  const { handleAsyncOperation } = useErrorHandling();
  const [syncStatus, setSyncStatus] = useState<{
    isSync: boolean;
    lastSync: Date | null;
    failedSyncs: string[];
  }>({
    isSync: false,
    lastSync: null,
    failedSyncs: [],
  });

  const syncData = useCallback(async (queryKeys: string[], context: string = 'DataSync') => {
    setSyncStatus(prev => ({ ...prev, isSync: true }));

    try {
      await handleAsyncOperation(async () => {
        const promises = queryKeys.map(key => 
          queryClient.invalidateQueries({ queryKey: [key] })
        );
        await Promise.all(promises);
      }, context);

      setSyncStatus(prev => ({
        ...prev,
        isSync: false,
        lastSync: new Date(),
        failedSyncs: [],
      }));

      logger.info('Data sync completed successfully', context, { queryKeys });
    } catch (error) {
      setSyncStatus(prev => ({
        ...prev,
        isSync: false,
        failedSyncs: queryKeys,
      }));

      logger.error('Data sync failed', context, { error, queryKeys });
      throw error;
    }
  }, [queryClient, handleAsyncOperation]);

  const retrySyncFailed = useCallback(async () => {
    if (syncStatus.failedSyncs.length > 0) {
      await syncData(syncStatus.failedSyncs, 'DataSync-Retry');
    }
  }, [syncData, syncStatus.failedSyncs]);

  return {
    syncStatus,
    syncData,
    retrySyncFailed,
  };
};

// Network status hook
export const useNetworkStatus = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [connectionQuality, setConnectionQuality] = useState<'good' | 'poor' | 'offline'>('good');

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      setConnectionQuality('good');
      logger.info('Network connection restored', 'NetworkStatus');
    };

    const handleOffline = () => {
      setIsOnline(false);
      setConnectionQuality('offline');
      logger.warn('Network connection lost', 'NetworkStatus');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Test connection quality periodically
    const testConnection = async () => {
      if (!navigator.onLine) return;

      try {
        const start = Date.now();
        await fetch('/api/health', { 
          method: 'HEAD',
          cache: 'no-cache',
        });
        const duration = Date.now() - start;

        setConnectionQuality(duration > 2000 ? 'poor' : 'good');
      } catch (error) {
        setConnectionQuality('poor');
      }
    };

    const interval = setInterval(testConnection, 30000); // Test every 30 seconds

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      clearInterval(interval);
    };
  }, []);

  return {
    isOnline,
    connectionQuality,
  };
};

// Optimistic updates hook
export const useOptimisticUpdates = () => {
  const queryClient = useQueryClient();
  const { addError } = useErrorHandling();

  const performOptimisticUpdate = useCallback(async <T>(
    queryKey: string[],
    updateFn: (oldData: T) => T,
    mutationFn: () => Promise<any>,
    context: string = 'OptimisticUpdate'
  ) => {
    // Store the previous data for rollback
    const previousData = queryClient.getQueryData<T>(queryKey);

    try {
      // Optimistically update the cache
      queryClient.setQueryData<T>(queryKey, updateFn);

      // Perform the actual mutation
      await mutationFn();

      // Invalidate and refetch to ensure consistency
      await queryClient.invalidateQueries({ queryKey });

      logger.info('Optimistic update completed successfully', context, { queryKey });
    } catch (error: any) {
      // Rollback on error
      if (previousData !== undefined) {
        queryClient.setQueryData<T>(queryKey, previousData);
      }

      addError({
        type: 'server',
        message: 'Failed to update data',
        details: error,
        context,
        retryable: true,
      });

      logger.error('Optimistic update failed, rolling back', context, { error, queryKey });
      throw error;
    }
  }, [queryClient, addError]);

  return {
    performOptimisticUpdate,
  };
};

// Helper function to classify errors
function classifyError(error: any, context: string): Omit<AppError, 'id' | 'timestamp'> {
  // Network errors
  if (error.name === 'TypeError' && error.message.includes('fetch')) {
    return {
      type: 'network',
      message: 'Network connection error. Please check your internet connection.',
      details: error,
      context,
      retryable: true,
    };
  }

  // HTTP errors
  if (error.response) {
    const status = error.response.status;
    
    if (status === 401) {
      return {
        type: 'authentication',
        message: 'Authentication failed. Please log in again.',
        details: error,
        context,
        retryable: false,
      };
    }
    
    if (status === 403) {
      return {
        type: 'permission',
        message: 'You do not have permission to perform this action.',
        details: error,
        context,
        retryable: false,
      };
    }
    
    if (status >= 400 && status < 500) {
      return {
        type: 'validation',
        message: error.response.data?.message || 'Invalid request. Please check your input.',
        details: error,
        context,
        retryable: false,
      };
    }
    
    if (status >= 500) {
      return {
        type: 'server',
        message: 'Server error. Please try again later.',
        details: error,
        context,
        retryable: true,
      };
    }
  }

  // Timeout errors
  if (error.name === 'AbortError' || error.message.includes('timeout')) {
    return {
      type: 'network',
      message: 'Request timed out. Please try again.',
      details: error,
      context,
      retryable: true,
    };
  }

  // Default unknown error
  return {
    type: 'unknown',
    message: error.message || 'An unexpected error occurred.',
    details: error,
    context,
    retryable: true,
  };
}
