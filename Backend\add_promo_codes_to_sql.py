#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to add sample promo codes directly to the SQL database.
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta
from uuid import uuid4

# Add the Backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database import AsyncSessionLocal
from Database.models.discounts import PromoCode

async def create_sample_promo_codes():
    """Create sample promo codes in the SQL database"""
    
    # Sample promo codes data
    sample_promo_codes = [
        {
            "code": "WELCOME10",
            "name": "Welcome Discount",
            "description": "10% off for new customers",
            "discount_type": "percentage",
            "discount_value": 10.0,
            "scope": "order_total",
            "minimum_spend": 20.0,
            "maximum_discount": 50.0,
            "start_date": datetime.now() - timedelta(days=30),
            "end_date": datetime.now() + timedelta(days=60),
            "usage_limit": 100,
            "usage_count": 0,
            "is_active": True
        },
        {
            "code": "SAVE5",
            "name": "Save £5",
            "description": "£5 off orders over £25",
            "discount_type": "fixed_amount",
            "discount_value": 5.0,
            "scope": "order_total",
            "minimum_spend": 25.0,
            "start_date": datetime.now() - timedelta(days=7),
            "end_date": datetime.now() + timedelta(days=90),
            "usage_limit": 50,
            "usage_count": 0,
            "is_active": True
        },
        {
            "code": "HAPPYHOUR",
            "name": "Happy Hour Special",
            "description": "15% off during happy hours",
            "discount_type": "percentage",
            "discount_value": 15.0,
            "scope": "order_total",
            "minimum_spend": 15.0,
            "maximum_discount": 30.0,
            "start_date": datetime.now() - timedelta(days=1),
            "end_date": datetime.now() + timedelta(days=30),
            "usage_limit": 200,
            "usage_count": 0,
            "is_active": True
        },
        {
            "code": "STUDENT20",
            "name": "Student Discount",
            "description": "20% off for students",
            "discount_type": "percentage",
            "discount_value": 20.0,
            "scope": "order_total",
            "minimum_spend": 10.0,
            "maximum_discount": 25.0,
            "start_date": datetime.now() - timedelta(days=14),
            "end_date": datetime.now() + timedelta(days=120),
            "usage_limit": 75,
            "usage_count": 0,
            "is_active": True
        },
        {
            "code": "WEEKEND15",
            "name": "Weekend Special",
            "description": "15% off weekend orders",
            "discount_type": "percentage",
            "discount_value": 15.0,
            "scope": "order_total",
            "minimum_spend": 30.0,
            "maximum_discount": 40.0,
            "start_date": datetime.now() - timedelta(days=3),
            "end_date": datetime.now() + timedelta(days=45),
            "usage_limit": 150,
            "usage_count": 0,
            "is_active": True
        }
    ]
    
    print("🎯 Creating sample promo codes in SQL database...")
    
    async with AsyncSessionLocal() as session:
        try:
            # Check existing promo codes
            from sqlalchemy import select
            result = await session.execute(select(PromoCode))
            existing_codes = result.scalars().all()
            existing_code_names = {code.code for code in existing_codes}
            
            print(f"📊 Found {len(existing_codes)} existing promo codes")
            
            created_count = 0
            for promo_data in sample_promo_codes:
                if promo_data["code"] not in existing_code_names:
                    try:
                        promo_code = PromoCode(**promo_data)
                        session.add(promo_code)
                        print(f"✅ Created promo code: {promo_data['code']} - {promo_data['name']}")
                        created_count += 1
                    except Exception as e:
                        print(f"❌ Failed to create promo code {promo_data['code']}: {e}")
                else:
                    print(f"⚠️  Promo code {promo_data['code']} already exists, skipping...")
            
            # Commit all changes
            await session.commit()
            print(f"\n🎉 Successfully created {created_count} new promo codes!")
            
            # Display final count
            result = await session.execute(select(PromoCode))
            final_codes = result.scalars().all()
            print(f"📈 Total promo codes in database: {len(final_codes)}")
            
            return created_count
            
        except Exception as e:
            await session.rollback()
            print(f"💥 Error creating promo codes: {e}")
            raise

async def main():
    try:
        created_count = await create_sample_promo_codes()
        print(f"\n✨ Sample promo codes creation completed! Created {created_count} new codes.")
    except Exception as e:
        print(f"💥 Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
