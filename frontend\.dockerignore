# Node modules (will be installed during build)
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs (will be generated during build)
dist
build

# Development files
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE and editor files
.vscode
.idea
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore

# Documentation and test files
*.md
README*
CHANGELOG*
LICENSE*
test*
*.test.*
*.spec.*

# Temporary files
temp
tmp
*.tmp
*.temp

# Log files
logs
*.log

# Coverage directory used by tools like istanbul
coverage

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# Webpack bundle analyzer
bundle-analyzer-report.html

# Development tools
tools/
scripts/
.eslintrc*
.prettierrc*
jest.config.*
cypress/
cypress.json

# Uploads and data directories (not needed in container)
uploads/
data/

# Backup files
*.backup
*.bak
*_original.*
*.backup.*

# Shell scripts (not needed in container)
*.sh
!docker-entrypoint.sh

# Test HTML files
test*.html
verify*.html
redirect.html

# Temporary directories
temp/
