import { UnavailabilityType } from "@/types/staffAvailability";
import { getUnavailabilityColor } from "@/services/staffAvailabilityService";

interface AvailabilityLegendProps {
  className?: string;
}

const AvailabilityLegend = ({ className }: AvailabilityLegendProps) => {
  const unavailabilityTypes: { type: UnavailabilityType; label: string }[] = [
    { type: 'holiday', label: 'Holiday' },
    { type: 'sick', label: 'Sick Leave' },
    { type: 'personal', label: 'Personal Days' },
    { type: 'other', label: 'Other' },
  ];

  return (
    <div className={`flex flex-wrap gap-4 ${className}`}>
      {unavailabilityTypes.map((item) => (
        <div key={item.type} className="flex items-center gap-2">
          <div
            className="w-4 h-4 rounded-sm"
            style={{ backgroundColor: getUnavailabilityColor(item.type) }}
          />
          <span className="text-sm">{item.label}</span>
        </div>
      ))}
    </div>
  );
};

export default AvailabilityLegend;
