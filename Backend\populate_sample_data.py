#!/usr/bin/env python3
"""
<PERSON>ript to populate sample data into the database using the correct schema.
This script adds menu items and tables for testing the EPOS system.
"""

import sqlite3
import uuid
from datetime import datetime
import json

def get_restaurant_ids():
    """Get existing restaurant IDs from the database"""
    conn = sqlite3.connect("restro_manage.db")
    cursor = conn.cursor()
    
    cursor.execute("SELECT id, name FROM restaurants LIMIT 3;")
    restaurants = cursor.fetchall()
    
    conn.close()
    return restaurants

def populate_menu_items(restaurant_id, restaurant_name):
    """Populate sample menu items for a restaurant"""
    conn = sqlite3.connect("restro_manage.db")
    cursor = conn.cursor()
    
    # Sample menu items with proper allergen information
    menu_items = [
        {
            "name": "Classic Burger",
            "price": 12.99,
            "category": "main_course",
            "description": "Juicy beef patty with lettuce, tomato, and special sauce",
            "allergens": ["gluten", "dairy"],
            "ingredients": ["beef", "lettuce", "tomato", "bun", "sauce"]
        },
        {
            "name": "Caesar Salad",
            "price": 8.99,
            "category": "appetizer", 
            "description": "Fresh romaine lettuce with Caesar dressing and croutons",
            "allergens": ["dairy", "gluten"],
            "ingredients": ["romaine", "croutons", "parmesan", "dressing"]
        },
        {
            "name": "Grilled Salmon",
            "price": 18.99,
            "category": "main_course",
            "description": "Fresh Atlantic salmon with lemon and herbs",
            "allergens": ["fish"],
            "ingredients": ["salmon", "lemon", "herbs", "olive oil"]
        },
        {
            "name": "Chocolate Cake",
            "price": 6.99,
            "category": "dessert",
            "description": "Rich chocolate cake with vanilla ice cream",
            "allergens": ["dairy", "eggs", "gluten"],
            "ingredients": ["flour", "sugar", "chocolate", "butter", "eggs"]
        },
        {
            "name": "Pad Thai",
            "price": 14.99,
            "category": "main_course",
            "description": "Traditional Thai stir-fried noodles with shrimp",
            "allergens": ["shellfish", "nuts", "soy"],
            "ingredients": ["rice noodles", "shrimp", "bean sprouts", "peanuts"]
        },
        {
            "name": "Vegetable Stir Fry",
            "price": 11.99,
            "category": "main_course",
            "description": "Fresh vegetables stir-fried with sesame oil",
            "allergens": ["sesame", "soy"],
            "ingredients": ["mixed vegetables", "sesame oil", "soy sauce"]
        }
    ]
    
    created_count = 0
    for item in menu_items:
        item_id = str(uuid.uuid4())
        now = datetime.now().isoformat()
        
        cursor.execute("""
            INSERT INTO menu_items (
                id, restaurant_id, name, price, category, description, 
                allergens, ingredients, available, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            item_id,
            restaurant_id,
            item["name"],
            item["price"],
            item["category"],
            item["description"],
            json.dumps(item["allergens"]),
            json.dumps(item["ingredients"]),
            True,
            now,
            now
        ))
        created_count += 1
    
    conn.commit()
    conn.close()
    
    print(f"✅ Created {created_count} menu items for {restaurant_name}")

def populate_tables(restaurant_id, restaurant_name):
    """Populate sample tables for a restaurant"""
    conn = sqlite3.connect("restro_manage.db")
    cursor = conn.cursor()
    
    created_count = 0
    for i in range(1, 11):  # Create 10 tables
        table_id = str(uuid.uuid4())
        now = datetime.now().isoformat()
        
        cursor.execute("""
            INSERT INTO tables (
                id, restaurant_id, number, capacity, location, status, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            table_id,
            restaurant_id,
            i,
            4 if i <= 6 else 6,  # Smaller tables for 1-6, larger for 7-10
            "Main Floor" if i <= 8 else "VIP Section",
            "available",
            now,
            now
        ))
        created_count += 1
    
    conn.commit()
    conn.close()
    
    print(f"✅ Created {created_count} tables for {restaurant_name}")

def main():
    """Main function to populate sample data"""
    print("🌱 Populating Sample Data for EPOS Testing")
    print("=" * 50)
    
    # Get existing restaurants
    restaurants = get_restaurant_ids()
    
    if not restaurants:
        print("❌ No restaurants found in database!")
        return
    
    print(f"📊 Found {len(restaurants)} restaurants")
    
    # Populate data for the first restaurant only (for testing)
    restaurant_id, restaurant_name = restaurants[0]
    print(f"\n🏪 Populating data for: {restaurant_name}")
    
    # Check if data already exists
    conn = sqlite3.connect("restro_manage.db")
    cursor = conn.cursor()
    
    cursor.execute("SELECT COUNT(*) FROM menu_items WHERE restaurant_id = ?", (restaurant_id,))
    menu_count = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM tables WHERE restaurant_id = ?", (restaurant_id,))
    table_count = cursor.fetchone()[0]
    
    conn.close()
    
    if menu_count > 0:
        print(f"⚠️  Restaurant already has {menu_count} menu items, skipping menu population")
    else:
        populate_menu_items(restaurant_id, restaurant_name)
    
    if table_count > 0:
        print(f"⚠️  Restaurant already has {table_count} tables, skipping table population")
    else:
        populate_tables(restaurant_id, restaurant_name)
    
    print("\n🎉 Sample data population completed!")
    print("\nNext steps:")
    print("1. Test the menu API: GET /api/menu")
    print("2. Test the tables API: GET /api/tables") 
    print("3. Test order creation with the populated data")

if __name__ == "__main__":
    main()
