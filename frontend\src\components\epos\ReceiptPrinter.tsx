import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Printer, Download, AlertTriangle, X } from 'lucide-react';
import { toast } from '@/components/ui/sonner';

interface CartItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  allergens?: string[];
  notes?: string;
  addOns?: Array<{
    id: string;
    name: string;
    price: number;
  }>;
}

interface DiscountApplication {
  promo_code: string;
  discount_amount: number;
}

interface ReceiptData {
  orderNumber: string;
  tableName?: string;
  items: CartItem[];
  subtotal: number;
  discounts: DiscountApplication[];
  vatRate: number;
  total: number;
  paymentMethod: string;
  timestamp: Date;
  restaurantInfo: {
    name: string;
    address: string;
    phone: string;
    vatNumber?: string;
  };
}

interface TableInfo {
  id: string;
  area: string;
  number: number;
  type: "table" | "takeout";
}

interface SessionInfo {
  tableId: string;
  tableNumber: number;
  partySize: number;
  allergens: string[];
  hasAllergens: boolean;
}

interface ReceiptPrinterProps {
  isOpen: boolean;
  onClose: () => void;
  // Option 1: Pass complete receiptData object
  receiptData?: ReceiptData;
  // Option 2: Pass individual props (for backward compatibility)
  orderNumber?: string;
  cart?: CartItem[];
  total?: number;
  tableInfo?: TableInfo | null;
  sessionInfo?: SessionInfo | null;
  paymentMethod?: string;
}

const ReceiptPrinter: React.FC<ReceiptPrinterProps> = ({
  isOpen,
  onClose,
  receiptData: providedReceiptData,
  orderNumber,
  cart,
  total,
  tableInfo,
  sessionInfo,
  paymentMethod
}) => {
  // Construct receiptData from individual props if not provided
  const receiptData: ReceiptData = providedReceiptData || {
    orderNumber: orderNumber || `ORD-${Date.now()}`,
    tableName: tableInfo ? (tableInfo.type === 'takeout' ? 'Takeaway' : `Table ${tableInfo.number}`) : undefined,
    items: cart || [],
    subtotal: total || 0,
    discounts: [], // TODO: Add discounts support
    vatRate: 0.20, // 20% VAT
    total: total || 0,
    paymentMethod: paymentMethod || 'Card',
    timestamp: new Date(),
    restaurantInfo: {
      name: 'RestroManage Restaurant',
      address: '123 Restaurant Street, City, Postcode',
      phone: '+44 ************',
      vatNumber: 'GB123456789'
    }
  };

  // Defensive programming: ensure receiptData has required properties
  const safeReceiptData: ReceiptData = {
    orderNumber: receiptData.orderNumber || `ORD-${Date.now()}`,
    tableName: receiptData.tableName,
    items: receiptData.items || [],
    subtotal: receiptData.subtotal || 0,
    discounts: receiptData.discounts || [],
    vatRate: receiptData.vatRate || 0.20,
    total: receiptData.total || 0,
    paymentMethod: receiptData.paymentMethod || 'Card',
    timestamp: receiptData.timestamp || new Date(),
    restaurantInfo: receiptData.restaurantInfo || {
      name: 'RestroManage Restaurant',
      address: '123 Restaurant Street, City, Postcode',
      phone: '+44 ************',
      vatNumber: 'GB123456789'
    }
  };
  const handlePrint = () => {
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      toast.error('Unable to open print window. Please check your browser settings.');
      return;
    }

    const receiptHtml = generateReceiptHtml(safeReceiptData);
    printWindow.document.write(receiptHtml);
    printWindow.document.close();
    printWindow.print();
    printWindow.close();

    toast.success('Receipt sent to printer');
  };

  const handleDownload = () => {
    const receiptHtml = generateReceiptHtml(safeReceiptData);
    const blob = new Blob([receiptHtml], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `receipt-${safeReceiptData.orderNumber}.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast.success('Receipt downloaded');
  };

  // Get all allergens from the order with defensive programming
  const getAllergens = () => {
    const allergenSet = new Set<string>();
    if (safeReceiptData.items && Array.isArray(safeReceiptData.items)) {
      safeReceiptData.items.forEach(item => {
        if (item && item.allergens && Array.isArray(item.allergens) && item.allergens.length > 0) {
          item.allergens.forEach(allergen => {
            if (allergen && typeof allergen === 'string') {
              allergenSet.add(allergen);
            }
          });
        }
      });
    }
    return Array.from(allergenSet).sort();
  };

  const hasAllergens = getAllergens().length > 0;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md max-h-[90vh] overflow-y-auto" aria-describedby="receipt-description">
        <DialogHeader>
          <DialogTitle>Receipt Preview</DialogTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </Button>
        </DialogHeader>

        <div id="receipt-description" className="sr-only">
          Receipt preview for order {safeReceiptData.orderNumber}. Contains order details, items, totals, and allergen information if applicable.
        </div>

        <div className="space-y-4">
          {/* Receipt Preview */}
          <div className="bg-card text-card-foreground p-4 border rounded-lg font-mono text-sm">
            <ReceiptPreview receiptData={safeReceiptData} />
          </div>

          {/* Allergen Warning if applicable */}
          {hasAllergens && (
            <div className="p-3 bg-orange-50 dark:bg-orange-900/30 border border-orange-200 dark:border-orange-700 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <AlertTriangle className="h-4 w-4 text-orange-600 dark:text-orange-400" />
                <span className="font-medium text-orange-800 dark:text-orange-200">Allergen Information Included</span>
              </div>
              <p className="text-sm text-orange-700 dark:text-orange-300">
                This receipt includes allergen information for food safety compliance.
              </p>
            </div>
          )}
        </div>

        <DialogFooter className="flex gap-2">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button variant="outline" onClick={handleDownload}>
            <Download className="h-4 w-4 mr-2" />
            Download
          </Button>
          <Button onClick={handlePrint}>
            <Printer className="h-4 w-4 mr-2" />
            Print
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

const ReceiptPreview: React.FC<{ receiptData: ReceiptData }> = ({ receiptData }) => {
  const getAllergens = () => {
    const allergenSet = new Set<string>();
    if (receiptData && receiptData.items && Array.isArray(receiptData.items)) {
      receiptData.items.forEach(item => {
        if (item && item.allergens && Array.isArray(item.allergens) && item.allergens.length > 0) {
          item.allergens.forEach(allergen => {
            if (allergen && typeof allergen === 'string') {
              allergenSet.add(allergen);
            }
          });
        }
      });
    }
    return Array.from(allergenSet).sort();
  };

  const hasAllergens = getAllergens().length > 0;

  return (
    <div className="text-center space-y-2">
      {/* Header */}
      <div className="border-b-2 border-dashed pb-2 mb-4">
        <h2 className="text-lg font-bold">{receiptData?.restaurantInfo?.name || 'Restaurant'}</h2>
        <p className="text-xs">{receiptData?.restaurantInfo?.address || 'Address not available'}</p>
        <p className="text-xs">{receiptData?.restaurantInfo?.phone || 'Phone not available'}</p>
        {receiptData?.restaurantInfo?.vatNumber && (
          <p className="text-xs">VAT: {receiptData.restaurantInfo.vatNumber}</p>
        )}
      </div>

      {/* Order Info */}
      <div className="text-left space-y-1">
        <div className="flex justify-between">
          <span>Order #:</span>
          <span>{receiptData?.orderNumber || 'N/A'}</span>
        </div>
        {receiptData?.tableName && (
          <div className="flex justify-between">
            <span>Table:</span>
            <span>{receiptData.tableName}</span>
          </div>
        )}
        <div className="flex justify-between">
          <span>Date:</span>
          <span>{receiptData?.timestamp ? receiptData.timestamp.toLocaleDateString() : new Date().toLocaleDateString()}</span>
        </div>
        <div className="flex justify-between">
          <span>Time:</span>
          <span>{receiptData?.timestamp ? receiptData.timestamp.toLocaleTimeString() : new Date().toLocaleTimeString()}</span>
        </div>
      </div>

      <div className="border-b border-dashed my-2"></div>

      {/* Items */}
      <div className="text-left space-y-2">
        {receiptData?.items && Array.isArray(receiptData.items) ? receiptData.items.map((item, index) => (
          <div key={index} className="space-y-1">
            <div className="flex justify-between">
              <span>{item?.quantity || 0}x {item?.name || 'Unknown Item'}</span>
              <span>£{((item?.price || 0) * (item?.quantity || 0)).toFixed(2)}</span>
            </div>
            {item?.addOns && Array.isArray(item.addOns) && item.addOns.length > 0 && (
              <div className="text-xs text-gray-600 ml-4">
                + {item.addOns.map(addOn => addOn?.name || 'Unknown Add-on').join(', ')}
              </div>
            )}
            {item?.notes && (
              <div className="text-xs text-gray-600 ml-4 italic">
                Note: {item.notes}
              </div>
            )}
          </div>
        )) : (
          <div className="text-center text-gray-500">No items in order</div>
        )}
      </div>

      <div className="border-b border-dashed my-2"></div>

      {/* Totals */}
      <div className="text-left space-y-1">
        <div className="flex justify-between">
          <span>Subtotal:</span>
          <span>£{(receiptData?.subtotal || 0).toFixed(2)}</span>
        </div>
        {receiptData?.discounts && Array.isArray(receiptData.discounts) && receiptData.discounts.length > 0 && (
          <>
            {receiptData.discounts.map((discount, index) => (
              <div key={index} className="flex justify-between text-green-600">
                <span>{discount?.promo_code || 'Discount'}:</span>
                <span>-£{(discount?.discount_amount || 0).toFixed(2)}</span>
              </div>
            ))}
          </>
        )}
        <div className="flex justify-between">
          <span>VAT ({((receiptData?.vatRate || 0.20) * 100).toFixed(0)}%):</span>
          <span>£{((receiptData?.total || 0) * (receiptData?.vatRate || 0.20)).toFixed(2)}</span>
        </div>
        <div className="flex justify-between font-bold border-t pt-1">
          <span>TOTAL:</span>
          <span>£{(receiptData?.total || 0).toFixed(2)}</span>
        </div>
        <div className="flex justify-between text-sm">
          <span>Payment Method:</span>
          <span>{receiptData?.paymentMethod || 'Card'}</span>
        </div>
      </div>

      {/* Allergen Information */}
      {hasAllergens && (
        <>
          <div className="border-b border-dashed my-2"></div>
          <div className="text-left">
            <div className="font-bold text-orange-600 mb-1">
              ⚠️ ALLERGEN INFORMATION
            </div>
            <div className="text-xs">
              <p className="mb-1">Possible Allergens: {getAllergens().join(', ')}</p>
              <p className="text-gray-600">
                Please inform staff of any allergies or dietary requirements.
                This information is provided for guidance only.
              </p>
            </div>
          </div>
        </>
      )}

      <div className="border-b-2 border-dashed my-2"></div>

      {/* Footer */}
      <div className="text-xs text-center space-y-1">
        <p>Thank you for dining with us!</p>
        <p>Please retain this receipt for your records</p>
      </div>
    </div>
  );
};

const generateReceiptHtml = (receiptData: ReceiptData): string => {
  const getAllergens = () => {
    const allergenSet = new Set<string>();
    if (receiptData && receiptData.items && Array.isArray(receiptData.items)) {
      receiptData.items.forEach(item => {
        if (item && item.allergens && Array.isArray(item.allergens) && item.allergens.length > 0) {
          item.allergens.forEach(allergen => {
            if (allergen && typeof allergen === 'string') {
              allergenSet.add(allergen);
            }
          });
        }
      });
    }
    return Array.from(allergenSet).sort();
  };

  const hasAllergens = getAllergens().length > 0;

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Receipt - ${receiptData.orderNumber}</title>
      <style>
        body { font-family: 'Courier New', monospace; font-size: 12px; margin: 0; padding: 20px; }
        .receipt { max-width: 300px; margin: 0 auto; }
        .center { text-align: center; }
        .left { text-align: left; }
        .bold { font-weight: bold; }
        .dashed { border-bottom: 1px dashed #000; margin: 10px 0; }
        .flex { display: flex; justify-content: space-between; }
        .allergen-warning { color: #d97706; font-weight: bold; }
        .allergen-info { font-size: 10px; color: #666; }
        @media print {
          body { margin: 0; padding: 10px; }
          .receipt { max-width: none; }
        }
      </style>
    </head>
    <body>
      <div class="receipt">
        <div class="center">
          <h2 class="bold">${receiptData?.restaurantInfo?.name || 'Restaurant'}</h2>
          <div>${receiptData?.restaurantInfo?.address || 'Address not available'}</div>
          <div>${receiptData?.restaurantInfo?.phone || 'Phone not available'}</div>
          ${receiptData?.restaurantInfo?.vatNumber ? `<div>VAT: ${receiptData.restaurantInfo.vatNumber}</div>` : ''}
        </div>

        <div class="dashed"></div>

        <div class="left">
          <div class="flex"><span>Order #:</span><span>${receiptData?.orderNumber || 'N/A'}</span></div>
          ${receiptData?.tableName ? `<div class="flex"><span>Table:</span><span>${receiptData.tableName}</span></div>` : ''}
          <div class="flex"><span>Date:</span><span>${receiptData?.timestamp ? receiptData.timestamp.toLocaleDateString() : new Date().toLocaleDateString()}</span></div>
          <div class="flex"><span>Time:</span><span>${receiptData?.timestamp ? receiptData.timestamp.toLocaleTimeString() : new Date().toLocaleTimeString()}</span></div>
        </div>
        
        <div class="dashed"></div>
        
        <div class="left">
          ${receiptData?.items && Array.isArray(receiptData.items) ? receiptData.items.map(item => `
            <div class="flex">
              <span>${item?.quantity || 0}x ${item?.name || 'Unknown Item'}</span>
              <span>£${((item?.price || 0) * (item?.quantity || 0)).toFixed(2)}</span>
            </div>
            ${item?.addOns && Array.isArray(item.addOns) && item.addOns.length > 0 ? `<div style="margin-left: 20px; font-size: 10px;">+ ${item.addOns.map(addOn => addOn?.name || 'Unknown Add-on').join(', ')}</div>` : ''}
            ${item?.notes ? `<div style="margin-left: 20px; font-size: 10px; font-style: italic;">Note: ${item.notes}</div>` : ''}
          `).join('') : '<div style="text-align: center; color: #666;">No items in order</div>'}
        </div>
        
        <div class="dashed"></div>
        
        <div class="left">
          <div class="flex"><span>Subtotal:</span><span>£${(receiptData?.subtotal || 0).toFixed(2)}</span></div>
          ${receiptData?.discounts && Array.isArray(receiptData.discounts) ? receiptData.discounts.map(discount => `
            <div class="flex" style="color: green;">
              <span>${discount?.promo_code || 'Discount'}:</span>
              <span>-£${(discount?.discount_amount || 0).toFixed(2)}</span>
            </div>
          `).join('') : ''}
          <div class="flex"><span>VAT (${((receiptData?.vatRate || 0.20) * 100).toFixed(0)}%):</span><span>£${((receiptData?.total || 0) * (receiptData?.vatRate || 0.20)).toFixed(2)}</span></div>
          <div class="flex bold" style="border-top: 1px solid #000; padding-top: 5px;">
            <span>TOTAL:</span><span>£${(receiptData?.total || 0).toFixed(2)}</span>
          </div>
          <div class="flex"><span>Payment Method:</span><span>${receiptData?.paymentMethod || 'Card'}</span></div>
        </div>
        
        ${hasAllergens ? `
          <div class="dashed"></div>
          <div class="left">
            <div class="allergen-warning">⚠️ ALLERGEN INFORMATION</div>
            <div class="allergen-info">
              <div>Possible Allergens: ${getAllergens().join(', ')}</div>
              <div>Please inform staff of any allergies or dietary requirements. This information is provided for guidance only.</div>
            </div>
          </div>
        ` : ''}
        
        <div class="dashed"></div>
        
        <div class="center">
          <div>Thank you for dining with us!</div>
          <div>Please retain this receipt for your records</div>
        </div>
      </div>
    </body>
    </html>
  `;
};

export default ReceiptPrinter;
