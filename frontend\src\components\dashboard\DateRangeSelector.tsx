import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DateRange } from "react-day-picker";

interface DateRangeSelectorProps {
  onRangeChange?: (range: { from: Date; to: Date }) => void;
}

const DateRangeSelector = ({ onRangeChange = () => {} }: DateRangeSelectorProps) => {
  const [date, setDate] = useState<{ from: Date; to: Date }>({
    from: new Date(new Date().setDate(new Date().getDate() - 7)),
    to: new Date(),
  });
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  
  // Predefined ranges
  const handleQuickSelect = (value: string) => {
    const today = new Date();
    let from = new Date();
    
    switch (value) {
      case "today":
        from = new Date(today);
        break;
      case "yesterday":
        from = new Date(today);
        from.setDate(from.getDate() - 1);
        break;
      case "7days":
        from = new Date(today);
        from.setDate(from.getDate() - 7);
        break;
      case "30days":
        from = new Date(today);
        from.setDate(from.getDate() - 30);
        break;
      case "thisMonth":
        from = new Date(today.getFullYear(), today.getMonth(), 1);
        break;
      case "lastMonth":
        from = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        today.setDate(0); // Last day of previous month
        break;
    }
    
    const newRange = { from, to: today };
    setDate(newRange);
    onRangeChange(newRange);
  };
  
  return (
    <div className="flex flex-col sm:flex-row gap-2">
      <Select onValueChange={handleQuickSelect} defaultValue="7days">
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder="Select range" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="today">Today</SelectItem>
          <SelectItem value="yesterday">Yesterday</SelectItem>
          <SelectItem value="7days">Last 7 days</SelectItem>
          <SelectItem value="30days">Last 30 days</SelectItem>
          <SelectItem value="thisMonth">This month</SelectItem>
          <SelectItem value="lastMonth">Last month</SelectItem>
        </SelectContent>
      </Select>
      
      <div className="grid gap-2">
        <Popover open={isCalendarOpen} onOpenChange={setIsCalendarOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                "w-[280px] justify-start text-left font-normal",
                !date && "text-muted-foreground"
              )}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {date?.from ? (
                date.to ? (
                  <>
                    {format(date.from, "LLL dd, y")} - {format(date.to, "LLL dd, y")}
                  </>
                ) : (
                  format(date.from, "LLL dd, y")
                )
              ) : (
                <span>Pick a date</span>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              initialFocus
              mode="range"
              defaultMonth={date?.from}
              selected={date}
              onSelect={(newDate: DateRange | undefined) => {
                if (newDate?.from && newDate?.to) {
                  const validRange = { from: newDate.from, to: newDate.to };
                  setDate(validRange);
                  onRangeChange(validRange);
                  setIsCalendarOpen(false);
                } else if (newDate?.from) {
                  // If only from date is selected, set to as the same date
                  const validRange = { from: newDate.from, to: newDate.from };
                  setDate(validRange);
                } else {
                  // Fallback to default range
                  const defaultRange = { from: new Date(), to: new Date() };
                  setDate(defaultRange);
                }
              }}
              numberOfMonths={2}
            />
          </PopoverContent>
        </Popover>
      </div>
    </div>
  );
};

export default DateRangeSelector;
