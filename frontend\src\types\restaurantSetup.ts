// Restaurant Registration and Setup Types for Promith

export interface RestaurantRegistrationData {
  // Basic Information
  restaurantName: string;
  ownerName: string;
  email: string;
  password: string;
  confirmPassword: string;
  
  // Enhanced Registration Fields
  restaurantType: 'restaurant' | 'bar' | 'cafe' | 'fast-food' | 'fine-dining';
  businessLicenseNumber: string;
  phone: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  
  // Logo Upload
  logo?: File;
  logoUrl?: string;
  
  // Legal Agreements
  acceptTerms: boolean;
  acceptPrivacy: boolean;
}

export interface TableConfiguration {
  id: string;
  number: string | number;
  capacity: 2 | 4 | 6 | 8 | 10 | 12;
  type: 'indoor' | 'outdoor' | 'bar' | 'private';
  shape: 'round' | 'square' | 'rectangular';
  isActive: boolean;
}

export interface OperatingHours {
  day: 'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday' | 'sunday';
  isOpen: boolean;
  openTime: string; // Format: "HH:MM"
  closeTime: string; // Format: "HH:MM"
  breakStart?: string; // Optional break time
  breakEnd?: string;
}

export interface RestaurantSetupData {
  // Logo Management
  logo?: File;
  logoUrl?: string;
  logoPosition: 'center' | 'left' | 'right';
  useDefaultLogo: boolean;
  
  // Table Configuration
  totalTables: number;
  tables: TableConfiguration[];
  defaultTableCapacity: 2 | 4 | 6 | 8;
  tableNamingSystem: 'numbers' | 'letters' | 'custom';
  
  // Business Details
  operatingHours: OperatingHours[];
  cuisineTypes: string[]; // Multiple selection
  priceRange: {
    min: number;
    max: number;
    currency: string;
  };
  averageServiceTime: number; // minutes
  
  // Calculated Fields
  totalSeatingCapacity: number;
  estimatedDailyCovers: number;
}

export interface CuisineType {
  id: string;
  name: string;
  category: 'international' | 'regional' | 'specialty';
}

export interface RestaurantProfile {
  id: string;
  registrationData: RestaurantRegistrationData;
  setupData: RestaurantSetupData;
  isSetupComplete: boolean;
  createdAt: string;
  updatedAt: string;
  status: 'pending' | 'active' | 'suspended';
}

// Form Validation Types
export interface ValidationError {
  field: string;
  message: string;
}

export interface FormValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

// File Upload Types
export interface LogoUploadConfig {
  maxSize: number; // bytes
  allowedFormats: string[];
  recommendedDimensions: {
    width: number;
    height: number;
  };
  cropAspectRatio: number;
}

// Default configurations
export const DEFAULT_LOGO_CONFIG: LogoUploadConfig = {
  maxSize: 2 * 1024 * 1024, // 2MB
  allowedFormats: ['image/png', 'image/jpeg', 'image/jpg', 'image/svg+xml'],
  recommendedDimensions: {
    width: 200,
    height: 200,
  },
  cropAspectRatio: 1, // Square aspect ratio
};

export const CUISINE_TYPES: CuisineType[] = [
  { id: 'american', name: 'American', category: 'international' },
  { id: 'italian', name: 'Italian', category: 'international' },
  { id: 'chinese', name: 'Chinese', category: 'international' },
  { id: 'mexican', name: 'Mexican', category: 'international' },
  { id: 'indian', name: 'Indian', category: 'international' },
  { id: 'french', name: 'French', category: 'international' },
  { id: 'japanese', name: 'Japanese', category: 'international' },
  { id: 'thai', name: 'Thai', category: 'international' },
  { id: 'mediterranean', name: 'Mediterranean', category: 'international' },
  { id: 'british', name: 'British', category: 'regional' },
  { id: 'seafood', name: 'Seafood', category: 'specialty' },
  { id: 'steakhouse', name: 'Steakhouse', category: 'specialty' },
  { id: 'vegetarian', name: 'Vegetarian', category: 'specialty' },
  { id: 'vegan', name: 'Vegan', category: 'specialty' },
  { id: 'pizza', name: 'Pizza', category: 'specialty' },
  { id: 'bbq', name: 'BBQ', category: 'specialty' },
];

export const RESTAURANT_TYPES = [
  { value: 'restaurant', label: 'Restaurant', description: 'Full-service dining establishment' },
  { value: 'bar', label: 'Bar', description: 'Primarily serves alcoholic beverages' },
  { value: 'cafe', label: 'Café', description: 'Casual dining with coffee and light meals' },
  { value: 'fast-food', label: 'Fast Food', description: 'Quick service restaurant' },
  { value: 'fine-dining', label: 'Fine Dining', description: 'Upscale dining experience' },
];

export const TABLE_CAPACITIES = [2, 4, 6, 8, 10, 12] as const;
export const TABLE_TYPES = ['indoor', 'outdoor', 'bar', 'private'] as const;
export const TABLE_SHAPES = ['round', 'square', 'rectangular'] as const;
