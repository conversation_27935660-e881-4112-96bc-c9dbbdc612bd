/**
 * Staff state slice for RestroManage
 * Manages staff list, scheduling data, authentication states, and role-based permissions
 */

import { StateCreator } from 'zustand';
import { 
  StaffState,
  StaffMember,
  StaffSchedule,
  Permission,
  Role,
  FilterState,
  Shift
} from '@/types/store';
import { userApi } from '@/services/apiService';
import logger from '@/utils/logger';

export interface StaffSlice extends StaffState {
  // State
  staffMembers: StaffMember[];
  staffSchedules: StaffSchedule[];
  activeStaff: StaffMember[];
  selectedStaffMember: StaffMember | null;
  staffFilters: FilterState;
  scheduleView: 'daily' | 'weekly' | 'monthly';
  permissions: Permission[];
  roles: Role[];
  isStaffLoading: boolean;
  staffError: string | null;
  lastStaffUpdate: Date | null;

  // Actions
  initializeStaff: (restaurantId: string) => Promise<void>;
  addStaffMember: (member: Omit<StaffMember, 'id'>) => Promise<void>;
  updateStaffMember: (memberId: string, updates: Partial<StaffMember>) => Promise<void>;
  removeStaffMember: (memberId: string) => Promise<void>;
  setSelectedStaffMember: (member: StaffMember | null) => void;
  updateStaffSchedule: (scheduleId: string, updates: Partial<StaffSchedule>) => Promise<void>;
  addShift: (staffId: string, shift: Omit<Shift, 'id'>) => Promise<void>;
  updateShift: (shiftId: string, updates: Partial<Shift>) => Promise<void>;
  removeShift: (shiftId: string) => Promise<void>;
  clockIn: (staffId: string) => Promise<void>;
  clockOut: (staffId: string) => Promise<void>;
  startBreak: (staffId: string) => Promise<void>;
  endBreak: (staffId: string) => Promise<void>;
  updateStaffFilters: (filters: Partial<FilterState>) => void;
  setScheduleView: (view: 'daily' | 'weekly' | 'monthly') => void;
  syncStaff: () => Promise<void>;
  resetStaff: () => void;
  clearStaffData: (restaurantId: string) => void;
}

const initialStaffFilters: FilterState = {
  searchQuery: '',
  dateRange: {
    from: new Date(),
    to: new Date(new Date().setDate(new Date().getDate() + 7)), // Next 7 days
  },
  categories: [],
  status: [],
};

const defaultPermissions: Permission[] = [
  {
    id: 'orders_view',
    name: 'View Orders',
    description: 'Can view order information',
    module: 'orders',
    actions: ['read'],
  },
  {
    id: 'orders_create',
    name: 'Create Orders',
    description: 'Can create new orders',
    module: 'orders',
    actions: ['create'],
  },
  {
    id: 'orders_update',
    name: 'Update Orders',
    description: 'Can modify existing orders',
    module: 'orders',
    actions: ['update'],
  },
  {
    id: 'inventory_view',
    name: 'View Inventory',
    description: 'Can view inventory information',
    module: 'inventory',
    actions: ['read'],
  },
  {
    id: 'staff_manage',
    name: 'Manage Staff',
    description: 'Can manage staff members and schedules',
    module: 'staff',
    actions: ['create', 'read', 'update', 'delete'],
  },
  {
    id: 'analytics_view',
    name: 'View Analytics',
    description: 'Can view analytics and reports',
    module: 'analytics',
    actions: ['read'],
  },
];

const defaultRoles: Role[] = [
  {
    id: 'admin',
    name: 'Administrator',
    description: 'Full system access',
    permissions: ['orders_view', 'orders_create', 'orders_update', 'inventory_view', 'staff_manage', 'analytics_view'],
    level: 10,
  },
  {
    id: 'manager',
    name: 'Manager',
    description: 'Management level access',
    permissions: ['orders_view', 'orders_create', 'orders_update', 'inventory_view', 'analytics_view'],
    level: 8,
  },
  {
    id: 'waiter',
    name: 'Waiter',
    description: 'Service staff access',
    permissions: ['orders_view', 'orders_create', 'orders_update'],
    level: 5,
  },
  {
    id: 'chef',
    name: 'Chef',
    description: 'Kitchen staff access',
    permissions: ['orders_view', 'inventory_view'],
    level: 5,
  },
  {
    id: 'hostess',
    name: 'Hostess',
    description: 'Front of house access',
    permissions: ['orders_view'],
    level: 3,
  },
];

export const createStaffSlice: StateCreator<
  StaffSlice,
  [],
  [],
  StaffSlice
> = (set, get) => ({
  // Initial state
  staffMembers: [],
  staffSchedules: [],
  activeStaff: [],
  selectedStaffMember: null,
  staffFilters: initialStaffFilters,
  scheduleView: 'weekly',
  permissions: defaultPermissions,
  roles: defaultRoles,
  isStaffLoading: false,
  staffError: null,
  lastStaffUpdate: null,

  // Actions
  initializeStaff: async (restaurantId: string) => {
    try {
      set((state) => {
        state.isStaffLoading = true;
        state.staffError = null;
      });

      logger.info('Initializing staff data', 'StaffSlice', { restaurantId });

      // Fetch staff data from API
      const staffData = await userApi.getUsers(restaurantId);

      set((state) => {
        // Transform API data to StaffMember format
        state.staffMembers = staffData.map((user: any) => ({
          id: user.id,
          name: user.name,
          email: user.email,
          phone: user.phone || '',
          role: user.role,
          position: user.position || user.role,
          status: user.status || 'active',
          hireDate: user.hireDate || new Date().toISOString().split('T')[0],
          performance: user.performance || 100,
          metrics: {
            sales: user.metrics?.sales || 0,
            tablesTurned: user.metrics?.tablesTurned || 0,
            customerRating: user.metrics?.customerRating || 5.0,
            ordersProcessed: user.metrics?.ordersProcessed || 0,
            averageServiceTime: user.metrics?.averageServiceTime || 0,
          },
          schedule: {
            id: `schedule_${user.id}`,
            staffId: user.id,
            shifts: [],
            availableDays: user.availableDays || ['mon', 'tue', 'wed', 'thu', 'fri'],
            assignedHours: user.assignedHours || 40,
            overtimeHours: 0,
          },
          permissions: getPermissionsForRole(user.role, state.roles, state.permissions),
          pin: user.pin,
        }));

        // Filter active staff
        state.activeStaff = state.staffMembers.filter(member => 
          member.status === 'active' || member.status === 'on-break'
        );

        state.isStaffLoading = false;
        state.lastStaffUpdate = new Date();
      });

      logger.info('Staff data initialized successfully', 'StaffSlice', { 
        restaurantId, 
        staffCount: get().staffMembers.length 
      });
    } catch (error) {
      set((state) => {
        state.isStaffLoading = false;
        state.staffError = error instanceof Error ? error.message : 'Failed to initialize staff data';
      });

      logger.error('Failed to initialize staff data', 'StaffSlice', { error, restaurantId });
      throw error;
    }
  },

  addStaffMember: async (member: Omit<StaffMember, 'id'>) => {
    const restaurantId = get().currentRestaurantId;
    if (!restaurantId) {
      logger.warn('Cannot add staff member without restaurant ID', 'StaffSlice');
      return;
    }

    try {
      logger.info('Adding staff member', 'StaffSlice', { name: member.name, role: member.role });

      // Create user via API
      const newUser = await userApi.createUser({
        name: member.name,
        email: member.email,
        phone: member.phone,
        role: member.role,
        position: member.position,
        restaurant_id: restaurantId,
        hire_date: member.hireDate,
        pin: member.pin,
      });

      set((state) => {
        const newMember: StaffMember = {
          ...member,
          id: newUser.id,
          permissions: getPermissionsForRole(member.role, state.roles, state.permissions),
        };

        state.staffMembers.push(newMember);
        
        if (newMember.status === 'active') {
          state.activeStaff.push(newMember);
        }

        state.lastStaffUpdate = new Date();
      });

      logger.info('Staff member added successfully', 'StaffSlice', { id: newUser.id, name: member.name });
    } catch (error) {
      logger.error('Failed to add staff member', 'StaffSlice', { error, member: member.name });
      throw error;
    }
  },

  updateStaffMember: async (memberId: string, updates: Partial<StaffMember>) => {
    try {
      logger.info('Updating staff member', 'StaffSlice', { memberId, updates });

      // Update user via API
      await userApi.updateUser(memberId, updates);

      set((state) => {
        const index = state.staffMembers.findIndex(member => member.id === memberId);
        if (index !== -1) {
          state.staffMembers[index] = { ...state.staffMembers[index], ...updates };

          // Update permissions if role changed
          if (updates.role) {
            state.staffMembers[index].permissions = getPermissionsForRole(
              updates.role, 
              state.roles, 
              state.permissions
            );
          }

          // Update active staff list
          const updatedMember = state.staffMembers[index];
          const activeIndex = state.activeStaff.findIndex(member => member.id === memberId);
          
          if (updatedMember.status === 'active' || updatedMember.status === 'on-break') {
            if (activeIndex === -1) {
              state.activeStaff.push(updatedMember);
            } else {
              state.activeStaff[activeIndex] = updatedMember;
            }
          } else if (activeIndex !== -1) {
            state.activeStaff.splice(activeIndex, 1);
          }

          // Update selected member if it's the same one
          if (state.selectedStaffMember?.id === memberId) {
            state.selectedStaffMember = updatedMember;
          }
        }

        state.lastStaffUpdate = new Date();
      });

      logger.info('Staff member updated successfully', 'StaffSlice', { memberId });
    } catch (error) {
      logger.error('Failed to update staff member', 'StaffSlice', { error, memberId });
      throw error;
    }
  },

  removeStaffMember: async (memberId: string) => {
    try {
      logger.info('Removing staff member', 'StaffSlice', { memberId });

      // Remove user via API
      await userApi.deleteUser(memberId);

      set((state) => {
        state.staffMembers = state.staffMembers.filter(member => member.id !== memberId);
        state.activeStaff = state.activeStaff.filter(member => member.id !== memberId);
        state.staffSchedules = state.staffSchedules.filter(schedule => schedule.staffId !== memberId);

        if (state.selectedStaffMember?.id === memberId) {
          state.selectedStaffMember = null;
        }

        state.lastStaffUpdate = new Date();
      });

      logger.info('Staff member removed successfully', 'StaffSlice', { memberId });
    } catch (error) {
      logger.error('Failed to remove staff member', 'StaffSlice', { error, memberId });
      throw error;
    }
  },

  setSelectedStaffMember: (member: StaffMember | null) => {
    set((state) => {
      state.selectedStaffMember = member;
    });
  },

  updateStaffSchedule: async (scheduleId: string, updates: Partial<StaffSchedule>) => {
    try {
      logger.info('Updating staff schedule', 'StaffSlice', { scheduleId, updates });

      set((state) => {
        const index = state.staffSchedules.findIndex(schedule => schedule.id === scheduleId);
        if (index !== -1) {
          state.staffSchedules[index] = { ...state.staffSchedules[index], ...updates };
        }

        // Update the schedule in the staff member as well
        const staffIndex = state.staffMembers.findIndex(member => member.schedule.id === scheduleId);
        if (staffIndex !== -1) {
          state.staffMembers[staffIndex].schedule = { 
            ...state.staffMembers[staffIndex].schedule, 
            ...updates 
          };
        }

        state.lastStaffUpdate = new Date();
      });

      logger.info('Staff schedule updated successfully', 'StaffSlice', { scheduleId });
    } catch (error) {
      logger.error('Failed to update staff schedule', 'StaffSlice', { error, scheduleId });
      throw error;
    }
  },

  addShift: async (staffId: string, shift: Omit<Shift, 'id'>) => {
    try {
      const newShift: Shift = {
        ...shift,
        id: `shift_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      };

      set((state) => {
        const staffIndex = state.staffMembers.findIndex(member => member.id === staffId);
        if (staffIndex !== -1) {
          state.staffMembers[staffIndex].schedule.shifts.push(newShift);
        }

        const scheduleIndex = state.staffSchedules.findIndex(schedule => schedule.staffId === staffId);
        if (scheduleIndex !== -1) {
          state.staffSchedules[scheduleIndex].shifts.push(newShift);
        }

        state.lastStaffUpdate = new Date();
      });

      logger.info('Shift added successfully', 'StaffSlice', { staffId, shiftId: newShift.id });
    } catch (error) {
      logger.error('Failed to add shift', 'StaffSlice', { error, staffId });
      throw error;
    }
  },

  updateShift: async (shiftId: string, updates: Partial<Shift>) => {
    try {
      set((state) => {
        // Update shift in staff members
        state.staffMembers.forEach(member => {
          const shiftIndex = member.schedule.shifts.findIndex(shift => shift.id === shiftId);
          if (shiftIndex !== -1) {
            member.schedule.shifts[shiftIndex] = { ...member.schedule.shifts[shiftIndex], ...updates };
          }
        });

        // Update shift in schedules
        state.staffSchedules.forEach(schedule => {
          const shiftIndex = schedule.shifts.findIndex(shift => shift.id === shiftId);
          if (shiftIndex !== -1) {
            schedule.shifts[shiftIndex] = { ...schedule.shifts[shiftIndex], ...updates };
          }
        });

        state.lastStaffUpdate = new Date();
      });

      logger.info('Shift updated successfully', 'StaffSlice', { shiftId });
    } catch (error) {
      logger.error('Failed to update shift', 'StaffSlice', { error, shiftId });
      throw error;
    }
  },

  removeShift: async (shiftId: string) => {
    try {
      set((state) => {
        // Remove shift from staff members
        state.staffMembers.forEach(member => {
          member.schedule.shifts = member.schedule.shifts.filter(shift => shift.id !== shiftId);
        });

        // Remove shift from schedules
        state.staffSchedules.forEach(schedule => {
          schedule.shifts = schedule.shifts.filter(shift => shift.id !== shiftId);
        });

        state.lastStaffUpdate = new Date();
      });

      logger.info('Shift removed successfully', 'StaffSlice', { shiftId });
    } catch (error) {
      logger.error('Failed to remove shift', 'StaffSlice', { error, shiftId });
      throw error;
    }
  },

  clockIn: async (staffId: string) => {
    await get().updateStaffMember(staffId, { status: 'active' });
    logger.info('Staff member clocked in', 'StaffSlice', { staffId });
  },

  clockOut: async (staffId: string) => {
    await get().updateStaffMember(staffId, { status: 'off-duty' });
    logger.info('Staff member clocked out', 'StaffSlice', { staffId });
  },

  startBreak: async (staffId: string) => {
    await get().updateStaffMember(staffId, { status: 'on-break' });
    logger.info('Staff member started break', 'StaffSlice', { staffId });
  },

  endBreak: async (staffId: string) => {
    await get().updateStaffMember(staffId, { status: 'active' });
    logger.info('Staff member ended break', 'StaffSlice', { staffId });
  },

  updateStaffFilters: (filters: Partial<FilterState>) => {
    set((state) => {
      state.staffFilters = { ...state.staffFilters, ...filters };
    });
  },

  setScheduleView: (view: 'daily' | 'weekly' | 'monthly') => {
    set((state) => {
      state.scheduleView = view;
    });
  },

  syncStaff: async () => {
    const restaurantId = get().currentRestaurantId;
    if (!restaurantId) return;

    await get().initializeStaff(restaurantId);
  },

  resetStaff: () => {
    set((state) => {
      state.staffMembers = [];
      state.staffSchedules = [];
      state.activeStaff = [];
      state.selectedStaffMember = null;
      state.staffFilters = initialStaffFilters;
      state.scheduleView = 'weekly';
      state.isStaffLoading = false;
      state.staffError = null;
      state.lastStaffUpdate = null;
    });
  },

  clearStaffData: (restaurantId: string) => {
    set((state) => {
      if (state.currentRestaurantId === restaurantId) {
        state.staffMembers = [];
        state.staffSchedules = [];
        state.activeStaff = [];
        state.selectedStaffMember = null;
      }
    });
  },
});

// Helper function to get permissions for a role
function getPermissionsForRole(roleName: string, roles: Role[], permissions: Permission[]): Permission[] {
  const role = roles.find(r => r.id === roleName || r.name.toLowerCase() === roleName.toLowerCase());
  if (!role) return [];

  return permissions.filter(permission => role.permissions.includes(permission.id));
}
