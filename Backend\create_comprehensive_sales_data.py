#!/usr/bin/env python3
"""
Comprehensive Sales Data Generator for RestroManage Analytics
Creates 90 days of realistic sales data with seasonal patterns, time-based preferences,
and category-specific ordering behaviors for advanced analytics.
"""

import sqlite3
import uuid
import random
import math
from datetime import datetime, timedelta
import json

def get_restaurant_data():
    """Get restaurant and menu data from the database"""
    conn = sqlite3.connect("restro_manage.db")
    cursor = conn.cursor()
    
    # Get first restaurant
    cursor.execute("SELECT id, name FROM restaurants LIMIT 1;")
    restaurant = cursor.fetchone()
    
    if not restaurant:
        print("❌ No restaurants found in database")
        return None, None, None, None
    
    restaurant_id, restaurant_name = restaurant
    
    # Get menu items with categories
    cursor.execute("""
        SELECT id, name, price, category 
        FROM menu_items 
        WHERE restaurant_id = ? 
        ORDER BY category, name
    """, (restaurant_id,))
    menu_items = cursor.fetchall()
    
    # Get tables
    cursor.execute("SELECT id, number FROM tables WHERE restaurant_id = ? LIMIT 15;", (restaurant_id,))
    tables = cursor.fetchall()
    
    # Group items by category
    categories = {}
    for item in menu_items:
        item_id, name, price, category = item
        if category not in categories:
            categories[category] = []
        categories[category].append({
            'id': item_id,
            'name': name,
            'price': float(price),
            'category': category
        })
    
    conn.close()
    
    return restaurant_id, menu_items, tables, categories

def get_seasonal_factor(date):
    """Get seasonal multiplier based on date"""
    month = date.month
    
    # Seasonal patterns for restaurant business
    if month in [12, 1]:  # Winter holidays
        return 1.4
    elif month in [6, 7, 8]:  # Summer
        return 1.2
    elif month in [3, 4, 5]:  # Spring
        return 1.1
    elif month in [9, 10, 11]:  # Fall
        return 1.0
    else:
        return 1.0

def get_weekday_factor(date):
    """Get weekday multiplier"""
    weekday = date.weekday()
    
    if weekday == 4:  # Friday
        return 1.5
    elif weekday == 5:  # Saturday
        return 1.6
    elif weekday == 6:  # Sunday
        return 1.3
    elif weekday in [0, 1, 2, 3]:  # Monday-Thursday
        return 0.8 + (weekday * 0.1)  # Gradual increase through week
    else:
        return 1.0

def get_time_based_preferences(hour, categories):
    """Get category preferences based on time of day"""
    preferences = {}
    
    if 11 <= hour <= 14:  # Lunch time
        preferences = {
            'Soups & Salads': 2.0,
            'Pasta & Risotto': 1.5,
            'Main Courses': 1.2,
            'Appetizers & Starters': 0.8,
            'Desserts': 0.6,
            'Beverages': 1.8
        }
    elif 17 <= hour <= 22:  # Dinner time
        preferences = {
            'Appetizers & Starters': 1.8,
            'Main Courses': 2.0,
            'Pasta & Risotto': 1.6,
            'Desserts': 1.5,
            'Beverages': 1.9,
            'Soups & Salads': 1.0
        }
    else:  # Off-peak hours
        preferences = {
            'Beverages': 2.5,
            'Desserts': 1.2,
            'Appetizers & Starters': 1.0,
            'Soups & Salads': 0.8,
            'Main Courses': 0.6,
            'Pasta & Risotto': 0.5
        }
    
    # Normalize preferences for categories that exist
    normalized = {}
    for category in categories.keys():
        normalized[category] = preferences.get(category, 1.0)
    
    return normalized

def select_items_for_order(categories, hour, order_size_factor=1.0):
    """Select items for an order based on time and preferences"""
    time_preferences = get_time_based_preferences(hour, categories)
    selected_items = []
    
    # Determine order size (1-6 items, weighted toward 2-3)
    order_size = random.choices(
        [1, 2, 3, 4, 5, 6],
        weights=[10, 30, 35, 15, 8, 2]
    )[0]
    
    # Apply order size factor for busy periods
    order_size = max(1, int(order_size * order_size_factor))
    
    # Select categories based on preferences
    available_categories = list(categories.keys())
    
    for _ in range(order_size):
        # Choose category based on time preferences
        category_weights = [time_preferences.get(cat, 1.0) for cat in available_categories]
        selected_category = random.choices(available_categories, weights=category_weights)[0]
        
        # Choose item from category
        category_items = categories[selected_category]
        selected_item = random.choice(category_items)
        
        # Determine quantity (mostly 1, sometimes 2)
        quantity = random.choices([1, 2], weights=[85, 15])[0]
        
        selected_items.append({
            'item': selected_item,
            'quantity': quantity
        })
    
    return selected_items

def generate_daily_orders(date, restaurant_id, categories, tables):
    """Generate orders for a specific day"""
    orders = []
    
    # Calculate daily factors
    seasonal_factor = get_seasonal_factor(date)
    weekday_factor = get_weekday_factor(date)
    
    # Base number of orders per day (adjusted by factors)
    base_orders = 15
    daily_orders = int(base_orders * seasonal_factor * weekday_factor * random.uniform(0.8, 1.2))
    
    # Generate orders throughout the day
    for _ in range(daily_orders):
        # Generate realistic order times
        if random.random() < 0.4:  # 40% lunch orders
            hour = random.randint(11, 14)
        elif random.random() < 0.8:  # 60% of remaining are dinner
            hour = random.randint(17, 22)
        else:  # 20% off-peak
            hour = random.choice([10, 15, 16, 23])
        
        minute = random.randint(0, 59)
        order_datetime = date.replace(hour=hour, minute=minute, second=0, microsecond=0)
        
        # Select table
        table = random.choice(tables)
        table_id = table[0]
        
        # Determine order size factor based on time
        if 12 <= hour <= 13 or 19 <= hour <= 20:  # Peak hours
            order_size_factor = 1.3
        else:
            order_size_factor = 1.0
        
        # Select items for order
        selected_items = select_items_for_order(categories, hour, order_size_factor)
        
        # Calculate totals
        subtotal = sum(item['item']['price'] * item['quantity'] for item in selected_items)
        tax_amount = subtotal * 0.20  # 20% VAT
        total = subtotal + tax_amount
        
        # Random status (95% completed, 5% cancelled)
        status = random.choices(['completed', 'cancelled'], weights=[95, 5])[0]
        
        if status == 'cancelled':
            total = 0
            subtotal = 0
            tax_amount = 0
        
        order = {
            'id': str(uuid.uuid4()),
            'restaurant_id': restaurant_id,
            'table_id': table_id,
            'status': status,
            'subtotal': subtotal,
            'tax_amount': tax_amount,
            'total': total,
            'payment_status': 'paid' if status == 'completed' else 'pending',
            'created_at': order_datetime,
            'items': selected_items
        }
        
        orders.append(order)
    
    return orders

def create_comprehensive_sales_data(restaurant_id, categories, tables, days=90):
    """Create comprehensive sales data for the specified number of days"""
    conn = sqlite3.connect("restro_manage.db")
    cursor = conn.cursor()
    
    # Clear existing orders
    cursor.execute("DELETE FROM order_items WHERE order_id IN (SELECT id FROM orders WHERE restaurant_id = ?)", (restaurant_id,))
    cursor.execute("DELETE FROM orders WHERE restaurant_id = ?", (restaurant_id,))
    conn.commit()
    
    print(f"🗑️  Cleared existing orders for restaurant")
    
    total_orders = 0
    total_revenue = 0
    category_stats = {cat: {'orders': 0, 'revenue': 0} for cat in categories.keys()}
    
    # Generate data for each day
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days-1)
    
    for day_offset in range(days):
        current_date = start_date + timedelta(days=day_offset)
        daily_orders = generate_daily_orders(current_date, restaurant_id, categories, tables)
        
        for order in daily_orders:
            # Insert order
            cursor.execute("""
                INSERT INTO orders (
                    id, restaurant_id, table_id, status, 
                    subtotal, tax_amount, total, payment_status,
                    created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                order['id'],
                order['restaurant_id'],
                order['table_id'],
                order['status'],
                order['subtotal'],
                order['tax_amount'],
                order['total'],
                order['payment_status'],
                order['created_at'].isoformat(),
                order['created_at'].isoformat()
            ))
            
            # Insert order items
            for item_data in order['items']:
                item = item_data['item']
                quantity = item_data['quantity']
                subtotal = item['price'] * quantity
                
                cursor.execute("""
                    INSERT INTO order_items (
                        id, order_id, menu_item_id, name, quantity, price, subtotal, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    str(uuid.uuid4()),
                    order['id'],
                    item['id'],
                    item['name'],
                    quantity,
                    item['price'],
                    subtotal,
                    order['created_at'].isoformat(),
                    order['created_at'].isoformat()
                ))
                
                # Update category stats
                if order['status'] == 'completed':
                    category_stats[item['category']]['orders'] += quantity
                    category_stats[item['category']]['revenue'] += subtotal
            
            if order['status'] == 'completed':
                total_orders += 1
                total_revenue += order['total']
        
        if (day_offset + 1) % 10 == 0:
            print(f"📅 Generated data for {day_offset + 1}/{days} days...")
    
    conn.commit()
    conn.close()
    
    return total_orders, total_revenue, category_stats

def main():
    """Main function to create comprehensive sales data"""
    print("📊 Creating Comprehensive 90-Day Sales Data")
    print("=" * 60)
    
    # Get restaurant data
    restaurant_id, menu_items, tables, categories = get_restaurant_data()
    
    if not restaurant_id:
        return False
    
    print(f"🏪 Restaurant: {len(menu_items)} menu items across {len(categories)} categories")
    print(f"🪑 Tables: {len(tables)} available")
    
    # Create comprehensive sales data
    print("🔄 Generating 90 days of sales data...")
    total_orders, total_revenue, category_stats = create_comprehensive_sales_data(
        restaurant_id, categories, tables, 90
    )
    
    print(f"\n✅ Generated comprehensive sales data:")
    print(f"   📦 Total Orders: {total_orders}")
    print(f"   💰 Total Revenue: £{total_revenue:.2f}")
    print(f"   📈 Average Order Value: £{total_revenue/total_orders:.2f}")
    
    print(f"\n📊 Category Performance:")
    for category, stats in category_stats.items():
        if stats['orders'] > 0:
            avg_price = stats['revenue'] / stats['orders']
            print(f"   {category}: {stats['orders']} items, £{stats['revenue']:.2f} revenue, £{avg_price:.2f} avg")
    
    print(f"\n🎉 Comprehensive sales data created successfully!")
    
    return True

if __name__ == "__main__":
    main()
