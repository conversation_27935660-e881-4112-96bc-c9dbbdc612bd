"""
Migration script to transfer data from JSON file storage to SQL database.
This script reads the existing data.json file and populates the SQL database.
"""

import json
import asyncio
import os
from datetime import datetime
from typing import Dict, Any, List
import logging

from app.database import init_database, get_db_session_context
from app.repositories.restaurant import RestaurantRepository, RestaurantUserRepository
from app.models.database_models import Restaurant, RestaurantUser

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class JSONToSQLMigrator:
    """Handles migration from JSON file storage to SQL database"""
    
    def __init__(self, json_file_path: str = "data.json"):
        self.json_file_path = json_file_path
        self.restaurant_repo = RestaurantRepository()
        self.user_repo = RestaurantUserRepository()
    
    async def migrate(self):
        """Run the complete migration process"""
        logger.info("Starting JSON to SQL migration...")
        
        try:
            # Initialize database
            await init_database()
            logger.info("Database initialized successfully")
            
            # Load JSON data
            json_data = self.load_json_data()
            if not json_data:
                logger.warning("No JSON data found to migrate")
                return
            
            # Migrate data
            await self.migrate_restaurants(json_data.get("restaurants", []))
            await self.migrate_users(json_data.get("users", []))
            
            # Verify migration
            await self.verify_migration()
            
            logger.info("Migration completed successfully!")
            
        except Exception as e:
            logger.error(f"Migration failed: {e}")
            raise
    
    def load_json_data(self) -> Dict[str, Any]:
        """Load data from JSON file"""
        try:
            if not os.path.exists(self.json_file_path):
                logger.warning(f"JSON file {self.json_file_path} not found")
                return {}
            
            with open(self.json_file_path, 'r') as f:
                data = json.load(f)
            
            logger.info(f"Loaded JSON data with {len(data.get('restaurants', []))} restaurants and {len(data.get('users', []))} users")
            return data
            
        except Exception as e:
            logger.error(f"Error loading JSON data: {e}")
            return {}
    
    async def migrate_restaurants(self, restaurants: List[Dict[str, Any]]):
        """Migrate restaurant data"""
        logger.info(f"Migrating {len(restaurants)} restaurants...")
        
        async with get_db_session_context() as db:
            migrated_count = 0
            
            for restaurant_data in restaurants:
                try:
                    # Check if restaurant already exists
                    existing = await self.restaurant_repo.get_by_id(db, restaurant_data.get("id"))
                    if existing:
                        logger.info(f"Restaurant {restaurant_data.get('name')} already exists, skipping...")
                        continue
                    
                    # Convert JSON data to database format
                    db_data = self.convert_restaurant_data(restaurant_data)
                    
                    # Create restaurant
                    await self.restaurant_repo.create(db, db_data)
                    migrated_count += 1
                    
                    logger.info(f"Migrated restaurant: {restaurant_data.get('name')}")
                    
                except Exception as e:
                    logger.error(f"Error migrating restaurant {restaurant_data.get('name', 'Unknown')}: {e}")
                    continue
            
            await db.commit()
            logger.info(f"Successfully migrated {migrated_count} restaurants")
    
    async def migrate_users(self, users: List[Dict[str, Any]]):
        """Migrate user data"""
        logger.info(f"Migrating {len(users)} users...")
        
        async with get_db_session_context() as db:
            migrated_count = 0
            
            for user_data in users:
                try:
                    # Check if user already exists
                    existing = await self.user_repo.get_by_id(db, user_data.get("id"))
                    if existing:
                        logger.info(f"User {user_data.get('name')} already exists, skipping...")
                        continue
                    
                    # Convert JSON data to database format
                    db_data = self.convert_user_data(user_data)
                    
                    # Verify restaurant exists
                    restaurant = await self.restaurant_repo.get_by_id(db, db_data.get("restaurant_id"))
                    if not restaurant:
                        logger.warning(f"Restaurant {db_data.get('restaurant_id')} not found for user {user_data.get('name')}, skipping...")
                        continue
                    
                    # Create user
                    await self.user_repo.create(db, db_data)
                    migrated_count += 1
                    
                    logger.info(f"Migrated user: {user_data.get('name')}")
                    
                except Exception as e:
                    logger.error(f"Error migrating user {user_data.get('name', 'Unknown')}: {e}")
                    continue
            
            await db.commit()
            logger.info(f"Successfully migrated {migrated_count} users")
    
    def convert_restaurant_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Convert JSON restaurant data to database format"""
        converted = {
            "id": data.get("id"),
            "code": data.get("code"),
            "name": data.get("name"),
            "logo": data.get("logo"),
            "address": data.get("address"),
            "phone": data.get("phone"),
            "email": data.get("email"),
            "vat_rate": data.get("vatRate", 20.0),
            "currency": data.get("currency", "GBP"),
            "is_active": data.get("isActive", True),
            "owner_name": data.get("ownerName"),
            "business_license_number": data.get("businessLicenseNumber"),
            "restaurant_type": data.get("restaurantType", "restaurant"),
            "password": data.get("password"),  # TODO: Hash passwords
            "setup_data": data.get("setupData"),
        }
        
        # Handle timestamps
        if data.get("createdAt"):
            try:
                converted["created_at"] = datetime.fromisoformat(data["createdAt"].replace("Z", "+00:00"))
            except:
                converted["created_at"] = datetime.now()
        
        if data.get("updatedAt"):
            try:
                converted["updated_at"] = datetime.fromisoformat(data["updatedAt"].replace("Z", "+00:00"))
            except:
                converted["updated_at"] = datetime.now()
        
        return converted
    
    def convert_user_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Convert JSON user data to database format"""
        converted = {
            "id": data.get("id"),
            "restaurant_id": data.get("restaurant_id"),
            "name": data.get("name"),
            "email": data.get("email"),
            "phone": data.get("phone"),
            "role": data.get("role"),
            "position": data.get("position"),
            "pin": data.get("pin"),
            "status": data.get("status", "active"),
            "hire_date": data.get("hireDate"),
            "performance": data.get("performance", 100),
            "access_level": data.get("accessLevel", "limited"),
        }
        
        return converted
    
    async def verify_migration(self):
        """Verify that migration was successful"""
        logger.info("Verifying migration...")
        
        async with get_db_session_context() as db:
            # Count restaurants
            restaurants = await self.restaurant_repo.get_all(db)
            logger.info(f"Database contains {len(restaurants)} restaurants")
            
            # Count users
            users = await self.user_repo.get_all_users(db)
            logger.info(f"Database contains {len(users)} users")
            
            # Verify relationships
            for restaurant in restaurants[:3]:  # Check first 3 restaurants
                restaurant_users = await self.user_repo.get_by_restaurant(db, restaurant.id)
                logger.info(f"Restaurant '{restaurant.name}' has {len(restaurant_users)} users")
    
    async def backup_json_data(self):
        """Create a backup of the original JSON file"""
        if os.path.exists(self.json_file_path):
            backup_path = f"{self.json_file_path}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            import shutil
            shutil.copy2(self.json_file_path, backup_path)
            logger.info(f"Original JSON file backed up to: {backup_path}")

async def run_migration():
    """Run the migration process"""
    migrator = JSONToSQLMigrator()

    try:
        # Create backup of original data
        await migrator.backup_json_data()

        # Run migration
        await migrator.migrate()

        logger.info("Migration completed successfully!")

    except Exception as e:
        logger.error(f"Migration failed: {e}")
        raise

if __name__ == "__main__":
    # Run migration
    asyncio.run(run_migration())
