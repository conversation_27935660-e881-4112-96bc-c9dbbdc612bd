<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notification System Test - RestroManage</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .notification-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
        }
        .notification-item {
            padding: 10px;
            margin: 5px 0;
            border: 1px solid #eee;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        .notification-item.unread {
            background-color: #fff3cd;
            border-color: #ffeaa7;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        .stat-card {
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 12px;
            color: #6c757d;
            text-transform: uppercase;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔔 Notification System Test</h1>
        <p>Testing the integrated notification system with backend API connectivity and React Query state management.</p>
        
        <div class="test-section">
            <h3>1. Backend Connectivity Test</h3>
            <button onclick="testBackendHealth()">Test Backend Health</button>
            <button onclick="testNotificationStats()">Test Notification Stats</button>
            <div id="backend-status" class="status info">Click buttons to test backend connectivity...</div>
        </div>

        <div class="test-section">
            <h3>2. Notification CRUD Operations</h3>
            <button onclick="createTestNotification()">Create Test Notification</button>
            <button onclick="fetchNotifications()">Fetch All Notifications</button>
            <button onclick="clearAllNotifications()">Clear All Notifications</button>
            <div id="crud-status" class="status info">Click buttons to test CRUD operations...</div>
        </div>

        <div class="test-section">
            <h3>3. Notification Statistics</h3>
            <div id="stats-container">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="total-count">-</div>
                        <div class="stat-label">Total</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="unread-count">-</div>
                        <div class="stat-label">Unread</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="info-count">-</div>
                        <div class="stat-label">Info</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="high-priority">-</div>
                        <div class="stat-label">High Priority</div>
                    </div>
                </div>
                <button onclick="refreshStats()">Refresh Stats</button>
            </div>
        </div>

        <div class="test-section">
            <h3>4. Restaurant ID Isolation Test</h3>
            <button onclick="testRestaurantIsolation()">Test Restaurant Isolation</button>
            <div id="isolation-status" class="status info">Click button to test restaurant ID isolation...</div>
        </div>

        <div class="test-section">
            <h3>5. Current Notifications</h3>
            <div id="notifications-list" class="notification-list">
                <p>No notifications loaded. Click "Fetch All Notifications" to load.</p>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5001/api';
        const RESTAURANT_ID = '4b41fcac-d638-43f1-b10b-0530d86fa781';

        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }

        async function testBackendHealth() {
            try {
                updateStatus('backend-status', 'Testing backend health...', 'info');
                const response = await fetch(`${API_BASE}/../health`);
                const data = await response.json();
                
                if (response.ok && data.status === 'healthy') {
                    updateStatus('backend-status', `✅ Backend is healthy! Version: ${data.version}`, 'success');
                } else {
                    updateStatus('backend-status', `❌ Backend health check failed: ${data.status}`, 'error');
                }
            } catch (error) {
                updateStatus('backend-status', `❌ Backend connection failed: ${error.message}`, 'error');
            }
        }

        async function testNotificationStats() {
            try {
                updateStatus('backend-status', 'Testing notification stats endpoint...', 'info');
                const response = await fetch(`${API_BASE}/notifications/stats?restaurant_id=${RESTAURANT_ID}`);
                const data = await response.json();
                
                if (response.ok) {
                    updateStatus('backend-status', `✅ Stats endpoint working! Total: ${data.total_count}, Unread: ${data.unread_count}`, 'success');
                    updateStatsDisplay(data);
                } else {
                    updateStatus('backend-status', `❌ Stats endpoint failed: ${response.status}`, 'error');
                }
            } catch (error) {
                updateStatus('backend-status', `❌ Stats request failed: ${error.message}`, 'error');
            }
        }

        async function createTestNotification() {
            try {
                updateStatus('crud-status', 'Creating test notification...', 'info');
                const notificationData = {
                    title: `Test Notification ${new Date().toLocaleTimeString()}`,
                    message: 'This is a test notification created from the test page.',
                    type: 'info',
                    priority: Math.random() > 0.5 ? 'high' : 'medium'
                };

                const response = await fetch(`${API_BASE}/notifications`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Restaurant-ID': RESTAURANT_ID
                    },
                    body: JSON.stringify(notificationData)
                });

                if (response.ok) {
                    const data = await response.json();
                    updateStatus('crud-status', `✅ Notification created successfully! ID: ${data.id}`, 'success');
                    await refreshStats();
                    await fetchNotifications();
                } else {
                    updateStatus('crud-status', `❌ Failed to create notification: ${response.status}`, 'error');
                }
            } catch (error) {
                updateStatus('crud-status', `❌ Create request failed: ${error.message}`, 'error');
            }
        }

        async function fetchNotifications() {
            try {
                updateStatus('crud-status', 'Fetching notifications...', 'info');
                const response = await fetch(`${API_BASE}/notifications?restaurant_id=${RESTAURANT_ID}&limit=20`);
                
                if (response.ok) {
                    const notifications = await response.json();
                    updateStatus('crud-status', `✅ Fetched ${notifications.length} notifications`, 'success');
                    displayNotifications(notifications);
                } else {
                    updateStatus('crud-status', `❌ Failed to fetch notifications: ${response.status}`, 'error');
                }
            } catch (error) {
                updateStatus('crud-status', `❌ Fetch request failed: ${error.message}`, 'error');
            }
        }

        async function clearAllNotifications() {
            try {
                updateStatus('crud-status', 'Clearing all notifications...', 'info');
                // Since we simplified the API, we'll just show this as a placeholder
                updateStatus('crud-status', '⚠️ Clear all functionality not implemented in simplified API', 'info');
            } catch (error) {
                updateStatus('crud-status', `❌ Clear request failed: ${error.message}`, 'error');
            }
        }

        async function refreshStats() {
            await testNotificationStats();
        }

        async function testRestaurantIsolation() {
            try {
                updateStatus('isolation-status', 'Testing restaurant ID isolation...', 'info');
                
                // Test with correct restaurant ID
                const response1 = await fetch(`${API_BASE}/notifications?restaurant_id=${RESTAURANT_ID}`);
                const notifications1 = await response1.json();
                
                // Test with different restaurant ID
                const differentRestaurantId = 'different-restaurant-id-123';
                const response2 = await fetch(`${API_BASE}/notifications?restaurant_id=${differentRestaurantId}`);
                const notifications2 = await response2.json();
                
                if (response1.ok && response2.ok) {
                    updateStatus('isolation-status', 
                        `✅ Isolation working! Restaurant ${RESTAURANT_ID}: ${notifications1.length} notifications, ` +
                        `Restaurant ${differentRestaurantId}: ${notifications2.length} notifications`, 'success');
                } else {
                    updateStatus('isolation-status', '❌ Isolation test failed', 'error');
                }
            } catch (error) {
                updateStatus('isolation-status', `❌ Isolation test failed: ${error.message}`, 'error');
            }
        }

        function updateStatsDisplay(stats) {
            document.getElementById('total-count').textContent = stats.total_count || 0;
            document.getElementById('unread-count').textContent = stats.unread_count || 0;
            document.getElementById('info-count').textContent = stats.by_type?.info || 0;
            document.getElementById('high-priority').textContent = stats.by_priority?.high || 0;
        }

        function displayNotifications(notifications) {
            const container = document.getElementById('notifications-list');
            
            if (notifications.length === 0) {
                container.innerHTML = '<p>No notifications found.</p>';
                return;
            }

            const notificationHtml = notifications.map(notification => `
                <div class="notification-item ${!notification.is_read ? 'unread' : ''}">
                    <strong>${notification.title}</strong>
                    <p>${notification.message}</p>
                    <small>
                        Type: ${notification.type} | Priority: ${notification.priority} | 
                        ${notification.is_read ? 'Read' : 'Unread'} | 
                        Created: ${new Date(notification.created_at).toLocaleString()}
                    </small>
                </div>
            `).join('');

            container.innerHTML = notificationHtml;
        }

        // Auto-run initial tests
        window.addEventListener('load', async () => {
            await testBackendHealth();
            await refreshStats();
            await fetchNotifications();
        });
    </script>
</body>
</html>
