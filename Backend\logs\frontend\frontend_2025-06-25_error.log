{"timestamp": "2025-06-25T06:17:30.863Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750832250048_6v7el9id0", "url": "http://localhost:5175/", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T06:17:30.916Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750832250048_6v7el9id0", "url": "http://localhost:5175/", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:13:27.637Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750835606033_prgipru6n", "url": "http://localhost:5175/", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:333:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1280, "height": 800, "colorDepth": 24}, "viewport": {"width": 944, "height": 735}}
{"timestamp": "2025-06-25T07:13:27.687Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750835606033_prgipru6n", "url": "http://localhost:5175/", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:333:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1280, "height": 800, "colorDepth": 24}, "viewport": {"width": 944, "height": 735}}
{"timestamp": "2025-06-25T07:18:21.333Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:333:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:18:21.389Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:333:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:38:40.404Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/admin", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx?t=1750837117041:333:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:38:51.334Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/admin", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx?t=1750837128087:334:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:39:09.752Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/admin", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx?t=1750837147812:335:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:39:09.777Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/admin", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx?t=1750837147812:335:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:39:24.095Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/admin", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx?t=1750837161426:335:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:40:37.227Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/admin", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx?t=1750837234129:335:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:41:57.374Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/admin", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx?t=1750837315391:335:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:42:12.554Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/admin", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx?t=1750837331120:335:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:42:32.614Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/admin", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx?t=1750837350605:335:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:43:33.677Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/admin", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx?t=1750837411515:335:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:44:53.537Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/admin", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx?t=1750837491896:335:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:46:59.397Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750837618356_g92cwo5dz", "url": "http://localhost:5175/", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx?t=1750837491896:335:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:46:59.461Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750837618356_g92cwo5dz", "url": "http://localhost:5175/", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx?t=1750837491896:335:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:47:28.911Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750837618356_g92cwo5dz", "url": "http://localhost:5175/login", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx?t=1750837491896:335:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:47:29.000Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750837618356_g92cwo5dz", "url": "http://localhost:5175/login", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx?t=1750837491896:335:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T08:08:43.202Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750837618356_g92cwo5dz", "url": "http://localhost:5175/admin/analytics", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx?t=1750838920721:335:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T08:08:44.022Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/admin", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx?t=1750838920721:335:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T08:09:12.010Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750837618356_g92cwo5dz", "url": "http://localhost:5175/admin/analytics", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx?t=1750838950893:335:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T08:09:12.896Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/admin", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx?t=1750838950893:335:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T08:10:14.370Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750837618356_g92cwo5dz", "url": "http://localhost:5175/admin/analytics", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx?t=1750839013242:345:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T08:10:15.642Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/admin", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx?t=1750839013242:345:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T08:10:32.922Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750837618356_g92cwo5dz", "url": "http://localhost:5175/admin/analytics", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx?t=1750839031841:345:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T08:10:33.789Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/admin", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx?t=1750839031841:345:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T08:11:31.037Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750837618356_g92cwo5dz", "url": "http://localhost:5175/admin/analytics", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx?t=1750839089946:345:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T08:11:31.897Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/admin", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx?t=1750839089946:345:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T08:11:48.107Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750837618356_g92cwo5dz", "url": "http://localhost:5175/admin/analytics", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx?t=1750839107063:345:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T08:11:48.782Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/admin", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx?t=1750839107063:345:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T10:48:15.525Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750837618356_g92cwo5dz", "url": "http://localhost:5175/admin/analytics", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx?t=1750839107063:345:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T10:48:15.587Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750837618356_g92cwo5dz", "url": "http://localhost:5175/admin/analytics", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx?t=1750839107063:345:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T12:34:14.173Z", "level": "error", "component": "main", "message": "Error in Global Error Handler", "sessionId": "session_1750852361790_cao4hqcak", "url": "http://localhost:5175/staff-login", "data": {"name": "ReferenceError", "message": "Uncaught ReferenceError: useState is not defined", "stack": "ReferenceError: useState is not defined\n    at NotificationProvider (http://localhost:5175/src/contexts/NotificationContext.tsx?t=1750854853303:26:47)\n    at renderWithHooks (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:11548:26)\n    at updateFunctionComponent (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:14582:28)\n    at beginWork (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:15924:22)\n    at HTMLUnknownElement.callCallback2 (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:3674:22)\n    at Object.invokeGuardedCallbackDev (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:3699:24)\n    at invokeGuardedCallback (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:3733:39)\n    at beginWork$1 (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:19765:15)\n    at performUnitOfWork (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:19198:20)\n    at workLoopSync (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:19137:13)", "context": "Global Error Handler", "filename": "http://localhost:5175/src/contexts/NotificationContext.tsx?t=1750854853303", "lineno": 26, "colno": 47}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T12:34:14.220Z", "level": "error", "component": "main", "message": "Error in Global Error Handler", "sessionId": "session_1750852361790_cao4hqcak", "url": "http://localhost:5175/staff-login", "data": {"name": "ReferenceError", "message": "Uncaught ReferenceError: useState is not defined", "stack": "ReferenceError: useState is not defined\n    at NotificationProvider (http://localhost:5175/src/contexts/NotificationContext.tsx?t=1750854853303:26:47)\n    at renderWithHooks (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:11548:26)\n    at updateFunctionComponent (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:14582:28)\n    at beginWork (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:15924:22)\n    at HTMLUnknownElement.callCallback2 (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:3674:22)\n    at Object.invokeGuardedCallbackDev (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:3699:24)\n    at invokeGuardedCallback (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:3733:39)\n    at beginWork$1 (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:19765:15)\n    at performUnitOfWork (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:19198:20)\n    at workLoopSync (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:19137:13)", "context": "Global Error Handler", "filename": "http://localhost:5175/src/contexts/NotificationContext.tsx?t=1750854853303", "lineno": 26, "colno": 47}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T12:34:47.174Z", "level": "error", "component": "main", "message": "Error in Global Error Handler", "sessionId": "session_1750852361790_cao4hqcak", "url": "http://localhost:5175/staff-login", "data": {"name": "Error", "message": "Uncaught Error: No QueryClient set, use QueryClientProvider to set one", "stack": "Error: No QueryClient set, use QueryClientProvider to set one\n    at useQueryClient (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:2789:11)\n    at useBaseQuery (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:2988:18)\n    at useQuery (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:3057:10)\n    at useNotificationsData (http://localhost:5175/src/hooks/useStoreQueries.ts?t=1750854773101:248:12)\n    at useNotificationStore (http://localhost:5175/src/hooks/useAppStore.ts?t=1750854829829:439:133)\n    at NotificationProvider (http://localhost:5175/src/contexts/NotificationContext.tsx?t=1750854886678:28:31)\n    at renderWithHooks (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:11548:26)\n    at mountIndeterminateComponent (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:14926:21)\n    at beginWork (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:15914:22)\n    at HTMLUnknownElement.callCallback2 (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:3674:22)", "context": "Global Error Handler", "filename": "http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d", "lineno": 2789, "colno": 5}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T12:34:27.233Z", "level": "error", "component": "main", "message": "Error in Unhandled Promise Rejection", "sessionId": "session_1750852361790_cao4hqcak", "url": "http://localhost:5175/staff-login", "data": {"name": "Error", "message": "No QueryClient set, use QueryClientProvider to set one", "stack": "Error: No QueryClient set, use QueryClientProvider to set one\n    at useQueryClient (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:2789:11)\n    at useBaseQuery (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:2988:18)\n    at useQuery (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:3057:10)\n    at useNotificationsData (http://localhost:5175/src/hooks/useStoreQueries.ts?t=1750854773101:248:12)\n    at useNotificationStore (http://localhost:5175/src/hooks/useAppStore.ts?t=1750854829829:439:133)\n    at NotificationProvider (http://localhost:5175/src/contexts/NotificationContext.tsx?t=1750854866338:28:31)\n    at renderWithHooks (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:11548:26)\n    at mountIndeterminateComponent (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:14926:21)\n    at beginWork (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:15914:22)\n    at beginWork$1 (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:19753:22)", "context": "Unhandled Promise Rejection", "promise": {}}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T12:34:14.244Z", "level": "error", "component": "main", "message": "Error in Unhandled Promise Rejection", "sessionId": "session_1750852361790_cao4hqcak", "url": "http://localhost:5175/staff-login", "data": {"name": "ReferenceError", "message": "useState is not defined", "stack": "ReferenceError: useState is not defined\n    at NotificationProvider (http://localhost:5175/src/contexts/NotificationContext.tsx?t=1750854853303:26:47)\n    at renderWithHooks (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:11548:26)\n    at updateFunctionComponent (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:14582:28)\n    at beginWork (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:15924:22)\n    at beginWork$1 (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:19753:22)\n    at performUnitOfWork (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:19198:20)\n    at workLoopSync (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:19137:13)\n    at renderRootSync (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:19116:15)\n    at recoverFromConcurrentError (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:18736:28)\n    at performSyncWorkOnRoot (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:18879:28)", "context": "Unhandled Promise Rejection", "promise": {}}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T12:34:27.213Z", "level": "error", "component": "main", "message": "Error in Global Error Handler", "sessionId": "session_1750852361790_cao4hqcak", "url": "http://localhost:5175/staff-login", "data": {"name": "Error", "message": "Uncaught Error: No QueryClient set, use QueryClientProvider to set one", "stack": "Error: No QueryClient set, use QueryClientProvider to set one\n    at useQueryClient (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:2789:11)\n    at useBaseQuery (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:2988:18)\n    at useQuery (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:3057:10)\n    at useNotificationsData (http://localhost:5175/src/hooks/useStoreQueries.ts?t=1750854773101:248:12)\n    at useNotificationStore (http://localhost:5175/src/hooks/useAppStore.ts?t=1750854829829:439:133)\n    at NotificationProvider (http://localhost:5175/src/contexts/NotificationContext.tsx?t=1750854866338:28:31)\n    at renderWithHooks (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:11548:26)\n    at mountIndeterminateComponent (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:14926:21)\n    at beginWork (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:15914:22)\n    at HTMLUnknownElement.callCallback2 (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:3674:22)", "context": "Global Error Handler", "filename": "http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d", "lineno": 2789, "colno": 5}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T12:34:27.171Z", "level": "error", "component": "main", "message": "Error in Global Error Handler", "sessionId": "session_1750852361790_cao4hqcak", "url": "http://localhost:5175/staff-login", "data": {"name": "Error", "message": "Uncaught Error: No QueryClient set, use QueryClientProvider to set one", "stack": "Error: No QueryClient set, use QueryClientProvider to set one\n    at useQueryClient (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:2789:11)\n    at useBaseQuery (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:2988:18)\n    at useQuery (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:3057:10)\n    at useNotificationsData (http://localhost:5175/src/hooks/useStoreQueries.ts?t=1750854773101:248:12)\n    at useNotificationStore (http://localhost:5175/src/hooks/useAppStore.ts?t=1750854829829:439:133)\n    at NotificationProvider (http://localhost:5175/src/contexts/NotificationContext.tsx?t=1750854866338:28:31)\n    at renderWithHooks (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:11548:26)\n    at mountIndeterminateComponent (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:14926:21)\n    at beginWork (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:15914:22)\n    at HTMLUnknownElement.callCallback2 (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:3674:22)", "context": "Global Error Handler", "filename": "http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d", "lineno": 2789, "colno": 5}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T12:34:47.217Z", "level": "error", "component": "main", "message": "Error in Global Error Handler", "sessionId": "session_1750852361790_cao4hqcak", "url": "http://localhost:5175/staff-login", "data": {"name": "Error", "message": "Uncaught Error: No QueryClient set, use QueryClientProvider to set one", "stack": "Error: No QueryClient set, use QueryClientProvider to set one\n    at useQueryClient (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:2789:11)\n    at useBaseQuery (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:2988:18)\n    at useQuery (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:3057:10)\n    at useNotificationsData (http://localhost:5175/src/hooks/useStoreQueries.ts?t=1750854773101:248:12)\n    at useNotificationStore (http://localhost:5175/src/hooks/useAppStore.ts?t=1750854829829:439:133)\n    at NotificationProvider (http://localhost:5175/src/contexts/NotificationContext.tsx?t=1750854886678:28:31)\n    at renderWithHooks (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:11548:26)\n    at mountIndeterminateComponent (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:14926:21)\n    at beginWork (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:15914:22)\n    at HTMLUnknownElement.callCallback2 (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:3674:22)", "context": "Global Error Handler", "filename": "http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d", "lineno": 2789, "colno": 5}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T12:34:47.239Z", "level": "error", "component": "main", "message": "Error in Unhandled Promise Rejection", "sessionId": "session_1750852361790_cao4hqcak", "url": "http://localhost:5175/staff-login", "data": {"name": "Error", "message": "No QueryClient set, use QueryClientProvider to set one", "stack": "Error: No QueryClient set, use QueryClientProvider to set one\n    at useQueryClient (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:2789:11)\n    at useBaseQuery (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:2988:18)\n    at useQuery (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:3057:10)\n    at useNotificationsData (http://localhost:5175/src/hooks/useStoreQueries.ts?t=1750854773101:248:12)\n    at useNotificationStore (http://localhost:5175/src/hooks/useAppStore.ts?t=1750854829829:439:133)\n    at NotificationProvider (http://localhost:5175/src/contexts/NotificationContext.tsx?t=1750854886678:28:31)\n    at renderWithHooks (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:11548:26)\n    at mountIndeterminateComponent (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:14926:21)\n    at beginWork (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:15914:22)\n    at beginWork$1 (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:19753:22)", "context": "Unhandled Promise Rejection", "promise": {}}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T12:35:00.185Z", "level": "error", "component": "main", "message": "Error in Global Error Handler", "sessionId": "session_1750852361790_cao4hqcak", "url": "http://localhost:5175/staff-login", "data": {"name": "Error", "message": "Uncaught Error: No QueryClient set, use QueryClientProvider to set one", "stack": "Error: No QueryClient set, use QueryClientProvider to set one\n    at useQueryClient (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:2789:11)\n    at useBaseQuery (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:2988:18)\n    at useQuery (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:3057:10)\n    at useNotificationsData (http://localhost:5175/src/hooks/useStoreQueries.ts?t=1750854773101:248:12)\n    at useNotificationStore (http://localhost:5175/src/hooks/useAppStore.ts?t=1750854829829:439:133)\n    at NotificationProvider (http://localhost:5175/src/contexts/NotificationContext.tsx?t=1750854899025:28:31)\n    at renderWithHooks (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:11548:26)\n    at mountIndeterminateComponent (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:14926:21)\n    at beginWork (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:15914:22)\n    at HTMLUnknownElement.callCallback2 (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:3674:22)", "context": "Global Error Handler", "filename": "http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d", "lineno": 2789, "colno": 5}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T12:35:00.240Z", "level": "error", "component": "main", "message": "Error in Global Error Handler", "sessionId": "session_1750852361790_cao4hqcak", "url": "http://localhost:5175/staff-login", "data": {"name": "Error", "message": "Uncaught Error: No QueryClient set, use QueryClientProvider to set one", "stack": "Error: No QueryClient set, use QueryClientProvider to set one\n    at useQueryClient (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:2789:11)\n    at useBaseQuery (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:2988:18)\n    at useQuery (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:3057:10)\n    at useNotificationsData (http://localhost:5175/src/hooks/useStoreQueries.ts?t=1750854773101:248:12)\n    at useNotificationStore (http://localhost:5175/src/hooks/useAppStore.ts?t=1750854829829:439:133)\n    at NotificationProvider (http://localhost:5175/src/contexts/NotificationContext.tsx?t=1750854899025:28:31)\n    at renderWithHooks (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:11548:26)\n    at mountIndeterminateComponent (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:14926:21)\n    at beginWork (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:15914:22)\n    at HTMLUnknownElement.callCallback2 (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:3674:22)", "context": "Global Error Handler", "filename": "http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d", "lineno": 2789, "colno": 5}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T12:35:00.263Z", "level": "error", "component": "main", "message": "Error in Unhandled Promise Rejection", "sessionId": "session_1750852361790_cao4hqcak", "url": "http://localhost:5175/staff-login", "data": {"name": "Error", "message": "No QueryClient set, use QueryClientProvider to set one", "stack": "Error: No QueryClient set, use QueryClientProvider to set one\n    at useQueryClient (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:2789:11)\n    at useBaseQuery (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:2988:18)\n    at useQuery (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:3057:10)\n    at useNotificationsData (http://localhost:5175/src/hooks/useStoreQueries.ts?t=1750854773101:248:12)\n    at useNotificationStore (http://localhost:5175/src/hooks/useAppStore.ts?t=1750854829829:439:133)\n    at NotificationProvider (http://localhost:5175/src/contexts/NotificationContext.tsx?t=1750854899025:28:31)\n    at renderWithHooks (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:11548:26)\n    at mountIndeterminateComponent (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:14926:21)\n    at beginWork (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:15914:22)\n    at beginWork$1 (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:19753:22)", "context": "Unhandled Promise Rejection", "promise": {}}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T12:44:32.358Z", "level": "error", "component": "main", "message": "Error in Global Error Handler", "sessionId": "session_1750855472213_cnth0ej1g", "url": "http://localhost:5175/", "data": {"name": "Error", "message": "Uncaught Error: No QueryClient set, use QueryClientProvider to set one", "stack": "Error: No QueryClient set, use QueryClientProvider to set one\n    at useQueryClient (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:2789:11)\n    at useBaseQuery (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:2988:18)\n    at useQuery (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:3057:10)\n    at useNotificationsData (http://localhost:5175/src/hooks/useStoreQueries.ts:248:12)\n    at useNotificationStore (http://localhost:5175/src/hooks/useAppStore.ts:439:133)\n    at NotificationProvider (http://localhost:5175/src/contexts/NotificationContext.tsx:28:31)\n    at renderWithHooks (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:11548:26)\n    at mountIndeterminateComponent (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:14926:21)\n    at beginWork (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:15914:22)\n    at HTMLUnknownElement.callCallback2 (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:3674:22)", "context": "Global Error Handler", "filename": "http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d", "lineno": 2789, "colno": 5}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T12:44:32.406Z", "level": "error", "component": "main", "message": "Error in Global Error Handler", "sessionId": "session_1750855472213_cnth0ej1g", "url": "http://localhost:5175/", "data": {"name": "Error", "message": "Uncaught Error: No QueryClient set, use QueryClientProvider to set one", "stack": "Error: No QueryClient set, use QueryClientProvider to set one\n    at useQueryClient (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:2789:11)\n    at useBaseQuery (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:2988:18)\n    at useQuery (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:3057:10)\n    at useNotificationsData (http://localhost:5175/src/hooks/useStoreQueries.ts:248:12)\n    at useNotificationStore (http://localhost:5175/src/hooks/useAppStore.ts:439:133)\n    at NotificationProvider (http://localhost:5175/src/contexts/NotificationContext.tsx:28:31)\n    at renderWithHooks (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:11548:26)\n    at mountIndeterminateComponent (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:14926:21)\n    at beginWork (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:15914:22)\n    at HTMLUnknownElement.callCallback2 (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:3674:22)", "context": "Global Error Handler", "filename": "http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d", "lineno": 2789, "colno": 5}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T12:44:32.421Z", "level": "error", "component": "main", "message": "Error in Global Error Handler", "sessionId": "session_1750855472213_cnth0ej1g", "url": "http://localhost:5175/", "data": {"name": "Error", "message": "Uncaught Error: No QueryClient set, use QueryClientProvider to set one", "stack": "Error: No QueryClient set, use QueryClientProvider to set one\n    at useQueryClient (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:2789:11)\n    at useBaseQuery (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:2988:18)\n    at useQuery (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:3057:10)\n    at useNotificationsData (http://localhost:5175/src/hooks/useStoreQueries.ts:248:12)\n    at useNotificationStore (http://localhost:5175/src/hooks/useAppStore.ts:439:133)\n    at NotificationProvider (http://localhost:5175/src/contexts/NotificationContext.tsx:28:31)\n    at renderWithHooks (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:11548:26)\n    at mountIndeterminateComponent (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:14926:21)\n    at beginWork (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:15914:22)\n    at beginWork$1 (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:19753:22)", "context": "Global Error Handler", "filename": "http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d", "lineno": 19413, "colno": 13}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T14:36:56.320Z", "level": "error", "component": "main", "message": "Error in Global Error Handler", "sessionId": "session_1750862216181_tldbnakeb", "url": "http://localhost:5175/", "data": {"name": "Error", "message": "Uncaught Error: No QueryClient set, use QueryClientProvider to set one", "stack": "Error: No QueryClient set, use QueryClientProvider to set one\n    at useQueryClient (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:2789:11)\n    at useBaseQuery (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:2988:18)\n    at useQuery (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:3057:10)\n    at useNotificationsData (http://localhost:5175/src/hooks/useStoreQueries.ts:248:12)\n    at useNotificationStore (http://localhost:5175/src/hooks/useAppStore.ts:439:133)\n    at NotificationProvider (http://localhost:5175/src/contexts/NotificationContext.tsx:28:31)\n    at renderWithHooks (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:11548:26)\n    at mountIndeterminateComponent (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:14926:21)\n    at beginWork (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:15914:22)\n    at HTMLUnknownElement.callCallback2 (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:3674:22)", "context": "Global Error Handler", "filename": "http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d", "lineno": 2789, "colno": 5}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T14:36:56.359Z", "level": "error", "component": "main", "message": "Error in Global Error Handler", "sessionId": "session_1750862216181_tldbnakeb", "url": "http://localhost:5175/", "data": {"name": "Error", "message": "Uncaught Error: No QueryClient set, use QueryClientProvider to set one", "stack": "Error: No QueryClient set, use QueryClientProvider to set one\n    at useQueryClient (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:2789:11)\n    at useBaseQuery (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:2988:18)\n    at useQuery (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:3057:10)\n    at useNotificationsData (http://localhost:5175/src/hooks/useStoreQueries.ts:248:12)\n    at useNotificationStore (http://localhost:5175/src/hooks/useAppStore.ts:439:133)\n    at NotificationProvider (http://localhost:5175/src/contexts/NotificationContext.tsx:28:31)\n    at renderWithHooks (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:11548:26)\n    at mountIndeterminateComponent (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:14926:21)\n    at beginWork (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:15914:22)\n    at HTMLUnknownElement.callCallback2 (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:3674:22)", "context": "Global Error Handler", "filename": "http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d", "lineno": 2789, "colno": 5}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T14:36:56.374Z", "level": "error", "component": "main", "message": "Error in Global Error Handler", "sessionId": "session_1750862216181_tldbnakeb", "url": "http://localhost:5175/", "data": {"name": "Error", "message": "Uncaught Error: No QueryClient set, use QueryClientProvider to set one", "stack": "Error: No QueryClient set, use QueryClientProvider to set one\n    at useQueryClient (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:2789:11)\n    at useBaseQuery (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:2988:18)\n    at useQuery (http://localhost:5175/node_modules/.vite/deps/@tanstack_react-query.js?v=cec7a99d:3057:10)\n    at useNotificationsData (http://localhost:5175/src/hooks/useStoreQueries.ts:248:12)\n    at useNotificationStore (http://localhost:5175/src/hooks/useAppStore.ts:439:133)\n    at NotificationProvider (http://localhost:5175/src/contexts/NotificationContext.tsx:28:31)\n    at renderWithHooks (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:11548:26)\n    at mountIndeterminateComponent (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:14926:21)\n    at beginWork (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:15914:22)\n    at beginWork$1 (http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d:19753:22)", "context": "Global Error Handler", "filename": "http://localhost:5175/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=cec7a99d", "lineno": 19413, "colno": 13}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T14:38:21.072Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750862216181_tldbnakeb", "url": "http://localhost:5175/", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:345:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1280, "height": 800, "colorDepth": 24}, "viewport": {"width": 944, "height": 735}}
{"timestamp": "2025-06-25T14:38:21.528Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750862216181_tldbnakeb", "url": "http://localhost:5175/", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:345:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1280, "height": 800, "colorDepth": 24}, "viewport": {"width": 944, "height": 735}}
{"timestamp": "2025-06-25T14:49:44.210Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750862983429_qf4726xrz", "url": "http://localhost:5176/", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5176/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5176/src/contexts/AuthContext.tsx:345:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T14:49:44.244Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750862983429_qf4726xrz", "url": "http://localhost:5176/", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5176/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5176/src/contexts/AuthContext.tsx:345:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T15:10:24.740Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750864223018_opzlrystu", "url": "http://localhost:5176/", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5176/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5176/src/contexts/AuthContext.tsx:345:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T15:10:24.801Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750864223018_opzlrystu", "url": "http://localhost:5176/", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5176/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5176/src/contexts/AuthContext.tsx:345:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T15:26:33.060Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750865191955_0u3shtghq", "url": "http://localhost:5175/", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:345:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1280, "height": 800, "colorDepth": 24}, "viewport": {"width": 774, "height": 728}}
