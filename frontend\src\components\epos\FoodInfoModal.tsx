/**
 * Food Information Modal Component
 * Displays detailed food information with proper scrollable content
 */

import React from "react";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { AllergenDisplay } from "@/components/epos/AllergenDisplay";
import { cn } from "@/lib/utils";
import { Clock, Users, Flame, Info } from "lucide-react";

interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  image?: string;
  allergens?: string[];
  ingredients?: string[];
  nutritionalInfo?: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber?: number;
    sugar?: number;
    sodium?: number;
  };
  preparationTime?: number;
  servingSize?: string;
  spiceLevel?: "mild" | "medium" | "hot" | "very-hot";
  dietaryTags?: string[];
  chefNotes?: string;
}

interface FoodInfoModalProps {
  isOpen: boolean;
  onClose: () => void;
  item: MenuItem | null;
  className?: string;
}

const FoodInfoModal: React.FC<FoodInfoModalProps> = ({
  isOpen,
  onClose,
  item,
  className
}) => {
  if (!item) return null;

  const spiceLevelColors = {
    mild: "bg-green-100 text-green-800",
    medium: "bg-yellow-100 text-yellow-800", 
    hot: "bg-orange-100 text-orange-800",
    "very-hot": "bg-red-100 text-red-800"
  };

  const spiceLevelIcons = {
    mild: "🌶️",
    medium: "🌶️🌶️",
    hot: "🌶️🌶️🌶️",
    "very-hot": "🌶️🌶️🌶️🌶️"
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className={cn("sm:max-w-[600px] max-h-[90vh] flex flex-col", className)}>
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="text-xl font-bold">{item.name}</DialogTitle>
        </DialogHeader>

        <ScrollArea className="flex-1 pr-4">
          <div className="space-y-6">
            {/* Image and Basic Info */}
            <div className="space-y-4">
              {item.image && (
                <div className="w-full h-48 rounded-lg overflow-hidden bg-gray-100">
                  <img
                    src={item.image}
                    alt={item.name}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.currentTarget.src = `https://placehold.co/400x200/3b82f6/ffffff?text=${item.category.charAt(0).toUpperCase()}`;
                    }}
                  />
                </div>
              )}

              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <p className="text-muted-foreground text-sm leading-relaxed">
                    {item.description}
                  </p>
                </div>
                <div className="ml-4 text-right">
                  <div className="text-2xl font-bold text-primary">£{item.price.toFixed(2)}</div>
                  {item.servingSize && (
                    <div className="text-sm text-muted-foreground">{item.servingSize}</div>
                  )}
                </div>
              </div>
            </div>

            {/* Tags and Quick Info */}
            <div className="flex flex-wrap gap-2">
              <Badge variant="secondary" className="capitalize">
                {item.category}
              </Badge>
              
              {item.preparationTime && (
                <Badge variant="outline" className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {item.preparationTime} min
                </Badge>
              )}

              {item.spiceLevel && (
                <Badge 
                  variant="outline" 
                  className={cn("flex items-center gap-1", spiceLevelColors[item.spiceLevel])}
                >
                  <span>{spiceLevelIcons[item.spiceLevel]}</span>
                  {item.spiceLevel.replace("-", " ")}
                </Badge>
              )}

              {item.dietaryTags?.map((tag) => (
                <Badge key={tag} variant="outline" className="capitalize">
                  {tag}
                </Badge>
              ))}
            </div>

            <Separator />

            {/* Allergen Information */}
            <div className="space-y-3">
              <h3 className="font-semibold flex items-center gap-2">
                <Info className="h-4 w-4" />
                Allergen Information
              </h3>
              <AllergenDisplay 
                allergens={item.allergens} 
                alwaysShow={true}
                variant="outline"
                size="sm"
              />
            </div>

            {/* Ingredients */}
            {item.ingredients && item.ingredients.length > 0 && (
              <>
                <Separator />
                <div className="space-y-3">
                  <h3 className="font-semibold">Ingredients</h3>
                  <div className="text-sm text-muted-foreground leading-relaxed">
                    {item.ingredients.join(", ")}
                  </div>
                </div>
              </>
            )}

            {/* Nutritional Information */}
            {item.nutritionalInfo && (
              <>
                <Separator />
                <div className="space-y-3">
                  <h3 className="font-semibold flex items-center gap-2">
                    <Flame className="h-4 w-4" />
                    Nutritional Information
                  </h3>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span>Calories:</span>
                        <span className="font-medium">{item.nutritionalInfo.calories} kcal</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Protein:</span>
                        <span className="font-medium">{item.nutritionalInfo.protein}g</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Carbohydrates:</span>
                        <span className="font-medium">{item.nutritionalInfo.carbs}g</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Fat:</span>
                        <span className="font-medium">{item.nutritionalInfo.fat}g</span>
                      </div>
                    </div>
                    <div className="space-y-2">
                      {item.nutritionalInfo.fiber && (
                        <div className="flex justify-between">
                          <span>Fiber:</span>
                          <span className="font-medium">{item.nutritionalInfo.fiber}g</span>
                        </div>
                      )}
                      {item.nutritionalInfo.sugar && (
                        <div className="flex justify-between">
                          <span>Sugar:</span>
                          <span className="font-medium">{item.nutritionalInfo.sugar}g</span>
                        </div>
                      )}
                      {item.nutritionalInfo.sodium && (
                        <div className="flex justify-between">
                          <span>Sodium:</span>
                          <span className="font-medium">{item.nutritionalInfo.sodium}mg</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </>
            )}

            {/* Chef's Notes */}
            {item.chefNotes && (
              <>
                <Separator />
                <div className="space-y-3">
                  <h3 className="font-semibold">Chef's Notes</h3>
                  <div className="text-sm text-muted-foreground leading-relaxed italic bg-muted/50 p-3 rounded-lg">
                    "{item.chefNotes}"
                  </div>
                </div>
              </>
            )}
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
};

export default FoodInfoModal;
