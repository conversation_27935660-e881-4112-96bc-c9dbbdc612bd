import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { 
  Building2, 
  Table, 
  Utensils, 
  Users, 
  Settings,
  Plus,
  Trash2,
  Clock,
  CreditCard
} from 'lucide-react';

interface OnboardingData {
  basicInfo: {
    name: string;
    address: string;
    phone: string;
    email: string;
    website: string;
    description: string;
  };
  tables: {
    tableCount: number;
    tables: Array<{ number: number; capacity: number; }>;
  };
  menu: {
    categories: Array<{ name: string; description: string; }>;
    sampleItems: boolean;
  };
  staff: {
    adminUser: {
      name: string;
      email: string;
      pin: string;
      role: string;
    };
  };
  settings: {
    currency: string;
    timezone: string;
    taxRate: number;
    operatingHours: {
      [key: string]: { open: string; close: string; closed: boolean; };
    };
    paymentMethods: string[];
  };
}

interface StepProps {
  data: OnboardingData;
  updateData: (step: string, data: any) => void;
}

export const BasicInfoStep: React.FC<StepProps> = ({ data, updateData }) => {
  const handleChange = (field: string, value: string) => {
    updateData('basicInfo', { [field]: value });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Building2 className="h-5 w-5" />
          Restaurant Information
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="name">Restaurant Name *</Label>
            <Input
              id="name"
              value={data.basicInfo.name}
              onChange={(e) => handleChange('name', e.target.value)}
              placeholder="Enter restaurant name"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="website">Website</Label>
            <Input
              id="website"
              value={data.basicInfo.website}
              onChange={(e) => handleChange('website', e.target.value)}
              placeholder="https://yourrestaurant.com"
            />
          </div>
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="address">Address *</Label>
          <Textarea
            id="address"
            value={data.basicInfo.address}
            onChange={(e) => handleChange('address', e.target.value)}
            placeholder="Enter full address"
            rows={3}
          />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="phone">Phone</Label>
            <Input
              id="phone"
              value={data.basicInfo.phone}
              onChange={(e) => handleChange('phone', e.target.value)}
              placeholder="+44 ************"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={data.basicInfo.email}
              onChange={(e) => handleChange('email', e.target.value)}
              placeholder="<EMAIL>"
            />
          </div>
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            value={data.basicInfo.description}
            onChange={(e) => handleChange('description', e.target.value)}
            placeholder="Brief description of your restaurant"
            rows={3}
          />
        </div>
      </CardContent>
    </Card>
  );
};

export const TableSetupStep: React.FC<StepProps> = ({ data, updateData }) => {
  const handleTableCountChange = (count: number) => {
    updateData('tables', { tableCount: count });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Table className="h-5 w-5" />
          Table Configuration
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="tableCount">Number of Tables</Label>
          <div className="flex items-center gap-4">
            <Input
              id="tableCount"
              type="number"
              min="1"
              max="50"
              value={data.tables.tableCount}
              onChange={(e) => handleTableCountChange(parseInt(e.target.value) || 1)}
              className="w-24"
            />
            <span className="text-sm text-gray-600">
              Tables will be numbered 1 to {data.tables.tableCount}
            </span>
          </div>
        </div>
        
        <div className="p-4 bg-blue-50 rounded-lg">
          <h4 className="font-medium mb-2">Table Preview</h4>
          <div className="grid grid-cols-4 gap-2">
            {Array.from({ length: Math.min(data.tables.tableCount, 12) }, (_, i) => (
              <div key={i} className="bg-white p-2 rounded border text-center text-sm">
                Table {i + 1}
                <br />
                <span className="text-xs text-gray-500">
                  {i < 4 ? '2 seats' : i < 8 ? '4 seats' : '6 seats'}
                </span>
              </div>
            ))}
            {data.tables.tableCount > 12 && (
              <div className="bg-gray-100 p-2 rounded border text-center text-sm text-gray-600">
                +{data.tables.tableCount - 12} more
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export const MenuSetupStep: React.FC<StepProps> = ({ data, updateData }) => {
  const addCategory = () => {
    const newCategories = [...data.menu.categories, { name: '', description: '' }];
    updateData('menu', { categories: newCategories });
  };

  const updateCategory = (index: number, field: string, value: string) => {
    const newCategories = [...data.menu.categories];
    newCategories[index] = { ...newCategories[index], [field]: value };
    updateData('menu', { categories: newCategories });
  };

  const removeCategory = (index: number) => {
    const newCategories = data.menu.categories.filter((_, i) => i !== index);
    updateData('menu', { categories: newCategories });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Utensils className="h-5 w-5" />
          Menu Categories
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-4">
          {data.menu.categories.map((category, index) => (
            <div key={index} className="border rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium">Category {index + 1}</h4>
                {data.menu.categories.length > 1 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeCategory(index)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                )}
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Category Name</Label>
                  <Input
                    value={category.name}
                    onChange={(e) => updateCategory(index, 'name', e.target.value)}
                    placeholder="e.g., Appetizers"
                  />
                </div>
                <div className="space-y-2">
                  <Label>Description</Label>
                  <Input
                    value={category.description}
                    onChange={(e) => updateCategory(index, 'description', e.target.value)}
                    placeholder="e.g., Start your meal right"
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <Button onClick={addCategory} variant="outline" className="w-full">
          <Plus className="h-4 w-4 mr-2" />
          Add Category
        </Button>
        
        <div className="flex items-center space-x-2 p-4 bg-gray-50 rounded-lg">
          <Switch
            id="sampleItems"
            checked={data.menu.sampleItems}
            onCheckedChange={(checked) => updateData('menu', { sampleItems: checked })}
          />
          <Label htmlFor="sampleItems" className="text-sm">
            Include sample menu items for each category
          </Label>
        </div>
      </CardContent>
    </Card>
  );
};

export const StaffSetupStep: React.FC<StepProps> = ({ data, updateData }) => {
  const handleChange = (field: string, value: string) => {
    updateData('staff', { 
      adminUser: { ...data.staff.adminUser, [field]: value }
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          Admin User Setup
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="adminName">Admin Name *</Label>
            <Input
              id="adminName"
              value={data.staff.adminUser.name}
              onChange={(e) => handleChange('name', e.target.value)}
              placeholder="Enter admin name"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="adminEmail">Admin Email</Label>
            <Input
              id="adminEmail"
              type="email"
              value={data.staff.adminUser.email}
              onChange={(e) => handleChange('email', e.target.value)}
              placeholder="<EMAIL>"
            />
          </div>
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="adminPin">Admin PIN *</Label>
          <Input
            id="adminPin"
            type="password"
            value={data.staff.adminUser.pin}
            onChange={(e) => handleChange('pin', e.target.value)}
            placeholder="Enter 4-6 digit PIN"
            maxLength={6}
          />
          <p className="text-xs text-gray-600">
            This PIN will be used to access admin functions in the system
          </p>
        </div>
        
        <div className="p-4 bg-blue-50 rounded-lg">
          <h4 className="font-medium mb-2">Admin Permissions</h4>
          <div className="space-y-1 text-sm text-gray-600">
            <div>• Full access to all system features</div>
            <div>• Manage staff and permissions</div>
            <div>• View reports and analytics</div>
            <div>• Configure system settings</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export const SettingsStep: React.FC<StepProps> = ({ data, updateData }) => {
  const handleOperatingHoursChange = (day: string, field: string, value: string | boolean) => {
    const newHours = {
      ...data.settings.operatingHours,
      [day]: { ...data.settings.operatingHours[day], [field]: value }
    };
    updateData('settings', { operatingHours: newHours });
  };

  const handlePaymentMethodToggle = (method: string, checked: boolean) => {
    const newMethods = checked
      ? [...data.settings.paymentMethods, method]
      : data.settings.paymentMethods.filter(m => m !== method);
    updateData('settings', { paymentMethods: newMethods });
  };

  const paymentOptions = [
    { id: 'cash', label: 'Cash' },
    { id: 'card', label: 'Credit/Debit Card' },
    { id: 'contactless', label: 'Contactless' },
    { id: 'mobile', label: 'Mobile Payment' }
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          System Settings
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Basic Settings */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label htmlFor="currency">Currency</Label>
            <Select
              value={data.settings.currency}
              onValueChange={(value) => updateData('settings', { currency: value })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="GBP">GBP (£)</SelectItem>
                <SelectItem value="USD">USD ($)</SelectItem>
                <SelectItem value="EUR">EUR (€)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="timezone">Timezone</Label>
            <Select
              value={data.settings.timezone}
              onValueChange={(value) => updateData('settings', { timezone: value })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Europe/London">London (GMT)</SelectItem>
                <SelectItem value="America/New_York">New York (EST)</SelectItem>
                <SelectItem value="Europe/Paris">Paris (CET)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="taxRate">Tax Rate (%)</Label>
            <Input
              id="taxRate"
              type="number"
              min="0"
              max="100"
              step="0.1"
              value={data.settings.taxRate}
              onChange={(e) => updateData('settings', { taxRate: parseFloat(e.target.value) || 0 })}
            />
          </div>
        </div>

        {/* Operating Hours */}
        <div>
          <h4 className="font-medium mb-4 flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Operating Hours
          </h4>
          <div className="space-y-3">
            {Object.entries(data.settings.operatingHours).map(([day, hours]) => (
              <div key={day} className="flex items-center justify-between">
                <div className="w-1/4">
                  <span className="capitalize font-medium">{day}</span>
                </div>
                <div className="flex items-center gap-2 w-3/4">
                  <Switch
                    checked={!hours.closed}
                    onCheckedChange={(checked) =>
                      handleOperatingHoursChange(day, 'closed', !checked)
                    }
                  />
                  {!hours.closed ? (
                    <div className="flex items-center gap-2">
                      <Input
                        type="time"
                        value={hours.open}
                        onChange={(e) =>
                          handleOperatingHoursChange(day, 'open', e.target.value)
                        }
                        className="w-24"
                      />
                      <span>to</span>
                      <Input
                        type="time"
                        value={hours.close}
                        onChange={(e) =>
                          handleOperatingHoursChange(day, 'close', e.target.value)
                        }
                        className="w-24"
                      />
                    </div>
                  ) : (
                    <span className="text-gray-500">Closed</span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Payment Methods */}
        <div>
          <h4 className="font-medium mb-4 flex items-center gap-2">
            <CreditCard className="h-4 w-4" />
            Accepted Payment Methods
          </h4>
          <div className="grid grid-cols-2 gap-4">
            {paymentOptions.map((option) => (
              <div key={option.id} className="flex items-center space-x-2">
                <Switch
                  id={option.id}
                  checked={data.settings.paymentMethods.includes(option.id)}
                  onCheckedChange={(checked) =>
                    handlePaymentMethodToggle(option.id, checked)
                  }
                />
                <Label htmlFor={option.id}>{option.label}</Label>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
