/**
 * Dashboard state slice for RestroManage
 * Manages restaurant data loading states, user authentication persistence, and real-time metrics
 */

import { StateCreator } from 'zustand';
import { 
  DashboardState, 
  DashboardMetrics, 
  Order, 
  Alert, 
  QuickStat,
  LoadingState 
} from '@/types/store';
import { analyticsApi } from '@/services/api';
import { orderApi } from '@/services/apiService';
import logger from '@/utils/logger';

export interface DashboardSlice extends DashboardState {
  // State
  dashboardMetrics: DashboardMetrics;
  recentOrders: Order[];
  dashboardAlerts: Alert[];
  quickStats: QuickStat[];
  isDashboardLoading: boolean;
  dashboardError: string | null;
  lastDashboardUpdate: Date | null;
  isRealTimeEnabled: boolean;
  refreshInterval: number;

  // Actions
  initializeDashboard: (restaurantId: string) => Promise<void>;
  refreshDashboardMetrics: () => Promise<void>;
  updateMetrics: (metrics: Partial<DashboardMetrics>) => void;
  addRecentOrder: (order: Order) => void;
  updateRecentOrder: (orderId: string, updates: Partial<Order>) => void;
  addDashboardAlert: (alert: Omit<Alert, 'id' | 'timestamp'>) => void;
  removeDashboardAlert: (alertId: string) => void;
  clearDashboardAlerts: () => void;
  updateQuickStats: (stats: QuickStat[]) => void;
  toggleRealTime: () => void;
  setRefreshInterval: (interval: number) => void;
  syncDashboard: () => Promise<void>;
  resetDashboard: () => void;
  clearDashboardData: (restaurantId: string) => void;
}

const initialDashboardMetrics: DashboardMetrics = {
  todayRevenue: 0,
  todayCustomers: 0,
  inventoryItems: 0,
  staffOnDuty: 0,
  ordersToday: 0,
  averageOrderValue: 0,
  customerSatisfaction: 0,
  tableOccupancy: 0,
};

const initialQuickStats: QuickStat[] = [
  {
    id: 'revenue',
    label: 'Today\'s Revenue',
    value: '£0.00',
    change: 0,
    trend: 'stable',
    icon: 'DollarSign',
  },
  {
    id: 'orders',
    label: 'Orders Today',
    value: 0,
    change: 0,
    trend: 'stable',
    icon: 'ShoppingBag',
  },
  {
    id: 'customers',
    label: 'Customers Served',
    value: 0,
    change: 0,
    trend: 'stable',
    icon: 'Users',
  },
  {
    id: 'staff',
    label: 'Staff On Duty',
    value: 0,
    change: 0,
    trend: 'stable',
    icon: 'UserCheck',
  },
];

export const createDashboardSlice: StateCreator<
  DashboardSlice,
  [],
  [],
  DashboardSlice
> = (set, get) => ({
  // Initial state
  dashboardMetrics: initialDashboardMetrics,
  recentOrders: [],
  dashboardAlerts: [],
  quickStats: initialQuickStats,
  isDashboardLoading: false,
  dashboardError: null,
  lastDashboardUpdate: null,
  isRealTimeEnabled: true,
  refreshInterval: 30000, // 30 seconds

  // Actions
  initializeDashboard: async (restaurantId: string) => {
    try {
      set((state) => {
        state.isDashboardLoading = true;
        state.dashboardError = null;
      });

      logger.info('Initializing dashboard', 'DashboardSlice', { restaurantId });

      // Fetch dashboard data from API
      const [dashboardData, recentOrdersData] = await Promise.allSettled([
        analyticsApi.getDashboardData(restaurantId),
        orderApi.getOrders(restaurantId, undefined, undefined),
      ]);

      set((state) => {
        // Update metrics from dashboard data
        if (dashboardData.status === 'fulfilled') {
          const data = dashboardData.value;
          state.dashboardMetrics = {
            todayRevenue: data.todayRevenue || 0,
            todayCustomers: data.todayCustomers || 0,
            inventoryItems: data.inventoryItems || 0,
            staffOnDuty: data.staffOnDuty || 0,
            ordersToday: data.ordersToday || 0,
            averageOrderValue: data.averageOrderValue || 0,
            customerSatisfaction: data.customerSatisfaction || 0,
            tableOccupancy: data.tableOccupancy || 0,
          };

          // Update quick stats
          state.quickStats = [
            {
              ...state.quickStats[0],
              value: `£${data.todayRevenue?.toFixed(2) || '0.00'}`,
              change: data.revenueChange || 0,
              trend: (data.revenueChange || 0) > 0 ? 'up' : (data.revenueChange || 0) < 0 ? 'down' : 'stable',
            },
            {
              ...state.quickStats[1],
              value: data.ordersToday || 0,
              change: data.ordersChange || 0,
              trend: (data.ordersChange || 0) > 0 ? 'up' : (data.ordersChange || 0) < 0 ? 'down' : 'stable',
            },
            {
              ...state.quickStats[2],
              value: data.todayCustomers || 0,
              change: data.customersChange || 0,
              trend: (data.customersChange || 0) > 0 ? 'up' : (data.customersChange || 0) < 0 ? 'down' : 'stable',
            },
            {
              ...state.quickStats[3],
              value: data.staffOnDuty || 0,
              change: 0,
              trend: 'stable',
            },
          ];
        }

        // Update recent orders
        if (recentOrdersData.status === 'fulfilled') {
          state.recentOrders = recentOrdersData.value.slice(0, 10); // Keep only 10 most recent
        }

        state.isDashboardLoading = false;
        state.lastDashboardUpdate = new Date();
      });

      logger.info('Dashboard initialized successfully', 'DashboardSlice', { restaurantId });
    } catch (error) {
      set((state) => {
        state.isDashboardLoading = false;
        state.dashboardError = error instanceof Error ? error.message : 'Failed to initialize dashboard';
      });

      logger.error('Failed to initialize dashboard', 'DashboardSlice', { error, restaurantId });
      throw error;
    }
  },

  refreshDashboardMetrics: async () => {
    const restaurantId = get().currentRestaurantId;
    if (!restaurantId) {
      logger.warn('Cannot refresh metrics without restaurant ID', 'DashboardSlice');
      return;
    }

    try {
      set((state) => {
        state.isDashboardLoading = true;
        state.dashboardError = null;
      });

      const dashboardData = await analyticsApi.getDashboardData(restaurantId);

      set((state) => {
        state.dashboardMetrics = {
          todayRevenue: dashboardData.todayRevenue || 0,
          todayCustomers: dashboardData.todayCustomers || 0,
          inventoryItems: dashboardData.inventoryItems || 0,
          staffOnDuty: dashboardData.staffOnDuty || 0,
          ordersToday: dashboardData.ordersToday || 0,
          averageOrderValue: dashboardData.averageOrderValue || 0,
          customerSatisfaction: dashboardData.customerSatisfaction || 0,
          tableOccupancy: dashboardData.tableOccupancy || 0,
        };

        state.isDashboardLoading = false;
        state.lastDashboardUpdate = new Date();
      });

      logger.info('Dashboard metrics refreshed', 'DashboardSlice', { restaurantId });
    } catch (error) {
      set((state) => {
        state.isDashboardLoading = false;
        state.dashboardError = error instanceof Error ? error.message : 'Failed to refresh metrics';
      });

      logger.error('Failed to refresh dashboard metrics', 'DashboardSlice', { error, restaurantId });
    }
  },

  updateMetrics: (metrics: Partial<DashboardMetrics>) => {
    set((state) => {
      state.dashboardMetrics = { ...state.dashboardMetrics, ...metrics };
      state.lastDashboardUpdate = new Date();
    });
  },

  addRecentOrder: (order: Order) => {
    set((state) => {
      state.recentOrders = [order, ...state.recentOrders.slice(0, 9)]; // Keep only 10 orders
    });
  },

  updateRecentOrder: (orderId: string, updates: Partial<Order>) => {
    set((state) => {
      const index = state.recentOrders.findIndex(order => order.id === orderId);
      if (index !== -1) {
        state.recentOrders[index] = { ...state.recentOrders[index], ...updates };
      }
    });
  },

  addDashboardAlert: (alert: Omit<Alert, 'id' | 'timestamp'>) => {
    set((state) => {
      const newAlert: Alert = {
        ...alert,
        id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date(),
        isRead: false,
      };
      state.dashboardAlerts = [newAlert, ...state.dashboardAlerts];
    });
  },

  removeDashboardAlert: (alertId: string) => {
    set((state) => {
      state.dashboardAlerts = state.dashboardAlerts.filter(alert => alert.id !== alertId);
    });
  },

  clearDashboardAlerts: () => {
    set((state) => {
      state.dashboardAlerts = [];
    });
  },

  updateQuickStats: (stats: QuickStat[]) => {
    set((state) => {
      state.quickStats = stats;
    });
  },

  toggleRealTime: () => {
    set((state) => {
      state.isRealTimeEnabled = !state.isRealTimeEnabled;
    });
  },

  setRefreshInterval: (interval: number) => {
    set((state) => {
      state.refreshInterval = interval;
    });
  },

  syncDashboard: async () => {
    const restaurantId = get().currentRestaurantId;
    if (!restaurantId) return;

    await get().refreshDashboardMetrics();
  },

  resetDashboard: () => {
    set((state) => {
      state.dashboardMetrics = initialDashboardMetrics;
      state.recentOrders = [];
      state.dashboardAlerts = [];
      state.quickStats = initialQuickStats;
      state.isDashboardLoading = false;
      state.dashboardError = null;
      state.lastDashboardUpdate = null;
      state.isRealTimeEnabled = true;
      state.refreshInterval = 30000;
    });
  },

  clearDashboardData: (restaurantId: string) => {
    // Clear cached data for specific restaurant
    set((state) => {
      if (state.currentRestaurantId === restaurantId) {
        state.dashboardMetrics = initialDashboardMetrics;
        state.recentOrders = [];
        state.dashboardAlerts = [];
        state.quickStats = initialQuickStats;
      }
    });
  },
});
