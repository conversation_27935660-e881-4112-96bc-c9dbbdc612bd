import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Users } from "lucide-react";
import { toast } from "@/components/ui/sonner";

interface PartySizeInputProps {
  isOpen: boolean;
  tableNumber: number;
  onConfirm: (partySize: number) => void;
  onCancel: () => void;
}

const PartySizeInput: React.FC<PartySizeInputProps> = ({
  isOpen,
  tableNumber,
  onConfirm,
  onCancel
}) => {
  const [partySize, setPartySize] = useState<string>("");

  const handleConfirm = () => {
    const size = parseInt(partySize);
    
    if (isNaN(size) || size < 1) {
      toast.error("Please enter a valid number of guests (minimum 1)");
      return;
    }
    
    if (size > 20) {
      toast.error("Maximum party size is 20 guests. For larger groups, please contact management.");
      return;
    }

    onConfirm(size);
    setPartySize("");
  };

  const handleCancel = () => {
    setPartySize("");
    onCancel();
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleConfirm();
    }
  };

  // Quick select buttons for common party sizes
  const quickSizes = [1, 2, 3, 4, 5, 6];

  return (
    <Dialog open={isOpen} onOpenChange={() => {}}>
      <DialogContent className="sm:max-w-md [&>button]:hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            Party Size for Table {tableNumber}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            How many guests will be seated at Table {tableNumber}?
          </p>

          {/* Quick select buttons */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Quick Select:</Label>
            <div className="grid grid-cols-3 gap-2">
              {quickSizes.map(size => (
                <Button
                  key={size}
                  variant="outline"
                  className="h-12"
                  onClick={() => setPartySize(size.toString())}
                >
                  {size} {size === 1 ? 'Guest' : 'Guests'}
                </Button>
              ))}
            </div>
          </div>

          {/* Manual input */}
          <div className="space-y-2">
            <Label htmlFor="party-size">Or enter custom number:</Label>
            <Input
              id="party-size"
              type="number"
              min="1"
              max="20"
              value={partySize}
              onChange={(e) => setPartySize(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Enter number of guests"
              className="text-center text-lg"
            />
          </div>

          <div className="text-xs text-muted-foreground text-center">
            Valid range: 1-20 guests
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button 
            onClick={handleConfirm}
            disabled={!partySize || parseInt(partySize) < 1 || parseInt(partySize) > 20}
          >
            Confirm Party Size
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default PartySizeInput;
