#!/usr/bin/env python3
"""
Test script for the lightweight AI implementation
Validates functionality, performance, and integration
"""

import asyncio
import time
import json
import sys
import os
from typing import List, Dict, Any

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.services.functional_ai_service import (
    chat_with_ai,
    get_ai_status,
    reload_ai_configuration,
    get_cache_stats,
    clear_cache
)

class AITestSuite:
    """Test suite for lightweight AI implementation"""
    
    def __init__(self):
        self.test_results: List[Dict[str, Any]] = []
        self.start_time = time.time()
    
    def log_test(self, test_name: str, success: bool, duration: float, details: Dict[str, Any] = None):
        """Log test result"""
        result = {
            "test_name": test_name,
            "success": success,
            "duration_ms": round(duration * 1000, 2),
            "details": details or {}
        }
        self.test_results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name} ({result['duration_ms']}ms)")
        
        if details:
            for key, value in details.items():
                print(f"    {key}: {value}")
    
    async def test_ai_status(self) -> bool:
        """Test AI status endpoint"""
        start_time = time.time()
        try:
            status = await get_ai_status()
            duration = time.time() - start_time
            
            # Validate response structure
            required_fields = ["ai_enabled", "model", "features", "message"]
            missing_fields = [field for field in required_fields if field not in status]
            
            if missing_fields:
                self.log_test("AI Status", False, duration, {
                    "error": f"Missing fields: {missing_fields}",
                    "response": status
                })
                return False
            
            self.log_test("AI Status", True, duration, {
                "ai_enabled": status["ai_enabled"],
                "model": status.get("model", "None"),
                "features_count": len(status["features"])
            })
            return True
            
        except Exception as e:
            duration = time.time() - start_time
            self.log_test("AI Status", False, duration, {"error": str(e)})
            return False
    
    async def test_basic_chat(self) -> bool:
        """Test basic chat functionality"""
        test_queries = [
            "Hello, how are you?",
            "What can you help me with?",
            "Tell me a fun fact",
            "Thank you for your help"
        ]
        
        all_passed = True
        
        for query in test_queries:
            start_time = time.time()
            try:
                response = await chat_with_ai(query)
                duration = time.time() - start_time
                
                # Validate response structure
                required_fields = ["success", "response", "ai_enabled", "timestamp"]
                missing_fields = [field for field in required_fields if field not in response]
                
                if missing_fields:
                    self.log_test(f"Chat: '{query[:20]}...'", False, duration, {
                        "error": f"Missing fields: {missing_fields}"
                    })
                    all_passed = False
                    continue
                
                # Check if response is meaningful
                if not response["response"] or len(response["response"]) < 10:
                    self.log_test(f"Chat: '{query[:20]}...'", False, duration, {
                        "error": "Response too short or empty"
                    })
                    all_passed = False
                    continue
                
                self.log_test(f"Chat: '{query[:20]}...'", True, duration, {
                    "success": response["success"],
                    "ai_enabled": response["ai_enabled"],
                    "response_length": len(response["response"]),
                    "cached": response.get("cached", False)
                })
                
            except Exception as e:
                duration = time.time() - start_time
                self.log_test(f"Chat: '{query[:20]}...'", False, duration, {"error": str(e)})
                all_passed = False
        
        return all_passed
    
    async def test_caching(self) -> bool:
        """Test response caching functionality"""
        test_query = "What is artificial intelligence?"
        
        # First request (should not be cached)
        start_time = time.time()
        try:
            response1 = await chat_with_ai(test_query)
            duration1 = time.time() - start_time
            
            # Second request (should be cached)
            start_time = time.time()
            response2 = await chat_with_ai(test_query)
            duration2 = time.time() - start_time
            
            # Validate caching
            if response2.get("cached", False) and duration2 < duration1:
                self.log_test("Caching", True, duration2, {
                    "first_request_ms": round(duration1 * 1000, 2),
                    "cached_request_ms": round(duration2 * 1000, 2),
                    "speedup": f"{round(duration1 / duration2, 1)}x faster"
                })
                return True
            else:
                self.log_test("Caching", False, duration2, {
                    "error": "Caching not working as expected",
                    "cached_flag": response2.get("cached", False)
                })
                return False
                
        except Exception as e:
            duration = time.time() - start_time
            self.log_test("Caching", False, duration, {"error": str(e)})
            return False
    
    async def test_cache_management(self) -> bool:
        """Test cache management functions"""
        start_time = time.time()
        try:
            # Get initial cache stats
            stats_before = get_cache_stats()
            
            # Clear cache
            clear_result = clear_cache()
            
            # Get stats after clearing
            stats_after = get_cache_stats()
            
            duration = time.time() - start_time
            
            if (clear_result.get("success", False) and 
                stats_after["cache_size"] == 0):
                self.log_test("Cache Management", True, duration, {
                    "cache_size_before": stats_before["cache_size"],
                    "cache_size_after": stats_after["cache_size"],
                    "max_size": stats_after["max_size"]
                })
                return True
            else:
                self.log_test("Cache Management", False, duration, {
                    "error": "Cache clearing failed",
                    "clear_result": clear_result
                })
                return False
                
        except Exception as e:
            duration = time.time() - start_time
            self.log_test("Cache Management", False, duration, {"error": str(e)})
            return False
    
    async def test_performance(self) -> bool:
        """Test performance characteristics"""
        queries = [
            "Hello",
            "How are you?",
            "What's the weather like?",
            "Tell me about AI",
            "Thank you"
        ]
        
        start_time = time.time()
        durations = []
        
        try:
            for query in queries:
                query_start = time.time()
                response = await chat_with_ai(query)
                query_duration = time.time() - query_start
                durations.append(query_duration)
                
                if not response["success"]:
                    self.log_test("Performance", False, time.time() - start_time, {
                        "error": f"Query failed: {query}"
                    })
                    return False
            
            total_duration = time.time() - start_time
            avg_duration = sum(durations) / len(durations)
            max_duration = max(durations)
            min_duration = min(durations)
            
            # Performance criteria: average response time < 5 seconds
            if avg_duration < 5.0:
                self.log_test("Performance", True, total_duration, {
                    "queries_tested": len(queries),
                    "avg_response_ms": round(avg_duration * 1000, 2),
                    "min_response_ms": round(min_duration * 1000, 2),
                    "max_response_ms": round(max_duration * 1000, 2)
                })
                return True
            else:
                self.log_test("Performance", False, total_duration, {
                    "error": "Average response time too slow",
                    "avg_response_ms": round(avg_duration * 1000, 2),
                    "threshold_ms": 5000
                })
                return False
                
        except Exception as e:
            duration = time.time() - start_time
            self.log_test("Performance", False, duration, {"error": str(e)})
            return False
    
    async def test_error_handling(self) -> bool:
        """Test error handling with invalid inputs"""
        test_cases = [
            ("", "Empty query"),
            ("x" * 3000, "Query too long"),
        ]
        
        all_passed = True
        
        for query, description in test_cases:
            start_time = time.time()
            try:
                response = await chat_with_ai(query)
                duration = time.time() - start_time
                
                # For invalid inputs, we expect either success=False or a fallback response
                if query == "":
                    # Empty query should be handled gracefully
                    expected_success = False
                elif len(query) > 2000:
                    # Long query should be truncated or handled
                    expected_success = True  # Should handle gracefully
                
                self.log_test(f"Error Handling: {description}", True, duration, {
                    "query_length": len(query),
                    "response_success": response["success"],
                    "handled_gracefully": True
                })
                
            except Exception as e:
                duration = time.time() - start_time
                self.log_test(f"Error Handling: {description}", False, duration, {
                    "error": str(e)
                })
                all_passed = False
        
        return all_passed
    
    async def run_all_tests(self):
        """Run all tests and generate report"""
        print("🚀 Starting Lightweight AI Test Suite")
        print("=" * 50)
        
        tests = [
            ("AI Status", self.test_ai_status),
            ("Basic Chat", self.test_basic_chat),
            ("Caching", self.test_caching),
            ("Cache Management", self.test_cache_management),
            ("Performance", self.test_performance),
            ("Error Handling", self.test_error_handling),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n📋 Running {test_name} tests...")
            try:
                result = await test_func()
                if result:
                    passed += 1
            except Exception as e:
                print(f"❌ FAIL {test_name} - Unexpected error: {e}")
        
        # Generate summary
        print("\n" + "=" * 50)
        print("📊 TEST SUMMARY")
        print("=" * 50)
        
        total_duration = time.time() - self.start_time
        success_rate = (passed / total) * 100
        
        print(f"Tests Passed: {passed}/{total} ({success_rate:.1f}%)")
        print(f"Total Duration: {total_duration:.2f}s")
        
        if passed == total:
            print("🎉 All tests passed! Lightweight AI is working correctly.")
        else:
            print("⚠️  Some tests failed. Check the details above.")
        
        # Save detailed results
        with open("ai_test_results.json", "w") as f:
            json.dump({
                "summary": {
                    "passed": passed,
                    "total": total,
                    "success_rate": success_rate,
                    "total_duration": total_duration
                },
                "test_results": self.test_results
            }, f, indent=2)
        
        print(f"📄 Detailed results saved to ai_test_results.json")
        
        return passed == total

async def main():
    """Main test runner"""
    test_suite = AITestSuite()
    success = await test_suite.run_all_tests()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    asyncio.run(main())
