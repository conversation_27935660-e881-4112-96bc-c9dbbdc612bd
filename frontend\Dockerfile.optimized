# Multi-stage build for React frontend
# This Dockerfile is optimized to be built from the frontend directory
# Usage: cd frontend && docker build -t frontend:latest -f Dockerfile.optimized .

FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files first for better layer caching
COPY package*.json ./

# Install dependencies
RUN npm ci --silent --only=production=false

# Copy source code
COPY . .

# Build the application (skip TypeScript checking for production build)
RUN npm run build:no-check

# Production stage with nginx
FROM nginx:alpine AS production

# Install curl for health checks
RUN apk add --no-cache curl

# Copy custom nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Copy built application from builder stage
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy environment configuration script
COPY docker-entrypoint.sh /docker-entrypoint.sh
RUN chmod +x /docker-entrypoint.sh

# Set permissions for security (nginx user already exists in nginx:alpine)
RUN chown -R nginx:nginx /usr/share/nginx/html

# Expose port 80
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/ || exit 1

# Use custom entrypoint script
ENTRYPOINT ["/docker-entrypoint.sh"]

# Default command for nginx
CMD ["nginx", "-g", "daemon off;"]
