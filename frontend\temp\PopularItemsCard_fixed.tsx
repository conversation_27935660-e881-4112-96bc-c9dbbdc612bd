import { 
  <PERSON>, 
  <PERSON><PERSON>onte<PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>it<PERSON> 
} from "@/components/ui/card";
import { TrendingUp, TrendingDown } from "lucide-react";

interface MenuItem {
  id: string;
  name: string;
  category: string;
  price: number;
  orderCount: number;
  percentageOfSales: number;
  trend: number; // Positive for upward trend, negative for downward
}

interface PopularItemsCardProps {
  items?: MenuItem[];
}

// Default empty items array
const defaultItems: MenuItem[] = [];

const PopularItemsCard = ({ items = defaultItems }: PopularItemsCardProps) => {
  // Sort items by order count (highest first)
  const sortedItems = [...items].sort((a, b) => b.orderCount - a.orderCount);
  
  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg font-medium">Popular Menu Items</CardTitle>
      </CardHeader>
      <CardContent>
        {items.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No menu items data available
          </div>
        ) : (
          <div className="space-y-4">
            {sortedItems.map((item) => (
              <div key={item.id} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                <div>
                  <div className="font-medium">{item.name}</div>
                  <div className="text-xs text-muted-foreground">{item.category}</div>
                </div>
                <div className="flex flex-col items-end">
                  <div className="font-medium">${item.price.toFixed(2)}</div>
                  <div className="flex items-center text-xs">
                    <span className="text-muted-foreground mr-1">{item.orderCount} orders</span>
                    {item.trend > 0 ? (
                      <span className="flex items-center text-green-600">
                        <TrendingUp className="h-3 w-3 mr-1" />
                        {item.trend}%
                      </span>
                    ) : (
                      <span className="flex items-center text-red-600">
                        <TrendingDown className="h-3 w-3 mr-1" />
                        {Math.abs(item.trend)}%
                      </span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default PopularItemsCard;
