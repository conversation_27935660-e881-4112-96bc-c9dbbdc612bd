﻿/**
 * Production-ready API service for RestroManage frontend
 * Handles all backend communication with proper error handling and authentication
 */

import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';

// API Configuration
// In development, use proxy paths; in production, use full URLs
const API_BASE_URL = import.meta.env.DEV
  ? '' // Use proxy in development (Vite will proxy to backend)
  : (import.meta.env.VITE_API_URL || 'http://localhost:5001');
const API_TIMEOUT = 30000; // 30 seconds

// Types
export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  success: boolean;
}

export interface ApiError {
  message: string;
  status: number;
  details?: any;
}

export interface SalesData {
  date: string;
  sales: number;
  orders: number;
}

export interface PopularItem {
  id: string;
  name: string;
  category: string;
  price: number;
  orderCount: number;
  percentageOfSales: number;
  trend: number;
}

export interface RevenueData {
  period: string;
  revenue: number;
}

export interface ForecastData {
  day: string;
  customers: number;
  projectedRevenue: number;
}

export interface AnalyticsResponse {
  daily_sales: SalesData[];
  weekly_revenue: RevenueData[];
  monthly_revenue: RevenueData[];
  forecast: ForecastData[];
}

// Create axios instance
const createApiInstance = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: API_BASE_URL,
    timeout: API_TIMEOUT,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // Request interceptor for authentication
  instance.interceptors.request.use(
    (config) => {
      // Add restaurant ID to headers if available
      const restaurantId = localStorage.getItem('restaurant_id');
      if (restaurantId) {
        config.headers['X-Restaurant-ID'] = restaurantId;
      }

      // Add authentication token if available
      const token = localStorage.getItem('access_token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }

      console.log(`[API] ${config.method?.toUpperCase()} ${config.url}`, {
        params: config.params,
        headers: config.headers,
      });

      return config;
    },
    (error) => {
      console.error('[API] Request error:', error);
      return Promise.reject(error);
    }
  );

  // Response interceptor for error handling
  instance.interceptors.response.use(
    (response: AxiosResponse) => {
      console.log(`[API] Response ${response.status}:`, response.data);
      return response;
    },
    (error: AxiosError) => {
      console.error('[API] Response error:', error);
      
      if (error.response?.status === 401) {
        // Handle authentication errors
        localStorage.removeItem('access_token');
        localStorage.removeItem('restaurant_id');
        window.location.href = '/login';
      }

      return Promise.reject(error);
    }
  );

  return instance;
};

// API instance
const api = createApiInstance();

// Generic API request handler
const handleApiRequest = async <T>(
  request: () => Promise<AxiosResponse<T>>
): Promise<T> => {
  try {
    const response = await request();
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      const apiError: ApiError = {
        message: error.response?.data?.detail || error.message || 'An error occurred',
        status: error.response?.status || 500,
        details: error.response?.data,
      };
      throw apiError;
    }
    throw error;
  }
};

// Analytics API
export const analyticsApi = {
  // Get sales data
  getSalesData: async (
    startDate?: string,
    endDate?: string,
    restaurantId?: string
  ): Promise<SalesData[]> => {
    return handleApiRequest(() =>
      api.get('/api/analytics/sales', {
        params: { start_date: startDate, end_date: endDate, restaurant_id: restaurantId },
      })
    );
  },

  // Get popular items
  getPopularItems: async (limit: number = 5, restaurantId?: string): Promise<PopularItem[]> => {
    return handleApiRequest(() =>
      api.get('/api/analytics/popular-items', {
        params: { limit, restaurant_id: restaurantId },
      })
    );
  },

  // Get revenue data
  getRevenueData: async (
    periodType: 'weekly' | 'monthly' = 'weekly',
    restaurantId?: string
  ): Promise<RevenueData[]> => {
    return handleApiRequest(() =>
      api.get('/api/analytics/revenue', {
        params: { period_type: periodType, restaurant_id: restaurantId },
      })
    );
  },

  // Get forecast data
  getForecastData: async (restaurantId?: string): Promise<ForecastData[]> => {
    return handleApiRequest(() =>
      api.get('/api/analytics/forecast', {
        params: { restaurant_id: restaurantId },
      })
    );
  },

  // Get dashboard data
  getDashboardData: async (restaurantId?: string): Promise<AnalyticsResponse> => {
    return handleApiRequest(() =>
      api.get('/api/analytics/dashboard', {
        params: { restaurant_id: restaurantId },
      })
    );
  },

  // Get inventory forecast
  getInventoryForecast: async (restaurantId?: string): Promise<any[]> => {
    return handleApiRequest(() =>
      api.get('/api/analytics/inventory-forecast', {
        params: { restaurant_id: restaurantId },
      })
    );
  },

  // Get advanced forecast data
  getAdvancedForecast: async (): Promise<any[]> => {
    return handleApiRequest(() =>
      api.get('/api/analytics/advanced-forecast')
    );
  },

  // Get seasonal analysis
  getSeasonalAnalysis: async (): Promise<any[]> => {
    return handleApiRequest(() =>
      api.get('/api/analytics/seasonal-analysis')
    );
  },

  // Get revenue projections
  getRevenueProjections: async (months: number = 12): Promise<any[]> => {
    return handleApiRequest(() =>
      api.get('/api/analytics/revenue-projections', {
        params: { months },
      })
    );
  },

  // Get staff scheduling data
  getStaffScheduling: async (): Promise<any[]> => {
    return handleApiRequest(() =>
      api.get('/api/analytics/staff-scheduling')
    );
  },

  // Get category performance
  getCategoryPerformance: async (restaurantId?: string, days: number = 30): Promise<any[]> => {
    return handleApiRequest(() =>
      api.get('/api/analytics/category-performance', {
        params: { days, restaurant_id: restaurantId },
      })
    );
  },

  // Get peak hours analysis
  getPeakHoursAnalysis: async (restaurantId?: string, days: number = 30): Promise<any[]> => {
    return handleApiRequest(() =>
      api.get('/api/analytics/peak-hours', {
        params: { days, restaurant_id: restaurantId },
      })
    );
  },

  // Get profit margin analysis
  getProfitMarginAnalysis: async (restaurantId?: string, days: number = 30): Promise<any[]> => {
    return handleApiRequest(() =>
      api.get('/api/analytics/profit-margins', {
        params: { days, restaurant_id: restaurantId },
      })
    );
  },
};

// Health check
export const healthCheck = async (): Promise<{ status: string }> => {
  return handleApiRequest(() => api.get('/health'));
};

// Test API connection
export const testConnection = async (): Promise<boolean> => {
  try {
    await healthCheck();
    return true;
  } catch (error) {
    console.error('[API] Connection test failed:', error);
    return false;
  }
};

// Export default API instance
export default api;

// AI Assistant API
export const aiApi = {
  // Chat with AI assistant
  chatWithAI: async (request: { message: string; context_type: string }, restaurantId?: string): Promise<any> => {
    return handleApiRequest(() =>
      api.post("/api/ai/chat", request, {
        params: { restaurant_id: restaurantId },
      })
    );
  },

  // Get menu recommendations
  getMenuRecommendations: async (restaurantId?: string): Promise<any> => {
    return handleApiRequest(() =>
      api.get("/api/ai/menu-recommendations", {
        params: { restaurant_id: restaurantId },
      })
    );
  },

  // Get inventory insights
  getInventoryInsights: async (restaurantId?: string): Promise<any> => {
    return handleApiRequest(() =>
      api.get("/api/ai/inventory-insights", {
        params: { restaurant_id: restaurantId },
      })
    );
  },

  // Get sales analysis
  getSalesAnalysis: async (restaurantId?: string): Promise<any> => {
    return handleApiRequest(() =>
      api.get("/api/ai/sales-analysis", {
        params: { restaurant_id: restaurantId },
      })
    );
  },
};
