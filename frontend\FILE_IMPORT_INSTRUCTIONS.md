# File Import Dialog for Inventory

This document provides instructions on how to implement the file import dialog for the inventory tab in RestroManage.

## Overview

The implementation adds a file selection dialog with support for .csv, .xlsx, and .pdf files to the inventory tab.

## Implementation

### Step 1: Add the FileImportDialog Component

The `FileImportDialog` component has been created in `src/components/inventory/FileImportDialog.tsx`. This component provides a reusable dialog for importing files.

### Step 2: Modify the Inventory Component

To use the FileImportDialog component in the Inventory page, follow these steps:

1. Import the component at the top of your Inventory.tsx file:

```tsx
import { FileImportDialog } from "@/components/inventory/FileImportDialog";
```

2. Replace the existing Import Dialog with the FileImportDialog component:

```tsx
{/* Import Dialog */}
<FileImportDialog 
  isOpen={isImportOpen} 
  onClose={() => setIsImportOpen(false)} 
  onImport={(items) => {
    // Update inventory with imported items
    const updatedInventory = [...inventory];
    let updatedCount = 0;

    for (const item of items) {
      // Find matching inventory item
      const matchingItem = inventory.find(invItem =>
        invItem.name.toLowerCase().includes(item.name.toLowerCase())
      );

      if (matchingItem) {
        const index = updatedInventory.findIndex(i => i.id === matchingItem.id);
        if (index !== -1) {
          const newStock = updatedInventory[index].stock + item.quantity;
          updatedInventory[index] = {
            ...updatedInventory[index],
            stock: newStock,
            isLow: newStock <= updatedInventory[index].reorderLevel
          };
          updatedCount++;
        }
      }
    }

    setInventory(updatedInventory);
    toast.success(`Updated ${updatedCount} inventory items from import`);
  }}
/>
```

3. Remove the existing handleImportData and handleApplyImport functions if they are no longer needed.

### Step 3: Test the Implementation

1. Start the development server:
   ```bash
   npm run dev
   ```

2. Navigate to the Inventory tab
3. Click the "Import" button
4. Select a CSV, XLSX, or PDF file
5. Verify that the file is processed and the data is displayed
6. Click "Apply Changes" to update the inventory

## Features

- File selection dialog with support for .csv, .xlsx, and .pdf files
- Preview of imported data with status indicators
- Ability to apply changes to the inventory

## Notes

- This implementation uses a simplified approach with mock data for demonstration purposes
- In a production environment, you would want to implement actual file parsing logic
- The component is designed to be reusable and can be extended to support additional file formats
