import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  LineChart,
  Line,
  ComposedChart,
  Area,
  Cell
} from "recharts";
import { ForecastData } from "@/components/dashboard/ForecastCard";

interface StaffMember {
  id: string;
  name: string;
  role: string;
  position: string;
  status?: "active" | "inactive";
  availableDays?: string[];
  assignedHours?: number;
}

interface LaborCostAnalysisProps {
  forecastData: ForecastData[];
  staffData: StaffMember[];
  timeframe: "week" | "month" | "quarter";
}

export const LaborCostAnalysis = ({
  forecastData,
  staffData,
  timeframe
}: LaborCostAnalysisProps) => {
  // Filter data based on timeframe
  const filteredData = forecastData.slice(0, timeframe === "week" ? 7 : timeframe === "month" ? 30 : 90);
  
  // Define hourly rates by role
  const hourlyRates = {
    "Waiter": 11.50,
    "Chef": 16.00,
    "Kitchen Assistant": 12.00,
    "Bartender": 13.50,
    "Host": 11.00,
    "default": 12.50
  };
  
  // Calculate labor costs data
  const laborCostsData = filteredData.map(day => {
    const customers = day.customers || 0;
    const revenue = day.projectedRevenue || 0;
    
    // Calculate optimal staff needed based on customer volume
    const waiters = Math.ceil(customers / 15); // 1 waiter per 15 customers
    const kitchen = Math.ceil(customers / 20); // 1 kitchen staff per 20 customers
    const bar = Math.ceil(customers / 30); // 1 bar staff per 30 customers
    const host = Math.ceil(customers / 50); // 1 host per 50 customers
    
    // Calculate labor hours (assuming 8-hour shifts)
    const waiterHours = waiters * 8;
    const kitchenHours = kitchen * 8;
    const barHours = bar * 8;
    const hostHours = host * 8;
    
    // Calculate labor costs
    const waiterCost = waiterHours * hourlyRates["Waiter"];
    const kitchenCost = kitchenHours * hourlyRates["Chef"]; // Simplification
    const barCost = barHours * hourlyRates["Bartender"];
    const hostCost = hostHours * hourlyRates["Host"];
    
    const totalLaborCost = waiterCost + kitchenCost + barCost + hostCost;
    const laborPercentage = revenue > 0 ? (totalLaborCost / revenue) * 100 : 0;
    
    return {
      day: day.day,
      revenue,
      laborCost: totalLaborCost,
      laborPercentage,
      breakdown: {
        waiter: waiterCost,
        kitchen: kitchenCost,
        bar: barCost,
        host: hostCost
      }
    };
  });
  
  // Calculate labor costs by role
  const laborCostsByRole = [
    { 
      name: "Waiters", 
      value: laborCostsData.reduce((sum, day) => sum + day.breakdown.waiter, 0),
      color: "#8884d8"
    },
    { 
      name: "Kitchen", 
      value: laborCostsData.reduce((sum, day) => sum + day.breakdown.kitchen, 0),
      color: "#82ca9d"
    },
    { 
      name: "Bar", 
      value: laborCostsData.reduce((sum, day) => sum + day.breakdown.bar, 0),
      color: "#ffc658"
    },
    { 
      name: "Host", 
      value: laborCostsData.reduce((sum, day) => sum + day.breakdown.host, 0),
      color: "#ff8042"
    }
  ];
  
  // Calculate total labor cost
  const totalLaborCost = laborCostsByRole.reduce((sum, role) => sum + role.value, 0);
  
  // Calculate average labor percentage
  const averageLaborPercentage = laborCostsData.reduce((sum, day) => sum + day.laborPercentage, 0) / laborCostsData.length;
  
  // Custom tooltip formatter for labor costs
  const LaborCostTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border rounded-md shadow-sm p-2 text-sm">
          <p className="font-medium">{label}</p>
          <p className="text-blue-500">Revenue: £{payload[0].payload.revenue.toLocaleString(undefined, { maximumFractionDigits: 0 })}</p>
          <p className="text-green-500">Labor Cost: £{payload[0].payload.laborCost.toLocaleString(undefined, { maximumFractionDigits: 0 })}</p>
          <p className="text-red-500">Labor %: {payload[0].payload.laborPercentage.toFixed(1)}%</p>
        </div>
      );
    }
    return null;
  };
  
  // Custom tooltip formatter for labor costs by role
  const RoleTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const percentage = ((payload[0].value / totalLaborCost) * 100).toFixed(1);
      return (
        <div className="bg-background border rounded-md shadow-sm p-2 text-sm">
          <p className="font-medium">{payload[0].name}</p>
          <p className="text-blue-500">Cost: £{payload[0].value.toLocaleString(undefined, { maximumFractionDigits: 0 })}</p>
          <p className="text-green-500">Percentage: {percentage}%</p>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Labor Cost vs. Revenue</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <ComposedChart
                data={laborCostsData}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="day" />
                <YAxis yAxisId="left" orientation="left" tickFormatter={(value) => `£${value}`} />
                <YAxis yAxisId="right" orientation="right" tickFormatter={(value) => `${value}%`} />
                <Tooltip content={<LaborCostTooltip />} />
                <Legend />
                <Bar yAxisId="left" dataKey="revenue" name="Revenue" fill="#8884d8" />
                <Bar yAxisId="left" dataKey="laborCost" name="Labor Cost" fill="#82ca9d" />
                <Line yAxisId="right" type="monotone" dataKey="laborPercentage" name="Labor %" stroke="#ff7300" />
              </ComposedChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Labor Cost by Role</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-[250px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={laborCostsByRole}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis tickFormatter={(value) => `£${value}`} />
                  <Tooltip content={<RoleTooltip />} />
                  <Bar dataKey="value" name="Cost" fill="#8884d8">
                    {laborCostsByRole.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Labor Cost Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-muted rounded-lg p-4">
                  <p className="text-sm font-medium text-muted-foreground">Total Labor Cost</p>
                  <p className="text-2xl font-bold">£{totalLaborCost.toLocaleString(undefined, { maximumFractionDigits: 0 })}</p>
                </div>
                <div className="bg-muted rounded-lg p-4">
                  <p className="text-sm font-medium text-muted-foreground">Average Labor %</p>
                  <p className="text-2xl font-bold">{averageLaborPercentage.toFixed(1)}%</p>
                </div>
              </div>
              
              <div className="space-y-2">
                <h3 className="font-medium">Cost Optimization Insights</h3>
                <p className="text-sm text-muted-foreground">
                  Industry standard labor percentage is 25-35%. Your current average is {averageLaborPercentage.toFixed(1)}%, 
                  which is {averageLaborPercentage > 35 ? "above" : "within"} the recommended range.
                </p>
                {averageLaborPercentage > 35 && (
                  <p className="text-sm text-muted-foreground">
                    Consider optimizing staff scheduling to reduce labor costs by approximately 
                    £{Math.round((averageLaborPercentage - 35) / 100 * totalLaborCost).toLocaleString()} 
                    to reach the 35% target.
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default LaborCostAnalysis;
