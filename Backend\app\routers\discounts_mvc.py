"""
Discounts MVC router implementing EPOS discount and promo code functionality.
Uses DiscountController for business logic and provides RESTful API endpoints.
"""

from fastapi import APIRouter, HTTPException, status, Depends, Query
from typing import List, Optional
from pydantic import BaseModel

from app.controllers.discount_controller import DiscountController
from app.utils.logging_config import logger

router = APIRouter(prefix="/discounts", tags=["Discounts (MVC)"])

# Pydantic models for request/response
class PromoCodeCreate(BaseModel):
    code: str
    discount_type: str  # "percentage", "fixed_amount", "buy_one_get_one"
    discount_value: float
    description: Optional[str] = None
    restaurant_id: Optional[str] = None
    min_order_amount: Optional[float] = None
    usage_limit: Optional[int] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    is_active: bool = True

class PromoCodeUpdate(BaseModel):
    code: Optional[str] = None
    discount_type: Optional[str] = None
    discount_value: Optional[float] = None
    description: Optional[str] = None
    min_order_amount: Optional[float] = None
    usage_limit: Optional[int] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    is_active: Optional[bool] = None

class PromoCodeValidation(BaseModel):
    code: str
    restaurant_id: str
    order_total: float
    customer_id: Optional[str] = None

class DiscountApplication(BaseModel):
    order_id: str
    promo_code: str
    order_total: float
    restaurant_id: str
    customer_id: Optional[str] = None

# Dependency injection for controller
async def get_discount_controller() -> DiscountController:
    """Dependency injection for discount controller"""
    return DiscountController()

@router.get("/")
async def get_discounts(
    restaurant_id: Optional[str] = Query(None, description="Filter by restaurant ID"),
    active_only: bool = Query(True, description="Show only active discounts"),
    discount_type: Optional[str] = Query(None, description="Filter by discount type"),
    skip: int = Query(0, ge=0, description="Number of discounts to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of discounts to return"),
    controller: DiscountController = Depends(get_discount_controller)
):
    """
    Get discounts/promo codes with filtering.
    
    This endpoint supports EPOS functionality by providing:
    - Active promo codes for order processing
    - Restaurant-specific discounts
    - Filtering by discount type
    """
    try:
        discounts = await controller.get_discounts(
            restaurant_id=restaurant_id,
            active_only=active_only,
            discount_type=discount_type,
            skip=skip,
            limit=limit
        )
        return {
            "success": True,
            "data": discounts,
            "count": len(discounts),
            "filters": {
                "restaurant_id": restaurant_id,
                "active_only": active_only,
                "discount_type": discount_type
            }
        }
    except Exception as e:
        logger.error(f"Failed to get discounts: {e}", "DiscountsRouter")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve discounts"
        )

@router.post("/validate")
async def validate_promo_code(
    validation_request: PromoCodeValidation,
    controller: DiscountController = Depends(get_discount_controller)
):
    """
    Validate promo code - Critical EPOS endpoint.
    
    This endpoint is essential for EPOS checkout process:
    - Validates promo code against business rules
    - Calculates discount amount
    - Checks usage limits and date validity
    - Returns final order total with discount applied
    """
    try:
        validation_result = await controller.validate_promo_code(
            code=validation_request.code,
            restaurant_id=validation_request.restaurant_id,
            order_total=validation_request.order_total,
            customer_id=validation_request.customer_id
        )
        
        return {
            "success": True,
            "data": validation_result
        }
    except Exception as e:
        logger.error(f"Failed to validate promo code: {e}", "DiscountsRouter")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to validate promo code"
        )

@router.post("/apply")
async def apply_discount(
    application_request: DiscountApplication,
    controller: DiscountController = Depends(get_discount_controller)
):
    """
    Apply discount to order - Critical EPOS endpoint.
    
    This endpoint completes the discount application process:
    - Applies validated discount to order
    - Updates promo code usage count
    - Creates usage tracking record
    - Returns final order details with discount
    """
    try:
        application_result = await controller.apply_discount(
            order_id=application_request.order_id,
            promo_code=application_request.promo_code,
            order_total=application_request.order_total,
            restaurant_id=application_request.restaurant_id,
            customer_id=application_request.customer_id
        )
        
        return {
            "success": application_result["success"],
            "data": application_result
        }
    except Exception as e:
        logger.error(f"Failed to apply discount: {e}", "DiscountsRouter")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to apply discount"
        )

@router.get("/{promo_code_id}")
async def get_promo_code(
    promo_code_id: str,
    controller: DiscountController = Depends(get_discount_controller)
):
    """Get a specific promo code by ID"""
    promo_code = await controller.get_promo_code_by_id(promo_code_id)
    if not promo_code:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Promo code not found"
        )
    return {
        "success": True,
        "data": promo_code
    }

@router.post("/")
async def create_promo_code(
    promo_code_data: PromoCodeCreate,
    controller: DiscountController = Depends(get_discount_controller)
):
    """Create a new promo code - Admin functionality"""
    try:
        created_code = await controller.create_promo_code(promo_code_data.model_dump())
        return {
            "success": True,
            "message": "Promo code created successfully",
            "data": created_code
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error creating promo code: {e}", "DiscountsRouter")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create promo code"
        )

@router.put("/{promo_code_id}")
async def update_promo_code(
    promo_code_id: str,
    promo_code_data: PromoCodeUpdate,
    controller: DiscountController = Depends(get_discount_controller)
):
    """Update promo code - Admin functionality"""
    try:
        updated_code = await controller.update_promo_code(
            promo_code_id,
            promo_code_data.model_dump(exclude_unset=True)
        )
        return {
            "success": True,
            "message": "Promo code updated successfully",
            "data": updated_code
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error updating promo code: {e}", "DiscountsRouter")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update promo code"
        )

@router.patch("/{promo_code_id}/deactivate")
async def deactivate_promo_code(
    promo_code_id: str,
    controller: DiscountController = Depends(get_discount_controller)
):
    """Deactivate a promo code"""
    try:
        updated_code = await controller.deactivate_promo_code(promo_code_id)
        return {
            "success": True,
            "message": "Promo code deactivated successfully",
            "data": updated_code
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error deactivating promo code: {e}", "DiscountsRouter")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to deactivate promo code"
        )

@router.get("/{promo_code_id}/usage")
async def get_promo_code_usage(
    promo_code_id: str,
    skip: int = Query(0, ge=0, description="Number of usage records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of usage records to return"),
    controller: DiscountController = Depends(get_discount_controller)
):
    """Get usage history for a specific promo code"""
    try:
        usage_records = await controller.get_promo_code_usage(
            promo_code_id=promo_code_id,
            skip=skip,
            limit=limit
        )
        return {
            "success": True,
            "data": usage_records,
            "count": len(usage_records)
        }
    except Exception as e:
        logger.error(f"Failed to get promo code usage: {e}", "DiscountsRouter")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve usage records"
        )

@router.get("/analytics/{restaurant_id}")
async def get_discount_analytics(
    restaurant_id: str,
    controller: DiscountController = Depends(get_discount_controller)
):
    """
    Get discount analytics for restaurant dashboard.
    
    Provides insights for restaurant management:
    - Total discounts applied
    - Discount effectiveness
    - Popular promo codes
    - Revenue impact
    """
    try:
        analytics_data = await controller.get_discount_analytics(restaurant_id)
        return {
            "success": True,
            "data": analytics_data
        }
    except Exception as e:
        logger.error(f"Failed to get discount analytics: {e}", "DiscountsRouter")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve discount analytics"
        )

# Quick validation endpoint for EPOS integration
@router.get("/quick-validate/{code}")
async def quick_validate_promo_code(
    code: str,
    restaurant_id: str = Query(..., description="Restaurant ID"),
    order_total: float = Query(..., description="Order total amount"),
    controller: DiscountController = Depends(get_discount_controller)
):
    """
    Quick promo code validation for EPOS integration.
    
    Simplified endpoint for real-time validation during order entry.
    Returns basic validation status and discount amount.
    """
    try:
        validation_result = await controller.validate_promo_code(
            code=code,
            restaurant_id=restaurant_id,
            order_total=order_total
        )
        
        return {
            "valid": validation_result["valid"],
            "discount_amount": validation_result.get("discount_amount", 0),
            "final_total": validation_result.get("final_total", order_total),
            "error": validation_result.get("error")
        }
    except Exception as e:
        logger.error(f"Failed to quick validate promo code: {e}", "DiscountsRouter")
        return {
            "valid": False,
            "discount_amount": 0,
            "final_total": order_total,
            "error": "Validation service unavailable"
        }
