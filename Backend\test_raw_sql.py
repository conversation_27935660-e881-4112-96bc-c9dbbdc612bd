#!/usr/bin/env python3
"""
Test raw SQL database access to verify database integration works.
This bypasses SQLAlchemy ORM to test basic database connectivity.
"""

import asyncio
import aiosqlite
import json
from pathlib import Path

async def test_raw_database_access():
    """Test direct database access using raw SQL"""
    print("🔍 Testing Raw Database Access")
    print("=" * 60)
    
    # Database path
    db_path = Path("restro_manage.db")
    
    if not db_path.exists():
        print("❌ Database file not found!")
        return False
    
    try:
        # Connect to database
        async with aiosqlite.connect(str(db_path)) as db:
            print("✅ Database connection successful")
            
            # Test 1: Get restaurants
            print("\n1. Testing Restaurants...")
            cursor = await db.execute("SELECT id, name, code FROM restaurants LIMIT 5")
            restaurants = await cursor.fetchall()
            print(f"   Found {len(restaurants)} restaurants")
            if restaurants:
                print(f"   First restaurant: {restaurants[0][1]} (Code: {restaurants[0][2]})")
            
            # Test 2: Get menu items
            print("\n2. Testing Menu Items...")
            cursor = await db.execute("SELECT id, name, price, category FROM menu_items LIMIT 5")
            menu_items = await cursor.fetchall()
            print(f"   Found {len(menu_items)} menu items")
            if menu_items:
                for item in menu_items:
                    print(f"   - {item[1]}: £{item[2]} ({item[3]})")
            
            # Test 3: Get tables
            print("\n3. Testing Tables...")
            cursor = await db.execute("SELECT id, number, capacity, location, status FROM tables LIMIT 5")
            tables = await cursor.fetchall()
            print(f"   Found {len(tables)} tables")
            if tables:
                for table in tables:
                    print(f"   - Table {table[1]}: {table[2]} seats, {table[3]}, {table[4]}")
            
            # Test 4: Test join query
            print("\n4. Testing Join Query...")
            cursor = await db.execute("""
                SELECT r.name as restaurant_name, COUNT(m.id) as menu_count
                FROM restaurants r
                LEFT JOIN menu_items m ON r.id = m.restaurant_id
                GROUP BY r.id, r.name
                LIMIT 5
            """)
            restaurant_menu_counts = await cursor.fetchall()
            print(f"   Restaurant menu counts:")
            for row in restaurant_menu_counts:
                print(f"   - {row[0]}: {row[1]} menu items")
            
            print("\n✅ All raw SQL tests passed!")
            return True
            
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

async def test_api_endpoints():
    """Test API endpoints using aiohttp"""
    import aiohttp
    
    print("\n🌐 Testing API Endpoints")
    print("=" * 60)
    
    base_url = "http://127.0.0.1:8000"
    
    async with aiohttp.ClientSession() as session:
        # Test health endpoint
        try:
            async with session.get(f"{base_url}/health") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Health Check: {data.get('status', 'unknown')}")
                else:
                    print(f"❌ Health Check failed: {response.status}")
        except Exception as e:
            print(f"❌ Health Check error: {e}")
        
        # Test restaurants endpoint
        try:
            async with session.get(f"{base_url}/api/restaurants/") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Restaurants API: Found {len(data)} restaurants")
                else:
                    print(f"❌ Restaurants API failed: {response.status}")
        except Exception as e:
            print(f"❌ Restaurants API error: {e}")

if __name__ == "__main__":
    async def main():
        print("🚀 RestroManage Database Integration Test")
        print("=" * 60)
        
        # Test raw database access
        db_success = await test_raw_database_access()
        
        if db_success:
            # Test API endpoints
            await test_api_endpoints()
        
        print("\n" + "=" * 60)
        print("🎯 Test Complete")
    
    asyncio.run(main())
