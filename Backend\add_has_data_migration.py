#!/usr/bin/env python3
"""
Migration script to add has_data column to restaurants table
and set appropriate values for established vs new restaurants.
"""

import sys
import os
import asyncio
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database import AsyncSessionLocal, engine
from app.models.database_models import Restaurant
from sqlalchemy import text
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def run_migration():
    """Run the migration to add has_data column and update existing restaurants"""

    logger.info("Starting has_data migration...")

    # Get database session
    async with AsyncSessionLocal() as db:
        try:
            # Step 1: Add has_data column if it doesn't exist
            logger.info("Adding has_data column to restaurants table...")
            try:
                await db.execute(text('ALTER TABLE restaurants ADD COLUMN has_data BOOLEAN DEFAULT FALSE'))
                await db.commit()
                logger.info("✅ Added has_data column successfully")
            except Exception as e:
                if "duplicate column name" in str(e).lower() or "already exists" in str(e).lower():
                    logger.info("ℹ️  has_data column already exists, skipping creation")
                    await db.rollback()
                else:
                    logger.error(f"❌ Error adding column: {e}")
                    await db.rollback()
                    raise

            # Step 2: Update established restaurants to have has_data = True
            established_codes = ['GK001', 'SS003', 'PP002']

            logger.info("Updating established restaurants...")
            for code in established_codes:
                result = await db.execute(text("SELECT * FROM restaurants WHERE code = :code"), {"code": code})
                restaurant_data = result.fetchone()
                if restaurant_data:
                    await db.execute(text("UPDATE restaurants SET has_data = TRUE WHERE code = :code"), {"code": code})
                    logger.info(f"✅ Updated {code} - has_data = True")
                else:
                    logger.warning(f"⚠️  Restaurant {code} not found in database")

            # Step 3: Set has_data = False for all other restaurants
            logger.info("Updating new/other restaurants...")
            await db.execute(text("UPDATE restaurants SET has_data = FALSE WHERE code NOT IN ('GK001', 'SS003', 'PP002')"))

            # Step 4: Commit all changes
            await db.commit()
            logger.info("✅ Migration completed successfully!")

            # Step 5: Verify the changes
            logger.info("\n📊 Verification - Current restaurant status:")
            result = await db.execute(text("SELECT code, name, has_data FROM restaurants ORDER BY code"))
            restaurants = result.fetchall()

            established_count = 0
            new_count = 0

            for restaurant in restaurants:
                status = "ESTABLISHED" if restaurant.has_data else "NEW"
                logger.info(f"  {restaurant.code} ({restaurant.name}) - {status}")

                if restaurant.has_data:
                    established_count += 1
                else:
                    new_count += 1

            logger.info(f"\n📈 Summary:")
            logger.info(f"  Established restaurants (has_data=True): {established_count}")
            logger.info(f"  New restaurants (has_data=False): {new_count}")
            logger.info(f"  Total restaurants: {len(restaurants)}")

        except Exception as e:
            logger.error(f"❌ Migration failed: {e}")
            await db.rollback()
            raise

if __name__ == "__main__":
    try:
        asyncio.run(run_migration())
        print("\n🎉 Migration completed successfully!")
        print("✅ Established restaurants (GK001, SS003, PP002) will show full UI")
        print("✅ New restaurants (PU837, etc.) will show onboarding UI")
    except Exception as e:
        print(f"\n❌ Migration failed: {e}")
        sys.exit(1)
