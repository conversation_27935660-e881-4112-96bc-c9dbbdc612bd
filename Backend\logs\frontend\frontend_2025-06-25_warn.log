{"timestamp": "2025-06-25T06:17:30.877Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750832250048_6v7el9id0", "url": "http://localhost:5175/", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T06:17:30.928Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750832250048_6v7el9id0", "url": "http://localhost:5175/", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:13:27.653Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750835606033_prgipru6n", "url": "http://localhost:5175/", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1280, "height": 800, "colorDepth": 24}, "viewport": {"width": 944, "height": 735}}
{"timestamp": "2025-06-25T07:13:27.705Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750835606033_prgipru6n", "url": "http://localhost:5175/", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1280, "height": 800, "colorDepth": 24}, "viewport": {"width": 944, "height": 735}}
{"timestamp": "2025-06-25T07:13:28.380Z", "level": "warn", "component": "AuthContext", "message": "\ud83d\ude80 Development mode: Restoring user session without token validation", "sessionId": "session_1750835606033_prgipru6n", "url": "http://localhost:5175/", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1280, "height": 800, "colorDepth": 24}, "viewport": {"width": 944, "height": 735}}
{"timestamp": "2025-06-25T07:18:21.350Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:18:21.410Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:18:21.959Z", "level": "warn", "component": "AuthContext", "message": "\ud83d\ude80 Development mode: Restoring user session without token validation", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:38:39.384Z", "level": "warn", "component": "AuthContext", "message": "\ud83d\ude80 Development mode: Restoring user session without token validation", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/admin", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:38:40.418Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/admin", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:38:50.410Z", "level": "warn", "component": "AuthContext", "message": "\ud83d\ude80 Development mode: Restoring user session without token validation", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/admin", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:38:51.350Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/admin", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:39:09.763Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/admin", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:39:09.792Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/admin", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:39:10.342Z", "level": "warn", "component": "AuthContext", "message": "\ud83d\ude80 Development mode: Restoring user session without token validation", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/admin", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:39:23.172Z", "level": "warn", "component": "AuthContext", "message": "\ud83d\ude80 Development mode: Restoring user session without token validation", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/admin", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:39:24.107Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/admin", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:40:36.139Z", "level": "warn", "component": "AuthContext", "message": "\ud83d\ude80 Development mode: Restoring user session without token validation", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/admin", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:40:37.242Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/admin", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:41:57.387Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/admin", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:42:12.565Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/admin", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:42:32.630Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/admin", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:43:33.690Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/admin", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:44:53.549Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/admin", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:46:59.416Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750837618356_g92cwo5dz", "url": "http://localhost:5175/", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:46:59.480Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750837618356_g92cwo5dz", "url": "http://localhost:5175/", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:46:59.942Z", "level": "warn", "component": "AuthContext", "message": "\ud83d\ude80 Development mode: Restoring user session without token validation", "sessionId": "session_1750837618356_g92cwo5dz", "url": "http://localhost:5175/", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:47:28.934Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750837618356_g92cwo5dz", "url": "http://localhost:5175/login", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:47:29.022Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750837618356_g92cwo5dz", "url": "http://localhost:5175/login", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T07:47:29.461Z", "level": "warn", "component": "AuthContext", "message": "\ud83d\ude80 Development mode: Restoring user session without token validation", "sessionId": "session_1750837618356_g92cwo5dz", "url": "http://localhost:5175/login", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T08:08:43.237Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750837618356_g92cwo5dz", "url": "http://localhost:5175/admin/analytics", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T08:08:44.067Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/admin", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T08:09:12.031Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750837618356_g92cwo5dz", "url": "http://localhost:5175/admin/analytics", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T08:09:12.915Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/admin", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T08:10:14.401Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750837618356_g92cwo5dz", "url": "http://localhost:5175/admin/analytics", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T08:10:15.655Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/admin", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T08:10:32.940Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750837618356_g92cwo5dz", "url": "http://localhost:5175/admin/analytics", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T08:10:33.806Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/admin", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T08:11:31.056Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750837618356_g92cwo5dz", "url": "http://localhost:5175/admin/analytics", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T08:11:31.918Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/admin", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T08:11:48.125Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750837618356_g92cwo5dz", "url": "http://localhost:5175/admin/analytics", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T08:11:48.800Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750835899812_ni1d0r98q", "url": "http://localhost:5175/admin", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T10:48:15.538Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750837618356_g92cwo5dz", "url": "http://localhost:5175/admin/analytics", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T10:48:15.597Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750837618356_g92cwo5dz", "url": "http://localhost:5175/admin/analytics", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T10:48:16.141Z", "level": "warn", "component": "ProtectedRoute", "message": "No user authenticated, redirecting to staff login", "sessionId": "session_1750837618356_g92cwo5dz", "url": "http://localhost:5175/admin/analytics", "data": {"path": "/admin/analytics", "isAuthenticated": "[REDACTED]", "hasUser": false}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T10:48:16.184Z", "level": "warn", "component": "ProtectedRoute", "message": "No user authenticated, redirecting to staff login", "sessionId": "session_1750837618356_g92cwo5dz", "url": "http://localhost:5175/admin/analytics", "data": {"path": "/admin/analytics", "isAuthenticated": "[REDACTED]", "hasUser": false}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T11:52:50.296Z", "level": "warn", "component": "Analytics", "message": "Backend connection failed, using fallback data", "sessionId": "session_1750852361790_cao4hqcak", "url": "http://localhost:5175/admin/analytics", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T11:52:50.836Z", "level": "warn", "component": "Analytics", "message": "Backend connection failed, using fallback data", "sessionId": "session_1750852361790_cao4hqcak", "url": "http://localhost:5175/admin/analytics", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T12:32:42.135Z", "level": "warn", "component": "ApiService", "message": "Backend connectivity check failed", "sessionId": "session_1750852361790_cao4hqcak", "url": "http://localhost:5175/staff-login", "data": {"error": "signal is aborted without reason"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T12:33:17.161Z", "level": "warn", "component": "ApiService", "message": "Backend connectivity check failed", "sessionId": "session_1750852361790_cao4hqcak", "url": "http://localhost:5175/staff-login", "data": {"error": "signal is aborted without reason"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T12:33:00.153Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750852361790_cao4hqcak", "url": "http://localhost:5175/staff-login", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T12:31:45.786Z", "level": "warn", "component": "ProtectedRoute", "message": "No user authenticated, redirecting to staff login", "sessionId": "session_1750852361790_cao4hqcak", "url": "http://localhost:5175/admin/discounts", "data": {"path": "/admin/discounts", "isAuthenticated": "[REDACTED]", "hasUser": false}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T12:32:42.188Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750852361790_cao4hqcak", "url": "http://localhost:5175/staff-login", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T12:31:46.520Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750852361790_cao4hqcak", "url": "http://localhost:5175/staff-login", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T12:31:45.129Z", "level": "warn", "component": "ApiService", "message": "Backend connectivity check failed", "sessionId": "session_1750852361790_cao4hqcak", "url": "http://localhost:5175/admin/discounts", "data": {"error": "signal is aborted without reason"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T12:33:57.169Z", "level": "warn", "component": "ApiService", "message": "Backend connectivity check failed", "sessionId": "session_1750852361790_cao4hqcak", "url": "http://localhost:5175/staff-login", "data": {"error": "signal is aborted without reason"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T12:31:45.761Z", "level": "warn", "component": "ProtectedRoute", "message": "No user authenticated, redirecting to staff login", "sessionId": "session_1750852361790_cao4hqcak", "url": "http://localhost:5175/admin/discounts", "data": {"path": "/admin/discounts", "isAuthenticated": "[REDACTED]", "hasUser": false}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T12:33:17.150Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750852361790_cao4hqcak", "url": "http://localhost:5175/staff-login", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T12:32:14.184Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750852361790_cao4hqcak", "url": "http://localhost:5175/staff-login", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T12:33:57.183Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750852361790_cao4hqcak", "url": "http://localhost:5175/staff-login", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T12:33:00.180Z", "level": "warn", "component": "ApiService", "message": "Backend connectivity check failed", "sessionId": "session_1750852361790_cao4hqcak", "url": "http://localhost:5175/staff-login", "data": {"error": "signal is aborted without reason"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T12:33:00.137Z", "level": "warn", "component": "ApiService", "message": "Backend connectivity check failed", "sessionId": "session_1750852361790_cao4hqcak", "url": "http://localhost:5175/staff-login", "data": {"error": "signal is aborted without reason"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T12:31:45.145Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750852361790_cao4hqcak", "url": "http://localhost:5175/admin/discounts", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T12:33:00.190Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750852361790_cao4hqcak", "url": "http://localhost:5175/staff-login", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T12:32:42.174Z", "level": "warn", "component": "ApiService", "message": "Backend connectivity check failed", "sessionId": "session_1750852361790_cao4hqcak", "url": "http://localhost:5175/staff-login", "data": {"error": "signal is aborted without reason"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T12:33:57.157Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750852361790_cao4hqcak", "url": "http://localhost:5175/staff-login", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T12:33:17.123Z", "level": "warn", "component": "ApiService", "message": "Backend connectivity check failed", "sessionId": "session_1750852361790_cao4hqcak", "url": "http://localhost:5175/staff-login", "data": {"error": "signal is aborted without reason"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T12:32:42.164Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750852361790_cao4hqcak", "url": "http://localhost:5175/staff-login", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T12:31:46.494Z", "level": "warn", "component": "ApiService", "message": "Backend connectivity check failed", "sessionId": "session_1750852361790_cao4hqcak", "url": "http://localhost:5175/staff-login", "data": {"error": "signal is aborted without reason"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T12:33:17.175Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750852361790_cao4hqcak", "url": "http://localhost:5175/staff-login", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T12:32:14.128Z", "level": "warn", "component": "ApiService", "message": "Backend connectivity check failed", "sessionId": "session_1750852361790_cao4hqcak", "url": "http://localhost:5175/staff-login", "data": {"error": "signal is aborted without reason"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T12:33:57.130Z", "level": "warn", "component": "ApiService", "message": "Backend connectivity check failed", "sessionId": "session_1750852361790_cao4hqcak", "url": "http://localhost:5175/staff-login", "data": {"error": "signal is aborted without reason"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T12:32:14.145Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750852361790_cao4hqcak", "url": "http://localhost:5175/staff-login", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T12:32:14.173Z", "level": "warn", "component": "ApiService", "message": "Backend connectivity check failed", "sessionId": "session_1750852361790_cao4hqcak", "url": "http://localhost:5175/staff-login", "data": {"error": "signal is aborted without reason"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T14:38:21.091Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750862216181_tldbnakeb", "url": "http://localhost:5175/", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1280, "height": 800, "colorDepth": 24}, "viewport": {"width": 944, "height": 735}}
{"timestamp": "2025-06-25T14:38:21.560Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750862216181_tldbnakeb", "url": "http://localhost:5175/", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1280, "height": 800, "colorDepth": 24}, "viewport": {"width": 944, "height": 735}}
{"timestamp": "2025-06-25T14:49:44.222Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750862983429_qf4726xrz", "url": "http://localhost:5176/", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T14:49:44.257Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750862983429_qf4726xrz", "url": "http://localhost:5176/", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T15:10:24.768Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750864223018_opzlrystu", "url": "http://localhost:5176/", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-25T15:10:24.835Z", "level": "warn", "component": "AuthContext", "message": "\u26a0\ufe0f Backend unavailable, falling back to data.json", "sessionId": "session_1750864223018_opzlrystu", "url": "http://localhost:5176/", "data": null, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
