import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import MarketingLayout from "@/components/marketing/MarketingLayout";

interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  image: string;
  category: string;
  tags: string[];
  popular?: boolean;
  vegetarian?: boolean;
  vegan?: boolean;
  glutenFree?: boolean;
  spicy?: boolean;
}

const Menu = () => {
  const [activeCategory, setActiveCategory] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");

  // Mock menu data
  const menuItems: MenuItem[] = [
    {
      id: "1",
      name: "Classic Margherita Pizza",
      description: "Fresh mozzarella, tomato sauce, basil, and extra virgin olive oil",
      price: 14.99,
      image: "/images/menu/margherita.jpg",
      category: "pizza",
      tags: ["italian", "vegetarian"],
      popular: true,
      vegetarian: true
    },
    {
      id: "2",
      name: "Pepperoni Pizza",
      description: "Tomato sauce, mozzarella, and pepperoni",
      price: 16.99,
      image: "/images/menu/pepperoni.jpg",
      category: "pizza",
      tags: ["italian", "meat"],
      popular: true
    },
    {
      id: "3",
      name: "Vegetable Supreme Pizza",
      description: "Bell peppers, onions, mushrooms, olives, and tomatoes",
      price: 15.99,
      image: "/images/menu/veggie.jpg",
      category: "pizza",
      tags: ["italian", "vegetarian"],
      vegetarian: true
    },
    {
      id: "4",
      name: "Spaghetti Carbonara",
      description: "Pancetta, egg, parmesan, and black pepper",
      price: 18.99,
      image: "/images/menu/carbonara.jpg",
      category: "pasta",
      tags: ["italian"]
    },
    {
      id: "5",
      name: "Fettuccine Alfredo",
      description: "Creamy parmesan sauce with garlic and butter",
      price: 17.99,
      image: "/images/menu/alfredo.jpg",
      category: "pasta",
      tags: ["italian", "vegetarian"],
      vegetarian: true
    },
    {
      id: "6",
      name: "Penne Arrabbiata",
      description: "Spicy tomato sauce with garlic and red chili",
      price: 16.99,
      image: "/images/menu/arrabbiata.jpg",
      category: "pasta",
      tags: ["italian", "vegetarian", "spicy"],
      vegetarian: true,
      spicy: true
    },
    {
      id: "7",
      name: "Caesar Salad",
      description: "Romaine lettuce, croutons, parmesan, and Caesar dressing",
      price: 12.99,
      image: "/images/menu/caesar.jpg",
      category: "salad",
      tags: ["vegetarian"],
      vegetarian: true
    },
    {
      id: "8",
      name: "Greek Salad",
      description: "Cucumber, tomato, olives, feta cheese, and olive oil",
      price: 13.99,
      image: "/images/menu/greek.jpg",
      category: "salad",
      tags: ["vegetarian"],
      vegetarian: true
    },
    {
      id: "9",
      name: "Tiramisu",
      description: "Coffee-soaked ladyfingers layered with mascarpone cream",
      price: 8.99,
      image: "/images/menu/tiramisu.jpg",
      category: "dessert",
      tags: ["italian", "vegetarian"],
      vegetarian: true,
      popular: true
    },
    {
      id: "10",
      name: "Chocolate Lava Cake",
      description: "Warm chocolate cake with a molten center, served with vanilla ice cream",
      price: 9.99,
      image: "/images/menu/lava-cake.jpg",
      category: "dessert",
      tags: ["vegetarian"],
      vegetarian: true
    },
    {
      id: "11",
      name: "Garlic Bread",
      description: "Toasted bread with garlic butter and herbs",
      price: 6.99,
      image: "/images/menu/garlic-bread.jpg",
      category: "appetizer",
      tags: ["vegetarian"],
      vegetarian: true
    },
    {
      id: "12",
      name: "Bruschetta",
      description: "Toasted bread topped with diced tomatoes, garlic, basil, and olive oil",
      price: 8.99,
      image: "/images/menu/bruschetta.jpg",
      category: "appetizer",
      tags: ["italian", "vegetarian"],
      vegetarian: true
    }
  ];

  // Filter menu items based on active category and search query
  const filteredItems = menuItems.filter((item) => {
    const matchesCategory = activeCategory === "all" || item.category === activeCategory;
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          item.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          item.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    return matchesCategory && matchesSearch;
  });

  return (
    <MarketingLayout>
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">Our Menu</h1>
            <p className="text-xl mb-8">
              Explore our delicious offerings, made with fresh ingredients and passion
            </p>
          </div>
        </div>
      </section>

      {/* Menu Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            {/* Search and Filters */}
            <div className="mb-12">
              <div className="flex flex-col md:flex-row gap-4 justify-between">
                <div className="relative w-full md:w-72">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    type="search"
                    placeholder="Search menu..."
                    className="pl-10"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <div className="flex flex-wrap gap-2">
                  <Badge
                    variant={activeCategory === "all" ? "default" : "outline"}
                    className="cursor-pointer"
                    onClick={() => setActiveCategory("all")}
                  >
                    All
                  </Badge>
                  <Badge
                    variant={activeCategory === "appetizer" ? "default" : "outline"}
                    className="cursor-pointer"
                    onClick={() => setActiveCategory("appetizer")}
                  >
                    Appetizers
                  </Badge>
                  <Badge
                    variant={activeCategory === "pizza" ? "default" : "outline"}
                    className="cursor-pointer"
                    onClick={() => setActiveCategory("pizza")}
                  >
                    Pizza
                  </Badge>
                  <Badge
                    variant={activeCategory === "pasta" ? "default" : "outline"}
                    className="cursor-pointer"
                    onClick={() => setActiveCategory("pasta")}
                  >
                    Pasta
                  </Badge>
                  <Badge
                    variant={activeCategory === "salad" ? "default" : "outline"}
                    className="cursor-pointer"
                    onClick={() => setActiveCategory("salad")}
                  >
                    Salads
                  </Badge>
                  <Badge
                    variant={activeCategory === "dessert" ? "default" : "outline"}
                    className="cursor-pointer"
                    onClick={() => setActiveCategory("dessert")}
                  >
                    Desserts
                  </Badge>
                </div>
              </div>
            </div>

            {/* Menu Items */}
            {filteredItems.length === 0 ? (
              <div className="text-center py-12">
                <h3 className="text-xl font-semibold mb-2">No items found</h3>
                <p className="text-gray-600">Try adjusting your search or filters</p>
              </div>
            ) : (
              <div className="grid md:grid-cols-2 gap-8">
                {filteredItems.map((item) => (
                  <MenuItemCard key={item.id} item={item} />
                ))}
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Dietary Information */}
      <section className="py-12 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-2xl font-bold mb-6 text-center text-foreground">Dietary Information</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-card p-4 rounded-lg shadow-sm">
                <div className="flex items-center gap-2">
                  <span className="w-3 h-3 rounded-full bg-green-500"></span>
                  <span className="font-medium text-card-foreground">Vegetarian</span>
                </div>
                <p className="text-sm text-muted-foreground mt-1">
                  Dishes that contain no meat or fish
                </p>
              </div>
              <div className="bg-card p-4 rounded-lg shadow-sm">
                <div className="flex items-center gap-2">
                  <span className="w-3 h-3 rounded-full bg-green-700"></span>
                  <span className="font-medium text-card-foreground">Vegan</span>
                </div>
                <p className="text-sm text-muted-foreground mt-1">
                  Dishes that contain no animal products
                </p>
              </div>
              <div className="bg-card p-4 rounded-lg shadow-sm">
                <div className="flex items-center gap-2">
                  <span className="w-3 h-3 rounded-full bg-yellow-500"></span>
                  <span className="font-medium text-card-foreground">Gluten-Free</span>
                </div>
                <p className="text-sm text-muted-foreground mt-1">
                  Dishes that contain no gluten
                </p>
              </div>
              <div className="bg-card p-4 rounded-lg shadow-sm">
                <div className="flex items-center gap-2">
                  <span className="w-3 h-3 rounded-full bg-red-500"></span>
                  <span className="font-medium text-card-foreground">Spicy</span>
                </div>
                <p className="text-sm text-muted-foreground mt-1">
                  Dishes that have a spicy flavor
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-blue-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">Ready to Taste Our Delicious Food?</h2>
          <p className="text-xl text-white/80 max-w-3xl mx-auto mb-8">
            Join us for a memorable dining experience or order online for delivery.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-white text-blue-600 hover:bg-white/90">
              Order Online
            </Button>
            <Button size="lg" variant="outline" className="text-white border-white hover:bg-white/10">
              Make a Reservation
            </Button>
          </div>
        </div>
      </section>
    </MarketingLayout>
  );
};

// Helper Component
const MenuItemCard = ({ item }: { item: MenuItem }) => {
  return (
    <div className="flex flex-col md:flex-row gap-4 p-4 border border-border rounded-lg hover:shadow-md transition-shadow bg-card">
      <div className="md:w-1/3 h-48 md:h-auto rounded-md overflow-hidden bg-muted">
        <img
          src={item.image}
          alt={item.name}
          className="w-full h-full object-cover"
          onError={(e) => {
            e.currentTarget.src = `https://placehold.co/300x300/3b82f6/ffffff?text=${item.category.charAt(0).toUpperCase()}`;
          }}
        />
      </div>
      <div className="md:w-2/3 flex flex-col">
        <div className="flex justify-between items-start mb-2">
          <h3 className="text-xl font-semibold text-card-foreground">{item.name}</h3>
          <span className="font-bold text-card-foreground">£{item.price.toFixed(2)}</span>
        </div>
        <p className="text-muted-foreground mb-4">{item.description}</p>
        <div className="mt-auto flex flex-wrap gap-2">
          {item.popular && (
            <Badge className="bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 hover:bg-blue-100 dark:hover:bg-blue-900/50">
              Popular
            </Badge>
          )}
          {item.vegetarian && (
            <Badge className="bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 hover:bg-green-100 dark:hover:bg-green-900/50">
              Vegetarian
            </Badge>
          )}
          {item.vegan && (
            <Badge className="bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 hover:bg-green-100 dark:hover:bg-green-900/50">
              Vegan
            </Badge>
          )}
          {item.glutenFree && (
            <Badge className="bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200 hover:bg-yellow-100 dark:hover:bg-yellow-900/50">
              Gluten-Free
            </Badge>
          )}
          {item.spicy && (
            <Badge className="bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200 hover:bg-red-100 dark:hover:bg-red-900/50">
              Spicy
            </Badge>
          )}
        </div>
      </div>
    </div>
  );
};

export default Menu;
