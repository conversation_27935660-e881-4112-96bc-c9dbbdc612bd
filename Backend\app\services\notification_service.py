"""
Notification service for RestroManage.
Handles notification CRUD operations with restaurant ID isolation.
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import and_, desc, asc, func
from datetime import datetime, timezone
from fastapi import Depends

from app.models.database_models import Notification, NotificationType, NotificationPriority
from app.models.notifications import (
    NotificationCreate,
    NotificationUpdate,
    NotificationResponse,
    NotificationFilter,
    NotificationStats
)
from app.database import get_db_session
from app.utils.logging_config import logger


class NotificationService:
    """Service class for notification management"""

    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_notification(
        self, 
        restaurant_id: str, 
        notification_data: NotificationCreate
    ) -> NotificationResponse:
        """Create a new notification for a restaurant"""
        try:
            # Create notification instance
            notification = Notification(
                restaurant_id=restaurant_id,
                title=notification_data.title,
                message=notification_data.message,
                type=notification_data.type,
                priority=notification_data.priority,
                link=notification_data.link,
                related_id=notification_data.related_id,
                is_read=False
            )
            
            # Add to database
            self.db.add(notification)
            await self.db.commit()
            await self.db.refresh(notification)
            
            logger.info(
                f"Created notification for restaurant {restaurant_id}",
                "NotificationService",
                {"notification_id": notification.id, "type": notification.type.value}
            )
            
            return NotificationResponse.model_validate(notification)
            
        except Exception as e:
            await self.db.rollback()
            logger.error(
                f"Failed to create notification: {str(e)}",
                "NotificationService",
                {"restaurant_id": restaurant_id, "error": str(e)}
            )
            raise
    
    async def get_notifications(
        self, 
        restaurant_id: str, 
        filters: Optional[NotificationFilter] = None
    ) -> List[NotificationResponse]:
        """Get notifications for a restaurant with optional filtering"""
        try:
            from sqlalchemy import select
            query = select(Notification).filter(
                Notification.restaurant_id == restaurant_id
            )
            
            # Apply filters if provided
            if filters:
                if filters.type:
                    query = query.filter(Notification.type == filters.type)
                if filters.priority:
                    query = query.filter(Notification.priority == filters.priority)
                if filters.is_read is not None:
                    query = query.filter(Notification.is_read == filters.is_read)
                
                # Apply ordering
                order_field = getattr(Notification, filters.order_by, Notification.created_at)
                if filters.order_desc:
                    query = query.order_by(desc(order_field))
                else:
                    query = query.order_by(asc(order_field))
                
                # Apply pagination
                query = query.offset(filters.offset).limit(filters.limit)
            else:
                # Default ordering: newest first
                query = query.order_by(desc(Notification.created_at))
            
            notifications = query.all()
            
            logger.debug(
                f"Retrieved {len(notifications)} notifications for restaurant {restaurant_id}",
                "NotificationService",
                {"restaurant_id": restaurant_id, "count": len(notifications)}
            )
            
            return [NotificationResponse.model_validate(notification) for notification in notifications]
            
        except Exception as e:
            logger.error(
                f"Failed to get notifications: {str(e)}",
                "NotificationService",
                {"restaurant_id": restaurant_id, "error": str(e)}
            )
            raise
    
    async def get_notification(
        self, 
        restaurant_id: str, 
        notification_id: str
    ) -> Optional[NotificationResponse]:
        """Get a specific notification by ID"""
        try:
            notification = self.db.query(Notification).filter(
                and_(
                    Notification.id == notification_id,
                    Notification.restaurant_id == restaurant_id
                )
            ).first()
            
            if notification:
                return NotificationResponse.model_validate(notification)
            return None
            
        except Exception as e:
            logger.error(
                f"Failed to get notification: {str(e)}",
                "NotificationService",
                {"restaurant_id": restaurant_id, "notification_id": notification_id, "error": str(e)}
            )
            raise
    
    async def update_notification(
        self, 
        restaurant_id: str, 
        notification_id: str, 
        update_data: NotificationUpdate
    ) -> Optional[NotificationResponse]:
        """Update a notification"""
        try:
            notification = self.db.query(Notification).filter(
                and_(
                    Notification.id == notification_id,
                    Notification.restaurant_id == restaurant_id
                )
            ).first()
            
            if not notification:
                return None
            
            # Update fields
            update_dict = update_data.model_dump(exclude_unset=True)
            for field, value in update_dict.items():
                setattr(notification, field, value)

            notification.updated_at = datetime.now(timezone.utc)
            
            self.db.commit()
            self.db.refresh(notification)
            
            logger.info(
                f"Updated notification {notification_id}",
                "NotificationService",
                {"notification_id": notification_id, "restaurant_id": restaurant_id}
            )
            
            return NotificationResponse.model_validate(notification)
            
        except Exception as e:
            self.db.rollback()
            logger.error(
                f"Failed to update notification: {str(e)}",
                "NotificationService",
                {"restaurant_id": restaurant_id, "notification_id": notification_id, "error": str(e)}
            )
            raise
    
    async def delete_notification(
        self, 
        restaurant_id: str, 
        notification_id: str
    ) -> bool:
        """Delete a notification"""
        try:
            notification = self.db.query(Notification).filter(
                and_(
                    Notification.id == notification_id,
                    Notification.restaurant_id == restaurant_id
                )
            ).first()
            
            if not notification:
                return False
            
            self.db.delete(notification)
            self.db.commit()
            
            logger.info(
                f"Deleted notification {notification_id}",
                "NotificationService",
                {"notification_id": notification_id, "restaurant_id": restaurant_id}
            )
            
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(
                f"Failed to delete notification: {str(e)}",
                "NotificationService",
                {"restaurant_id": restaurant_id, "notification_id": notification_id, "error": str(e)}
            )
            raise
    
    async def mark_as_read(
        self, 
        restaurant_id: str, 
        notification_ids: List[str]
    ) -> int:
        """Mark multiple notifications as read"""
        try:
            updated_count = self.db.query(Notification).filter(
                and_(
                    Notification.id.in_(notification_ids),
                    Notification.restaurant_id == restaurant_id,
                    Notification.is_read == False
                )
            ).update(
                {"is_read": True, "updated_at": datetime.now(timezone.utc)},
                synchronize_session=False
            )
            
            self.db.commit()
            
            logger.info(
                f"Marked {updated_count} notifications as read",
                "NotificationService",
                {"restaurant_id": restaurant_id, "count": updated_count}
            )
            
            return updated_count
            
        except Exception as e:
            self.db.rollback()
            logger.error(
                f"Failed to mark notifications as read: {str(e)}",
                "NotificationService",
                {"restaurant_id": restaurant_id, "error": str(e)}
            )
            raise
    
    async def mark_all_as_read(self, restaurant_id: str) -> int:
        """Mark all notifications as read for a restaurant"""
        try:
            updated_count = self.db.query(Notification).filter(
                and_(
                    Notification.restaurant_id == restaurant_id,
                    Notification.is_read == False
                )
            ).update(
                {"is_read": True, "updated_at": datetime.now(timezone.utc)},
                synchronize_session=False
            )
            
            self.db.commit()
            
            logger.info(
                f"Marked all {updated_count} notifications as read",
                "NotificationService",
                {"restaurant_id": restaurant_id, "count": updated_count}
            )
            
            return updated_count
            
        except Exception as e:
            self.db.rollback()
            logger.error(
                f"Failed to mark all notifications as read: {str(e)}",
                "NotificationService",
                {"restaurant_id": restaurant_id, "error": str(e)}
            )
            raise
    
    async def delete_all_notifications(self, restaurant_id: str) -> int:
        """Delete all notifications for a restaurant"""
        try:
            deleted_count = self.db.query(Notification).filter(
                Notification.restaurant_id == restaurant_id
            ).count()
            
            self.db.query(Notification).filter(
                Notification.restaurant_id == restaurant_id
            ).delete(synchronize_session=False)
            
            self.db.commit()
            
            logger.info(
                f"Deleted all {deleted_count} notifications",
                "NotificationService",
                {"restaurant_id": restaurant_id, "count": deleted_count}
            )
            
            return deleted_count
            
        except Exception as e:
            self.db.rollback()
            logger.error(
                f"Failed to delete all notifications: {str(e)}",
                "NotificationService",
                {"restaurant_id": restaurant_id, "error": str(e)}
            )
            raise
    
    async def get_notification_stats(self, restaurant_id: str) -> NotificationStats:
        """Get notification statistics for a restaurant"""
        try:
            # Total count
            total_count = self.db.query(Notification).filter(
                Notification.restaurant_id == restaurant_id
            ).count()
            
            # Unread count
            unread_count = self.db.query(Notification).filter(
                and_(
                    Notification.restaurant_id == restaurant_id,
                    Notification.is_read == False
                )
            ).count()
            
            # Count by type
            type_counts = self.db.query(
                Notification.type, func.count(Notification.id)
            ).filter(
                Notification.restaurant_id == restaurant_id
            ).group_by(Notification.type).all()
            
            by_type = {type_val.value: count for type_val, count in type_counts}
            
            # Count by priority
            priority_counts = self.db.query(
                Notification.priority, func.count(Notification.id)
            ).filter(
                Notification.restaurant_id == restaurant_id
            ).group_by(Notification.priority).all()
            
            by_priority = {priority_val.value: count for priority_val, count in priority_counts}
            
            return NotificationStats(
                total_count=total_count,
                unread_count=unread_count,
                by_type=by_type,
                by_priority=by_priority
            )
            
        except Exception as e:
            logger.error(
                f"Failed to get notification stats: {str(e)}",
                "NotificationService",
                {"restaurant_id": restaurant_id, "error": str(e)}
            )
            raise


# Dependency to get notification service
async def get_notification_service(db: AsyncSession = Depends(get_db_session)) -> NotificationService:
    """Dependency to get notification service instance"""
    return NotificationService(db)
