"""
Order models for RestroManage database.
Corresponds to app/models/orders.py Pydantic models.
"""

from sqlalchemy import Column, String, Boolean, Float, Text, JSON, ForeignKey, Integer, DateTime
from sqlalchemy.orm import relationship
from .base import BaseModel, TimestampMixin, StatusMixin, SoftDeleteMixin

class Order(BaseModel, TimestampMixin, StatusMixin, SoftDeleteMixin):
    """
    Order model for restaurant order management.
    Corresponds to Order Pydantic model in app/models/orders.py
    """
    __tablename__ = "orders"
    
    # Restaurant and table association
    restaurant_id = Column(String(36), ForeignKey("restaurants.id"), nullable=False, index=True)
    table_id = Column(String(36), ForeignKey("tables.id"), nullable=False, index=True)
    
    # Customer information
    customer_name = Column(String(255), nullable=True)
    customer_phone = Column(String(20), nullable=True)
    customer_email = Column(String(255), nullable=True)
    customer_id = Column(String(36), nullable=True, index=True)  # For registered customers
    
    # Order details
    order_number = Column(String(50), nullable=False, unique=True, index=True)
    special_instructions = Column(Text, nullable=True)
    
    # Status tracking
    status = Column(String(20), default="pending", nullable=False, index=True)
    # Status values: pending, preparing, ready, delivered, completed, cancelled
    payment_status = Column(String(20), default="unpaid", nullable=False, index=True)
    # Payment status: unpaid, partially_paid, paid, refunded
    
    # Financial information
    subtotal = Column(Float, nullable=False, default=0.0)
    tax_amount = Column(Float, nullable=False, default=0.0)
    tip_amount = Column(Float, nullable=False, default=0.0)
    discount_amount = Column(Float, nullable=False, default=0.0)
    total = Column(Float, nullable=False, default=0.0)
    
    # Promo codes and discounts
    promo_codes = Column(JSON, nullable=True)  # List of applied promo codes
    
    # Split bill information
    is_split_bill = Column(Boolean, default=False, nullable=False)
    split_bill_id = Column(String(36), ForeignKey("split_bills.id"), nullable=True, index=True)
    
    # Timing information
    estimated_prep_time = Column(Integer, nullable=True)  # minutes
    actual_prep_time = Column(Integer, nullable=True)     # minutes
    completed_at = Column(DateTime(timezone=True), nullable=True)
    delivered_at = Column(DateTime(timezone=True), nullable=True)
    
    # Staff assignment
    created_by_user_id = Column(String(36), ForeignKey("users.id"), nullable=True, index=True)
    assigned_chef_id = Column(String(36), ForeignKey("staff.id"), nullable=True, index=True)
    assigned_waiter_id = Column(String(36), ForeignKey("staff.id"), nullable=True, index=True)
    
    # Order source and type
    order_source = Column(String(20), default="dine_in", nullable=False)
    # Sources: dine_in, takeaway, delivery, online
    order_type = Column(String(20), default="regular", nullable=False)
    # Types: regular, catering, group, special_event
    
    # Kitchen and service notes
    kitchen_notes = Column(Text, nullable=True)
    service_notes = Column(Text, nullable=True)
    
    # Customer feedback
    customer_rating = Column(Integer, nullable=True)  # 1-5 scale
    customer_feedback = Column(Text, nullable=True)
    
    # Relationships
    restaurant = relationship("Restaurant", back_populates="orders")
    table = relationship("Table", back_populates="orders", foreign_keys=[table_id])
    created_by_user = relationship("User", back_populates="orders", foreign_keys=[created_by_user_id])
    order_items = relationship("OrderItem", back_populates="order", cascade="all, delete-orphan")
    order_discounts = relationship("OrderDiscount", back_populates="order", cascade="all, delete-orphan")
    split_bill = relationship("SplitBill", back_populates="original_order", foreign_keys="SplitBill.original_order_id")
    
    def __repr__(self):
        return f"<Order(id={self.id}, order_number={self.order_number}, total={self.total})>"

class OrderItem(BaseModel, TimestampMixin):
    """
    Order item model for individual items within an order.
    Corresponds to OrderItem Pydantic model in app/models/orders.py
    """
    __tablename__ = "order_items"
    
    # Order association
    order_id = Column(String(36), ForeignKey("orders.id"), nullable=False, index=True)
    
    # Menu item association
    menu_item_id = Column(String(36), ForeignKey("menu_items.id"), nullable=False, index=True)
    menu_item_name = Column(String(255), nullable=False)  # Denormalized for performance
    
    # Item details
    quantity = Column(Integer, nullable=False, default=1)
    unit_price = Column(Float, nullable=False)
    subtotal = Column(Float, nullable=False)
    
    # Customizations
    special_instructions = Column(Text, nullable=True)
    customizations = Column(JSON, nullable=True)  # Selected customizations
    size_option = Column(String(50), nullable=True)
    addons = Column(JSON, nullable=True)  # Selected add-ons
    
    # Status tracking
    status = Column(String(20), default="pending", nullable=False, index=True)
    # Status: pending, preparing, ready, served, cancelled
    
    # Kitchen information
    prep_time = Column(Integer, nullable=True)  # minutes
    kitchen_notes = Column(Text, nullable=True)
    allergen_notes = Column(Text, nullable=True)
    
    # Timing
    started_prep_at = Column(DateTime(timezone=True), nullable=True)
    completed_prep_at = Column(DateTime(timezone=True), nullable=True)
    served_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    order = relationship("Order", back_populates="order_items")
    menu_item = relationship("MenuItem", back_populates="order_items")
    
    def __repr__(self):
        return f"<OrderItem(id={self.id}, menu_item_name={self.menu_item_name}, quantity={self.quantity})>"

class OrderDiscount(BaseModel, TimestampMixin):
    """
    Order discount model for tracking applied discounts.
    Corresponds to DiscountApplication Pydantic model in app/models/orders.py
    """
    __tablename__ = "order_discounts"
    
    # Order association
    order_id = Column(String(36), ForeignKey("orders.id"), nullable=False, index=True)
    
    # Promo code association
    promo_code_id = Column(String(36), ForeignKey("promo_codes.id"), nullable=False, index=True)
    promo_code = Column(String(50), nullable=False, index=True)
    
    # Discount details
    discount_amount = Column(Float, nullable=False)
    discount_type = Column(String(20), nullable=False)  # percentage, fixed_amount
    discount_value = Column(Float, nullable=False)
    
    # Application details
    applied_to_items = Column(JSON, nullable=True)  # List of order item IDs
    applied_to_total = Column(Boolean, default=True, nullable=False)
    
    # Validation information
    minimum_spend_met = Column(Boolean, default=True, nullable=False)
    maximum_discount_applied = Column(Boolean, default=False, nullable=False)
    
    # Relationships
    order = relationship("Order", back_populates="order_discounts")
    promo_code = relationship("PromoCode", back_populates="order_discounts")
    
    def __repr__(self):
        return f"<OrderDiscount(id={self.id}, promo_code={self.promo_code}, discount_amount={self.discount_amount})>"
