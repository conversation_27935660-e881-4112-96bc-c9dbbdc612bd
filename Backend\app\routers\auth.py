from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from datetime import datetime, timedelta
from app.models.auth import Token, UserCreate, UserLogin, User
from app.utils.auth import (
    authenticate_user,
    create_access_token,
    create_refresh_token,
    get_password_hash,
    get_current_active_user
)
from app.utils.storage import create, query

router = APIRouter(prefix="/auth", tags=["Authentication"])

@router.post("/token", response_model=Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    user = authenticate_user(form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    access_token_expires = timedelta(minutes=30)
    access_token = create_access_token(
        data={"sub": user["id"]}, expires_delta=access_token_expires
    )
    
    # Calculate expiration timestamp
    expires_at = int((datetime.utcnow() + access_token_expires).timestamp())
    
    # Convert user dict to User model
    user_model = User(
        id=user["id"],
        username=user["username"],
        email=user["email"],
        full_name=user["full_name"],
        role=user["role"],
        is_active=user["is_active"],
        created_at=datetime.fromisoformat(user["created_at"]),
        updated_at=datetime.fromisoformat(user["updated_at"])
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_at": expires_at,
        "user": user_model
    }

@router.post("/register", response_model=User)
async def register_user(user_data: UserCreate):
    # Check if username already exists
    existing_users = query("users", {"username": user_data.username})
    if existing_users:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username already registered"
        )
    
    # Check if email already exists
    existing_emails = query("users", {"email": user_data.email})
    if existing_emails:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )
    
    # Create new user
    hashed_password = get_password_hash(user_data.password)
    user_dict = user_data.dict()
    user_dict.pop("password")
    user_dict["password_hash"] = hashed_password
    user_dict["is_active"] = True
    
    new_user = create("users", user_dict)
    
    # Convert to User model
    return User(
        id=new_user["id"],
        username=new_user["username"],
        email=new_user["email"],
        full_name=new_user["full_name"],
        role=new_user["role"],
        is_active=new_user["is_active"],
        created_at=datetime.fromisoformat(new_user["created_at"]),
        updated_at=datetime.fromisoformat(new_user["updated_at"])
    )

@router.get("/validate-token")
async def validate_token(current_user = Depends(get_current_active_user)):
    """Validate the current user's token"""
    return {
        "valid": True,
        "user_id": current_user["id"],
        "username": current_user.get("username", ""),
        "email": current_user.get("email", ""),
        "role": current_user.get("role", ""),
        "is_active": current_user.get("is_active", False)
    }

@router.post("/refresh", response_model=Token)
async def refresh_access_token(current_user = Depends(get_current_active_user)):
    """Refresh the access token for the current user"""
    access_token_expires = timedelta(minutes=30)
    access_token = create_access_token(
        data={"sub": current_user["id"]}, expires_delta=access_token_expires
    )

    # Calculate expiration timestamp
    expires_at = int((datetime.utcnow() + access_token_expires).timestamp())

    # Convert user dict to User model
    user_model = User(
        id=current_user["id"],
        username=current_user["username"],
        email=current_user["email"],
        full_name=current_user["full_name"],
        role=current_user["role"],
        is_active=current_user["is_active"],
        created_at=datetime.fromisoformat(current_user["created_at"]),
        updated_at=datetime.fromisoformat(current_user["updated_at"])
    )

    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_at": expires_at,
        "user": user_model
    }
