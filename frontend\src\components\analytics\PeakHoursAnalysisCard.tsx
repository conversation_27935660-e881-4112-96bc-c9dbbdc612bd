import {
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON>er,
  CardTitle
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, Area, AreaChart } from 'recharts';
import { Clock, TrendingUp, Users, DollarSign } from "lucide-react";

interface HourlyData {
  hour: number;
  totalOrders: number;
  totalRevenue: number;
  categories: {
    [category: string]: {
      orderCount: number;
      revenue: number;
      itemsSold: number;
    };
  };
}

interface PeakHoursAnalysisCardProps {
  data: HourlyData[];
  title?: string;
}

const PeakHoursAnalysisCard = ({ data, title = "Peak Hours Analysis" }: PeakHoursAnalysisCardProps) => {
  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  // Format hour for display
  const formatHour = (hour: number) => {
    if (hour === 0) return '12 AM';
    if (hour === 12) return '12 PM';
    if (hour < 12) return `${hour} AM`;
    return `${hour - 12} PM`;
  };

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border rounded-md shadow-md p-3">
          <p className="font-medium">{formatHour(Number(label))}</p>
          {payload.map((entry: any, index: number) => (
            <p key={`item-${index}`} style={{ color: entry.color }}>
              {entry.name}: {
                entry.name.includes('Revenue') 
                  ? formatCurrency(entry.value)
                  : entry.value.toLocaleString()
              }
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  // Sort data by hour
  const sortedData = [...data].sort((a, b) => a.hour - b.hour);

  // Find peak hours
  const peakRevenueHour = sortedData.reduce((max, current) => 
    current.totalRevenue > max.totalRevenue ? current : max, sortedData[0] || { hour: 0, totalRevenue: 0 }
  );

  const peakOrdersHour = sortedData.reduce((max, current) => 
    current.totalOrders > max.totalOrders ? current : max, sortedData[0] || { hour: 0, totalOrders: 0 }
  );

  // Calculate business hours stats
  const businessHours = sortedData.filter(d => d.hour >= 11 && d.hour <= 22);
  const totalBusinessRevenue = businessHours.reduce((sum, d) => sum + d.totalRevenue, 0);
  const totalBusinessOrders = businessHours.reduce((sum, d) => sum + d.totalOrders, 0);

  // Get all categories
  const allCategories = new Set<string>();
  data.forEach(hourData => {
    Object.keys(hourData.categories).forEach(cat => allCategories.add(cat));
  });

  // Prepare category breakdown data
  const categoryBreakdownData = sortedData.map(hourData => {
    const result: any = { hour: hourData.hour };
    allCategories.forEach(category => {
      result[category] = hourData.categories[category]?.revenue || 0;
    });
    return result;
  });

  const categoryColors = ['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6', '#06b6d4'];

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg font-medium flex items-center">
          <Clock className="h-5 w-5 mr-2" />
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {data.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No peak hours data available
          </div>
        ) : (
          <Tabs defaultValue="overview">
            <TabsList className="mb-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="revenue">Revenue Trends</TabsTrigger>
              <TabsTrigger value="orders">Order Volume</TabsTrigger>
              <TabsTrigger value="categories">Category Breakdown</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              {/* Key Metrics */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-3 bg-muted rounded-lg">
                  <div className="flex items-center justify-center mb-1">
                    <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
                    <span className="text-sm font-medium">Peak Revenue</span>
                  </div>
                  <div className="text-lg font-bold text-green-600">{formatHour(peakRevenueHour.hour)}</div>
                  <div className="text-xs text-muted-foreground">{formatCurrency(peakRevenueHour.totalRevenue)}</div>
                </div>
                
                <div className="text-center p-3 bg-muted rounded-lg">
                  <div className="flex items-center justify-center mb-1">
                    <Users className="h-4 w-4 text-blue-600 mr-1" />
                    <span className="text-sm font-medium">Peak Orders</span>
                  </div>
                  <div className="text-lg font-bold text-blue-600">{formatHour(peakOrdersHour.hour)}</div>
                  <div className="text-xs text-muted-foreground">{peakOrdersHour.totalOrders} orders</div>
                </div>

                <div className="text-center p-3 bg-muted rounded-lg">
                  <div className="flex items-center justify-center mb-1">
                    <DollarSign className="h-4 w-4 text-purple-600 mr-1" />
                    <span className="text-sm font-medium">Business Hours</span>
                  </div>
                  <div className="text-lg font-bold text-purple-600">{formatCurrency(totalBusinessRevenue)}</div>
                  <div className="text-xs text-muted-foreground">11 AM - 10 PM</div>
                </div>

                <div className="text-center p-3 bg-muted rounded-lg">
                  <div className="flex items-center justify-center mb-1">
                    <Clock className="h-4 w-4 text-orange-600 mr-1" />
                    <span className="text-sm font-medium">Avg/Hour</span>
                  </div>
                  <div className="text-lg font-bold text-orange-600">
                    {Math.round(totalBusinessOrders / businessHours.length)}
                  </div>
                  <div className="text-xs text-muted-foreground">orders</div>
                </div>
              </div>

              {/* Hourly Summary */}
              <div className="space-y-2">
                <h4 className="text-sm font-medium">Hourly Performance</h4>
                <div className="max-h-64 overflow-y-auto space-y-2">
                  {sortedData.map((hourData) => (
                    <div key={hourData.hour} className="flex items-center justify-between p-2 bg-muted rounded">
                      <div className="flex items-center space-x-3">
                        <div className="text-sm font-medium w-16">{formatHour(hourData.hour)}</div>
                        <div className="text-xs text-muted-foreground">
                          {hourData.totalOrders} orders
                        </div>
                      </div>
                      <div className="text-sm font-bold text-green-600">
                        {formatCurrency(hourData.totalRevenue)}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="revenue" className="h-[400px]">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={sortedData} margin={{ top: 10, right: 10, left: 10, bottom: 20 }}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="hour" 
                    tickFormatter={formatHour}
                    tick={{ fontSize: 10 }}
                  />
                  <YAxis 
                    tickFormatter={(value) => `£${value}`}
                    tick={{ fontSize: 10 }}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Area
                    type="monotone"
                    dataKey="totalRevenue"
                    name="Revenue"
                    stroke="#3b82f6"
                    fill="#3b82f6"
                    fillOpacity={0.3}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </TabsContent>

            <TabsContent value="orders" className="h-[400px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={sortedData} margin={{ top: 10, right: 10, left: 10, bottom: 20 }}>
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis 
                    dataKey="hour" 
                    tickFormatter={formatHour}
                    tick={{ fontSize: 10 }}
                  />
                  <YAxis tick={{ fontSize: 10 }} />
                  <Tooltip content={<CustomTooltip />} />
                  <Bar 
                    dataKey="totalOrders" 
                    name="Orders"
                    fill="#10b981" 
                    radius={[4, 4, 0, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </TabsContent>

            <TabsContent value="categories" className="h-[400px]">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={categoryBreakdownData} margin={{ top: 10, right: 10, left: 10, bottom: 20 }}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="hour" 
                    tickFormatter={formatHour}
                    tick={{ fontSize: 10 }}
                  />
                  <YAxis 
                    tickFormatter={(value) => `£${value}`}
                    tick={{ fontSize: 10 }}
                  />
                  <Tooltip 
                    formatter={(value, name) => [formatCurrency(Number(value)), name]}
                    labelFormatter={(label) => formatHour(Number(label))}
                  />
                  {Array.from(allCategories).map((category, index) => (
                    <Area
                      key={category}
                      type="monotone"
                      dataKey={category}
                      stackId="1"
                      stroke={categoryColors[index % categoryColors.length]}
                      fill={categoryColors[index % categoryColors.length]}
                      fillOpacity={0.6}
                    />
                  ))}
                </AreaChart>
              </ResponsiveContainer>
            </TabsContent>
          </Tabs>
        )}
      </CardContent>
    </Card>
  );
};

export default PeakHoursAnalysisCard;
