{"timestamp": "2025-06-24T11:49:41.939Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750765267005_lfenjc6nz", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T11:49:42.021Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750765267005_lfenjc6nz", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T11:52:18.167Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750765267005_lfenjc6nz", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1280, "height": 800, "colorDepth": 24}, "viewport": {"width": 944, "height": 735}}
{"timestamp": "2025-06-24T11:52:18.211Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750765267005_lfenjc6nz", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1280, "height": 800, "colorDepth": 24}, "viewport": {"width": 944, "height": 735}}
{"timestamp": "2025-06-24T13:22:25.182Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T13:22:25.219Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:07:41.062Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:07:41.125Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:08:03.000Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:08:03.039Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:08:15.710Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:08:15.782Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:08:48.532Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:08:48.565Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:09:32.646Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:09:32.726Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:09:53.026Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:09:53.126Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:10:47.593Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:10:47.654Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:11:19.703Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:11:19.753Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:11:34.581Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:11:34.619Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:11:55.980Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:11:56.030Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:12:11.077Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:12:11.137Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:12:29.914Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:12:29.964Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:12:47.571Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:12:47.628Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:13:01.242Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:13:01.290Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:16:03.561Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1280, "height": 800, "colorDepth": 24}, "viewport": {"width": 772, "height": 728}}
{"timestamp": "2025-06-24T14:16:03.613Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1280, "height": 800, "colorDepth": 24}, "viewport": {"width": 772, "height": 728}}
{"timestamp": "2025-06-24T14:28:17.062Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:28:17.275Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:29:16.016Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:29:16.237Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:29:34.299Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:29:34.364Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:29:46.814Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:29:46.880Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:30:25.763Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:30:25.810Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:30:59.527Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:30:59.769Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:38:26.810Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/inventory", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:38:26.847Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/inventory", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-24T14:38:46.254Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1280, "height": 800, "colorDepth": 24}, "viewport": {"width": 944, "height": 735}}
{"timestamp": "2025-06-24T14:38:46.326Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1280, "height": 800, "colorDepth": 24}, "viewport": {"width": 944, "height": 735}}
{"timestamp": "2025-06-24T14:40:49.091Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750776048647_9xi9jh9mh", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "apiRequest@http://localhost:5175/src/services/apiService.ts:92:19\n", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1536, "height": 864, "colorDepth": 24}, "viewport": {"width": 1485, "height": 703}}
{"timestamp": "2025-06-24T14:40:49.105Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750776048647_9xi9jh9mh", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "apiRequest@http://localhost:5175/src/services/apiService.ts:92:19\n", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1536, "height": 864, "colorDepth": 24}, "viewport": {"width": 1485, "height": 703}}
{"timestamp": "2025-06-24T14:41:42.585Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750776048647_9xi9jh9mh", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "apiRequest@http://localhost:5175/src/services/apiService.ts:92:19\n", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1536, "height": 864, "colorDepth": 24}, "viewport": {"width": 1485, "height": 703}}
{"timestamp": "2025-06-24T14:41:42.607Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750776048647_9xi9jh9mh", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "apiRequest@http://localhost:5175/src/services/apiService.ts:92:19\n", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1536, "height": 864, "colorDepth": 24}, "viewport": {"width": 1485, "height": 703}}
{"timestamp": "2025-06-24T14:44:49.617Z", "level": "error", "component": "LightweightAI", "message": "Failed to get AI status", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"error": "Failed to fetch"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1280, "height": 800, "colorDepth": 24}, "viewport": {"width": 944, "height": 735}}
{"timestamp": "2025-06-24T14:44:50.353Z", "level": "error", "component": "LightweightAI", "message": "AI API call failed", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"error": "Failed to fetch"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1280, "height": 800, "colorDepth": 24}, "viewport": {"width": 944, "height": 735}}
{"timestamp": "2025-06-24T14:44:50.363Z", "level": "error", "component": "LightweightAI", "message": "AI chat failed", "sessionId": "session_1750771344637_8kkot6l2w", "url": "http://localhost:5175/admin/assistant", "data": {"error": "Failed to fetch"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1280, "height": 800, "colorDepth": 24}, "viewport": {"width": 944, "height": 735}}
