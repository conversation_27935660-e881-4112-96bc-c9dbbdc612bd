"""
Tables MVC router implementing EPOS table management functionality.
Uses TableController for business logic and provides RESTful API endpoints.
"""

from fastapi import APIRouter, HTTPException, status, Depends, Query
from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel

from app.controllers.table_controller import TableController
from app.utils.logging_config import logger

router = APIRouter(prefix="/tables", tags=["Tables (MVC)"])

# Pydantic models for request/response
class TableCreate(BaseModel):
    number: str
    capacity: int
    location: Optional[str] = None
    restaurant_id: str

class TableUpdate(BaseModel):
    number: Optional[str] = None
    capacity: Optional[int] = None
    location: Optional[str] = None

class TableStatusUpdate(BaseModel):
    status: str
    notes: Optional[str] = None
    order_id: Optional[str] = None
    updated_by: Optional[str] = None

class TableReservation(BaseModel):
    reserved_until: datetime
    customer_name: Optional[str] = None
    party_size: Optional[int] = None
    notes: Optional[str] = None

# Dependency injection for controller
async def get_table_controller() -> TableController:
    """Dependency injection for table controller"""
    return TableController()

@router.get("/")
async def get_tables(
    restaurant_id: Optional[str] = Query(None, description="Filter by restaurant ID"),
    status: Optional[str] = Query(None, description="Filter by table status"),
    capacity: Optional[int] = Query(None, description="Minimum table capacity"),
    location: Optional[str] = Query(None, description="Filter by location"),
    skip: int = Query(0, ge=0, description="Number of tables to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of tables to return"),
    controller: TableController = Depends(get_table_controller)
):
    """
    Get tables with filtering and pagination.
    
    This endpoint supports EPOS functionality by providing:
    - Real-time table status information
    - Filtering by availability and capacity
    - Location-based filtering for multi-area restaurants
    """
    try:
        tables = await controller.get_tables(
            restaurant_id=restaurant_id,
            status=status,
            capacity=capacity,
            location=location,
            skip=skip,
            limit=limit
        )
        return {
            "success": True,
            "data": tables,
            "count": len(tables),
            "filters": {
                "restaurant_id": restaurant_id,
                "status": status,
                "capacity": capacity,
                "location": location
            }
        }
    except Exception as e:
        logger.error(f"Failed to get tables: {e}", "TablesRouter")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve tables"
        )

@router.get("/available")
async def get_available_tables(
    restaurant_id: str = Query(..., description="Restaurant ID"),
    capacity: Optional[int] = Query(None, description="Minimum table capacity"),
    location: Optional[str] = Query(None, description="Filter by location"),
    controller: TableController = Depends(get_table_controller)
):
    """
    Get available tables for EPOS order placement.
    
    This is a key EPOS endpoint that provides real-time available tables
    for order assignment and customer seating.
    """
    try:
        available_tables = await controller.get_available_tables(
            restaurant_id=restaurant_id,
            capacity=capacity,
            location=location
        )
        return {
            "success": True,
            "data": available_tables,
            "count": len(available_tables),
            "restaurant_id": restaurant_id
        }
    except Exception as e:
        logger.error(f"Failed to get available tables: {e}", "TablesRouter")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve available tables"
        )

@router.get("/{table_id}")
async def get_table(
    table_id: str,
    controller: TableController = Depends(get_table_controller)
):
    """Get a specific table by ID"""
    table = await controller.get_table_by_id(table_id)
    if not table:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Table not found"
        )
    return {
        "success": True,
        "data": table
    }

@router.post("/")
async def create_table(
    table_data: TableCreate,
    controller: TableController = Depends(get_table_controller)
):
    """Create a new table"""
    try:
        created_table = await controller.create_table(table_data.model_dump())
        return {
            "success": True,
            "message": "Table created successfully",
            "data": created_table
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error creating table: {e}", "TablesRouter")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create table"
        )

@router.put("/{table_id}")
async def update_table(
    table_id: str,
    table_data: TableUpdate,
    controller: TableController = Depends(get_table_controller)
):
    """Update table information"""
    try:
        updated_table = await controller.update_table(
            table_id, 
            table_data.model_dump(exclude_unset=True)
        )
        return {
            "success": True,
            "message": "Table updated successfully",
            "data": updated_table
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error updating table: {e}", "TablesRouter")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update table"
        )

@router.patch("/{table_id}/status")
async def update_table_status(
    table_id: str,
    status_update: TableStatusUpdate,
    controller: TableController = Depends(get_table_controller)
):
    """
    Update table status - Critical EPOS endpoint.
    
    This endpoint is essential for EPOS operations:
    - Mark tables as occupied when orders are placed
    - Set tables to cleaning when customers leave
    - Mark tables as available when ready for new customers
    - Handle out-of-service scenarios
    """
    try:
        updated_table = await controller.update_table_status(
            table_id=table_id,
            status=status_update.status,
            notes=status_update.notes,
            order_id=status_update.order_id,
            updated_by=status_update.updated_by
        )
        return {
            "success": True,
            "message": f"Table status updated to {status_update.status}",
            "data": updated_table
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error updating table status: {e}", "TablesRouter")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update table status"
        )

@router.post("/{table_id}/reserve")
async def reserve_table(
    table_id: str,
    reservation: TableReservation,
    controller: TableController = Depends(get_table_controller)
):
    """Reserve a table for future use"""
    try:
        result = await controller.reserve_table(
            table_id=table_id,
            reserved_until=reservation.reserved_until,
            customer_name=reservation.customer_name,
            party_size=reservation.party_size,
            notes=reservation.notes
        )
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error reserving table: {e}", "TablesRouter")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to reserve table"
        )

@router.delete("/{table_id}/reservation")
async def clear_reservation(
    table_id: str,
    controller: TableController = Depends(get_table_controller)
):
    """Clear table reservation"""
    try:
        result = await controller.clear_reservation(table_id)
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error clearing reservation: {e}", "TablesRouter")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to clear reservation"
        )

@router.get("/{restaurant_id}/utilization")
async def get_table_utilization(
    restaurant_id: str,
    controller: TableController = Depends(get_table_controller)
):
    """
    Get table utilization analytics for restaurant dashboard.
    
    Provides real-time insights for restaurant management:
    - Overall utilization rate
    - Status breakdown
    - Available capacity
    """
    try:
        utilization_data = await controller.get_table_utilization(restaurant_id)
        return {
            "success": True,
            "data": utilization_data
        }
    except Exception as e:
        logger.error(f"Failed to get table utilization: {e}", "TablesRouter")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve table utilization"
        )

@router.delete("/{table_id}")
async def delete_table(
    table_id: str,
    controller: TableController = Depends(get_table_controller)
):
    """Delete a table with validation"""
    try:
        result = await controller.delete_table(table_id)
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error deleting table: {e}", "TablesRouter")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete table"
        )
