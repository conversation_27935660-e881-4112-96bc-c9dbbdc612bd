import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Header, <PERSON>alogTitle, DialogFooter } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { toast } from "@/components/ui/sonner";
import { Calendar, Clock, Plus, Edit2, Trash2 } from "lucide-react";

interface Shift {
  id: string;
  day: string;
  startTime: string;
  endTime: string;
}

interface StaffScheduleProps {
  staffId: string;
  staffName: string;
}

const StaffSchedule = ({ staffId, staffName }: StaffScheduleProps) => {
  const [shifts, setShifts] = useState<Shift[]>([
    { id: "1", day: "Monday", startTime: "09:00", endTime: "17:00" },
    { id: "2", day: "Wednesday", startTime: "09:00", endTime: "17:00" },
    { id: "3", day: "Friday", startTime: "12:00", endTime: "20:00" },
  ]);
  
  const [isAddShiftOpen, setIsAddShiftOpen] = useState(false);
  const [isEditShiftOpen, setIsEditShiftOpen] = useState(false);
  const [currentShift, setCurrentShift] = useState<Shift | null>(null);
  const [newShift, setNewShift] = useState<Omit<Shift, "id">>({
    day: "Monday",
    startTime: "09:00",
    endTime: "17:00"
  });

  const daysOfWeek = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"];

  const handleAddShift = () => {
    const id = (shifts.length + 1).toString();
    const shiftToAdd = { ...newShift, id };
    
    setShifts([...shifts, shiftToAdd]);
    setIsAddShiftOpen(false);
    setNewShift({
      day: "Monday",
      startTime: "09:00",
      endTime: "17:00"
    });
    toast.success("Shift added successfully");
  };

  const handleEditShift = () => {
    if (!currentShift) return;
    
    const updatedShifts = shifts.map(shift => 
      shift.id === currentShift.id ? currentShift : shift
    );
    
    setShifts(updatedShifts);
    setIsEditShiftOpen(false);
    setCurrentShift(null);
    toast.success("Shift updated successfully");
  };

  const handleDeleteShift = (id: string) => {
    const updatedShifts = shifts.filter(shift => shift.id !== id);
    setShifts(updatedShifts);
    toast.success("Shift deleted successfully");
  };

  const calculateHours = () => {
    let totalHours = 0;
    
    shifts.forEach(shift => {
      const start = new Date(`2000-01-01T${shift.startTime}`);
      const end = new Date(`2000-01-01T${shift.endTime}`);
      const diff = (end.getTime() - start.getTime()) / (1000 * 60 * 60);
      totalHours += diff;
    });
    
    return totalHours;
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-lg font-medium">Weekly Schedule</CardTitle>
        <Button size="sm" onClick={() => setIsAddShiftOpen(true)}>
          <Plus className="h-4 w-4 mr-1" /> Add Shift
        </Button>
      </CardHeader>
      <CardContent>
        <div className="text-sm text-muted-foreground mb-4">
          Total hours: <span className="font-medium text-foreground">{calculateHours()}</span> hrs/week
        </div>
        
        {shifts.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No shifts scheduled
          </div>
        ) : (
          <div className="space-y-3">
            {shifts.map((shift) => (
              <div key={shift.id} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                  <span className="font-medium mr-6 w-24">{shift.day}</span>
                  <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
                  <span>{shift.startTime} - {shift.endTime}</span>
                </div>
                <div className="flex gap-1">
                  <Button 
                    variant="ghost" 
                    size="icon"
                    onClick={() => {
                      setCurrentShift(shift);
                      setIsEditShiftOpen(true);
                    }}
                  >
                    <Edit2 className="h-4 w-4" />
                  </Button>
                  <Button 
                    variant="ghost" 
                    size="icon"
                    onClick={() => handleDeleteShift(shift.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>

      {/* Add Shift Dialog */}
      <Dialog open={isAddShiftOpen} onOpenChange={setIsAddShiftOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Add New Shift</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="day">Day</Label>
              <Select
                value={newShift.day}
                onValueChange={(value) => setNewShift({ ...newShift, day: value })}
              >
                <SelectTrigger id="day">
                  <SelectValue placeholder="Select day" />
                </SelectTrigger>
                <SelectContent>
                  {daysOfWeek.map((day) => (
                    <SelectItem key={day} value={day}>{day}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="startTime">Start Time</Label>
                <Input
                  id="startTime"
                  type="time"
                  value={newShift.startTime}
                  onChange={(e) => setNewShift({ ...newShift, startTime: e.target.value })}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="endTime">End Time</Label>
                <Input
                  id="endTime"
                  type="time"
                  value={newShift.endTime}
                  onChange={(e) => setNewShift({ ...newShift, endTime: e.target.value })}
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddShiftOpen(false)}>Cancel</Button>
            <Button onClick={handleAddShift}>Add Shift</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Shift Dialog */}
      <Dialog open={isEditShiftOpen} onOpenChange={setIsEditShiftOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit Shift</DialogTitle>
          </DialogHeader>
          {currentShift && (
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="edit-day">Day</Label>
                <Select
                  value={currentShift.day}
                  onValueChange={(value) => setCurrentShift({ ...currentShift, day: value })}
                >
                  <SelectTrigger id="edit-day">
                    <SelectValue placeholder="Select day" />
                  </SelectTrigger>
                  <SelectContent>
                    {daysOfWeek.map((day) => (
                      <SelectItem key={day} value={day}>{day}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="edit-startTime">Start Time</Label>
                  <Input
                    id="edit-startTime"
                    type="time"
                    value={currentShift.startTime}
                    onChange={(e) => setCurrentShift({ ...currentShift, startTime: e.target.value })}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="edit-endTime">End Time</Label>
                  <Input
                    id="edit-endTime"
                    type="time"
                    value={currentShift.endTime}
                    onChange={(e) => setCurrentShift({ ...currentShift, endTime: e.target.value })}
                  />
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditShiftOpen(false)}>Cancel</Button>
            <Button onClick={handleEditShift}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export default StaffSchedule;
