// Table Session Management Service
// Handles table state persistence during EPOS sessions with subscription integration
import logger from '@/utils/logger';

export interface CartItem {
  id: string;
  name: string;
  description?: string;
  price: number;
  category?: string;
  quantity: number;
  allergens?: string[];
  notes?: string;
  allergenAlert?: boolean;
  addOns?: Array<{
    id: string;
    name: string;
    price: number;
  }>;
}

export interface TableSession {
  tableId: string;
  tableNumber: number;
  orderType?: 'dine-in' | 'takeaway';
  partySize: number;
  allergens: string[];
  hasAllergens: boolean;
  setupComplete: boolean;
  status: 'available' | 'occupied';
  cartItems: CartItem[];
  createdAt: string;
  lastUpdated: string;
  orderNumber?: string;
  totalAmount?: number;
  subscriptionPlan?: string;
  restaurantId?: string;
}

export interface TableInfo {
  id: string;
  area: string;
  number: number;
  type: "table" | "takeout";
  capacity?: number;
  status?: 'available' | 'occupied';
}

class TableSessionService {
  private readonly STORAGE_KEY = 'epos_table_sessions';
  private readonly CURRENT_SESSION_KEY = 'epos_current_session';
  private sessions: Map<string, TableSession> = new Map();

  constructor() {
    this.loadSessions();
  }

  // Load sessions from localStorage
  private loadSessions(): void {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const sessionsArray: TableSession[] = JSON.parse(stored);
        this.sessions = new Map(sessionsArray.map(session => [session.tableId, session]));
      }
    } catch (error) {
      logger.error('Failed to load table sessions', 'TableSessionService', { error });
      this.sessions = new Map();
    }
  }

  // Save sessions to localStorage
  private saveSessions(): void {
    try {
      const sessionsArray = Array.from(this.sessions.values());
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(sessionsArray));
    } catch (error) {
      logger.error('Failed to save table sessions', 'TableSessionService', { error });
    }
  }

  /**
   * Initialize a new table session with subscription context
   */
  initializeSession(
    tableInfo: TableInfo,
    orderType: 'dine-in' | 'takeaway',
    subscriptionPlan?: string,
    restaurantId?: string
  ): TableSession {
    const now = new Date().toISOString();
    const session: TableSession = {
      tableId: tableInfo.id,
      tableNumber: tableInfo.number,
      orderType,
      partySize: 0,
      allergens: [],
      hasAllergens: false,
      setupComplete: false,
      status: 'occupied',
      cartItems: [],
      createdAt: now,
      lastUpdated: now,
      totalAmount: 0,
      subscriptionPlan,
      restaurantId
    };

    this.sessions.set(tableInfo.id, session);
    this.saveSessions();
    this.setCurrentSession(session);

    logger.userAction('table session initialized', 'TableSessionService', {
      tableId: tableInfo.id,
      tableNumber: tableInfo.number,
      orderType,
      subscriptionPlan
    });

    return session;
  }

  /**
   * Update party size for current session
   */
  updatePartySize(partySize: number): TableSession | null {
    const session = this.getCurrentSession();
    if (!session) {
      logger.error('No current session to update party size', 'TableSessionService');
      return null;
    }

    if (partySize < 1 || partySize > 20) {
      logger.warn('Invalid party size', 'TableSessionService', { partySize });
      throw new Error('Party size must be between 1 and 20');
    }

    session.partySize = partySize;
    session.lastUpdated = new Date().toISOString();

    // Update in Map and save
    this.sessions.set(session.tableId, session);
    this.saveSessions();
    this.setCurrentSession(session);

    logger.userAction('party size updated', 'TableSessionService', {
      tableId: session.tableId,
      partySize
    });

    return session;
  }

  /**
   * Update allergen information for current session
   */
  updateAllergens(hasAllergens: boolean, allergens: string[] = []): TableSession | null {
    const session = this.getCurrentSession();
    if (!session) {
      logger.error('No current session to update allergens', 'TableSessionService');
      return null;
    }

    session.hasAllergens = hasAllergens;
    session.allergens = hasAllergens ? allergens : [];
    session.lastUpdated = new Date().toISOString();

    // Update in Map and save
    this.sessions.set(session.tableId, session);
    this.saveSessions();
    this.setCurrentSession(session);

    logger.userAction('allergens updated', 'TableSessionService', {
      tableId: session.tableId,
      hasAllergens,
      allergenCount: allergens.length
    });

    return session;
  }

  /**
   * Complete session setup
   */
  completeSetup(): TableSession | null {
    const session = this.getCurrentSession();
    if (!session) {
      logger.error('No current session to complete setup', 'TableSessionService');
      return null;
    }

    if (session.partySize === 0) {
      throw new Error('Party size must be set before completing setup');
    }

    session.setupComplete = true;
    session.lastUpdated = new Date().toISOString();

    // Update in Map and save
    this.sessions.set(session.tableId, session);
    this.saveSessions();
    this.setCurrentSession(session);

    logger.userAction('session setup completed', 'TableSessionService', {
      tableId: session.tableId,
      partySize: session.partySize,
      hasAllergens: session.hasAllergens
    });

    return session;
  }

  /**
   * Add item to cart
   */
  addToCart(item: CartItem): TableSession | null {
    const session = this.getCurrentSession();
    if (!session) {
      logger.error('No current session to add cart item', 'TableSessionService');
      return null;
    }

    if (!session.setupComplete) {
      throw new Error('Session setup must be completed before adding items');
    }

    const existingItemIndex = session.cartItems.findIndex(cartItem =>
      cartItem.id === item.id &&
      cartItem.notes === item.notes &&
      JSON.stringify(cartItem.addOns) === JSON.stringify(item.addOns)
    );

    if (existingItemIndex >= 0) {
      session.cartItems[existingItemIndex].quantity += item.quantity;
    } else {
      session.cartItems.push(item);
    }

    // Update total amount
    session.totalAmount = session.cartItems.reduce((total, cartItem) =>
      total + (cartItem.price * cartItem.quantity), 0
    );

    session.lastUpdated = new Date().toISOString();

    // Update in Map and save
    this.sessions.set(session.tableId, session);
    this.saveSessions();
    this.setCurrentSession(session);

    logger.userAction('item added to cart', 'TableSessionService', {
      tableId: session.tableId,
      itemId: item.id,
      itemName: item.name,
      quantity: item.quantity
    });

    return session;
  }

  /**
   * Remove item from cart
   */
  removeFromCart(itemId: string, removeAll: boolean = false): TableSession | null {
    const session = this.getCurrentSession();
    if (!session) {
      logger.error('No current session to remove cart item', 'TableSessionService');
      return null;
    }

    const existingItemIndex = session.cartItems.findIndex(item => item.id === itemId);
    if (existingItemIndex >= 0) {
      const existingItem = session.cartItems[existingItemIndex];

      if (removeAll || existingItem.quantity <= 1) {
        session.cartItems.splice(existingItemIndex, 1);
      } else {
        session.cartItems[existingItemIndex].quantity -= 1;
      }

      // Update total amount
      session.totalAmount = session.cartItems.reduce((total, cartItem) =>
        total + (cartItem.price * cartItem.quantity), 0
      );

      session.lastUpdated = new Date().toISOString();

      // Update in Map and save
      this.sessions.set(session.tableId, session);
      this.saveSessions();
      this.setCurrentSession(session);

      logger.userAction('item removed from cart', 'TableSessionService', {
        tableId: session.tableId,
        itemId,
        removeAll
      });
    }

    return session;
  }

  /**
   * Clear current session
   */
  clearSession(): void {
    const session = this.getCurrentSession();
    if (session) {
      this.sessions.delete(session.tableId);
      this.saveSessions();
      localStorage.removeItem(this.CURRENT_SESSION_KEY);

      logger.userAction('session cleared', 'TableSessionService', {
        tableId: session.tableId
      });
    }
  }

  /**
   * Get current active session
   */
  getCurrentSession(): TableSession | null {
    try {
      const sessionData = localStorage.getItem(this.CURRENT_SESSION_KEY);
      return sessionData ? JSON.parse(sessionData) : null;
    } catch (error) {
      logger.error('Failed to get current session', 'TableSessionService', { error });
      return null;
    }
  }

  /**
   * Get session for specific table
   */
  getSessionForTable(tableId: string): TableSession | null {
    return this.sessions.get(tableId) || null;
  }

  /**
   * Get all active sessions
   */
  getAllSessions(): TableSession[] {
    return Array.from(this.sessions.values());
  }

  /**
   * Check if table has active session
   */
  hasActiveSession(tableId: string): boolean {
    return this.getSessionForTable(tableId) !== null;
  }

  /**
   * Set current active session
   */
  private setCurrentSession(session: TableSession): void {
    try {
      localStorage.setItem(this.CURRENT_SESSION_KEY, JSON.stringify(session));
    } catch (error) {
      logger.error('Failed to set current session', 'TableSessionService', { error });
    }
  }

  // Additional methods from external repository for enhanced functionality

  /**
   * Create or update a table session (alternative method)
   */
  createSession(
    tableId: string,
    tableNumber: number,
    partySize: number,
    allergens: string[],
    hasAllergens: boolean
  ): TableSession {
    const now = new Date().toISOString();

    const session: TableSession = {
      tableId,
      tableNumber,
      partySize,
      allergens,
      hasAllergens,
      setupComplete: true,
      status: 'occupied',
      cartItems: [],
      createdAt: this.sessions.get(tableId)?.createdAt || now,
      lastUpdated: now,
      totalAmount: 0,
    };

    this.sessions.set(tableId, session);
    this.saveSessions();
    return session;
  }

  /**
   * Get a table session (alternative method)
   */
  getSession(tableId: string): TableSession | null {
    return this.sessions.get(tableId) || null;
  }

  /**
   * Check if table setup is complete
   */
  isSetupComplete(tableId: string): boolean {
    const session = this.sessions.get(tableId);
    return session?.setupComplete || false;
  }
  // Update table status
  updateTableStatus(tableId: string, status: TableSession['status']): void {
    const session = this.sessions.get(tableId);
    if (session) {
      session.status = status;
      session.lastUpdated = new Date().toISOString();
      this.saveSessions();
    }
  }

  // Set order number for a table
  setOrderNumber(tableId: string, orderNumber: string): void {
    const session = this.sessions.get(tableId);
    if (session) {
      session.orderNumber = orderNumber;
      session.lastUpdated = new Date().toISOString();
      this.saveSessions();
    }
  }

  // Add item to table cart (alternative method)
  addCartItem(tableId: string, item: CartItem): void {
    const session = this.sessions.get(tableId);
    if (session) {
      const existingItemIndex = session.cartItems.findIndex(cartItem => cartItem.id === item.id);

      if (existingItemIndex >= 0) {
        // Update quantity if item already exists
        session.cartItems[existingItemIndex].quantity += item.quantity;
      } else {
        // Add new item
        session.cartItems.push(item);
      }

      // Update total amount
      session.totalAmount = session.cartItems.reduce((total, cartItem) =>
        total + (cartItem.price * cartItem.quantity), 0
      );

      session.lastUpdated = new Date().toISOString();
      this.saveSessions();
    }
  }

  // Update cart item quantity
  updateCartItemQuantity(tableId: string, itemId: string, quantity: number): void {
    const session = this.sessions.get(tableId);
    if (session) {
      const itemIndex = session.cartItems.findIndex(item => item.id === itemId);

      if (itemIndex >= 0) {
        if (quantity <= 0) {
          // Remove item if quantity is 0 or less
          session.cartItems.splice(itemIndex, 1);
        } else {
          // Update quantity
          session.cartItems[itemIndex].quantity = quantity;
        }

        // Update total amount
        session.totalAmount = session.cartItems.reduce((total, cartItem) =>
          total + (cartItem.price * cartItem.quantity), 0
        );

        session.lastUpdated = new Date().toISOString();
        this.saveSessions();
      }
    }
  }

  // Update cart item notes
  updateCartItemNotes(tableId: string, itemId: string, notes: string): void {
    const session = this.sessions.get(tableId);
    if (session) {
      const itemIndex = session.cartItems.findIndex(item => item.id === itemId);

      if (itemIndex >= 0) {
        // Update notes
        session.cartItems[itemIndex].notes = notes.trim() || undefined;
        session.lastUpdated = new Date().toISOString();
        this.saveSessions();
      }
    }
  }

  // Update cart item allergen alert
  updateCartItemAllergenAlert(tableId: string, itemId: string, allergenAlert: boolean): void {
    const session = this.sessions.get(tableId);
    if (session) {
      const itemIndex = session.cartItems.findIndex(item => item.id === itemId);

      if (itemIndex >= 0) {
        // Update allergen alert
        session.cartItems[itemIndex].allergenAlert = allergenAlert;
        session.lastUpdated = new Date().toISOString();
        this.saveSessions();
      }
    }
  }

  // Remove item from cart (alternative method)
  removeCartItem(tableId: string, itemId: string): void {
    const session = this.sessions.get(tableId);
    if (session) {
      session.cartItems = session.cartItems.filter(item => item.id !== itemId);

      // Update total amount
      session.totalAmount = session.cartItems.reduce((total, cartItem) =>
        total + (cartItem.price * cartItem.quantity), 0
      );

      session.lastUpdated = new Date().toISOString();
      this.saveSessions();
    }
  }

  // Clear cart for a table
  clearCart(tableId: string): void {
    const session = this.sessions.get(tableId);
    if (session) {
      session.cartItems = [];
      session.totalAmount = 0;
      session.lastUpdated = new Date().toISOString();
      this.saveSessions();
    }
  }

  // Get cart items for a table
  getCartItems(tableId: string): CartItem[] {
    const session = this.sessions.get(tableId);
    return session?.cartItems || [];
  }

  // Get occupied table IDs
  getOccupiedTableIds(): string[] {
    return Array.from(this.sessions.values())
      .filter(session => session.status === 'occupied')
      .map(session => session.tableId);
  }

  // Check if table is occupied
  isTableOccupied(tableId: string): boolean {
    const session = this.sessions.get(tableId);
    return session?.status === 'occupied' || false;
  }

  // Get table status for color coding
  getTableStatus(tableId: string): 'available' | 'occupied' {
    const session = this.sessions.get(tableId);
    if (!session) return 'available';
    return session.status === 'occupied' ? 'occupied' : 'available';
  }

  // Get table allergen information
  getTableAllergens(tableId: string): string[] {
    const session = this.sessions.get(tableId);
    return session?.allergens || [];
  }

  // Get table party size
  getTablePartySize(tableId: string): number | null {
    const session = this.sessions.get(tableId);
    return session?.partySize || null;
  }

  // Clear all sessions (for testing/reset purposes)
  clearAllSessions(): void {
    this.sessions.clear();
    localStorage.removeItem(this.STORAGE_KEY);
  }

  // Clean up old sessions (older than 24 hours)
  cleanupOldSessions(): void {
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();

    for (const [tableId, session] of this.sessions.entries()) {
      if (session.lastUpdated < oneDayAgo) {
        this.sessions.delete(tableId);
      }
    }

    this.saveSessions();
  }
}

// Export singleton instance
export const tableSessionService = new TableSessionService();

// Clean up old sessions on service initialization
tableSessionService.cleanupOldSessions();
