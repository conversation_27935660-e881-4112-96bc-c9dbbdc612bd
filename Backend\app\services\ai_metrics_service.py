"""
AI Metrics and Performance Monitoring Service
Tracks AI usage, performance metrics, token consumption, and response quality
for comprehensive monitoring and optimization of the AI assistant system.
"""

import time
import asyncio
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import json

from app.utils.logging_config import logger
from app.services.ai_memory_service import ai_memory_service


@dataclass
class AIInteractionMetrics:
    """Metrics for a single AI interaction"""
    interaction_id: str
    restaurant_id: str
    user_id: Optional[str]
    session_id: Optional[str]
    query: str
    query_length: int
    response: str
    response_length: int
    function_calls: List[Dict[str, Any]]
    function_call_count: int
    processing_time_ms: float
    database_query_time_ms: float
    ai_response_time_ms: float
    success: bool
    error: Optional[str]
    timestamp: datetime
    model_used: str
    estimated_tokens: int
    response_quality_score: Optional[float] = None


@dataclass
class DatabaseQueryMetrics:
    """Metrics for database queries made by AI"""
    query_id: str
    function_name: str
    restaurant_id: str
    parameters: Dict[str, Any]
    execution_time_ms: float
    success: bool
    result_size: int
    error: Optional[str]
    timestamp: datetime


@dataclass
class AIPerformanceStats:
    """Aggregated AI performance statistics"""
    total_interactions: int
    successful_interactions: int
    failed_interactions: int
    average_response_time_ms: float
    average_query_length: int
    average_response_length: int
    total_function_calls: int
    total_estimated_tokens: int
    most_used_functions: List[Dict[str, Any]]
    error_rate: float
    uptime_percentage: float
    period_start: datetime
    period_end: datetime


class AIMetricsService:
    """Service for tracking and analyzing AI performance metrics"""
    
    def __init__(self):
        self.metrics_buffer: deque = deque(maxlen=1000)  # Keep last 1000 interactions
        self.db_query_buffer: deque = deque(maxlen=5000)  # Keep last 5000 DB queries
        self.function_usage_stats = defaultdict(int)
        self.error_stats = defaultdict(int)
        self.response_times = deque(maxlen=100)  # Last 100 response times
        self.start_time = datetime.now()
        
        logger.info("AI Metrics Service initialized", "AIMetrics")
    
    def estimate_tokens(self, text: str) -> int:
        """
        Estimate token count for text (rough approximation).
        In production, use actual tokenizer from the AI model.
        """
        # Rough estimation: ~4 characters per token for English text
        return max(1, len(text) // 4)
    
    def calculate_response_quality_score(
        self, 
        query: str, 
        response: str, 
        function_calls: List[Dict[str, Any]],
        success: bool
    ) -> float:
        """
        Calculate a quality score for the AI response.
        This is a simplified scoring system - in production, you might use
        more sophisticated metrics like semantic similarity, user feedback, etc.
        """
        if not success:
            return 0.0
        
        score = 0.5  # Base score for successful response
        
        # Length appropriateness (not too short, not too long)
        response_length = len(response)
        if 50 <= response_length <= 1000:
            score += 0.2
        elif response_length > 1000:
            score += 0.1
        
        # Function call relevance
        if function_calls:
            score += 0.2  # Bonus for using data
            if len(function_calls) <= 3:  # Not too many calls
                score += 0.1
        
        # Query complexity handling
        query_length = len(query)
        if query_length > 50:  # Complex query
            if response_length > 100:  # Detailed response
                score += 0.1
        
        return min(1.0, score)
    
    async def record_interaction(
        self,
        interaction_id: str,
        restaurant_id: str,
        user_id: Optional[str],
        session_id: Optional[str],
        query: str,
        response: str,
        function_calls: List[Dict[str, Any]],
        processing_time_ms: float,
        database_query_time_ms: float,
        ai_response_time_ms: float,
        success: bool,
        error: Optional[str] = None,
        model_used: str = "gemini-1.5-flash"
    ) -> None:
        """Record metrics for an AI interaction"""
        try:
            # Calculate derived metrics
            estimated_tokens = self.estimate_tokens(query + response)
            quality_score = self.calculate_response_quality_score(
                query, response, function_calls, success
            )
            
            # Create metrics record
            metrics = AIInteractionMetrics(
                interaction_id=interaction_id,
                restaurant_id=restaurant_id,
                user_id=user_id,
                session_id=session_id,
                query=query[:500],  # Truncate for storage
                query_length=len(query),
                response=response[:1000],  # Truncate for storage
                response_length=len(response),
                function_calls=function_calls,
                function_call_count=len(function_calls),
                processing_time_ms=processing_time_ms,
                database_query_time_ms=database_query_time_ms,
                ai_response_time_ms=ai_response_time_ms,
                success=success,
                error=error,
                timestamp=datetime.now(),
                model_used=model_used,
                estimated_tokens=estimated_tokens,
                response_quality_score=quality_score
            )
            
            # Store in buffer
            self.metrics_buffer.append(metrics)
            
            # Update aggregated stats
            self.response_times.append(processing_time_ms)
            
            # Track function usage
            for func_call in function_calls:
                func_name = func_call.get('function', 'unknown')
                self.function_usage_stats[func_name] += 1
            
            # Track errors
            if not success and error:
                self.error_stats[error] += 1
            
            # Log the interaction
            logger.info("AI interaction recorded", "AIMetrics", {
                "interaction_id": interaction_id,
                "restaurant_id": restaurant_id,
                "success": success,
                "processing_time_ms": processing_time_ms,
                "function_calls": len(function_calls),
                "quality_score": quality_score,
                "estimated_tokens": estimated_tokens
            })
            
        except Exception as e:
            logger.error("Error recording AI interaction metrics", "AIMetrics", {
                "interaction_id": interaction_id,
                "error": str(e)
            })
    
    async def record_database_query(
        self,
        query_id: str,
        function_name: str,
        restaurant_id: str,
        parameters: Dict[str, Any],
        execution_time_ms: float,
        success: bool,
        result_size: int,
        error: Optional[str] = None
    ) -> None:
        """Record metrics for a database query made by AI"""
        try:
            metrics = DatabaseQueryMetrics(
                query_id=query_id,
                function_name=function_name,
                restaurant_id=restaurant_id,
                parameters=parameters,
                execution_time_ms=execution_time_ms,
                success=success,
                result_size=result_size,
                error=error,
                timestamp=datetime.now()
            )
            
            self.db_query_buffer.append(metrics)
            
            logger.debug("Database query recorded", "AIMetrics", {
                "query_id": query_id,
                "function_name": function_name,
                "execution_time_ms": execution_time_ms,
                "success": success,
                "result_size": result_size
            })
            
        except Exception as e:
            logger.error("Error recording database query metrics", "AIMetrics", {
                "query_id": query_id,
                "error": str(e)
            })
    
    def get_performance_stats(
        self, 
        period_hours: int = 24
    ) -> AIPerformanceStats:
        """Get aggregated performance statistics for a time period"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=period_hours)
            
            # Filter metrics for the period
            period_metrics = [
                m for m in self.metrics_buffer 
                if m.timestamp >= cutoff_time
            ]
            
            if not period_metrics:
                return AIPerformanceStats(
                    total_interactions=0,
                    successful_interactions=0,
                    failed_interactions=0,
                    average_response_time_ms=0.0,
                    average_query_length=0,
                    average_response_length=0,
                    total_function_calls=0,
                    total_estimated_tokens=0,
                    most_used_functions=[],
                    error_rate=0.0,
                    uptime_percentage=100.0,
                    period_start=cutoff_time,
                    period_end=datetime.now()
                )
            
            # Calculate statistics
            total_interactions = len(period_metrics)
            successful_interactions = sum(1 for m in period_metrics if m.success)
            failed_interactions = total_interactions - successful_interactions
            
            avg_response_time = sum(m.processing_time_ms for m in period_metrics) / total_interactions
            avg_query_length = sum(m.query_length for m in period_metrics) / total_interactions
            avg_response_length = sum(m.response_length for m in period_metrics) / total_interactions
            total_function_calls = sum(m.function_call_count for m in period_metrics)
            total_tokens = sum(m.estimated_tokens for m in period_metrics)
            
            # Most used functions
            function_counts = defaultdict(int)
            for m in period_metrics:
                for func_call in m.function_calls:
                    func_name = func_call.get('function', 'unknown')
                    function_counts[func_name] += 1
            
            most_used_functions = [
                {"function": func, "count": count}
                for func, count in sorted(function_counts.items(), key=lambda x: x[1], reverse=True)[:5]
            ]
            
            error_rate = (failed_interactions / total_interactions) * 100 if total_interactions > 0 else 0
            uptime_percentage = 100.0  # Simplified - in production, track actual uptime
            
            return AIPerformanceStats(
                total_interactions=total_interactions,
                successful_interactions=successful_interactions,
                failed_interactions=failed_interactions,
                average_response_time_ms=round(avg_response_time, 2),
                average_query_length=int(avg_query_length),
                average_response_length=int(avg_response_length),
                total_function_calls=total_function_calls,
                total_estimated_tokens=total_tokens,
                most_used_functions=most_used_functions,
                error_rate=round(error_rate, 2),
                uptime_percentage=uptime_percentage,
                period_start=cutoff_time,
                period_end=datetime.now()
            )
            
        except Exception as e:
            logger.error("Error calculating performance stats", "AIMetrics", {
                "error": str(e)
            })
            raise
    
    def get_restaurant_metrics(
        self, 
        restaurant_id: str, 
        period_hours: int = 24
    ) -> Dict[str, Any]:
        """Get metrics specific to a restaurant"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=period_hours)
            
            # Filter metrics for the restaurant and period
            restaurant_metrics = [
                m for m in self.metrics_buffer 
                if m.restaurant_id == restaurant_id and m.timestamp >= cutoff_time
            ]
            
            if not restaurant_metrics:
                return {
                    "restaurant_id": restaurant_id,
                    "period_hours": period_hours,
                    "total_interactions": 0,
                    "message": "No interactions found for this period"
                }
            
            # Calculate restaurant-specific stats
            total_interactions = len(restaurant_metrics)
            successful_interactions = sum(1 for m in restaurant_metrics if m.success)
            avg_quality_score = sum(
                m.response_quality_score for m in restaurant_metrics 
                if m.response_quality_score is not None
            ) / total_interactions
            
            # Most common queries (simplified)
            query_patterns = defaultdict(int)
            for m in restaurant_metrics:
                # Simple pattern detection based on keywords
                query_lower = m.query.lower()
                if 'sales' in query_lower or 'revenue' in query_lower:
                    query_patterns['sales_queries'] += 1
                elif 'inventory' in query_lower or 'stock' in query_lower:
                    query_patterns['inventory_queries'] += 1
                elif 'customer' in query_lower:
                    query_patterns['customer_queries'] += 1
                elif 'menu' in query_lower:
                    query_patterns['menu_queries'] += 1
                else:
                    query_patterns['other_queries'] += 1
            
            return {
                "restaurant_id": restaurant_id,
                "period_hours": period_hours,
                "total_interactions": total_interactions,
                "successful_interactions": successful_interactions,
                "success_rate": round((successful_interactions / total_interactions) * 100, 2),
                "average_quality_score": round(avg_quality_score, 2),
                "query_patterns": dict(query_patterns),
                "period_start": cutoff_time.isoformat(),
                "period_end": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error("Error getting restaurant metrics", "AIMetrics", {
                "restaurant_id": restaurant_id,
                "error": str(e)
            })
            return {
                "restaurant_id": restaurant_id,
                "error": str(e)
            }


# Global instance
ai_metrics_service = AIMetricsService()
