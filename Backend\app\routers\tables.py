from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import List, Optional
from datetime import datetime
from app.models.tables import Table, TableCreate, TableUpdate, TableStatus, TableLocation
from app.repositories.simple_table_repository import simple_table_repository
from app.utils.auth import get_current_active_user, check_manager_role

router = APIRouter(prefix="/tables", tags=["Tables"])

@router.get("/", response_model=List[Table])
async def get_tables(
    restaurant_id: Optional[str] = Query(None, description="Filter by restaurant ID"),
    status: Optional[str] = Query(None, description="Filter by status"),
    location: Optional[str] = Query(None, description="Filter by location")
):
    """Get all tables with optional filtering"""
    return await simple_table_repository.get_all(
        restaurant_id=restaurant_id,
        status=status,
        location=location
    )

@router.get("/{table_id}", response_model=Table)
async def get_table(table_id: str):
    """Get a table by ID"""
    table = await simple_table_repository.get_by_id(table_id)
    if not table:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Table not found"
        )
    return table

@router.post("/", response_model=Table)
async def create_table(
    table: TableCreate,
    current_user = Depends(get_current_active_user)
):
    """Create a new table"""
    # Check if user has manager or admin role
    check_manager_role(current_user)

    # Check if table number already exists for the restaurant
    if hasattr(table, 'restaurant_id') and table.restaurant_id:
        existing_table = await simple_table_repository.get_by_number(table.restaurant_id, table.number)
        if existing_table:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Table number already exists for this restaurant"
            )

    # Create table dict
    table_dict = table.model_dump()
    table_dict["status"] = TableStatus.AVAILABLE
    table_dict["current_order_id"] = None
    table_dict["reserved_until"] = None
    table_dict["last_cleaned"] = None

    return await simple_table_repository.create(table_dict)

@router.put("/{table_id}", response_model=Table)
async def update_table(
    table_id: str,
    table_update: TableUpdate,
    current_user = Depends(get_current_active_user)
):
    """Update a table"""
    # Check if user has manager or admin role
    check_manager_role(current_user)

    # Check if table exists
    existing_table = await simple_table_repository.get_by_id(table_id)
    if not existing_table:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Table not found"
        )

    # Check if table number is being changed and already exists
    if table_update.number and table_update.number != existing_table["number"]:
        restaurant_id = existing_table.get("restaurant_id")
        if restaurant_id:
            existing_number_table = await simple_table_repository.get_by_number(restaurant_id, table_update.number)
            if existing_number_table and existing_number_table["id"] != table_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Table number already exists for this restaurant"
                )

    update_data = table_update.model_dump(exclude_unset=True)
    updated_table = await simple_table_repository.update(table_id, update_data)
    return updated_table

@router.put("/{table_id}/status", response_model=Table)
async def update_table_status(
    table_id: str,
    status: TableStatus,
    notes: Optional[str] = None,
    current_user = Depends(get_current_active_user)
):
    """Update a table's status"""
    # Check if table exists
    table = await simple_table_repository.get_by_id(table_id)
    if not table:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Table not found"
        )

    update_data = {"status": status}

    # Add timestamp for specific status changes
    if status == TableStatus.CLEANING:
        update_data["last_cleaned"] = datetime.now().isoformat()
    elif status == TableStatus.AVAILABLE and table.get("status") == TableStatus.CLEANING:
        update_data["last_cleaned"] = datetime.now().isoformat()

    if notes:
        update_data["notes"] = notes

    # Update table
    updated_table = await simple_table_repository.update(table_id, update_data)
    return updated_table

@router.put("/{table_id}/reserve")
async def reserve_table(
    table_id: str,
    reserved_until: datetime,
    customer_name: Optional[str] = None,
    current_user = Depends(get_current_active_user)
):
    """Reserve a table"""
    table = await simple_table_repository.get_by_id(table_id)
    if not table:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Table not found"
        )

    if table.get("status") not in [TableStatus.AVAILABLE]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Table is not available for reservation"
        )

    update_data = {
        "status": TableStatus.RESERVED,
        "reserved_until": reserved_until.isoformat(),
        "notes": f"Reserved for {customer_name}" if customer_name else "Reserved"
    }

    updated_table = await simple_table_repository.update(table_id, update_data)
    return {"message": "Table reserved successfully", "table": updated_table}

@router.put("/{table_id}/clear-reservation")
async def clear_reservation(
    table_id: str,
    current_user = Depends(get_current_active_user)
):
    """Clear table reservation"""
    table = await simple_table_repository.get_by_id(table_id)
    if not table:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Table not found"
        )

    update_data = {
        "status": TableStatus.AVAILABLE,
        "reserved_until": None,
        "notes": None
    }

    updated_table = await simple_table_repository.update(table_id, update_data)
    return {"message": "Reservation cleared successfully", "table": updated_table}

@router.get("/status/{status}")
async def get_tables_by_status(
    status: TableStatus,
    restaurant_id: Optional[str] = Query(None, description="Filter by restaurant ID"),
    current_user = Depends(get_current_active_user)
):
    """Get tables by status"""
    tables = await simple_table_repository.get_by_status(status, restaurant_id)
    return tables

@router.get("/location/{location}")
async def get_tables_by_location(
    location: TableLocation,
    restaurant_id: Optional[str] = Query(None, description="Filter by restaurant ID"),
    current_user = Depends(get_current_active_user)
):
    """Get tables by location"""
    tables = await simple_table_repository.get_all(restaurant_id=restaurant_id, location=location)
    return tables

@router.get("/analytics/table-utilization")
async def get_table_utilization(
    restaurant_id: Optional[str] = Query(None, description="Filter by restaurant ID"),
    current_user = Depends(get_current_active_user)
):
    """Get table utilization analytics"""
    tables = await simple_table_repository.get_all(restaurant_id=restaurant_id)

    total_tables = len(tables)
    if total_tables == 0:
        return {"message": "No tables found"}

    status_counts = {}
    for status in TableStatus:
        status_counts[status] = len([t for t in tables if t.get("status") == status])

    utilization_rate = (status_counts.get(TableStatus.OCCUPIED, 0) / total_tables) * 100

    return {
        "total_tables": total_tables,
        "status_breakdown": status_counts,
        "utilization_rate": round(utilization_rate, 2),
        "available_tables": status_counts.get(TableStatus.AVAILABLE, 0),
        "occupied_tables": status_counts.get(TableStatus.OCCUPIED, 0),
        "reserved_tables": status_counts.get(TableStatus.RESERVED, 0),
        "cleaning_tables": status_counts.get(TableStatus.CLEANING, 0),
        "out_of_service_tables": status_counts.get(TableStatus.OUT_OF_SERVICE, 0)
    }

@router.delete("/{table_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_table(
    table_id: str,
    current_user = Depends(get_current_active_user)
):
    """Delete a table"""
    # Check if user has manager or admin role
    check_manager_role(current_user)

    # Check if table exists
    existing_table = await simple_table_repository.get_by_id(table_id)
    if not existing_table:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Table not found"
        )

    # Check if table has an active order
    if existing_table.get("current_order_id"):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete table with active order"
        )

    success = await simple_table_repository.delete(table_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete table"
        )
