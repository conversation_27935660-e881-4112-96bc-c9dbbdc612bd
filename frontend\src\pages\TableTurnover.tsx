
import { useState } from "react";
import Layout from "@/components/layout/Layout";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { <PERSON><PERSON><PERSON>, <PERSON>, XAxis, ResponsiveContainer, <PERSON>lt<PERSON>, Legend } from "recharts";
import { Clock, Table as TableIcon } from "lucide-react";
import StatCard from "@/components/dashboard/StatCard";

const TableTurnover = () => {
  // Mock data for table turnover analysis
  const [selectedTimeframe, setSelectedTimeframe] = useState("today");

  // Table usage data for different time periods
  const tableUsageData = {
    today: [
      { time: "11 AM", tableUtilization: 45 },
      { time: "12 PM", tableUtilization: 85 },
      { time: "1 PM", tableUtilization: 95 },
      { time: "2 PM", tableUtilization: 70 },
      { time: "3 PM", tableUtilization: 40 },
      { time: "4 PM", tableUtilization: 35 },
      { time: "5 PM", tableUtilization: 60 },
      { time: "6 PM", tableUtilization: 90 },
      { time: "7 PM", tableUtilization: 100 },
      { time: "8 PM", tableUtilization: 95 },
      { time: "9 PM", tableUtilization: 75 },
      { time: "10 PM", tableUtilization: 40 }
    ],
    thisWeek: [
      { time: "Monday", tableUtilization: 65 },
      { time: "Tuesday", tableUtilization: 55 },
      { time: "Wednesday", tableUtilization: 75 },
      { time: "Thursday", tableUtilization: 80 },
      { time: "Friday", tableUtilization: 90 },
      { time: "Saturday", tableUtilization: 100 },
      { time: "Sunday", tableUtilization: 85 }
    ],
    thisMonth: [
      { time: "Week 1", tableUtilization: 70 },
      { time: "Week 2", tableUtilization: 75 },
      { time: "Week 3", tableUtilization: 80 },
      { time: "Week 4", tableUtilization: 85 }
    ]
  };

  // Table data with turnover metrics
  const tableData = [
    {
      id: 1,
      tableNumber: "Table 1",
      capacity: 2,
      avgTurnover: 35,
      utilization: 92,
      revenue: 980,
      status: "Occupied",
      waitTime: "0:25"
    },
    {
      id: 2,
      tableNumber: "Table 2",
      capacity: 4,
      avgTurnover: 45,
      utilization: 88,
      revenue: 1250,
      status: "Available",
      waitTime: "0:00"
    },
    {
      id: 3,
      tableNumber: "Table 3",
      capacity: 4,
      avgTurnover: 55,
      utilization: 75,
      revenue: 950,
      status: "Reserved",
      waitTime: "0:15"
    },
    {
      id: 4,
      tableNumber: "Table 4",
      capacity: 6,
      avgTurnover: 60,
      utilization: 85,
      revenue: 1850,
      status: "Occupied",
      waitTime: "0:40"
    },
    {
      id: 5,
      tableNumber: "Table 5",
      capacity: 8,
      avgTurnover: 75,
      utilization: 65,
      revenue: 2200,
      status: "Available",
      waitTime: "0:00"
    },
    {
      id: 6,
      tableNumber: "Table 6",
      capacity: 2,
      avgTurnover: 30,
      utilization: 95,
      revenue: 890,
      status: "Occupied",
      waitTime: "0:10"
    }
  ];

  // Current data based on selected timeframe
  const currentUsageData = tableUsageData[selectedTimeframe as keyof typeof tableUsageData];

  // Calculate average metrics
  const calculateAverages = () => {
    const avgTurnover = tableData.reduce((acc, table) => acc + table.avgTurnover, 0) / tableData.length;
    const avgUtilization = tableData.reduce((acc, table) => acc + table.utilization, 0) / tableData.length;
    const totalRevenue = tableData.reduce((acc, table) => acc + table.revenue, 0);

    return { avgTurnover, avgUtilization, totalRevenue };
  };

  const averages = calculateAverages();

  // Custom tooltip for chart
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border border-border p-3 rounded-md shadow-md">
          <p className="text-sm font-medium">{`${label}`}</p>
          <p className="text-sm">{`Utilization: ${payload[0].value}%`}</p>
        </div>
      );
    }
    return null;
  };

  return (
    <Layout title="Table Turnover">
      <div className="space-y-6">
        {/* Stats Overview */}
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
          <StatCard
            title="Average Turnover Time"
            value={`${averages.avgTurnover} min`}
            icon={<Clock className="h-4 w-4" />}
            trend={{ value: 5, isPositive: true }}
          />
          <StatCard
            title="Table Utilization"
            value={`${averages.avgUtilization.toFixed(1)}%`}
            icon={<TableIcon className="h-4 w-4" />}
            trend={{ value: 8, isPositive: true }}
          />
          <StatCard
            title="Revenue Per Table"
            value={`£${(averages.totalRevenue / tableData.length).toFixed(2)}`}
            icon={<TableIcon className="h-4 w-4" />}
            trend={{ value: 12, isPositive: true }}
          />
          <StatCard
            title="Tables Available"
            value={`${tableData.filter(t => t.status === "Available").length}/${tableData.length}`}
            icon={<TableIcon className="h-4 w-4" />}
          />
        </div>

        {/* Table Utilization Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg font-medium flex justify-between">
              <span>Table Utilization Over Time</span>
              <div className="flex space-x-2">
                <button
                  onClick={() => setSelectedTimeframe("today")}
                  className={`px-3 py-1 text-sm rounded-md ${selectedTimeframe === "today"
                    ? "bg-primary text-primary-foreground"
                    : "bg-muted text-muted-foreground"}`}
                >
                  Today
                </button>
                <button
                  onClick={() => setSelectedTimeframe("thisWeek")}
                  className={`px-3 py-1 text-sm rounded-md ${selectedTimeframe === "thisWeek"
                    ? "bg-primary text-primary-foreground"
                    : "bg-muted text-muted-foreground"}`}
                >
                  This Week
                </button>
                <button
                  onClick={() => setSelectedTimeframe("thisMonth")}
                  className={`px-3 py-1 text-sm rounded-md ${selectedTimeframe === "thisMonth"
                    ? "bg-primary text-primary-foreground"
                    : "bg-muted text-muted-foreground"}`}
                >
                  This Month
                </button>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={currentUsageData}>
                  <XAxis
                    dataKey="time"
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12 }}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Bar
                    dataKey="tableUtilization"
                    name="Table Utilization"
                    fill="hsl(var(--primary))"
                    radius={[4, 4, 0, 0]}
                    maxBarSize={50}
                  />
                  <Legend />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Tables List */}
        <Tabs defaultValue="all">
          <TabsList className="mb-4">
            <TabsTrigger value="all">All Tables</TabsTrigger>
            <TabsTrigger value="occupied">Occupied</TabsTrigger>
            <TabsTrigger value="available">Available</TabsTrigger>
            <TabsTrigger value="reserved">Reserved</TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="space-y-4">
            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[120px]">Table</TableHead>
                      <TableHead>Capacity</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Avg. Turnover</TableHead>
                      <TableHead>Utilization</TableHead>
                      <TableHead>Wait Time</TableHead>
                      <TableHead className="text-right">Revenue</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {tableData.map((table) => (
                      <TableRow key={table.id}>
                        <TableCell className="font-medium">{table.tableNumber}</TableCell>
                        <TableCell>{table.capacity} seats</TableCell>
                        <TableCell>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            table.status === "Occupied"
                              ? "bg-restaurant-secondary/20 text-restaurant-secondary"
                              : table.status === "Available"
                                ? "bg-restaurant-success/20 text-restaurant-success"
                                : "bg-restaurant-warning/20 text-restaurant-warning"
                          }`}>
                            {table.status}
                          </span>
                        </TableCell>
                        <TableCell>{table.avgTurnover} min</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Progress value={table.utilization} className="h-2 w-20" />
                            <span className="text-xs">{table.utilization}%</span>
                          </div>
                        </TableCell>
                        <TableCell>{table.waitTime}</TableCell>
                        <TableCell className="text-right">£{table.revenue}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="occupied">
            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[120px]">Table</TableHead>
                      <TableHead>Capacity</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Avg. Turnover</TableHead>
                      <TableHead>Wait Time</TableHead>
                      <TableHead className="text-right">Revenue</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {tableData.filter(t => t.status === "Occupied").map((table) => (
                      <TableRow key={table.id}>
                        <TableCell className="font-medium">{table.tableNumber}</TableCell>
                        <TableCell>{table.capacity} seats</TableCell>
                        <TableCell>
                          <span className="px-2 py-1 rounded-full text-xs font-medium bg-restaurant-secondary/20 text-restaurant-secondary">
                            {table.status}
                          </span>
                        </TableCell>
                        <TableCell>{table.avgTurnover} min</TableCell>
                        <TableCell>{table.waitTime}</TableCell>
                        <TableCell className="text-right">£{table.revenue}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="available">
            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[120px]">Table</TableHead>
                      <TableHead>Capacity</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Avg. Turnover</TableHead>
                      <TableHead className="text-right">Revenue</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {tableData.filter(t => t.status === "Available").map((table) => (
                      <TableRow key={table.id}>
                        <TableCell className="font-medium">{table.tableNumber}</TableCell>
                        <TableCell>{table.capacity} seats</TableCell>
                        <TableCell>
                          <span className="px-2 py-1 rounded-full text-xs font-medium bg-restaurant-success/20 text-restaurant-success">
                            {table.status}
                          </span>
                        </TableCell>
                        <TableCell>{table.avgTurnover} min</TableCell>
                        <TableCell className="text-right">£{table.revenue}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="reserved">
            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[120px]">Table</TableHead>
                      <TableHead>Capacity</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Avg. Turnover</TableHead>
                      <TableHead>Wait Time</TableHead>
                      <TableHead className="text-right">Revenue</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {tableData.filter(t => t.status === "Reserved").map((table) => (
                      <TableRow key={table.id}>
                        <TableCell className="font-medium">{table.tableNumber}</TableCell>
                        <TableCell>{table.capacity} seats</TableCell>
                        <TableCell>
                          <span className="px-2 py-1 rounded-full text-xs font-medium bg-restaurant-warning/20 text-restaurant-warning">
                            {table.status}
                          </span>
                        </TableCell>
                        <TableCell>{table.avgTurnover} min</TableCell>
                        <TableCell>{table.waitTime}</TableCell>
                        <TableCell className="text-right">£{table.revenue}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </Layout>
  );
};

export default TableTurnover;
