#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create sample promo codes for testing the discount system.
This script adds promo codes directly to the storage system.
"""

import sys
import os
import json
from datetime import datetime, timedelta
from uuid import uuid4

# Add the Backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.utils.storage import create, get_all

def create_sample_promo_codes():
    """Create sample promo codes for testing"""
    
    # Sample promo codes data
    sample_promo_codes = [
        {
            "id": str(uuid4()),
            "code": "WELCOME10",
            "name": "Welcome Discount",
            "description": "10% off for new customers",
            "discount_type": "percentage",
            "discount_value": 10.0,
            "scope": "order_total",
            "minimum_spend": 20.0,
            "maximum_discount": 50.0,
            "start_date": (datetime.now() - timedelta(days=30)).isoformat(),
            "end_date": (datetime.now() + timedelta(days=60)).isoformat(),
            "usage_limit": 100,
            "usage_count": 0,
            "is_active": True,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        },
        {
            "id": str(uuid4()),
            "code": "SAVE5",
            "name": "Save £5",
            "description": "£5 off orders over £25",
            "discount_type": "fixed_amount",
            "discount_value": 5.0,
            "scope": "order_total",
            "minimum_spend": 25.0,
            "start_date": (datetime.now() - timedelta(days=7)).isoformat(),
            "end_date": (datetime.now() + timedelta(days=90)).isoformat(),
            "usage_limit": 50,
            "usage_count": 0,
            "is_active": True,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        },
        {
            "id": str(uuid4()),
            "code": "HAPPYHOUR",
            "name": "Happy Hour Special",
            "description": "15% off during happy hours",
            "discount_type": "percentage",
            "discount_value": 15.0,
            "scope": "order_total",
            "minimum_spend": 15.0,
            "maximum_discount": 30.0,
            "start_date": (datetime.now() - timedelta(days=1)).isoformat(),
            "end_date": (datetime.now() + timedelta(days=30)).isoformat(),
            "usage_limit": 200,
            "usage_count": 0,
            "is_active": True,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        },
        {
            "id": str(uuid4()),
            "code": "STUDENT20",
            "name": "Student Discount",
            "description": "20% off for students",
            "discount_type": "percentage",
            "discount_value": 20.0,
            "scope": "order_total",
            "minimum_spend": 10.0,
            "maximum_discount": 25.0,
            "start_date": (datetime.now() - timedelta(days=14)).isoformat(),
            "end_date": (datetime.now() + timedelta(days=120)).isoformat(),
            "usage_limit": 75,
            "usage_count": 0,
            "is_active": True,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        },
        {
            "id": str(uuid4()),
            "code": "WEEKEND15",
            "name": "Weekend Special",
            "description": "15% off weekend orders",
            "discount_type": "percentage",
            "discount_value": 15.0,
            "scope": "order_total",
            "minimum_spend": 30.0,
            "maximum_discount": 40.0,
            "start_date": (datetime.now() - timedelta(days=3)).isoformat(),
            "end_date": (datetime.now() + timedelta(days=45)).isoformat(),
            "usage_limit": 150,
            "usage_count": 0,
            "is_active": True,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
    ]
    
    print("🎯 Creating sample promo codes...")
    
    # Check if promo codes already exist
    existing_codes = get_all("promo_codes")
    print(f"📊 Found {len(existing_codes)} existing promo codes")
    
    created_count = 0
    for promo_code in sample_promo_codes:
        # Check if code already exists
        code_exists = any(existing["code"] == promo_code["code"] for existing in existing_codes)
        
        if not code_exists:
            try:
                created_promo = create("promo_codes", promo_code)
                print(f"✅ Created promo code: {promo_code['code']} - {promo_code['name']}")
                created_count += 1
            except Exception as e:
                print(f"❌ Failed to create promo code {promo_code['code']}: {e}")
        else:
            print(f"⚠️  Promo code {promo_code['code']} already exists, skipping...")
    
    print(f"\n🎉 Successfully created {created_count} new promo codes!")
    
    # Display final count
    final_codes = get_all("promo_codes")
    print(f"📈 Total promo codes in system: {len(final_codes)}")
    
    return created_count

if __name__ == "__main__":
    try:
        created_count = create_sample_promo_codes()
        print(f"\n✨ Sample promo codes creation completed! Created {created_count} new codes.")
    except Exception as e:
        print(f"💥 Error creating sample promo codes: {e}")
        sys.exit(1)
