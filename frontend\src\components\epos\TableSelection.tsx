import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import { toast } from "@/components/ui/sonner";
import { tableSessionService } from "@/services/tableSessionService";

// Generate all tables (1-20) with status based on session service
const generateTables = () => {
  return Array.from({ length: 20 }, (_, i) => {
    const tableId = `table-${i + 1}`;
    const status = tableSessionService.getTableStatus(tableId);

    return {
      id: tableId,
      number: i + 1,
      status: status,
    };
  });
};

interface TableSelectionProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectTable: (tableInfo: { id: string; area: string; number: number; type: "table" | "takeout" }) => void;
}

const TableSelection: React.FC<TableSelectionProps> = ({ isOpen, onClose, onSelectTable }) => {
  const [customTableNumber, setCustomTableNumber] = useState("");
  const [tables, setTables] = useState(generateTables());

  // Refresh tables when dialog opens
  useEffect(() => {
    if (isOpen) {
      setTables(generateTables());
    }
  }, [isOpen]);

  const handleTableSelect = (tableId: string, number: number, status: string) => {
    // Allow selection of both available and occupied tables
    // Occupied tables will resume existing orders
    onSelectTable({
      id: tableId,
      area: "Table",
      number,
      type: "table"
    });
    onClose();
  };

  const getTableButtonClass = (status: string) => {
    const baseClass = "h-20 flex flex-col transition-all";

    switch (status) {
      case 'available':
        // Available tables show as red
        return `${baseClass} bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200 border-red-300 dark:border-red-700 hover:bg-red-200 dark:hover:bg-red-900/50`;
      case 'occupied':
        // Occupied tables show as default/green
        return `${baseClass} bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 border-green-300 dark:border-green-700 hover:bg-green-200 dark:hover:bg-green-900/50`;
      default:
        return `${baseClass} bg-muted text-muted-foreground border-border hover:bg-muted/80`;
    }
  };

  const handleCustomTableSubmit = () => {
    const tableNum = parseInt(customTableNumber);
    if (isNaN(tableNum) || tableNum <= 0) {
      toast.error("Please enter a valid table number");
      return;
    }

    onSelectTable({
      id: `custom-${tableNum}`,
      area: "Table",
      number: tableNum,
      type: "table"
    });
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Select Table for Dine-in Order</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Legend */}
          <div className="flex flex-wrap gap-4 text-sm">
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-red-100 dark:bg-red-900/30 border border-red-300 dark:border-red-700 rounded"></div>
              <span>Available</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-green-100 dark:bg-green-900/30 border border-green-300 dark:border-green-700 rounded"></div>
              <span>Occupied (Click to resume)</span>
            </div>
          </div>

          <ScrollArea className="h-[400px] rounded-md border p-4">
            <div className="grid grid-cols-4 gap-4">
              {tables.map(table => {
                const session = tableSessionService.getSession(table.id);
                const statusText = table.status === 'available' ? 'Available' : 'Occupied';

                return (
                  <Button
                    key={table.id}
                    variant="outline"
                    className={getTableButtonClass(table.status)}
                    onClick={() => handleTableSelect(table.id, table.number, table.status)}
                  >
                    <span className="text-lg font-bold">Table {table.number}</span>
                    <span className="text-xs">
                      {statusText}
                      {session && table.status === 'occupied' && (
                        <span className="block text-xs opacity-75">
                          {session.partySize} guests
                        </span>
                      )}
                    </span>
                  </Button>
                );
              })}
            </div>
          </ScrollArea>

          <div className="space-y-2 p-4 border-t">
            <Label htmlFor="custom-table">Custom Table Number</Label>
            <div className="flex gap-2">
              <Input
                id="custom-table"
                value={customTableNumber}
                onChange={(e) => setCustomTableNumber(e.target.value)}
                placeholder="Enter table number"
                type="number"
              />
              <Button onClick={handleCustomTableSubmit}>
                Confirm
              </Button>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default TableSelection;
