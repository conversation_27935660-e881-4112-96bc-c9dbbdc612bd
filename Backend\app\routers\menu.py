from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import List, Optional
from app.models.menu import MenuItem, MenuItemCreate
from app.repositories.simple_menu_repository import simple_menu_repository
from app.utils.auth import get_current_active_user, check_manager_role
from app.constants.allergens import VALID_ALLERGENS, ALLERGEN_DESCRIPTIONS

router = APIRouter(prefix="/menu", tags=["Menu"])

@router.get("/", response_model=List[MenuItem])
async def get_menu_items(
    allergens: Optional[str] = Query(None, description="Filter by allergens (comma-separated)"),
    category: Optional[str] = Query(None, description="Filter by category"),
    available: Optional[bool] = Query(None, description="Filter by availability"),
    restaurant_id: Optional[str] = Query(None, description="Filter by restaurant ID")
):
    """Get all menu items with optional filtering"""
    # Parse allergens if provided
    allergen_list = None
    if allergens:
        allergen_list = [a.strip().lower() for a in allergens.split(",")]

    # Use database repository with filters
    items = await simple_menu_repository.get_all(
        restaurant_id=restaurant_id,
        category=category,
        available=available
    )

    return items

@router.get("/allergens")
async def get_valid_allergens():
    """Get list of valid allergens with descriptions"""
    return {
        "allergens": VALID_ALLERGENS,
        "descriptions": ALLERGEN_DESCRIPTIONS
    }

@router.get("/{item_id}", response_model=MenuItem)
async def get_menu_item(item_id: str):
    """Get a menu item by ID"""
    item = await simple_menu_repository.get_by_id(item_id)
    if not item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Menu item not found"
        )
    return item

@router.post("/", response_model=MenuItem)
async def create_menu_item(
    item: MenuItemCreate,
    current_user = Depends(get_current_active_user)
):
    """Create a new menu item"""
    # Check if user has manager or admin role
    check_manager_role(current_user)

    return await simple_menu_repository.create(item.model_dump())

@router.put("/{item_id}", response_model=MenuItem)
async def update_menu_item(
    item_id: str,
    item: MenuItemCreate,
    current_user = Depends(get_current_active_user)
):
    """Update a menu item"""
    # Check if user has manager or admin role
    check_manager_role(current_user)

    # Check if item exists
    existing_item = await simple_menu_repository.get_by_id(item_id)
    if not existing_item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Menu item not found"
        )

    updated_item = await simple_menu_repository.update(item_id, item.model_dump())
    return updated_item

@router.delete("/{item_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_menu_item(
    item_id: str,
    current_user = Depends(get_current_active_user)
):
    """Delete a menu item"""
    # Check if user has manager or admin role
    check_manager_role(current_user)

    # Check if item exists
    existing_item = await simple_menu_repository.get_by_id(item_id)
    if not existing_item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Menu item not found"
        )

    success = await simple_menu_repository.delete(item_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete menu item"
        )
