/**
 * Utility functions for file upload and processing
 */

// Function to upload a file to the server
export async function uploadFile(file: File) {
  try {
    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch('/api/upload', {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      let errorData; try { errorData = await response.json(); } catch (e) { errorData = { error: "Failed to parse server response" }; }
      throw new Error(errorData.error || 'Error uploading file');
    }

    try { return await response.json(); } catch (e) { throw new Error("Failed to parse server response: " + e.message); }
  } catch (error) {
    console.error('Error uploading file:', error);
    throw error;
  }
}

// Function to validate file type
export function validateFileType(file: File): boolean {
  const allowedTypes = [
    'text/csv',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/pdf'
  ];
  
  // Also check file extension as a fallback
  const fileExtension = file.name.split('.').pop()?.toLowerCase();
  const allowedExtensions = ['csv', 'xlsx', 'pdf'];
  
  return allowedTypes.includes(file.type) || 
         (fileExtension ? allowedExtensions.includes(fileExtension) : false);
}

// Function to get file extension from file name
export function getFileExtension(fileName: string): string {
  return fileName.slice(((fileName.lastIndexOf(".") - 1) >>> 0) + 2);
}

// Function to check if file is CSV
export function isCSV(file: File): boolean {
  return file.type === 'text/csv' || getFileExtension(file.name).toLowerCase() === 'csv';
}

// Function to check if file is XLSX
export function isXLSX(file: File): boolean {
  return file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
         getFileExtension(file.name).toLowerCase() === 'xlsx';
}

// Function to check if file is PDF
export function isPDF(file: File): boolean {
  return file.type === 'application/pdf' || getFileExtension(file.name).toLowerCase() === 'pdf';
}
