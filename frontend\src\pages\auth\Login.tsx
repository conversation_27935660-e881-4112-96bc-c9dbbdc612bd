import { useState, useEffect } from "react";
import logger from "@/utils/logger";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "@/components/ui/sonner";
import { ChefHat, Mail, Lock, AlertCircle, Loader2 } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";

const Login = () => {
  // Initialize component logging
  logger.setComponent('Login');
  logger.info('Component initialized', 'Login');
  const navigate = useNavigate();
  const location = useLocation();
  const { login, isAuthenticated, user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    usernameOrEmail: "",
    password: "",
    rememberMe: false,
  });

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      // If user is admin, go to admin dashboard
      if (user.role === 'admin') {
        navigate('/admin');
      } else {
        // For regular users, go to the appropriate dashboard based on role
        if (user.accessLevel === 'full') {
          navigate('/admin');
        } else {
          navigate('/staff-dashboard');
        }
      }
    }
  }, [isAuthenticated, user, navigate]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    logger.userAction(`form field update: ${name}`, "Login", { field: name, hasValue: !!value });
    setFormData((prev) => ({ ...prev, [name]: value }));
    // Clear error when user types
    if (error) setError(null);
  };

  const handleCheckboxChange = (checked: boolean) => {
    logger.userAction("remember me toggle", "Login", { checked });
    setFormData((prev) => ({ ...prev, rememberMe: checked }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    logger.formSubmit("login form", "Login", { usernameOrEmail: formData.usernameOrEmail });
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      const result = await login(formData.usernameOrEmail, formData.password, formData.rememberMe);

      if (result.success) {
        logger.authentication("user login", "success", "Login", { usernameOrEmail: formData.usernameOrEmail });
        toast.success("Login successful!");
        // Navigation is handled by the useEffect above
      } else {
        logger.authentication("user login", "failure", "Login", { usernameOrEmail: formData.usernameOrEmail, reason: result.message });
        setError(result.message);
        toast.error(result.message);
      }
    } catch (err) {
      logger.logError(err, "user login", "Login");
      setError("An unexpected error occurred. Please try again.");
      toast.error("Login failed. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <div className="flex-1 flex flex-col lg:flex-row">
        {/* Left Side - Form */}
        <div className="w-full lg:w-1/2 flex flex-col justify-center items-center p-8 lg:p-16">
          <div className="w-full max-w-md">
            <div className="text-center mb-8">
              <Link to="/" className="inline-flex items-center gap-2">
                <ChefHat size={36} className="text-blue-600" />
                <span className="text-2xl font-bold">Promith</span>
              </Link>
              <h1 className="text-3xl font-bold mt-6 mb-2">Welcome back</h1>
              <p className="text-gray-600">Sign in to your account to continue</p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              {error && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md flex items-start">
                  <AlertCircle className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" />
                  <span>{error}</span>
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="usernameOrEmail">Username or Email</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="usernameOrEmail"
                    name="usernameOrEmail"
                    type="text"
                    placeholder="<NAME_EMAIL>"
                    className="pl-10"
                    value={formData.usernameOrEmail}
                    onChange={handleChange}
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label htmlFor="password">Password</Label>
                  <Link to="/forgot-password" className="text-sm text-blue-600 hover:text-blue-800">
                    Forgot password?
                  </Link>
                </div>
                <div className="relative">
                  <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="password"
                    name="password"
                    type="password"
                    placeholder="••••••••"
                    className="pl-10"
                    value={formData.password}
                    onChange={handleChange}
                    required
                  />
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="remember"
                  checked={formData.rememberMe}
                  onCheckedChange={handleCheckboxChange}
                />
                <Label htmlFor="remember" className="text-sm font-normal">
                  Remember me for 30 days
                </Label>
              </div>

              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? "Signing in..." : "Sign in"}
              </Button>

              <div className="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded-md text-sm">
                <p className="font-medium mb-1">Demo Accounts (Username or Email):</p>
                <ul className="list-disc list-inside space-y-1">
                  <li>Admin: jennifer.<NAME_EMAIL> / 0000</li>
                  <li>Manager (Gourmet): robert.<NAME_EMAIL> / 5678</li>
                  <li>Staff (Gourmet): michael.<NAME_EMAIL> / 1234</li>
                  <li>Manager (Pasta): <EMAIL> / 7890</li>
                  <li>Manager (Sushi): <EMAIL> / 9012</li>
                </ul>
              </div>

              <div className="text-center">
                <p className="text-sm text-gray-600">
                  Don't have an account?{" "}
                  <Link to="/register" className="text-blue-600 hover:text-blue-800 font-medium">
                    Sign up
                  </Link>
                </p>
              </div>
            </form>
          </div>
        </div>

        {/* Right Side - Image */}
        <div className="hidden lg:block lg:w-1/2 bg-blue-600">
          <div className="h-full flex items-center justify-center p-12">
            <div className="max-w-lg text-white">
              <h2 className="text-3xl font-bold mb-6">Streamline Your Restaurant Operations</h2>
              <p className="text-xl mb-8 text-blue-100">
                Access your dashboard to manage staff, inventory, analytics, and more in one place.
              </p>
              <div className="bg-white/10 rounded-lg p-6 backdrop-blur-sm">
                <div className="flex items-start space-x-4">
                  <div className="bg-white rounded-full p-2">
                    <img
                      src="https://randomuser.me/api/portraits/women/17.jpg"
                      alt="Testimonial"
                      className="h-10 w-10 rounded-full"
                      onError={(e) => {
                        e.currentTarget.src = "https://placehold.co/100x100/3b82f6/ffffff?text=JD";
                      }}
                    />
                  </div>
                  <div>
                    <p className="text-white/90 italic mb-4">
                      "Promith has transformed how we run our restaurant. Staff scheduling is now a breeze, and the analytics help us make better business decisions."
                    </p>
                    <p className="font-semibold">Jennifer Davis</p>
                    <p className="text-sm text-blue-200">Owner, The Gourmet Kitchen</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
