# RestroManage Enhanced AI Assistant Documentation

## Overview

The Enhanced AI Assistant is a comprehensive database-driven AI system that provides intelligent insights and analysis for restaurant management. It uses Google Generative AI with function calling capabilities to dynamically query restaurant data and provide actionable insights.

## Architecture

### Core Components

1. **Enhanced AI Service** (`app/services/enhanced_ai_service.py`)
   - Main AI orchestration with function calling
   - Google Generative AI integration
   - Dynamic database querying

2. **AI Database Query Service** (`app/services/ai_database_queries.py`)
   - Specialized database query functions
   - Multi-tenant data filtering
   - Optimized SQL queries

3. **AI Memory Service** (`app/services/ai_memory_service.py`)
   - Redis-based conversational memory
   - Session management
   - Context persistence

4. **AI Metrics Service** (`app/services/ai_metrics_service.py`)
   - Performance monitoring
   - Usage analytics
   - Quality scoring

## Features

### Intelligent Database Querying

The AI can dynamically call the following functions based on user queries:

- `get_daily_sales(restaurant_id, target_date, include_items)`
- `get_inventory_levels(restaurant_id, item_name, low_stock_only)`
- `get_customer_analytics(restaurant_id, period_days)`
- `get_menu_performance(restaurant_id, period_days, sort_by)`
- `get_operational_insights(restaurant_id, period_days)`
- `get_revenue_trends(restaurant_id, period_days, group_by)`

### Conversational Memory

- Maintains conversation context across multiple interactions
- Session-based memory with Redis persistence
- Automatic cleanup of expired sessions

### Performance Monitoring

- Real-time metrics collection
- Response quality scoring
- Token usage tracking
- Error rate monitoring

## Configuration

### Environment Variables

```bash
# Google AI Configuration
GOOGLE_API_KEY=your_google_api_key_here
GEMINI_MODEL=gemini-1.5-flash
GOOGLE_AI_TEMPERATURE=0.7
GOOGLE_AI_MAX_TOKENS=2000

# Redis Configuration (for memory service)
REDIS_URL=redis://localhost:6379/0

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/restro_manage
```

### Required Dependencies

```bash
pip install google-generativeai redis sqlalchemy asyncpg
```

## API Endpoints

### Enhanced AI Endpoints

#### POST `/api/ai/enhanced/query`
Process an intelligent query with function calling.

**Request:**
```json
{
  "query": "What were today's sales?",
  "session_id": "optional-session-id",
  "include_context": true,
  "context": {}
}
```

**Response:**
```json
{
  "success": true,
  "response": "Today's sales were excellent with £1,250 in revenue from 45 orders...",
  "ai_enabled": true,
  "session_id": "session-uuid",
  "function_calls": [
    {
      "function": "get_daily_sales",
      "arguments": {"restaurant_id": "rest_123"},
      "result": {"total_revenue": 1250.0, "total_orders": 45}
    }
  ],
  "restaurant_id": "rest_123",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

#### POST `/api/ai/enhanced/session`
Create a new conversation session.

#### GET `/api/ai/enhanced/session/{session_id}`
Get session details.

#### DELETE `/api/ai/enhanced/session/{session_id}`
Delete a conversation session.

#### GET `/api/ai/enhanced/summary`
Get AI-generated restaurant summary.

#### GET `/api/ai/enhanced/status`
Get AI service status and capabilities.

## Usage Examples

### Basic Queries

```javascript
// Sales inquiry
const response = await enhancedAIService.sendQuery({
  query: "How did we perform today compared to yesterday?",
  include_context: true
});

// Inventory check
const response = await enhancedAIService.sendQuery({
  query: "What items are running low in stock?",
  include_context: true
});

// Customer analysis
const response = await enhancedAIService.sendQuery({
  query: "Analyze our customer behavior patterns this month",
  include_context: true
});
```

### Advanced Queries

```javascript
// Multi-faceted analysis
const response = await enhancedAIService.sendQuery({
  query: "Give me a comprehensive analysis of our restaurant performance including sales trends, inventory status, and customer insights for the past week",
  include_context: true
});

// Specific recommendations
const response = await enhancedAIService.sendQuery({
  query: "Based on our menu performance data, what items should we promote and which ones need improvement?",
  include_context: true
});
```

### Session Management

```javascript
// Create session
const session = await enhancedAIService.createSession({
  initial_context: {
    user_role: "manager",
    focus_area: "sales_analysis"
  }
});

// Use session for contextual conversation
const response1 = await enhancedAIService.sendQuery({
  query: "What were our sales yesterday?",
  session_id: session.session_id
});

const response2 = await enhancedAIService.sendQuery({
  query: "How does that compare to last week?",
  session_id: session.session_id // AI remembers previous context
});
```

## Query Patterns and Examples

### Sales and Revenue Queries

- "What were today's sales?"
- "Show me revenue trends for the past month"
- "How do weekday sales compare to weekend sales?"
- "What's our average order value this week?"

### Inventory Management Queries

- "What items are running low in stock?"
- "Show me inventory levels for all dairy products"
- "Which items need to be reordered?"
- "What's the total value of our current inventory?"

### Customer Analytics Queries

- "Analyze customer behavior patterns"
- "What are our peak hours?"
- "Who are our high-value customers?"
- "How has customer traffic changed this month?"

### Menu Performance Queries

- "Which menu items are most popular?"
- "What items generate the highest revenue?"
- "Show me menu performance by category"
- "Which items should we consider removing?"

### Operational Insights Queries

- "How is our table utilization?"
- "What's our order completion rate?"
- "Show me operational efficiency metrics"
- "How are we performing during peak hours?"

## Best Practices

### Query Optimization

1. **Be Specific**: More specific queries yield better results
   - Good: "Show me sales for lunch hours today"
   - Better: "What was our revenue between 11 AM and 2 PM today?"

2. **Use Context**: Leverage conversational memory for follow-up questions
   - First: "What were yesterday's sales?"
   - Follow-up: "How does that compare to the same day last week?"

3. **Combine Insights**: Ask for comprehensive analysis
   - "Give me a complete picture of our restaurant performance including sales, inventory, and customer data"

### Performance Considerations

1. **Session Management**: Create sessions for multi-turn conversations
2. **Caching**: The system automatically caches responses for better performance
3. **Rate Limiting**: Be mindful of API rate limits for high-frequency usage

### Error Handling

```javascript
try {
  const response = await enhancedAIService.sendQuery({
    query: "What were today's sales?"
  });
  
  if (response.success) {
    console.log(response.response);
  } else {
    console.error('AI Error:', response.error);
  }
} catch (error) {
  console.error('Network Error:', error.message);
}
```

## Monitoring and Analytics

### Performance Metrics

The system tracks:
- Response times
- Function call frequency
- Success rates
- Token usage
- Response quality scores

### Accessing Metrics

```javascript
// Get AI status
const status = await enhancedAIService.getStatus();

// Check performance metrics
console.log(status.memory_stats);
```

## Troubleshooting

### Common Issues

1. **AI Service Disabled**
   - Check `GOOGLE_API_KEY` environment variable
   - Verify API key is valid and has proper permissions

2. **Memory Service Unavailable**
   - Check Redis connection
   - Verify `REDIS_URL` configuration

3. **Database Query Failures**
   - Check database connection
   - Verify restaurant ID permissions

4. **Slow Response Times**
   - Monitor function call complexity
   - Check database query performance
   - Review Redis memory usage

### Debug Mode

Enable debug logging:
```bash
export LOG_LEVEL=DEBUG
```

## Security Considerations

1. **Multi-Tenant Isolation**: All queries are automatically filtered by restaurant ID
2. **Authentication**: All endpoints require valid authentication
3. **Data Privacy**: Conversation data is isolated per restaurant
4. **API Key Security**: Store API keys securely and rotate regularly

## Quick Setup Guide

### 1. Install Dependencies
```bash
cd Backend
pip install google-generativeai redis sqlalchemy asyncpg
```

### 2. Configure Environment
```bash
# Copy example environment file
cp .env.example .env

# Edit .env file with your configuration
GOOGLE_API_KEY=your_actual_google_api_key
REDIS_URL=redis://localhost:6379/0
DATABASE_URL=postgresql://user:password@localhost:5432/restro_manage
```

### 3. Start Services
```bash
# Start Redis (if not already running)
redis-server

# Start the FastAPI backend
uvicorn app.api:app --host 0.0.0.0 --port 5001 --reload
```

### 4. Test the AI Assistant
```bash
# Check AI status
curl http://localhost:5001/api/ai/enhanced/status

# Test query (requires authentication)
curl -X POST http://localhost:5001/api/ai/enhanced/query \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"query": "What were today'\''s sales?"}'
```

### 5. Frontend Integration
```typescript
import { enhancedAIService } from '@/services/enhancedAIService';

// Use in React component
const response = await enhancedAIService.sendQuery({
  query: "Show me today's performance"
});
```

## Testing

### Running Tests
```bash
# Run AI service tests
pytest Backend/tests/test_enhanced_ai_service.py -v

# Run database query tests
pytest Backend/tests/test_ai_database_queries.py -v

# Run all AI-related tests
pytest Backend/tests/test_ai_*.py -v
```

### Test Coverage
The test suite covers:
- Database query functions
- AI service functionality
- Memory service operations
- Error handling scenarios
- Performance metrics

## Production Deployment

### Docker Configuration
```dockerfile
# Add to your Dockerfile
RUN pip install google-generativeai redis

# Environment variables
ENV GOOGLE_API_KEY=${GOOGLE_API_KEY}
ENV REDIS_URL=${REDIS_URL}
```

### Railway.app Deployment
```yaml
# railway.toml
[build]
builder = "DOCKERFILE"

[deploy]
healthcheckPath = "/health"
healthcheckTimeout = 300
restartPolicyType = "ON_FAILURE"

[[services]]
name = "backend"
source = "Backend"

[[services]]
name = "redis"
source = "redis:7-alpine"
```

### Performance Optimization
1. **Redis Configuration**: Optimize Redis memory settings
2. **Database Indexing**: Ensure proper indexes on restaurant_id columns
3. **Connection Pooling**: Configure appropriate connection pool sizes
4. **Caching**: Implement response caching for frequent queries

## Future Enhancements

1. **Advanced Analytics**: More sophisticated data analysis capabilities
2. **Custom Functions**: Restaurant-specific function definitions
3. **Voice Integration**: Voice-to-text query processing
4. **Predictive Analytics**: Machine learning-based forecasting
5. **Integration APIs**: Third-party service integrations
6. **Multi-language Support**: Support for multiple languages
7. **Custom Dashboards**: AI-generated dashboard recommendations
8. **Automated Alerts**: Proactive notifications based on AI analysis
