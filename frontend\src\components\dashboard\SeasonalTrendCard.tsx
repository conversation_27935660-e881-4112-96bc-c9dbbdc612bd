import { useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>er,
  CardTitle,
  CardDescription
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer, 
  Legend,
  LineChart,
  Line,
  ComposedChart,
  Area
} from 'recharts';
import { ForecastData } from "./ForecastCard";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { 
  Download,
  Info,
  TrendingUp,
  TrendingDown,
  Calendar,
  Snowflake,
  Sun,
  Cloud,
  Leaf
} from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface SeasonalTrendCardProps {
  data?: ForecastData[];
  title?: string;
  historicalData?: {
    season: string;
    year: number;
    revenue: number;
    customers: number;
  }[];
}

// Custom tooltip component
const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-background border rounded p-2 shadow-sm">
        <p className="font-medium">{label}</p>
        {payload.map((entry: any, index: number) => (
          <p key={`item-${index}`} style={{ color: entry.color }}>
            {entry.name}: {entry.dataKey.includes('revenue') ? '£' : ''}{entry.value.toLocaleString()}
            {entry.dataKey === 'customers' ? ' customers' : ''}
          </p>
        ))}
      </div>
    );
  }

  return null;
};

// Get season icon
const getSeasonIcon = (season: string) => {
  switch (season.toLowerCase()) {
    case 'winter':
      return <Snowflake className="h-5 w-5" />;
    case 'spring':
      return <Leaf className="h-5 w-5" />;
    case 'summer':
      return <Sun className="h-5 w-5" />;
    case 'autumn':
    case 'fall':
      return <Cloud className="h-5 w-5" />;
    default:
      return <Calendar className="h-5 w-5" />;
  }
};

// Mock historical data
const defaultHistoricalData = [
  { season: 'Winter', year: 2021, revenue: 180000, customers: 7200 },
  { season: 'Spring', year: 2021, revenue: 210000, customers: 8400 },
  { season: 'Summer', year: 2021, revenue: 250000, customers: 10000 },
  { season: 'Autumn', year: 2021, revenue: 220000, customers: 8800 },
  { season: 'Winter', year: 2022, revenue: 195000, customers: 7800 },
  { season: 'Spring', year: 2022, revenue: 225000, customers: 9000 },
  { season: 'Summer', year: 2022, revenue: 270000, customers: 10800 },
  { season: 'Autumn', year: 2022, revenue: 240000, customers: 9600 },
  { season: 'Winter', year: 2023, revenue: 210000, customers: 8400 },
  { season: 'Spring', year: 2023, revenue: 245000, customers: 9800 },
  { season: 'Summer', year: 2023, revenue: 290000, customers: 11600 },
  { season: 'Autumn', year: 2023, revenue: 260000, customers: 10400 }
];

const SeasonalTrendCard = ({ 
  data = [], 
  title = "Seasonal Trend Analysis",
  historicalData = defaultHistoricalData
}: SeasonalTrendCardProps) => {
  const [selectedYear, setSelectedYear] = useState<string>("all");
  const [selectedMetric, setSelectedMetric] = useState<"revenue" | "customers">("revenue");
  
  // Filter historical data based on selected year
  const filteredData = selectedYear === "all" 
    ? historicalData 
    : historicalData.filter(item => item.year.toString() === selectedYear);
  
  // Get current season
  const getCurrentSeason = () => {
    const month = new Date().getMonth();
    if (month >= 2 && month <= 4) return "Spring";
    if (month >= 5 && month <= 7) return "Summer";
    if (month >= 8 && month <= 10) return "Autumn";
    return "Winter";
  };
  
  const currentSeason = getCurrentSeason();
  
  // Get previous season data
  const getPreviousSeasonData = () => {
    const seasons = ["Winter", "Spring", "Summer", "Autumn"];
    const currentSeasonIndex = seasons.indexOf(currentSeason);
    const previousSeasonIndex = currentSeasonIndex === 0 ? 3 : currentSeasonIndex - 1;
    const previousSeason = seasons[previousSeasonIndex];
    
    const currentYear = new Date().getFullYear();
    const previousSeasonYear = previousSeasonIndex === 3 && currentSeasonIndex === 0 
      ? currentYear - 1 
      : currentYear;
    
    return historicalData.find(
      item => item.season === previousSeason && item.year === previousSeasonYear
    );
  };
  
  const previousSeasonData = getPreviousSeasonData();
  
  // Get current season data from last year
  const getLastYearSameSeasonData = () => {
    const currentYear = new Date().getFullYear();
    return historicalData.find(
      item => item.season === currentSeason && item.year === currentYear - 1
    );
  };
  
  const lastYearSameSeasonData = getLastYearSameSeasonData();
  
  // Get current season data
  const getCurrentSeasonData = () => {
    const currentYear = new Date().getFullYear();
    return historicalData.find(
      item => item.season === currentSeason && item.year === currentYear
    );
  };
  
  const currentSeasonData = getCurrentSeasonData();
  
  // Calculate growth rates
  const quarterOverQuarterGrowth = previousSeasonData && currentSeasonData
    ? ((currentSeasonData.revenue - previousSeasonData.revenue) / previousSeasonData.revenue) * 100
    : 0;
  
  const yearOverYearGrowth = lastYearSameSeasonData && currentSeasonData
    ? ((currentSeasonData.revenue - lastYearSameSeasonData.revenue) / lastYearSameSeasonData.revenue) * 100
    : 0;
  
  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  return (
    <Card className="col-span-full">
      <CardHeader>
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2">
          <div className="flex items-center gap-2">
            <div>
              <CardTitle>{title}</CardTitle>
              <CardDescription>
                Compare historical performance across seasons
              </CardDescription>
            </div>
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <Info className="h-4 w-4" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80">
                <div className="space-y-2">
                  <h4 className="font-medium">About Seasonal Analysis</h4>
                  <p className="text-sm text-muted-foreground">
                    This analysis compares performance across different seasons and years to identify
                    patterns and trends. Use this data to prepare for seasonal fluctuations and optimize
                    your menu and staffing accordingly.
                  </p>
                </div>
              </PopoverContent>
            </Popover>
          </div>
          <div className="flex items-center gap-2">
            <Select
              value={selectedYear}
              onValueChange={setSelectedYear}
            >
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder="Year" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Years</SelectItem>
                <SelectItem value="2021">2021</SelectItem>
                <SelectItem value="2022">2022</SelectItem>
                <SelectItem value="2023">2023</SelectItem>
              </SelectContent>
            </Select>
            <Select
              value={selectedMetric}
              onValueChange={(value) => setSelectedMetric(value as "revenue" | "customers")}
            >
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder="Metric" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="revenue">Revenue</SelectItem>
                <SelectItem value="customers">Customers</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" size="icon">
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="overview">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="comparison">Year Comparison</TabsTrigger>
            <TabsTrigger value="forecast">Seasonal Forecast</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm font-medium">Current Season</div>
                      <div className="text-2xl font-bold mt-1">{currentSeason}</div>
                    </div>
                    <div className="h-8 w-8 flex items-center justify-center text-blue-500">
                      {getSeasonIcon(currentSeason)}
                    </div>
                  </div>
                  <div className="text-sm text-muted-foreground mt-2">
                    {currentSeasonData ? formatCurrency(currentSeasonData.revenue) : 'N/A'} projected revenue
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm font-medium">Quarter-over-Quarter</div>
                      <div className="text-2xl font-bold mt-1">
                        {quarterOverQuarterGrowth > 0 ? '+' : ''}{quarterOverQuarterGrowth.toFixed(1)}%
                      </div>
                    </div>
                    {quarterOverQuarterGrowth >= 0 ? (
                      <TrendingUp className="h-8 w-8 text-green-500" />
                    ) : (
                      <TrendingDown className="h-8 w-8 text-red-500" />
                    )}
                  </div>
                  <div className="text-sm text-muted-foreground mt-2">
                    Compared to {previousSeasonData?.season || 'previous season'}
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm font-medium">Year-over-Year</div>
                      <div className="text-2xl font-bold mt-1">
                        {yearOverYearGrowth > 0 ? '+' : ''}{yearOverYearGrowth.toFixed(1)}%
                      </div>
                    </div>
                    {yearOverYearGrowth >= 0 ? (
                      <TrendingUp className="h-8 w-8 text-green-500" />
                    ) : (
                      <TrendingDown className="h-8 w-8 text-red-500" />
                    )}
                  </div>
                  <div className="text-sm text-muted-foreground mt-2">
                    Compared to {currentSeason} {new Date().getFullYear() - 1}
                  </div>
                </CardContent>
              </Card>
            </div>
            
            <div className="h-[300px] mt-4">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={filteredData}
                  margin={{ top: 10, right: 10, left: 10, bottom: 20 }}
                >
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis
                    dataKey="season"
                    tick={{ fontSize: 12 }}
                    tickLine={false}
                    axisLine={false}
                  />
                  <YAxis
                    tickFormatter={(value) => selectedMetric === "revenue" ? `£${value/1000}k` : value.toString()}
                    tick={{ fontSize: 12 }}
                    tickLine={false}
                    axisLine={false}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                  {selectedYear === "all" ? (
                    <>
                      <Line
                        type="monotone"
                        dataKey={selectedMetric}
                        name={selectedMetric === "revenue" ? "Revenue" : "Customers"}
                        stroke="#3b82f6"
                        strokeWidth={2}
                        dot={{ r: 4 }}
                        activeDot={{ r: 6 }}
                      />
                    </>
                  ) : (
                    <>
                      <Line
                        type="monotone"
                        dataKey={selectedMetric}
                        name={`${selectedMetric === "revenue" ? "Revenue" : "Customers"} ${selectedYear}`}
                        stroke="#3b82f6"
                        strokeWidth={2}
                        dot={{ r: 4 }}
                        activeDot={{ r: 6 }}
                      />
                    </>
                  )}
                </LineChart>
              </ResponsiveContainer>
            </div>
          </TabsContent>
          
          <TabsContent value="comparison" className="space-y-4">
            <div className="h-[400px] mt-4">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={historicalData.filter(item => item.season === currentSeason)}
                  margin={{ top: 10, right: 10, left: 10, bottom: 20 }}
                >
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis
                    dataKey="year"
                    tick={{ fontSize: 12 }}
                    tickLine={false}
                    axisLine={false}
                  />
                  <YAxis
                    yAxisId="left"
                    tickFormatter={(value) => `£${value/1000}k`}
                    tick={{ fontSize: 12 }}
                    tickLine={false}
                    axisLine={false}
                  />
                  <YAxis
                    yAxisId="right"
                    orientation="right"
                    tick={{ fontSize: 12 }}
                    tickLine={false}
                    axisLine={false}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                  <Bar
                    yAxisId="left"
                    dataKey="revenue"
                    name="Revenue"
                    fill="#3b82f6"
                    radius={[4, 4, 0, 0]}
                    barSize={30}
                  />
                  <Line
                    yAxisId="right"
                    type="monotone"
                    dataKey="customers"
                    name="Customers"
                    stroke="#10b981"
                    strokeWidth={2}
                    dot={{ r: 4 }}
                    activeDot={{ r: 6 }}
                  />
                </BarChart>
              </ResponsiveContainer>
            </div>
            
            <div className="grid grid-cols-4 gap-4 mt-4">
              {["Winter", "Spring", "Summer", "Autumn"].map((season) => (
                <Card key={season}>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="text-sm font-medium">{season}</div>
                        <div className="text-2xl font-bold mt-1">
                          {formatCurrency(
                            historicalData
                              .filter(item => item.season === season && item.year === 2023)
                              .reduce((sum, item) => sum + item.revenue, 0)
                          )}
                        </div>
                      </div>
                      <div className="h-8 w-8 flex items-center justify-center text-blue-500">
                        {getSeasonIcon(season)}
                      </div>
                    </div>
                    <div className="text-sm text-muted-foreground mt-2">
                      {historicalData
                        .filter(item => item.season === season && item.year === 2023)
                        .reduce((sum, item) => sum + item.customers, 0)
                        .toLocaleString()} customers
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
          
          <TabsContent value="forecast" className="space-y-4">
            <div className="h-[400px] mt-4">
              <ResponsiveContainer width="100%" height="100%">
                <ComposedChart
                  data={[
                    ...historicalData.filter(item => item.year === 2023),
                    {
                      season: "Winter",
                      year: 2024,
                      revenue: 225000,
                      customers: 9000
                    },
                    {
                      season: "Spring",
                      year: 2024,
                      revenue: 260000,
                      customers: 10400
                    },
                    {
                      season: "Summer",
                      year: 2024,
                      revenue: 310000,
                      customers: 12400
                    },
                    {
                      season: "Autumn",
                      year: 2024,
                      revenue: 280000,
                      customers: 11200
                    }
                  ]}
                  margin={{ top: 10, right: 10, left: 10, bottom: 20 }}
                >
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis
                    dataKey="season"
                    tick={{ fontSize: 12 }}
                    tickLine={false}
                    axisLine={false}
                  />
                  <YAxis
                    yAxisId="left"
                    tickFormatter={(value) => `£${value/1000}k`}
                    tick={{ fontSize: 12 }}
                    tickLine={false}
                    axisLine={false}
                  />
                  <YAxis
                    yAxisId="right"
                    orientation="right"
                    tick={{ fontSize: 12 }}
                    tickLine={false}
                    axisLine={false}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                  <Bar
                    yAxisId="left"
                    dataKey="revenue"
                    name="2023 Revenue"
                    fill="#3b82f6"
                    radius={[4, 4, 0, 0]}
                    barSize={20}
                  />
                  <Area
                    yAxisId="left"
                    type="monotone"
                    dataKey="revenue"
                    name="2024 Forecast"
                    stroke="#f59e0b"
                    fill="#f59e0b30"
                    activeDot={{ r: 6 }}
                  />
                  <Line
                    yAxisId="right"
                    type="monotone"
                    dataKey="customers"
                    name="Customers"
                    stroke="#10b981"
                    strokeWidth={2}
                    dot={{ r: 4 }}
                    activeDot={{ r: 6 }}
                  />
                </ComposedChart>
              </ResponsiveContainer>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default SeasonalTrendCard;
