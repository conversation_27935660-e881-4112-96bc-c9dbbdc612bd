import logger from "@/utils/logger";
import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, Di<PERSON>Title, DialogFooter, DialogDescription } from "@/components/ui/dialog";
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { toast } from "@/components/ui/sonner";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ForecastData } from "@/components/dashboard/ForecastCard";
import { format, addDays, startOfWeek, endOfWeek } from "date-fns";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import {
  Calendar,
  Users,
  Clock,
  AlertCircle,
  ChefHat,
  GlassWater,
  UserCheck,
  CalendarRange
} from "lucide-react";

// Define interfaces
interface StaffMember {
  id: string;
  name: string;
  role: string;
  position: string;
  status?: 'active' | 'inactive';
  availableDays?: string[];
  assignedHours?: number;
}

export interface ForecastShift {
  id: string;
  staffId: string;
  staffName: string;
  role: string;
  day: string;
  date: string;
  startTime: string;
  endTime: string;
  forecastBased: boolean;
}

interface StaffRequirement {
  day: string;
  date: string;
  totalStaffNeeded: number;
  waiters: number;
  kitchen: number;
  bar: number;
  host: number;
  shifts: {
    morning: number;
    afternoon: number;
    evening: number;
  };
}

interface ForecastBasedSchedulerProps {
  forecastData: ForecastData[];
  staffData: StaffMember[];
  onScheduleGenerated?: (shifts: ForecastShift[]) => void;
}

// Helper functions
const calculateStaffRequirements = (forecastData: ForecastData[]): StaffRequirement[] => {
  return forecastData.map(forecast => {
    // Calculate staff requirements based on forecast data
    const totalStaffNeeded = forecast.staffNeeded || Math.ceil(forecast.customers / 10);

    // Distribute staff across roles
    const waiters = Math.ceil(totalStaffNeeded * 0.4); // 40% waiters
    const kitchen = Math.ceil(totalStaffNeeded * 0.3); // 30% kitchen
    const bar = Math.ceil(totalStaffNeeded * 0.2); // 20% bar
    const host = Math.ceil(totalStaffNeeded * 0.1); // 10% host

    // Distribute shifts across the day
    const morning = Math.ceil(totalStaffNeeded * 0.3); // 30% morning
    const afternoon = Math.ceil(totalStaffNeeded * 0.3); // 30% afternoon
    const evening = Math.ceil(totalStaffNeeded * 0.4); // 40% evening

    return {
      day: forecast.day,
      date: format(new Date(), "yyyy-MM-dd"), // Placeholder date
      totalStaffNeeded,
      waiters,
      kitchen,
      bar,
      host,
      shifts: {
        morning,
        afternoon,
        evening
      }
    };
  });
};

const generateShiftsFromForecast = (
  requirements: StaffRequirement[],
  staffData: StaffMember[],
  dateRange: { startDate: Date; endDate: Date }
): ForecastShift[] => {
  const shifts: ForecastShift[] = [];
  let shiftId = 1;

  // Filter active staff
  const activeStaff = staffData.filter(staff => staff.status !== 'inactive');

  requirements.forEach(req => {
    // Map day name to day of week (0-6)
    const dayMap: { [key: string]: number } = {
      "Monday": 1, "Tuesday": 2, "Wednesday": 3, "Thursday": 4,
      "Friday": 5, "Saturday": 6, "Sunday": 0
    };

    // Find the date for this day within the date range
    const dayOfWeek = dayMap[req.day];
    let shiftDate = new Date(dateRange.startDate);

    // Adjust to the correct day of the week
    while (shiftDate.getDay() !== dayOfWeek) {
      shiftDate = addDays(shiftDate, 1);
      if (shiftDate > dateRange.endDate) {
        // Skip if the day is outside the date range
        return;
      }
    }

    // Format the date
    const formattedDate = format(shiftDate, "yyyy-MM-dd");

    // Assign morning shifts
    const morningStaff = assignStaffToShifts(
      activeStaff,
      req.shifts.morning,
      req.day,
      "08:00",
      "14:00"
    );

    // Assign afternoon shifts
    const afternoonStaff = assignStaffToShifts(
      activeStaff.filter(staff => !morningStaff.some(s => s.id === staff.id)),
      req.shifts.afternoon,
      req.day,
      "12:00",
      "18:00"
    );

    // Assign evening shifts
    const eveningStaff = assignStaffToShifts(
      activeStaff.filter(staff =>
        !morningStaff.some(s => s.id === staff.id) &&
        !afternoonStaff.some(s => s.id === staff.id)
      ),
      req.shifts.evening,
      req.day,
      "16:00",
      "22:00"
    );

    // Create shifts for all assigned staff
    [...morningStaff, ...afternoonStaff, ...eveningStaff].forEach(staff => {
      const isMorning = morningStaff.some(s => s.id === staff.id);
      const isAfternoon = afternoonStaff.some(s => s.id === staff.id);

      shifts.push({
        id: `forecast-${shiftId++}`,
        staffId: staff.id,
        staffName: staff.name,
        role: staff.role,
        day: req.day,
        date: formattedDate,
        startTime: isMorning ? "08:00" : isAfternoon ? "12:00" : "16:00",
        endTime: isMorning ? "14:00" : isAfternoon ? "18:00" : "22:00",
        forecastBased: true
      });
    });
  });

  return shifts;
};

const assignStaffToShifts = (
  availableStaff: StaffMember[],
  count: number,
  day: string,
  startTime: string,
  endTime: string
): StaffMember[] => {
  // Map day name to short code
  const dayMap: { [key: string]: string } = {
    "Monday": "mon", "Tuesday": "tue", "Wednesday": "wed", "Thursday": "thu",
    "Friday": "fri", "Saturday": "sat", "Sunday": "sun"
  };

  const dayCode = dayMap[day].toLowerCase();

  // Filter staff by availability for this day
  const availableForDay = availableStaff.filter(staff =>
    !staff.availableDays || staff.availableDays.includes(dayCode)
  );

  // If not enough staff available, use any available staff
  const staffToAssign = availableForDay.length >= count
    ? availableForDay.slice(0, count)
    : [...availableForDay, ...availableStaff.slice(0, count - availableForDay.length)];

  return staffToAssign.slice(0, count);
};

const saveGeneratedShifts = (shifts: ForecastShift[]): void => {
  // In a real app, this would save to a database
  logger.dataOperation("save", "generated shifts", "ForecastBasedScheduler", { count: shifts.length });
  // For now, we'll just return the shifts to be handled by the parent component
};

const ForecastBasedScheduler = ({
  forecastData,
  staffData,
  onScheduleGenerated
}: ForecastBasedSchedulerProps) => {
  // Initialize component logging
  logger.setComponent("ForecastBasedScheduler");
  logger.info("Component initialized", "ForecastBasedScheduler");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<"requirements" | "preview">("requirements");
  const [staffRequirements, setStaffRequirements] = useState<StaffRequirement[]>([]);
  const [generatedShifts, setGeneratedShifts] = useState<ForecastShift[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);


  // Date selection state
  const today = new Date();
  const defaultStartDate = startOfWeek(today, { weekStartsOn: 1 }); // Monday
  const defaultEndDate = endOfWeek(today, { weekStartsOn: 1 }); // Sunday
  const [dateRange, setDateRange] = useState<{ from: Date; to: Date }>({
    from: defaultStartDate,
    to: defaultEndDate
  });

  // Week selection options
  const [weekOptions, setWeekOptions] = useState<{ label: string; value: string; dates: { from: Date; to: Date } }[]>([]);
  const [selectedWeek, setSelectedWeek] = useState<string>("current");

  // Generate week options
  useEffect(() => {
    const options = [];

    // Current week
    const currentWeekStart = startOfWeek(today, { weekStartsOn: 1 });
    const currentWeekEnd = endOfWeek(today, { weekStartsOn: 1 });
    options.push({
      label: "Current Week",
      value: "current",
      dates: { from: currentWeekStart, to: currentWeekEnd }
    });

    // Next 4 weeks
    for (let i = 1; i <= 4; i++) {
      const weekStart = addDays(currentWeekStart, i * 7);
      const weekEnd = addDays(currentWeekEnd, i * 7);
      options.push({
        label: `Week ${i}: ${format(weekStart, "MMM d")} - ${format(weekEnd, "MMM d")}`,
        value: `week-${i}`,
        dates: { from: weekStart, to: weekEnd }
      });
    }

    setWeekOptions(options);
  }, []);

  // Calculate staff requirements when forecast data changes
  useEffect(() => {
    if (forecastData.length > 0) {
      const requirements = calculateStaffRequirements(forecastData);
      setStaffRequirements(requirements);
    }
  }, [forecastData]);

  // Handle week selection change
  const handleWeekChange = (weekValue: string) => {
    setSelectedWeek(weekValue);
    const selectedOption = weekOptions.find(option => option.value === weekValue);
    if (selectedOption) {
      setDateRange(selectedOption.dates);
    }
  };

  // Handle generate schedule button click
  const handleGenerateSchedule = () => {
    setIsGenerating(true);

    try {
      // Generate shifts based on requirements and available staff
      const shifts = generateShiftsFromForecast(
        staffRequirements,
        staffData,
        {
          startDate: dateRange.from,
          endDate: dateRange.to
        }
      );
      setGeneratedShifts(shifts);
      setActiveTab("preview");
      setIsGenerating(false);
    } catch (error) {
      logger.logError(error, "schedule generation", "ForecastBasedScheduler");
      toast.error("Failed to generate schedule. Please try again.");
      setIsGenerating(false);
    }
  };

  // Handle save schedule button click
  const handleSaveSchedule = () => {
    try {
      // Save generated shifts
      saveGeneratedShifts(generatedShifts);

      // Notify parent component
      if (onScheduleGenerated) {
        onScheduleGenerated(generatedShifts);
      }

      toast.success("Schedule generated and saved successfully!");
      setIsDialogOpen(false);
    } catch (error) {
      logger.logError(error, "schedule save", "ForecastBasedScheduler");
      toast.error("Failed to save schedule. Please try again.");
    }
  };

  // Helper function to get icon for role
  const getRoleIcon = (role: string) => {
    switch (role.toLowerCase()) {
      case 'waiter':
        return <Users className="h-4 w-4 text-blue-500" />;
      case 'chef':
        return <ChefHat className="h-4 w-4 text-red-500" />;
      case 'bartender':
        return <GlassWater className="h-4 w-4 text-purple-500" />;
      case 'manager':
        return <UserCheck className="h-4 w-4 text-green-500" />;
      case 'hostess':
        return <UserCheck className="h-4 w-4 text-yellow-500" />;
      default:
        return <Users className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg font-medium">Forecast-Based Scheduling</CardTitle>
            <CardDescription>
              Generate staff schedules based on forecast data
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <div className="space-y-1">
              <div className="text-sm font-medium">Available Forecast Data</div>
              <div className="text-sm text-muted-foreground">
                {forecastData.length} days of forecast data available
              </div>
            </div>
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-2">
                <Select value={selectedWeek} onValueChange={handleWeekChange}>
                  <SelectTrigger className="w-[250px]">
                    <CalendarRange className="mr-2 h-4 w-4" />
                    <SelectValue placeholder="Select week" />
                  </SelectTrigger>
                  <SelectContent>
                    {weekOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "justify-start text-left font-normal",
                        !dateRange && "text-muted-foreground"
                      )}
                    >
                      <Calendar className="mr-2 h-4 w-4" />
                      Custom Range
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="end">
                    <CalendarComponent
                      initialFocus
                      mode="range"
                      defaultMonth={dateRange?.from}
                      selected={dateRange}
                      onSelect={(range) => {
                        if (range?.from && range?.to) {
                          setDateRange({ from: range.from, to: range.to });
                          setSelectedWeek("custom");
                        }
                      }}
                      numberOfMonths={2}
                    />
                  </PopoverContent>
                </Popover>
              </div>
              <Button onClick={() => setIsDialogOpen(true)}>
                Generate Schedule
              </Button>
            </div>
          </div>
        </div>
      </CardContent>

      {/* Schedule Generation Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Generate Schedule from Forecast</DialogTitle>
            <DialogDescription>
              Review staff requirements and generate a schedule based on forecast data
            </DialogDescription>
          </DialogHeader>

          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as "requirements" | "preview")}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="requirements">Staff Requirements</TabsTrigger>
              <TabsTrigger value="preview">Schedule Preview</TabsTrigger>
            </TabsList>

            <TabsContent value="requirements" className="max-h-[60vh] overflow-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Day</TableHead>
                    <TableHead>Total Staff</TableHead>
                    <TableHead>Waiters</TableHead>
                    <TableHead>Kitchen</TableHead>
                    <TableHead>Bar</TableHead>
                    <TableHead>Host</TableHead>
                    <TableHead>Shift Distribution</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {staffRequirements.map((req) => (
                    <TableRow key={req.date}>
                      <TableCell className="font-medium">{req.day}</TableCell>
                      <TableCell>{req.totalStaffNeeded}</TableCell>
                      <TableCell>{req.waiters}</TableCell>
                      <TableCell>{req.kitchen}</TableCell>
                      <TableCell>{req.bar}</TableCell>
                      <TableCell>{req.host}</TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Badge variant="outline">Morning: {req.shifts.morning}</Badge>
                          <Badge variant="outline">Afternoon: {req.shifts.afternoon}</Badge>
                          <Badge variant="outline">Evening: {req.shifts.evening}</Badge>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              <div className="mt-4 flex justify-end">
                <Button
                  onClick={handleGenerateSchedule}
                  disabled={isGenerating || staffRequirements.length === 0}
                >
                  {isGenerating ? "Generating..." : "Generate Schedule"}
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="preview" className="max-h-[60vh] overflow-auto">
              {generatedShifts.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-8 text-center">
                  <AlertCircle className="h-10 w-10 text-muted-foreground mb-2" />
                  <p className="text-muted-foreground">
                    No schedule generated yet. Go to Staff Requirements tab and click "Generate Schedule".
                  </p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Day</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Staff</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Shift</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {generatedShifts.map((shift) => (
                      <TableRow key={shift.id}>
                        <TableCell className="font-medium">{shift.day}</TableCell>
                        <TableCell>{format(new Date(shift.date), "dd MMM yyyy")}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {getRoleIcon(shift.role)}
                            <span>{shift.staffName}</span>
                          </div>
                        </TableCell>
                        <TableCell>{shift.role}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Clock className="h-4 w-4 text-muted-foreground" />
                            <span>{shift.startTime} - {shift.endTime}</span>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </TabsContent>
          </Tabs>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleSaveSchedule}
              disabled={generatedShifts.length === 0}
            >
              Save Schedule
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export default ForecastBasedScheduler;