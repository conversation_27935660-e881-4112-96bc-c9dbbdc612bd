import numpy as np
import pandas as pd
from datetime import datetime, timedelta, date
from typing import List, Dict, Any, Optional
import random

from app.models.forecasting import (
    SalesForecast, CustomerTrafficForecast, InventoryDemandForecast,
    StaffSchedulingForecast, SeasonalTrendsForecast, ComprehensiveForecast,
    ForecastPeriod, TrendDirection, ConfidenceLevel, SeasonalPattern,
    InventoryDemandItem, StaffScheduleRecommendation, SeasonalEvent,
    TrendAnalysis, ForecastMetrics
)
from app.utils.forecasting_algorithms import ForecastingEngine
from app.utils.storage import get_all

class ForecastingService:
    """Service class for generating various types of forecasts"""
    
    def __init__(self):
        self.engine = ForecastingEngine()
    
    def get_historical_sales_data(self) -> List[Dict[str, Any]]:
        """Get historical sales data from storage"""
        # Get orders data
        orders = get_all("orders")
        
        # If no real data, generate mock historical data
        if not orders:
            return self._generate_mock_sales_data()
        
        # Process real orders data
        sales_data = []
        for order in orders:
            if order.get('completed_at'):
                sales_data.append({
                    'date': order['completed_at'][:10],  # Extract date part
                    'revenue': order.get('total', 0),
                    'customer_count': 1
                })
        
        return sales_data
    
    def get_historical_inventory_data(self) -> List[Dict[str, Any]]:
        """Get historical inventory usage data"""
        inventory_items = get_all("inventory_items")
        
        if not inventory_items:
            return self._generate_mock_inventory_data()
        
        return inventory_items
    
    def _generate_mock_sales_data(self) -> List[Dict[str, Any]]:
        """Generate mock historical sales data for demonstration"""
        sales_data = []
        start_date = datetime.now() - timedelta(days=90)
        
        for i in range(90):
            current_date = start_date + timedelta(days=i)
            
            # Base revenue with weekly and seasonal patterns
            base_revenue = 1500
            
            # Weekly pattern (higher on weekends)
            if current_date.weekday() >= 5:  # Weekend
                base_revenue *= 1.4
            elif current_date.weekday() == 4:  # Friday
                base_revenue *= 1.2
            
            # Monthly pattern (higher at month end/start)
            if current_date.day <= 5 or current_date.day >= 25:
                base_revenue *= 1.1
            
            # Add some randomness
            revenue = base_revenue * (0.8 + random.random() * 0.4)
            customer_count = int(revenue / 25)  # Average spend per customer
            
            sales_data.append({
                'date': current_date.strftime('%Y-%m-%d'),
                'revenue': round(revenue, 2),
                'customer_count': customer_count
            })
        
        return sales_data
    
    def _generate_mock_inventory_data(self) -> List[Dict[str, Any]]:
        """Generate mock inventory data"""
        items = [
            {"id": "1", "name": "Tomatoes", "category": "Vegetables", "unit": "kg"},
            {"id": "2", "name": "Chicken Breast", "category": "Meat", "unit": "kg"},
            {"id": "3", "name": "Olive Oil", "category": "Grocery", "unit": "liters"},
            {"id": "4", "name": "Rice", "category": "Grocery", "unit": "kg"},
            {"id": "5", "name": "Mozzarella", "category": "Dairy", "unit": "kg"},
            {"id": "6", "name": "Flour", "category": "Grocery", "unit": "kg"},
            {"id": "7", "name": "Onions", "category": "Vegetables", "unit": "kg"},
            {"id": "8", "name": "Garlic", "category": "Vegetables", "unit": "kg"}
        ]
        
        # Add usage patterns
        for item in items:
            item['daily_usage'] = random.uniform(2, 15)
            item['reorder_level'] = random.uniform(5, 20)
            item['current_stock'] = random.uniform(10, 50)
            item['cost_per_unit'] = random.uniform(2, 25)
        
        return items
    
    def generate_sales_forecast(self, period: ForecastPeriod, days_ahead: int = 30) -> SalesForecast:
        """Generate sales forecast"""
        # Get historical data
        historical_data = self.get_historical_sales_data()
        
        if not historical_data:
            # Return empty forecast if no data
            return self._create_empty_sales_forecast(period)
        
        # Prepare data for forecasting
        dates = [datetime.strptime(item['date'], '%Y-%m-%d').date() for item in historical_data]
        revenues = [item['revenue'] for item in historical_data]
        
        # Create time series DataFrame
        df = self.engine.prepare_time_series_features(dates, revenues)
        
        # Generate forecast
        predictions, confidence_intervals, metrics = self.engine.forecast_linear_regression(df, days_ahead)
        
        # Create forecast points
        start_date = max(dates) if dates else date.today()
        forecast_points = self.engine.generate_forecast_points(start_date, predictions, confidence_intervals)
        
        # Calculate trend analysis
        trend_analysis = self.engine.calculate_trend(revenues)
        
        # Detect seasonal patterns
        seasonality = self.engine.detect_seasonality(revenues)
        seasonal_patterns = []
        
        if seasonality['has_seasonality']:
            seasonal_patterns.append(SeasonalPattern(
                pattern_type="weekly",
                peak_periods=["Friday", "Saturday", "Sunday"],
                low_periods=["Monday", "Tuesday"],
                impact_factor=1.3
            ))
        
        # Historical comparison
        recent_avg = np.mean(revenues[-7:]) if len(revenues) >= 7 else np.mean(revenues)
        forecast_avg = np.mean(predictions)
        historical_comparison = {
            "recent_average": recent_avg,
            "forecast_average": forecast_avg,
            "change_percentage": ((forecast_avg - recent_avg) / recent_avg * 100) if recent_avg > 0 else 0
        }
        
        return SalesForecast(
            period=period,
            predictions=forecast_points,
            trend_analysis=trend_analysis,
            seasonal_patterns=seasonal_patterns,
            metrics=metrics,
            historical_comparison=historical_comparison
        )
    
    def generate_customer_traffic_forecast(self, period: ForecastPeriod, days_ahead: int = 30) -> CustomerTrafficForecast:
        """Generate customer traffic forecast"""
        historical_data = self.get_historical_sales_data()
        
        if not historical_data:
            return self._create_empty_traffic_forecast(period)
        
        # Extract customer counts
        dates = [datetime.strptime(item['date'], '%Y-%m-%d').date() for item in historical_data]
        customer_counts = [item['customer_count'] for item in historical_data]
        
        # Create time series DataFrame
        df = self.engine.prepare_time_series_features(dates, customer_counts)
        
        # Generate forecast
        predictions, confidence_intervals, metrics = self.engine.forecast_linear_regression(df, days_ahead)
        
        # Create forecast points
        start_date = max(dates) if dates else date.today()
        forecast_points = self.engine.generate_forecast_points(start_date, predictions, confidence_intervals)
        
        # Calculate trend analysis
        trend_analysis = self.engine.calculate_trend(customer_counts)
        
        # Define peak and low hours based on restaurant patterns
        peak_hours = ["12:00-14:00", "18:00-21:00"]
        low_hours = ["09:00-11:00", "15:00-17:00", "22:00-23:00"]
        
        average_daily_customers = np.mean(customer_counts) if customer_counts else 0
        
        return CustomerTrafficForecast(
            period=period,
            predictions=forecast_points,
            peak_hours=peak_hours,
            low_hours=low_hours,
            average_daily_customers=average_daily_customers,
            trend_analysis=trend_analysis,
            metrics=metrics
        )
    
    def generate_inventory_demand_forecast(self, period: ForecastPeriod, days_ahead: int = 30) -> InventoryDemandForecast:
        """Generate inventory demand forecast"""
        inventory_data = self.get_historical_inventory_data()
        
        if not inventory_data:
            return self._create_empty_inventory_forecast(period)
        
        items = []
        total_cost = 0
        critical_items = []
        
        for item_data in inventory_data:
            # Calculate predicted usage based on historical patterns
            daily_usage = item_data.get('daily_usage', 5)
            predicted_usage = daily_usage * days_ahead
            
            # Determine if reorder is needed
            current_stock = item_data.get('current_stock', 0)
            reorder_level = item_data.get('reorder_level', 10)
            reorder_needed = current_stock < (predicted_usage + reorder_level)
            
            # Calculate recommended quantity
            if reorder_needed:
                recommended_quantity = max(predicted_usage * 1.5, reorder_level * 2)
            else:
                recommended_quantity = 0
            
            # Determine confidence level
            confidence_level = ConfidenceLevel.HIGH if daily_usage > 0 else ConfidenceLevel.LOW
            
            # Calculate cost
            cost_per_unit = item_data.get('cost_per_unit', 0)
            total_cost += recommended_quantity * cost_per_unit
            
            # Check if critical
            if current_stock < reorder_level:
                critical_items.append(item_data['name'])
            
            items.append(InventoryDemandItem(
                item_id=item_data['id'],
                item_name=item_data['name'],
                predicted_usage=predicted_usage,
                unit=item_data.get('unit', 'units'),
                reorder_recommendation=reorder_needed,
                recommended_quantity=recommended_quantity,
                confidence_level=confidence_level
            ))
        
        # Create trend analysis for inventory
        usage_values = [item.predicted_usage for item in items]
        trend_analysis = self.engine.calculate_trend(usage_values)
        
        # Create dummy metrics for inventory forecast
        metrics = self.engine.forecast_simple_trend(usage_values, days_ahead)[2]
        
        return InventoryDemandForecast(
            period=period,
            items=items,
            total_cost_prediction=total_cost,
            critical_items=critical_items,
            trend_analysis=trend_analysis,
            metrics=metrics
        )
    
    def _create_empty_sales_forecast(self, period: ForecastPeriod) -> SalesForecast:
        """Create empty sales forecast when no data is available"""
        from app.models.forecasting import TrendAnalysis, ForecastMetrics
        
        return SalesForecast(
            period=period,
            predictions=[],
            trend_analysis=TrendAnalysis(
                direction=TrendDirection.STABLE,
                strength=0.0,
                description="No historical data available",
                change_percentage=0.0
            ),
            seasonal_patterns=[],
            metrics=ForecastMetrics(
                accuracy=0.0,
                mean_absolute_error=0.0,
                mean_squared_error=0.0,
                r_squared=0.0,
                model_type="No Data"
            ),
            historical_comparison={}
        )
    
    def _create_empty_traffic_forecast(self, period: ForecastPeriod) -> CustomerTrafficForecast:
        """Create empty traffic forecast when no data is available"""
        from app.models.forecasting import TrendAnalysis, ForecastMetrics
        
        return CustomerTrafficForecast(
            period=period,
            predictions=[],
            peak_hours=[],
            low_hours=[],
            average_daily_customers=0.0,
            trend_analysis=TrendAnalysis(
                direction=TrendDirection.STABLE,
                strength=0.0,
                description="No historical data available",
                change_percentage=0.0
            ),
            metrics=ForecastMetrics(
                accuracy=0.0,
                mean_absolute_error=0.0,
                mean_squared_error=0.0,
                r_squared=0.0,
                model_type="No Data"
            )
        )
    
    def _create_empty_inventory_forecast(self, period: ForecastPeriod) -> InventoryDemandForecast:
        """Create empty inventory forecast when no data is available"""
        from app.models.forecasting import TrendAnalysis, ForecastMetrics
        
        return InventoryDemandForecast(
            period=period,
            items=[],
            total_cost_prediction=0.0,
            critical_items=[],
            trend_analysis=TrendAnalysis(
                direction=TrendDirection.STABLE,
                strength=0.0,
                description="No historical data available",
                change_percentage=0.0
            ),
            metrics=ForecastMetrics(
                accuracy=0.0,
                mean_absolute_error=0.0,
                mean_squared_error=0.0,
                r_squared=0.0,
                model_type="No Data"
            )
        )

    def generate_staff_scheduling_forecast(self, period: ForecastPeriod, days_ahead: int = 30) -> StaffSchedulingForecast:
        """Generate staff scheduling forecast"""
        # Get historical sales data to predict staffing needs
        historical_data = self.get_historical_sales_data()

        recommendations = []

        # Generate recommendations for each day
        start_date = date.today()
        for i in range(days_ahead):
            forecast_date = start_date + timedelta(days=i)

            # Determine base staffing needs based on day of week
            weekday = forecast_date.weekday()

            # Define hourly staffing recommendations
            for hour in range(9, 23):  # 9 AM to 11 PM
                if hour in [12, 13, 18, 19, 20]:  # Peak hours
                    if weekday >= 5:  # Weekend
                        front_of_house = 4
                        back_of_house = 3
                    else:
                        front_of_house = 3
                        back_of_house = 2
                elif hour in [9, 10, 21, 22]:  # Low hours
                    front_of_house = 2
                    back_of_house = 1
                else:  # Regular hours
                    front_of_house = 3
                    back_of_house = 2

                total_staff = front_of_house + back_of_house

                recommendations.append(StaffScheduleRecommendation(
                    date=forecast_date,
                    hour=hour,
                    recommended_staff_count=total_staff,
                    position_breakdown={
                        "front_of_house": front_of_house,
                        "back_of_house": back_of_house
                    },
                    confidence_level=ConfidenceLevel.MEDIUM
                ))

        # Calculate peak hours and optimal levels
        peak_hours = ["12:00-14:00", "18:00-21:00"]
        optimal_staff_levels = {
            "peak_front_of_house": 4,
            "peak_back_of_house": 3,
            "regular_front_of_house": 3,
            "regular_back_of_house": 2,
            "low_front_of_house": 2,
            "low_back_of_house": 1
        }

        # Calculate cost optimization
        avg_hourly_rate = 15.0  # Average hourly rate
        total_hours = len(recommendations)
        avg_staff_per_hour = np.mean([rec.recommended_staff_count for rec in recommendations])
        estimated_cost = total_hours * avg_staff_per_hour * avg_hourly_rate

        cost_optimization = {
            "estimated_total_cost": estimated_cost,
            "cost_per_day": estimated_cost / days_ahead,
            "average_staff_per_hour": avg_staff_per_hour
        }

        # Create trend analysis
        staff_counts = [rec.recommended_staff_count for rec in recommendations]
        trend_analysis = self.engine.calculate_trend(staff_counts)

        # Create metrics
        metrics = self.engine.forecast_simple_trend(staff_counts, days_ahead)[2]

        return StaffSchedulingForecast(
            period=period,
            recommendations=recommendations,
            peak_hours=peak_hours,
            optimal_staff_levels=optimal_staff_levels,
            cost_optimization=cost_optimization,
            trend_analysis=trend_analysis,
            metrics=metrics
        )

    def generate_seasonal_trends_forecast(self, period: ForecastPeriod, days_ahead: int = 30) -> SeasonalTrendsForecast:
        """Generate seasonal trends forecast"""
        # Define upcoming seasonal events
        today = date.today()
        upcoming_events = []

        # Check for upcoming holidays/events in the forecast period
        end_date = today + timedelta(days=days_ahead)

        # Christmas period
        if today <= date(today.year, 12, 25) <= end_date:
            upcoming_events.append(SeasonalEvent(
                event_name="Christmas Period",
                date_range={"start": f"{today.year}-12-20", "end": f"{today.year}-12-26"},
                impact_multiplier=1.5,
                affected_categories=["appetizers", "desserts", "drinks"]
            ))

        # New Year period
        if today <= date(today.year + 1, 1, 1) <= end_date:
            upcoming_events.append(SeasonalEvent(
                event_name="New Year Period",
                date_range={"start": f"{today.year}-12-31", "end": f"{today.year + 1}-01-02"},
                impact_multiplier=1.3,
                affected_categories=["drinks", "appetizers"]
            ))

        # Valentine's Day
        if today <= date(today.year, 2, 14) <= end_date:
            upcoming_events.append(SeasonalEvent(
                event_name="Valentine's Day",
                date_range={"start": f"{today.year}-02-13", "end": f"{today.year}-02-15"},
                impact_multiplier=1.4,
                affected_categories=["desserts", "drinks", "main_courses"]
            ))

        # Define seasonal patterns
        seasonal_patterns = [
            SeasonalPattern(
                pattern_type="weekly",
                peak_periods=["Friday", "Saturday", "Sunday"],
                low_periods=["Monday", "Tuesday"],
                impact_factor=1.3
            ),
            SeasonalPattern(
                pattern_type="monthly",
                peak_periods=["Month End", "Month Start"],
                low_periods=["Mid Month"],
                impact_factor=1.1
            ),
            SeasonalPattern(
                pattern_type="holiday",
                peak_periods=["Christmas", "New Year", "Valentine's Day"],
                low_periods=["Post-Holiday"],
                impact_factor=1.5
            )
        ]

        # Calculate revenue impact
        base_revenue = 1500  # Daily average
        revenue_impact = {}

        for event in upcoming_events:
            event_days = (datetime.strptime(event.date_range["end"], "%Y-%m-%d") -
                         datetime.strptime(event.date_range["start"], "%Y-%m-%d")).days + 1
            additional_revenue = base_revenue * (event.impact_multiplier - 1) * event_days
            revenue_impact[event.event_name] = additional_revenue

        # Generate menu recommendations
        menu_recommendations = []
        if any("Christmas" in event.event_name for event in upcoming_events):
            menu_recommendations.extend([
                "Add seasonal cocktails and warm beverages",
                "Introduce holiday-themed desserts",
                "Create special Christmas menu items"
            ])

        if any("Valentine" in event.event_name for event in upcoming_events):
            menu_recommendations.extend([
                "Create romantic dinner packages",
                "Add chocolate-based desserts",
                "Offer wine pairing options"
            ])

        # Generate marketing opportunities
        marketing_opportunities = []
        for event in upcoming_events:
            marketing_opportunities.append(f"Promote special offers for {event.event_name}")
            marketing_opportunities.append(f"Create social media campaign for {event.event_name}")

        # Create trend analysis
        impact_values = [event.impact_multiplier for event in upcoming_events]
        if impact_values:
            trend_analysis = self.engine.calculate_trend(impact_values)
        else:
            trend_analysis = TrendAnalysis(
                direction=TrendDirection.STABLE,
                strength=0.0,
                description="No significant seasonal events in forecast period",
                change_percentage=0.0
            )

        return SeasonalTrendsForecast(
            period=period,
            upcoming_events=upcoming_events,
            seasonal_patterns=seasonal_patterns,
            revenue_impact=revenue_impact,
            menu_recommendations=menu_recommendations,
            marketing_opportunities=marketing_opportunities,
            trend_analysis=trend_analysis
        )

    def generate_comprehensive_forecast(self, period: ForecastPeriod, days_ahead: int = 30) -> ComprehensiveForecast:
        """Generate comprehensive forecast with all types"""
        # Generate all individual forecasts
        sales_forecast = self.generate_sales_forecast(period, days_ahead)
        customer_traffic_forecast = self.generate_customer_traffic_forecast(period, days_ahead)
        inventory_demand_forecast = self.generate_inventory_demand_forecast(period, days_ahead)
        staff_scheduling_forecast = self.generate_staff_scheduling_forecast(period, days_ahead)
        seasonal_trends_forecast = self.generate_seasonal_trends_forecast(period, days_ahead)

        # Create summary
        summary = {
            "forecast_period": f"{days_ahead} days",
            "total_predicted_revenue": sum(point.value for point in sales_forecast.predictions),
            "average_daily_customers": customer_traffic_forecast.average_daily_customers,
            "total_inventory_cost": inventory_demand_forecast.total_cost_prediction,
            "critical_inventory_items": len(inventory_demand_forecast.critical_items),
            "peak_staffing_hours": len(staff_scheduling_forecast.peak_hours),
            "upcoming_seasonal_events": len(seasonal_trends_forecast.upcoming_events)
        }

        # Generate recommendations
        recommendations = []

        # Sales recommendations
        if sales_forecast.trend_analysis.direction == TrendDirection.DECREASING:
            recommendations.append("Consider promotional campaigns to boost sales")
        elif sales_forecast.trend_analysis.direction == TrendDirection.INCREASING:
            recommendations.append("Prepare for increased demand and ensure adequate staffing")

        # Inventory recommendations
        if inventory_demand_forecast.critical_items:
            recommendations.append(f"Urgent: Restock {len(inventory_demand_forecast.critical_items)} critical items")

        # Staffing recommendations
        recommendations.append("Optimize staff scheduling during predicted peak hours")

        # Seasonal recommendations
        if seasonal_trends_forecast.upcoming_events:
            recommendations.append("Prepare special menus and promotions for upcoming seasonal events")

        return ComprehensiveForecast(
            sales_forecast=sales_forecast,
            customer_traffic_forecast=customer_traffic_forecast,
            inventory_demand_forecast=inventory_demand_forecast,
            staff_scheduling_forecast=staff_scheduling_forecast,
            seasonal_trends_forecast=seasonal_trends_forecast,
            summary=summary,
            recommendations=recommendations
        )
