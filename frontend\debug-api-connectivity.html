<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Connectivity Debug Tool</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            line-height: 1.6;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #28a745;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .button:hover {
            background: #0056b3;
        }
        .button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: 500;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 API Connectivity Debug Tool</h1>
        <p>Debug tool to identify and fix notification API connectivity issues in RestroManage-v0.</p>
        
        <div class="test-section">
            <h3>🌐 Network Connectivity Tests</h3>
            <div class="grid">
                <div>
                    <h4>Direct Backend Tests</h4>
                    <button class="button" onclick="testDirectBackend()">Test Direct Backend (5001)</button>
                    <button class="button" onclick="testBackendHealth()">Test Backend Health</button>
                    <button class="button" onclick="testBackendNotifications()">Test Backend Notifications</button>
                    <div id="direct-status" class="status info">Click buttons to test direct backend connectivity...</div>
                </div>
                
                <div>
                    <h4>Proxy Tests (via Vite)</h4>
                    <button class="button" onclick="testProxyHealth()">Test Proxy Health</button>
                    <button class="button" onclick="testProxyNotifications()">Test Proxy Notifications</button>
                    <button class="button" onclick="testProxyStats()">Test Proxy Stats</button>
                    <div id="proxy-status" class="status info">Click buttons to test proxy connectivity...</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🔍 Detailed Error Analysis</h3>
            <button class="button" onclick="runDetailedDiagnostics()">Run Detailed Diagnostics</button>
            <button class="button" onclick="testAllEndpoints()">Test All Notification Endpoints</button>
            <div id="diagnostics-status" class="status info">Click buttons to run detailed diagnostics...</div>
            <div id="diagnostics-output" class="code-block" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📊 Current Configuration</h3>
            <div id="config-info" class="code-block"></div>
        </div>

        <div class="test-section">
            <h3>🛠️ Quick Fixes</h3>
            <button class="button" onclick="clearCache()">Clear Browser Cache</button>
            <button class="button" onclick="resetLocalStorage()">Reset Local Storage</button>
            <button class="button" onclick="testWithMockData()">Test with Mock Data</button>
            <div id="fixes-status" class="status info">Click buttons to apply quick fixes...</div>
        </div>

        <div class="test-section">
            <h3>📝 Test Results Log</h3>
            <div id="test-log" class="code-block" style="min-height: 200px;"></div>
        </div>
    </div>

    <script>
        let testLog = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            testLog.push(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
            updateTestLog();
        }

        function updateTestLog() {
            const logElement = document.getElementById('test-log');
            logElement.textContent = testLog.join('\n');
            logElement.scrollTop = logElement.scrollHeight;
        }

        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = message;
                element.className = `status ${type}`;
            }
        }

        function updateDiagnostics(data) {
            const element = document.getElementById('diagnostics-output');
            element.style.display = 'block';
            element.textContent = JSON.stringify(data, null, 2);
        }

        // Initialize configuration display
        function displayConfiguration() {
            const config = {
                currentURL: window.location.href,
                userAgent: navigator.userAgent,
                timestamp: new Date().toISOString(),
                expectedFrontendPort: 5178,
                expectedBackendPort: 5001,
                proxyConfiguration: {
                    '/api': 'http://localhost:5001',
                    '/health': 'http://localhost:5001'
                },
                localStorage: {
                    restaurant_id: localStorage.getItem('restaurant_id'),
                    access_token: localStorage.getItem('access_token') ? 'Present' : 'Not found'
                }
            };

            document.getElementById('config-info').textContent = JSON.stringify(config, null, 2);
        }

        // Direct backend tests
        async function testDirectBackend() {
            updateStatus('direct-status', 'Testing direct backend connection...', 'info');
            log('Starting direct backend test');

            try {
                const response = await fetch('http://localhost:5002/health', {
                    method: 'GET',
                    mode: 'cors'
                });

                if (response.ok) {
                    const data = await response.json();
                    updateStatus('direct-status', `✅ Direct backend connection successful! Status: ${data.status}`, 'success');
                    log(`Direct backend test successful: ${JSON.stringify(data)}`);
                } else {
                    updateStatus('direct-status', `❌ Direct backend returned ${response.status}`, 'error');
                    log(`Direct backend test failed: HTTP ${response.status}`);
                }
            } catch (error) {
                updateStatus('direct-status', `❌ Direct backend connection failed: ${error.message}`, 'error');
                log(`Direct backend test error: ${error.message}`, 'error');
            }
        }

        async function testBackendHealth() {
            log('Testing backend health endpoint');
            try {
                const response = await fetch('http://localhost:5002/health');
                const data = await response.json();
                log(`Backend health response: ${JSON.stringify(data)}`);
                updateStatus('direct-status', `Health check: ${data.status}`, response.ok ? 'success' : 'error');
            } catch (error) {
                log(`Backend health error: ${error.message}`, 'error');
                updateStatus('direct-status', `Health check failed: ${error.message}`, 'error');
            }
        }

        async function testBackendNotifications() {
            log('Testing backend notifications endpoint');
            try {
                const response = await fetch('http://localhost:5002/api/notifications?restaurant_id=1', {
                    headers: {
                        'X-Restaurant-ID': '1'
                    }
                });
                const data = await response.json();
                log(`Backend notifications response: ${JSON.stringify(data)}`);
                updateStatus('direct-status', `Notifications: ${data.length} items`, response.ok ? 'success' : 'error');
            } catch (error) {
                log(`Backend notifications error: ${error.message}`, 'error');
                updateStatus('direct-status', `Notifications failed: ${error.message}`, 'error');
            }
        }

        // Proxy tests
        async function testProxyHealth() {
            log('Testing proxy health endpoint');
            try {
                const response = await fetch('/health');
                const data = await response.json();
                log(`Proxy health response: ${JSON.stringify(data)}`);
                updateStatus('proxy-status', `Proxy health: ${data.status}`, response.ok ? 'success' : 'error');
            } catch (error) {
                log(`Proxy health error: ${error.message}`, 'error');
                updateStatus('proxy-status', `Proxy health failed: ${error.message}`, 'error');
            }
        }

        async function testProxyNotifications() {
            log('Testing proxy notifications endpoint');
            try {
                const response = await fetch('/api/notifications?restaurant_id=1', {
                    headers: {
                        'X-Restaurant-ID': '1'
                    }
                });
                const data = await response.json();
                log(`Proxy notifications response: ${JSON.stringify(data)}`);
                updateStatus('proxy-status', `Proxy notifications: ${data.length} items`, response.ok ? 'success' : 'error');
            } catch (error) {
                log(`Proxy notifications error: ${error.message}`, 'error');
                updateStatus('proxy-status', `Proxy notifications failed: ${error.message}`, 'error');
            }
        }

        async function testProxyStats() {
            log('Testing proxy stats endpoint');
            try {
                const response = await fetch('/api/notifications/stats?restaurant_id=1', {
                    headers: {
                        'X-Restaurant-ID': '1'
                    }
                });
                const data = await response.json();
                log(`Proxy stats response: ${JSON.stringify(data)}`);
                updateStatus('proxy-status', `Proxy stats: ${data.total_count} total`, response.ok ? 'success' : 'error');
            } catch (error) {
                log(`Proxy stats error: ${error.message}`, 'error');
                updateStatus('proxy-status', `Proxy stats failed: ${error.message}`, 'error');
            }
        }

        // Detailed diagnostics
        async function runDetailedDiagnostics() {
            updateStatus('diagnostics-status', 'Running detailed diagnostics...', 'info');
            log('Starting detailed diagnostics');

            const diagnostics = {
                timestamp: new Date().toISOString(),
                tests: {}
            };

            // Test 1: Network connectivity
            try {
                const response = await fetch('http://localhost:5002/health', {
                    method: 'GET',
                    timeout: 5000
                });
                diagnostics.tests.directBackend = {
                    status: response.ok ? 'success' : 'failed',
                    httpStatus: response.status,
                    response: await response.json()
                };
            } catch (error) {
                diagnostics.tests.directBackend = {
                    status: 'error',
                    error: error.message
                };
            }

            // Test 2: Proxy connectivity
            try {
                const response = await fetch('/health');
                diagnostics.tests.proxyBackend = {
                    status: response.ok ? 'success' : 'failed',
                    httpStatus: response.status,
                    response: await response.json()
                };
            } catch (error) {
                diagnostics.tests.proxyBackend = {
                    status: 'error',
                    error: error.message
                };
            }

            // Test 3: Notification endpoints
            const endpoints = [
                '/api/notifications?restaurant_id=1',
                '/api/notifications/stats?restaurant_id=1'
            ];

            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(endpoint, {
                        headers: { 'X-Restaurant-ID': '1' }
                    });
                    diagnostics.tests[endpoint] = {
                        status: response.ok ? 'success' : 'failed',
                        httpStatus: response.status,
                        response: await response.json()
                    };
                } catch (error) {
                    diagnostics.tests[endpoint] = {
                        status: 'error',
                        error: error.message
                    };
                }
            }

            updateDiagnostics(diagnostics);
            updateStatus('diagnostics-status', 'Detailed diagnostics completed', 'success');
            log(`Detailed diagnostics completed: ${JSON.stringify(diagnostics, null, 2)}`);
        }

        async function testAllEndpoints() {
            log('Testing all notification endpoints');
            const endpoints = [
                { method: 'GET', url: '/api/notifications?restaurant_id=1', headers: { 'X-Restaurant-ID': '1' } },
                { method: 'GET', url: '/api/notifications/stats?restaurant_id=1', headers: { 'X-Restaurant-ID': '1' } },
                { method: 'POST', url: '/api/notifications', headers: { 'X-Restaurant-ID': '1', 'Content-Type': 'application/json' }, body: JSON.stringify({ title: 'Test', message: 'Test notification', type: 'info', priority: 'low' }) },
                { method: 'PUT', url: '/api/notifications/mark-all-read', headers: { 'X-Restaurant-ID': '1', 'Content-Type': 'application/json' } },
                { method: 'DELETE', url: '/api/notifications', headers: { 'X-Restaurant-ID': '1' } }
            ];

            const results = {};
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(endpoint.url, {
                        method: endpoint.method,
                        headers: endpoint.headers,
                        body: endpoint.body
                    });
                    results[`${endpoint.method} ${endpoint.url}`] = {
                        status: response.status,
                        ok: response.ok,
                        response: await response.json()
                    };
                } catch (error) {
                    results[`${endpoint.method} ${endpoint.url}`] = {
                        error: error.message
                    };
                }
            }

            updateDiagnostics(results);
            log(`All endpoints tested: ${JSON.stringify(results, null, 2)}`);
        }

        // Quick fixes
        function clearCache() {
            if ('caches' in window) {
                caches.keys().then(names => {
                    names.forEach(name => {
                        caches.delete(name);
                    });
                });
            }
            updateStatus('fixes-status', '✅ Browser cache cleared', 'success');
            log('Browser cache cleared');
        }

        function resetLocalStorage() {
            localStorage.clear();
            updateStatus('fixes-status', '✅ Local storage reset', 'success');
            log('Local storage reset');
            displayConfiguration(); // Refresh config display
        }

        function testWithMockData() {
            // Set mock data in localStorage
            localStorage.setItem('restaurant_id', '1');
            localStorage.setItem('notifications', JSON.stringify([
                {
                    id: 'mock-1',
                    title: 'Mock Notification',
                    message: 'This is a mock notification for testing',
                    type: 'info',
                    priority: 'medium',
                    isRead: false,
                    timestamp: new Date().toISOString()
                }
            ]));
            updateStatus('fixes-status', '✅ Mock data created', 'success');
            log('Mock data created in localStorage');
            displayConfiguration(); // Refresh config display
        }

        // Initialize on page load
        window.addEventListener('load', () => {
            displayConfiguration();
            log('API Connectivity Debug Tool initialized');
        });
    </script>
</body>
</html>
