import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Check, HelpCircle } from "lucide-react";
import MarketingLayout from "@/components/marketing/MarketingLayout";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

const Pricing = () => {
  const [billingCycle, setBillingCycle] = useState<"monthly" | "annual">("monthly");

  const toggleBillingCycle = () => {
    setBillingCycle(billingCycle === "monthly" ? "annual" : "monthly");
  };

  const getPrice = (monthlyPrice: number) => {
    if (billingCycle === "annual") {
      const annualPrice = monthlyPrice * 10; // 2 months free
      return annualPrice / 12;
    }
    return monthlyPrice;
  };

  return (
    <MarketingLayout>
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">Simple, Transparent Pricing</h1>
            <p className="text-xl mb-8">
              Choose the plan that fits your restaurant's needs. No hidden fees, no surprises.
            </p>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            {/* Billing Toggle */}
            <div className="flex justify-center items-center mb-12">
              <span className={`text-lg ${billingCycle === "monthly" ? "font-semibold text-gray-900" : "text-gray-500"}`}>
                Monthly
              </span>
              <div className="mx-4">
                <Switch
                  checked={billingCycle === "annual"}
                  onCheckedChange={toggleBillingCycle}
                  className="data-[state=checked]:bg-blue-600"
                />
              </div>
              <span className={`text-lg ${billingCycle === "annual" ? "font-semibold text-gray-900" : "text-gray-500"}`}>
                Annual
              </span>
              <span className="ml-2 bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">
                Save 16%
              </span>
            </div>

            {/* Pricing Cards */}
            <div className="grid md:grid-cols-3 gap-8">
              <PricingCard
                title="Starter"
                price={getPrice(49)}
                description="Perfect for small cafes and food trucks"
                features={[
                  { name: "Staff management", included: true },
                  { name: "Basic analytics", included: true },
                  { name: "Inventory tracking", included: true },
                  { name: "Email support", included: true },
                  { name: "1 location", included: true },
                  { name: "Reservation system", included: false },
                  { name: "Customer feedback tools", included: false },
                  { name: "API access", included: false },
                ]}
                buttonText="Start Free Trial"
                buttonLink="/register"
                highlighted={false}
                billingCycle={billingCycle}
              />
              <PricingCard
                title="Professional"
                price={getPrice(99)}
                description="Ideal for established restaurants"
                features={[
                  { name: "Everything in Starter", included: true },
                  { name: "Advanced analytics", included: true },
                  { name: "Reservation system", included: true },
                  { name: "Customer feedback tools", included: true },
                  { name: "Priority support", included: true },
                  { name: "3 locations", included: true },
                  { name: "Custom reporting", included: false },
                  { name: "API access", included: false },
                ]}
                buttonText="Start Free Trial"
                buttonLink="/register"
                highlighted={true}
                billingCycle={billingCycle}
                popularBadge={true}
              />
              <PricingCard
                title="Enterprise"
                price={getPrice(199)}
                description="For restaurant groups and chains"
                features={[
                  { name: "Everything in Professional", included: true },
                  { name: "Custom reporting", included: true },
                  { name: "API access", included: true },
                  { name: "Dedicated account manager", included: true },
                  { name: "Staff training", included: true },
                  { name: "Unlimited locations", included: true },
                  { name: "White-label option", included: true },
                  { name: "Custom integrations", included: true },
                ]}
                buttonText="Contact Sales"
                buttonLink="/contact"
                highlighted={false}
                billingCycle={billingCycle}
              />
            </div>

            {/* Enterprise CTA */}
            <div className="mt-16 bg-gray-50 border border-gray-200 rounded-lg p-8 text-center">
              <h3 className="text-2xl font-bold mb-4">Need a custom solution?</h3>
              <p className="text-lg text-gray-600 mb-6">
                We offer tailored solutions for large restaurant chains and unique business models.
              </p>
              <Link to="/contact">
                <Button size="lg" variant="outline">
                  Contact Our Sales Team
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto">
            <h2 className="text-3xl font-bold mb-12 text-center">Frequently Asked Questions</h2>

            <div className="space-y-8">
              <FaqItem
                question="How does the free trial work?"
                answer="Our free trial gives you full access to all features of your selected plan for 14 days. No credit card required to start. You can upgrade, downgrade, or cancel at any time during the trial period."
              />
              <FaqItem
                question="Can I change plans later?"
                answer="Yes, you can upgrade, downgrade, or cancel your plan at any time. If you upgrade, the new pricing will be applied immediately. If you downgrade, the new pricing will be applied at the start of your next billing cycle."
              />
              <FaqItem
                question="Is there a setup fee?"
                answer="No, there are no setup fees for any of our plans. You only pay the advertised monthly or annual subscription fee."
              />
              <FaqItem
                question="What payment methods do you accept?"
                answer="We accept all major credit cards (Visa, Mastercard, American Express) as well as PayPal. For Enterprise plans, we can also arrange for bank transfers or other payment methods."
              />
              <FaqItem
                question="Can I get a refund if I'm not satisfied?"
                answer="We offer a 30-day money-back guarantee for all new subscriptions. If you're not satisfied with our service within the first 30 days, contact our support team for a full refund."
              />
              <FaqItem
                question="Do you offer discounts for non-profits?"
                answer="Yes, we offer special pricing for non-profit organizations. Please contact our sales team for more information."
              />
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-blue-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">Ready to Get Started?</h2>
          <p className="text-xl text-white/80 max-w-3xl mx-auto mb-8">
            Join thousands of restaurants already using Promith to streamline their operations.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/register">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-white/90 w-full sm:w-auto">
                Start Free Trial
              </Button>
            </Link>
            <Link to="/demo">
              <Button size="lg" variant="outline" className="text-white border-white hover:bg-white/10 w-full sm:w-auto">
                Request Demo
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </MarketingLayout>
  );
};

// Helper Components
interface PricingCardProps {
  title: string;
  price: number;
  description: string;
  features: { name: string; included: boolean }[];
  buttonText: string;
  buttonLink: string;
  highlighted: boolean;
  billingCycle: "monthly" | "annual";
  popularBadge?: boolean;
}

const PricingCard = ({
  title,
  price,
  description,
  features,
  buttonText,
  buttonLink,
  highlighted,
  billingCycle,
  popularBadge = false,
}: PricingCardProps) => {
  return (
    <div
      className={`rounded-lg overflow-hidden ${
        highlighted
          ? 'ring-2 ring-blue-600 shadow-lg relative z-10 scale-105'
          : 'border border-gray-200 shadow'
      }`}
    >
      {popularBadge && (
        <div className="bg-blue-600 text-white text-xs font-semibold px-3 py-1 text-center">
          MOST POPULAR
        </div>
      )}
      <div className={`p-6 ${highlighted ? 'bg-blue-600 text-white' : 'bg-white'}`}>
        <h3 className="text-xl font-bold mb-1">{title}</h3>
        <div className="flex items-baseline mb-2">
          <span className="text-3xl font-bold">£{price.toFixed(2)}</span>
          <span className={`ml-1 text-sm ${highlighted ? 'text-white/80' : 'text-gray-500'}`}>
            /month
          </span>
        </div>
        <p className={highlighted ? 'text-white/80' : 'text-gray-600'}>
          {description}
        </p>
        {billingCycle === "annual" && (
          <p className={`mt-2 text-sm ${highlighted ? 'text-white/80' : 'text-gray-500'}`}>
            Billed annually (£{(price * 12).toFixed(2)})
          </p>
        )}
      </div>
      <div className="p-6 bg-card">
        <ul className="space-y-3 mb-6">
          {features.map((feature, index) => (
            <li key={index} className="flex items-start">
              {feature.included ? (
                <Check className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" />
              ) : (
                <div className="h-5 w-5 mr-2 flex-shrink-0" />
              )}
              <span className={feature.included ? 'text-card-foreground' : 'text-muted-foreground'}>
                {feature.name}
              </span>
            </li>
          ))}
        </ul>
        <Link to={buttonLink}>
          <Button
            className={`w-full ${highlighted ? 'bg-blue-600 hover:bg-blue-700' : ''}`}
            variant={highlighted ? 'default' : 'outline'}
          >
            {buttonText}
          </Button>
        </Link>
      </div>
    </div>
  );
};

interface FaqItemProps {
  question: string;
  answer: string;
}

const FaqItem = ({ question, answer }: FaqItemProps) => {
  return (
    <div className="border-b border-border pb-6">
      <h3 className="text-xl font-semibold mb-3 text-foreground">{question}</h3>
      <p className="text-muted-foreground">{answer}</p>
    </div>
  );
};

export default Pricing;
