import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, <PERSON><PERSON>Title, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/components/ui/sonner';
import { Table, Users } from 'lucide-react';
import { useSubscriptionAccess } from '@/hooks/useSubscriptionAccess';
import { FeatureGate } from '@/components/subscription/FeatureGate';
import { tableSessionService } from '@/services/tableSessionService';
import logger from '@/utils/logger';

interface TableInfo {
  id: string;
  area: string;
  number: number;
  type: "table" | "takeout";
  capacity?: number;
  status?: 'available' | 'occupied';
}

interface EnhancedTableSelectionProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectTable: (tableInfo: TableInfo) => void;
  orderType: 'dine-in' | 'takeaway';
}

// Generate all tables (1-20) with session status
const generateTables = (): TableInfo[] => {
  return Array.from({ length: 20 }, (_, i) => {
    const tableId = `table-${i + 1}`;
    const hasSession = tableSessionService.hasActiveSession(tableId);
    
    return {
      id: tableId,
      area: 'Main Dining',
      number: i + 1,
      type: 'table' as const,
      capacity: Math.floor(Math.random() * 6) + 2, // 2-8 capacity
      status: hasSession ? 'occupied' : 'available'
    };
  });
};

export const EnhancedTableSelection: React.FC<EnhancedTableSelectionProps> = ({
  isOpen,
  onClose,
  onSelectTable,
  orderType
}) => {
  const [customTableNumber, setCustomTableNumber] = useState('');
  const [tables] = useState<TableInfo[]>(generateTables());
  const { hasFeatureAccess, currentPlanName } = useSubscriptionAccess();

  // Handle takeaway order type - MOVED TO TOP LEVEL TO FIX HOOKS VIOLATION
  useEffect(() => {
    if (orderType === 'takeaway' && isOpen) {
      const takeawayTable: TableInfo = {
        id: 'takeaway-001',
        area: 'Takeaway',
        number: 1,
        type: 'takeout'
      };
      onSelectTable(takeawayTable);
      onClose();
    }
  }, [orderType, isOpen, onSelectTable, onClose]);

  const handleTableSelect = (tableId: string, tableNumber: number) => {
    const table = tables.find(t => t.id === tableId);
    if (!table) return;

    // Check if table has existing session
    const existingSession = tableSessionService.getSessionForTable(tableId);
    
    if (existingSession && table.status === 'occupied') {
      // Resume existing session
      logger.userAction('existing session resumed', 'EnhancedTableSelection', {
        tableId,
        tableNumber,
        orderType
      });
      
      toast.info(`Resuming existing order for Table ${tableNumber}`);
    } else {
      // New session
      logger.userAction('new table selected', 'EnhancedTableSelection', {
        tableId,
        tableNumber,
        orderType
      });
    }

    const tableInfo: TableInfo = {
      id: tableId,
      area: table.area,
      number: tableNumber,
      type: 'table',
      capacity: table.capacity,
      status: table.status
    };

    onSelectTable(tableInfo);
    onClose();
  };

  const handleCustomTableSubmit = () => {
    const tableNumber = parseInt(customTableNumber);
    if (isNaN(tableNumber) || tableNumber < 1) {
      toast.error('Please enter a valid table number');
      return;
    }

    const customTableId = `custom-table-${tableNumber}`;
    const tableInfo: TableInfo = {
      id: customTableId,
      area: 'Custom',
      number: tableNumber,
      type: 'table'
    };

    logger.userAction('custom table selected', 'EnhancedTableSelection', {
      tableNumber,
      orderType
    });

    onSelectTable(tableInfo);
    onClose();
  };

  const getTableStatusColor = (status: 'available' | 'occupied') => {
    switch (status) {
      case 'available':
        return 'bg-red-500 hover:bg-red-600 text-white'; // Red for available
      case 'occupied':
        return 'bg-green-500 hover:bg-green-600 text-white'; // Green for occupied
      default:
        return 'bg-gray-500 hover:bg-gray-600 text-white';
    }
  };

  const getTableStatusText = (status: 'available' | 'occupied') => {
    switch (status) {
      case 'available':
        return 'Available';
      case 'occupied':
        return 'Resume Order';
      default:
        return 'Unknown';
    }
  };

  // Early return for takeaway orders - no dialog needed
  if (orderType === 'takeaway') {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-4xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span className="flex items-center">
              <Table className="h-5 w-5 mr-2" />
              Select Table for Dine-in Order
            </span>
            <Badge variant="outline" className="text-blue-600 border-blue-200">
              {currentPlanName}
            </Badge>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Table Status Legend */}
          <div className="flex items-center justify-center space-x-6 p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-red-500 rounded"></div>
              <span className="text-sm text-gray-700">Available (New Order)</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-green-500 rounded"></div>
              <span className="text-sm text-gray-700">Occupied (Resume Order)</span>
            </div>
          </div>

          {/* Table Grid */}
          <ScrollArea className="h-[400px] rounded-md border p-4">
            <div className="grid grid-cols-4 gap-4">
              {tables.map(table => (
                <Button
                  key={table.id}
                  variant="outline"
                  className={`h-24 flex flex-col border-2 ${getTableStatusColor(table.status!)}`}
                  onClick={() => handleTableSelect(table.id, table.number)}
                >
                  <span className="text-lg font-bold">Table {table.number}</span>
                  <span className="text-xs opacity-90">
                    {getTableStatusText(table.status!)}
                  </span>
                  {table.capacity && (
                    <div className="flex items-center text-xs opacity-75 mt-1">
                      <Users className="h-3 w-3 mr-1" />
                      {table.capacity}
                    </div>
                  )}
                </Button>
              ))}
            </div>
          </ScrollArea>

          {/* Advanced Table Features (Pro/Customized only) */}
          <FeatureGate feature="advanced-epos" silent={true}>
            <div className="space-y-2 p-4 border-t bg-blue-50 rounded-lg">
              <Label htmlFor="custom-table" className="text-sm font-medium">
                Custom Table Number (Advanced Feature)
              </Label>
              <div className="flex gap-2">
                <Input
                  id="custom-table"
                  value={customTableNumber}
                  onChange={(e) => setCustomTableNumber(e.target.value)}
                  placeholder="Enter table number"
                  type="number"
                  className="flex-1"
                />
                <Button onClick={handleCustomTableSubmit} variant="outline">
                  Select Custom Table
                </Button>
              </div>
              <p className="text-xs text-gray-600">
                Use this for tables not shown in the grid or special seating arrangements.
              </p>
            </div>
          </FeatureGate>

          {/* Basic Plan Custom Table (Limited) */}
          <FeatureGate
            feature="advanced-epos"
            fallback={
              <div className="space-y-2 p-4 border-t">
                <Label htmlFor="basic-custom-table" className="text-sm font-medium">
                  Custom Table Number
                </Label>
                <div className="flex gap-2">
                  <Input
                    id="basic-custom-table"
                    value={customTableNumber}
                    onChange={(e) => setCustomTableNumber(e.target.value)}
                    placeholder="Enter table number"
                    type="number"
                    className="flex-1"
                  />
                  <Button onClick={handleCustomTableSubmit} variant="outline">
                    Confirm
                  </Button>
                </div>
              </div>
            }
            silent={true}
          >
            {/* Advanced table management features would go here */}
            <div className="space-y-2 p-4 border-t">
              <p className="text-sm text-muted-foreground">
                Advanced table management features available with Pro plan.
              </p>
            </div>
          </FeatureGate>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
