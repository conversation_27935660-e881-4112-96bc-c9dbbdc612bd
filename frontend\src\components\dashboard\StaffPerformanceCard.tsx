
import { 
  <PERSON>, 
  <PERSON><PERSON>ontent, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON>Title 
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { cn } from "@/lib/utils";

interface StaffMember {
  id: string;
  name: string;
  role: string;
  performance: number;
  metrics: {
    sales: number;
    tablesTurned: number;
    customerRating: number;
  };
  avatar?: string;
}

interface StaffPerformanceCardProps {
  staff: StaffMember[];
}

const StaffPerformanceCard = ({ staff }: StaffPerformanceCardProps) => {
  // Sort by performance in descending order
  const sortedStaff = [...staff].sort((a, b) => b.performance - a.performance);
  
  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-medium">Staff Performance</CardTitle>
      </CardHeader>
      <CardContent className="px-6">
        <div className="space-y-5">
          {sortedStaff.map((member) => {
            // Determine color based on performance
            const getColor = () => {
              if (member.performance >= 85) return "bg-restaurant-success";
              if (member.performance >= 70) return "bg-restaurant-accent";
              if (member.performance >= 50) return "bg-restaurant-warning";
              return "bg-restaurant-secondary";
            };
            
            // Get first initials for avatar fallback
            const getInitials = () => {
              return member.name
                .split(" ")
                .map((n) => n[0])
                .join("")
                .toUpperCase();
            };

            return (
              <div key={member.id} className="space-y-2">
                <div className="flex items-center gap-3">
                  <Avatar className="h-8 w-8">
                    {member.avatar && <img src={member.avatar} alt={member.name} />}
                    <AvatarFallback>{getInitials()}</AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="flex justify-between text-sm">
                      <div className="font-medium">{member.name}</div>
                      <div className="text-muted-foreground">{member.performance}%</div>
                    </div>
                    <Progress
                      value={member.performance}
                      className="h-2 mt-1"
                      indicatorClassName={getColor()}
                    />
                  </div>
                </div>
                <div className="flex text-xs text-muted-foreground justify-between px-2">
                  <span>Sales: ${member.metrics.sales}</span>
                  <span>Tables: {member.metrics.tablesTurned}</span>
                  <span>Rating: {member.metrics.customerRating}/5</span>
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};

export default StaffPerformanceCard;
