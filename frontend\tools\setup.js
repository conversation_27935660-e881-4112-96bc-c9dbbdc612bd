/**
 * RestroManage-v0 Setup Tool
 * 
 * This script helps set up the initial environment for the RestroManage system.
 */

const fs = require('fs');
const path = require('path');

// Directories to create
const directories = [
  'uploads',
  'uploads/images',
  'uploads/invoices',
  'uploads/reports'
];

// Create necessary directories
console.log('Creating necessary directories...');
directories.forEach(dir => {
  const dirPath = path.join(__dirname, '..', dir);
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`Created directory: ${dir}`);
  } else {
    console.log(`Directory already exists: ${dir}`);
  }
});

console.log('\nSetup complete! The RestroManage system is ready to use.');
console.log('Run "npm start" to start the server or "npm run dev" for development mode.');
