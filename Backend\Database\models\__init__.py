"""
SQLAlchemy models for RestroManage application.
All database models are defined here and correspond to Pydantic models.
"""

from .base import BaseModel, TimestampMixin, SoftDeleteMixin
from .auth import User, UserSession
from .restaurants import Restaurant, RestaurantUser
from .menu import MenuItem, MenuCategory
from .orders import Order, OrderItem, OrderDiscount
from .discounts import PromoCode, PromoCodeUsage, Campaign
from .staff import Staff, StaffSchedule
from .tables import Table, TableReservation
from .inventory import InventoryItem, InventoryTransaction
from .split_bills import SplitBill, SplitPortion, SplitBillPayment
from .analytics import SalesRecord, CustomerAnalytics
from .forecasting import ForecastData, TrendAnalysis
from .ai_models import AIRequest, AIResponse, AIUsageStats
from .subscription import SubscriptionPlan, RestaurantSubscription, FeatureDefinition, FeatureAccessLog, SubscriptionUsageMetrics

__all__ = [
    # Base models
    "BaseModel",
    "TimestampMixin", 
    "SoftDeleteMixin",
    
    # Authentication & Users
    "User",
    "UserSession",
    
    # Restaurants
    "Restaurant",
    "RestaurantUser",
    
    # Menu
    "MenuItem",
    "MenuCategory",
    
    # Orders
    "Order",
    "OrderItem", 
    "OrderDiscount",
    
    # Discounts
    "PromoCode",
    "PromoCodeUsage",
    "Campaign",
    
    # Staff
    "Staff",
    "StaffSchedule",
    
    # Tables
    "Table",
    "TableReservation",
    
    # Inventory
    "InventoryItem",
    "InventoryTransaction",
    
    # Split Bills
    "SplitBill",
    "SplitPortion",
    "SplitBillPayment",
    
    # Analytics
    "SalesRecord",
    "CustomerAnalytics",
    
    # Forecasting
    "ForecastData",
    "TrendAnalysis",
    
    # AI Models
    "AIRequest",
    "AIResponse",
    "AIUsageStats",

    # Subscription Models
    "SubscriptionPlan",
    "RestaurantSubscription",
    "FeatureDefinition",
    "FeatureAccessLog",
    "SubscriptionUsageMetrics"
]
