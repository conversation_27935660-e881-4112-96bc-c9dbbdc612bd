import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { initializeMockData, loadPersistedData, getAllRestaurants, getAllUsers, validateLoginCredentials, findRestaurantByCode, findUserByEmail, debugMockData, setExistingRestaurants } from "@/services/mockDataService";
import apiService, { handleApiError, checkBackendConnectivity, apiRequest } from "@/services/apiService";
import logger from "@/utils/logger";
import { fetchCSV } from "@/utils/csvParser";
import { createDefaultUserForPin, validateTokenWithBackend } from "@/services/authService";
import { getStoredUser, isTokenExpired } from '@/utils/authUtils';

// Define user types
export type UserRole = 'admin' | 'manager' | 'waiter' | 'chef' | 'hostess' | 'bartender' | 'staff';

// Map any role string to a valid UserRole
const mapRoleToUserRole = (role: string): UserRole => {
  // Log the role for debugging
  logger.debug('Mapping role:', role);

  if (!role) {
    logger.warn('Empty role provided, defaulting to "staff"');
    return 'staff';
  }

  // Convert to lowercase for case-insensitive comparison
  const lowerRole = role.toLowerCase().trim();
  logger.debug('Normalized role for mapping:', lowerRole);

  // Direct mapping for known roles
  switch (lowerRole) {
    case 'admin':
      logger.debug('Mapped to admin role', 'AuthContext');
      return 'admin';
    case 'manager':
      logger.debug('Mapped to manager role', 'AuthContext');
      return 'manager';
    case 'waiter':
      logger.debug('Mapped to waiter role', 'AuthContext');
      return 'waiter';
    case 'chef':
      logger.debug('Mapped to chef role', 'AuthContext');
      return 'chef';
    case 'hostess':
      logger.debug('Mapped to hostess role', 'AuthContext');
      return 'hostess';
    case 'bartender':
      logger.debug('Mapped to bartender role', 'AuthContext');
      return 'bartender';
    default:
      // For unknown roles, check if they contain known keywords
      if (lowerRole.includes('admin')) {
        logger.debug('Contains "admin", mapped to admin role');
        return 'admin';
      } else if (lowerRole.includes('manage')) {
        logger.debug('Contains "manage", mapped to manager role');
        return 'manager';
      } else if (lowerRole.includes('wait')) {
        logger.debug('Contains "wait", mapped to waiter role');
        return 'waiter';
      } else if (lowerRole.includes('chef') || lowerRole.includes('cook')) {
        logger.debug('Contains "chef/cook", mapped to chef role');
        return 'chef';
      } else if (lowerRole.includes('host')) {
        logger.debug('Contains "host", mapped to hostess role');
        return 'hostess';
      } else if (lowerRole.includes('bar')) {
        logger.debug('Contains "bar", mapped to bartender role');
        return 'bartender';
      } else {
        logger.debug('Unknown role, defaulting to "staff"');
        return 'staff';
      }
  }
};

export interface User {
  id: string;
  name: string;
  username?: string; // Optional username field for login
  email: string;
  phone: string;
  restaurant_id: string;
  restaurant_name: string;
  role: UserRole;
  position: string;
  pin: string;
  status: string;
  hireDate: string;
  performance: number;
  accessLevel: 'full' | 'limited';
  access_token?: string;
  token_type?: string;
  expires_at?: number;
  assignedHours?: number;
  availableDays?: string;
}

export interface Restaurant {
  id: string;
  code: string;
  name: string;
  logo: string;
  address: string;
  phone: string;
  email: string;
  vatRate: number;
  currency: string;
  isActive: boolean;
  hasData?: boolean;
  subscriptionPlan: "basic" | "pro" | "customized";
  subscriptionStatus: "active" | "trial" | "expired" | "cancelled";
  subscriptionExpiresAt?: string;
  customizedFeatures?: string[];
  billingInfo?: {
    monthlyPrice: number;
    currency: string;
    nextBillingDate: string;
  };
}

// Define context type
interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isRestaurantAuthenticated: boolean;
  currentRestaurant: Restaurant | null;
  isLoading: boolean;
  isInitialized: boolean; // New state to track when initial auth check is complete
  login: (email: string, password: string, remember: boolean) => Promise<{
    success: boolean;
    message: string;
    user?: User;
  }>;
  loginRestaurant: (code: string, password: string, remember: boolean) => Promise<{
    success: boolean;
    message: string;
    restaurantName?: string;
  }>;
  loginWithPin: (pin: string) => Promise<{
    success: boolean;
    message: string;
    userName?: string;
    accessLevel?: 'full' | 'limited';
    role?: UserRole;
  }>;
  logout: () => void;
  logoutRestaurant: () => void;
  refreshRestaurantData: () => Promise<void>;
  allUsers: User[];
  restaurantUsers: User[];
}

// Create context with default values
const AuthContext = createContext<AuthContextType>({
  user: null,
  isAuthenticated: false,
  isRestaurantAuthenticated: false,
  currentRestaurant: null,
  isLoading: true,
  isInitialized: false,
  login: async () => ({ success: false, message: "Not implemented" }),
  loginRestaurant: async () => ({ success: false, message: "Not implemented" }),
  loginWithPin: async () => ({ success: false, message: "Not implemented" }),
  logout: () => {},
  logoutRestaurant: () => {},
  refreshRestaurantData: async () => {},
  allUsers: [],
  restaurantUsers: [],
});

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isRestaurantAuthenticated, setIsRestaurantAuthenticated] = useState<boolean>(false);
  const [currentRestaurant, setCurrentRestaurant] = useState<Restaurant | null>(null);
  const [allUsers, setAllUsers] = useState<User[]>([]);
  const [allRestaurants, setAllRestaurants] = useState<Restaurant[]>([]);
  const [restaurantUsers, setRestaurantUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isInitialized, setIsInitialized] = useState<boolean>(false);

  // Mock restaurant data for fallback
  logger.debug('Setting up mock restaurant data', 'AuthContext');
  const MOCK_RESTAURANTS: Restaurant[] = [
    {
      id: "1",
      code: "GK001",
      name: "The Gourmet Kitchen",
      logo: "/logos/gourmet-kitchen.png",
      address: "123 Main Street, Anytown, UK",
      phone: "(*************",
      email: "<EMAIL>",
      vatRate: 20,
      currency: "GBP",
      isActive: true,
      hasData: true,
      subscriptionPlan: "pro" as "basic" | "pro" | "customized",
      subscriptionStatus: "active" as "active" | "trial" | "expired" | "cancelled",
      subscriptionExpiresAt: "2025-12-31T23:59:59.000Z",
      billingInfo: {
        monthlyPrice: 69,
        currency: "GBP",
        nextBillingDate: "2025-07-10T00:00:00.000Z"
      }
    },
    {
      id: "2",
      code: "PP002",
      name: "Pasta Paradise",
      logo: "/logos/pasta-paradise.png",
      address: "456 Elm Avenue, Somewhere, UK",
      phone: "(*************",
      email: "<EMAIL>",
      vatRate: 20,
      currency: "GBP",
      isActive: true,
      hasData: true,
      subscriptionPlan: "basic" as "basic" | "pro" | "customized",
      subscriptionStatus: "active" as "active" | "trial" | "expired" | "cancelled",
      subscriptionExpiresAt: "2025-12-31T23:59:59.000Z",
      billingInfo: {
        monthlyPrice: 29,
        currency: "GBP",
        nextBillingDate: "2025-07-10T00:00:00.000Z"
      }
    },
    {
      id: "3",
      code: "SS003",
      name: "Sushi Paradise",
      logo: "/logos/sushi-paradise.png",
      address: "789 Sushi Street, Japan Town, UK",
      phone: "(*************",
      email: "<EMAIL>",
      vatRate: 20,
      currency: "GBP",
      isActive: true,
      hasData: true,
      subscriptionPlan: "basic" as "basic" | "pro" | "customized",
      subscriptionStatus: "active" as "active" | "trial" | "expired" | "cancelled",
      subscriptionExpiresAt: "2025-12-31T23:59:59.000Z",
      billingInfo: {
        monthlyPrice: 29,
        currency: "GBP",
        nextBillingDate: "2025-07-10T00:00:00.000Z"
      }
    }
  ].map(restaurant => ({
    ...restaurant,
    isActive: true // Ensure isActive is a boolean true
  }));

  // Mock user data
  const MOCK_USERS: User[] = [
        {
          id: "1",
          name: "Michael Rodriguez",
          username: "michael.r",
          email: "<EMAIL>",
          phone: "(*************",
          restaurant_id: "1",
          restaurant_name: "The Gourmet Kitchen",
          role: "waiter",
          position: "Head Waiter",
          pin: "1234",
          status: "active",
          hireDate: "2022-03-15",
          performance: 92,
          accessLevel: "limited"
        },
        {
          id: "2",
          name: "Jennifer Smith",
          username: "jennifer.s",
          email: "<EMAIL>",
          phone: "(*************",
          restaurant_id: "1",
          restaurant_name: "The Gourmet Kitchen",
          role: "waiter",
          position: "Waiter",
          pin: "2345",
          status: "active",
          hireDate: "2022-05-20",
          performance: 78,
          accessLevel: "limited"
        },
        {
          id: "3",
          name: "Robert Johnson",
          username: "robert.j",
          email: "<EMAIL>",
          phone: "(*************",
          restaurant_id: "1",
          restaurant_name: "The Gourmet Kitchen",
          role: "manager",
          position: "Floor Manager",
          pin: "5678",
          status: "active",
          hireDate: "2021-06-15",
          performance: 95,
          accessLevel: "full"
        },
        {
          id: "4",
          name: "Admin User",
          username: "admin",
          email: "<EMAIL>",
          phone: "(*************",
          restaurant_id: "0",
          restaurant_name: "All Restaurants",
          role: "admin",
          position: "System Admin",
          pin: "0000",
          status: "active",
          hireDate: "2020-01-01",
          performance: 100,
          accessLevel: "full"
        },
        // Add users for other restaurants
        {
          id: "5",
          name: "David Wilson",
          email: "<EMAIL>",
          phone: "(*************",
          restaurant_id: "2",
          restaurant_name: "Pasta Paradise",
          role: "manager",
          position: "Restaurant Manager",
          pin: "1111",
          status: "active",
          hireDate: "2021-08-10",
          performance: 90,
          accessLevel: "full"
        },
        {
          id: "6",
          name: "Sarah Miller",
          email: "<EMAIL>",
          phone: "(*************",
          restaurant_id: "2",
          restaurant_name: "Pasta Paradise",
          role: "waiter",
          position: "Senior Waiter",
          pin: "2222",
          status: "active",
          hireDate: "2022-01-15",
          performance: 85,
          accessLevel: "limited"
        },
        {
          id: "7",
          name: "James Brown",
          email: "<EMAIL>",
          phone: "(*************",
          restaurant_id: "3",
          restaurant_name: "Sushi Paradise",
          role: "manager",
          position: "Floor Manager",
          pin: "3333",
          status: "active",
          hireDate: "2021-05-20",
          performance: 88,
          accessLevel: "full"
        },
        {
          id: "8",
          name: "Emily Davis",
          email: "<EMAIL>",
          phone: "(*************",
          restaurant_id: "3",
          restaurant_name: "Sushi Paradise",
          role: "chef",
          position: "Sushi Chef",
          pin: "4444",
          status: "active",
          hireDate: "2021-09-15",
          performance: 95,
          accessLevel: "limited"
        }
      ];

  // Enhanced token validation with logging for STRICT TWO-STEP AUTH
  const validateToken = async (user: User): Promise<boolean> => {
    try {
      logger.debug('🔍 STRICT AUTH: Validating staff token with backend', 'AuthContext', { userId: user.id });

      // Check if token is expired locally first
      if (isTokenExpired(user)) {
        logger.warn('🔒 STRICT AUTH: Staff token expired locally, requiring fresh login', 'AuthContext');
        return false;
      }

      // Use the authService function for validation
      const isValid = await validateTokenWithBackend(user);

      if (isValid) {
        logger.debug('✅ STRICT AUTH: Staff token validation successful', 'AuthContext');
        return true;
      } else {
        logger.warn('❌ STRICT AUTH: Staff token validation failed, requiring fresh login', 'AuthContext');
        return false;
      }
    } catch (error) {
      logger.warn('🔒 STRICT AUTH: Staff token validation error, requiring fresh login', 'AuthContext', { error: error.message });
      return false;
    }
  };

  // Helper function to check authentication state for strict two-step flow
  const getAuthenticationStatus = () => {
    const status = {
      restaurantAuthenticated: isRestaurantAuthenticated,
      staffAuthenticated: isAuthenticated,
      bothAuthenticated: isRestaurantAuthenticated && isAuthenticated,
      nextRequiredStep: !isRestaurantAuthenticated ? 'restaurant-login' :
                       !isAuthenticated ? 'staff-login' : 'complete'
    };

    logger.debug('🔒 STRICT AUTH: Current authentication status', 'AuthContext', status);
    return status;
  };

  // Load users and restaurants - backend-first with fallbacks
  useEffect(() => {
    const loadData = async () => {
      logger.info('🔄 Starting authentication system initialization', 'AuthContext');
      setIsLoading(true);

      try {
        // Check backend connectivity first
        const isBackendAvailable = await checkBackendConnectivity();

        if (isBackendAvailable) {
          logger.info('✅ Backend available, loading data from API', 'AuthContext');
          // Load data from backend API
          const [restaurantsResponse, usersResponse] = await Promise.all([
            apiRequest('/restaurants'),
            apiRequest('/restaurants/users/all')
          ]);

          logger.dataOperation("load", "data from backend API", "AuthContext", {
            restaurants: Array.isArray(restaurantsResponse) ? restaurantsResponse.length : 0,
            users: Array.isArray(usersResponse) ? usersResponse.length : 0
          });

          setAllRestaurants(Array.isArray(restaurantsResponse) ? restaurantsResponse : []);
          setAllUsers(Array.isArray(usersResponse) ? usersResponse : []);
          setExistingRestaurants(Array.isArray(restaurantsResponse) ? restaurantsResponse : []);

          logger.info('✅ Backend data loaded successfully', 'AuthContext');
          return; // Don't set loading to false here - let session restoration complete first
        }
      } catch (error) {
        logger.logError(error, 'loading data from backend API', 'AuthContext');
      }

      // Fallback to data.json if backend unavailable
      try {
        logger.warn('⚠️ Backend unavailable, falling back to data.json', 'AuthContext');
        const response = await fetch('/data/data.json');
        if (response.ok) {
          const jsonData = await response.json();

          if (jsonData.restaurants && jsonData.users) {
            setAllRestaurants(jsonData.restaurants);
            setAllUsers(jsonData.users);
            setExistingRestaurants(jsonData.restaurants);
            logger.info('✅ Successfully loaded fallback data from data.json', 'AuthContext');
          }
        }
      } catch (fallbackError) {
        logger.logError(fallbackError, 'loading fallback data', 'AuthContext');

        // Final fallback to mock data
        logger.warn('⚠️ Using mock data as final fallback', 'AuthContext');
        setAllRestaurants(MOCK_RESTAURANTS);
        setAllUsers(MOCK_USERS);
        setExistingRestaurants(MOCK_RESTAURANTS);
      }

      // Data loading complete, but don't set loading to false yet
      // Session restoration will handle setting loading to false
      logger.info('📊 Data loading phase complete, proceeding to session restoration', 'AuthContext');
    };

    loadData();
  }, []);

  // Check for existing user session on mount - enhanced with proper loading state management
  useEffect(() => {
    // Only run session restoration when data is loaded but we haven't finished initialization
    if (allRestaurants.length === 0 || allUsers.length === 0) {
      logger.debug('⏳ Waiting for data to load before session restoration', 'AuthContext');
      return;
    }

    // If we've already completed initialization, don't run again
    if (isInitialized) {
      logger.debug('✅ Session already restored, skipping', 'AuthContext');
      return;
    }

    logger.info('🔍 Starting session restoration process', 'AuthContext');

    const restoreSession = async () => {
      try {
        const storedUser = localStorage.getItem('user');
        const storedAuth = localStorage.getItem('isLoggedIn');
        const storedRestaurant = localStorage.getItem('currentRestaurant');
        const storedRestaurantAuth = localStorage.getItem('isRestaurantLoggedIn');

        // DEVELOPMENT: Auto-login for development mode
        const isDevelopment = process.env.NODE_ENV === 'development';
        const devAutoLogin = localStorage.getItem('devAutoLogin') !== 'false'; // Default to true

        logger.debug('📋 Stored session data', 'AuthContext', {
          hasUser: !!storedUser,
          isLoggedIn: storedAuth === 'true',
          hasRestaurant: !!storedRestaurant,
          isRestaurantLoggedIn: storedRestaurantAuth === 'true',
          isDevelopment,
          devAutoLogin
        });

        // Restore restaurant session ONLY - staff authentication is always required
        if (storedRestaurant && storedRestaurantAuth === 'true') {
          try {
            const parsedRestaurant = JSON.parse(storedRestaurant);
            logger.info('🏪 Restoring restaurant session:', parsedRestaurant.name);

            // Ensure isActive is true
            const fixedRestaurant = {
              ...parsedRestaurant,
              isActive: true
            };

            logger.debug('✅ Restored restaurant with forced isActive=true', 'AuthContext', { restaurant: fixedRestaurant.name });

            setCurrentRestaurant(fixedRestaurant);
            setIsRestaurantAuthenticated(true);

            // STRICT TWO-STEP AUTH: Always clear staff authentication on session restoration
            // Staff must manually authenticate on every session
            if (storedUser || storedAuth === 'true') {
              logger.info('🔒 STRICT AUTH: Clearing staff session - staff login required every session', 'AuthContext');
              localStorage.removeItem('user');
              localStorage.removeItem('isLoggedIn');
              setUser(null);
              setIsAuthenticated(false);
            }

            logger.info('✅ Restaurant session restored, staff login required', 'AuthContext', {
              restaurant: fixedRestaurant.name,
              staffAuthRequired: true
            });
          } catch (restaurantParseError) {
            logger.logError(restaurantParseError, 'parsing stored restaurant data', 'AuthContext');
            // Clear corrupted restaurant data
            localStorage.removeItem('currentRestaurant');
            localStorage.removeItem('isRestaurantLoggedIn');
          }
        } else {
          logger.debug('🧹 No valid restaurant session found, clearing any stale data', 'AuthContext');
          // If no restaurant session, clear any user session too
          localStorage.removeItem('user');
          localStorage.removeItem('isLoggedIn');
          localStorage.removeItem('currentRestaurant');
          localStorage.removeItem('isRestaurantLoggedIn');

          // STRICT TWO-STEP AUTH: Always ensure clean state when no restaurant session
          setCurrentRestaurant(null);
          setIsRestaurantAuthenticated(false);
          setUser(null);
          setIsAuthenticated(false);

          logger.info('🔒 STRICT AUTH: No restaurant session - both restaurant and staff login required', 'AuthContext');
        }

        // Session restoration complete - now safe to set loading to false and mark as initialized
        logger.info('✅ Session restoration complete, authentication system ready', 'AuthContext');
        setIsLoading(false);
        setIsInitialized(true);

      } catch (error) {
        logger.logError(error, 'session restoration process', 'AuthContext');
        // Even if session restoration fails, we need to set loading to false and mark as initialized
        setIsLoading(false);
        setIsInitialized(true);
      }
    };

    restoreSession();
  }, [allRestaurants.length, allUsers.length, isInitialized]); // Run when data is loaded and we haven't initialized yet

  // Update restaurant users when restaurant changes
  useEffect(() => {
    logger.debug('Restaurant users filter effect triggered', 'AuthContext', {
      hasCurrentRestaurant: !!currentRestaurant,
      restaurantId: currentRestaurant?.id,
      restaurantName: currentRestaurant?.name,
      allUsersCount: allUsers.length,
      isLoading
    });

    if (currentRestaurant && !isLoading) {
      logger.debug('Filtering users for restaurant', 'AuthContext', {
        restaurantName: currentRestaurant.name,
        restaurantId: currentRestaurant.id,
        allUsersCount: allUsers.length
      });

      // Log all users for debugging
      logger.debug('All users data', 'AuthContext', {
        users: allUsers.map(u => ({
          id: u.id,
          name: u.name,
          role: u.role,
          restaurant_id: u.restaurant_id,
          pin: u.pin,
          status: u.status
        }))
      });

      // Filter users for the current restaurant
      const filteredUsers = allUsers.filter(u => {
        const isForThisRestaurant = u.restaurant_id === currentRestaurant.id;
        const isSystemAdmin = u.restaurant_id === '0'; // System-wide admin users
        const shouldInclude = isForThisRestaurant || isSystemAdmin;

        logger.debug('User filter check', 'AuthContext', {
          userName: u.name,
          userRestaurantId: u.restaurant_id,
          currentRestaurantId: currentRestaurant.id,
          isForThisRestaurant,
          isSystemAdmin,
          shouldInclude
        });

        return shouldInclude;
      });

      logger.debug('User filtering complete', 'AuthContext', {
        filteredCount: filteredUsers.length,
        filteredUsers: filteredUsers.map(u => ({
          id: u.id,
          name: u.name,
          role: u.role,
          restaurant_id: u.restaurant_id,
          pin: u.pin
        }))
      });

      // Force update restaurant users
      setRestaurantUsers([...filteredUsers]);
    } else {
      logger.debug('Clearing restaurant users', 'AuthContext', {
        reason: !currentRestaurant ? 'no current restaurant' : 'still loading'
      });
      setRestaurantUsers([]);
    }
  }, [currentRestaurant, allUsers, isLoading]);

  // Restaurant login function
  // Regular user login function for registered users
  const login = async (
    usernameOrEmail: string,
    password: string,
    remember: boolean
  ): Promise<{ success: boolean; message: string; user?: User }> => {
    logger.authentication("login attempt", "success", "AuthContext", { usernameOrEmail });

    if (isLoading) {
      logger.warn("Login attempted while system loading", "AuthContext");
      return { success: false, message: "System is loading, please try again" };
    }

    // Helper function to check if input is email format
    const isEmail = (input: string): boolean => {
      return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(input);
    };

    // First, try to validate against registered users
    // For registered users, we only support email login for now
    if (isEmail(usernameOrEmail)) {
      const registeredUser = validateLoginCredentials(usernameOrEmail, password);
      if (registeredUser && registeredUser.isSetupComplete) {
        logger.authentication("found registered user", "success", "AuthContext", { email: usernameOrEmail });

        // Find the corresponding mock user entry
        const mockUser = findUserByEmail(usernameOrEmail);
        if (mockUser) {
          logger.authentication("found mock user entry", "success", "AuthContext", { email: usernameOrEmail });

          // Set user and authentication state
          const completeUser: User = {
            ...mockUser,
            hireDate: mockUser.hireDate || "2020-01-01",
            performance: mockUser.performance || 100,
            accessLevel: mockUser.accessLevel || "limited"
          };
          setUser(completeUser);
          setIsAuthenticated(true);

          // Store session data
          if (remember) {
            localStorage.setItem("user", JSON.stringify(mockUser));
            localStorage.setItem("isLoggedIn", "true");
          }

          return {
            success: true,
            message: "Login successful",
            user: {
              ...mockUser,
              hireDate: mockUser.hireDate || "2020-01-01",
              performance: mockUser.performance || 100,
              accessLevel: mockUser.accessLevel || "limited"
            },
          };
        } else {
          logger.warn("Mock user entry not found for registered user", "AuthContext");
          return { success: false, message: "User profile not found. Please complete setup." };
        }
      }
    }

    // Fallback: try existing mock users (for backward compatibility)
    // Support both email and username lookup
    const existingUser = allUsers.find(user => {
      if (isEmail(usernameOrEmail)) {
        return user.email === usernameOrEmail;
      } else {
        // Check username (if available) or name field as fallback
        return user.username === usernameOrEmail || user.name.toLowerCase().replace(/\s+/g, '') === usernameOrEmail.toLowerCase();
      }
    });

    if (existingUser) {
      logger.authentication("found existing mock user", "success", "AuthContext", { usernameOrEmail });

      // For existing mock users, we dont have password validation
      // This is for backward compatibility with existing mock data
      setUser(existingUser);
      setIsAuthenticated(true);

      if (remember) {
        localStorage.setItem("user", JSON.stringify(existingUser));
        localStorage.setItem("isLoggedIn", "true");
      }

      return {
        success: true,
        message: "Login successful",
        user: existingUser,
      };
    }

    logger.authentication("login", "failure", "AuthContext", { usernameOrEmail });
    return { success: false, message: "Invalid username/email or password" };
  };

  const loginRestaurant = async (
    code: string,
    password: string,
    remember: boolean
  ): Promise<{ success: boolean; message: string; restaurantName?: string }> => {
    logger.authentication('restaurant login attempt', 'success', 'AuthContext', { code });

    if (isLoading) {
      logger.debug('System is still loading', 'AuthContext');
      return { success: false, message: 'System is loading, please try again' };
    }

    // Try backend authentication first
    try {
      const backendAvailable = await apiService.checkBackendConnectivity();

      if (backendAvailable) {
        logger.info('Attempting backend restaurant login', 'AuthContext', { code });

        const loginResponse = await apiService.restaurant.login(code, password);

        if (loginResponse.success && loginResponse.restaurant) {
          const restaurant = loginResponse.restaurant;

          // Ensure restaurant is active
          const foundRestaurant = {
            ...restaurant,
            isActive: true
          };

          logger.authentication('restaurant login', 'success', 'AuthContext', {
            restaurant: foundRestaurant.name,
            method: 'backend'
          });

          // Update state
          setCurrentRestaurant(foundRestaurant);
          setIsRestaurantAuthenticated(true);

          // Store in localStorage/sessionStorage
          const restaurantToStore = {
            ...foundRestaurant,
            isActive: true
          };

          localStorage.setItem('currentRestaurant', JSON.stringify(restaurantToStore));
          localStorage.setItem('isRestaurantLoggedIn', 'true');

          if (!remember) {
            sessionStorage.setItem('currentRestaurant', JSON.stringify(restaurantToStore));
            sessionStorage.setItem('isRestaurantLoggedIn', 'true');
          }

          // Load users for this restaurant from backend or local data
          const filteredUsers = allUsers.filter(u =>
            u.restaurant_id === foundRestaurant.id ||
            u.restaurant_id === '0' // System-wide admin users
          );

          setRestaurantUsers(filteredUsers);

          return {
            success: true,
            message: 'Restaurant authenticated - staff login required',
            restaurantName: foundRestaurant.name
          };
        }
      }
    } catch (apiError) {
      const errorInfo = handleApiError(apiError);
      logger.logError(apiError, 'backend restaurant login', 'AuthContext');

      if (!errorInfo.isConnectivityError) {
        // Non-connectivity error from backend
        return { success: false, message: errorInfo.message };
      }

      // Fall through to localStorage fallback for connectivity errors
      logger.warn('Backend unavailable, falling back to localStorage', 'AuthContext');
    }

    // Fallback to localStorage authentication
    logger.info('Using localStorage fallback for restaurant login', 'AuthContext', { code });
    logger.debug('All restaurants count: ' + allRestaurants.length, 'AuthContext');

    // Find restaurant with matching code
    let foundRestaurant = allRestaurants.find(r => r.code === code);

    if (foundRestaurant) {
      // Force isActive to be true for all restaurants
      foundRestaurant = {
        ...foundRestaurant,
        isActive: true
      };

      logger.debug('Found restaurant: ' + foundRestaurant.name + ' (Active: ' + foundRestaurant.isActive + ')', 'AuthContext');
    } else {
      logger.authentication('restaurant login', 'failure', 'AuthContext', { reason: 'restaurant not found' });
    }

    // Check if restaurant exists
    if (!foundRestaurant) {
      logger.debug('Restaurant not found with code:', code);
      return { success: false, message: 'Restaurant not found' };
    }

    // In a real app, we would check password hash
    // For demo purposes, we'll use a simple password check
    // GK001/gourmet123, PP002/pasta123, SS003/sushi123, PU837/test123
    const restaurantPasswords: Record<string, string> = {
      'GK001': 'gourmet123',
      'PP002': 'pasta123',
      'SS003': 'sushi123',
      'PU837': 'test123'
    };

    logger.debug('Checking password for restaurant code:', foundRestaurant.code);
    logger.debug('Expected password:', restaurantPasswords[foundRestaurant.code]);
    logger.debug('Provided password:', password ? '********' : 'empty');

    if (restaurantPasswords[foundRestaurant.code] !== password) {
      logger.debug('Invalid password for restaurant:', foundRestaurant.code);
      return { success: false, message: 'Invalid password' };
    }

    logger.debug('Checking if restaurant is active: ' + foundRestaurant.isActive, 'AuthContext');
    logger.debug('Restaurant isActive type:', typeof foundRestaurant.isActive);

    // Log to terminal for debugging
    logger.debug('Restaurant isActive status', 'AuthContext', { isActive: foundRestaurant.isActive, type: typeof foundRestaurant.isActive });
    logger.debug("Restaurant active status check", "AuthContext", { isActive: foundRestaurant.isActive });
    logger.debug('Restaurant isActive boolean conversion', 'AuthContext', { boolean: Boolean(foundRestaurant.isActive) });

    // Force isActive to be true again just to be sure
    foundRestaurant.isActive = true;
    logger.debug('Forced isActive to TRUE again', 'AuthContext');

    // Check isActive in multiple ways
    const isActiveBoolean = foundRestaurant.isActive === true;
    const isActiveString = String(foundRestaurant.isActive).toLowerCase() === 'true';
    const isActiveNotFalse = Boolean(foundRestaurant.isActive);

    logger.debug('isActive checks', 'AuthContext', {
      isActiveBoolean,
      isActiveString,
      isActiveNotFalse,
      rawValue: foundRestaurant.isActive
    });

    // Skip the check entirely - always consider active
    if (false) { // This condition will never be true
      logger.debug('Restaurant account is inactive', 'AuthContext');
      logger.warn('Restaurant account is inactive', 'AuthContext');
      return { success: false, message: 'Restaurant account is inactive' };
    }

    logger.debug('Restaurant account is active', 'AuthContext');
    logger.info('Restaurant account is active', 'AuthContext');

    // Update state
    setCurrentRestaurant(foundRestaurant);
    setIsRestaurantAuthenticated(true);

    logger.authentication('restaurant login', 'success', 'AuthContext', { restaurant: foundRestaurant });

    // Make sure isActive is true before storing
    const restaurantToStore = {
      ...foundRestaurant,
      isActive: true
    };

    logger.debug('Restaurant to store', 'AuthContext', { restaurant: restaurantToStore });
    logger.debug('Restaurant JSON data', 'AuthContext', { json: JSON.stringify(restaurantToStore) });

    // Store in localStorage if remember is checked
    localStorage.setItem('currentRestaurant', JSON.stringify(restaurantToStore));
    localStorage.setItem('isRestaurantLoggedIn', 'true');

    if (!remember) {
      // Use session storage for temporary storage
      sessionStorage.setItem('currentRestaurant', JSON.stringify(restaurantToStore));
      sessionStorage.setItem('isRestaurantLoggedIn', 'true');
    }

    // Load users for this restaurant
    const filteredUsers = allUsers.filter(u =>
      u.restaurant_id === foundRestaurant.id ||
      u.restaurant_id === '0' // System-wide admin users
    );

    logger.debug('Filtered users for restaurant', 'AuthContext', { users: filteredUsers });
    setRestaurantUsers(filteredUsers);

    // STRICT TWO-STEP AUTH: Ensure staff authentication is always required
    logger.info('🔒 STRICT AUTH: Restaurant login complete - staff authentication required next', 'AuthContext', {
      restaurant: foundRestaurant.name,
      availableStaffCount: filteredUsers.length,
      nextStep: 'staff-login'
    });

    return {
      success: true,
      message: 'Restaurant authenticated - staff login required',
      restaurantName: foundRestaurant.name
    };
  };

  // Staff PIN login function
  const loginWithPin = async (pin: string): Promise<{
    success: boolean;
    message: string;
    userName?: string;
    accessLevel?: 'full' | 'limited';
    role?: UserRole;
  }> => {
    logger.authentication('PIN login attempt', 'success', 'AuthContext', { pin: '[REDACTED]' });
    logger.debug('Restaurant Users count: ' + restaurantUsers.length, 'AuthContext');
    logger.debug('Current Restaurant:', currentRestaurant?.name);
    logger.debug('Is Restaurant Authenticated: ' + isRestaurantAuthenticated, 'AuthContext');

    if (isLoading) {
      logger.debug('System is still loading', 'AuthContext');
      return { success: false, message: 'System is loading, please try again' };
    }

    if (!isRestaurantAuthenticated || !currentRestaurant) {
      logger.debug('No restaurant authenticated', 'AuthContext');
      return { success: false, message: 'Please login to a restaurant first' };
    }

    // Find user with matching PIN in the current restaurant
    // Make sure we're comparing strings
    const foundUser = restaurantUsers.find(user => String(user.pin) === String(pin));
    logger.debug('Found User: ' + (foundUser ? foundUser.name + ' (' + foundUser.role + ')' : 'No user found'), 'AuthContext');

    // Check if user exists
    if (!foundUser) {
      logger.debug('No user found with PIN:', pin);
      return { success: false, message: 'Invalid PIN' };
    }

    if (foundUser.status !== 'active') {
      logger.debug('User account is not active', 'AuthContext');
      return { success: false, message: 'Your account is not active' };
    }

    // Ensure the role is a valid UserRole
    const mappedRole = mapRoleToUserRole(foundUser.role);
    logger.debug('Original role: ' + foundUser.role + ' Mapped role: ' + mappedRole, 'AuthContext');

    const userWithValidRole = {
      ...foundUser,
      role: mappedRole
    };

    logger.debug('User with valid role: ' + userWithValidRole.name + ' (' + userWithValidRole.role + ')', 'AuthContext');

    // Try to get JWT token from backend for API authentication
    try {
      const authResult = await createDefaultUserForPin(pin, userWithValidRole);
      if (authResult.success && authResult.user) {
        // Use the user with JWT token
        setUser(authResult.user);
        setIsAuthenticated(true);

        logger.authentication('PIN login with JWT token', 'success', 'AuthContext', {
          userName: authResult.user.name,
          hasToken: !!authResult.user.access_token
        });

        const response = {
          success: true,
          message: 'Login successful',
          userName: authResult.user.name,
          accessLevel: authResult.user.accessLevel,
          role: mappedRole
        };

        logger.debug('Login response with JWT success: ' + response.success, 'AuthContext');
        return response;
      }
    } catch (error) {
      logger.warn('Failed to get JWT token, using local auth only', 'AuthContext', { error: error.message });
    }

    // Fallback to local authentication without JWT token
    setUser(userWithValidRole);
    setIsAuthenticated(true);

    // STRICT TWO-STEP AUTH: Update localStorage for staff authentication
    localStorage.setItem('user', JSON.stringify(userWithValidRole));
    localStorage.setItem('isLoggedIn', 'true');

    const response = {
      success: true,
      message: 'Staff login successful - access granted to dashboard',
      userName: foundUser.name,
      accessLevel: foundUser.accessLevel,
      role: mappedRole
    };

    logger.authentication('staff login', 'success', 'AuthContext', {
      userName: foundUser.name,
      role: mappedRole,
      accessLevel: foundUser.accessLevel,
      strictTwoStepComplete: true
    });

    return response;
  };

  // Logout function (staff only) - enhanced with proper error handling
  const logout = () => {
    logger.authentication('logout', 'success', 'AuthContext');

    // Synchronously clear state and localStorage
    try {
      setUser(null);
      setIsAuthenticated(false);
      localStorage.removeItem('user');
      localStorage.removeItem('isLoggedIn');
      logger.debug('User session cleared successfully', 'AuthContext');
    } catch (error) {
      logger.logError(error, 'clearing user session during logout', 'AuthContext');
    }
  };

  // Logout restaurant function (logs out both restaurant and staff) - enhanced with error handling
  const logoutRestaurant = () => {
    logger.authentication('restaurant logout', 'success', 'AuthContext');

    try {
      // First logout staff
      logout();

      // Then logout restaurant with synchronized state updates
      setCurrentRestaurant(null);
      setIsRestaurantAuthenticated(false);
      setRestaurantUsers([]);

      // Clear all storage synchronously
      localStorage.removeItem('currentRestaurant');
      localStorage.removeItem('isRestaurantLoggedIn');
      sessionStorage.removeItem('currentRestaurant');
      sessionStorage.removeItem('isRestaurantLoggedIn');

      logger.debug('Restaurant session cleared successfully', 'AuthContext');
    } catch (error) {
      logger.logError(error, 'clearing restaurant session during logout', 'AuthContext');
    }
  };

  // Refresh restaurant data (useful after new restaurant registration)
  const refreshRestaurantData = async () => {
    logger.info('Refreshing restaurant data', 'AuthContext');

    try {
      // Re-initialize mock data service to pick up new restaurants
      await initializeMockData(MOCK_RESTAURANTS, MOCK_USERS);

      // Get updated data
      const updatedRestaurants = getAllRestaurants();
      const updatedUsers = getAllUsers();

      logger.dataOperation("refresh", "restaurant data", "AuthContext", {
        restaurants: updatedRestaurants.length,
        users: updatedUsers.length
      });

      // Update state
      setAllRestaurants(updatedRestaurants);
      const completeUsers: User[] = updatedUsers.map(user => ({
        ...user,
        hireDate: user.hireDate || "2020-01-01",
        performance: user.performance || 100,
        accessLevel: user.accessLevel || "limited"
      }));
      setAllUsers(completeUsers);
      setExistingRestaurants(updatedRestaurants);

    } catch (error) {
      logger.logError(error, 'refreshing restaurant data', 'AuthContext');
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isAuthenticated,
        isRestaurantAuthenticated,
        currentRestaurant,
        isLoading,
        isInitialized,
        login,
        loginRestaurant,
        loginWithPin,
        logout,
        logoutRestaurant,
        refreshRestaurantData,
        allUsers,
        restaurantUsers
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook for using auth context
export const useAuth = () => useContext(AuthContext);
