"""
Menu controller implementing business logic for menu operations.
Handles menu item CRUD operations, filtering, and allergen management.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime

from app.controllers.base import BaseController
from app.utils.storage_async import get_all_async, get_by_id_async, create_async, update_async, delete_async, query_async
from app.models.menu import MenuItem, MenuItemCreate, MenuItemUpdate
from app.constants.allergens import VALID_ALLERGENS, ALLERGEN_DESCRIPTIONS
from app.utils.logging_config import logger

class MenuController(BaseController):
    """Controller for menu business logic and operations"""
    
    def __init__(self):
        super().__init__()
        self.cache_prefix = "menu"
        self.default_cache_ttl = 300  # 5 minutes for menu data
    
    async def get_menu_items(
        self,
        restaurant_id: Optional[str] = None,
        allergens: Optional[str] = None,
        category: Optional[str] = None,
        available: Optional[bool] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Get menu items with filtering and caching.
        
        Args:
            restaurant_id: Filter by restaurant ID
            allergens: Comma-separated allergens to filter by
            category: Filter by category
            available: Filter by availability
            skip: Number of items to skip
            limit: Maximum number of items to return
            
        Returns:
            List of menu item dictionaries
        """
        # Validate pagination
        skip, limit = self.validate_pagination(skip, limit)
        
        # Create cache key based on filters
        filter_key = f"{restaurant_id}_{allergens}_{category}_{available}_{skip}_{limit}"
        cache_key = f"{self.cache_prefix}_filtered_{hash(filter_key)}"
        
        # Check cache first
        cached_result = self.get_cached_result(cache_key)
        if cached_result:
            return cached_result
        
        # Fetch all menu items
        items = await self.handle_async_operation(
            get_all_async,
            "menu_items",
            error_message="Failed to fetch menu items"
        )
        
        # Apply filters
        filtered_items = self._apply_filters(
            items, restaurant_id, allergens, category, available
        )
        
        # Apply pagination
        paginated_items = filtered_items[skip:skip + limit]
        
        # Cache the result
        self.cache_result(cache_key, paginated_items, self.default_cache_ttl)
        
        logger.info(
            f"Retrieved {len(paginated_items)} menu items",
            "MenuController",
            {
                "total_filtered": len(filtered_items),
                "restaurant_id": restaurant_id,
                "filters_applied": bool(allergens or category or available is not None)
            }
        )
        
        return paginated_items
    
    def _apply_filters(
        self,
        items: List[Dict[str, Any]],
        restaurant_id: Optional[str] = None,
        allergens: Optional[str] = None,
        category: Optional[str] = None,
        available: Optional[bool] = None
    ) -> List[Dict[str, Any]]:
        """
        Apply filters to menu items.
        
        Args:
            items: List of menu items
            restaurant_id: Filter by restaurant ID
            allergens: Comma-separated allergens to filter by
            category: Filter by category
            available: Filter by availability
            
        Returns:
            Filtered list of menu items
        """
        filtered_items = items.copy()
        
        # Filter by restaurant ID
        if restaurant_id:
            filtered_items = [
                item for item in filtered_items
                if item.get("restaurant_id") == restaurant_id
            ]
        
        # Filter by allergens
        if allergens:
            allergen_list = [a.strip().lower() for a in allergens.split(",")]
            filtered_items = [
                item for item in filtered_items
                if any(
                    allergen in [a.lower() for a in (item.get("allergens") or [])]
                    for allergen in allergen_list
                )
            ]
        
        # Filter by category
        if category:
            filtered_items = [
                item for item in filtered_items
                if item.get("category", "").lower() == category.lower()
            ]
        
        # Filter by availability
        if available is not None:
            filtered_items = [
                item for item in filtered_items
                if item.get("available") == available
            ]
        
        return filtered_items
    
    async def get_menu_item_by_id(self, item_id: str) -> Optional[Dict[str, Any]]:
        """
        Get menu item by ID with caching.
        
        Args:
            item_id: Menu item ID
            
        Returns:
            Menu item dictionary or None if not found
        """
        # Check cache first
        cache_key = f"{self.cache_prefix}_{item_id}"
        cached_result = self.get_cached_result(cache_key)
        
        if cached_result:
            return cached_result
        
        # Fetch from database
        item = await self.handle_async_operation(
            get_by_id_async,
            "menu_items",
            item_id,
            error_message=f"Failed to fetch menu item {item_id}"
        )
        
        if item:
            # Cache the result
            self.cache_result(cache_key, item, self.default_cache_ttl)
            logger.info(f"Retrieved menu item: {item.get('name', 'Unknown')}", "MenuController")
        
        return item
    
    async def create_menu_item(self, item_data: MenuItemCreate) -> Dict[str, Any]:
        """
        Create a new menu item with validation.
        
        Args:
            item_data: Menu item creation data
            
        Returns:
            Created menu item data
            
        Raises:
            HTTPException: If validation fails or creation errors occur
        """
        # Validate allergens
        if item_data.allergens:
            invalid_allergens = [
                allergen for allergen in item_data.allergens
                if allergen.lower() not in [a.lower() for a in VALID_ALLERGENS]
            ]
            if invalid_allergens:
                raise self.handle_validation_error(
                    "Invalid allergens provided",
                    {
                        "invalid_allergens": invalid_allergens,
                        "valid_allergens": VALID_ALLERGENS
                    }
                )
        
        # Prepare item data
        item_dict = item_data.model_dump()
        item_dict["created_at"] = datetime.now().isoformat()
        item_dict["updated_at"] = datetime.now().isoformat()
        
        # Create menu item
        created_item = await self.handle_async_operation(
            create_async,
            "menu_items",
            item_dict,
            error_message="Failed to create menu item"
        )
        
        # Invalidate relevant caches
        self.invalidate_cache_pattern(self.cache_prefix)
        
        logger.info(
            f"Created menu item: {created_item.get('name', 'Unknown')}",
            "MenuController",
            {"item_id": created_item.get("id")}
        )
        
        return created_item
    
    async def update_menu_item(
        self,
        item_id: str,
        item_data: MenuItemUpdate
    ) -> Dict[str, Any]:
        """
        Update an existing menu item.
        
        Args:
            item_id: Menu item ID
            item_data: Menu item update data
            
        Returns:
            Updated menu item data
            
        Raises:
            HTTPException: If item not found or update fails
        """
        # Check if item exists
        existing_item = await self.get_menu_item_by_id(item_id)
        if not existing_item:
            raise self.handle_not_found("Menu item", item_id)
        
        # Validate allergens if provided
        update_dict = item_data.model_dump(exclude_unset=True)
        if "allergens" in update_dict and update_dict["allergens"]:
            invalid_allergens = [
                allergen for allergen in update_dict["allergens"]
                if allergen.lower() not in [a.lower() for a in VALID_ALLERGENS]
            ]
            if invalid_allergens:
                raise self.handle_validation_error(
                    "Invalid allergens provided",
                    {
                        "invalid_allergens": invalid_allergens,
                        "valid_allergens": VALID_ALLERGENS
                    }
                )
        
        # Add update timestamp
        update_dict["updated_at"] = datetime.now().isoformat()
        
        # Update menu item
        updated_item = await self.handle_async_operation(
            update_async,
            "menu_items",
            item_id,
            update_dict,
            error_message=f"Failed to update menu item {item_id}"
        )
        
        # Invalidate relevant caches
        self.invalidate_cache(f"{self.cache_prefix}_{item_id}")
        self.invalidate_cache_pattern(f"{self.cache_prefix}_filtered")
        
        logger.info(
            f"Updated menu item: {updated_item.get('name', 'Unknown')}",
            "MenuController",
            {"item_id": item_id}
        )
        
        return updated_item
    
    async def delete_menu_item(self, item_id: str) -> bool:
        """
        Delete a menu item.
        
        Args:
            item_id: Menu item ID
            
        Returns:
            True if deletion successful
            
        Raises:
            HTTPException: If item not found or deletion fails
        """
        # Check if item exists
        existing_item = await self.get_menu_item_by_id(item_id)
        if not existing_item:
            raise self.handle_not_found("Menu item", item_id)
        
        # Delete menu item
        success = await self.handle_async_operation(
            delete_async,
            "menu_items",
            item_id,
            error_message=f"Failed to delete menu item {item_id}"
        )
        
        if success:
            # Invalidate relevant caches
            self.invalidate_cache(f"{self.cache_prefix}_{item_id}")
            self.invalidate_cache_pattern(f"{self.cache_prefix}_filtered")
            
            logger.info(
                f"Deleted menu item: {existing_item.get('name', 'Unknown')}",
                "MenuController",
                {"item_id": item_id}
            )
        
        return success
    
    async def get_valid_allergens(self) -> Dict[str, Any]:
        """
        Get list of valid allergens with descriptions.
        
        Returns:
            Dictionary containing allergens and descriptions
        """
        return {
            "allergens": VALID_ALLERGENS,
            "descriptions": ALLERGEN_DESCRIPTIONS
        }
    
    async def get_menu_categories(self, restaurant_id: Optional[str] = None) -> List[str]:
        """
        Get unique menu categories.
        
        Args:
            restaurant_id: Filter by restaurant ID
            
        Returns:
            List of unique categories
        """
        cache_key = f"{self.cache_prefix}_categories_{restaurant_id or 'all'}"
        cached_result = self.get_cached_result(cache_key)
        
        if cached_result:
            return cached_result
        
        # Fetch all menu items
        items = await self.handle_async_operation(
            get_all_async,
            "menu_items",
            error_message="Failed to fetch menu items for categories"
        )
        
        # Filter by restaurant if specified
        if restaurant_id:
            items = [item for item in items if item.get("restaurant_id") == restaurant_id]
        
        # Extract unique categories
        categories = list(set(
            item.get("category", "").strip()
            for item in items
            if item.get("category", "").strip()
        ))
        categories.sort()
        
        # Cache the result
        self.cache_result(cache_key, categories, self.default_cache_ttl)
        
        return categories
