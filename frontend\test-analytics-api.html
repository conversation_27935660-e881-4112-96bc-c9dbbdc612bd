<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analytics API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border-left: 4px solid #007bff;
            background-color: #f8f9fa;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .code {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            margin: 10px 0;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Analytics API Test</h1>
        
        <div class="status info">
            <strong>Purpose:</strong> Test the Analytics API endpoints to debug why Sales Performance and Popular Items are not displaying.
        </div>

        <div class="test-section">
            <h3>🔗 API Endpoints Test</h3>
            <button onclick="testHealthCheck()">Test Health Check</button>
            <button onclick="testSalesData()">Test Sales Data</button>
            <button onclick="testPopularItems()">Test Popular Items</button>
            <button onclick="testDashboardData()">Test Dashboard Data</button>
            <button onclick="runAllTests()">Run All Tests</button>
            <div id="api-results"></div>
        </div>

        <div class="test-section">
            <h3>📊 Data Structure Analysis</h3>
            <div id="data-analysis"></div>
        </div>

        <div class="test-section">
            <h3>🐛 Debug Information</h3>
            <div id="debug-info"></div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:5001';
        
        function addResult(message, type = 'info', containerId = 'api-results') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `status ${type}`;
            resultDiv.innerHTML = message;
            container.appendChild(resultDiv);
        }

        function addCode(data, containerId = 'api-results') {
            const container = document.getElementById(containerId);
            const codeDiv = document.createElement('div');
            codeDiv.className = 'code';
            codeDiv.textContent = JSON.stringify(data, null, 2);
            container.appendChild(codeDiv);
        }

        async function testHealthCheck() {
            try {
                addResult('🔄 Testing health check...', 'info');
                const response = await fetch(`${API_BASE_URL}/health`);
                
                if (response.ok) {
                    const data = await response.json();
                    addResult('✅ Health check successful', 'success');
                    addCode(data);
                } else {
                    addResult(`❌ Health check failed: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Health check error: ${error.message}`, 'error');
            }
        }

        async function testSalesData() {
            try {
                addResult('🔄 Testing sales data endpoint...', 'info');
                const response = await fetch(`${API_BASE_URL}/api/analytics/sales`);
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ Sales data retrieved: ${Array.isArray(data) ? data.length : 'Not array'} items`, 'success');
                    addCode(data);
                    
                    // Analyze data structure
                    if (Array.isArray(data) && data.length > 0) {
                        addResult(`📊 Sample sales data structure:`, 'info', 'data-analysis');
                        addCode(data[0], 'data-analysis');
                    }
                } else {
                    addResult(`❌ Sales data failed: ${response.status} ${response.statusText}`, 'error');
                    const errorText = await response.text();
                    addCode({ error: errorText });
                }
            } catch (error) {
                addResult(`❌ Sales data error: ${error.message}`, 'error');
            }
        }

        async function testPopularItems() {
            try {
                addResult('🔄 Testing popular items endpoint...', 'info');
                const response = await fetch(`${API_BASE_URL}/api/analytics/popular-items`);
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ Popular items retrieved: ${Array.isArray(data) ? data.length : 'Not array'} items`, 'success');
                    addCode(data);
                    
                    // Analyze data structure
                    if (Array.isArray(data) && data.length > 0) {
                        addResult(`📊 Sample popular item structure:`, 'info', 'data-analysis');
                        addCode(data[0], 'data-analysis');
                    }
                } else {
                    addResult(`❌ Popular items failed: ${response.status} ${response.statusText}`, 'error');
                    const errorText = await response.text();
                    addCode({ error: errorText });
                }
            } catch (error) {
                addResult(`❌ Popular items error: ${error.message}`, 'error');
            }
        }

        async function testDashboardData() {
            try {
                addResult('🔄 Testing dashboard data endpoint...', 'info');
                const response = await fetch(`${API_BASE_URL}/api/analytics/dashboard`);
                
                if (response.ok) {
                    const data = await response.json();
                    addResult('✅ Dashboard data retrieved successfully', 'success');
                    addCode(data);
                } else {
                    addResult(`❌ Dashboard data failed: ${response.status} ${response.statusText}`, 'error');
                    const errorText = await response.text();
                    addCode({ error: errorText });
                }
            } catch (error) {
                addResult(`❌ Dashboard data error: ${error.message}`, 'error');
            }
        }

        async function runAllTests() {
            document.getElementById('api-results').innerHTML = '';
            document.getElementById('data-analysis').innerHTML = '';
            document.getElementById('debug-info').innerHTML = '';
            
            addResult('🚀 Running comprehensive API tests...', 'info');
            
            await testHealthCheck();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testSalesData();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testPopularItems();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testDashboardData();
            
            // Add debug information
            addResult('🐛 Debug Information:', 'info', 'debug-info');
            addResult(`Current URL: ${window.location.href}`, 'info', 'debug-info');
            addResult(`API Base URL: ${API_BASE_URL}`, 'info', 'debug-info');
            addResult(`User Agent: ${navigator.userAgent}`, 'info', 'debug-info');
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            addResult('🔄 Page loaded, running initial tests...', 'info');
            setTimeout(runAllTests, 1000);
        });
    </script>
</body>
</html>
