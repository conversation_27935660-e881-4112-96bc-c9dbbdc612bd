"""
Test script to exactly mimic what the database service does
"""

import asyncio
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.database import get_db_session_context
from app.repositories.restaurant import RestaurantRepository
from sqlalchemy import text

async def test_service_context():
    """Test using the exact same context as the database service"""
    try:
        print("Testing service context...")
        
        # Use the exact same context manager as the database service
        async with get_db_session_context() as db:
            print(f"Session created via get_db_session_context: {db}")
            print(f"Session bind URL: {db.bind.url}")
            
            # Test the session directly (same as database service)
            test_result = await db.execute(text("SELECT COUNT(*) FROM restaurants"))
            test_count = test_result.scalar()
            print(f"Direct count test: {test_count}")
            
            # Test repository (same as database service)
            repo = RestaurantRepository()
            print(f"Got repository: {repo}")
            
            print(f"Calling repo.get_all for restaurants")
            items = await repo.get_all(db)
            print(f"Retrieved {len(items)} raw items from database")
            
            for item in items[:3]:
                print(f"  - {item.name} (ID: {item.id}, Code: {item.code})")
        
        print("\n✅ Service context test completed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Service context test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_service_context())
    sys.exit(0 if success else 1)
