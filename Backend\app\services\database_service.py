"""
Database service layer that provides a compatibility layer with the existing storage.py interface.
This allows for gradual migration from JSON file storage to SQL database.
"""

from typing import Dict, Any, List, Union, Callable, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from app.database import get_db_session_context
from app.repositories.restaurant import RestaurantRepository, RestaurantUserRepository
from app.models.database_models import Restaurant, RestaurantUser
import logging
import asyncio

logger = logging.getLogger(__name__)

class DatabaseService:
    """Service layer providing storage.py compatible interface"""
    
    def __init__(self):
        self.restaurant_repo = RestaurantRepository()
        self.user_repo = RestaurantUserRepository()
        
        # Collection mapping for backward compatibility
        self.collection_mapping = {
            "restaurants": self.restaurant_repo,
            "restaurant_users": self.user_repo,
            "users": self.user_repo,  # Alias for restaurant_users
        }
    
    def _get_repository(self, collection: str):
        """Get repository for a collection"""
        repo = self.collection_mapping.get(collection)
        if not repo:
            raise ValueError(f"Unsupported collection: {collection}")
        return repo
    
    async def get_all(self, collection: str) -> List[Dict[str, Any]]:
        """Get all items from a collection (compatible with storage.py)"""
        try:
            async with get_db_session_context() as db:
                repo = self._get_repository(collection)

                if collection in ["restaurants"]:
                    items = await repo.get_all(db)
                elif collection in ["restaurant_users", "users"]:
                    items = await self.user_repo.get_all_users(db)
                else:
                    items = await repo.get_all(db)

                # Convert SQLAlchemy models to dictionaries
                result = [self._model_to_dict(item) for item in items]
                logger.info(f"Retrieved {len(result)} items from {collection}")
                return result
        except Exception as e:
            logger.error(f"Error getting all items from {collection}: {e}")
            return []
    
    async def get_by_id(self, collection: str, item_id: str) -> Optional[Dict[str, Any]]:
        """Get an item by ID from a collection (compatible with storage.py)"""
        try:
            async with get_db_session_context() as db:
                repo = self._get_repository(collection)
                item = await repo.get_by_id(db, item_id)
                return self._model_to_dict(item) if item else None
        except Exception as e:
            logger.error(f"Error getting item {item_id} from {collection}: {e}")
            return None
    
    async def create(self, collection: str, item: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new item in a collection (compatible with storage.py)"""
        try:
            async with get_db_session_context() as db:
                repo = self._get_repository(collection)
                
                # Convert dictionary to model-compatible format
                model_data = self._dict_to_model_data(collection, item)
                created_item = await repo.create(db, model_data)
                await db.commit()
                
                return self._model_to_dict(created_item)
        except Exception as e:
            logger.error(f"Error creating item in {collection}: {e}")
            raise
    
    async def update(self, collection: str, item_id: str, updates: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Update an item in a collection (compatible with storage.py)"""
        try:
            async with get_db_session_context() as db:
                repo = self._get_repository(collection)
                
                # Convert dictionary to model-compatible format
                model_updates = self._dict_to_model_data(collection, updates)
                updated_item = await repo.update(db, item_id, model_updates)
                await db.commit()
                
                return self._model_to_dict(updated_item) if updated_item else None
        except Exception as e:
            logger.error(f"Error updating item {item_id} in {collection}: {e}")
            raise
    
    async def delete(self, collection: str, item_id: str) -> bool:
        """Delete an item from a collection (compatible with storage.py)"""
        try:
            async with get_db_session_context() as db:
                repo = self._get_repository(collection)
                success = await repo.delete(db, item_id)
                await db.commit()
                return success
        except Exception as e:
            logger.error(f"Error deleting item {item_id} from {collection}: {e}")
            return False
    
    async def query(self, collection: str, filters: Union[Dict[str, Any], Callable]) -> List[Dict[str, Any]]:
        """Query items from a collection with filters (compatible with storage.py)"""
        try:
            async with get_db_session_context() as db:
                repo = self._get_repository(collection)
                
                if callable(filters):
                    # For callable filters, we need to get all items and filter in Python
                    # This is less efficient but maintains compatibility
                    all_items = await repo.get_all(db)
                    filtered_items = [item for item in all_items if filters(self._model_to_dict(item))]
                    return [self._model_to_dict(item) for item in filtered_items]
                else:
                    # For dictionary filters, use database-level filtering
                    items = await repo.query(db, filters)
                    return [self._model_to_dict(item) for item in items]
        except Exception as e:
            logger.error(f"Error querying {collection} with filters: {e}")
            return []
    
    async def get_restaurant_users(self, restaurant_id: str) -> List[Dict[str, Any]]:
        """Get users for a specific restaurant"""
        try:
            async with get_db_session_context() as db:
                users = await self.user_repo.get_by_restaurant(db, restaurant_id)
                return [self._model_to_dict(user) for user in users]
        except Exception as e:
            logger.error(f"Error getting users for restaurant {restaurant_id}: {e}")
            return []
    
    async def verify_restaurant_credentials(self, code: str, password: str) -> Optional[Dict[str, Any]]:
        """Verify restaurant login credentials"""
        try:
            async with get_db_session_context() as db:
                restaurant = await self.restaurant_repo.verify_credentials(db, code, password)
                return self._model_to_dict(restaurant) if restaurant else None
        except Exception as e:
            logger.error(f"Error verifying credentials for restaurant {code}: {e}")
            return None
    
    async def verify_staff_pin(self, restaurant_id: str, pin: str) -> Optional[Dict[str, Any]]:
        """Verify staff PIN for a restaurant"""
        try:
            async with get_db_session_context() as db:
                user = await self.user_repo.verify_pin(db, restaurant_id, pin)
                return self._model_to_dict(user) if user else None
        except Exception as e:
            logger.error(f"Error verifying PIN for restaurant {restaurant_id}: {e}")
            return None
    
    def _model_to_dict(self, model) -> Dict[str, Any]:
        """Convert SQLAlchemy model to dictionary"""
        if model is None:
            return None
        
        result = {}
        for column in model.__table__.columns:
            value = getattr(model, column.name)
            
            # Convert datetime to ISO string for JSON compatibility
            if hasattr(value, 'isoformat'):
                value = value.isoformat()
            
            # Handle column name mapping for backward compatibility
            if isinstance(model, Restaurant):
                if column.name == 'vat_rate':
                    result['vatRate'] = value
                elif column.name == 'is_active':
                    result['isActive'] = value
                elif column.name == 'owner_name':
                    result['ownerName'] = value
                elif column.name == 'business_license_number':
                    result['businessLicenseNumber'] = value
                elif column.name == 'restaurant_type':
                    result['restaurantType'] = value
                elif column.name == 'setup_data':
                    result['setupData'] = value
                elif column.name == 'created_at':
                    result['createdAt'] = value
                elif column.name == 'updated_at':
                    result['updatedAt'] = value
                else:
                    result[column.name] = value
            elif isinstance(model, RestaurantUser):
                if column.name == 'hire_date':
                    result['hireDate'] = value
                elif column.name == 'access_level':
                    result['accessLevel'] = value
                else:
                    result[column.name] = value
            else:
                result[column.name] = value
        
        return result
    
    def _dict_to_model_data(self, collection: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Convert dictionary to model-compatible data"""
        result = data.copy()
        
        if collection == "restaurants":
            # Handle field name mapping for restaurants
            if 'vatRate' in result:
                result['vat_rate'] = result.pop('vatRate')
            if 'isActive' in result:
                result['is_active'] = result.pop('isActive')
            if 'ownerName' in result:
                result['owner_name'] = result.pop('ownerName')
            if 'businessLicenseNumber' in result:
                result['business_license_number'] = result.pop('businessLicenseNumber')
            if 'restaurantType' in result:
                result['restaurant_type'] = result.pop('restaurantType')
            if 'setupData' in result:
                result['setup_data'] = result.pop('setupData')
            if 'createdAt' in result:
                result['created_at'] = result.pop('createdAt')
            if 'updatedAt' in result:
                result['updated_at'] = result.pop('updatedAt')
        
        elif collection in ["restaurant_users", "users"]:
            # Handle field name mapping for users
            if 'hireDate' in result:
                result['hire_date'] = result.pop('hireDate')
            if 'accessLevel' in result:
                result['access_level'] = result.pop('accessLevel')
        
        return result

# Global database service instance
db_service = DatabaseService()

# Compatibility functions that match storage.py interface
async def get_all(collection: str) -> List[Dict[str, Any]]:
    """Get all items from a collection"""
    return await db_service.get_all(collection)

async def get_by_id(collection: str, item_id: str) -> Dict[str, Any]:
    """Get an item by ID from a collection"""
    return await db_service.get_by_id(collection, item_id)

async def create(collection: str, item: Dict[str, Any]) -> Dict[str, Any]:
    """Create a new item in a collection"""
    return await db_service.create(collection, item)

async def update(collection: str, item_id: str, updates: Dict[str, Any]) -> Dict[str, Any]:
    """Update an item in a collection"""
    return await db_service.update(collection, item_id, updates)

async def delete(collection: str, item_id: str) -> bool:
    """Delete an item from a collection"""
    return await db_service.delete(collection, item_id)

async def query(collection: str, filters: Union[Dict[str, Any], Callable]) -> List[Dict[str, Any]]:
    """Query items from a collection with filters"""
    return await db_service.query(collection, filters)
