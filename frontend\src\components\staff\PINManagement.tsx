import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { toast } from "@/components/ui/sonner";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Search, Pencil, Eye, EyeOff } from "lucide-react";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";

// Staff member interface
interface StaffMember {
  id: string;
  name: string;
  role: string;
  position: string;
  email: string;
  phone: string;
  pin?: string;
  status: "active" | "inactive" | "on-leave";
}

const PINManagement = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [isPinDialogOpen, setIsPinDialogOpen] = useState(false);
  const [currentStaff, setCurrentStaff] = useState<StaffMember | null>(null);
  const [newPin, setNewPin] = useState("");
  const [showPin, setShowPin] = useState<Record<string, boolean>>({});

  // Mock staff data
  const [staffData, setStaffData] = useState<StaffMember[]>([
    {
      id: "1",
      name: "Michael Rodriguez",
      role: "waiter",
      position: "Head Waiter",
      email: "<EMAIL>",
      phone: "(*************",
      status: "active",
      pin: "1234"
    },
    {
      id: "2",
      name: "Jennifer Smith",
      role: "waiter",
      position: "Waiter",
      email: "<EMAIL>",
      phone: "(*************",
      status: "active",
      pin: "2345"
    },
    {
      id: "3",
      name: "David Chen",
      role: "waiter",
      position: "Waiter",
      email: "<EMAIL>",
      phone: "(*************",
      status: "on-leave",
      pin: "3456"
    },
    {
      id: "4",
      name: "Maria Lopez",
      role: "chef",
      position: "Head Chef",
      email: "<EMAIL>",
      phone: "(*************",
      status: "active",
      pin: "4567"
    },
    {
      id: "5",
      name: "Robert Johnson",
      role: "manager",
      position: "Floor Manager",
      email: "<EMAIL>",
      phone: "(*************",
      status: "active",
      pin: "5678"
    }
  ]);

  // Filter staff based on search query
  const filteredStaff = staffData.filter(staff =>
    staff.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    staff.position.toLowerCase().includes(searchQuery.toLowerCase()) ||
    staff.role.toLowerCase().includes(searchQuery.toLowerCase()) ||
    staff.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Get initials for avatar
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  // Toggle PIN visibility
  const togglePinVisibility = (staffId: string) => {
    setShowPin(prev => ({
      ...prev,
      [staffId]: !prev[staffId]
    }));
  };

  // Handle changing PIN
  const handleChangePin = () => {
    if (!currentStaff) return;

    // Validate PIN (must be 4 digits)
    if (!/^\d{4}$/.test(newPin)) {
      toast.error("PIN must be exactly 4 digits");
      return;
    }

    // Update staff PIN
    setStaffData(staffData.map(staff =>
      staff.id === currentStaff.id ? { ...staff, pin: newPin } : staff
    ));

    // Reset and close dialog
    setNewPin("");
    setCurrentStaff(null);
    setIsPinDialogOpen(false);

    // Show success message
    toast.success("PIN updated successfully");
  };

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>PIN Management</CardTitle>
          <CardDescription>
            Manage staff PIN codes for login
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <div className="relative w-full sm:w-72">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search staff..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>

            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Position</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>PIN Code</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredStaff.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                      No staff members found
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredStaff.map((staff) => (
                    <TableRow key={staff.id}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <Avatar className="h-8 w-8">
                            <AvatarFallback>{getInitials(staff.name)}</AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">{staff.name}</div>
                            <div className="text-xs text-muted-foreground capitalize">{staff.role}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{staff.position}</TableCell>
                      <TableCell>{staff.email}</TableCell>
                      <TableCell>
                        {staff.pin ? (
                          <div className="flex items-center">
                            <span className="font-mono">
                              {showPin[staff.id] ? staff.pin : '••••'}
                            </span>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="ml-2 h-6 w-6 p-0"
                              onClick={() => togglePinVisibility(staff.id)}
                            >
                              {showPin[staff.id] ? (
                                <EyeOff className="h-3 w-3" />
                              ) : (
                                <Eye className="h-3 w-3" />
                              )}
                            </Button>
                          </div>
                        ) : (
                          <span className="text-muted-foreground">Not set</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge variant={staff.status === "active" ? "default" :
                                      staff.status === "inactive" ? "secondary" : "outline"}>
                          {staff.status.replace('-', ' ')}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setCurrentStaff(staff);
                            setNewPin(staff.pin || "");
                            setIsPinDialogOpen(true);
                          }}
                        >
                          <Pencil className="h-4 w-4 mr-2" /> Change PIN
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Change PIN Dialog */}
      <Dialog open={isPinDialogOpen} onOpenChange={setIsPinDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Change PIN Code</DialogTitle>
            <p className="text-sm text-muted-foreground">
              {currentStaff?.name} - {currentStaff?.position}
            </p>
          </DialogHeader>
          <div className="py-4">
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="new-pin">New PIN Code (4 digits)</Label>
                <Input
                  id="new-pin"
                  type="text"
                  maxLength={4}
                  placeholder="e.g. 1234"
                  value={newPin}
                  onChange={(e) => {
                    // Only allow numbers
                    const value = e.target.value.replace(/[^0-9]/g, '');
                    if (value.length <= 4) {
                      setNewPin(value);
                    }
                  }}
                />
                <p className="text-xs text-muted-foreground">
                  PIN must be exactly 4 digits and will be used for staff login.
                </p>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsPinDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleChangePin}>Save PIN</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default PINManagement;
