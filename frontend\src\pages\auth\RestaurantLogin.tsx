
import logger from "@/utils/logger";
import apiService from "@/services/apiService";
import { useState, useEffect } from "react";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "@/components/ui/sonner";
import { ChefHat, Mail, Lock, AlertCircle, Building2, Copy, Eye, EyeOff, RefreshCw } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useAuth } from "@/contexts/AuthContext";

const RestaurantLogin = () => {
  // Initialize component logging
  logger.setComponent('RestaurantLogin');
  logger.info('Component initialized', 'RestaurantLogin');
  const navigate = useNavigate();
  const location = useLocation();
  const { isRestaurantAuthenticated, loginRestaurant } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [backendAvailable, setBackendAvailable] = useState<boolean | null>(null);
  const [allRestaurants, setAllRestaurants] = useState<any[]>([]);
  const [loadingRestaurants, setLoadingRestaurants] = useState(false);
  const [showPasswords, setShowPasswords] = useState<{[key: string]: boolean}>({});
  const [formData, setFormData] = useState({
    restaurantCode: "",
    password: "",
    rememberRestaurant: false,
  });

  // Check backend connectivity and redirect if already authenticated
  useEffect(() => {
    logger.debug('Checking restaurant authentication', 'RestaurantLogin');
    logger.debug('Restaurant authentication status', 'RestaurantLogin', { isAuthenticated: isRestaurantAuthenticated });

    if (isRestaurantAuthenticated) {
      logger.userAction('redirect to staff login - already authenticated', 'RestaurantLogin');
      // Add small delay to prevent race conditions
      setTimeout(() => {
        navigate('/staff-login');
      }, 100);
    } else {
      logger.debug('No restaurant authenticated, showing login form', 'RestaurantLogin');
    }

    // Check for pre-filled restaurant code from registration success
    const preFilledCode = location.state?.restaurantCode;
    if (preFilledCode) {
      logger.info('Pre-filling restaurant code from registration', 'RestaurantLogin', { code: preFilledCode });
      setFormData(prev => ({ ...prev, restaurantCode: preFilledCode }));
    }

    // Check backend connectivity
    const checkBackend = async () => {
      const isAvailable = await apiService.checkBackendConnectivity();
      setBackendAvailable(isAvailable);

      if (!isAvailable) {
        logger.warn('Backend unavailable during restaurant login', 'RestaurantLogin');
      }
    };

    checkBackend();

    // Fetch all restaurants for credentials display
    fetchAllRestaurants();
  }, [isRestaurantAuthenticated, navigate, location.state]);

  // Function to fetch all restaurants from backend
  const fetchAllRestaurants = async () => {
    setLoadingRestaurants(true);
    try {
      logger.debug('Fetching all restaurants for credentials display', 'RestaurantLogin');
      const restaurants = await apiService.restaurant.getRestaurants();
      const restaurantArray = Array.isArray(restaurants) ? restaurants : [];
      setAllRestaurants(restaurantArray);
      logger.debug('Fetched restaurants', 'RestaurantLogin', { count: restaurantArray.length });
    } catch (error) {
      logger.warn('Failed to fetch restaurants', 'RestaurantLogin', { error: error.message });
      // Fallback to empty array if backend is unavailable
      setAllRestaurants([]);
    } finally {
      setLoadingRestaurants(false);
    }
  };

  // Utility functions for credentials display
  const copyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success(`${type} copied to clipboard!`);
      logger.userAction('credential copied', 'RestaurantLogin', { type });
    } catch (error) {
      toast.error('Failed to copy to clipboard');
      logger.warn('Failed to copy to clipboard', 'RestaurantLogin', { error: error.message });
    }
  };

  const togglePasswordVisibility = (restaurantId: string) => {
    setShowPasswords(prev => ({
      ...prev,
      [restaurantId]: !prev[restaurantId]
    }));
  };

  // Function to determine likely password for a restaurant
  const getRestaurantPassword = (restaurant: any): string => {
    // Common password patterns for testing
    const passwordMap: {[key: string]: string} = {
      'GK001': 'gourmet123',
      'PP002': 'pasta123',
      'SS003': 'sushi123',
      'TR511': 'testpass123',
      'TEST123': 'testpass123',
      'DTR735': 'testpass123',
      'ITR372': 'testpass123',
      'PU837': 'Pratham@2410'  // Specific password for Pratham-Test restaurant
    };

    // Check if we have a specific password for this restaurant code
    if (passwordMap[restaurant.code]) {
      return passwordMap[restaurant.code];
    }

    // Special case for Pratham-Test restaurant
    if (restaurant.code === 'PU837' ||
        restaurant.name?.includes('Pratham') ||
        restaurant.email === '<EMAIL>') {
      return 'Pratham@2410';
    }

    // For newer restaurants (created after 2025-05-26), use testpass123
    const createdDate = new Date(restaurant.createdAt);
    const cutoffDate = new Date('2025-05-26');
    if (createdDate > cutoffDate) {
      return 'testpass123';
    }

    // For restaurants with test/debug indicators
    if (restaurant.name?.toLowerCase().includes('test') ||
        restaurant.name?.toLowerCase().includes('debug') ||
        restaurant.email?.includes('test') ||
        restaurant.email?.includes('debug')) {
      return 'testpass123';
    }

    // For restaurants with setupData (indicating they went through registration)
    if (restaurant.setupData && Object.keys(restaurant.setupData).length > 0) {
      return 'testpass123';
    }

    // Default password for legacy restaurants
    return 'testpass123';
  };

  const fillCredentials = (code: string, password: string) => {
    setFormData(prev => ({
      ...prev,
      restaurantCode: code,
      password: password
    }));
    toast.success('Credentials filled in form!');
    logger.userAction('credentials auto-filled', 'RestaurantLogin', { code });
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    logger.userAction(`form field update: ${name}`, "RestaurantLogin", { field: name, hasValue: !!value });
    setFormData((prev) => ({ ...prev, [name]: value }));
    // Clear error when user types
    if (error) setError(null);
  };

  const handleCheckboxChange = (checked: boolean) => {
    logger.userAction("remember restaurant toggle", "RestaurantLogin", { checked });
    setFormData((prev) => ({ ...prev, rememberRestaurant: checked }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    logger.formSubmit('restaurant login form', 'RestaurantLogin');
    logger.debug('Form data', 'RestaurantLogin', {
      restaurantCode: formData.restaurantCode,
      rememberRestaurant: formData.rememberRestaurant
    });

    setIsLoading(true);
    setError(null);

    try {
      logger.debug('Attempting restaurant authentication via AuthContext', 'RestaurantLogin', { code: formData.restaurantCode });

      // Use AuthContext loginRestaurant method for proper state management
      const result = await loginRestaurant(
        formData.restaurantCode,
        formData.password,
        formData.rememberRestaurant
      );

      logger.debug('AuthContext login result', 'RestaurantLogin', { success: result.success });

      if (result.success) {
        logger.authentication('restaurant login', 'success', 'RestaurantLogin', {
          restaurantCode: formData.restaurantCode,
          restaurantName: result.restaurantName
        });

        // Wait a moment for the AuthContext to update with the restaurant data
        setTimeout(async () => {
          try {
            // Get the updated restaurant data from localStorage (set by AuthContext)
            const storedRestaurant = localStorage.getItem('currentRestaurant');
            let restaurantData = null;

            if (storedRestaurant) {
              restaurantData = JSON.parse(storedRestaurant);
            } else {
              // Fallback: fetch from API
              const restaurants = await apiService.restaurant.getRestaurants();
              const restaurantArray = Array.isArray(restaurants) ? restaurants : [];
              restaurantData = restaurantArray.find(r => r.code === formData.restaurantCode);
            }

            logger.debug('Checking restaurant setup status', 'RestaurantLogin', {
              restaurantCode: formData.restaurantCode,
              hasData: restaurantData?.hasData,
              restaurantData: restaurantData
            });

            if (restaurantData && restaurantData.hasData === false) {
              // New restaurant without setup - redirect to onboarding
              logger.info('Redirecting to onboarding for new restaurant', 'RestaurantLogin', {
                restaurantCode: formData.restaurantCode,
                hasData: restaurantData.hasData
              });

              toast.success(`Welcome to ${result.restaurantName}! Let's complete your setup.`);
              navigate(`/onboarding/new-restaurant?code=${formData.restaurantCode}`);
            } else {
              // Established restaurant - go to staff login
              logger.info('Redirecting to staff login for established restaurant', 'RestaurantLogin', {
                restaurantCode: formData.restaurantCode,
                hasData: restaurantData?.hasData
              });

              toast.success(`Welcome back to ${result.restaurantName}!`);
              navigate('/staff-login');
            }
          } catch (error) {
            logger.error('Failed to check restaurant setup status', 'RestaurantLogin', {
              error: error.message
            });

            // Fallback to staff login if we can't check setup status
            toast.success(`Welcome back to ${result.restaurantName}!`);
            navigate('/staff-login');
          }
        }, 300);
      } else {
        const errorMessage = result.message || 'Invalid restaurant code or password';
        logger.authentication('restaurant login', 'failure', 'RestaurantLogin', {
          restaurantCode: formData.restaurantCode,
          reason: errorMessage
        });
        setError(errorMessage);
        toast.error(errorMessage);
      }
    } catch (err) {
      logger.logError(err, 'restaurant login', 'RestaurantLogin');
      setError("An unexpected error occurred. Please try again.");
      toast.error("Login failed. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <div className="flex-1 flex flex-col lg:flex-row">
        {/* Left Side - Form */}
        <div className="w-full lg:w-1/2 flex flex-col justify-center items-center p-5 md:p-8 lg:p-16">
          <div className="w-full max-w-md">
            <div className="text-center mb-6 md:mb-8">
              <Link to="/" className="inline-flex items-center gap-2">
                <ChefHat size={32} className="text-blue-600" />
                <span className="text-xl md:text-2xl font-bold">Promith</span>
              </Link>
              <h1 className="text-2xl md:text-3xl font-bold mt-5 md:mt-6 mb-2">Restaurant Login</h1>
              <p className="text-sm md:text-base text-gray-600">Enter your restaurant credentials to continue</p>

              {/* Backend Status Indicator */}
              {backendAvailable !== null && (
                <div className={`mt-4 inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                  backendAvailable
                    ? 'bg-green-100 text-green-800'
                    : 'bg-yellow-100 text-yellow-800'
                }`}>
                  <div className={`w-2 h-2 rounded-full mr-2 ${
                    backendAvailable ? 'bg-green-400' : 'bg-yellow-400'
                  }`} />
                  {backendAvailable ? 'Backend Connected' : 'Offline Mode'}
                </div>
              )}
            </div>

            <form onSubmit={handleSubmit} className="space-y-4 md:space-y-6">
              {error && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-3 md:px-4 py-2 md:py-3 rounded-md flex items-start text-sm">
                  <AlertCircle className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                  <span>{error}</span>
                </div>
              )}

              <div className="space-y-1.5 md:space-y-2">
                <Label htmlFor="restaurantCode" className="text-sm md:text-base">Restaurant Code</Label>
                <div className="relative">
                  <Building2 className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="restaurantCode"
                    name="restaurantCode"
                    type="text"
                    placeholder="Enter restaurant code"
                    className="pl-10"
                    value={formData.restaurantCode}
                    onChange={handleChange}
                    required
                  />
                </div>
              </div>

              <div className="space-y-1.5 md:space-y-2">
                <div className="flex justify-between items-center">
                  <Label htmlFor="password" className="text-sm md:text-base">Password</Label>
                  <Link to="/forgot-password" className="text-xs md:text-sm text-blue-600 hover:text-blue-800">
                    Forgot password?
                  </Link>
                </div>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="password"
                    name="password"
                    type="password"
                    placeholder="••••••••"
                    className="pl-10"
                    value={formData.password}
                    onChange={handleChange}
                    required
                  />
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="rememberRestaurant"
                  checked={formData.rememberRestaurant}
                  onCheckedChange={handleCheckboxChange}
                />
                <Label htmlFor="rememberRestaurant" className="text-xs md:text-sm font-normal">
                  Remember restaurant on this device
                </Label>
              </div>

              <Button type="submit" className="w-full h-10 md:h-11" disabled={isLoading}>
                {isLoading ? "Signing in..." : "Sign in"}
              </Button>

              {/* All Restaurant Accounts Section */}
              <Card className="mt-4">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium flex items-center justify-between">
                    All Restaurant Accounts
                    <div className="flex items-center gap-2">
                      {loadingRestaurants && <RefreshCw className="h-4 w-4 animate-spin" />}
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={fetchAllRestaurants}
                        disabled={loadingRestaurants}
                        className="h-6 px-2 text-xs"
                      >
                        Refresh
                      </Button>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  {loadingRestaurants ? (
                    <div className="text-center py-4 text-sm text-gray-500">
                      Loading restaurant accounts...
                    </div>
                  ) : allRestaurants.length > 0 ? (
                    <div className="space-y-3 max-h-64 overflow-y-auto">
                      {allRestaurants.map((restaurant) => (
                        <div key={restaurant.id} className="border rounded-lg p-3 bg-gray-50">
                          <div className="flex items-start justify-between mb-2">
                            <div>
                              <h4 className="font-medium text-sm text-gray-900">{restaurant.name}</h4>
                              <p className="text-xs text-gray-500">{restaurant.email}</p>
                            </div>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => fillCredentials(restaurant.code, getRestaurantPassword(restaurant))}
                              className="h-6 px-2 text-xs"
                            >
                              Use
                            </Button>
                          </div>

                          <div className="space-y-2">
                            {/* Restaurant Code */}
                            <div className="flex items-center justify-between">
                              <span className="text-xs text-gray-600">Code:</span>
                              <div className="flex items-center gap-1">
                                <code className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-mono">
                                  {restaurant.code}
                                </code>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => copyToClipboard(restaurant.code, 'Restaurant Code')}
                                  className="h-6 w-6 p-0"
                                >
                                  <Copy className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>

                            {/* Password */}
                            <div className="flex items-center justify-between">
                              <span className="text-xs text-gray-600">Password:</span>
                              <div className="flex items-center gap-1">
                                <code className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-mono">
                                  {showPasswords[restaurant.id] ? getRestaurantPassword(restaurant) : '••••••••'}
                                </code>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => togglePasswordVisibility(restaurant.id)}
                                  className="h-6 w-6 p-0"
                                >
                                  {showPasswords[restaurant.id] ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
                                </Button>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => copyToClipboard(getRestaurantPassword(restaurant), 'Password')}
                                  className="h-6 w-6 p-0"
                                >
                                  <Copy className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>

                            {/* Owner PIN (if available) */}
                            <div className="flex items-center justify-between">
                              <span className="text-xs text-gray-600">Owner PIN:</span>
                              <div className="flex items-center gap-1">
                                <code className="bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs font-mono">
                                  {restaurant.setupData?.ownerPin || '1234'}
                                </code>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => copyToClipboard(restaurant.setupData?.ownerPin || '1234', 'Owner PIN')}
                                  className="h-6 w-6 p-0"
                                >
                                  <Copy className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>

                            {/* Restaurant Type & Status */}
                            <div className="flex items-center justify-between text-xs text-gray-500 mt-2 pt-2 border-t">
                              <span>Type: {restaurant.restaurantType || 'restaurant'}</span>
                              <span className={`px-2 py-1 rounded ${restaurant.isActive ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`}>
                                {restaurant.isActive ? 'Active' : 'Inactive'}
                              </span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-4">
                      <p className="text-sm text-gray-500 mb-2">No restaurant accounts found</p>
                      <p className="text-xs text-gray-400">
                        {backendAvailable === false
                          ? 'Backend is offline - showing fallback accounts'
                          : 'Try refreshing or check backend connection'
                        }
                      </p>
                    </div>
                  )}

                  {/* Fallback Demo Accounts when backend is unavailable */}
                  {!backendAvailable && allRestaurants.length === 0 && (
                    <div className="mt-4 pt-4 border-t">
                      <p className="text-xs font-medium text-gray-600 mb-2">Fallback Demo Accounts:</p>
                      <div className="space-y-2">
                        {[
                          { name: 'Gourmet Kitchen', code: 'GK001', password: 'gourmet123' },
                          { name: 'Pasta Paradise', code: 'PP002', password: 'pasta123' },
                          { name: 'Sushi Sensation', code: 'SS003', password: 'sushi123' },
                          { name: 'Test Cafe', code: 'TR511', password: 'testpass123' }
                        ].map((demo) => (
                          <div key={demo.code} className="flex items-center justify-between text-xs">
                            <span>{demo.name}</span>
                            <div className="flex items-center gap-1">
                              <code className="bg-gray-100 px-1 rounded">{demo.code}</code>
                              <span>/</span>
                              <code className="bg-gray-100 px-1 rounded">{demo.password}</code>
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => fillCredentials(demo.code, demo.password)}
                                className="h-5 px-1 text-xs"
                              >
                                Use
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              <div className="text-center">
                <p className="text-xs md:text-sm text-gray-600">
                  Need a restaurant account?{" "}
                  <Link to="/register" className="text-blue-600 hover:text-blue-800 font-medium">
                    Sign up for Promith
                  </Link>
                </p>
              </div>
            </form>
          </div>
        </div>

        {/* Right Side - Image (hidden on mobile) */}
        <div className="hidden lg:block lg:w-1/2 bg-blue-600">
          <div className="h-full flex items-center justify-center p-8 lg:p-12">
            <div className="max-w-lg text-white">
              <h2 className="text-2xl lg:text-3xl font-bold mb-4 lg:mb-6">Restaurant Management System</h2>
              <p className="text-lg lg:text-xl mb-6 lg:mb-8 text-blue-100">
                Access your restaurant dashboard to manage staff, inventory, analytics, and more in one place.
              </p>
              <div className="bg-white/10 rounded-lg p-4 lg:p-6 backdrop-blur-sm">
                <div className="flex items-start space-x-4">
                  <div className="bg-white rounded-full p-2">
                    <img
                      src="https://randomuser.me/api/portraits/men/32.jpg"
                      alt="Testimonial"
                      className="h-10 w-10 rounded-full"
                      onError={(e) => {
                        e.currentTarget.src = "https://placehold.co/100x100/3b82f6/ffffff?text=JD";
                      }}
                    />
                  </div>
                  <div>
                    <p className="text-white/90 italic mb-3 lg:mb-4 text-sm lg:text-base">
                      "Promith has transformed how we run our restaurant chain. Managing multiple locations has never been easier."
                    </p>
                    <p className="font-semibold text-sm lg:text-base">Michael Johnson</p>
                    <p className="text-xs lg:text-sm text-blue-200">CEO, Restaurant Group International</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RestaurantLogin;
