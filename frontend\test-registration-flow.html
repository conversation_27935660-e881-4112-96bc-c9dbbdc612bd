<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registration Flow Test - RestroManage</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border-color: #ffeaa7;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border-color: #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .code {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            border-left: 4px solid #007bff;
            background: #f8f9fa;
        }
        .step.completed {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .step.failed {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Registration Flow Test - RestroManage</h1>
        
        <div class="test-section info">
            <h3>Purpose</h3>
            <p>This test will create a new restaurant registration with known credentials and immediately test the login flow to identify authentication issues.</p>
        </div>

        <div class="test-section">
            <h3>Test Configuration</h3>
            <div>
                <label>Restaurant Name: <input type="text" id="restaurantName" value="Test Restaurant Flow" style="margin-left: 10px; padding: 5px;"></label><br><br>
                <label>Owner Name: <input type="text" id="ownerName" value="Test Owner" style="margin-left: 10px; padding: 5px;"></label><br><br>
                <label>Email: <input type="email" id="email" value="<EMAIL>" style="margin-left: 10px; padding: 5px;"></label><br><br>
                <label>Password: <input type="text" id="password" value="testpass123" style="margin-left: 10px; padding: 5px;"></label><br><br>
                <button onclick="runCompleteTest()">Run Complete Registration & Login Test</button>
                <button onclick="clearResults()">Clear Results</button>
            </div>
        </div>

        <div id="testResults"></div>
    </div>

    <script>
        let testResults = [];
        let testCredentials = null;

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = { timestamp, message, type };
            testResults.push(logEntry);
            updateDisplay();
            console.log(`[${timestamp}] ${message}`);
        }

        function updateDisplay() {
            const resultsDiv = document.getElementById('testResults');
            let html = '<div class="test-section"><h3>Test Results</h3>';
            
            testResults.forEach(result => {
                const stepClass = result.type === 'success' ? 'completed' : 
                                 result.type === 'error' ? 'failed' : '';
                html += `<div class="step ${stepClass}">[${result.timestamp}] ${result.message}</div>`;
            });
            
            html += '</div>';
            resultsDiv.innerHTML = html;
        }

        async function runCompleteTest() {
            testResults = [];
            testCredentials = null;
            
            const restaurantName = document.getElementById('restaurantName').value;
            const ownerName = document.getElementById('ownerName').value;
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;

            log('🚀 Starting complete registration and login flow test', 'info');
            log(`Testing with: ${restaurantName}, ${email}, password: ${password}`, 'info');

            try {
                // Step 1: Generate restaurant code
                await generateRestaurantCode(restaurantName);
                
                // Step 2: Create registration data
                await createRegistrationData(restaurantName, ownerName, email, password);
                
                // Step 3: Complete registration
                await completeRegistration();
                
                // Step 4: Test login immediately
                await testLogin();
                
                log('✅ Complete test finished successfully!', 'success');
                
            } catch (error) {
                log(`❌ Test failed: ${error.message}`, 'error');
            }
        }

        async function generateRestaurantCode(restaurantName) {
            log('Step 1: Generating restaurant code...', 'info');
            
            try {
                const response = await fetch('/api/restaurants/auth/generate-code', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ name: restaurantName })
                });

                if (!response.ok) {
                    throw new Error(`Failed to generate code: ${response.status}`);
                }

                const data = await response.json();
                testCredentials = { restaurantCode: data.code };
                log(`✅ Generated restaurant code: ${data.code}`, 'success');
                
            } catch (error) {
                log(`❌ Failed to generate restaurant code: ${error.message}`, 'error');
                throw error;
            }
        }

        async function createRegistrationData(restaurantName, ownerName, email, password) {
            log('Step 2: Creating registration data structure...', 'info');
            
            // Generate owner PIN
            const ownerPin = Math.floor(1000 + Math.random() * 9000).toString();
            testCredentials.ownerPin = ownerPin;
            
            // Create registration data matching the expected structure
            const registrationData = {
                restaurantName: restaurantName,
                ownerName: ownerName,
                email: email,
                phone: "+44 ************",
                password: password,
                confirmPassword: password,
                address: {
                    street: "123 Test Street",
                    city: "Test City",
                    state: "Test State",
                    zipCode: "TE5T 1NG",
                    country: "United Kingdom"
                },
                businessLicenseNumber: "TEST-" + Date.now(),
                restaurantType: "restaurant",
                acceptTerms: true,
                acceptPrivacy: true
            };

            const setupData = {
                useDefaultLogo: true,
                logoPosition: "center",
                logoUrl: null,
                totalTables: 5,
                tables: [
                    { id: 1, number: 1, capacity: 4, location: "Main Area" },
                    { id: 2, number: 2, capacity: 4, location: "Main Area" },
                    { id: 3, number: 3, capacity: 4, location: "Main Area" },
                    { id: 4, number: 4, capacity: 4, location: "Main Area" },
                    { id: 5, number: 5, capacity: 4, location: "Main Area" }
                ],
                defaultTableCapacity: 4,
                tableNamingSystem: "numbers",
                operatingHours: [
                    { day: "Monday", isOpen: true, openTime: "09:00", closeTime: "22:00" },
                    { day: "Tuesday", isOpen: true, openTime: "09:00", closeTime: "22:00" },
                    { day: "Wednesday", isOpen: true, openTime: "09:00", closeTime: "22:00" },
                    { day: "Thursday", isOpen: true, openTime: "09:00", closeTime: "22:00" },
                    { day: "Friday", isOpen: true, openTime: "09:00", closeTime: "23:00" },
                    { day: "Saturday", isOpen: true, openTime: "09:00", closeTime: "23:00" },
                    { day: "Sunday", isOpen: true, openTime: "10:00", closeTime: "21:00" }
                ],
                cuisineTypes: ["British", "International"],
                priceRange: { min: 10, max: 50 },
                averageServiceTime: 45,
                totalSeatingCapacity: 20,
                estimatedDailyCovers: 80
            };

            testCredentials.registrationData = registrationData;
            testCredentials.setupData = setupData;
            
            log(`✅ Created registration data for ${email} with password: ${password}`, 'success');
            log(`✅ Generated owner PIN: ${ownerPin}`, 'success');
        }

        async function completeRegistration() {
            log('Step 3: Completing restaurant registration...', 'info');
            
            try {
                const requestData = {
                    registrationData: {
                        ...testCredentials.registrationData,
                        agreeToTerms: testCredentials.registrationData.acceptTerms
                    },
                    setupData: testCredentials.setupData,
                    credentials: {
                        restaurantCode: testCredentials.restaurantCode,
                        ownerPin: testCredentials.ownerPin
                    }
                };

                // Remove frontend-specific fields
                delete requestData.registrationData.acceptTerms;
                delete requestData.registrationData.acceptPrivacy;

                log('Sending registration request to backend...', 'info');
                log(`Registration data: ${JSON.stringify(requestData, null, 2)}`, 'info');

                const response = await fetch('/api/restaurants/register', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(requestData)
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(`Registration failed: ${errorData.detail || response.statusText}`);
                }

                const result = await response.json();
                log(`✅ Registration successful! Restaurant ID: ${result.restaurant?.id}`, 'success');
                log(`✅ Restaurant code: ${testCredentials.restaurantCode}`, 'success');
                log(`✅ Owner PIN: ${testCredentials.ownerPin}`, 'success');
                
                return result;
                
            } catch (error) {
                log(`❌ Registration failed: ${error.message}`, 'error');
                throw error;
            }
        }

        async function testLogin() {
            log('Step 4: Testing login with registered credentials...', 'info');
            
            try {
                const loginData = {
                    code: testCredentials.restaurantCode,
                    password: testCredentials.registrationData.password
                };

                log(`Attempting login with code: ${loginData.code}, password: ${loginData.password}`, 'info');

                const response = await fetch('/api/restaurants/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(loginData)
                });

                if (response.ok) {
                    const result = await response.json();
                    log(`✅ Login successful! Restaurant: ${result.restaurant?.name}`, 'success');
                    log(`✅ AUTHENTICATION WORKING - Password: ${loginData.password}`, 'success');
                } else {
                    const errorData = await response.json();
                    log(`❌ Login failed: ${errorData.detail}`, 'error');
                    log(`❌ Used password: ${loginData.password}`, 'error');
                    
                    // Try alternative passwords
                    await tryAlternativePasswords();
                }
                
            } catch (error) {
                log(`❌ Login test failed: ${error.message}`, 'error');
                throw error;
            }
        }

        async function tryAlternativePasswords() {
            log('Trying alternative passwords...', 'info');
            
            const alternatives = ['testpass123', 'password123', 'test123', 'admin123'];
            
            for (const altPassword of alternatives) {
                try {
                    const response = await fetch('/api/restaurants/auth/login', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            code: testCredentials.restaurantCode,
                            password: altPassword
                        })
                    });

                    if (response.ok) {
                        const result = await response.json();
                        log(`✅ Alternative password works: ${altPassword}`, 'success');
                        return;
                    } else {
                        log(`❌ Alternative password failed: ${altPassword}`, 'error');
                    }
                } catch (error) {
                    log(`❌ Error testing ${altPassword}: ${error.message}`, 'error');
                }
            }
            
            log('❌ No alternative passwords worked', 'error');
        }

        function clearResults() {
            testResults = [];
            testCredentials = null;
            document.getElementById('testResults').innerHTML = '';
        }

        // Auto-generate unique email on page load
        window.addEventListener('load', () => {
            const timestamp = Date.now();
            document.getElementById('email').value = `test-flow-${timestamp}@example.com`;
            document.getElementById('restaurantName').value = `Test Restaurant ${timestamp}`;
        });
    </script>
</body>
</html>
