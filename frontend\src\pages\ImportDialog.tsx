// This file contains the modified Import Dialog component for the Inventory page
// It includes a file selection dialog with support for .csv, .xlsx, and .pdf files

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { toast } from "@/components/ui/sonner";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  FileUp,
  FileText,
  Check,
  X,
  AlertTriangle,
  Loader2
} from "lucide-react";
import { uploadFile, validateFileType } from "@/utils/fileUpload";

// Import Dialog Component
export const ImportDialog = ({ 
  isOpen, 
  onClose, 
  inventory, 
  onApplyChanges 
}) => {
  const [importData, setImportData] = useState({
    text: "",
    processing: false,
    results: null
  });

  // Handle file upload
  const handleFileUpload = async (file) => {
    if (!validateFileType(file)) {
      toast.error("Invalid file type. Only CSV, XLSX, and PDF files are allowed.");
      return;
    }

    setImportData(prev => ({ ...prev, processing: true }));

    try {
      // Upload the file to the server
      const response = await uploadFile(file);
      
      if (!response.success) {
        throw new Error(response.error || 'Error processing file');
      }

      // Process the results
      const processedResults = [];
      
      for (const item of response.results) {
        // Find matching inventory item
        const matchingItem = inventory.find(invItem =>
          invItem.name.toLowerCase().includes((item.name || '').toLowerCase())
        );

        processedResults.push({
          name: item.name,
          quantity: item.quantity,
          unit: item.unit,
          matchedItem: matchingItem || null,
          status: matchingItem ? "matched" : "not_found"
        });
      }

      setImportData(prev => ({
        ...prev,
        processing: false,
        results: processedResults
      }));
    } catch (error) {
      console.error("Error processing file:", error);
      setImportData(prev => ({
        ...prev,
        processing: false,
        results: null
      }));
      toast.error("Error processing file: " + error.message);
    }
  };

  // Apply imported data to inventory
  const handleApplyImport = () => {
    if (!importData.results || importData.results.length === 0) {
      toast.error("No valid data to import");
      return;
    }

    // Filter only matched items
    const matchedItems = importData.results.filter(r => r.status === "matched");
    
    if (matchedItems.length === 0) {
      toast.error("No matched items to import");
      return;
    }

    // Call the parent component's handler
    onApplyChanges(matchedItems);
    
    // Reset the form
    setImportData({
      text: "",
      processing: false,
      results: null
    });
    
    // Close the dialog
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[700px]">
        <DialogHeader>
          <DialogTitle>Import Inventory</DialogTitle>
          <p className="text-sm text-muted-foreground mt-1">
            Upload a file to automatically update inventory
          </p>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="fileUpload">Select File</Label>
            <div className="flex items-center gap-2">
              <Input
                id="fileUpload"
                type="file"
                accept=".csv,.xlsx,.pdf"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    // Clear previous results
                    setImportData({
                      text: "",
                      processing: false,
                      results: null
                    });
                    
                    // Handle file upload
                    handleFileUpload(file);
                  }
                }}
                disabled={importData.processing}
              />
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Supported file types: CSV, XLSX, PDF
            </p>
          </div>

          {importData.processing && (
            <div className="flex items-center justify-center py-4">
              <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
              <span className="ml-2">Processing file data...</span>
            </div>
          )}

          {importData.results && (
            <div className="border rounded-md overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Quantity</TableHead>
                    <TableHead>Unit</TableHead>
                    <TableHead>Matched Item</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {importData.results.map((result, index) => (
                    <TableRow key={index}>
                      <TableCell>{result.name || 'Unknown'}</TableCell>
                      <TableCell>{result.quantity || 'N/A'}</TableCell>
                      <TableCell>{result.unit || 'N/A'}</TableCell>
                      <TableCell>
                        {result.matchedItem ? (
                          <div className="text-sm">
                            <div className="font-medium">{result.matchedItem.name}</div>
                            <div className="text-xs text-muted-foreground">Current: {result.matchedItem.stock} {result.matchedItem.unit}</div>
                          </div>
                        ) : (
                          <span className="text-muted-foreground text-sm">No match found</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs ${
                          result.status === "matched" ? "bg-green-100 text-green-800" :
                          result.status === "not_found" ? "bg-yellow-100 text-yellow-800" :
                          "bg-red-100 text-red-800"
                        }`}>
                          {result.status === "matched" ? (
                            <><Check className="h-3 w-3 mr-1" /> Matched</>
                          ) : result.status === "not_found" ? (
                            <><AlertTriangle className="h-3 w-3 mr-1" /> Not Found</>
                          ) : (
                            <><X className="h-3 w-3 mr-1" /> Error</>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </div>
        <DialogFooter className="flex items-center justify-between">
          <div>
            {importData.results && (
              <div className="text-sm">
                <span className="font-medium">
                  {importData.results.filter(r => r.status === "matched").length} of {importData.results.length} items matched
                </span>
              </div>
            )}
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            {importData.results && (
              <Button onClick={handleApplyImport} disabled={!importData.results.some(r => r.status === "matched")}>
                <FileText className="mr-2 h-4 w-4" />
                Apply Changes
              </Button>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ImportDialog;
