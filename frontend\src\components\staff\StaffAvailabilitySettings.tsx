import logger from "@/utils/logger";
import { useState } from "react";
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "@/components/ui/sonner";
import { Calendar, Clock, Save, Sunrise, Sunset } from "lucide-react";

interface StaffAvailabilitySettingsProps {
  staffId: string;
  staffName: string;
  initialAssignedHours?: number;
  initialAvailableDays?: string[];
  initialTimePreferences?: {
    morning?: boolean;
    evening?: boolean;
  };
}

const daysOfWeek = [
  { id: "mon", label: "Monday" },
  { id: "tue", label: "Tuesday" },
  { id: "wed", label: "Wednesday" },
  { id: "thu", label: "Thursday" },
  { id: "fri", label: "Friday" },
  { id: "sat", label: "Saturday" },
  { id: "sun", label: "Sunday" },
];

const StaffAvailabilitySettings = ({
  // Initialize component logging
  staffName,
  initialAssignedHours = 35,
  initialAvailableDays = ["mon", "tue", "wed", "thu", "fri"],
  initialTimePreferences = { morning: true, evening: false }
}: StaffAvailabilitySettingsProps) => {
  // Initialize component logging
  logger.setComponent("StaffAvailabilitySettings");
  logger.info("Component initialized", "StaffAvailabilitySettings");
  const [assignedHours, setAssignedHours] = useState<number>(initialAssignedHours);
  const [availableDays, setAvailableDays] = useState<string[]>(initialAvailableDays);
  const [timePreferences, setTimePreferences] = useState({
    morning: initialTimePreferences?.morning ?? true,
    evening: initialTimePreferences?.evening ?? false
  });

  const handleDayToggle = (day: string) => {
    if (availableDays.includes(day)) {
      setAvailableDays(availableDays.filter(d => d !== day));
    } else {
      setAvailableDays([...availableDays, day]);
    }
  };

  const handleTimePreferenceToggle = (preference: 'morning' | 'evening') => {
    setTimePreferences(prev => ({
      ...prev,
      [preference]: !prev[preference]
    }));
  };

  const handleSave = () => {
    // In a real app, this would save to the database
    toast.success("Staff availability settings saved successfully");

    // You could also update the parent component or context here
    logger.dataOperation("save", "availability settings", "StaffAvailabilitySettings", {
      assignedHours,
      availableDays: availableDays.length,
      timePreferences
    });
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-medium">Availability Settings</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="assigned-hours">Assigned Hours per Week</Label>
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <Input
                id="assigned-hours"
                type="number"
                min="0"
                max="168"
                value={assignedHours}
                onChange={(e) => setAssignedHours(parseInt(e.target.value) || 0)}
                className="w-24"
              />
              <span className="text-muted-foreground">hours</span>
            </div>
          </div>

          <div className="space-y-3">
            <Label>Available Days</Label>
            <div className="flex items-center gap-2 mb-1">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">Select the days when this staff member is available to work</span>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
              {daysOfWeek.map((day) => (
                <div key={day.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={`day-${day.id}`}
                    checked={availableDays.includes(day.id)}
                    onCheckedChange={() => handleDayToggle(day.id)}
                  />
                  <Label htmlFor={`day-${day.id}`} className="cursor-pointer">
                    {day.label}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <div className="space-y-3">
            <Label>Time Preferences</Label>
            <div className="flex items-center gap-2 mb-1">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">Select preferred shift times</span>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="morning-preference"
                  checked={timePreferences.morning}
                  onCheckedChange={() => handleTimePreferenceToggle('morning')}
                />
                <Label htmlFor="morning-preference" className="cursor-pointer flex items-center">
                  <Sunrise className="h-4 w-4 mr-2 text-amber-500" /> Morning
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="evening-preference"
                  checked={timePreferences.evening}
                  onCheckedChange={() => handleTimePreferenceToggle('evening')}
                />
                <Label htmlFor="evening-preference" className="cursor-pointer flex items-center">
                  <Sunset className="h-4 w-4 mr-2 text-indigo-500" /> Evening
                </Label>
              </div>
            </div>
          </div>

          <Button onClick={handleSave} className="w-full">
            <Save className="h-4 w-4 mr-2" /> Save Availability Settings
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default StaffAvailabilitySettings;