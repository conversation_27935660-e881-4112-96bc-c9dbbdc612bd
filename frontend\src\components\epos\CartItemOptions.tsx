import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, Dialog<PERSON>ontent, Di<PERSON>Header, <PERSON><PERSON><PERSON><PERSON>le, DialogFooter } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { AlertTriangle, Edit3, Plus } from "lucide-react";

interface MenuItem {
  id: string;
  name: string;
  price: number;
  allergens?: string[];
  category?: string;
}

interface AddOn {
  id: string;
  name: string;
  price: number;
}

interface CartItemOptionsProps {
  isOpen: boolean;
  onClose: () => void;
  item: MenuItem | null;
  onConfirm: () => void;
  selectedAddOns: AddOn[];
  onAddOnsChange: (addOns: AddOn[]) => void;
  availableAddOns: AddOn[];
}

const CartItemOptions: React.FC<CartItemOptionsProps> = ({
  isOpen,
  onClose,
  item,
  onConfirm,
  selectedAddOns,
  onAddOnsChange,
  availableAddOns
}) => {
  const [notes, setNotes] = useState("");
  const [allergenAlert, setAllergenAlert] = useState(false);

  if (!item) return null;

  const handleAddOnToggle = (addOn: AddOn) => {
    const isSelected = selectedAddOns.some(selected => selected.id === addOn.id);
    if (isSelected) {
      onAddOnsChange(selectedAddOns.filter(selected => selected.id !== addOn.id));
    } else {
      onAddOnsChange([...selectedAddOns, addOn]);
    }
  };

  const calculateTotal = () => {
    const addOnsTotal = selectedAddOns.reduce((total, addOn) => total + addOn.price, 0);
    return item.price + addOnsTotal;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit3 className="w-5 h-5" />
            Customize {item.name}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Allergen Alert */}
          {item.allergens && item.allergens.length > 0 && (
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="allergen-alert"
                  checked={allergenAlert}
                  onCheckedChange={(checked) => setAllergenAlert(checked === true)}
                />
                <Label htmlFor="allergen-alert" className="text-sm font-medium">
                  Enable allergen alert for kitchen
                </Label>
              </div>
              
              <div className="bg-orange-50 border border-orange-200 rounded p-2">
                <p className="text-xs text-orange-700 mb-1">This item contains:</p>
                <div className="flex flex-wrap gap-1">
                  {item.allergens.map((allergen) => (
                    <Badge key={allergen} variant="outline" className="text-xs bg-orange-100 text-orange-800">
                      {allergen}
                    </Badge>
                  ))}
                </div>
              </div>

              {allergenAlert && (
                <div className="bg-red-50 border border-red-200 rounded p-2">
                  <p className="text-xs font-medium text-red-800">
                    ⚠️ Kitchen will see "** ALLERGY ALERT **" for this item
                  </p>
                </div>
              )}
            </div>
          )}

          {/* Special Instructions */}
          <div className="space-y-2">
            <Label htmlFor="notes" className="text-sm font-medium">
              Special Instructions
            </Label>
            <Textarea
              id="notes"
              placeholder="e.g., No onions, extra sauce, well done..."
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              className="min-h-[60px] text-sm"
              maxLength={150}
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>Kitchen instructions</span>
              <span>{notes.length}/150</span>
            </div>
          </div>

          {/* Add-ons */}
          {availableAddOns.length > 0 && (
            <div className="space-y-2">
              <Label className="text-sm font-medium">Add-ons</Label>
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {availableAddOns.map((addOn) => (
                  <div key={addOn.id} className="flex items-center justify-between p-2 border rounded">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        checked={selectedAddOns.some(selected => selected.id === addOn.id)}
                        onCheckedChange={() => handleAddOnToggle(addOn)}
                      />
                      <Label className="text-sm cursor-pointer">
                        {addOn.name}
                      </Label>
                    </div>
                    <span className="text-sm font-medium">
                      +£{addOn.price.toFixed(2)}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Total */}
          <div className="border-t pt-2">
            <div className="flex justify-between items-center">
              <span className="font-medium">Total:</span>
              <span className="font-bold text-lg">£{calculateTotal().toFixed(2)}</span>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={onConfirm}>
            <Plus className="w-4 h-4 mr-2" />
            Add to Order
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CartItemOptions;
