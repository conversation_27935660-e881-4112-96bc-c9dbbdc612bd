
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Bell } from "lucide-react";
import { useNotifications } from "@/contexts/NotificationContext";
import {
  createInventoryLowNotification,
  createInventoryExpiringNotification
} from "@/utils/notificationUtils";

interface InventoryItem {
  id: string;
  name: string;
  stock: number;
  maxStock: number;
  expiryDate?: string;
  category: string;
  isLow: boolean;
  isExpiring: boolean;
}

interface InventoryAlertCardProps {
  items: InventoryItem[];
}

const InventoryAlertCard = ({ items }: InventoryAlertCardProps) => {
  const { addNotification } = useNotifications();
  const [processedAlerts, setProcessedAlerts] = useState<Set<string>>(new Set());

  // Process inventory alerts and send notifications
  useEffect(() => {
    // Only process items that haven't been processed yet
    const unprocessedItems = items.filter(item => !processedAlerts.has(item.id));

    if (unprocessedItems.length > 0) {
      // Create a new set with all processed items
      const newProcessedAlerts = new Set(processedAlerts);

      // Process each unprocessed item
      unprocessedItems.forEach(item => {
        // Add to processed set
        newProcessedAlerts.add(item.id);

        // Send low stock notification
        if (item.isLow) {
          addNotification(createInventoryLowNotification(item.name, item.stock));
        }

        // Send expiring notification
        if (item.isExpiring && item.expiryDate) {
          addNotification(createInventoryExpiringNotification(item.name, item.expiryDate));
        }
      });

      // Update processed alerts
      setProcessedAlerts(newProcessedAlerts);
    }
  }, [items, processedAlerts, addNotification]);

  // Function to notify all alerts manually
  const handleNotifyAll = () => {
    items.forEach(item => {
      if (item.isLow) {
        addNotification(createInventoryLowNotification(item.name, item.stock));
      }

      if (item.isExpiring && item.expiryDate) {
        addNotification(createInventoryExpiringNotification(item.name, item.expiryDate));
      }
    });
  };

  return (
    <Card>
      <CardHeader className="pb-3 flex flex-row items-center justify-between">
        <CardTitle className="text-lg font-medium">Inventory Alerts</CardTitle>
        {items.length > 0 && (
          <Button
            variant="outline"
            size="sm"
            className="h-8 gap-1"
            onClick={handleNotifyAll}
          >
            <Bell className="h-3.5 w-3.5" />
            <span className="text-xs">Notify</span>
          </Button>
        )}
      </CardHeader>
      <CardContent className="px-6">
        <div className="space-y-4">
          {items.length === 0 ? (
            <p className="text-sm text-muted-foreground text-center py-4">
              No inventory alerts at this time
            </p>
          ) : (
            items.map((item) => (
              <div key={item.id} className="space-y-2">
                <div className="flex justify-between text-sm">
                  <div className="font-medium">
                    {item.name}{" "}
                    {item.isExpiring && (
                      <span className="text-xs text-restaurant-warning bg-restaurant-warning/10 px-1.5 py-0.5 rounded-full ml-1">
                        Expiring soon
                      </span>
                    )}
                  </div>
                  <div className="text-muted-foreground">
                    {item.stock} / {item.maxStock}
                  </div>
                </div>
                <Progress
                  value={(item.stock / item.maxStock) * 100}
                  className={cn("h-2", item.isLow && "bg-restaurant-secondary/20")}
                  indicatorClassName={cn(
                    item.isLow ? "bg-restaurant-secondary" : "bg-restaurant-success"
                  )}
                />
                {item.expiryDate && item.isExpiring && (
                  <div className="text-xs text-muted-foreground">
                    Expires on {item.expiryDate}
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default InventoryAlertCard;
