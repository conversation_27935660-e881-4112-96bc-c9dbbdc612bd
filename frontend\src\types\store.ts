/**
 * Comprehensive type definitions for the RestroManage state management system
 * Covers all tab states with restaurant ID isolation and proper TypeScript types
 */

// Base types for common patterns
export interface LoadingState {
  isLoading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

export interface PaginationState {
  page: number;
  limit: number;
  total: number;
  hasMore: boolean;
}

export interface FilterState {
  searchQuery: string;
  dateRange: {
    from: Date | null;
    to: Date | null;
  };
  categories: string[];
  status: string[];
}

// Dashboard State Types
export interface DashboardMetrics {
  todayRevenue: number;
  todayCustomers: number;
  inventoryItems: number;
  staffOnDuty: number;
  ordersToday: number;
  averageOrderValue: number;
  customerSatisfaction: number;
  tableOccupancy: number;
}

export interface DashboardState extends LoadingState {
  metrics: DashboardMetrics;
  recentOrders: Order[];
  alerts: Alert[];
  quickStats: QuickStat[];
  isRealTimeEnabled: boolean;
  refreshInterval: number;
}

// Analytics State Types
export interface AnalyticsData {
  salesData: SalesDataPoint[];
  revenueData: RevenueDataPoint[];
  popularItems: PopularItem[];
  categoryPerformance: CategoryPerformance[];
  peakHoursData: PeakHoursData[];
  profitMarginData: ProfitMarginData[];
  forecastData: ForecastDataPoint[];
}

export interface AnalyticsFilters extends FilterState {
  period: 'daily' | 'weekly' | 'monthly' | 'yearly';
  metrics: string[];
  compareWithPrevious: boolean;
}

export interface AnalyticsState extends LoadingState {
  data: AnalyticsData;
  filters: AnalyticsFilters;
  cachedData: Map<string, AnalyticsData>;
  chartConfigs: ChartConfig[];
  exportFormats: string[];
}

// Staff State Types
export interface StaffMember {
  id: string;
  name: string;
  email: string;
  phone: string;
  role: string;
  position: string;
  status: 'active' | 'inactive' | 'on-break' | 'off-duty';
  hireDate: string;
  performance: number;
  metrics: StaffMetrics;
  schedule: StaffSchedule;
  permissions: Permission[];
  pin?: string;
}

export interface StaffSchedule {
  id: string;
  staffId: string;
  shifts: Shift[];
  availableDays: string[];
  assignedHours: number;
  overtimeHours: number;
}

export interface StaffState extends LoadingState {
  members: StaffMember[];
  schedules: StaffSchedule[];
  activeStaff: StaffMember[];
  filters: FilterState;
  selectedMember: StaffMember | null;
  scheduleView: 'daily' | 'weekly' | 'monthly';
  permissions: Permission[];
  roles: Role[];
}

// Inventory State Types
export interface InventoryItem {
  id: string;
  name: string;
  category: string;
  stock: number;
  unit: string;
  reorderLevel: number;
  cost: number;
  supplier: string;
  expiryDate?: string;
  allergens: string[];
  isLow: boolean;
  isExpiring: boolean;
  lastUpdated: Date;
}

export interface InventoryTransaction {
  id: string;
  itemId: string;
  type: 'in' | 'out' | 'adjustment' | 'waste';
  quantity: number;
  reason: string;
  timestamp: Date;
  staffId: string;
  cost?: number;
}

export interface InventoryState extends LoadingState {
  items: InventoryItem[];
  transactions: InventoryTransaction[];
  suppliers: Supplier[];
  categories: string[];
  filters: InventoryFilters;
  alerts: InventoryAlert[];
  forecasts: InventoryForecast[];
  wasteLog: WasteEntry[];
}

// Orders State Types
export interface Order {
  id: string;
  tableId: string;
  customerId?: string;
  items: OrderItem[];
  status: 'pending' | 'preparing' | 'ready' | 'served' | 'paid' | 'cancelled';
  totalAmount: number;
  tax: number;
  discount: number;
  paymentMethod?: string;
  paymentStatus: 'pending' | 'processing' | 'completed' | 'failed';
  timestamp: Date;
  estimatedTime?: number;
  notes?: string;
  staffId: string;
}

export interface OrdersState extends LoadingState {
  activeOrders: Order[];
  orderHistory: Order[];
  queue: Order[];
  filters: FilterState;
  selectedOrder: Order | null;
  paymentStates: Map<string, PaymentState>;
  customerInfo: Map<string, Customer>;
  kitchenDisplay: KitchenDisplayOrder[];
}

// Settings State Types
export interface RestaurantSettings {
  id: string;
  name: string;
  logo?: File | string;
  address: string;
  phone: string;
  email: string;
  vatRate: number;
  currency: string;
  timezone: string;
  operatingHours: OperatingHours;
  tableConfiguration: TableConfig[];
  paymentMethods: PaymentMethod[];
  integrations: Integration[];
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  notifications: NotificationSettings;
  dashboard: DashboardPreferences;
  accessibility: AccessibilitySettings;
}

export interface SettingsState extends LoadingState {
  restaurant: RestaurantSettings;
  userPreferences: UserPreferences;
  systemSettings: SystemSettings;
  backupSettings: BackupSettings;
  securitySettings: SecuritySettings;
  isDirty: boolean;
  validationErrors: ValidationError[];
}

// Supporting types
export interface Alert {
  id: string;
  type: 'info' | 'warning' | 'error' | 'success';
  title: string;
  message: string;
  timestamp: Date;
  isRead: boolean;
  priority: 'low' | 'medium' | 'high';
}

export interface QuickStat {
  id: string;
  label: string;
  value: string | number;
  change: number;
  trend: 'up' | 'down' | 'stable';
  icon: string;
}

export interface SalesDataPoint {
  date: string;
  sales: number;
  orders: number;
  customers: number;
}

export interface RevenueDataPoint {
  period: string;
  revenue: number;
  profit: number;
  expenses: number;
}

export interface PopularItem {
  id: string;
  name: string;
  category: string;
  sales: number;
  revenue: number;
  rank: number;
}

export interface CategoryPerformance {
  category: string;
  sales: number;
  revenue: number;
  profit: number;
  growth: number;
}

export interface PeakHoursData {
  hour: number;
  orders: number;
  revenue: number;
  customers: number;
}

export interface ProfitMarginData {
  item: string;
  cost: number;
  price: number;
  margin: number;
  volume: number;
}

export interface ForecastDataPoint {
  date: string;
  predicted: number;
  actual?: number;
  confidence: number;
}

export interface ChartConfig {
  id: string;
  type: 'line' | 'bar' | 'pie' | 'area';
  dataKey: string;
  color: string;
  visible: boolean;
}

export interface StaffMetrics {
  sales: number;
  tablesTurned: number;
  customerRating: number;
  ordersProcessed: number;
  averageServiceTime: number;
}

export interface Shift {
  id: string;
  date: string;
  startTime: string;
  endTime: string;
  breakTime?: number;
  status: 'scheduled' | 'active' | 'completed' | 'missed';
}

export interface Permission {
  id: string;
  name: string;
  description: string;
  module: string;
  actions: string[];
}

export interface Role {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  level: number;
}

export interface Supplier {
  id: string;
  name: string;
  contact: string;
  email: string;
  phone: string;
  address: string;
  rating: number;
  paymentTerms: string;
}

export interface InventoryFilters extends FilterState {
  stockStatus: ('low' | 'normal' | 'high')[];
  suppliers: string[];
  expiryStatus: ('fresh' | 'expiring' | 'expired')[];
  minStock: number;
  maxStock: number;
}

export interface InventoryAlert {
  id: string;
  type: 'low-stock' | 'expiring' | 'expired' | 'overstock';
  itemId: string;
  message: string;
  severity: 'low' | 'medium' | 'high';
  timestamp: Date;
}

export interface InventoryForecast {
  itemId: string;
  predictedUsage: number;
  recommendedOrder: number;
  confidence: number;
  period: string;
}

export interface WasteEntry {
  id: string;
  itemId: string;
  quantity: number;
  reason: string;
  cost: number;
  timestamp: Date;
  staffId: string;
}

export interface OrderItem {
  id: string;
  menuItemId: string;
  name: string;
  quantity: number;
  price: number;
  modifications: string[];
  notes?: string;
}

export interface PaymentState {
  orderId: string;
  status: 'idle' | 'processing' | 'success' | 'error';
  method?: string;
  amount: number;
  transactionId?: string;
  error?: string;
}

export interface Customer {
  id: string;
  name?: string;
  email?: string;
  phone?: string;
  preferences: string[];
  orderHistory: string[];
  loyaltyPoints: number;
}

export interface KitchenDisplayOrder {
  orderId: string;
  tableId: string;
  items: OrderItem[];
  priority: 'low' | 'normal' | 'high';
  estimatedTime: number;
  status: 'pending' | 'preparing' | 'ready';
}

export interface OperatingHours {
  [key: string]: {
    open: string;
    close: string;
    isOpen: boolean;
  };
}

export interface TableConfig {
  id: string;
  number: number;
  seats: number;
  section: string;
  status: 'available' | 'occupied' | 'reserved' | 'maintenance';
}

export interface PaymentMethod {
  id: string;
  name: string;
  type: 'cash' | 'card' | 'digital';
  isEnabled: boolean;
  processingFee: number;
}

export interface Integration {
  id: string;
  name: string;
  type: string;
  isEnabled: boolean;
  config: Record<string, any>;
}

export interface NotificationSettings {
  email: boolean;
  push: boolean;
  sms: boolean;
  inApp: boolean;
  types: string[];
}

export interface DashboardPreferences {
  layout: string;
  widgets: string[];
  refreshInterval: number;
  defaultView: string;
}

export interface AccessibilitySettings {
  fontSize: 'small' | 'medium' | 'large';
  highContrast: boolean;
  reducedMotion: boolean;
  screenReader: boolean;
}

export interface SystemSettings {
  backupFrequency: string;
  dataRetention: number;
  maintenanceMode: boolean;
  debugMode: boolean;
}

export interface BackupSettings {
  autoBackup: boolean;
  frequency: string;
  retention: number;
  location: string;
}

export interface SecuritySettings {
  sessionTimeout: number;
  passwordPolicy: PasswordPolicy;
  twoFactorAuth: boolean;
  ipWhitelist: string[];
}

export interface PasswordPolicy {
  minLength: number;
  requireUppercase: boolean;
  requireLowercase: boolean;
  requireNumbers: boolean;
  requireSymbols: boolean;
  expiryDays: number;
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
}
