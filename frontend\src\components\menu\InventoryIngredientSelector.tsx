import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Plus, Trash2 } from "lucide-react";

interface InventoryItem {
  id: string;
  name: string;
  category: string;
  stock: number;
  unit: string;
  reorderLevel: number;
  cost: number;
  expiryDate?: string;
  isLow: boolean;
  isExpiring: boolean;
}

interface InventoryIngredient {
  inventoryItemId: string;
  name: string;
  quantity: number;
  unit: string;
}

interface InventoryIngredientSelectorProps {
  inventoryItems: InventoryItem[];
  selectedIngredients: InventoryIngredient[];
  onChange: (ingredients: InventoryIngredient[]) => void;
}

const InventoryIngredientSelector = ({
  inventoryItems,
  selectedIngredients,
  onChange
}: InventoryIngredientSelectorProps) => {
  const [selectedItemId, setSelectedItemId] = useState<string>("");
  const [quantity, setQuantity] = useState<number>(0);
  const [unit, setUnit] = useState<string>("g");

  const handleAddIngredient = () => {
    if (!selectedItemId || quantity <= 0) return;

    const selectedItem = inventoryItems.find(item => item.id === selectedItemId);
    if (!selectedItem) return;

    const newIngredient: InventoryIngredient = {
      inventoryItemId: selectedItem.id,
      name: selectedItem.name,
      quantity,
      unit
    };

    onChange([...selectedIngredients, newIngredient]);

    // Reset form
    setSelectedItemId("");
    setQuantity(0);
    setUnit("g");
  };

  const handleRemoveIngredient = (index: number) => {
    const updatedIngredients = [...selectedIngredients];
    updatedIngredients.splice(index, 1);
    onChange(updatedIngredients);
  };

  const availableInventoryItems = inventoryItems.filter(
    item => !selectedIngredients.some(ing => ing.inventoryItemId === item.id)
  );

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-4 gap-4">
        <div className="grid gap-2">
          <Label htmlFor="inventory-item">Inventory Item</Label>
          <Select
            value={selectedItemId}
            onValueChange={setSelectedItemId}
          >
            <SelectTrigger id="inventory-item">
              <SelectValue placeholder="Select item" />
            </SelectTrigger>
            <SelectContent>
              {availableInventoryItems.map(item => (
                <SelectItem key={item.id} value={item.id}>
                  {item.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="grid gap-2">
          <Label htmlFor="quantity">Quantity</Label>
          <Input
            id="quantity"
            type="number"
            min="0.1"
            step="0.1"
            value={quantity || ""}
            onChange={(e) => setQuantity(parseFloat(e.target.value) || 0)}
          />
        </div>
        <div className="grid gap-2">
          <Label htmlFor="unit">Unit</Label>
          <Select
            value={unit}
            onValueChange={setUnit}
          >
            <SelectTrigger id="unit">
              <SelectValue placeholder="Select unit" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="g">g (grams)</SelectItem>
              <SelectItem value="ml">ml (milliliters)</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="flex items-end">
          <Button
            onClick={handleAddIngredient}
            disabled={!selectedItemId || quantity <= 0}
            className="w-full"
          >
            <Plus className="h-4 w-4 mr-2" /> Add
          </Button>
        </div>
      </div>

      {selectedIngredients.length > 0 && (
        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Ingredient</TableHead>
                <TableHead>Quantity</TableHead>
                <TableHead>Unit</TableHead>
                <TableHead className="w-[80px]"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {selectedIngredients.map((ingredient, index) => (
                <TableRow key={index}>
                  <TableCell>{ingredient.name}</TableCell>
                  <TableCell>{ingredient.quantity}</TableCell>
                  <TableCell>{ingredient.unit}</TableCell>
                  <TableCell>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleRemoveIngredient(index)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  );
};

export default InventoryIngredientSelector;
