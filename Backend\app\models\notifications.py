"""
Pydantic models for notification management in RestroManage.
These models define the API request/response schemas for notifications.
"""

from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from enum import Enum


class NotificationType(str, Enum):
    """Notification type enumeration"""
    INFO = "info"
    SUCCESS = "success"
    WARNING = "warning"
    ERROR = "error"
    INVENTORY = "inventory"
    STAFF = "staff"
    ORDER = "order"
    RESERVATION = "reservation"


class NotificationPriority(str, Enum):
    """Notification priority enumeration"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"


class NotificationBase(BaseModel):
    """Base notification model"""
    title: str = Field(..., min_length=1, max_length=255, description="Notification title")
    message: str = Field(..., min_length=1, description="Notification message")
    type: NotificationType = Field(default=NotificationType.INFO, description="Notification type")
    priority: NotificationPriority = Field(default=NotificationPriority.MEDIUM, description="Notification priority")
    link: Optional[str] = Field(None, max_length=500, description="Optional navigation link")
    related_id: Optional[str] = Field(None, max_length=255, description="Optional related entity ID")


class NotificationCreate(NotificationBase):
    """Model for creating a new notification"""
    pass


class NotificationUpdate(BaseModel):
    """Model for updating a notification"""
    title: Optional[str] = Field(None, min_length=1, max_length=255, description="Notification title")
    message: Optional[str] = Field(None, min_length=1, description="Notification message")
    type: Optional[NotificationType] = Field(None, description="Notification type")
    priority: Optional[NotificationPriority] = Field(None, description="Notification priority")
    is_read: Optional[bool] = Field(None, description="Read status")
    link: Optional[str] = Field(None, max_length=500, description="Optional navigation link")
    related_id: Optional[str] = Field(None, max_length=255, description="Optional related entity ID")


class NotificationResponse(NotificationBase):
    """Model for notification responses"""
    id: str = Field(..., description="Notification ID")
    restaurant_id: str = Field(..., description="Restaurant ID")
    is_read: bool = Field(..., description="Read status")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    class Config:
        from_attributes = True


class NotificationMarkRead(BaseModel):
    """Model for marking notifications as read"""
    notification_ids: List[str] = Field(..., description="List of notification IDs to mark as read")


class NotificationStats(BaseModel):
    """Model for notification statistics"""
    total_count: int = Field(..., description="Total number of notifications")
    unread_count: int = Field(..., description="Number of unread notifications")
    by_type: dict = Field(..., description="Count by notification type")
    by_priority: dict = Field(..., description="Count by notification priority")


class NotificationFilter(BaseModel):
    """Model for filtering notifications"""
    type: Optional[NotificationType] = Field(None, description="Filter by notification type")
    priority: Optional[NotificationPriority] = Field(None, description="Filter by notification priority")
    is_read: Optional[bool] = Field(None, description="Filter by read status")
    limit: int = Field(default=50, ge=1, le=100, description="Maximum number of notifications to return")
    offset: int = Field(default=0, ge=0, description="Number of notifications to skip")
    order_by: str = Field(default="created_at", description="Field to order by")
    order_desc: bool = Field(default=True, description="Order in descending order")
