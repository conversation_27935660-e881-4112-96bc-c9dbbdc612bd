import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  CheckCircle, 
  Copy, 
  Eye, 
  EyeOff, 
  ArrowRight, 
  Building2, 
  Key, 
  User, 
  Mail,
  Phone,
  MapPin,
  AlertTriangle
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import logger from '@/utils/logger';

interface RegistrationData {
  restaurant: {
    id: string;
    name: string;
    code: string;
    email: string;
    phone: string;
    address: string;
    ownerName: string;
  };
  user: {
    id: string;
    name: string;
    email: string;
    role: string;
    pin: string;
  };
  credentials: {
    restaurantCode: string;
    ownerPin: string;
  };
}

const RegistrationSuccess: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [registrationData, setRegistrationData] = useState<RegistrationData | null>(null);
  const [showPin, setShowPin] = useState(false);
  const [copied, setCopied] = useState<{ [key: string]: boolean }>({});

  useEffect(() => {
    logger.setComponent('RegistrationSuccess');
    logger.registrationFlow.stepStart('credentials-display', 'RegistrationSuccess');

    // Get registration data from location state or localStorage
    const data = location.state?.registrationData ||
                 JSON.parse(localStorage.getItem('registrationResult') || 'null');

    if (!data) {
      logger.registrationFlow.stepError('credentials-display', 'No registration data found', 'RegistrationSuccess');
      toast({
        title: "Registration Required",
        description: "Please complete the registration process first.",
        variant: "destructive",
      });
      navigate('/register');
      return;
    }

    setRegistrationData(data);
    logger.registrationFlow.credentialsDisplayed('RegistrationSuccess');
    logger.registrationFlow.stepComplete('credentials-display', 'RegistrationSuccess', {
      restaurantName: data.restaurant?.name,
      restaurantCode: data.credentials?.restaurantCode
    });
  }, [location.state, navigate]);

  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied({ ...copied, [label]: true });
      toast({
        title: "Copied!",
        description: `${label} copied to clipboard`,
      });
      
      // Reset copied state after 2 seconds
      setTimeout(() => {
        setCopied({ ...copied, [label]: false });
      }, 2000);
    } catch (error) {
      logger.error('Failed to copy to clipboard', 'RegistrationSuccess', { error });
      toast({
        title: "Copy Failed",
        description: "Please copy the information manually",
        variant: "destructive",
      });
    }
  };

  const handleProceedToLogin = () => {
    const restaurantCode = registrationData?.credentials?.restaurantCode;
    logger.registrationFlow.loginRedirect(restaurantCode || 'unknown', 'RegistrationSuccess');

    // Clear registration data from localStorage
    localStorage.removeItem('registrationResult');
    localStorage.removeItem('registrationData');

    // Navigate to new restaurant onboarding flow
    navigate(`/onboarding/new-restaurant?code=${restaurantCode}`);
  };

  if (!registrationData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading registration details...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 flex items-center justify-center p-4">
      <div className="w-full max-w-2xl space-y-6">
        {/* Success Header */}
        <Card className="border-green-200 bg-green-50">
          <CardContent className="p-6 text-center">
            <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-green-800 mb-2">
              Registration Successful!
            </h1>
            <p className="text-green-700">
              Welcome to RestroManage! Your restaurant has been successfully registered.
            </p>
          </CardContent>
        </Card>

        {/* Restaurant Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              Restaurant Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-600">Restaurant Name</label>
                <p className="font-semibold">{registrationData.restaurant.name}</p>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-600">Owner</label>
                <p className="font-semibold">{registrationData.restaurant.ownerName}</p>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-600">Email</label>
                <p className="font-semibold">{registrationData.restaurant.email}</p>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-600">Phone</label>
                <p className="font-semibold">{registrationData.restaurant.phone}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Login Credentials */}
        <Card className="border-blue-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Key className="h-5 w-5" />
              Your Login Credentials
            </CardTitle>
            <div className="bg-amber-50 border border-amber-200 rounded-lg p-3 flex items-start gap-2">
              <AlertTriangle className="h-4 w-4 text-amber-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-amber-800">
                <p className="font-medium">Important: Save these credentials securely!</p>
                <p>You'll need these to access your restaurant dashboard.</p>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Restaurant Code */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-600">Restaurant Code</label>
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="text-lg px-4 py-2 font-mono">
                  {registrationData.credentials.restaurantCode}
                </Badge>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(registrationData.credentials.restaurantCode, 'Restaurant Code')}
                  className="flex items-center gap-1"
                >
                  <Copy className="h-3 w-3" />
                  {copied['Restaurant Code'] ? 'Copied!' : 'Copy'}
                </Button>
              </div>
            </div>

            {/* Owner PIN */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-600">Owner PIN</label>
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="text-lg px-4 py-2 font-mono">
                  {showPin ? registrationData.credentials.ownerPin : '••••'}
                </Badge>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowPin(!showPin)}
                  className="flex items-center gap-1"
                >
                  {showPin ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
                  {showPin ? 'Hide' : 'Show'}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(registrationData.credentials.ownerPin, 'Owner PIN')}
                  className="flex items-center gap-1"
                >
                  <Copy className="h-3 w-3" />
                  {copied['Owner PIN'] ? 'Copied!' : 'Copy'}
                </Button>
              </div>
            </div>

            <Separator />

            {/* Login Instructions */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="font-medium text-blue-800 mb-2">How to Access Your Dashboard:</h3>
              <ol className="text-sm text-blue-700 space-y-1 list-decimal list-inside">
                <li>Click "Proceed to Login" below</li>
                <li>Enter your Restaurant Code: <code className="bg-blue-100 px-1 rounded">{registrationData.credentials.restaurantCode}</code></li>
                <li>Enter your Owner PIN when prompted</li>
                <li>Start managing your restaurant!</li>
              </ol>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3">
          <Button
            onClick={handleProceedToLogin}
            className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
            size="lg"
          >
            <ArrowRight className="h-4 w-4 mr-2" />
            Proceed to Login
          </Button>
          <Button
            variant="outline"
            onClick={() => window.print()}
            size="lg"
          >
            Print Credentials
          </Button>
        </div>

        {/* Support Information */}
        <Card className="bg-gray-50">
          <CardContent className="p-4 text-center">
            <p className="text-sm text-gray-600">
              Need help? Contact our support team at{' '}
              <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
                <EMAIL>
              </a>
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default RegistrationSuccess;
