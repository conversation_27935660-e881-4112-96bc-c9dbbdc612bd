import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import Layout from "@/components/layout/Layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/contexts/AuthContext";
import { ShoppingBasket, CreditCard, LogOut } from "lucide-react";

// Import the enhanced time clock component
import EnhancedStaffTimeClock from "@/components/staff/EnhancedStaffTimeClock";

const StaffDashboard = () => {
  const navigate = useNavigate();
  const { user, isAuthenticated, logout, currentRestaurant } = useAuth();

  // Handle logout
  const handleLogout = () => {
    logout();
    navigate('/');
  };

  return (
    <Layout title="Staff Dashboard" requiredRoles={["staff", "waiter", "chef", "hostess", "bartender"]}>
      <div className="space-y-6">
        {/* Staff Time Clock */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-1">
            <EnhancedStaffTimeClock />
          </div>

          <div className="md:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-medium">Welcome, {user?.name}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-6">
                  You have access to the following modules:
                </p>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <Card className="bg-muted/50 hover:bg-muted transition-colors cursor-pointer" onClick={() => navigate('/admin/epos')}>
                    <CardContent className="p-6 flex flex-col items-center text-center">
                      <CreditCard className="h-12 w-12 mb-4 text-primary" />
                      <h3 className="text-lg font-medium mb-2">EPOS System</h3>
                      <p className="text-sm text-muted-foreground">
                        Process orders and payments
                      </p>
                    </CardContent>
                  </Card>

                  <Card className="bg-muted/50 hover:bg-muted transition-colors cursor-pointer" onClick={() => navigate('/admin/inventory')}>
                    <CardContent className="p-6 flex flex-col items-center text-center">
                      <ShoppingBasket className="h-12 w-12 mb-4 text-primary" />
                      <h3 className="text-lg font-medium mb-2">Inventory</h3>
                      <p className="text-sm text-muted-foreground">
                        Check stock levels and inventory
                      </p>
                    </CardContent>
                  </Card>
                </div>

                <div className="mt-6 flex justify-center">
                  <Button variant="outline" className="gap-2" onClick={handleLogout}>
                    <LogOut className="h-4 w-4" />
                    Logout
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default StaffDashboard;
