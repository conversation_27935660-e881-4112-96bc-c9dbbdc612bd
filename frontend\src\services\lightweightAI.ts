/**
 * Lightweight AI Service for Frontend
 * Optimized for performance with minimal overhead
 * Uses functional programming principles and efficient caching
 */

import logger from '@/utils/logger';

// Types
export interface AIMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  cached?: boolean;
}

export interface AIResponse {
  success: boolean;
  response: string;
  ai_enabled: boolean;
  cached?: boolean;
  timestamp: string;
  error?: string;
}

export interface AIStatus {
  ai_enabled: boolean;
  model?: string;
  features: string[];
  message: string;
}

// Configuration
const AI_CONFIG = {
  BASE_URL: '/api/ai',
  CACHE_TTL: 5 * 60 * 1000, // 5 minutes
  MAX_CACHE_SIZE: 50,
  REQUEST_TIMEOUT: 8000, // 8 seconds (reduced for faster response)
  DEBOUNCE_DELAY: 150, // 150ms (reduced for faster response)
} as const;

// Simple in-memory cache with TTL
class ResponseCache {
  private cache = new Map<string, { response: AIResponse; timestamp: number }>();
  
  get(query: string): AIResponse | null {
    const key = query.toLowerCase().trim();
    const cached = this.cache.get(key);
    
    if (!cached) return null;
    
    // Check if expired
    if (Date.now() - cached.timestamp > AI_CONFIG.CACHE_TTL) {
      this.cache.delete(key);
      return null;
    }
    
    return { ...cached.response, cached: true };
  }
  
  set(query: string, response: AIResponse): void {
    const key = query.toLowerCase().trim();
    
    // Evict oldest if cache is full
    if (this.cache.size >= AI_CONFIG.MAX_CACHE_SIZE) {
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
    }
    
    this.cache.set(key, {
      response: { ...response, cached: false },
      timestamp: Date.now()
    });
  }
  
  clear(): void {
    this.cache.clear();
  }
  
  size(): number {
    return this.cache.size;
  }
}

// Global cache instance
const responseCache = new ResponseCache();

// Debounce utility
function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => Promise<ReturnType<T>> {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>): Promise<ReturnType<T>> => {
    return new Promise((resolve, reject) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(async () => {
        try {
          const result = await func(...args);
          resolve(result);
        } catch (error) {
          reject(error);
        }
      }, delay);
    });
  };
}

// HTTP request utility with timeout
async function fetchWithTimeout(url: string, options: RequestInit = {}): Promise<Response> {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), AI_CONFIG.REQUEST_TIMEOUT);
  
  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });
    
    clearTimeout(timeoutId);
    return response;
  } catch (error) {
    clearTimeout(timeoutId);
    throw error;
  }
}

// Core AI functions
async function callAIAPI(query: string): Promise<AIResponse> {
  try {
    const response = await fetchWithTimeout(`${AI_CONFIG.BASE_URL}/chat`, {
      method: 'POST',
      body: JSON.stringify({ message: query }),
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    logger.error('AI API call failed', 'LightweightAI', { error: error.message });
    throw error;
  }
}

// Debounced AI call for typing scenarios
const debouncedAICall = debounce(callAIAPI, AI_CONFIG.DEBOUNCE_DELAY);

// Direct AI call for immediate sends (no debouncing)
const directAICall = callAIAPI;

// Main AI service class
class LightweightAIService {
  private isInitialized = false;
  private status: AIStatus | null = null;
  
  /**
   * Initialize the AI service
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;
    
    try {
      this.status = await this.getStatus();
      this.isInitialized = true;
      logger.info('Lightweight AI service initialized', 'LightweightAI', {
        ai_enabled: this.status.ai_enabled
      });
    } catch (error) {
      logger.error('Failed to initialize AI service', 'LightweightAI', { error: error.message });
      this.status = {
        ai_enabled: false,
        features: [],
        message: 'AI service unavailable'
      };
      this.isInitialized = true;
    }
  }
  
  /**
   * Get AI service status
   */
  async getStatus(): Promise<AIStatus> {
    try {
      const response = await fetchWithTimeout(`${AI_CONFIG.BASE_URL}/status`);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      logger.error('Failed to get AI status', 'LightweightAI', { error: error.message });
      return {
        ai_enabled: false,
        features: [],
        message: 'AI service unavailable'
      };
    }
  }
  
  /**
   * Chat with AI assistant (optimized for immediate response)
   */
  async chat(query: string, immediate: boolean = true): Promise<AIResponse> {
    await this.initialize();

    // Validate input
    if (!query || query.trim().length === 0) {
      throw new Error('Query cannot be empty');
    }

    if (query.length > 2000) {
      throw new Error('Query too long (max 2000 characters)');
    }

    const trimmedQuery = query.trim();

    // Check cache first
    const cachedResponse = responseCache.get(trimmedQuery);
    if (cachedResponse) {
      logger.debug('Returning cached AI response', 'LightweightAI');
      return cachedResponse;
    }

    try {
      // Use direct call for immediate responses, debounced for typing scenarios
      const response = immediate
        ? await directAICall(trimmedQuery)
        : await debouncedAICall(trimmedQuery);

      // Cache successful responses
      if (response.success) {
        responseCache.set(trimmedQuery, response);
      }

      logger.info('AI chat completed', 'LightweightAI', {
        success: response.success,
        cached: false,
        immediate,
        query_length: trimmedQuery.length
      });

      return response;
    } catch (error) {
      logger.error('AI chat failed', 'LightweightAI', { error: error.message });

      // Return fallback response
      return {
        success: false,
        response: "I'm sorry, I'm currently experiencing technical difficulties. Please try again in a moment.",
        ai_enabled: this.status?.ai_enabled || false,
        timestamp: new Date().toISOString(),
        error: error.message
      };
    }
  }
  
  /**
   * Check if AI is enabled
   */
  isEnabled(): boolean {
    return this.status?.ai_enabled || false;
  }
  
  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; maxSize: number } {
    return {
      size: responseCache.size(),
      maxSize: AI_CONFIG.MAX_CACHE_SIZE
    };
  }
  
  /**
   * Clear response cache
   */
  clearCache(): void {
    responseCache.clear();
    logger.info('AI response cache cleared', 'LightweightAI');
  }
  
  /**
   * Generate message suggestions
   */
  getSuggestions(): string[] {
    return [
      "Hello! How are you today?",
      "What can you help me with?",
      "Tell me a fun fact",
      "How can I be more productive?",
      "What's new in technology?",
      "Can you explain something to me?",
      "Tell me a joke",
      "What are some good books?",
      "How do I learn something new?",
      "What's the weather like?"
    ];
  }
}

// Export singleton instance
export const lightweightAI = new LightweightAIService();
export default lightweightAI;
