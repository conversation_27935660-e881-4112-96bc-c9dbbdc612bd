"""
Discount controller implementing business logic for discount operations.
Handles discount management, promo codes, and promotional campaigns.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime

from app.controllers.base import BaseController
from app.utils.storage_async import get_all_async, get_by_id_async, create_async, update_async, delete_async, query_async
from app.utils.logging_config import logger

class DiscountController(BaseController):
    """Controller for discount business logic and operations"""

    def __init__(self):
        super().__init__()
        self.cache_prefix = "discount"
        self.default_cache_ttl = 600  # 10 minutes for discount data
        self.max_discount_percentage = 40.0  # Maximum 40% discount limit
        self.valid_discount_types = ["percentage", "fixed_amount", "buy_one_get_one"]
        self.valid_scopes = ["restaurant", "category", "item"]

    async def get_discounts(
        self,
        restaurant_id: Optional[str] = None,
        active_only: bool = True,
        discount_type: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Get discounts with filtering and caching"""
        # Validate pagination
        skip, limit = self.validate_pagination(skip, limit)

        # Create cache key based on filters
        filter_key = f"{restaurant_id}_{active_only}_{discount_type}_{skip}_{limit}"
        cache_key = f"{self.cache_prefix}_filtered_{hash(filter_key)}"

        # Check cache first
        cached_result = self.get_cached_result(cache_key)
        if cached_result:
            return cached_result

        # Fetch promo codes
        promo_codes = await self.handle_async_operation(
            get_all_async,
            "promo_codes",
            error_message="Failed to fetch promo codes"
        )

        # Apply filters
        filtered_discounts = self._apply_filters(promo_codes, restaurant_id, active_only, discount_type)

        # Apply pagination
        paginated_discounts = filtered_discounts[skip:skip + limit]

        # Cache the result
        self.cache_result(cache_key, paginated_discounts, self.default_cache_ttl)

        logger.info(
            f"Retrieved {len(paginated_discounts)} discounts",
            "DiscountController",
            {"total_filtered": len(filtered_discounts), "restaurant_id": restaurant_id}
        )

        return paginated_discounts

    def _apply_filters(
        self,
        promo_codes: List[Dict[str, Any]],
        restaurant_id: Optional[str] = None,
        active_only: bool = True,
        discount_type: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Apply filters to promo codes"""
        filtered_codes = promo_codes.copy()

        if restaurant_id:
            filtered_codes = [
                code for code in filtered_codes
                if code.get("restaurant_id") == restaurant_id or code.get("scope") == "global"
            ]

        if active_only:
            current_time = datetime.now()
            filtered_codes = [
                code for code in filtered_codes
                if (
                    code.get("is_active", True) and
                    (not code.get("start_date") or datetime.fromisoformat(code["start_date"]) <= current_time) and
                    (not code.get("end_date") or datetime.fromisoformat(code["end_date"]) >= current_time) and
                    (not code.get("usage_limit") or code.get("usage_count", 0) < code["usage_limit"])
                )
            ]

        if discount_type:
            filtered_codes = [
                code for code in filtered_codes
                if code.get("discount_type") == discount_type
            ]

        return filtered_codes

    async def validate_promo_code(
        self,
        code: str,
        restaurant_id: str,
        order_total: float,
        customer_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Validate promo code and calculate discount"""
        # Find promo code
        promo_codes = await self.handle_async_operation(
            query_async,
            "promo_codes",
            lambda p: p.get("code", "").upper() == code.upper(),
            error_message="Failed to fetch promo code"
        )

        if not promo_codes:
            return {
                "valid": False,
                "error": "Promo code not found",
                "discount_amount": 0
            }

        promo_code = promo_codes[0]

        # Validate restaurant scope
        if promo_code.get("restaurant_id") and promo_code["restaurant_id"] != restaurant_id:
            if promo_code.get("scope") != "global":
                return {
                    "valid": False,
                    "error": "Promo code not valid for this restaurant",
                    "discount_amount": 0
                }

        # Check if promo code is active
        if not promo_code.get("is_active", True):
            return {
                "valid": False,
                "error": "Promo code is inactive",
                "discount_amount": 0
            }

        # Check date validity
        current_time = datetime.now()
        if promo_code.get("start_date"):
            start_date = datetime.fromisoformat(promo_code["start_date"])
            if current_time < start_date:
                return {
                    "valid": False,
                    "error": "Promo code is not yet active",
                    "discount_amount": 0
                }

        if promo_code.get("end_date"):
            end_date = datetime.fromisoformat(promo_code["end_date"])
            if current_time > end_date:
                return {
                    "valid": False,
                    "error": "Promo code has expired",
                    "discount_amount": 0
                }

        # Check usage limit
        usage_count = promo_code.get("usage_count", 0)
        usage_limit = promo_code.get("usage_limit")
        if usage_limit and usage_count >= usage_limit:
            return {
                "valid": False,
                "error": "Promo code usage limit reached",
                "discount_amount": 0
            }

        # Check minimum order amount
        min_order_amount = promo_code.get("min_order_amount", 0)
        if order_total < min_order_amount:
            return {
                "valid": False,
                "error": f"Minimum order amount of £{min_order_amount:.2f} required",
                "discount_amount": 0
            }

        # Calculate discount amount
        discount_amount = self._calculate_discount_amount(promo_code, order_total)

        # Apply maximum discount limit
        max_discount = order_total * (self.max_discount_percentage / 100)
        if discount_amount > max_discount:
            discount_amount = max_discount

        return {
            "valid": True,
            "promo_code": promo_code,
            "discount_amount": round(discount_amount, 2),
            "discount_type": promo_code.get("discount_type"),
            "description": promo_code.get("description", ""),
            "final_total": round(order_total - discount_amount, 2)
        }

    def _calculate_discount_amount(self, promo_code: Dict[str, Any], order_total: float) -> float:
        """Calculate discount amount based on promo code type"""
        discount_type = promo_code.get("discount_type", "percentage")
        discount_value = promo_code.get("discount_value", 0)

        if discount_type == "percentage":
            # Ensure percentage doesn't exceed maximum
            percentage = min(discount_value, self.max_discount_percentage)
            return order_total * (percentage / 100)
        elif discount_type == "fixed_amount":
            # Fixed amount discount, but not more than order total
            return min(discount_value, order_total)
        elif discount_type == "buy_one_get_one":
            # BOGO logic - 50% discount
            return order_total * 0.5
        else:
            return 0

    async def apply_discount(
        self,
        order_id: str,
        promo_code: str,
        order_total: float,
        restaurant_id: str,
        customer_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Apply discount to order and update usage count"""
        # Validate promo code first
        validation_result = await self.validate_promo_code(
            promo_code, restaurant_id, order_total, customer_id
        )

        if not validation_result["valid"]:
            return {
                "success": False,
                "error": validation_result["error"],
                "discount_amount": 0
            }

        promo_code_data = validation_result["promo_code"]
        discount_amount = validation_result["discount_amount"]

        try:
            # Update promo code usage count
            current_usage = promo_code_data.get("usage_count", 0)
            await self.handle_async_operation(
                update_async,
                "promo_codes",
                promo_code_data["id"],
                {
                    "usage_count": current_usage + 1,
                    "last_used": datetime.now().isoformat()
                },
                error_message="Failed to update promo code usage"
            )

            # Create usage record
            usage_record = {
                "promo_code_id": promo_code_data["id"],
                "order_id": order_id,
                "customer_id": customer_id,
                "restaurant_id": restaurant_id,
                "discount_amount": discount_amount,
                "order_total": order_total,
                "used_at": datetime.now().isoformat()
            }

            await self.handle_async_operation(
                create_async,
                "promo_code_usage",
                usage_record,
                error_message="Failed to create usage record"
            )

            # Invalidate relevant caches
            self.invalidate_cache_pattern(self.cache_prefix)

            logger.info(
                f"Applied discount: {promo_code} to order {order_id}",
                "DiscountController",
                {
                    "discount_amount": discount_amount,
                    "order_total": order_total,
                    "restaurant_id": restaurant_id
                }
            )

            return {
                "success": True,
                "discount_amount": discount_amount,
                "final_total": order_total - discount_amount,
                "promo_code": promo_code,
                "description": promo_code_data.get("description", "")
            }

        except Exception as e:
            logger.error(f"Failed to apply discount: {e}", "DiscountController")
            return {
                "success": False,
                "error": "Failed to apply discount",
                "discount_amount": 0
            }

    async def create_promo_code(self, promo_code_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new promo code with validation"""
        # Validate required fields
        required_fields = ["code", "discount_type", "discount_value"]
        missing_fields = [field for field in required_fields if field not in promo_code_data]

        if missing_fields:
            raise self.handle_validation_error(
                f"Missing required fields: {', '.join(missing_fields)}",
                {"missing_fields": missing_fields}
            )

        # Validate discount type
        if promo_code_data["discount_type"] not in self.valid_discount_types:
            raise self.handle_validation_error(
                f"Invalid discount type: {promo_code_data['discount_type']}",
                {"valid_types": self.valid_discount_types}
            )

        # Validate discount value
        if promo_code_data["discount_type"] == "percentage":
            if promo_code_data["discount_value"] > self.max_discount_percentage:
                raise self.handle_validation_error(
                    f"Percentage discount cannot exceed {self.max_discount_percentage}%",
                    {"max_percentage": self.max_discount_percentage}
                )

        # Check if code already exists
        code_upper = promo_code_data["code"].upper()
        existing_codes = await self.handle_async_operation(
            query_async,
            "promo_codes",
            lambda p: p.get("code", "").upper() == code_upper,
            error_message="Failed to check existing promo codes"
        )

        if existing_codes:
            raise self.handle_validation_error(
                f"Promo code '{promo_code_data['code']}' already exists",
                {"code": promo_code_data["code"]}
            )

        # Set default values
        promo_code_data["code"] = code_upper
        promo_code_data["usage_count"] = 0
        promo_code_data["is_active"] = promo_code_data.get("is_active", True)
        promo_code_data["created_at"] = datetime.now().isoformat()
        promo_code_data["updated_at"] = datetime.now().isoformat()

        # Create promo code
        created_code = await self.handle_async_operation(
            create_async,
            "promo_codes",
            promo_code_data,
            error_message="Failed to create promo code"
        )

        # Invalidate relevant caches
        self.invalidate_cache_pattern(self.cache_prefix)

        logger.info(
            f"Created promo code: {created_code.get('code')}",
            "DiscountController",
            {"promo_code_id": created_code.get("id")}
        )

        return created_code

    async def get_promo_code_by_id(self, promo_code_id: str) -> Optional[Dict[str, Any]]:
        """Get promo code by ID with caching"""
        cache_key = f"{self.cache_prefix}_code_{promo_code_id}"
        cached_result = self.get_cached_result(cache_key)

        if cached_result:
            return cached_result

        promo_code = await self.handle_async_operation(
            get_by_id_async,
            "promo_codes",
            promo_code_id,
            error_message=f"Failed to fetch promo code {promo_code_id}"
        )

        if promo_code:
            self.cache_result(cache_key, promo_code, self.default_cache_ttl)
            logger.info(f"Retrieved promo code: {promo_code.get('code')}", "DiscountController")

        return promo_code

    async def update_promo_code(
        self,
        promo_code_id: str,
        update_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Update promo code with validation"""
        # Get existing promo code
        existing_code = await self.get_promo_code_by_id(promo_code_id)
        if not existing_code:
            raise self.handle_not_found("Promo code", promo_code_id)

        # Validate discount type if being updated
        if "discount_type" in update_data:
            if update_data["discount_type"] not in self.valid_discount_types:
                raise self.handle_validation_error(
                    f"Invalid discount type: {update_data['discount_type']}",
                    {"valid_types": self.valid_discount_types}
                )

        # Validate discount value if being updated
        if "discount_value" in update_data:
            discount_type = update_data.get("discount_type", existing_code.get("discount_type"))
            if discount_type == "percentage" and update_data["discount_value"] > self.max_discount_percentage:
                raise self.handle_validation_error(
                    f"Percentage discount cannot exceed {self.max_discount_percentage}%",
                    {"max_percentage": self.max_discount_percentage}
                )

        # Check code uniqueness if being updated
        if "code" in update_data:
            code_upper = update_data["code"].upper()
            existing_codes = await self.handle_async_operation(
                query_async,
                "promo_codes",
                lambda p: (
                    p.get("code", "").upper() == code_upper and
                    p.get("id") != promo_code_id
                ),
                error_message="Failed to check code uniqueness"
            )

            if existing_codes:
                raise self.handle_validation_error(
                    f"Promo code '{update_data['code']}' already exists",
                    {"code": update_data["code"]}
                )

            update_data["code"] = code_upper

        # Add update timestamp
        update_data["updated_at"] = datetime.now().isoformat()

        # Update promo code
        updated_code = await self.handle_async_operation(
            update_async,
            "promo_codes",
            promo_code_id,
            update_data,
            error_message=f"Failed to update promo code {promo_code_id}"
        )

        # Invalidate relevant caches
        self.invalidate_cache(f"{self.cache_prefix}_code_{promo_code_id}")
        self.invalidate_cache_pattern(f"{self.cache_prefix}_filtered")

        logger.info(
            f"Updated promo code: {updated_code.get('code')}",
            "DiscountController",
            {"promo_code_id": promo_code_id}
        )

        return updated_code

    async def deactivate_promo_code(self, promo_code_id: str) -> Dict[str, Any]:
        """Deactivate a promo code"""
        return await self.update_promo_code(
            promo_code_id,
            {"is_active": False, "deactivated_at": datetime.now().isoformat()}
        )

    async def get_promo_code_usage(
        self,
        promo_code_id: Optional[str] = None,
        restaurant_id: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Get promo code usage records"""
        # Validate pagination
        skip, limit = self.validate_pagination(skip, limit)

        # Create cache key
        filter_key = f"{promo_code_id}_{restaurant_id}_{skip}_{limit}"
        cache_key = f"{self.cache_prefix}_usage_{hash(filter_key)}"

        # Check cache first
        cached_result = self.get_cached_result(cache_key)
        if cached_result:
            return cached_result

        # Fetch usage records
        usage_records = await self.handle_async_operation(
            get_all_async,
            "promo_code_usage",
            error_message="Failed to fetch usage records"
        )

        # Apply filters
        filtered_records = usage_records
        if promo_code_id:
            filtered_records = [
                record for record in filtered_records
                if record.get("promo_code_id") == promo_code_id
            ]

        if restaurant_id:
            filtered_records = [
                record for record in filtered_records
                if record.get("restaurant_id") == restaurant_id
            ]

        # Apply pagination
        paginated_records = filtered_records[skip:skip + limit]

        # Cache the result
        self.cache_result(cache_key, paginated_records, self.default_cache_ttl)

        return paginated_records

    async def get_discount_analytics(self, restaurant_id: str) -> Dict[str, Any]:
        """Get discount analytics for restaurant"""
        cache_key = f"{self.cache_prefix}_analytics_{restaurant_id}"
        cached_result = self.get_cached_result(cache_key)

        if cached_result:
            return cached_result

        # Get usage records for restaurant
        usage_records = await self.get_promo_code_usage(restaurant_id=restaurant_id, limit=1000)

        # Calculate analytics
        total_discounts = len(usage_records)
        total_discount_amount = sum(record.get("discount_amount", 0) for record in usage_records)
        total_order_value = sum(record.get("order_total", 0) for record in usage_records)

        # Get active promo codes
        active_codes = await self.get_discounts(restaurant_id=restaurant_id, active_only=True, limit=1000)

        analytics_data = {
            "restaurant_id": restaurant_id,
            "total_discounts_applied": total_discounts,
            "total_discount_amount": round(total_discount_amount, 2),
            "total_order_value": round(total_order_value, 2),
            "average_discount": round(total_discount_amount / total_discounts, 2) if total_discounts > 0 else 0,
            "discount_percentage": round((total_discount_amount / total_order_value) * 100, 2) if total_order_value > 0 else 0,
            "active_promo_codes": len(active_codes),
            "generated_at": datetime.now().isoformat()
        }

        # Cache the result
        self.cache_result(cache_key, analytics_data, 900)  # 15 minutes cache for analytics

        return analytics_data
