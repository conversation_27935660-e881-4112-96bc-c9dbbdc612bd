from pydantic import BaseModel, Field, field_validator
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum

class SplitType(str, Enum):
    BY_ITEMS = "by_items"
    EQUAL_SPLIT = "equal_split"
    CUSTOM_AMOUNTS = "custom_amounts"
    PERCENTAGE_SPLIT = "percentage_split"

class PaymentMethod(str, Enum):
    CASH = "cash"
    CARD = "card"
    DIGITAL_WALLET = "digital_wallet"
    GIFT_CARD = "gift_card"
    LOYALTY_POINTS = "loyalty_points"

class SplitBillStatus(str, Enum):
    PENDING = "pending"
    PARTIALLY_PAID = "partially_paid"
    COMPLETED = "completed"
    CANCELLED = "cancelled"

class SplitBillItemBase(BaseModel):
    order_item_id: str
    quantity: int = 1
    price: float
    customer_name: Optional[str] = None
    customer_id: Optional[str] = None

class SplitBillItem(SplitBillItemBase):
    id: str
    item_name: str
    subtotal: float

class SplitPortionBase(BaseModel):
    customer_name: Optional[str] = None
    customer_id: Optional[str] = None
    amount: float
    items: Optional[List[SplitBillItemBase]] = None
    tip_amount: Optional[float] = 0.0
    tax_amount: Optional[float] = 0.0
    discount_amount: Optional[float] = 0.0

class SplitPortion(SplitPortionBase):
    id: str
    portion_number: int
    total_amount: float
    payment_status: str = "pending"
    payment_method: Optional[PaymentMethod] = None
    payment_reference: Optional[str] = None
    paid_at: Optional[datetime] = None
    items: List[SplitBillItem] = []

class SplitBillBase(BaseModel):
    original_order_id: str
    split_type: SplitType
    number_of_splits: int
    portions: List[SplitPortionBase]
    notes: Optional[str] = None

    @field_validator('number_of_splits')
    @classmethod
    def validate_splits(cls, v):
        if v < 2:
            raise ValueError('Number of splits must be at least 2')
        if v > 10:
            raise ValueError('Number of splits cannot exceed 10')
        return v

    @field_validator('portions')
    @classmethod
    def validate_portions(cls, v, info):
        if len(v) != info.data.get('number_of_splits', 0):
            raise ValueError('Number of portions must match number of splits')
        return v

class SplitBillCreate(SplitBillBase):
    pass

class SplitBillUpdate(BaseModel):
    split_type: Optional[SplitType] = None
    portions: Optional[List[SplitPortionBase]] = None
    notes: Optional[str] = None

class SplitBill(SplitBillBase):
    id: str
    status: SplitBillStatus = SplitBillStatus.PENDING
    original_total: float
    total_paid: float = 0.0
    remaining_balance: float
    portions: List[SplitPortion] = []
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)

class PaymentRequest(BaseModel):
    split_bill_id: str
    portion_id: str
    payment_method: PaymentMethod
    amount: float
    tip_amount: Optional[float] = 0.0
    payment_reference: Optional[str] = None
    customer_name: Optional[str] = None

class PaymentResponse(BaseModel):
    success: bool
    payment_id: str
    receipt_number: str
    amount_paid: float
    remaining_balance: float
    payment_method: PaymentMethod
    processed_at: datetime = Field(default_factory=datetime.now)

class SplitBillSummary(BaseModel):
    split_bill_id: str
    original_order_id: str
    total_amount: float
    total_paid: float
    remaining_balance: float
    number_of_portions: int
    paid_portions: int
    status: SplitBillStatus
    created_at: datetime

class ReceiptData(BaseModel):
    receipt_number: str
    split_bill_id: str
    portion_id: str
    customer_name: Optional[str] = None
    items: List[SplitBillItem]
    subtotal: float
    tax_amount: float
    tip_amount: float
    discount_amount: float
    total_amount: float
    payment_method: PaymentMethod
    payment_reference: Optional[str] = None
    generated_at: datetime = Field(default_factory=datetime.now)
