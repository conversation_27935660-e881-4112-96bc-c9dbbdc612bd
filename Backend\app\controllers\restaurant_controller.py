"""
Restaurant controller implementing business logic for restaurant operations.
Handles restaurant CRUD operations, authentication, and user management.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
import random
import string

from app.controllers.base import BaseController
from app.utils.storage_async import get_all_async, get_by_id_async, create_async, update_async, delete_async, query_async
from app.models.restaurants import (
    Restaurant, RestaurantCreate, RestaurantUpdate, RestaurantResponse,
    RestaurantUser, RestaurantUserCreate, RestaurantUserUpdate, RestaurantUserResponse
)
from app.utils.logging_config import logger

class RestaurantController(BaseController):
    """Controller for restaurant business logic and operations"""
    
    def __init__(self):
        super().__init__()
        self.cache_prefix = "restaurant"
        self.default_cache_ttl = 600  # 10 minutes for restaurant data
    
    async def get_all_restaurants(self, skip: int = 0, limit: int = 100) -> List[Dict[str, Any]]:
        """
        Get all restaurants with caching and pagination.
        
        Args:
            skip: Number of restaurants to skip
            limit: Maximum number of restaurants to return
            
        Returns:
            List of restaurant dictionaries
        """
        # Validate pagination
        skip, limit = self.validate_pagination(skip, limit)
        
        # Check cache first
        cache_key = f"{self.cache_prefix}_all_{skip}_{limit}"
        cached_result = self.get_cached_result(cache_key)
        
        if cached_result:
            return cached_result
        
        # Fetch from database
        restaurants = await self.handle_async_operation(
            get_all_async,
            "restaurants",
            error_message="Failed to fetch restaurants"
        )
        
        # Apply pagination
        paginated_restaurants = restaurants[skip:skip + limit]
        
        # Cache the result
        self.cache_result(cache_key, paginated_restaurants, self.default_cache_ttl)
        
        logger.info(f"Retrieved {len(paginated_restaurants)} restaurants", "RestaurantController")
        return paginated_restaurants
    
    async def get_restaurant_by_id(self, restaurant_id: str) -> Optional[Dict[str, Any]]:
        """
        Get restaurant by ID with caching.
        
        Args:
            restaurant_id: Restaurant ID
            
        Returns:
            Restaurant dictionary or None if not found
        """
        # Check cache first
        cache_key = f"{self.cache_prefix}_{restaurant_id}"
        cached_result = self.get_cached_result(cache_key)
        
        if cached_result:
            return cached_result
        
        # Fetch from database
        restaurant = await self.handle_async_operation(
            get_by_id_async,
            "restaurants",
            restaurant_id,
            error_message=f"Failed to fetch restaurant {restaurant_id}"
        )
        
        if restaurant:
            # Cache the result
            self.cache_result(cache_key, restaurant, self.default_cache_ttl)
            logger.info(f"Retrieved restaurant: {restaurant.get('name', 'Unknown')}", "RestaurantController")
        
        return restaurant
    
    async def get_restaurant_by_code(self, restaurant_code: str) -> Optional[Dict[str, Any]]:
        """
        Get restaurant by code with caching.
        
        Args:
            restaurant_code: Restaurant code
            
        Returns:
            Restaurant dictionary or None if not found
        """
        # Check cache first
        cache_key = f"{self.cache_prefix}_code_{restaurant_code}"
        cached_result = self.get_cached_result(cache_key)
        
        if cached_result:
            return cached_result
        
        # Fetch from database
        restaurants = await self.handle_async_operation(
            query_async,
            "restaurants",
            lambda r: r.get("code") == restaurant_code,
            error_message=f"Failed to fetch restaurant with code {restaurant_code}"
        )
        
        restaurant = restaurants[0] if restaurants else None
        
        if restaurant:
            # Cache the result
            self.cache_result(cache_key, restaurant, self.default_cache_ttl)
            logger.info(f"Retrieved restaurant by code: {restaurant.get('name', 'Unknown')}", "RestaurantController")
        
        return restaurant
    
    def generate_restaurant_code(self, name: str) -> str:
        """
        Generate a unique restaurant code based on the restaurant name.
        
        Args:
            name: Restaurant name
            
        Returns:
            Generated restaurant code
        """
        # Take first letters of each word, up to 3 characters
        words = name.upper().split()
        code_base = ''.join([word[0] for word in words[:3]])
        
        # Pad with random letters if needed
        while len(code_base) < 2:
            code_base += random.choice(string.ascii_uppercase)
        
        # Add random numbers
        code_suffix = ''.join([str(random.randint(0, 9)) for _ in range(3)])
        
        return f"{code_base}{code_suffix}"
    
    def generate_owner_pin(self) -> str:
        """
        Generate a 4-digit PIN for the owner.
        
        Returns:
            Generated 4-digit PIN
        """
        return ''.join([str(random.randint(0, 9)) for _ in range(4)])
    
    async def create_restaurant(self, restaurant_data: RestaurantCreate) -> Dict[str, Any]:
        """
        Create a new restaurant with validation and business logic.
        
        Args:
            restaurant_data: Restaurant creation data
            
        Returns:
            Created restaurant data
            
        Raises:
            HTTPException: If validation fails or creation errors occur
        """
        # Check if restaurant code already exists
        existing_code = await self.handle_async_operation(
            query_async,
            "restaurants",
            lambda r: r.get("code") == restaurant_data.code,
            error_message="Failed to check existing restaurant code"
        )
        
        if existing_code:
            raise self.handle_validation_error(
                "Restaurant code already exists",
                {"code": restaurant_data.code}
            )
        
        # Check if email already exists
        existing_email = await self.handle_async_operation(
            query_async,
            "restaurants",
            lambda r: r.get("email") == restaurant_data.email,
            error_message="Failed to check existing email"
        )
        
        if existing_email:
            raise self.handle_validation_error(
                "Email already registered",
                {"email": restaurant_data.email}
            )
        
        # Prepare restaurant data
        restaurant_dict = restaurant_data.model_dump()
        restaurant_dict["createdAt"] = datetime.now().isoformat()
        restaurant_dict["updatedAt"] = datetime.now().isoformat()
        
        # Create restaurant
        created_restaurant = await self.handle_async_operation(
            create_async,
            "restaurants",
            restaurant_dict,
            error_message="Failed to create restaurant"
        )
        
        # Invalidate relevant caches
        self.invalidate_cache_pattern(self.cache_prefix)
        
        logger.info(
            f"Created restaurant: {created_restaurant.get('name', 'Unknown')}",
            "RestaurantController",
            {"restaurant_id": created_restaurant.get("id")}
        )
        
        return created_restaurant
    
    async def update_restaurant(
        self, 
        restaurant_id: str, 
        restaurant_data: RestaurantUpdate
    ) -> Dict[str, Any]:
        """
        Update an existing restaurant.
        
        Args:
            restaurant_id: Restaurant ID
            restaurant_data: Restaurant update data
            
        Returns:
            Updated restaurant data
            
        Raises:
            HTTPException: If restaurant not found or update fails
        """
        # Check if restaurant exists
        existing_restaurant = await self.get_restaurant_by_id(restaurant_id)
        if not existing_restaurant:
            raise self.handle_not_found("Restaurant", restaurant_id)
        
        # Prepare update data
        update_dict = restaurant_data.model_dump(exclude_unset=True)
        update_dict["updatedAt"] = datetime.now().isoformat()
        
        # Update restaurant
        updated_restaurant = await self.handle_async_operation(
            update_async,
            "restaurants",
            restaurant_id,
            update_dict,
            error_message=f"Failed to update restaurant {restaurant_id}"
        )
        
        # Invalidate relevant caches
        self.invalidate_cache(f"{self.cache_prefix}_{restaurant_id}")
        self.invalidate_cache_pattern(f"{self.cache_prefix}_all")
        
        logger.info(
            f"Updated restaurant: {updated_restaurant.get('name', 'Unknown')}",
            "RestaurantController",
            {"restaurant_id": restaurant_id}
        )
        
        return updated_restaurant
    
    async def soft_delete_restaurant(self, restaurant_id: str) -> Dict[str, Any]:
        """
        Soft delete a restaurant (mark as inactive).
        
        Args:
            restaurant_id: Restaurant ID
            
        Returns:
            Success response
            
        Raises:
            HTTPException: If restaurant not found or has active dependencies
        """
        # Check if restaurant exists
        existing_restaurant = await self.get_restaurant_by_id(restaurant_id)
        if not existing_restaurant:
            raise self.handle_not_found("Restaurant", restaurant_id)
        
        # Check for active staff sessions
        active_users = await self.handle_async_operation(
            query_async,
            "restaurant_users",
            lambda u: (
                u.get("restaurant_id") == restaurant_id and
                u.get("status") == "active"
            ),
            error_message="Failed to check active users"
        )
        
        if active_users:
            raise self.handle_validation_error(
                f"Cannot delete restaurant with {len(active_users)} active staff members",
                {"active_users_count": len(active_users)}
            )
        
        # Perform soft delete
        update_data = {
            "isActive": False,
            "is_active": False,
            "deletedAt": datetime.now().isoformat(),
            "deleted_at": datetime.now().isoformat()
        }
        
        await self.handle_async_operation(
            update_async,
            "restaurants",
            restaurant_id,
            update_data,
            error_message=f"Failed to delete restaurant {restaurant_id}"
        )
        
        # Invalidate relevant caches
        self.invalidate_cache(f"{self.cache_prefix}_{restaurant_id}")
        self.invalidate_cache_pattern(f"{self.cache_prefix}_all")
        
        logger.info(
            f"Soft deleted restaurant: {existing_restaurant.get('name', 'Unknown')}",
            "RestaurantController",
            {"restaurant_id": restaurant_id}
        )
        
        return {
            "success": True,
            "message": "Restaurant deactivated successfully",
            "restaurant_id": restaurant_id,
            "restaurant_name": existing_restaurant.get("name")
        }

    async def restore_restaurant(self, restaurant_id: str) -> Dict[str, Any]:
        """
        Restore a soft-deleted restaurant (mark as active).

        Args:
            restaurant_id: Restaurant ID

        Returns:
            Success response

        Raises:
            HTTPException: If restaurant not found
        """
        # Check if restaurant exists
        existing_restaurant = await self.get_restaurant_by_id(restaurant_id)
        if not existing_restaurant:
            raise self.handle_not_found("Restaurant", restaurant_id)

        # Restore by marking as active
        update_data = {
            "isActive": True,
            "is_active": True,
            "restoredAt": datetime.now().isoformat(),
            "restored_at": datetime.now().isoformat()
        }

        await self.handle_async_operation(
            update_async,
            "restaurants",
            restaurant_id,
            update_data,
            error_message=f"Failed to restore restaurant {restaurant_id}"
        )

        # Invalidate relevant caches
        self.invalidate_cache(f"{self.cache_prefix}_{restaurant_id}")
        self.invalidate_cache_pattern(f"{self.cache_prefix}_all")

        logger.info(
            f"Restored restaurant: {existing_restaurant.get('name', 'Unknown')}",
            "RestaurantController",
            {"restaurant_id": restaurant_id}
        )

        return {
            "success": True,
            "message": "Restaurant restored successfully",
            "restaurant_id": restaurant_id,
            "restaurant_name": existing_restaurant.get("name")
        }

    async def authenticate_restaurant(self, code: str, password: str) -> Optional[Dict[str, Any]]:
        """
        Authenticate restaurant with code and password.

        Args:
            code: Restaurant code
            password: Restaurant password

        Returns:
            Restaurant data if authentication successful, None otherwise
        """
        # Get restaurant by code
        restaurant = await self.get_restaurant_by_code(code)

        if not restaurant:
            logger.warning(f"Restaurant not found for code: {code}", "RestaurantAuth")
            return None

        # Verify password (in production, use proper password hashing)
        if restaurant.get("password") != password:
            logger.warning(f"Invalid password for restaurant: {code}", "RestaurantAuth")
            return None

        # Check if restaurant is active
        if not restaurant.get("isActive", True):
            logger.warning(f"Inactive restaurant login attempt: {code}", "RestaurantAuth")
            return None

        logger.info(f"Successful restaurant authentication: {code}", "RestaurantAuth")
        return restaurant

    async def reset_restaurant_password(self, code: str, new_password: str) -> Dict[str, Any]:
        """
        Reset restaurant password.

        Args:
            code: Restaurant code
            new_password: New password

        Returns:
            Success response

        Raises:
            HTTPException: If restaurant not found
        """
        # Get restaurant by code
        restaurant = await self.get_restaurant_by_code(code)

        if not restaurant:
            raise self.handle_not_found("Restaurant", f"code: {code}")

        # Hash the new password (in production, use proper password hashing)
        from app.utils.auth import get_password_hash
        hashed_password = get_password_hash(new_password)

        # Update password
        update_data = {"password": hashed_password}
        await self.handle_async_operation(
            update_async,
            "restaurants",
            restaurant["id"],
            update_data,
            error_message=f"Failed to reset password for restaurant {code}"
        )

        # Invalidate cache
        self.invalidate_cache(f"{self.cache_prefix}_{restaurant['id']}")
        self.invalidate_cache(f"{self.cache_prefix}_code_{code}")

        logger.info(f"Password reset successful for restaurant: {code}", "RestaurantAuth")

        return {
            "success": True,
            "message": "Password reset successful",
            "restaurant_code": code,
            "restaurant_name": restaurant.get("name")
        }
