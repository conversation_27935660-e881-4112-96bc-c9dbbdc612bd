import logger from "@/utils/logger";
import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardT<PERSON>le, CardDescription } from "@/components/ui/card";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { toast } from "@/components/ui/sonner";
import { 
  Clock, 
  Calendar, 
  Bell, 
  UserCog, 
  Save, 
  RefreshCw, 
  Briefcase, 
  DollarSign,
  Users,
  ShieldCheck
} from "lucide-react";

// Define the settings interface
interface StaffSettings {
  scheduling: {
    defaultShiftDuration: number;
    minHoursPerWeek: number;
    maxHoursPerWeek: number;
    enableAutoScheduling: boolean;
    schedulingPeriod: "weekly" | "biweekly" | "monthly";
    notifyStaffOnScheduleChange: boolean;
  };
  notifications: {
    enableEmailNotifications: boolean;
    enableSmsNotifications: boolean;
    notifyForShiftReminders: boolean;
    notifyForScheduleChanges: boolean;
    notifyForTimeOffApprovals: boolean;
    reminderHoursBeforeShift: number;
  };
  permissions: {
    allowStaffToRequestTimeOff: boolean;
    allowStaffToSwapShifts: boolean;
    allowStaffToViewOtherSchedules: boolean;
    requireManagerApprovalForSwaps: boolean;
    allowStaffToClockInEarly: number; // minutes
    allowStaffToClockOutLate: number; // minutes
  };
  payroll: {
    payPeriod: "weekly" | "biweekly" | "monthly";
    overtimeThreshold: number;
    overtimeMultiplier: number;
    trackBreakTime: boolean;
    paidBreaks: boolean;
  };
}

const StaffSettings = () => {
  // Initialize component logging
  logger.setComponent("StaffSettings");
  logger.info("Component initialized", "StaffSettings");
  // Initialize settings with default values
  const [settings, setSettings] = useState<StaffSettings>({
    scheduling: {
      defaultShiftDuration: 8,
      minHoursPerWeek: 20,
      maxHoursPerWeek: 40,
      enableAutoScheduling: true,
      schedulingPeriod: "weekly",
      notifyStaffOnScheduleChange: true,
    },
    notifications: {
      enableEmailNotifications: true,
      enableSmsNotifications: false,
      notifyForShiftReminders: true,
      notifyForScheduleChanges: true,
      notifyForTimeOffApprovals: true,
      reminderHoursBeforeShift: 2,
    },
    permissions: {
      allowStaffToRequestTimeOff: true,
      allowStaffToSwapShifts: true,
      allowStaffToViewOtherSchedules: false,
      requireManagerApprovalForSwaps: true,
      allowStaffToClockInEarly: 15, // minutes
      allowStaffToClockOutLate: 15, // minutes
    },
    payroll: {
      payPeriod: "biweekly",
      overtimeThreshold: 40,
      overtimeMultiplier: 1.5,
      trackBreakTime: true,
      paidBreaks: false,
    },
  });

  // Handle saving settings
  const handleSaveSettings = () => {
    // In a real app, this would save to the database
    toast.success("Staff settings saved successfully");
    logger.dataOperation("save", "staff settings", "StaffSettings", settings);
  };

  // Handle reset to defaults
  const handleResetDefaults = () => {
    // Reset to default values (would be fetched from the server in a real app)
    setSettings({
      scheduling: {
        defaultShiftDuration: 8,
        minHoursPerWeek: 20,
        maxHoursPerWeek: 40,
        enableAutoScheduling: true,
        schedulingPeriod: "weekly",
        notifyStaffOnScheduleChange: true,
      },
      notifications: {
        enableEmailNotifications: true,
        enableSmsNotifications: false,
        notifyForShiftReminders: true,
        notifyForScheduleChanges: true,
        notifyForTimeOffApprovals: true,
        reminderHoursBeforeShift: 2,
      },
      permissions: {
        allowStaffToRequestTimeOff: true,
        allowStaffToSwapShifts: true,
        allowStaffToViewOtherSchedules: false,
        requireManagerApprovalForSwaps: true,
        allowStaffToClockInEarly: 15,
        allowStaffToClockOutLate: 15,
      },
      payroll: {
        payPeriod: "biweekly",
        overtimeThreshold: 40,
        overtimeMultiplier: 1.5,
        trackBreakTime: true,
        paidBreaks: false,
      },
    });
    toast.info("Settings reset to defaults");
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-xl font-semibold">Staff Settings</CardTitle>
        <CardDescription>
          Configure global settings for staff scheduling, notifications, and permissions
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="scheduling" className="space-y-4">
          <TabsList>
            <TabsTrigger value="scheduling">
              <Calendar className="h-4 w-4 mr-2" />
              Scheduling
            </TabsTrigger>
            <TabsTrigger value="notifications">
              <Bell className="h-4 w-4 mr-2" />
              Notifications
            </TabsTrigger>
            <TabsTrigger value="permissions">
              <ShieldCheck className="h-4 w-4 mr-2" />
              Permissions
            </TabsTrigger>
            <TabsTrigger value="payroll">
              <DollarSign className="h-4 w-4 mr-2" />
              Payroll
            </TabsTrigger>
          </TabsList>

          {/* Scheduling Settings */}
          <TabsContent value="scheduling" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="defaultShiftDuration">Default Shift Duration (hours)</Label>
                <Input
                  id="defaultShiftDuration"
                  type="number"
                  min="1"
                  max="24"
                  value={settings.scheduling.defaultShiftDuration}
                  onChange={(e) => setSettings({
                    ...settings,
                    scheduling: {
                      ...settings.scheduling,
                      defaultShiftDuration: parseInt(e.target.value) || 8
                    }
                  })}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="schedulingPeriod">Scheduling Period</Label>
                <Select
                  value={settings.scheduling.schedulingPeriod}
                  onValueChange={(value: "weekly" | "biweekly" | "monthly") => setSettings({
                    ...settings,
                    scheduling: {
                      ...settings.scheduling,
                      schedulingPeriod: value
                    }
                  })}
                >
                  <SelectTrigger id="schedulingPeriod">
                    <SelectValue placeholder="Select period" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="weekly">Weekly</SelectItem>
                    <SelectItem value="biweekly">Bi-weekly</SelectItem>
                    <SelectItem value="monthly">Monthly</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="minHoursPerWeek">Minimum Hours Per Week</Label>
                <Input
                  id="minHoursPerWeek"
                  type="number"
                  min="0"
                  max="168"
                  value={settings.scheduling.minHoursPerWeek}
                  onChange={(e) => setSettings({
                    ...settings,
                    scheduling: {
                      ...settings.scheduling,
                      minHoursPerWeek: parseInt(e.target.value) || 0
                    }
                  })}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="maxHoursPerWeek">Maximum Hours Per Week</Label>
                <Input
                  id="maxHoursPerWeek"
                  type="number"
                  min="0"
                  max="168"
                  value={settings.scheduling.maxHoursPerWeek}
                  onChange={(e) => setSettings({
                    ...settings,
                    scheduling: {
                      ...settings.scheduling,
                      maxHoursPerWeek: parseInt(e.target.value) || 40
                    }
                  })}
                />
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="enableAutoScheduling">Enable Auto Scheduling</Label>
                  <p className="text-sm text-muted-foreground">
                    Automatically generate schedules based on staff availability
                  </p>
                </div>
                <Switch
                  id="enableAutoScheduling"
                  checked={settings.scheduling.enableAutoScheduling}
                  onCheckedChange={(checked) => setSettings({
                    ...settings,
                    scheduling: {
                      ...settings.scheduling,
                      enableAutoScheduling: checked
                    }
                  })}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="notifyStaffOnScheduleChange">Notify Staff on Schedule Changes</Label>
                  <p className="text-sm text-muted-foreground">
                    Send notifications when staff schedules are changed
                  </p>
                </div>
                <Switch
                  id="notifyStaffOnScheduleChange"
                  checked={settings.scheduling.notifyStaffOnScheduleChange}
                  onCheckedChange={(checked) => setSettings({
                    ...settings,
                    scheduling: {
                      ...settings.scheduling,
                      notifyStaffOnScheduleChange: checked
                    }
                  })}
                />
              </div>
            </div>
          </TabsContent>

          {/* Notifications Settings */}
          <TabsContent value="notifications" className="space-y-4">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="enableEmailNotifications">Enable Email Notifications</Label>
                  <p className="text-sm text-muted-foreground">
                    Send notifications to staff via email
                  </p>
                </div>
                <Switch
                  id="enableEmailNotifications"
                  checked={settings.notifications.enableEmailNotifications}
                  onCheckedChange={(checked) => setSettings({
                    ...settings,
                    notifications: {
                      ...settings.notifications,
                      enableEmailNotifications: checked
                    }
                  })}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="enableSmsNotifications">Enable SMS Notifications</Label>
                  <p className="text-sm text-muted-foreground">
                    Send notifications to staff via SMS
                  </p>
                </div>
                <Switch
                  id="enableSmsNotifications"
                  checked={settings.notifications.enableSmsNotifications}
                  onCheckedChange={(checked) => setSettings({
                    ...settings,
                    notifications: {
                      ...settings.notifications,
                      enableSmsNotifications: checked
                    }
                  })}
                />
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <h3 className="text-sm font-medium">Notification Types</h3>
              
              <div className="flex items-center justify-between">
                <Label htmlFor="notifyForShiftReminders">Shift Reminders</Label>
                <Switch
                  id="notifyForShiftReminders"
                  checked={settings.notifications.notifyForShiftReminders}
                  onCheckedChange={(checked) => setSettings({
                    ...settings,
                    notifications: {
                      ...settings.notifications,
                      notifyForShiftReminders: checked
                    }
                  })}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="notifyForScheduleChanges">Schedule Changes</Label>
                <Switch
                  id="notifyForScheduleChanges"
                  checked={settings.notifications.notifyForScheduleChanges}
                  onCheckedChange={(checked) => setSettings({
                    ...settings,
                    notifications: {
                      ...settings.notifications,
                      notifyForScheduleChanges: checked
                    }
                  })}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="notifyForTimeOffApprovals">Time Off Approvals</Label>
                <Switch
                  id="notifyForTimeOffApprovals"
                  checked={settings.notifications.notifyForTimeOffApprovals}
                  onCheckedChange={(checked) => setSettings({
                    ...settings,
                    notifications: {
                      ...settings.notifications,
                      notifyForTimeOffApprovals: checked
                    }
                  })}
                />
              </div>
            </div>

            <Separator />

            <div className="space-y-2">
              <Label htmlFor="reminderHoursBeforeShift">Reminder Hours Before Shift</Label>
              <Input
                id="reminderHoursBeforeShift"
                type="number"
                min="1"
                max="48"
                value={settings.notifications.reminderHoursBeforeShift}
                onChange={(e) => setSettings({
                  ...settings,
                  notifications: {
                    ...settings.notifications,
                    reminderHoursBeforeShift: parseInt(e.target.value) || 2
                  }
                })}
              />
              <p className="text-xs text-muted-foreground">
                How many hours before a shift to send a reminder notification
              </p>
            </div>
          </TabsContent>

          {/* Permissions Settings */}
          <TabsContent value="permissions" className="space-y-4">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="allowStaffToRequestTimeOff">Allow Staff to Request Time Off</Label>
                  <p className="text-sm text-muted-foreground">
                    Staff can submit time off requests through the system
                  </p>
                </div>
                <Switch
                  id="allowStaffToRequestTimeOff"
                  checked={settings.permissions.allowStaffToRequestTimeOff}
                  onCheckedChange={(checked) => setSettings({
                    ...settings,
                    permissions: {
                      ...settings.permissions,
                      allowStaffToRequestTimeOff: checked
                    }
                  })}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="allowStaffToSwapShifts">Allow Staff to Swap Shifts</Label>
                  <p className="text-sm text-muted-foreground">
                    Staff can request to swap shifts with other staff members
                  </p>
                </div>
                <Switch
                  id="allowStaffToSwapShifts"
                  checked={settings.permissions.allowStaffToSwapShifts}
                  onCheckedChange={(checked) => setSettings({
                    ...settings,
                    permissions: {
                      ...settings.permissions,
                      allowStaffToSwapShifts: checked
                    }
                  })}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="allowStaffToViewOtherSchedules">Allow Staff to View Other Schedules</Label>
                  <p className="text-sm text-muted-foreground">
                    Staff can view the schedules of other staff members
                  </p>
                </div>
                <Switch
                  id="allowStaffToViewOtherSchedules"
                  checked={settings.permissions.allowStaffToViewOtherSchedules}
                  onCheckedChange={(checked) => setSettings({
                    ...settings,
                    permissions: {
                      ...settings.permissions,
                      allowStaffToViewOtherSchedules: checked
                    }
                  })}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="requireManagerApprovalForSwaps">Require Manager Approval for Swaps</Label>
                  <p className="text-sm text-muted-foreground">
                    Manager approval is required for shift swaps
                  </p>
                </div>
                <Switch
                  id="requireManagerApprovalForSwaps"
                  checked={settings.permissions.requireManagerApprovalForSwaps}
                  onCheckedChange={(checked) => setSettings({
                    ...settings,
                    permissions: {
                      ...settings.permissions,
                      requireManagerApprovalForSwaps: checked
                    }
                  })}
                />
              </div>
            </div>

            <Separator />

            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="allowStaffToClockInEarly">Allow Clock In Early (minutes)</Label>
                <Input
                  id="allowStaffToClockInEarly"
                  type="number"
                  min="0"
                  max="60"
                  value={settings.permissions.allowStaffToClockInEarly}
                  onChange={(e) => setSettings({
                    ...settings,
                    permissions: {
                      ...settings.permissions,
                      allowStaffToClockInEarly: parseInt(e.target.value) || 0
                    }
                  })}
                />
                <p className="text-xs text-muted-foreground">
                  How many minutes before a shift staff can clock in
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="allowStaffToClockOutLate">Allow Clock Out Late (minutes)</Label>
                <Input
                  id="allowStaffToClockOutLate"
                  type="number"
                  min="0"
                  max="60"
                  value={settings.permissions.allowStaffToClockOutLate}
                  onChange={(e) => setSettings({
                    ...settings,
                    permissions: {
                      ...settings.permissions,
                      allowStaffToClockOutLate: parseInt(e.target.value) || 0
                    }
                  })}
                />
                <p className="text-xs text-muted-foreground">
                  How many minutes after a shift staff can clock out
                </p>
              </div>
            </div>
          </TabsContent>

          {/* Payroll Settings */}
          <TabsContent value="payroll" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="payPeriod">Pay Period</Label>
                <Select
                  value={settings.payroll.payPeriod}
                  onValueChange={(value: "weekly" | "biweekly" | "monthly") => setSettings({
                    ...settings,
                    payroll: {
                      ...settings.payroll,
                      payPeriod: value
                    }
                  })}
                >
                  <SelectTrigger id="payPeriod">
                    <SelectValue placeholder="Select period" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="weekly">Weekly</SelectItem>
                    <SelectItem value="biweekly">Bi-weekly</SelectItem>
                    <SelectItem value="monthly">Monthly</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="overtimeThreshold">Overtime Threshold (hours)</Label>
                <Input
                  id="overtimeThreshold"
                  type="number"
                  min="0"
                  max="168"
                  value={settings.payroll.overtimeThreshold}
                  onChange={(e) => setSettings({
                    ...settings,
                    payroll: {
                      ...settings.payroll,
                      overtimeThreshold: parseInt(e.target.value) || 40
                    }
                  })}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="overtimeMultiplier">Overtime Multiplier</Label>
                <Input
                  id="overtimeMultiplier"
                  type="number"
                  min="1"
                  max="3"
                  step="0.1"
                  value={settings.payroll.overtimeMultiplier}
                  onChange={(e) => setSettings({
                    ...settings,
                    payroll: {
                      ...settings.payroll,
                      overtimeMultiplier: parseFloat(e.target.value) || 1.5
                    }
                  })}
                />
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="trackBreakTime">Track Break Time</Label>
                  <p className="text-sm text-muted-foreground">
                    Track and record staff break times
                  </p>
                </div>
                <Switch
                  id="trackBreakTime"
                  checked={settings.payroll.trackBreakTime}
                  onCheckedChange={(checked) => setSettings({
                    ...settings,
                    payroll: {
                      ...settings.payroll,
                      trackBreakTime: checked
                    }
                  })}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="paidBreaks">Paid Breaks</Label>
                  <p className="text-sm text-muted-foreground">
                    Staff are paid during their break time
                  </p>
                </div>
                <Switch
                  id="paidBreaks"
                  checked={settings.payroll.paidBreaks}
                  onCheckedChange={(checked) => setSettings({
                    ...settings,
                    payroll: {
                      ...settings.payroll,
                      paidBreaks: checked
                    }
                  })}
                />
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <div className="flex justify-between mt-6">
          <Button variant="outline" onClick={handleResetDefaults}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Reset to Defaults
          </Button>
          <Button onClick={handleSaveSettings}>
            <Save className="h-4 w-4 mr-2" />
            Save Settings
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default StaffSettings;
