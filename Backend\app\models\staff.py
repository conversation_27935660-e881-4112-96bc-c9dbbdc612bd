from pydantic import BaseModel, Field, EmailStr
from typing import Optional
from datetime import datetime
from enum import Enum

class StaffPosition(str, Enum):
    FRONT_OF_HOUSE = "front_of_house"
    BACK_OF_HOUSE = "back_of_house"
    MANAGEMENT = "management"

class StaffStatus(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    ON_LEAVE = "on_leave"

class StaffBase(BaseModel):
    name: str
    role: str
    position: StaffPosition
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    hourly_rate: Optional[float] = None

class StaffCreate(StaffBase):
    pass

class Staff(StaffBase):
    id: str
    status: StaffStatus = StaffStatus.ACTIVE
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
