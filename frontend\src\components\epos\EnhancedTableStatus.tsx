import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from '@/components/ui/sonner';
import { 
  Table as TableIcon, 
  Users, 
  MapPin, 
  Clock, 
  AlertTriangle, 
  CheckCircle,
  XCircle,
  Wrench,
  Calendar
} from 'lucide-react';

interface Table {
  id: string;
  number: number;
  capacity: number;
  location: 'indoor' | 'outdoor' | 'patio' | 'bar' | 'private_room';
  section?: string;
  shape?: string;
  status: 'available' | 'occupied' | 'reserved' | 'cleaning' | 'out_of_service';
  current_order_id?: string;
  reserved_until?: string;
  last_cleaned?: string;
  notes?: string;
}

interface EnhancedTableStatusProps {
  tables: Table[];
  onTableSelect: (tableId: string, tableNumber: number) => void;
  onTableStatusUpdate: (tableId: string, status: string, notes?: string) => void;
  selectedTableId?: string;
}

const EnhancedTableStatus: React.FC<EnhancedTableStatusProps> = ({
  tables,
  onTableSelect,
  onTableStatusUpdate,
  selectedTableId
}) => {
  const [isStatusDialogOpen, setIsStatusDialogOpen] = useState(false);
  const [selectedTable, setSelectedTable] = useState<Table | null>(null);
  const [newStatus, setNewStatus] = useState('');
  const [statusNotes, setStatusNotes] = useState('');
  const [reservationTime, setReservationTime] = useState('');
  const [customerName, setCustomerName] = useState('');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available':
        return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 border-green-200 dark:border-green-700';
      case 'occupied':
        return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200 border-red-200 dark:border-red-700';
      case 'reserved':
        return 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 border-blue-200 dark:border-blue-700';
      case 'cleaning':
        return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200 border-yellow-200 dark:border-yellow-700';
      case 'out_of_service':
        return 'bg-muted text-muted-foreground border-border';
      default:
        return 'bg-muted text-muted-foreground border-border';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'available':
        return <CheckCircle className="w-4 h-4" />;
      case 'occupied':
        return <Users className="w-4 h-4" />;
      case 'reserved':
        return <Calendar className="w-4 h-4" />;
      case 'cleaning':
        return <AlertTriangle className="w-4 h-4" />;
      case 'out_of_service':
        return <XCircle className="w-4 h-4" />;
      default:
        return <TableIcon className="w-4 h-4" />;
    }
  };

  const getLocationIcon = (location: string) => {
    switch (location) {
      case 'outdoor':
      case 'patio':
        return '🌿';
      case 'bar':
        return '🍺';
      case 'private_room':
        return '🏠';
      default:
        return '🏢';
    }
  };

  const handleTableClick = (table: Table) => {
    if (table.status === 'available') {
      onTableSelect(table.id, table.number);
    } else {
      setSelectedTable(table);
      setNewStatus(table.status);
      setStatusNotes(table.notes || '');
      setIsStatusDialogOpen(true);
    }
  };

  const handleStatusUpdate = async () => {
    if (!selectedTable) return;

    try {
      let updateData: any = { status: newStatus };
      
      if (statusNotes) {
        updateData.notes = statusNotes;
      }

      if (newStatus === 'reserved' && reservationTime && customerName) {
        const reservedUntil = new Date(reservationTime).toISOString();
        updateData.reserved_until = reservedUntil;
        updateData.notes = `Reserved for ${customerName}`;
      }

      await onTableStatusUpdate(selectedTable.id, newStatus, statusNotes);
      setIsStatusDialogOpen(false);
      setSelectedTable(null);
      setStatusNotes('');
      setReservationTime('');
      setCustomerName('');
      toast.success('Table status updated successfully');
    } catch (error) {
      toast.error('Failed to update table status');
    }
  };

  const formatTime = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const isReservationExpired = (reservedUntil?: string) => {
    if (!reservedUntil) return false;
    return new Date(reservedUntil) < new Date();
  };

  // Group tables by location
  const tablesByLocation = tables.reduce((acc, table) => {
    const location = table.location || 'indoor';
    if (!acc[location]) {
      acc[location] = [];
    }
    acc[location].push(table);
    return acc;
  }, {} as Record<string, Table[]>);

  return (
    <>
      <div className="space-y-6">
        {Object.entries(tablesByLocation).map(([location, locationTables]) => (
          <div key={location} className="space-y-3">
            <div className="flex items-center gap-2">
              <span className="text-lg">{getLocationIcon(location)}</span>
              <h3 className="text-lg font-semibold capitalize">
                {location.replace('_', ' ')} ({locationTables.length} tables)
              </h3>
            </div>
            
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3">
              {locationTables.map((table) => (
                <Card
                  key={table.id}
                  className={`cursor-pointer transition-all hover:shadow-md ${
                    selectedTableId === table.id ? 'ring-2 ring-blue-500' : ''
                  } ${table.status === 'available' ? 'hover:bg-green-50' : 'hover:bg-gray-50'}`}
                  onClick={() => handleTableClick(table)}
                >
                  <CardContent className="p-3">
                    <div className="text-center space-y-2">
                      <div className="flex items-center justify-center">
                        <TableIcon className="w-6 h-6 text-gray-600" />
                      </div>
                      
                      <div>
                        <p className="font-bold text-lg">Table {table.number}</p>
                        <div className="flex items-center justify-center gap-1 text-xs text-gray-500">
                          <Users className="w-3 h-3" />
                          <span>{table.capacity}</span>
                        </div>
                      </div>

                      <Badge className={`text-xs ${getStatusColor(table.status)}`}>
                        <div className="flex items-center gap-1">
                          {getStatusIcon(table.status)}
                          <span className="capitalize">{table.status.replace('_', ' ')}</span>
                        </div>
                      </Badge>

                      {table.section && (
                        <p className="text-xs text-gray-500">{table.section}</p>
                      )}

                      {table.status === 'reserved' && table.reserved_until && (
                        <div className="text-xs">
                          <p className={isReservationExpired(table.reserved_until) ? 'text-red-500' : 'text-blue-600'}>
                            Until {formatTime(table.reserved_until)}
                          </p>
                        </div>
                      )}

                      {table.status === 'cleaning' && table.last_cleaned && (
                        <div className="text-xs text-yellow-600">
                          <p>Cleaned: {formatTime(table.last_cleaned)}</p>
                        </div>
                      )}

                      {table.status === 'out_of_service' && table.notes && (
                        <div className="text-xs text-red-600">
                          <p>{table.notes}</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* Status Update Dialog */}
      <Dialog open={isStatusDialogOpen} onOpenChange={setIsStatusDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>
              Update Table {selectedTable?.number} Status
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Current Status</Label>
              <Badge className={getStatusColor(selectedTable?.status || '')}>
                {selectedTable?.status?.replace('_', ' ')}
              </Badge>
            </div>

            <div className="space-y-2">
              <Label htmlFor="new-status">New Status</Label>
              <Select value={newStatus} onValueChange={setNewStatus}>
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="available">Available</SelectItem>
                  <SelectItem value="occupied">Occupied</SelectItem>
                  <SelectItem value="reserved">Reserved</SelectItem>
                  <SelectItem value="cleaning">Cleaning</SelectItem>
                  <SelectItem value="out_of_service">Out of Service</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {newStatus === 'reserved' && (
              <>
                <div className="space-y-2">
                  <Label htmlFor="customer-name">Customer Name</Label>
                  <Input
                    id="customer-name"
                    value={customerName}
                    onChange={(e) => setCustomerName(e.target.value)}
                    placeholder="Enter customer name"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="reservation-time">Reserved Until</Label>
                  <Input
                    id="reservation-time"
                    type="datetime-local"
                    value={reservationTime}
                    onChange={(e) => setReservationTime(e.target.value)}
                  />
                </div>
              </>
            )}

            <div className="space-y-2">
              <Label htmlFor="status-notes">Notes (Optional)</Label>
              <Input
                id="status-notes"
                value={statusNotes}
                onChange={(e) => setStatusNotes(e.target.value)}
                placeholder="Add notes..."
              />
            </div>

            {selectedTable?.last_cleaned && (
              <div className="text-sm text-gray-500">
                <p>Last cleaned: {formatTime(selectedTable.last_cleaned)}</p>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsStatusDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleStatusUpdate}>
              Update Status
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default EnhancedTableStatus;
