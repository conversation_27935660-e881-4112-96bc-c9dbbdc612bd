"""
Split bill controller implementing business logic for split bill operations.
Handles bill splitting, payment processing, and transaction management.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
import uuid

from app.controllers.base import BaseController
from app.utils.storage_async import get_all_async, get_by_id_async, create_async, update_async, delete_async, query_async
from app.utils.logging_config import logger

class SplitBillController(BaseController):
    """Controller for split bill business logic and operations"""

    def __init__(self):
        super().__init__()
        self.cache_prefix = "split_bill"
        self.default_cache_ttl = 300  # 5 minutes for split bill data
        self.valid_payment_methods = ["cash", "card", "digital_wallet", "bank_transfer"]
        self.valid_split_types = ["equal", "custom", "by_item"]

    async def create_split_bill(
        self,
        order_id: str,
        split_details: List[Dict[str, Any]],
        split_type: str = "equal"
    ) -> Dict[str, Any]:
        """Create a split bill for an order with validation"""
        # Validate split type
        if split_type not in self.valid_split_types:
            raise self.handle_validation_error(
                f"Invalid split type: {split_type}",
                {"valid_types": self.valid_split_types}
            )

        # Get order details
        order = await self.handle_async_operation(
            get_by_id_async,
            "orders",
            order_id,
            error_message=f"Failed to fetch order {order_id}"
        )

        if not order:
            raise self.handle_not_found("Order", order_id)

        # Check if order already has a split bill
        existing_split_bills = await self.handle_async_operation(
            query_async,
            "split_bills",
            lambda s: s.get("order_id") == order_id,
            error_message="Failed to check existing split bills"
        )

        if existing_split_bills:
            raise self.handle_validation_error(
                "Order already has a split bill",
                {"order_id": order_id, "existing_split_bill_id": existing_split_bills[0].get("id")}
            )

        # Validate split details
        total_order_amount = order.get("total", 0)
        self._validate_split_details(split_details, total_order_amount, split_type)

        # Create split bill
        split_bill_data = {
            "order_id": order_id,
            "restaurant_id": order.get("restaurant_id"),
            "total_amount": total_order_amount,
            "split_type": split_type,
            "status": "pending",
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }

        created_split_bill = await self.handle_async_operation(
            create_async,
            "split_bills",
            split_bill_data,
            error_message="Failed to create split bill"
        )

        # Create split portions
        split_portions = []
        for i, detail in enumerate(split_details):
            portion_data = {
                "split_bill_id": created_split_bill["id"],
                "portion_number": i + 1,
                "amount": detail["amount"],
                "customer_name": detail.get("customer_name", f"Customer {i + 1}"),
                "customer_email": detail.get("customer_email"),
                "items": detail.get("items", []),
                "status": "pending",
                "created_at": datetime.now().isoformat()
            }

            created_portion = await self.handle_async_operation(
                create_async,
                "split_portions",
                portion_data,
                error_message="Failed to create split portion"
            )
            split_portions.append(created_portion)

        # Invalidate relevant caches
        self.invalidate_cache_pattern(self.cache_prefix)

        logger.info(
            f"Created split bill for order: {order_id}",
            "SplitBillController",
            {
                "split_bill_id": created_split_bill["id"],
                "portions_count": len(split_portions),
                "total_amount": total_order_amount
            }
        )

        return {
            "split_bill": created_split_bill,
            "portions": split_portions,
            "total_portions": len(split_portions)
        }

    def _validate_split_details(
        self,
        split_details: List[Dict[str, Any]],
        total_amount: float,
        split_type: str
    ) -> None:
        """Validate split details based on split type"""
        if not split_details:
            raise self.handle_validation_error(
                "Split details cannot be empty",
                {"split_type": split_type}
            )

        # Calculate total split amount
        total_split_amount = sum(detail.get("amount", 0) for detail in split_details)

        # Allow small rounding differences (1 penny)
        if abs(total_split_amount - total_amount) > 0.01:
            raise self.handle_validation_error(
                f"Split amounts ({total_split_amount:.2f}) do not match order total ({total_amount:.2f})",
                {
                    "total_split_amount": total_split_amount,
                    "order_total": total_amount,
                    "difference": abs(total_split_amount - total_amount)
                }
            )

        # Validate individual portions
        for i, detail in enumerate(split_details):
            if detail.get("amount", 0) <= 0:
                raise self.handle_validation_error(
                    f"Split portion {i + 1} must have a positive amount",
                    {"portion_number": i + 1, "amount": detail.get("amount")}
                )

    async def get_split_bills(
        self,
        order_id: Optional[str] = None,
        restaurant_id: Optional[str] = None,
        status: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Get split bills with filtering and caching"""
        # Validate pagination
        skip, limit = self.validate_pagination(skip, limit)

        # Create cache key based on filters
        filter_key = f"{order_id}_{restaurant_id}_{status}_{skip}_{limit}"
        cache_key = f"{self.cache_prefix}_filtered_{hash(filter_key)}"

        # Check cache first
        cached_result = self.get_cached_result(cache_key)
        if cached_result:
            return cached_result

        # Fetch split bills
        split_bills = await self.handle_async_operation(
            get_all_async,
            "split_bills",
            error_message="Failed to fetch split bills"
        )

        # Apply filters
        filtered_bills = self._apply_filters(split_bills, order_id, restaurant_id, status)

        # Apply pagination
        paginated_bills = filtered_bills[skip:skip + limit]

        # Enrich with split portions
        enriched_bills = []
        for bill in paginated_bills:
            portions = await self.handle_async_operation(
                query_async,
                "split_portions",
                lambda p: p.get("split_bill_id") == bill["id"],
                error_message="Failed to fetch split portions"
            )
            bill["portions"] = portions
            enriched_bills.append(bill)

        # Cache the result
        self.cache_result(cache_key, enriched_bills, self.default_cache_ttl)

        logger.info(
            f"Retrieved {len(enriched_bills)} split bills",
            "SplitBillController",
            {"total_filtered": len(filtered_bills), "restaurant_id": restaurant_id}
        )

        return enriched_bills

    def _apply_filters(
        self,
        split_bills: List[Dict[str, Any]],
        order_id: Optional[str] = None,
        restaurant_id: Optional[str] = None,
        status: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Apply filters to split bills"""
        filtered_bills = split_bills.copy()

        if order_id:
            filtered_bills = [
                bill for bill in filtered_bills
                if bill.get("order_id") == order_id
            ]

        if restaurant_id:
            filtered_bills = [
                bill for bill in filtered_bills
                if bill.get("restaurant_id") == restaurant_id
            ]

        if status:
            filtered_bills = [
                bill for bill in filtered_bills
                if bill.get("status") == status
            ]

        return filtered_bills

    async def get_split_bill_by_id(self, split_bill_id: str) -> Optional[Dict[str, Any]]:
        """Get split bill by ID with portions"""
        cache_key = f"{self.cache_prefix}_{split_bill_id}"
        cached_result = self.get_cached_result(cache_key)

        if cached_result:
            return cached_result

        # Get split bill
        split_bill = await self.handle_async_operation(
            get_by_id_async,
            "split_bills",
            split_bill_id,
            error_message=f"Failed to fetch split bill {split_bill_id}"
        )

        if not split_bill:
            return None

        # Get split portions
        portions = await self.handle_async_operation(
            query_async,
            "split_portions",
            lambda p: p.get("split_bill_id") == split_bill_id,
            error_message="Failed to fetch split portions"
        )

        split_bill["portions"] = portions

        # Cache the result
        self.cache_result(cache_key, split_bill, self.default_cache_ttl)

        logger.info(f"Retrieved split bill: {split_bill_id}", "SplitBillController")

        return split_bill

    async def process_payment(
        self,
        split_bill_id: str,
        portion_number: int,
        payment_details: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process payment for a specific portion of a split bill"""
        # Validate payment method
        payment_method = payment_details.get("payment_method")
        if payment_method not in self.valid_payment_methods:
            raise self.handle_validation_error(
                f"Invalid payment method: {payment_method}",
                {"valid_methods": self.valid_payment_methods}
            )

        # Get split bill
        split_bill = await self.get_split_bill_by_id(split_bill_id)
        if not split_bill:
            raise self.handle_not_found("Split bill", split_bill_id)

        # Find the specific portion
        portion = None
        for p in split_bill.get("portions", []):
            if p.get("portion_number") == portion_number:
                portion = p
                break

        if not portion:
            raise self.handle_not_found("Split portion", f"{split_bill_id}:{portion_number}")

        # Check if portion is already paid
        if portion.get("status") == "paid":
            raise self.handle_validation_error(
                "Portion is already paid",
                {"portion_number": portion_number, "status": portion.get("status")}
            )

        # Validate payment amount
        expected_amount = portion.get("amount", 0)
        paid_amount = payment_details.get("amount", 0)

        if abs(paid_amount - expected_amount) > 0.01:  # Allow 1 penny difference
            raise self.handle_validation_error(
                f"Payment amount ({paid_amount:.2f}) does not match portion amount ({expected_amount:.2f})",
                {
                    "expected_amount": expected_amount,
                    "paid_amount": paid_amount,
                    "difference": abs(paid_amount - expected_amount)
                }
            )

        # Create payment record
        payment_record = {
            "split_bill_id": split_bill_id,
            "portion_number": portion_number,
            "amount": paid_amount,
            "payment_method": payment_method,
            "transaction_id": payment_details.get("transaction_id"),
            "customer_name": portion.get("customer_name"),
            "customer_email": portion.get("customer_email"),
            "processed_at": datetime.now().isoformat(),
            "status": "completed"
        }

        created_payment = await self.handle_async_operation(
            create_async,
            "split_payments",
            payment_record,
            error_message="Failed to create payment record"
        )

        # Update portion status
        await self.handle_async_operation(
            update_async,
            "split_portions",
            portion["id"],
            {
                "status": "paid",
                "paid_at": datetime.now().isoformat(),
                "payment_id": created_payment["id"]
            },
            error_message="Failed to update portion status"
        )

        # Check if all portions are paid and update split bill status
        await self._check_and_update_split_bill_status(split_bill_id)

        # Invalidate relevant caches
        self.invalidate_cache(f"{self.cache_prefix}_{split_bill_id}")
        self.invalidate_cache_pattern(f"{self.cache_prefix}_filtered")

        logger.info(
            f"Processed payment for split bill portion: {split_bill_id}:{portion_number}",
            "SplitBillController",
            {
                "amount": paid_amount,
                "payment_method": payment_method,
                "customer": portion.get("customer_name")
            }
        )

        return {
            "success": True,
            "payment": created_payment,
            "portion_status": "paid",
            "message": f"Payment processed successfully for portion {portion_number}"
        }

    async def _check_and_update_split_bill_status(self, split_bill_id: str) -> None:
        """Check if all portions are paid and update split bill status"""
        split_bill = await self.get_split_bill_by_id(split_bill_id)
        if not split_bill:
            return

        portions = split_bill.get("portions", [])
        if not portions:
            return

        # Check if all portions are paid
        all_paid = all(portion.get("status") == "paid" for portion in portions)

        if all_paid and split_bill.get("status") != "completed":
            # Update split bill status to completed
            await self.handle_async_operation(
                update_async,
                "split_bills",
                split_bill_id,
                {
                    "status": "completed",
                    "completed_at": datetime.now().isoformat()
                },
                error_message="Failed to update split bill status"
            )

            # Update order status to paid
            order_id = split_bill.get("order_id")
            if order_id:
                await self.handle_async_operation(
                    update_async,
                    "orders",
                    order_id,
                    {
                        "payment_status": "paid",
                        "paid_at": datetime.now().isoformat()
                    },
                    error_message="Failed to update order payment status"
                )

            logger.info(
                f"Split bill completed: {split_bill_id}",
                "SplitBillController",
                {"order_id": order_id}
            )

    async def get_payment_status(self, split_bill_id: str) -> Dict[str, Any]:
        """Get payment status for a split bill"""
        split_bill = await self.get_split_bill_by_id(split_bill_id)
        if not split_bill:
            raise self.handle_not_found("Split bill", split_bill_id)

        portions = split_bill.get("portions", [])
        total_portions = len(portions)
        paid_portions = len([p for p in portions if p.get("status") == "paid"])
        pending_portions = total_portions - paid_portions

        total_amount = split_bill.get("total_amount", 0)
        paid_amount = sum(
            p.get("amount", 0) for p in portions if p.get("status") == "paid"
        )
        remaining_amount = total_amount - paid_amount

        return {
            "split_bill_id": split_bill_id,
            "status": split_bill.get("status"),
            "total_portions": total_portions,
            "paid_portions": paid_portions,
            "pending_portions": pending_portions,
            "total_amount": total_amount,
            "paid_amount": round(paid_amount, 2),
            "remaining_amount": round(remaining_amount, 2),
            "completion_percentage": round((paid_portions / total_portions) * 100, 2) if total_portions > 0 else 0,
            "portions": [
                {
                    "portion_number": p.get("portion_number"),
                    "customer_name": p.get("customer_name"),
                    "amount": p.get("amount"),
                    "status": p.get("status"),
                    "paid_at": p.get("paid_at")
                }
                for p in portions
            ]
        }

    async def cancel_split_bill(self, split_bill_id: str, reason: Optional[str] = None) -> Dict[str, Any]:
        """Cancel a split bill if no payments have been made"""
        split_bill = await self.get_split_bill_by_id(split_bill_id)
        if not split_bill:
            raise self.handle_not_found("Split bill", split_bill_id)

        # Check if any payments have been made
        portions = split_bill.get("portions", [])
        paid_portions = [p for p in portions if p.get("status") == "paid"]

        if paid_portions:
            raise self.handle_validation_error(
                f"Cannot cancel split bill with {len(paid_portions)} paid portions",
                {"paid_portions": len(paid_portions)}
            )

        # Update split bill status
        await self.handle_async_operation(
            update_async,
            "split_bills",
            split_bill_id,
            {
                "status": "cancelled",
                "cancelled_at": datetime.now().isoformat(),
                "cancellation_reason": reason
            },
            error_message="Failed to cancel split bill"
        )

        # Update all portions to cancelled
        for portion in portions:
            await self.handle_async_operation(
                update_async,
                "split_portions",
                portion["id"],
                {
                    "status": "cancelled",
                    "cancelled_at": datetime.now().isoformat()
                },
                error_message="Failed to cancel split portion"
            )

        # Invalidate relevant caches
        self.invalidate_cache(f"{self.cache_prefix}_{split_bill_id}")
        self.invalidate_cache_pattern(f"{self.cache_prefix}_filtered")

        logger.info(
            f"Cancelled split bill: {split_bill_id}",
            "SplitBillController",
            {"reason": reason}
        )

        return {
            "success": True,
            "message": "Split bill cancelled successfully",
            "split_bill_id": split_bill_id,
            "reason": reason
        }

    async def get_split_bill_analytics(self, restaurant_id: str) -> Dict[str, Any]:
        """Get split bill analytics for restaurant"""
        cache_key = f"{self.cache_prefix}_analytics_{restaurant_id}"
        cached_result = self.get_cached_result(cache_key)

        if cached_result:
            return cached_result

        # Get split bills for restaurant
        split_bills = await self.get_split_bills(restaurant_id=restaurant_id, limit=1000)

        # Calculate analytics
        total_split_bills = len(split_bills)
        completed_bills = len([b for b in split_bills if b.get("status") == "completed"])
        pending_bills = len([b for b in split_bills if b.get("status") == "pending"])
        cancelled_bills = len([b for b in split_bills if b.get("status") == "cancelled"])

        total_amount = sum(bill.get("total_amount", 0) for bill in split_bills)
        completed_amount = sum(
            bill.get("total_amount", 0) for bill in split_bills
            if bill.get("status") == "completed"
        )

        # Calculate average portions per bill
        total_portions = sum(len(bill.get("portions", [])) for bill in split_bills)
        avg_portions = total_portions / total_split_bills if total_split_bills > 0 else 0

        analytics_data = {
            "restaurant_id": restaurant_id,
            "total_split_bills": total_split_bills,
            "completed_bills": completed_bills,
            "pending_bills": pending_bills,
            "cancelled_bills": cancelled_bills,
            "completion_rate": round((completed_bills / total_split_bills) * 100, 2) if total_split_bills > 0 else 0,
            "total_amount": round(total_amount, 2),
            "completed_amount": round(completed_amount, 2),
            "average_portions_per_bill": round(avg_portions, 2),
            "generated_at": datetime.now().isoformat()
        }

        # Cache the result
        self.cache_result(cache_key, analytics_data, 900)  # 15 minutes cache for analytics

        return analytics_data
