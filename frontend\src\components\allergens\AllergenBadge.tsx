import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { AlertTriangle } from "lucide-react";

interface AllergenBadgeProps {
  allergen: string;
  variant?: "default" | "outline" | "secondary" | "destructive";
  size?: "sm" | "default";
  showIcon?: boolean;
  className?: string;
}

// Color mapping for different allergens - Dark theme compatible
const allergenColors: Record<string, string> = {
  nuts: "bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-200 border-orange-200 dark:border-orange-700",
  dairy: "bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 border-blue-200 dark:border-blue-700",
  gluten: "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200 border-yellow-200 dark:border-yellow-700",
  shellfish: "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200 border-red-200 dark:border-red-700",
  eggs: "bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-200 border-amber-200 dark:border-amber-700",
  soy: "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 border-green-200 dark:border-green-700",
  fish: "bg-cyan-100 dark:bg-cyan-900/30 text-cyan-800 dark:text-cyan-200 border-cyan-200 dark:border-cyan-700",
  sesame: "bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-200 border-purple-200 dark:border-purple-700"
};

export const AllergenBadge = ({
  allergen,
  variant = "outline",
  size = "default",
  showIcon = false,
  className
}: AllergenBadgeProps) => {
  const colorClass = allergenColors[allergen.toLowerCase()] || "bg-gray-100 text-gray-800 border-gray-200";
  
  return (
    <Badge
      variant={variant}
      className={cn(
        "capitalize font-medium",
        size === "sm" && "text-xs px-1.5 py-0.5",
        variant === "outline" && colorClass,
        className
      )}
    >
      {showIcon && <AlertTriangle className="w-3 h-3 mr-1" />}
      {allergen}
    </Badge>
  );
};

interface AllergenBadgeListProps {
  allergens: string[];
  variant?: "default" | "outline" | "secondary" | "destructive";
  size?: "sm" | "default";
  showIcon?: boolean;
  maxDisplay?: number;
  className?: string;
}

export const AllergenBadgeList = ({
  allergens,
  variant = "outline",
  size = "default", 
  showIcon = false,
  maxDisplay,
  className
}: AllergenBadgeListProps) => {
  if (!allergens || allergens.length === 0) {
    return null;
  }

  const displayAllergens = maxDisplay ? allergens.slice(0, maxDisplay) : allergens;
  const remainingCount = maxDisplay && allergens.length > maxDisplay 
    ? allergens.length - maxDisplay 
    : 0;

  return (
    <div className={cn("flex flex-wrap gap-1", className)}>
      {displayAllergens.map((allergen) => (
        <AllergenBadge
          key={allergen}
          allergen={allergen}
          variant={variant}
          size={size}
          showIcon={showIcon}
        />
      ))}
      {remainingCount > 0 && (
        <Badge
          variant={variant}
          className={cn(
            "font-medium",
            size === "sm" && "text-xs px-1.5 py-0.5"
          )}
        >
          +{remainingCount} more
        </Badge>
      )}
    </div>
  );
};
