from pydantic import BaseModel, EmailStr
from typing import Optional, List, Dict, Any
from datetime import datetime

class RestaurantBase(BaseModel):
    name: str
    code: str
    logo: Optional[str] = None
    address: str
    phone: str
    email: EmailStr
    vatRate: float = 20.0
    currency: str = "GBP"
    isActive: bool = True
    ownerName: str
    businessLicenseNumber: str
    restaurantType: str = "restaurant"
    hasData: bool = False

class RestaurantCreate(RestaurantBase):
    password: str
    setupData: Optional[Dict[str, Any]] = None

class Restaurant(RestaurantBase):
    id: str
    createdAt: datetime
    updatedAt: datetime
    setupData: Optional[Dict[str, Any]] = None

    class Config:
        from_attributes = True

class RestaurantUpdate(BaseModel):
    name: Optional[str] = None
    logo: Optional[str] = None
    address: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[EmailStr] = None
    vatRate: Optional[float] = None
    currency: Optional[str] = None
    isActive: Optional[bool] = None
    ownerName: Optional[str] = None
    businessLicenseNumber: Optional[str] = None
    restaurantType: Optional[str] = None
    setupData: Optional[Dict[str, Any]] = None
    hasData: Optional[bool] = None

class RestaurantResponse(BaseModel):
    success: bool
    message: str
    restaurant: Optional[Restaurant] = None
    credentials: Optional[Dict[str, str]] = None

# Restaurant User Models
class RestaurantUserBase(BaseModel):
    name: str
    email: EmailStr
    phone: str
    restaurant_id: str
    restaurant_name: str
    role: str
    position: str
    pin: str
    status: str = "active"
    hireDate: str
    performance: int = 100
    accessLevel: str = "limited"

class RestaurantUserCreate(RestaurantUserBase):
    pass

class RestaurantUser(RestaurantUserBase):
    id: str

    class Config:
        from_attributes = True

class RestaurantUserUpdate(BaseModel):
    name: Optional[str] = None
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    role: Optional[str] = None
    position: Optional[str] = None
    pin: Optional[str] = None
    status: Optional[str] = None
    performance: Optional[int] = None
    accessLevel: Optional[str] = None

class RestaurantUserResponse(BaseModel):
    success: bool
    message: str
    user: Optional[RestaurantUser] = None

# Combined Registration Models
class RegistrationData(BaseModel):
    restaurantName: str
    ownerName: str
    email: EmailStr
    phone: str
    password: str
    confirmPassword: str
    address: Dict[str, str]
    businessLicenseNumber: str
    restaurantType: str
    agreeToTerms: bool

class SetupData(BaseModel):
    useDefaultLogo: bool = True
    logoPosition: str = "center"
    logoUrl: Optional[str] = None
    totalTables: int
    tables: List[Dict[str, Any]]
    defaultTableCapacity: int
    tableNamingSystem: str
    operatingHours: List[Dict[str, Any]]
    cuisineTypes: List[str]
    priceRange: Dict[str, Any]
    averageServiceTime: int
    totalSeatingCapacity: int
    estimatedDailyCovers: int

class CompleteRegistrationRequest(BaseModel):
    registrationData: RegistrationData
    setupData: SetupData
    credentials: Dict[str, str]

class CompleteRegistrationResponse(BaseModel):
    success: bool
    message: str
    restaurant: Optional[Restaurant] = None
    user: Optional[RestaurantUser] = None
    credentials: Optional[Dict[str, str]] = None
