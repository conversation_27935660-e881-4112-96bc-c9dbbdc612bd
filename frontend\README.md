# Promith

A comprehensive restaurant management system designed to streamline operations, manage inventory, handle staff scheduling, and provide detailed analytics.

## Features

- **EPOS System**: Point of sale system with card payment integration
- **Inventory Management**: Track stock levels, waste, and deliveries
- **Staff Management**: Schedule shifts, track hours, and manage tasks
- **Reporting**: Comprehensive analytics and financial reporting
- **Customer Management**: Track customer preferences and feedback

## Getting Started

1. Clone the repository:
```bash
git clone https://github.com/prathamsurti/Promith.git
cd Promith
```

2. Install dependencies:
```bash
npm install
```

3. Run the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

## Project Structure

- `/Backend` - Server-side code and API endpoints
- `/src` - Frontend React application
- `/tools` - Utility scripts and tools
- `/uploads` - User uploaded files
- `/restro-ai-pilot-system` - Core system components

## Technologies Used

This project is built with:

- Node.js
- React
- TypeScript
- Tailwind CSS
- shadcn-ui
- SQLite (for development)
- PostgreSQL (for production)

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
