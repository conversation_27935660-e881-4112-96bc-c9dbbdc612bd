import { useState } from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  Card<PERSON><PERSON>er,
  CardTitle,
  CardDescription
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  <PERSON><PERSON>hart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer, 
  Legend,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import { ForecastData } from "./ForecastCard";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { 
  Download,
  Info,
  ShoppingCart,
  AlertTriangle,
  Check
} from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface IngredientForecastCardProps {
  data?: ForecastData[];
  title?: string;
}

// Custom tooltip component
const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-background border rounded p-2 shadow-sm">
        <p className="font-medium">{label}</p>
        {payload.map((entry: any, index: number) => (
          <p key={`item-${index}`} style={{ color: entry.color }}>
            {entry.name}: {entry.value.toLocaleString()} {entry.unit || ''}
          </p>
        ))}
      </div>
    );
  }

  return null;
};

// Custom tooltip for pie chart
const PieTooltip = ({ active, payload }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-background border rounded p-2 shadow-sm">
        <p className="font-medium">{payload[0].name}</p>
        <p style={{ color: payload[0].color }}>
          {payload[0].value.toLocaleString()} {payload[0].payload.unit}
        </p>
        <p className="text-xs text-muted-foreground">
          £{payload[0].payload.cost.toFixed(2)} estimated cost
        </p>
      </div>
    );
  }

  return null;
};

// Colors for pie chart
const COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#ec4899', '#6366f1', '#14b8a6', '#f43f5e', '#84cc16'];

const IngredientForecastCard = ({ data = [], title = "Ingredient Usage Forecast" }: IngredientForecastCardProps) => {
  const [timeframe, setTimeframe] = useState<"week" | "month">("week");
  
  // Get all ingredients from all days
  const allIngredients = data.flatMap(day => day.ingredientUsage || []);
  
  // Aggregate ingredients by name
  const aggregatedIngredients = allIngredients.reduce((acc, ingredient) => {
    const existing = acc.find(i => i.name === ingredient.name);
    if (existing) {
      existing.quantity += ingredient.quantity;
      existing.cost += ingredient.cost;
    } else {
      acc.push({ ...ingredient });
    }
    return acc;
  }, [] as { name: string; quantity: number; unit: string; cost: number }[]);
  
  // Sort by quantity
  const sortedIngredients = [...aggregatedIngredients].sort((a, b) => b.quantity - a.quantity);
  
  // Top ingredients for pie chart (top 5)
  const topIngredients = sortedIngredients.slice(0, 5);
  
  // Calculate total cost
  const totalCost = sortedIngredients.reduce((sum, item) => sum + item.cost, 0);
  
  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  // Check if any ingredients are low in stock (mock function)
  const getLowStockIngredients = () => {
    return sortedIngredients.filter((_, index) => index % 3 === 0); // Just for demo purposes
  };

  const lowStockIngredients = getLowStockIngredients();

  return (
    <Card className="col-span-full">
      <CardHeader>
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2">
          <div className="flex items-center gap-2">
            <div>
              <CardTitle>{title}</CardTitle>
              <CardDescription>
                Optimize inventory with AI-powered ingredient predictions
              </CardDescription>
            </div>
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <Info className="h-4 w-4" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80">
                <div className="space-y-2">
                  <h4 className="font-medium">About Ingredient Forecasting</h4>
                  <p className="text-sm text-muted-foreground">
                    These predictions are generated by analyzing historical menu item sales and their ingredient requirements.
                    The system considers seasonal variations and special events to optimize inventory management.
                  </p>
                </div>
              </PopoverContent>
            </Popover>
          </div>
          <div className="flex items-center gap-2">
            <Select
              value={timeframe}
              onValueChange={(value) => setTimeframe(value as "week" | "month")}
            >
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder="Timeframe" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="week">This Week</SelectItem>
                <SelectItem value="month">This Month</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" size="icon">
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="overview">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="details">Ingredient Details</TabsTrigger>
            <TabsTrigger value="ordering">Ordering Guide</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm font-medium">Total Ingredients</div>
                      <div className="text-2xl font-bold mt-1">{sortedIngredients.length}</div>
                    </div>
                    <ShoppingCart className="h-8 w-8 text-blue-500" />
                  </div>
                  <div className="text-sm text-muted-foreground mt-2">
                    Needed for the {timeframe === "week" ? "next 7 days" : "next 30 days"}
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm font-medium">Estimated Cost</div>
                      <div className="text-2xl font-bold mt-1">{formatCurrency(totalCost)}</div>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {timeframe === "week" ? "Weekly" : "Monthly"}
                    </Badge>
                  </div>
                  <div className="text-sm text-muted-foreground mt-2">
                    Based on current market prices
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm font-medium">Low Stock Alert</div>
                      <div className="text-2xl font-bold mt-1">{lowStockIngredients.length}</div>
                    </div>
                    <AlertTriangle className="h-8 w-8 text-amber-500" />
                  </div>
                  <div className="text-sm text-muted-foreground mt-2">
                    Ingredients that need reordering
                  </div>
                </CardContent>
              </Card>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              <div className="h-[300px]">
                <h3 className="text-sm font-medium mb-2">Top Ingredients by Usage</h3>
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={sortedIngredients.slice(0, 8)}
                    margin={{ top: 10, right: 10, left: 10, bottom: 20 }}
                    layout="vertical"
                  >
                    <CartesianGrid strokeDasharray="3 3" horizontal={true} vertical={false} />
                    <XAxis type="number" />
                    <YAxis 
                      dataKey="name" 
                      type="category" 
                      tick={{ fontSize: 12 }}
                      width={100}
                    />
                    <Tooltip content={<CustomTooltip />} />
                    <Bar 
                      dataKey="quantity" 
                      name="Quantity" 
                      fill="#3b82f6" 
                      radius={[0, 4, 4, 0]}
                    />
                  </BarChart>
                </ResponsiveContainer>
              </div>
              
              <div className="h-[300px]">
                <h3 className="text-sm font-medium mb-2">Cost Distribution</h3>
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={topIngredients}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="cost"
                      nameKey="name"
                    >
                      {topIngredients.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip content={<PieTooltip />} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="details" className="space-y-4">
            <div className="rounded-md border mt-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Ingredient</TableHead>
                    <TableHead>Quantity</TableHead>
                    <TableHead>Unit</TableHead>
                    <TableHead>Est. Cost</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sortedIngredients.map((ingredient, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">{ingredient.name}</TableCell>
                      <TableCell>{ingredient.quantity.toLocaleString()}</TableCell>
                      <TableCell>{ingredient.unit}</TableCell>
                      <TableCell>£{ingredient.cost.toFixed(2)}</TableCell>
                      <TableCell>
                        {lowStockIngredients.some(i => i.name === ingredient.name) ? (
                          <Badge variant="destructive" className="text-xs bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                            Low Stock
                          </Badge>
                        ) : (
                          <Badge variant="outline" className="text-xs bg-green-50 text-green-800 dark:bg-green-900 dark:text-green-200">
                            In Stock
                          </Badge>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </TabsContent>
          
          <TabsContent value="ordering" className="space-y-4">
            <div className="rounded-md border mt-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Ingredient</TableHead>
                    <TableHead>Order Quantity</TableHead>
                    <TableHead>Unit</TableHead>
                    <TableHead>Priority</TableHead>
                    <TableHead>Action</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {lowStockIngredients.map((ingredient, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">{ingredient.name}</TableCell>
                      <TableCell>{Math.ceil(ingredient.quantity * 1.2).toLocaleString()}</TableCell>
                      <TableCell>{ingredient.unit}</TableCell>
                      <TableCell>
                        <Badge 
                          variant={index < 2 ? "destructive" : index < 4 ? "default" : "outline"} 
                          className="text-xs"
                        >
                          {index < 2 ? "High" : index < 4 ? "Medium" : "Low"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Button variant="outline" size="sm" className="h-8 text-xs">
                          <Check className="h-3 w-3 mr-1" /> Add to Order
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default IngredientForecastCard;
