import logger from "@/utils/logger";

import { ReactNode } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth, UserRole } from '@/contexts/AuthContext';
import InitializingLoader from '@/components/auth/InitializingLoader';

interface ProtectedRouteProps {
  children: ReactNode;
  allowedRoles?: string[];
  requiresFullAccess?: boolean;
}

const ProtectedRoute = ({
  children,
  allowedRoles = ['admin', 'staff'],
  requiresFullAccess = false
}: ProtectedRouteProps) => {
  // Initialize component logging
  logger.setComponent("ProtectedRoute");
  logger.info("Component initialized", "ProtectedRoute");

  const { isAuthenticated, user, isRestaurantAuthenticated, currentRestaurant, isLoading } = useAuth();
  const location = useLocation();

  // If still loading authentication state, show loading screen
  if (isLoading) {
    logger.debug('Authentication still loading, showing loading screen', 'ProtectedRoute');
    return <InitializingLoader message="Verifying Authentication..." />;
  }

  // Check restaurant authentication first
  if (!isRestaurantAuthenticated || !currentRestaurant) {
    logger.warn("No restaurant authenticated, redirecting to restaurant login", "ProtectedRoute", {
      path: location.pathname,
      isRestaurantAuthenticated,
      hasCurrentRestaurant: !!currentRestaurant
    });
    return <Navigate to="/" replace />;
  }

  // Check user authentication
  if (!isAuthenticated || !user) {
    logger.warn("No user authenticated, redirecting to staff login", "ProtectedRoute", {
      path: location.pathname,
      isAuthenticated,
      hasUser: !!user
    });
    return <Navigate to="/staff-login" replace />;
  }

  logger.info("Authentication bypassed for development with valid user", "ProtectedRoute", {
    user: user.name,
    role: user.role,
    restaurant: currentRestaurant.name
  });
  return <>{children}</>;

  /* COMMENTED OUT FOR DEVELOPMENT - UNCOMMENT TO RE-ENABLE AUTHENTICATION
  const { isAuthenticated, user, isRestaurantAuthenticated, currentRestaurant } = useAuth();
  const location = useLocation();

  logger.debug('Checking access for path', 'ProtectedRoute', { path: location.pathname });
  logger.debug('Auth state check', 'ProtectedRoute', {
    isAuthenticated,
    isRestaurantAuthenticated,
    user: user?.name,
    currentRestaurant: currentRestaurant?.name
  });
  logger.debug('Required access check', 'ProtectedRoute', { allowedRoles, requiresFullAccess });

  // If restaurant is not authenticated, redirect to restaurant login
  if (!isRestaurantAuthenticated) {
    logger.authentication('restaurant access', 'failure', 'ProtectedRoute', { reason: 'not authenticated' });
    return <Navigate to="/" state={{ from: location }} replace />;
  }

  // If not authenticated, redirect to staff login or PIN page based on the route
  if (!isAuthenticated || !user) {
    logger.authentication('user access', 'failure', 'ProtectedRoute', { reason: 'not authenticated' });

    // Special case for EPOS page - redirect to PIN page
    if (location.pathname === '/admin/epos') {
      logger.userAction('EPOS page access redirect', 'ProtectedRoute');
      return <Navigate to="/staff-pin" state={{ from: location }} replace />;
    }

    logger.userAction('redirect to staff login', 'ProtectedRoute');
    return <Navigate to="/staff-login" state={{ from: location }} replace />;
  }

  // If authenticated but role not allowed, redirect to appropriate dashboard
  const userRole = user.role || 'staff';

  // More flexible role checking - check if any of the allowed roles matches the user role
  // or if the allowed roles include a more general category
  const roleAllowed = allowedRoles.some(allowedRole => {
    const lowerAllowedRole = allowedRole.toLowerCase().trim();
    const lowerUserRole = userRole.toLowerCase().trim();

    // Direct match
    if (lowerAllowedRole === lowerUserRole) {
      return true;
    }

    // Check if 'staff' is allowed and user has a staff-like role
    if (lowerAllowedRole === 'staff' &&
        ['waiter', 'chef', 'hostess', 'bartender', 'staff'].includes(lowerUserRole)) {
      return true;
    }

    // Check if 'admin' is allowed and user has an admin-like role
    if (lowerAllowedRole === 'admin' &&
        ['admin', 'manager'].includes(lowerUserRole)) {
      return true;
    }

    return false;
  });

  logger.debug('Enhanced role check', 'ProtectedRoute', {
    userRole,
    allowedRoles,
    roleAllowed,
    path: location.pathname
  });

  if (!roleAllowed) {
    logger.authentication('role access', 'failure', 'ProtectedRoute', { userRole, allowedRoles });

    // Avoid circular redirects by checking the current path
    const currentPath = location.pathname;
    logger.debug('Current path check', 'ProtectedRoute', { currentPath });

    // If already on a dashboard, don't redirect to avoid loops
    if (currentPath === '/admin' || currentPath === '/staff-dashboard') {
      logger.debug('Dashboard loop prevention', 'ProtectedRoute');
      return <>{children}</>;
    }

    // Redirect based on role - case insensitive check
    const lowerUserRole = userRole.toLowerCase();
    if (lowerUserRole === 'admin' || lowerUserRole === 'manager') {
      logger.userAction('redirect admin/manager to dashboard', 'ProtectedRoute');
      return <Navigate to="/admin" replace />;
    } else {
      logger.userAction('redirect staff to dashboard', 'ProtectedRoute');
      return <Navigate to="/staff-dashboard" replace />;
    }
  }

  // If route requires full access but user has limited access, redirect to staff dashboard
  if (requiresFullAccess && user.accessLevel === 'limited') {
    logger.authentication('access level', 'failure', 'ProtectedRoute', { reason: 'limited access' });

    // Avoid circular redirects
    if (location.pathname === '/staff-dashboard') {
      logger.debug('Staff dashboard loop prevention', 'ProtectedRoute');
      return <>{children}</>;
    }

    logger.userAction('redirect limited access to staff dashboard', 'ProtectedRoute');
    return <Navigate to="/staff-dashboard" replace />;
  }

  logger.authentication('access', 'success', 'ProtectedRoute');

  // If authenticated and role allowed, render children
  return <>{children}</>;
  END COMMENTED OUT AUTHENTICATION CODE */
};

export default ProtectedRoute;
