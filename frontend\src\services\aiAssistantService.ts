/**
 * AI Assistant Service for Restaurant Management
 * Provides AI-powered assistance for restaurant operations
 */

import logger from '@/utils/logger';

// Types
export interface AIQueryResponse {
  success: boolean;
  response: string;
  ai_enabled: boolean;
  error?: string;
  timestamp?: string;
  data?: any;
}

export interface AIStatus {
  ai_enabled: boolean;
  model?: string;
  features: string[];
  message: string;
}

// Configuration
const AI_CONFIG = {
  BASE_URL: '/api/ai',
  REQUEST_TIMEOUT: 10000, // 10 seconds
} as const;

// HTTP request utility with timeout
async function fetchWithTimeout(url: string, options: RequestInit = {}): Promise<Response> {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), AI_CONFIG.REQUEST_TIMEOUT);
  
  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });
    
    clearTimeout(timeoutId);
    return response;
  } catch (error) {
    clearTimeout(timeoutId);
    throw error;
  }
}

// AI Assistant Service Class
class AIAssistantService {
  private status: AIStatus | null = null;
  
  /**
   * Get AI service status
   */
  async getStatus(): Promise<AIStatus> {
    try {
      const response = await fetchWithTimeout(`${AI_CONFIG.BASE_URL}/status`);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }
      
      const status = await response.json();
      this.status = status;
      return status;
    } catch (error) {
      logger.error('Failed to get AI status', 'AIAssistantService', { error: error.message });
      const fallbackStatus = {
        ai_enabled: false,
        features: [],
        message: 'AI service unavailable'
      };
      this.status = fallbackStatus;
      return fallbackStatus;
    }
  }
  
  /**
   * Process AI query for restaurant management
   */
  async processQuery(query: string, restaurantId: string): Promise<AIQueryResponse> {
    try {
      // Validate input
      if (!query || query.trim().length === 0) {
        throw new Error('Query cannot be empty');
      }

      if (query.length > 2000) {
        throw new Error('Query too long (max 2000 characters)');
      }

      const response = await fetchWithTimeout(`${AI_CONFIG.BASE_URL}/chat`, {
        method: 'POST',
        body: JSON.stringify({ 
          message: query.trim(),
          context_type: 'restaurant_management',
          restaurant_id: restaurantId
        }),
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      logger.info('AI query processed successfully', 'AIAssistantService', {
        query_length: query.length,
        restaurant_id: restaurantId,
        ai_enabled: data.ai_enabled
      });
      
      return data;
    } catch (error) {
      logger.error('AI query processing failed', 'AIAssistantService', { 
        error: error.message,
        restaurant_id: restaurantId
      });

      // Return fallback response
      return {
        success: false,
        response: "I'm sorry, I'm currently experiencing technical difficulties. Please try again in a moment.",
        ai_enabled: this.status?.ai_enabled || false,
        timestamp: new Date().toISOString(),
        error: error.message
      };
    }
  }
  
  /**
   * Generate restaurant-specific suggestions
   */
  generateSuggestions(restaurantName?: string): string[] {
    const baseName = restaurantName || "your restaurant";
    
    return [
      `What are today's sales figures for ${baseName}?`,
      "Which menu items are most popular this week?",
      "Show me current inventory levels",
      "What are the peak hours for today?",
      "How is staff performance this month?",
      "Generate a sales forecast for next week",
      "Which items need restocking?",
      "Show me customer feedback summary",
      "What's the average table turnover time?",
      "Help me optimize the menu pricing"
    ];
  }
  
  /**
   * Check if AI is enabled
   */
  isEnabled(): boolean {
    return this.status?.ai_enabled || false;
  }
}

// Export singleton instance
const aiAssistantService = new AIAssistantService();
export default aiAssistantService;
