import logger from "@/utils/logger";
import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { toast } from "@/components/ui/sonner";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Search,
  Plus,
  Pencil,
  Trash2,
  <PERSON><PERSON>ircle,
  Clock,
  Calendar,
  AlertCircle,
  ClipboardList,
  Brush, // Replacing Broom with Brush
  Scissors,
  RefreshCw,
  MoreHorizontal
} from "lucide-react";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Task, getAllTasks, getTasksByCategory, addTask, updateTask, deleteTask, markTaskAsCompleted, markTaskAsInProgress } from "@/services/taskService";

interface ThingsToDoTabProps {
  staffData: any[];
}

const ThingsToDoTab: React.FC<ThingsToDoTabProps> = ({ staffData }) => {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [filteredTasks, setFilteredTasks] = useState<Task[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  const [selectedPriority, setSelectedPriority] = useState<string>("all");
  const [isAddTaskDialogOpen, setIsAddTaskDialogOpen] = useState(false);
  const [isEditTaskDialogOpen, setIsEditTaskDialogOpen] = useState(false);
  const [isDeleteTaskDialogOpen, setIsDeleteTaskDialogOpen] = useState(false);
  const [currentTask, setCurrentTask] = useState<Task | null>(null);
  const [newTask, setNewTask] = useState<Partial<Task>>({
    title: "",
    description: "",
    category: "cleaning",
    assignedTo: "",
    assignedBy: "1", // Assuming admin ID is 1
    status: "pending",
    priority: "medium",
    dueDate: new Date(new Date().setDate(new Date().getDate() + 1)).toISOString().split('T')[0],
    completedAt: null
  });

  // Load tasks on component mount
  useEffect(() => {
    logger.info("Component mounted", "ThingsToDoTab");
    toast.success("Things to Do tab loaded successfully");
    loadTasks();
  }, []);

  // Filter tasks when search query or filters change
  useEffect(() => {
    filterTasks();
  }, [tasks, searchQuery, selectedCategory, selectedStatus, selectedPriority]);

  // Load all tasks from localStorage
  const loadTasks = () => {
    const allTasks = getAllTasks();
    setTasks(allTasks);
  };

  // Filter tasks based on search query and selected filters
  const filterTasks = () => {
    let filtered = [...tasks];

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(task =>
        task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        task.description.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Filter by category
    if (selectedCategory !== "all") {
      filtered = filtered.filter(task => task.category === selectedCategory);
    }

    // Filter by status
    if (selectedStatus !== "all") {
      filtered = filtered.filter(task => task.status === selectedStatus);
    }

    // Filter by priority
    if (selectedPriority !== "all") {
      filtered = filtered.filter(task => task.priority === selectedPriority);
    }

    setFilteredTasks(filtered);
  };

  // Get staff member by ID
  const getStaffMember = (id: string) => {
    return staffData.find(staff => staff.id === id);
  };

  // Get category icon
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "cleaning":
        return <Brush className="h-4 w-4" />;
      case "cutting":
        return <Scissors className="h-4 w-4" />;
      case "refilling":
        return <RefreshCw className="h-4 w-4" />;
      default:
        return <ClipboardList className="h-4 w-4" />;
    }
  };

  // Get priority badge variant
  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "high":
        return <Badge variant="destructive">High</Badge>;
      case "medium":
        return <Badge variant="default">Medium</Badge>;
      case "low":
        return <Badge variant="outline">Low</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  // Get status badge variant
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return <Badge variant="default" className="bg-green-500">Completed</Badge>;
      case "in-progress":
        return <Badge variant="outline" className="border-blue-500 text-blue-500">In Progress</Badge>;
      case "pending":
        return <Badge variant="outline" className="border-orange-500 text-orange-500">Pending</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Handle adding new task
  const handleAddTask = () => {
    // Validate required fields
    if (!newTask.title || !newTask.dueDate) {
      toast.error("Please fill in all required fields");
      return;
    }

    // Add the task
    const addedTask = addTask(newTask as Omit<Task, 'id' | 'createdAt'>);

    // Update local state
    setTasks([...tasks, addedTask]);

    // Reset form and close dialog
    setNewTask({
      title: "",
      description: "",
      category: "cleaning",
      assignedTo: "",
      assignedBy: "1",
      status: "pending",
      priority: "medium",
      dueDate: new Date(new Date().setDate(new Date().getDate() + 1)).toISOString().split('T')[0],
      completedAt: null
    });
    setIsAddTaskDialogOpen(false);

    // Show success message
    toast.success("Task added successfully");
  };

  // Handle editing task
  const handleEditTask = () => {
    // Validate required fields
    if (!currentTask || !currentTask.title || !currentTask.dueDate) {
      toast.error("Please fill in all required fields");
      return;
    }

    // Update the task
    updateTask(currentTask);

    // Update local state
    setTasks(tasks.map(task => task.id === currentTask.id ? currentTask : task));

    // Reset and close dialog
    setCurrentTask(null);
    setIsEditTaskDialogOpen(false);

    // Show success message
    toast.success("Task updated successfully");
  };

  // Handle deleting task
  const handleDeleteTask = () => {
    if (!currentTask) return;

    // Delete the task
    deleteTask(currentTask.id);

    // Update local state
    setTasks(tasks.filter(task => task.id !== currentTask.id));

    // Reset and close dialog
    setCurrentTask(null);
    setIsDeleteTaskDialogOpen(false);

    // Show success message
    toast.success("Task deleted successfully");
  };

  // Handle marking task as completed
  const handleMarkAsCompleted = (taskId: string) => {
    // Get the current user ID (assuming the first staff member for demo purposes)
    // In a real app, this would be the logged-in user's ID
    const currentUserId = staffData[0]?.id;
    const staffName = staffData[0]?.name || "Unknown staff";

    logger.userAction(`task completion: ${taskId}`, "ThingsToDoTab", { userId: currentUserId, staffName });

    // Find the task before update
    const taskBeforeUpdate = tasks.find(task => task.id === taskId);
    logger.debug('Task before update', 'ThingsToDoTab', { task: taskBeforeUpdate });

    // Mark the task as completed with the current user ID
    const updatedTask = markTaskAsCompleted(taskId, currentUserId);

    if (updatedTask) {
      logger.dataOperation('update', 'task', 'ThingsToDoTab', { taskId: updatedTask.id });

      // Update local state with the task returned from the service
      setTasks(tasks.map(task => {
        if (task.id === taskId) {
          return updatedTask;
        }
        return task;
      }));
    } else {
      logger.error('Failed to update task', 'ThingsToDoTab');
      toast.error('Failed to mark task as completed');
    }

    // Force a refresh of the tasks from localStorage
    setTimeout(() => {
      logger.dataOperation('refresh', 'tasks from localStorage', 'ThingsToDoTab');
      const refreshedTasks = getAllTasks();
      const refreshedTask = refreshedTasks.find(t => t.id === taskId);
      logger.debug('Refreshed task from localStorage', 'ThingsToDoTab', { taskId: refreshedTask.id });
      setTasks(refreshedTasks);
    }, 500);

    // Show success message with staff name
    toast.success(`Task marked as completed by ${staffName}`);
  };

  // Handle marking task as in-progress
  const handleMarkAsInProgress = (taskId: string) => {
    // Get the current user ID (assuming the first staff member for demo purposes)
    // In a real app, this would be the logged-in user's ID
    const currentUserId = staffData[0]?.id;
    const staffName = staffData[0]?.name || "Unknown staff";

    logger.userAction(`task in-progress: ${taskId}`, "ThingsToDoTab", { userId: currentUserId, staffName });

    // Find the task before update
    const taskBeforeUpdate = tasks.find(task => task.id === taskId);
    logger.debug('Task before update', 'ThingsToDoTab', { task: taskBeforeUpdate });

    // Mark the task as in-progress with the current user ID
    const updatedTask = markTaskAsInProgress(taskId, currentUserId);

    if (updatedTask) {
      logger.dataOperation('update', 'task', 'ThingsToDoTab', { taskId: updatedTask.id });

      // Update local state with the task returned from the service
      setTasks(tasks.map(task => {
        if (task.id === taskId) {
          return updatedTask;
        }
        return task;
      }));
    } else {
      logger.error('Failed to update task', 'ThingsToDoTab');
      toast.error('Failed to mark task as in-progress');
    }

    // Force a refresh of the tasks from localStorage
    setTimeout(() => {
      logger.dataOperation('refresh', 'tasks from localStorage', 'ThingsToDoTab');
      const refreshedTasks = getAllTasks();
      const refreshedTask = refreshedTasks.find(t => t.id === taskId);
      logger.debug('Refreshed task from localStorage', 'ThingsToDoTab', { taskId: refreshedTask.id });
      setTasks(refreshedTasks);
    }, 500);

    // Show success message with staff name
    toast.success(`Task started by ${staffName}`);
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="relative w-full sm:w-72">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search tasks..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex flex-wrap gap-2">
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-[130px]">
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              <SelectItem value="cleaning">Cleaning</SelectItem>
              <SelectItem value="cutting">Cutting</SelectItem>
              <SelectItem value="refilling">Refilling</SelectItem>
              <SelectItem value="other">Other</SelectItem>
            </SelectContent>
          </Select>
          <Select value={selectedStatus} onValueChange={setSelectedStatus}>
            <SelectTrigger className="w-[130px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="in-progress">In Progress</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
            </SelectContent>
          </Select>
          <Select value={selectedPriority} onValueChange={setSelectedPriority}>
            <SelectTrigger className="w-[130px]">
              <SelectValue placeholder="Priority" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Priorities</SelectItem>
              <SelectItem value="high">High</SelectItem>
              <SelectItem value="medium">Medium</SelectItem>
              <SelectItem value="low">Low</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={() => setIsAddTaskDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" /> Add Task
          </Button>
        </div>
      </div>

      <Tabs defaultValue="all" className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">All Tasks</TabsTrigger>
          <TabsTrigger value="cleaning">
            <Brush className="h-4 w-4 mr-2" /> Cleaning
          </TabsTrigger>
          <TabsTrigger value="cutting">
            <Scissors className="h-4 w-4 mr-2" /> Cutting
          </TabsTrigger>
          <TabsTrigger value="refilling">
            <RefreshCw className="h-4 w-4 mr-2" /> Refilling
          </TabsTrigger>
          <TabsTrigger value="other">
            <ClipboardList className="h-4 w-4 mr-2" /> Other
          </TabsTrigger>
        </TabsList>

        <TabsContent value="all">
          <TaskList
            tasks={filteredTasks}
            getStaffMember={getStaffMember}
            getCategoryIcon={getCategoryIcon}
            getPriorityBadge={getPriorityBadge}
            getStatusBadge={getStatusBadge}
            formatDate={formatDate}
            setCurrentTask={setCurrentTask}
            setIsEditTaskDialogOpen={setIsEditTaskDialogOpen}
            setIsDeleteTaskDialogOpen={setIsDeleteTaskDialogOpen}
            handleMarkAsCompleted={handleMarkAsCompleted}
            handleMarkAsInProgress={handleMarkAsInProgress}
          />
        </TabsContent>

        <TabsContent value="cleaning">
          <TaskList
            tasks={filteredTasks.filter(task => task.category === "cleaning")}
            getStaffMember={getStaffMember}
            getCategoryIcon={getCategoryIcon}
            getPriorityBadge={getPriorityBadge}
            getStatusBadge={getStatusBadge}
            formatDate={formatDate}
            setCurrentTask={setCurrentTask}
            setIsEditTaskDialogOpen={setIsEditTaskDialogOpen}
            setIsDeleteTaskDialogOpen={setIsDeleteTaskDialogOpen}
            handleMarkAsCompleted={handleMarkAsCompleted}
            handleMarkAsInProgress={handleMarkAsInProgress}
          />
        </TabsContent>

        <TabsContent value="cutting">
          <TaskList
            tasks={filteredTasks.filter(task => task.category === "cutting")}
            getStaffMember={getStaffMember}
            getCategoryIcon={getCategoryIcon}
            getPriorityBadge={getPriorityBadge}
            getStatusBadge={getStatusBadge}
            formatDate={formatDate}
            setCurrentTask={setCurrentTask}
            setIsEditTaskDialogOpen={setIsEditTaskDialogOpen}
            setIsDeleteTaskDialogOpen={setIsDeleteTaskDialogOpen}
            handleMarkAsCompleted={handleMarkAsCompleted}
            handleMarkAsInProgress={handleMarkAsInProgress}
          />
        </TabsContent>

        <TabsContent value="refilling">
          <TaskList
            tasks={filteredTasks.filter(task => task.category === "refilling")}
            getStaffMember={getStaffMember}
            getCategoryIcon={getCategoryIcon}
            getPriorityBadge={getPriorityBadge}
            getStatusBadge={getStatusBadge}
            formatDate={formatDate}
            setCurrentTask={setCurrentTask}
            setIsEditTaskDialogOpen={setIsEditTaskDialogOpen}
            setIsDeleteTaskDialogOpen={setIsDeleteTaskDialogOpen}
            handleMarkAsCompleted={handleMarkAsCompleted}
            handleMarkAsInProgress={handleMarkAsInProgress}
          />
        </TabsContent>

        <TabsContent value="other">
          <TaskList
            tasks={filteredTasks.filter(task => task.category === "other")}
            getStaffMember={getStaffMember}
            getCategoryIcon={getCategoryIcon}
            getPriorityBadge={getPriorityBadge}
            getStatusBadge={getStatusBadge}
            formatDate={formatDate}
            setCurrentTask={setCurrentTask}
            setIsEditTaskDialogOpen={setIsEditTaskDialogOpen}
            setIsDeleteTaskDialogOpen={setIsDeleteTaskDialogOpen}
            handleMarkAsCompleted={handleMarkAsCompleted}
            handleMarkAsInProgress={handleMarkAsInProgress}
          />
        </TabsContent>
      </Tabs>

      {/* Add Task Dialog */}
      <Dialog open={isAddTaskDialogOpen} onOpenChange={setIsAddTaskDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Add New Task</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="title">Task Title *</Label>
              <Input
                id="title"
                value={newTask.title}
                onChange={(e) => setNewTask({ ...newTask, title: e.target.value })}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="description">Description</Label>
              <Input
                id="description"
                value={newTask.description}
                onChange={(e) => setNewTask({ ...newTask, description: e.target.value })}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="category">Category</Label>
                <Select
                  value={newTask.category as string}
                  onValueChange={(value) => setNewTask({ ...newTask, category: value as Task['category'] })}
                >
                  <SelectTrigger id="category">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="cleaning">Cleaning</SelectItem>
                    <SelectItem value="cutting">Cutting</SelectItem>
                    <SelectItem value="refilling">Refilling</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="priority">Priority</Label>
                <Select
                  value={newTask.priority as string}
                  onValueChange={(value) => setNewTask({ ...newTask, priority: value as Task['priority'] })}
                >
                  <SelectTrigger id="priority">
                    <SelectValue placeholder="Select priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="low">Low</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="assignedTo">Assign To (Optional)</Label>
              <Select
                value={newTask.assignedTo}
                onValueChange={(value) => setNewTask({ ...newTask, assignedTo: value })}
              >
                <SelectTrigger id="assignedTo">
                  <SelectValue placeholder="Select staff member" />
                </SelectTrigger>
                <SelectContent>
                  {staffData.map((staff) => (
                    <SelectItem key={staff.id} value={staff.id}>
                      {staff.name} ({staff.position})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">Leave unassigned to allow any staff to complete this task</p>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="dueDate">Due Date *</Label>
              <Input
                id="dueDate"
                type="date"
                value={newTask.dueDate as string}
                onChange={(e) => setNewTask({ ...newTask, dueDate: e.target.value })}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddTaskDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleAddTask}>Add Task</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Task Dialog */}
      <Dialog open={isEditTaskDialogOpen} onOpenChange={setIsEditTaskDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit Task</DialogTitle>
          </DialogHeader>
          {currentTask && (
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="edit-title">Task Title *</Label>
                <Input
                  id="edit-title"
                  value={currentTask.title}
                  onChange={(e) => setCurrentTask({ ...currentTask, title: e.target.value })}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-description">Description</Label>
                <Input
                  id="edit-description"
                  value={currentTask.description}
                  onChange={(e) => setCurrentTask({ ...currentTask, description: e.target.value })}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="edit-category">Category</Label>
                  <Select
                    value={currentTask.category}
                    onValueChange={(value) => setCurrentTask({ ...currentTask, category: value as Task['category'] })}
                  >
                    <SelectTrigger id="edit-category">
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="cleaning">Cleaning</SelectItem>
                      <SelectItem value="cutting">Cutting</SelectItem>
                      <SelectItem value="refilling">Refilling</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="edit-priority">Priority</Label>
                  <Select
                    value={currentTask.priority}
                    onValueChange={(value) => setCurrentTask({ ...currentTask, priority: value as Task['priority'] })}
                  >
                    <SelectTrigger id="edit-priority">
                      <SelectValue placeholder="Select priority" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="low">Low</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-assignedTo">Assign To (Optional)</Label>
                <Select
                  value={currentTask.assignedTo}
                  onValueChange={(value) => setCurrentTask({ ...currentTask, assignedTo: value })}
                >
                  <SelectTrigger id="edit-assignedTo">
                    <SelectValue placeholder="Select staff member" />
                  </SelectTrigger>
                  <SelectContent>
                    {staffData.map((staff) => (
                      <SelectItem key={staff.id} value={staff.id}>
                        {staff.name} ({staff.position})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">Leave unassigned to allow any staff to complete this task</p>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-status">Status</Label>
                <Select
                  value={currentTask.status}
                  onValueChange={(value) => setCurrentTask({ ...currentTask, status: value as Task['status'] })}
                >
                  <SelectTrigger id="edit-status">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="in-progress">In Progress</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-dueDate">Due Date *</Label>
                <Input
                  id="edit-dueDate"
                  type="date"
                  value={currentTask.dueDate.split('T')[0]}
                  onChange={(e) => setCurrentTask({ ...currentTask, dueDate: e.target.value })}
                />
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditTaskDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleEditTask}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteTaskDialogOpen} onOpenChange={setIsDeleteTaskDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>Are you sure you want to delete the task "{currentTask?.title}"? This action cannot be undone.</p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteTaskDialogOpen(false)}>Cancel</Button>
            <Button variant="destructive" onClick={handleDeleteTask}>Delete</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

// Task List Component
interface TaskListProps {
  tasks: Task[];
  getStaffMember: (id: string) => any;
  getCategoryIcon: (category: string) => JSX.Element;
  getPriorityBadge: (priority: string) => JSX.Element;
  getStatusBadge: (status: string) => JSX.Element;
  formatDate: (dateString: string) => string;
  setCurrentTask: (task: Task) => void;
  setIsEditTaskDialogOpen: (open: boolean) => void;
  setIsDeleteTaskDialogOpen: (open: boolean) => void;
  handleMarkAsCompleted: (taskId: string) => void;
  handleMarkAsInProgress: (taskId: string) => void;
}

const TaskList: React.FC<TaskListProps> = ({
  tasks,
  getStaffMember,
  getCategoryIcon,
  getPriorityBadge,
  getStatusBadge,
  formatDate,
  setCurrentTask,
  setIsEditTaskDialogOpen,
  setIsDeleteTaskDialogOpen,
  handleMarkAsCompleted,
  handleMarkAsInProgress
}) => {
  return (
    <Card>
      <CardContent className="p-0">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Task</TableHead>
              <TableHead>Assigned To</TableHead>
              <TableHead>Due Date</TableHead>
              <TableHead>Priority</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {tasks.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                  No tasks found
                </TableCell>
              </TableRow>
            ) : (
              tasks.map((task) => {
                const staff = getStaffMember(task.assignedTo);
                return (
                  <TableRow key={task.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <div className="bg-muted p-2 rounded-full">
                          {getCategoryIcon(task.category)}
                        </div>
                        <div>
                          <div className="font-medium">{task.title}</div>
                          <div className="text-xs text-muted-foreground">{task.description}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {staff ? (
                        <div className="flex items-center gap-2">
                          <Avatar className="h-8 w-8">
                            <AvatarFallback>
                              {staff.name.split(" ").map((n: string) => n[0]).join("").toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">{staff.name}</div>
                            <div className="text-xs text-muted-foreground">{staff.position}</div>
                          </div>
                        </div>
                      ) : (
                        <span className="text-muted-foreground">Unassigned</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span>{formatDate(task.dueDate)}</span>
                      </div>
                    </TableCell>
                    <TableCell>{getPriorityBadge(task.priority)}</TableCell>
                    <TableCell>
                      {task.status === "completed" ? (
                        <div>
                          {getStatusBadge(task.status)}
                          <div className="text-xs text-muted-foreground mt-1">
                            Completed by: {task.completedBy ? getStaffMember(task.completedBy)?.name || "Unknown" : "Unknown"}
                            {task.completedBy && <span className="text-xs text-green-600 ml-1">(ID: {task.completedBy})</span>}
                          </div>
                        </div>
                      ) : task.status === "in-progress" ? (
                        <div>
                          {getStatusBadge(task.status)}
                          <div className="text-xs text-muted-foreground mt-1">
                            Started by: {task.assignedTo ? getStaffMember(task.assignedTo)?.name || "Unknown" : "Unknown"}
                            {task.assignedTo && <span className="text-xs text-blue-600 ml-1">(ID: {task.assignedTo})</span>}
                          </div>
                        </div>
                      ) : (
                        getStatusBadge(task.status)
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        {task.status !== "completed" && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleMarkAsCompleted(task.id)}
                          >
                            <CheckCircle className="h-4 w-4 mr-2" /> Complete
                          </Button>
                        )}
                        {task.status === "pending" && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleMarkAsInProgress(task.id)}
                          >
                            <Clock className="h-4 w-4 mr-2" /> Start
                          </Button>
                        )}
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => {
                            setCurrentTask(task);
                            setIsEditTaskDialogOpen(true);
                          }}
                        >
                          <Pencil className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => {
                            setCurrentTask(task);
                            setIsDeleteTaskDialogOpen(true);
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => {
                            logger.debug('Task details', 'ThingsToDoTab', { task });
                            toast.info(`Task ID: ${task.id}, Status: ${task.status}, CompletedBy: ${task.completedBy || 'None'}`);
                          }}
                        >
                          <AlertCircle className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })
            )}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};

export default ThingsToDoTab;
