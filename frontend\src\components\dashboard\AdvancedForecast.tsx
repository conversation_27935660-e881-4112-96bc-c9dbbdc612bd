import { useState } from "react";
import {
  Card,
  Card<PERSON>ontent,
  Card<PERSON>eader,
  CardTitle,
  CardDescription
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer, 
  Legend,
  LineChart,
  Line,
  AreaChart,
  Area
} from 'recharts';
import { ForecastData } from "./ForecastCard";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { 
  Calendar, 
  TrendingUp, 
  AlertCircle, 
  Clock, 
  Users, 
  ChefHat, 
  BarChart3,
  Download
} from "lucide-react";

interface AdvancedForecastCardProps {
  data?: ForecastData[];
  title?: string;
}

// Custom tooltip component
const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-background border rounded p-2 shadow-sm">
        <p className="font-medium">{label}</p>
        {payload.map((entry: any, index: number) => (
          <p key={`item-${index}`} style={{ color: entry.color }}>
            {entry.name}: {entry.dataKey.includes('Revenue') ? '£' : ''}{entry.value.toLocaleString()}
            {entry.dataKey === 'customers' ? ' customers' : ''}
            {entry.dataKey === 'tableOccupancy' ? '%' : ''}
          </p>
        ))}
      </div>
    );
  }

  return null;
};

// Peak hours tooltip
const PeakHoursTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-background border rounded p-2 shadow-sm">
        <p className="font-medium">{label}:00</p>
        {payload.map((entry: any, index: number) => (
          <p key={`item-${index}`} style={{ color: entry.color }}>
            {entry.name}: {entry.value} customers
          </p>
        ))}
      </div>
    );
  }

  return null;
};

const AdvancedForecastCard = ({ data = [], title = "Advanced Forecast Analysis" }: AdvancedForecastCardProps) => {
  const [timeframe, setTimeframe] = useState<"week" | "month" | "quarter">("week");
  const [selectedDay, setSelectedDay] = useState<string>(data.length > 0 ? data[0].day : "");

  // Get peak hours data for the selected day
  const peakHoursData = data.find(d => d.day === selectedDay)?.peakHours || [];
  
  // Format peak hours data for chart
  const formattedPeakHours = peakHoursData.map(ph => ({
    hour: ph.hour,
    customers: ph.customers
  }));

  // Fill in missing hours (8am to 10pm)
  const completeHoursData = Array.from({ length: 15 }, (_, i) => i + 8).map(hour => {
    const existingData = formattedPeakHours.find(ph => ph.hour === hour);
    return existingData || { hour, customers: 0 };
  });

  return (
    <Card className="col-span-2">
      <CardHeader>
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2">
          <div>
            <CardTitle>{title}</CardTitle>
            <CardDescription>
              Detailed analysis of customer patterns and revenue forecasts
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Select
              value={timeframe}
              onValueChange={(value) => setTimeframe(value as "week" | "month" | "quarter")}
            >
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder="Timeframe" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="week">This Week</SelectItem>
                <SelectItem value="month">This Month</SelectItem>
                <SelectItem value="quarter">This Quarter</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" size="icon">
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="overview">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="peak-hours">Peak Hours</TabsTrigger>
            <TabsTrigger value="staff-needs">Staff Needs</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
              {data.map((day) => (
                <Card 
                  key={day.day} 
                  className={`cursor-pointer hover:border-primary transition-colors ${
                    selectedDay === day.day ? 'border-primary bg-primary/5' : ''
                  }`}
                  onClick={() => setSelectedDay(day.day)}
                >
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <div className="text-sm font-medium">{day.day}</div>
                        <div className="text-2xl font-bold mt-1">£{day.projectedRevenue}</div>
                      </div>
                      <Badge variant={day.confidence && day.confidence > 85 ? "default" : "outline"}>
                        {day.confidence || 80}% confidence
                      </Badge>
                    </div>
                    <div className="flex justify-between mt-2 text-sm text-muted-foreground">
                      <div className="flex items-center">
                        <Users className="h-3 w-3 mr-1" />
                        {day.customers}
                      </div>
                      <div className="flex items-center">
                        <TrendingUp className="h-3 w-3 mr-1" />
                        {day.tableOccupancy || 70}%
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
            
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={data}
                  margin={{ top: 10, right: 10, left: 10, bottom: 20 }}
                >
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis
                    dataKey="day"
                    tick={{ fontSize: 12 }}
                    tickLine={false}
                    axisLine={false}
                  />
                  <YAxis
                    yAxisId="left"
                    tickFormatter={(value) => `£${value}`}
                    tick={{ fontSize: 12 }}
                    tickLine={false}
                    axisLine={false}
                  />
                  <YAxis
                    yAxisId="right"
                    orientation="right"
                    tick={{ fontSize: 12 }}
                    tickLine={false}
                    axisLine={false}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                  <Bar
                    yAxisId="left"
                    dataKey="projectedRevenue"
                    name="Projected Revenue"
                    fill="#3b82f6"
                    radius={[4, 4, 0, 0]}
                    barSize={20}
                  />
                  <Line
                    yAxisId="right"
                    type="monotone"
                    dataKey="customers"
                    name="Customers"
                    stroke="#10b981"
                    strokeWidth={2}
                    dot={{ r: 4 }}
                    activeDot={{ r: 6 }}
                  />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </TabsContent>
          
          <TabsContent value="peak-hours" className="space-y-4">
            <div className="flex justify-between items-center mb-4">
              <div>
                <h3 className="text-lg font-medium">{selectedDay || "Select a day"} Peak Hours</h3>
                <p className="text-sm text-muted-foreground">
                  Customer traffic throughout the day
                </p>
              </div>
              <Select
                value={selectedDay}
                onValueChange={setSelectedDay}
              >
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Select day" />
                </SelectTrigger>
                <SelectContent>
                  {data.map((day) => (
                    <SelectItem key={day.day} value={day.day}>
                      {day.day}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart
                  data={completeHoursData}
                  margin={{ top: 10, right: 10, left: 10, bottom: 20 }}
                >
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis
                    dataKey="hour"
                    tickFormatter={(value) => `${value}:00`}
                    tick={{ fontSize: 12 }}
                    tickLine={false}
                    axisLine={false}
                  />
                  <YAxis
                    tick={{ fontSize: 12 }}
                    tickLine={false}
                    axisLine={false}
                  />
                  <Tooltip content={<PeakHoursTooltip />} />
                  <Area
                    type="monotone"
                    dataKey="customers"
                    name="Customers"
                    stroke="#8b5cf6"
                    fill="#8b5cf680"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
            
            <div className="grid grid-cols-3 gap-4 mt-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm font-medium">Lunch Peak</div>
                      <div className="text-2xl font-bold mt-1">
                        {Math.max(...completeHoursData.filter(d => d.hour >= 11 && d.hour <= 14).map(d => d.customers))} customers
                      </div>
                    </div>
                    <Clock className="h-8 w-8 text-muted-foreground" />
                  </div>
                  <div className="text-sm text-muted-foreground mt-2">
                    Typically between 12:00 - 14:00
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm font-medium">Dinner Peak</div>
                      <div className="text-2xl font-bold mt-1">
                        {Math.max(...completeHoursData.filter(d => d.hour >= 18 && d.hour <= 21).map(d => d.customers))} customers
                      </div>
                    </div>
                    <Clock className="h-8 w-8 text-muted-foreground" />
                  </div>
                  <div className="text-sm text-muted-foreground mt-2">
                    Typically between 18:00 - 21:00
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm font-medium">Quiet Hours</div>
                      <div className="text-2xl font-bold mt-1">
                        {Math.min(...completeHoursData.filter(d => d.hour >= 14 && d.hour <= 17).map(d => d.customers))} customers
                      </div>
                    </div>
                    <AlertCircle className="h-8 w-8 text-muted-foreground" />
                  </div>
                  <div className="text-sm text-muted-foreground mt-2">
                    Typically between 14:00 - 17:00
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          
          <TabsContent value="staff-needs" className="space-y-4">
            <div className="flex justify-between items-center mb-4">
              <div>
                <h3 className="text-lg font-medium">Staff Requirements</h3>
                <p className="text-sm text-muted-foreground">
                  Recommended staffing levels based on forecast
                </p>
              </div>
            </div>
            
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={data}
                  margin={{ top: 10, right: 10, left: 10, bottom: 20 }}
                >
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis
                    dataKey="day"
                    tick={{ fontSize: 12 }}
                    tickLine={false}
                    axisLine={false}
                  />
                  <YAxis
                    tick={{ fontSize: 12 }}
                    tickLine={false}
                    axisLine={false}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                  <Bar
                    dataKey="staffNeeded"
                    name="Total Staff"
                    fill="#3b82f6"
                    radius={[4, 4, 0, 0]}
                    barSize={20}
                  />
                </BarChart>
              </ResponsiveContainer>
            </div>
            
            <div className="grid grid-cols-4 gap-4 mt-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm font-medium">Waiters</div>
                      <div className="text-2xl font-bold mt-1">
                        {Math.ceil(data.find(d => d.day === selectedDay)?.staffNeeded * 0.4 || 0)}
                      </div>
                    </div>
                    <Users className="h-8 w-8 text-blue-500" />
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm font-medium">Kitchen</div>
                      <div className="text-2xl font-bold mt-1">
                        {Math.ceil(data.find(d => d.day === selectedDay)?.staffNeeded * 0.3 || 0)}
                      </div>
                    </div>
                    <ChefHat className="h-8 w-8 text-red-500" />
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm font-medium">Bar</div>
                      <div className="text-2xl font-bold mt-1">
                        {Math.ceil(data.find(d => d.day === selectedDay)?.staffNeeded * 0.2 || 0)}
                      </div>
                    </div>
                    <BarChart3 className="h-8 w-8 text-purple-500" />
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm font-medium">Host</div>
                      <div className="text-2xl font-bold mt-1">
                        {Math.ceil(data.find(d => d.day === selectedDay)?.staffNeeded * 0.1 || 0)}
                      </div>
                    </div>
                    <Users className="h-8 w-8 text-yellow-500" />
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default AdvancedForecastCard;