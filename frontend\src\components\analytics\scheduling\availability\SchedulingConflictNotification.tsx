import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { AlertCircle, CheckCircle } from "lucide-react";
import { SchedulingConflict } from "@/types/staffAvailability";
import { 
  getUnresolvedConflicts, 
  resolveSchedulingConflict,
  formatUKDate
} from "@/services/staffAvailabilityService";

interface SchedulingConflictNotificationProps {
  onResolve?: () => void;
}

const SchedulingConflictNotification = ({ 
  onResolve 
}: SchedulingConflictNotificationProps) => {
  const [conflicts, setConflicts] = useState<SchedulingConflict[]>([]);

  useEffect(() => {
    // Load unresolved conflicts
    const loadConflicts = () => {
      const unresolvedConflicts = getUnresolvedConflicts();
      setConflicts(unresolvedConflicts);
    };

    loadConflicts();
    
    // Set up an interval to check for new conflicts
    const intervalId = setInterval(loadConflicts, 30000); // Check every 30 seconds
    
    return () => clearInterval(intervalId);
  }, []);

  const handleResolveConflict = (conflictId: string) => {
    resolveSchedulingConflict(conflictId);
    setConflicts(conflicts.filter(conflict => conflict.id !== conflictId));
    onResolve?.();
  };

  if (conflicts.length === 0) {
    return null;
  }

  return (
    <Card className="border-amber-500">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg font-medium flex items-center gap-2">
          <AlertCircle className="h-5 w-5 text-amber-500" />
          Scheduling Conflicts
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            The following scheduling conflicts have been detected. Please resolve them to ensure adequate staffing levels.
          </p>
          
          <div className="space-y-3">
            {conflicts.map((conflict) => (
              <div key={conflict.id} className="flex items-start justify-between gap-4 p-3 bg-muted rounded-md">
                <div>
                  <p className="font-medium">Staff ID: {conflict.staffId}</p>
                  <p className="text-sm text-muted-foreground">Date: {formatUKDate(conflict.date)}</p>
                  <p className="text-sm text-muted-foreground">Description: {conflict.description}</p>
                  <Badge variant="outline" className="mt-1">Shift ID: {conflict.shiftId}</Badge>
                </div>
                <Button 
                  size="sm" 
                  variant="outline" 
                  className="flex items-center gap-1"
                  onClick={() => handleResolveConflict(conflict.id)}
                >
                  <CheckCircle className="h-4 w-4" />
                  Resolve
                </Button>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SchedulingConflictNotification;
