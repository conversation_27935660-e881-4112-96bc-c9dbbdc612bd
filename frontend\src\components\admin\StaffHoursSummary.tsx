import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useNavigate } from "react-router-dom";
import { Clock, Users, ArrowRight } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface StaffHoursSummaryProps {
  staffData: any[];
}

const StaffHoursSummary = ({ staffData }: StaffHoursSummaryProps) => {
  const navigate = useNavigate();
  const [selectedPeriod, setSelectedPeriod] = useState<"week" | "month">("week");
  const [totalHours, setTotalHours] = useState<number>(0);
  const [staffHours, setStaffHours] = useState<{ id: string; name: string; hours: number; role: string }[]>([]);

  // Generate mock hours data
  useEffect(() => {
    if (staffData && staffData.length > 0) {
      // Calculate total hours for all staff
      let total = 0;
      
      // Generate random hours for each staff member
      const hoursData = staffData.map(staff => {
        // Base hours on assigned hours if available, otherwise random
        const assignedHours = staff.assignedHours || Math.floor(Math.random() * 20) + 20;
        
        // For week view, show a portion of assigned hours
        // For month view, show full assigned hours with some variation
        const hours = selectedPeriod === "week" 
          ? Math.round((assignedHours / 4) * (0.8 + Math.random() * 0.4)) 
          : Math.round(assignedHours * (0.9 + Math.random() * 0.2));
        
        total += hours;
        
        return {
          id: staff.id,
          name: staff.name,
          hours: hours,
          role: staff.role
        };
      });
      
      // Sort by hours worked (descending)
      hoursData.sort((a, b) => b.hours - a.hours);
      
      setStaffHours(hoursData);
      setTotalHours(total);
    }
  }, [staffData, selectedPeriod]);

  return (
    <Card className="col-span-1 md:col-span-2">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-lg font-medium">Staff Hours Summary</CardTitle>
        <div className="flex items-center gap-2">
          <Select value={selectedPeriod} onValueChange={(value: "week" | "month") => setSelectedPeriod(value)}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="Select period" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="week">This Week</SelectItem>
              <SelectItem value="month">This Month</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent>
        <div className="mb-4 p-4 bg-muted rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Clock className="h-5 w-5 mr-2 text-muted-foreground" />
              <span className="text-sm font-medium">Total Hours:</span>
            </div>
            <span className="text-xl font-bold">{totalHours} hours</span>
          </div>
          <div className="flex items-center justify-between mt-2">
            <div className="flex items-center">
              <Users className="h-5 w-5 mr-2 text-muted-foreground" />
              <span className="text-sm font-medium">Staff Count:</span>
            </div>
            <span className="text-xl font-bold">{staffHours.length}</span>
          </div>
        </div>

        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Staff</TableHead>
              <TableHead>Role</TableHead>
              <TableHead className="text-right">Hours</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {staffHours.slice(0, 5).map((staff) => (
              <TableRow key={staff.id}>
                <TableCell className="font-medium">{staff.name}</TableCell>
                <TableCell className="capitalize">{staff.role}</TableCell>
                <TableCell className="text-right">{staff.hours}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>

        <Button 
          variant="outline" 
          className="w-full mt-4"
          onClick={() => navigate('/admin/staff')}
        >
          View All Staff <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </CardContent>
    </Card>
  );
};

export default StaffHoursSummary;
