"""
Menu models for RestroManage database.
Corresponds to app/models/menu.py Pydantic models.
"""

from sqlalchemy import Column, String, Boolean, Float, Text, JSON, ForeignKey, Integer, DateTime
from sqlalchemy.orm import relationship
from .base import BaseModel, TimestampMixin, StatusMixin, SoftDeleteMixin

class MenuCategory(BaseModel, TimestampMixin, StatusMixin):
    """
    Menu category model for organizing menu items.
    """
    __tablename__ = "menu_categories"
    
    # Basic information
    name = Column(String(100), nullable=False, index=True)
    description = Column(Text, nullable=True)
    
    # Restaurant association
    restaurant_id = Column(String(36), ForeignKey("restaurants.id"), nullable=False, index=True)
    
    # Display settings
    display_order = Column(Integer, default=0, nullable=False)
    color = Column(String(7), nullable=True)  # Hex color code
    icon = Column(String(50), nullable=True)
    image_url = Column(String(500), nullable=True)
    
    # Category settings
    is_featured = Column(Boolean, default=False)
    is_seasonal = Column(Boolean, default=False)
    seasonal_start = Column(String(10), nullable=True)  # MM-DD format
    seasonal_end = Column(String(10), nullable=True)    # MM-DD format
    
    # Relationships
    restaurant = relationship("Restaurant", back_populates="menu_categories")
    menu_items = relationship("MenuItem", back_populates="category")
    
    def __repr__(self):
        return f"<MenuCategory(id={self.id}, name={self.name})>"

class MenuItem(BaseModel, TimestampMixin):
    """
    Menu item model for restaurant menu management.
    Simplified to match existing database schema.
    """
    __tablename__ = "menu_items"

    # Basic information (matches existing schema)
    restaurant_id = Column(String(36), nullable=False, index=True)
    name = Column(String(255), nullable=False, index=True)
    price = Column(Float, nullable=False, index=True)
    category = Column(String(100), nullable=True)  # Simple category string
    description = Column(Text, nullable=True)
    image_url = Column(String(500), nullable=True)
    available = Column(Boolean, default=True, nullable=False)

    # JSON fields for complex data
    ingredients = Column(JSON, nullable=True)  # List of ingredients
    allergens = Column(JSON, nullable=True)   # List of allergens

    # Relationships (simplified)
    # restaurant = relationship("Restaurant", back_populates="menu_items")
    # order_items = relationship("OrderItem", back_populates="menu_item")
    
    def __repr__(self):
        return f"<MenuItem(id={self.id}, name={self.name}, price={self.price})>"
