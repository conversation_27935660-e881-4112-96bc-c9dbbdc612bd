import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from '@/components/ui/sonner';
import { Users, Calculator, CreditCard, Receipt, Minus, Plus } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { getAuthHeaders } from '@/utils/authUtils';

interface CartItem {
  id: string;
  name: string;
  description?: string;
  price: number;
  quantity: number;
  category?: string;
  allergens?: string[];
  notes?: string;
  allergenAlert?: boolean;
}

interface OrderItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  subtotal: number;
}

interface SplitPortion {
  customer_name?: string;
  amount: number;
  items?: OrderItem[];
  tip_amount?: number;
}

interface SplitBillManagerProps {
  isOpen: boolean;
  onClose: () => void;
  cart: CartItem[];
  onSplitComplete: (splitId: string) => void;
}

const SplitBillManager: React.FC<SplitBillManagerProps> = ({
  isOpen,
  onClose,
  cart,
  onSplitComplete
}) => {
  const { user } = useAuth();
  const [splitType, setSplitType] = useState<'equal_split' | 'by_items' | 'custom_amounts'>('equal_split');
  const [numberOfSplits, setNumberOfSplits] = useState(2);
  const [portions, setPortions] = useState<SplitPortion[]>([]);
  const [isCreating, setIsCreating] = useState(false);

  // Convert cart items to order items
  const orderItems: OrderItem[] = cart.map(item => ({
    id: item.id,
    name: item.name,
    price: item.price,
    quantity: item.quantity,
    subtotal: item.price * item.quantity
  }));

  // Calculate order total
  const orderTotal = cart.reduce((total, item) => total + (item.price * item.quantity), 0);

  const initializePortions = () => {
    const newPortions: SplitPortion[] = [];
    const safeOrderTotal = orderTotal || 0; // Prevent undefined errors
    
    for (let i = 0; i < numberOfSplits; i++) {
      if (splitType === 'equal_split') {
        newPortions.push({
          customer_name: `Person ${i + 1}`,
          amount: safeOrderTotal / numberOfSplits,
          tip_amount: 0
        });
      } else if (splitType === 'by_items') {
        newPortions.push({
          customer_name: `Person ${i + 1}`,
          amount: 0,
          items: [],
          tip_amount: 0
        });
      } else {
        newPortions.push({
          customer_name: `Person ${i + 1}`,
          amount: 0,
          tip_amount: 0
        });
      }
    }
    
    setPortions(newPortions);
  };

  // Initialize portions when dialog opens or split type changes
  useEffect(() => {
    if (isOpen) {
      initializePortions();
    }
  }, [isOpen, splitType, numberOfSplits, orderTotal]);

  const updatePortionName = (index: number, name: string) => {
    const newPortions = [...portions];
    newPortions[index].customer_name = name;
    setPortions(newPortions);
  };

  const updatePortionAmount = (index: number, amount: number) => {
    const newPortions = [...portions];
    newPortions[index].amount = amount;
    setPortions(newPortions);
  };

  const updatePortionTip = (index: number, tip: number) => {
    const newPortions = [...portions];
    newPortions[index].tip_amount = tip;
    setPortions(newPortions);
  };

  const assignItemToPortion = (itemId: string, portionIndex: number) => {
    const item = orderItems.find(i => i.id === itemId);
    if (!item) return;

    const newPortions = [...portions];

    // Remove item from all portions first
    newPortions.forEach(portion => {
      if (portion.items) {
        portion.items = portion.items.filter(i => i.id !== itemId);
      }
    });

    // Add item to selected portion
    if (!newPortions[portionIndex].items) {
      newPortions[portionIndex].items = [];
    }
    newPortions[portionIndex].items!.push(item);

    // Recalculate amounts for by_items split
    if (splitType === 'by_items') {
      newPortions.forEach(portion => {
        if (portion.items) {
          portion.amount = portion.items.reduce((sum, item) => sum + item.subtotal, 0);
        } else {
          portion.amount = 0;
        }
      });
    }

    setPortions(newPortions);
  };

  const createSplitBill = async () => {
    const safeOrderTotal = orderTotal || 0; // Prevent undefined errors
    
    // Validate portions
    if (splitType === 'custom_amounts') {
      const totalCustomAmount = portions.reduce((sum, portion) => sum + portion.amount, 0);
      if (Math.abs(totalCustomAmount - safeOrderTotal) > 0.01) {
        toast.error(`Custom amounts must total £${safeOrderTotal.toFixed(2)}`);
        return;
      }
    }

    if (splitType === 'by_items') {
      const assignedItems = portions.flatMap(portion => portion.items || []);
      if (assignedItems.length !== orderItems.length) {
        toast.error('All items must be assigned to a person');
        return;
      }
    }

    setIsCreating(true);

    try {
      const splitBillData = {
        original_order_id: `order_${Date.now()}`, // Generate a temporary order ID
        split_type: splitType,
        number_of_splits: numberOfSplits,
        portions: portions.map(portion => ({
          customer_name: portion.customer_name,
          amount: portion.amount,
          tip_amount: portion.tip_amount || 0,
          items: portion.items?.map(item => ({
            order_item_id: item.id,
            quantity: item.quantity,
            price: item.price
          })) || []
        }))
      };

      const response = await fetch('http://localhost:5001/api/split-bills/', {
        method: 'POST',
        headers: getAuthHeaders(user),
        body: JSON.stringify(splitBillData),
      });

      if (response.ok) {
        const splitBill = await response.json();
        onSplitComplete(splitBill.id);
        onClose();
        toast.success('Split bill created successfully!');
      } else {
        const error = await response.json();
        toast.error(error.detail || 'Failed to create split bill');
      }
    } catch (error) {
      console.error('Error creating split bill:', error);
      toast.error('Failed to create split bill');
    } finally {
      setIsCreating(false);
    }
  };

  const totalAssigned = portions.reduce((sum, portion) => sum + portion.amount, 0);
  const safeOrderTotal = orderTotal || 0; // Prevent undefined errors
  const remaining = safeOrderTotal - totalAssigned;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto" aria-describedby="split-bill-description">
        <DialogHeader>
          <DialogTitle>Split Bill - £{(orderTotal || 0).toFixed(2)}</DialogTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            ✕
            <span className="sr-only">Close</span>
          </Button>
        </DialogHeader>

        <div id="split-bill-description" className="sr-only">
          Split your bill between multiple people. Choose how to divide the total amount of £{(orderTotal || 0).toFixed(2)}.
        </div>

        <Tabs value={splitType} onValueChange={(value) => setSplitType(value as any)}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="equal_split">Equal Split</TabsTrigger>
            <TabsTrigger value="by_items">By Items</TabsTrigger>
            <TabsTrigger value="custom_amounts">Custom Amounts</TabsTrigger>
          </TabsList>

          <div className="mt-4 space-y-4">
            {/* Number of splits */}
            <div className="flex items-center gap-4">
              <Label>Number of people:</Label>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setNumberOfSplits(Math.max(2, numberOfSplits - 1))}
                  disabled={numberOfSplits <= 2}
                >
                  <Minus className="w-3 h-3" />
                </Button>
                <span className="w-8 text-center">{numberOfSplits}</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setNumberOfSplits(Math.min(10, numberOfSplits + 1))}
                  disabled={numberOfSplits >= 10}
                >
                  <Plus className="w-3 h-3" />
                </Button>
              </div>
              <Button variant="outline" size="sm" onClick={initializePortions}>
                Update Split
              </Button>
            </div>

            <TabsContent value="equal_split" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {portions.map((portion, index) => (
                  <Card key={index}>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm">Person {index + 1}</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <Input
                        placeholder="Name (optional)"
                        value={portion.customer_name || ''}
                        onChange={(e) => updatePortionName(index, e.target.value)}
                      />
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Amount:</span>
                        <Badge variant="secondary">£{(portion.amount || 0).toFixed(2)}</Badge>
                      </div>
                      <div className="flex items-center gap-2">
                        <Label className="text-xs">Tip:</Label>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          value={portion.tip_amount || 0}
                          onChange={(e) => updatePortionTip(index, parseFloat(e.target.value) || 0)}
                          className="w-20 h-8 text-xs"
                        />
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="by_items" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-2">Order Items</h4>
                  <div className="space-y-2 max-h-60 overflow-y-auto">
                    {orderItems.map((item) => (
                      <Card key={item.id} className="p-2">
                        <div className="flex justify-between items-center">
                          <div>
                            <p className="text-sm font-medium">{item.name}</p>
                            <p className="text-xs text-gray-500">
                              {item.quantity}x £{item.price.toFixed(2)} = £{item.subtotal.toFixed(2)}
                            </p>
                          </div>
                          <Select onValueChange={(value) => assignItemToPortion(item.id, parseInt(value))}>
                            <SelectTrigger className="w-32 h-8">
                              <SelectValue placeholder="Assign to..." />
                            </SelectTrigger>
                            <SelectContent>
                              {portions.map((_, index) => (
                                <SelectItem key={index} value={index.toString()}>
                                  Person {index + 1}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </Card>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-2">Split Summary</h4>
                  <div className="space-y-2">
                    {portions.map((portion, index) => (
                      <Card key={index} className="p-2">
                        <div className="space-y-1">
                          <Input
                            placeholder="Name (optional)"
                            value={portion.customer_name || ''}
                            onChange={(e) => updatePortionName(index, e.target.value)}
                            className="h-8"
                          />
                          <div className="flex justify-between text-sm">
                            <span>Items: {portion.items?.length || 0}</span>
                            <span>£{(portion.amount || 0).toFixed(2)}</span>
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="custom_amounts" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {portions.map((portion, index) => (
                  <Card key={index}>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm">Person {index + 1}</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <Input
                        placeholder="Name (optional)"
                        value={portion.customer_name || ''}
                        onChange={(e) => updatePortionName(index, e.target.value)}
                      />
                      <div className="flex items-center gap-2">
                        <Label className="text-sm">Amount:</Label>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          value={portion.amount || 0}
                          onChange={(e) => updatePortionAmount(index, parseFloat(e.target.value) || 0)}
                          className="flex-1"
                        />
                      </div>
                      <div className="flex items-center gap-2">
                        <Label className="text-xs">Tip:</Label>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          value={portion.tip_amount || 0}
                          onChange={(e) => updatePortionTip(index, parseFloat(e.target.value) || 0)}
                          className="w-20 h-8 text-xs"
                        />
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              <div className="p-3 bg-gray-50 rounded-md">
                <div className="flex justify-between items-center">
                  <span>Total Assigned:</span>
                  <span className={remaining !== 0 ? 'text-red-600' : 'text-green-600'}>
                    £{totalAssigned.toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span>Remaining:</span>
                  <span className={remaining !== 0 ? 'text-red-600' : 'text-green-600'}>
                    £{remaining.toFixed(2)}
                  </span>
                </div>
              </div>
            </TabsContent>
          </div>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={createSplitBill}
            disabled={isCreating || (splitType === 'custom_amounts' && Math.abs(remaining) > 0.01)}
          >
            {isCreating ? 'Creating...' : 'Create Split Bill'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default SplitBillManager;
