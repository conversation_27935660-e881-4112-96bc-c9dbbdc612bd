"""
Authentication and User models for RestroManage database.
Corresponds to app/models/auth.py Pydantic models.
"""

from sqlalchemy import Column, String, Boolean, DateTime, Text, ForeignKey, Integer
from sqlalchemy.orm import relationship
from .base import BaseModel, TimestampMixin, StatusMixin, SoftDeleteMixin

class User(BaseModel, TimestampMixin, StatusMixin, SoftDeleteMixin):
    """
    User model for authentication and user management.
    Corresponds to User Pydantic model in app/models/auth.py
    """
    __tablename__ = "users"
    
    # Basic user information
    username = Column(String(50), unique=True, nullable=False, index=True)
    email = Column(String(255), unique=True, nullable=False, index=True)
    full_name = Column(String(255), nullable=False)
    
    # Authentication
    password_hash = Column(String(255), nullable=False)
    
    # Role and permissions
    role = Column(String(20), nullable=False, default="staff", index=True)
    # Roles: admin, manager, staff, waiter, chef, cashier
    
    # Restaurant association
    restaurant_id = Column(String(36), ForeignKey("restaurants.id"), nullable=True, index=True)
    
    # Additional user details
    phone = Column(String(20), nullable=True)
    position = Column(String(100), nullable=True)
    pin = Column(String(10), nullable=True, index=True)  # For quick login
    hire_date = Column(DateTime(timezone=True), nullable=True)
    performance_score = Column(Integer, default=100)
    access_level = Column(String(20), default="limited")  # limited, full
    
    # Profile information
    avatar_url = Column(String(500), nullable=True)
    bio = Column(Text, nullable=True)
    preferences = Column(Text, nullable=True)  # JSON string
    
    # Security settings
    last_login = Column(DateTime(timezone=True), nullable=True)
    failed_login_attempts = Column(Integer, default=0)
    account_locked_until = Column(DateTime(timezone=True), nullable=True)
    password_changed_at = Column(DateTime(timezone=True), nullable=True)
    
    # Two-factor authentication
    two_factor_enabled = Column(Boolean, default=False)
    two_factor_secret = Column(String(100), nullable=True)
    
    # Relationships
    restaurant = relationship("Restaurant", back_populates="users")
    sessions = relationship("UserSession", back_populates="user", cascade="all, delete-orphan")
    orders = relationship("Order", back_populates="created_by_user")
    ai_requests = relationship("AIRequest", back_populates="user")
    
    def __repr__(self):
        return f"<User(id={self.id}, username={self.username}, email={self.email})>"

class UserSession(BaseModel, TimestampMixin):
    """
    User session tracking for authentication tokens.
    """
    __tablename__ = "user_sessions"
    
    # Session details
    user_id = Column(String(36), ForeignKey("users.id"), nullable=False, index=True)
    session_token = Column(String(500), nullable=False, unique=True, index=True)
    refresh_token = Column(String(500), nullable=True, unique=True, index=True)
    
    # Session metadata
    expires_at = Column(DateTime(timezone=True), nullable=False, index=True)
    is_active = Column(Boolean, default=True, nullable=False, index=True)
    
    # Device and location information
    device_info = Column(Text, nullable=True)  # JSON string
    ip_address = Column(String(45), nullable=True)  # IPv6 support
    user_agent = Column(Text, nullable=True)
    location = Column(String(255), nullable=True)
    
    # Session activity
    last_activity = Column(DateTime(timezone=True), nullable=True)
    login_method = Column(String(50), nullable=True)  # password, pin, oauth, etc.
    
    # Security flags
    is_revoked = Column(Boolean, default=False, nullable=False)
    revoked_at = Column(DateTime(timezone=True), nullable=True)
    revoked_reason = Column(String(255), nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="sessions")
    
    def __repr__(self):
        return f"<UserSession(id={self.id}, user_id={self.user_id}, active={self.is_active})>"
