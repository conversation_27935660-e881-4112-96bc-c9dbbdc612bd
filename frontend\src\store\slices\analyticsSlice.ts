/**
 * Analytics state slice for RestroManage
 * Manages historical data caching, forecast calculations, chart data, and category-based analytics
 */

import { StateCreator } from 'zustand';
import { 
  AnalyticsState,
  AnalyticsData,
  AnalyticsFilters,
  SalesDataPoint,
  RevenueDataPoint,
  PopularItem,
  CategoryPerformance,
  PeakHoursData,
  ProfitMarginData,
  ForecastDataPoint,
  ChartConfig
} from '@/types/store';
import { analyticsApi } from '@/services/api';
import logger from '@/utils/logger';

export interface AnalyticsSlice extends AnalyticsState {
  // State
  analyticsData: AnalyticsData;
  analyticsFilters: AnalyticsFilters;
  analyticsCachedData: Map<string, AnalyticsData>;
  chartConfigs: ChartConfig[];
  isAnalyticsLoading: boolean;
  analyticsError: string | null;
  lastAnalyticsUpdate: Date | null;
  exportFormats: string[];

  // Actions
  initializeAnalytics: (restaurantId: string) => Promise<void>;
  refreshAnalyticsData: () => Promise<void>;
  updateAnalyticsFilters: (filters: Partial<AnalyticsFilters>) => void;
  updateChartConfig: (chartId: string, config: Partial<ChartConfig>) => void;
  exportAnalyticsData: (format: string) => Promise<void>;
  clearAnalyticsCache: () => void;
  syncAnalytics: () => Promise<void>;
  resetAnalytics: () => void;
  clearAnalyticsData: (restaurantId: string) => void;
}

const initialAnalyticsData: AnalyticsData = {
  salesData: [],
  revenueData: [],
  popularItems: [],
  categoryPerformance: [],
  peakHoursData: [],
  profitMarginData: [],
  forecastData: [],
};

const initialAnalyticsFilters: AnalyticsFilters = {
  searchQuery: '',
  dateRange: {
    from: new Date(new Date().setDate(new Date().getDate() - 30)), // Last 30 days
    to: new Date(),
  },
  categories: [],
  status: [],
  period: 'daily',
  metrics: ['revenue', 'orders', 'customers'],
  compareWithPrevious: false,
};

const defaultChartConfigs: ChartConfig[] = [
  {
    id: 'sales-chart',
    type: 'line',
    dataKey: 'sales',
    color: '#3b82f6',
    visible: true,
  },
  {
    id: 'revenue-chart',
    type: 'bar',
    dataKey: 'revenue',
    color: '#10b981',
    visible: true,
  },
  {
    id: 'customers-chart',
    type: 'area',
    dataKey: 'customers',
    color: '#f59e0b',
    visible: true,
  },
  {
    id: 'category-performance',
    type: 'pie',
    dataKey: 'revenue',
    color: '#8b5cf6',
    visible: true,
  },
];

export const createAnalyticsSlice: StateCreator<
  AnalyticsSlice,
  [],
  [],
  AnalyticsSlice
> = (set, get) => ({
  // Initial state
  analyticsData: initialAnalyticsData,
  analyticsFilters: initialAnalyticsFilters,
  analyticsCachedData: new Map(),
  chartConfigs: defaultChartConfigs,
  isAnalyticsLoading: false,
  analyticsError: null,
  lastAnalyticsUpdate: null,
  exportFormats: ['CSV', 'PDF', 'Excel'],

  // Actions
  initializeAnalytics: async (restaurantId: string) => {
    try {
      set((state) => {
        state.isAnalyticsLoading = true;
        state.analyticsError = null;
      });

      logger.info('Initializing analytics', 'AnalyticsSlice', { restaurantId });

      const filters = get().analyticsFilters;
      const dateFrom = filters.dateRange.from?.toISOString().split('T')[0];
      const dateTo = filters.dateRange.to?.toISOString().split('T')[0];

      // Fetch all analytics data in parallel
      const [
        salesData,
        revenueData,
        popularItems,
        categoryPerformance,
        peakHoursData,
        profitMarginData,
        forecastData,
      ] = await Promise.allSettled([
        analyticsApi.getSalesData(dateFrom!, dateTo!, restaurantId),
        analyticsApi.getRevenueData(filters.period, restaurantId),
        analyticsApi.getPopularItems(10, restaurantId),
        analyticsApi.getCategoryPerformance(restaurantId, 30),
        analyticsApi.getPeakHoursAnalysis(restaurantId, 30),
        analyticsApi.getProfitMarginAnalysis(restaurantId, 30),
        analyticsApi.getForecastData(restaurantId),
      ]);

      set((state) => {
        const newData: AnalyticsData = {
          salesData: salesData.status === 'fulfilled' ? salesData.value : [],
          revenueData: revenueData.status === 'fulfilled' ? revenueData.value : [],
          popularItems: popularItems.status === 'fulfilled' ? popularItems.value : [],
          categoryPerformance: categoryPerformance.status === 'fulfilled' ? categoryPerformance.value : [],
          peakHoursData: peakHoursData.status === 'fulfilled' ? peakHoursData.value : [],
          profitMarginData: profitMarginData.status === 'fulfilled' ? profitMarginData.value : [],
          forecastData: forecastData.status === 'fulfilled' ? forecastData.value : [],
        };

        state.analyticsData = newData;

        // Cache the data
        const cacheKey = `${restaurantId}_${dateFrom}_${dateTo}_${filters.period}`;
        state.analyticsCachedData.set(cacheKey, newData);

        state.isAnalyticsLoading = false;
        state.lastAnalyticsUpdate = new Date();
      });

      logger.info('Analytics initialized successfully', 'AnalyticsSlice', { restaurantId });
    } catch (error) {
      set((state) => {
        state.isAnalyticsLoading = false;
        state.analyticsError = error instanceof Error ? error.message : 'Failed to initialize analytics';
      });

      logger.error('Failed to initialize analytics', 'AnalyticsSlice', { error, restaurantId });
      throw error;
    }
  },

  refreshAnalyticsData: async () => {
    const restaurantId = get().currentRestaurantId;
    if (!restaurantId) {
      logger.warn('Cannot refresh analytics without restaurant ID', 'AnalyticsSlice');
      return;
    }

    await get().initializeAnalytics(restaurantId);
  },

  updateAnalyticsFilters: (filters: Partial<AnalyticsFilters>) => {
    set((state) => {
      state.analyticsFilters = { ...state.analyticsFilters, ...filters };
    });

    // Refresh data with new filters
    const restaurantId = get().currentRestaurantId;
    if (restaurantId) {
      get().initializeAnalytics(restaurantId);
    }
  },

  updateChartConfig: (chartId: string, config: Partial<ChartConfig>) => {
    set((state) => {
      const index = state.chartConfigs.findIndex(chart => chart.id === chartId);
      if (index !== -1) {
        state.chartConfigs[index] = { ...state.chartConfigs[index], ...config };
      }
    });
  },

  exportAnalyticsData: async (format: string) => {
    const restaurantId = get().currentRestaurantId;
    if (!restaurantId) {
      logger.warn('Cannot export analytics without restaurant ID', 'AnalyticsSlice');
      return;
    }

    try {
      logger.info('Exporting analytics data', 'AnalyticsSlice', { format, restaurantId });

      const data = get().analyticsData;
      const filters = get().analyticsFilters;

      // Create export data structure
      const exportData = {
        restaurant_id: restaurantId,
        date_range: filters.dateRange,
        period: filters.period,
        metrics: filters.metrics,
        data: {
          sales: data.salesData,
          revenue: data.revenueData,
          popular_items: data.popularItems,
          category_performance: data.categoryPerformance,
          peak_hours: data.peakHoursData,
          profit_margins: data.profitMarginData,
          forecasts: data.forecastData,
        },
        exported_at: new Date().toISOString(),
      };

      // Handle different export formats
      switch (format.toLowerCase()) {
        case 'csv':
          await exportToCSV(exportData);
          break;
        case 'pdf':
          await exportToPDF(exportData);
          break;
        case 'excel':
          await exportToExcel(exportData);
          break;
        default:
          throw new Error(`Unsupported export format: ${format}`);
      }

      logger.info('Analytics data exported successfully', 'AnalyticsSlice', { format, restaurantId });
    } catch (error) {
      logger.error('Failed to export analytics data', 'AnalyticsSlice', { error, format, restaurantId });
      throw error;
    }
  },

  clearAnalyticsCache: () => {
    set((state) => {
      state.analyticsCachedData.clear();
    });
  },

  syncAnalytics: async () => {
    const restaurantId = get().currentRestaurantId;
    if (!restaurantId) return;

    await get().refreshAnalyticsData();
  },

  resetAnalytics: () => {
    set((state) => {
      state.analyticsData = initialAnalyticsData;
      state.analyticsFilters = initialAnalyticsFilters;
      state.analyticsCachedData.clear();
      state.chartConfigs = defaultChartConfigs;
      state.isAnalyticsLoading = false;
      state.analyticsError = null;
      state.lastAnalyticsUpdate = null;
    });
  },

  clearAnalyticsData: (restaurantId: string) => {
    set((state) => {
      // Clear cached data for specific restaurant
      const keysToDelete: string[] = [];
      state.analyticsCachedData.forEach((_, key) => {
        if (key.startsWith(restaurantId)) {
          keysToDelete.push(key);
        }
      });
      keysToDelete.forEach(key => state.analyticsCachedData.delete(key));

      if (state.currentRestaurantId === restaurantId) {
        state.analyticsData = initialAnalyticsData;
      }
    });
  },
});

// Helper functions for export functionality
async function exportToCSV(data: any): Promise<void> {
  // Implementation for CSV export
  const csvContent = convertToCSV(data);
  downloadFile(csvContent, 'analytics-data.csv', 'text/csv');
}

async function exportToPDF(data: any): Promise<void> {
  // Implementation for PDF export
  // This would typically use a library like jsPDF
  logger.info('PDF export not yet implemented', 'AnalyticsSlice');
}

async function exportToExcel(data: any): Promise<void> {
  // Implementation for Excel export
  // This would typically use a library like xlsx
  logger.info('Excel export not yet implemented', 'AnalyticsSlice');
}

function convertToCSV(data: any): string {
  // Simple CSV conversion - in a real implementation, this would be more sophisticated
  const headers = ['Date', 'Sales', 'Revenue', 'Orders', 'Customers'];
  const rows = data.data.sales.map((item: any) => [
    item.date,
    item.sales,
    item.revenue || 0,
    item.orders,
    item.customers || 0,
  ]);

  return [headers, ...rows].map(row => row.join(',')).join('\n');
}

function downloadFile(content: string, filename: string, mimeType: string): void {
  const blob = new Blob([content], { type: mimeType });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
}
