{"timestamp": "2025-06-21T15:50:00Z", "level": "ERROR", "component": "TestComponent", "message": "Test error message", "sessionId": "test-session-123", "url": "http://localhost:3000/test", "data": null, "browser": null, "screen": null, "viewport": null}
{"timestamp": "2025-06-21T16:30:06.024Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750523139577_925n86ptw", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "apiRequest@http://localhost:5175/src/services/apiService.ts?t=1750523403787:72:19\n", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1536, "height": 864, "colorDepth": 24}, "viewport": {"width": 1485, "height": 703}}
{"timestamp": "2025-06-21T16:35:28.458Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750523727233_5mkhrkhfz", "url": "http://localhost:5175/", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:72:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-21T16:35:28.505Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750523727233_5mkhrkhfz", "url": "http://localhost:5175/", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:72:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-21T16:35:46.210Z", "level": "error", "component": "AIAssistantService", "message": "AI query failed", "sessionId": "session_1750523727233_5mkhrkhfz", "url": "http://localhost:5175/admin/assistant", "data": {"error": "Not authenticated", "error_type": "Error", "restaurant_id": "1", "query_length": 2, "timestamp": "2025-06-21T16:35:46.207Z"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-21T16:39:35.505Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750523727233_5mkhrkhfz", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts?t=1750523971869:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx?t=1750523971763:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-21T16:44:53.035Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750524292089_4rtoiupm4", "url": "http://localhost:5175/", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts?t=1750523971869:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx?t=1750523971869:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-21T16:44:53.103Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750524292089_4rtoiupm4", "url": "http://localhost:5175/", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts?t=1750523971869:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx?t=1750523971869:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-21T16:48:32.209Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750524292089_4rtoiupm4", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts?t=1750523971869:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx?t=1750523971869:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1280, "height": 800, "colorDepth": 24}, "viewport": {"width": 944, "height": 735}}
{"timestamp": "2025-06-21T16:48:32.284Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750524292089_4rtoiupm4", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts?t=1750523971869:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx?t=1750523971869:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1280, "height": 800, "colorDepth": 24}, "viewport": {"width": 944, "height": 735}}
{"timestamp": "2025-06-21T16:56:18.420Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750524292089_4rtoiupm4", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1280, "height": 800, "colorDepth": 24}, "viewport": {"width": 944, "height": 735}}
{"timestamp": "2025-06-21T16:56:18.673Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750524292089_4rtoiupm4", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1280, "height": 800, "colorDepth": 24}, "viewport": {"width": 944, "height": 735}}
{"timestamp": "2025-06-21T16:56:22.872Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750524981760_aqkgq86lj", "url": "http://localhost:5175/", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-21T16:56:23.164Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750524981760_aqkgq86lj", "url": "http://localhost:5175/", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-21T16:57:36.161Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750524981760_aqkgq86lj", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-21T16:57:36.224Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750524981760_aqkgq86lj", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-21T16:58:07.298Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750523727233_5mkhrkhfz", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-21T16:58:07.411Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750523727233_5mkhrkhfz", "url": "http://localhost:5175/admin/assistant", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "Error: HTTP 500 - Internal Server Error\n    at apiRequest (http://localhost:5175/src/services/apiService.ts:92:19)\n    at async Promise.all (index 1)\n    at async loadData (http://localhost:5175/src/contexts/AuthContext.tsx:308:56)", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1680, "height": 1050, "colorDepth": 24}, "viewport": {"width": 1536, "height": 735}}
{"timestamp": "2025-06-21T16:58:32.438Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750525112043_5wln5m8xs", "url": "http://localhost:5175/", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "apiRequest@http://localhost:5175/src/services/apiService.ts:92:19\n", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1536, "height": 864, "colorDepth": 24}, "viewport": {"width": 1485, "height": 703}}
{"timestamp": "2025-06-21T16:58:32.452Z", "level": "error", "component": "AuthContext", "message": "Error in loading data from backend API", "sessionId": "session_1750525112043_5wln5m8xs", "url": "http://localhost:5175/", "data": {"name": "Error", "message": "HTTP 500 - Internal Server Error", "stack": "apiRequest@http://localhost:5175/src/services/apiService.ts:92:19\n", "context": "loading data from backend API"}, "browser": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0", "language": "en-US", "platform": "Win32", "cookieEnabled": true, "onLine": true}, "screen": {"width": 1536, "height": 864, "colorDepth": 24}, "viewport": {"width": 1485, "height": 703}}
