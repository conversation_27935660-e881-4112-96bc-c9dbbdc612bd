// Define the task interface
export interface Task {
  id: string;
  title: string;
  description: string;
  category: "cleaning" | "cutting" | "refilling" | "other";
  assignedTo?: string; // staff ID (optional)
  assignedBy: string; // admin ID
  status: "pending" | "in-progress" | "completed";
  priority: "low" | "medium" | "high";
  createdAt: string; // ISO date
  dueDate: string; // ISO date
  startedAt?: string; // ISO date when task was started (optional)
  completedAt: string | null;
  completedBy?: string; // staff ID who completed the task (optional)
}

// Define the task template interface
export interface TaskTemplate {
  id: string;
  title: string;
  description: string;
  category: "cleaning" | "cutting" | "refilling" | "other";
  priority: "low" | "medium" | "high";
  estimatedTime: string;
}

// Storage keys
const TASKS_KEY = 'staffTasks';
const TEMPLATES_KEY = 'taskTemplates';

// Get all tasks from localStorage
export const getAllTasks = (): Task[] => {
  const tasksJson = localStorage.getItem(TASKS_KEY);
  return tasksJson ? JSON.parse(tasksJson) : [];
};

// Get tasks by category
export const getTasksByCategory = (category: Task['category']): Task[] => {
  const allTasks = getAllTasks();
  return allTasks.filter(task => task.category === category);
};

// Get tasks by staff member
export const getTasksByStaff = (staffId: string): Task[] => {
  const allTasks = getAllTasks();
  return allTasks.filter(task => task.assignedTo === staffId);
};

// Get tasks by status
export const getTasksByStatus = (status: Task['status']): Task[] => {
  const allTasks = getAllTasks();
  return allTasks.filter(task => task.status === status);
};

// Add a new task
export const addTask = (task: Omit<Task, 'id' | 'createdAt'>): Task => {
  const allTasks = getAllTasks();

  // Create a new task with generated ID and timestamp
  const newTask: Task = {
    ...task,
    id: Date.now().toString(),
    createdAt: new Date().toISOString(),
  };

  allTasks.push(newTask);
  localStorage.setItem(TASKS_KEY, JSON.stringify(allTasks));

  return newTask;
};

// Update an existing task
export const updateTask = (updatedTask: Task): void => {
  const allTasks = getAllTasks();
  const index = allTasks.findIndex(task => task.id === updatedTask.id);

  if (index !== -1) {
    allTasks[index] = updatedTask;
    localStorage.setItem(TASKS_KEY, JSON.stringify(allTasks));
  }
};

// Delete a task
export const deleteTask = (taskId: string): void => {
  const allTasks = getAllTasks();
  const filteredTasks = allTasks.filter(task => task.id !== taskId);
  localStorage.setItem(TASKS_KEY, JSON.stringify(filteredTasks));
};

// Mark a task as completed
export const markTaskAsCompleted = (taskId: string, completedBy?: string): Task | null => {
  console.log(`Marking task ${taskId} as completed by ${completedBy}`);

  try {
    const allTasks = getAllTasks();
    const index = allTasks.findIndex(task => task.id === taskId);

    if (index !== -1) {
      // Create the updated task with explicit completedBy field
      const updatedTask: Task = {
        ...allTasks[index],
        status: "completed" as const,
        completedAt: new Date().toISOString(),
        completedBy: completedBy || undefined
      };

      // Log the task before and after update
      console.log('Task before update:', allTasks[index]);
      console.log('Task after update:', updatedTask);

      // Update the task in the array
      allTasks[index] = updatedTask;

      // Save to localStorage
      localStorage.setItem(TASKS_KEY, JSON.stringify(allTasks));

      // Verify the task was updated correctly
      const verifyTasks = getAllTasks();
      const verifyTask = verifyTasks.find(t => t.id === taskId);
      console.log('Task after saving to localStorage:', verifyTask);

      // Return the updated task
      return updatedTask;
    } else {
      console.error(`Task with ID ${taskId} not found`);
      return null;
    }
  } catch (error) {
    console.error('Error marking task as completed:', error);
    return null;
  }
};

// Mark a task as in-progress
export const markTaskAsInProgress = (taskId: string, startedBy?: string): Task | null => {
  console.log(`Marking task ${taskId} as in-progress by ${startedBy}`);

  try {
    const allTasks = getAllTasks();
    const index = allTasks.findIndex(task => task.id === taskId);

    if (index !== -1) {
      // Create the updated task with explicit assignedTo field
      const updatedTask: Task = {
        ...allTasks[index],
        status: "in-progress" as const,
        assignedTo: startedBy || allTasks[index].assignedTo,
        startedAt: new Date().toISOString()
      };

      // Log the task before and after update
      console.log('Task before update:', allTasks[index]);
      console.log('Task after update:', updatedTask);

      // Update the task in the array
      allTasks[index] = updatedTask;

      // Save to localStorage
      localStorage.setItem(TASKS_KEY, JSON.stringify(allTasks));

      // Verify the task was updated correctly
      const verifyTasks = getAllTasks();
      const verifyTask = verifyTasks.find(t => t.id === taskId);
      console.log('Task after saving to localStorage:', verifyTask);

      // Return the updated task
      return updatedTask;
    } else {
      console.error(`Task with ID ${taskId} not found`);
      return null;
    }
  } catch (error) {
    console.error('Error marking task as in-progress:', error);
    return null;
  }
};

// Generate mock tasks for testing
export const generateMockTasks = (staffData: any[]): Task[] => {
  const tasks: Task[] = [];
  const categories: Task['category'][] = ["cleaning", "cutting", "refilling", "other"];
  const priorities: Task['priority'][] = ["low", "medium", "high"];
  const statuses: Task['status'][] = ["pending", "in-progress", "completed"];

  // Generate 20 mock tasks
  for (let i = 0; i < 20; i++) {
    const category = categories[Math.floor(Math.random() * categories.length)];
    const priority = priorities[Math.floor(Math.random() * priorities.length)];
    const status = statuses[Math.floor(Math.random() * statuses.length)];

    // Generate a due date between today and 7 days from now
    const dueDate = new Date();
    dueDate.setDate(dueDate.getDate() + Math.floor(Math.random() * 7) + 1);

    // Generate a created date between 7 days ago and today
    const createdDate = new Date();
    createdDate.setDate(createdDate.getDate() - Math.floor(Math.random() * 7));

    // For completed or in-progress tasks, randomly assign to staff
    let assignedTo = undefined;
    let completedBy = undefined;
    let completedAt = null;

    if (status === "in-progress" || status === "completed") {
      // 50% chance to have an assigned staff member
      if (Math.random() > 0.5) {
        const staffMember = staffData[Math.floor(Math.random() * staffData.length)];
        assignedTo = staffMember.id;
      }
    }

    if (status === "completed") {
      // Always have a completedBy for completed tasks
      const staffMember = staffData[Math.floor(Math.random() * staffData.length)];
      completedBy = staffMember.id;

      // Generate a completed date
      const completedDate = new Date(createdDate);
      completedDate.setDate(completedDate.getDate() + Math.floor(Math.random() * 3) + 1);
      completedAt = completedDate.toISOString();
    }

    // Create task title based on category
    let title = "";
    switch (category) {
      case "cleaning":
        title = `Clean ${["kitchen", "dining area", "restrooms", "bar", "storage room"][Math.floor(Math.random() * 5)]}`;
        break;
      case "cutting":
        title = `Prep ${["vegetables", "meat", "fruits", "herbs", "garnishes"][Math.floor(Math.random() * 5)]}`;
        break;
      case "refilling":
        title = `Refill ${["condiments", "napkins", "utensils", "glasses", "ice"][Math.floor(Math.random() * 5)]}`;
        break;
      case "other":
        title = `${["Check inventory", "Organize storage", "Update menu", "Staff training", "Equipment maintenance"][Math.floor(Math.random() * 5)]}`;
        break;
    }

    // Create detailed descriptions based on the task
    let description = "";
    switch (category) {
      case "cleaning":
        if (title.includes("kitchen")) {
          description = "Clean all surfaces, equipment, and floors. Empty trash bins. Sanitize food preparation areas. Check cleaning supplies inventory.";
        } else if (title.includes("dining")) {
          description = "Wipe down all tables and chairs. Sweep and mop floors. Clean windows and mirrors. Arrange furniture neatly.";
        } else if (title.includes("restrooms")) {
          description = "Clean toilets, sinks, and mirrors. Restock toilet paper, paper towels, and soap. Mop floors and empty trash bins.";
        } else if (title.includes("bar")) {
          description = "Wipe down bar top and shelves. Clean glasses and equipment. Organize bottles and supplies. Check stock levels.";
        } else {
          description = "Organize shelves and clean floors. Check for expired items. Ensure proper storage of all items.";
        }
        break;
      case "cutting":
        if (title.includes("vegetables")) {
          description = "Wash and prep vegetables according to prep list. Store in labeled containers with date. Follow proper knife safety procedures.";
        } else if (title.includes("meat")) {
          description = "Trim and portion meat according to recipes. Store properly with labels and dates. Sanitize all surfaces after completion.";
        } else {
          description = "Prepare items according to recipe specifications. Label and date all containers. Follow food safety guidelines.";
        }
        break;
      case "refilling":
        description = "Check inventory levels and refill as needed. Clean containers before refilling. Report any low stock items to manager.";
        break;
      case "other":
        if (title.includes("inventory")) {
          description = "Count all stock items and update inventory system. Note any discrepancies. Identify items that need to be ordered.";
        } else if (title.includes("menu")) {
          description = "Review current menu items. Update prices and availability. Ensure menu boards and printed menus are current.";
        } else {
          description = "Complete task according to restaurant standards. Document any issues encountered. Report completion to manager.";
        }
        break;
    }

    tasks.push({
      id: (i + 1).toString(),
      title,
      description,
      category,
      assignedTo,
      assignedBy: "1", // Assuming admin ID is 1
      status,
      priority,
      createdAt: createdDate.toISOString(),
      dueDate: dueDate.toISOString(),
      completedAt,
      completedBy
    });
  }

  // Add specific common tasks that should always be present
  const commonTasks = [
    // Cleaning tasks
    {
      id: "101",
      title: "Morning Kitchen Cleaning",
      description: "Complete full kitchen cleaning before opening. Sanitize all surfaces, check equipment, and prepare stations for service.",
      category: "cleaning" as const,
      assignedBy: "1",
      status: "pending" as const,
      priority: "high" as const,
      createdAt: new Date().toISOString(),
      dueDate: new Date(new Date().setHours(10, 0, 0, 0)).toISOString(),
      completedAt: null
    },
    {
      id: "102",
      title: "Evening Floor Cleaning",
      description: "Sweep and mop all floors after closing. Ensure all areas are clean and ready for the next day's service.",
      category: "cleaning" as const,
      assignedBy: "1",
      status: "pending" as const,
      priority: "high" as const,
      createdAt: new Date().toISOString(),
      dueDate: new Date(new Date().setHours(22, 0, 0, 0)).toISOString(),
      completedAt: null
    },
    {
      id: "106",
      title: "Clean Refrigerators",
      description: "Clean and organize all refrigerators. Check for expired items and proper storage of all food items.",
      category: "cleaning" as const,
      assignedBy: "1",
      status: "pending" as const,
      priority: "medium" as const,
      createdAt: new Date().toISOString(),
      dueDate: new Date(new Date().setHours(15, 0, 0, 0)).toISOString(),
      completedAt: null
    },
    // Cutting tasks
    {
      id: "103",
      title: "Daily Vegetable Prep",
      description: "Prepare all vegetables needed for today's service according to the prep list. Label and date all containers.",
      category: "cutting" as const,
      assignedBy: "1",
      status: "pending" as const,
      priority: "medium" as const,
      createdAt: new Date().toISOString(),
      dueDate: new Date(new Date().setHours(11, 0, 0, 0)).toISOString(),
      completedAt: null
    },
    {
      id: "107",
      title: "Meat Preparation",
      description: "Trim and portion all meat items according to recipes. Follow proper food safety guidelines.",
      category: "cutting" as const,
      assignedBy: "1",
      status: "pending" as const,
      priority: "high" as const,
      createdAt: new Date().toISOString(),
      dueDate: new Date(new Date().setHours(12, 0, 0, 0)).toISOString(),
      completedAt: null
    },
    // Refilling tasks
    {
      id: "104",
      title: "Refill Condiment Stations",
      description: "Check and refill all condiment stations. Clean containers and ensure all items are fully stocked.",
      category: "refilling" as const,
      assignedBy: "1",
      status: "pending" as const,
      priority: "low" as const,
      createdAt: new Date().toISOString(),
      dueDate: new Date(new Date().setHours(16, 0, 0, 0)).toISOString(),
      completedAt: null
    },
    {
      id: "108",
      title: "Restock Bar Supplies",
      description: "Check and restock all bar supplies including glasses, napkins, and garnishes.",
      category: "refilling" as const,
      assignedBy: "1",
      status: "pending" as const,
      priority: "medium" as const,
      createdAt: new Date().toISOString(),
      dueDate: new Date(new Date().setHours(17, 0, 0, 0)).toISOString(),
      completedAt: null
    },
    // Other tasks
    {
      id: "105",
      title: "Weekly Inventory Check",
      description: "Complete full inventory count of all items. Update inventory system and prepare order list for any low stock items.",
      category: "other" as const,
      assignedBy: "1",
      status: "pending" as const,
      priority: "medium" as const,
      createdAt: new Date().toISOString(),
      dueDate: new Date(new Date().setDate(new Date().getDate() + 2)).toISOString(),
      completedAt: null
    },
    {
      id: "109",
      title: "Staff Training Session",
      description: "Conduct training session for new menu items and service procedures.",
      category: "other" as const,
      assignedBy: "1",
      status: "pending" as const,
      priority: "high" as const,
      createdAt: new Date().toISOString(),
      dueDate: new Date(new Date().setDate(new Date().getDate() + 1)).toISOString(),
      completedAt: null
    }
  ];

  return [...tasks, ...commonTasks];
};

// Initialize mock tasks in localStorage
export const initializeMockTasks = (staffData: any[]): void => {
  try {
    // Only initialize if no tasks exist yet
    if (typeof window !== 'undefined' && window.localStorage) {
      // Clear existing tasks to ensure proper initialization
      localStorage.removeItem(TASKS_KEY);

      // Generate and save new tasks
      const mockTasks = generateMockTasks(staffData);
      localStorage.setItem(TASKS_KEY, JSON.stringify(mockTasks));
      console.log(`Initialized ${mockTasks.length} mock tasks`);
    } else {
      console.error('localStorage is not available');
    }
  } catch (error) {
    console.error('Error initializing mock tasks:', error);
  }
};

// Default task templates
const defaultTaskTemplates: TaskTemplate[] = [
  {
    id: "cleaning-kitchen",
    title: "Clean Kitchen",
    description: "Clean all kitchen surfaces, equipment, and floors. Dispose of trash and sanitize all food preparation areas.",
    category: "cleaning",
    priority: "high",
    estimatedTime: "60 min"
  },
  {
    id: "cleaning-dining",
    title: "Clean Dining Area",
    description: "Wipe down all tables, chairs, and counters. Sweep and mop floors. Ensure all condiments are refilled.",
    category: "cleaning",
    priority: "high",
    estimatedTime: "45 min"
  },
  {
    id: "cleaning-restrooms",
    title: "Clean Restrooms",
    description: "Clean toilets, sinks, mirrors, and floors. Restock toilet paper, paper towels, and soap. Empty trash bins.",
    category: "cleaning",
    priority: "high",
    estimatedTime: "30 min"
  },
  {
    id: "cutting-vegetables",
    title: "Prep Vegetables",
    description: "Wash, peel, and cut vegetables according to prep list. Store in labeled containers with date.",
    category: "cutting",
    priority: "medium",
    estimatedTime: "45 min"
  },
  {
    id: "cutting-meat",
    title: "Prep Meat",
    description: "Trim, portion, and prepare meat according to recipes. Store properly with labels and dates.",
    category: "cutting",
    priority: "medium",
    estimatedTime: "60 min"
  },
  {
    id: "refilling-condiments",
    title: "Refill Condiments",
    description: "Check and refill all condiment containers, salt, pepper, sugar, etc.",
    category: "refilling",
    priority: "low",
    estimatedTime: "20 min"
  },
  {
    id: "refilling-bar",
    title: "Restock Bar",
    description: "Check inventory and restock bar with necessary liquor, mixers, and garnishes.",
    category: "refilling",
    priority: "medium",
    estimatedTime: "30 min"
  },
  {
    id: "other-inventory",
    title: "Inventory Check",
    description: "Perform inventory count of all items and update inventory management system.",
    category: "other",
    priority: "medium",
    estimatedTime: "90 min"
  }
];

// Get all task templates
export const getTaskTemplates = (): TaskTemplate[] => {
  const templatesJson = localStorage.getItem(TEMPLATES_KEY);
  if (templatesJson) {
    return JSON.parse(templatesJson);
  } else {
    // Initialize with default templates if none exist
    localStorage.setItem(TEMPLATES_KEY, JSON.stringify(defaultTaskTemplates));
    return defaultTaskTemplates;
  }
};

// Add a new task template
export const addTaskTemplate = (template: Omit<TaskTemplate, 'id'>): TaskTemplate => {
  const allTemplates = getTaskTemplates();

  // Create a new template with generated ID
  const newTemplate: TaskTemplate = {
    ...template,
    id: `${template.category}-${Date.now()}`
  };

  allTemplates.push(newTemplate);
  localStorage.setItem(TEMPLATES_KEY, JSON.stringify(allTemplates));

  return newTemplate;
};

// Update an existing task template
export const updateTaskTemplate = (updatedTemplate: TaskTemplate): void => {
  const allTemplates = getTaskTemplates();
  const index = allTemplates.findIndex(template => template.id === updatedTemplate.id);

  if (index !== -1) {
    allTemplates[index] = updatedTemplate;
    localStorage.setItem(TEMPLATES_KEY, JSON.stringify(allTemplates));
  }
};

// Delete a task template
export const deleteTaskTemplate = (templateId: string): void => {
  const allTemplates = getTaskTemplates();
  const filteredTemplates = allTemplates.filter(template => template.id !== templateId);
  localStorage.setItem(TEMPLATES_KEY, JSON.stringify(filteredTemplates));
};

// Create a task from a template
export const createTaskFromTemplate = (templateId: string, dueDate: string, assignedTo?: string, assignedBy: string = "1"): Task => {
  const templates = getTaskTemplates();
  const template = templates.find(t => t.id === templateId);

  if (!template) {
    throw new Error(`Template with ID ${templateId} not found`);
  }

  const taskData: Omit<Task, 'id' | 'createdAt'> = {
    title: template.title,
    description: template.description,
    category: template.category,
    assignedBy,
    status: "pending",
    priority: template.priority,
    dueDate,
    completedAt: null
  };

  // Only add assignedTo if it's provided
  if (assignedTo) {
    taskData.assignedTo = assignedTo;
  }

  return addTask(taskData);
};
