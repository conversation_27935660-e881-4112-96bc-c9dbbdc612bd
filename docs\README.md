# RestroManage-V1 Documentation

Welcome to the comprehensive documentation for RestroManage-V1, a modern restaurant management platform built with React (Vite) frontend and FastAPI backend.

## 📚 Documentation Structure

### Core Documentation
- **[API Documentation](./api-documentation.md)** - Complete API endpoint reference
- **[Architecture Overview](./architecture-overview.md)** - System design and component interaction
- **[Development Setup](./development-setup.md)** - Local development environment setup
- **[Proxy Configuration](./proxy-configuration.md)** - Frontend-backend proxy setup guide

### Troubleshooting & Maintenance
- **[Troubleshooting Guide](./troubleshooting-guide.md)** - Common issues and solutions
- **[Connection Issues](./connection-issues.md)** - Proxy and network connectivity problems
- **[Deployment Guide](./deployment-guide.md)** - Production deployment instructions

### Development Guides
- **[Frontend Development](./frontend-development.md)** - React/Vite development guide
- **[Backend Development](./backend-development.md)** - FastAPI development guide
- **[Database Management](./database-management.md)** - Database setup and migrations

## 🚀 Quick Start

1. **Setup Environment**: Follow [Development Setup](./development-setup.md)
2. **Fix Proxy Issues**: Check [Proxy Configuration](./proxy-configuration.md)
3. **Troubleshoot**: Use [Troubleshooting Guide](./troubleshooting-guide.md)

## 🔧 Current Known Issues

### Proxy Connection Issues (RESOLVED)
- **Problem**: Frontend (port 5175) cannot connect to backend
- **Root Cause**: Port mismatch - frontend proxies to 5001, backend runs on 5002
- **Solution**: See [Connection Issues](./connection-issues.md) for detailed fix

## 📋 System Requirements

- **Frontend**: Node.js 18+, npm/yarn
- **Backend**: Python 3.11+, FastAPI, SQLite/PostgreSQL
- **Development**: Git, VS Code (recommended)

## 🤝 Contributing

1. Read the [Architecture Overview](./architecture-overview.md)
2. Follow [Development Setup](./development-setup.md)
3. Check [Troubleshooting Guide](./troubleshooting-guide.md) for common issues

## 📞 Support

For technical issues:
1. Check [Troubleshooting Guide](./troubleshooting-guide.md)
2. Review [Connection Issues](./connection-issues.md)
3. Consult [API Documentation](./api-documentation.md)

---

**Last Updated**: July 17, 2025  
**Version**: 1.0.0
