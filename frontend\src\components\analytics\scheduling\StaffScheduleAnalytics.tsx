import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardDescription } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Calendar, Users, Clock, TrendingUp, CalendarDays } from "lucide-react";
import { ForecastData } from "@/components/dashboard/ForecastCard";
import StaffUtilizationChart from "./StaffUtilizationChart";
import LaborCostAnalysis from "./LaborCostAnalysis";

interface StaffMember {
  id: string;
  name: string;
  role: string;
  position: string;
  status?: 'active' | 'inactive';
  availableDays?: string[];
  assignedHours?: number;
}

interface StaffScheduleAnalyticsProps {
  forecastData: ForecastData[];
  staffData?: StaffMember[];
}

export const StaffScheduleAnalytics = ({
  forecastData,
  staffData = []
}: StaffScheduleAnalyticsProps) => {
  const [timeframe, setTimeframe] = useState<"week" | "month" | "quarter">("week");
  
  // Generate mock staff data if none provided
  const mockStaffData: StaffMember[] = staffData.length > 0 ? staffData : [
    { id: "1", name: "John Smith", role: "Waiter", position: "Front of House", status: "active", assignedHours: 40 },
    { id: "2", name: "Sarah Johnson", role: "Chef", position: "Kitchen", status: "active", assignedHours: 45 },
    { id: "3", name: "Michael Brown", role: "Bartender", position: "Bar", status: "active", assignedHours: 35 },
    { id: "4", name: "Emily Davis", role: "Host", position: "Front of House", status: "active", assignedHours: 30 },
    { id: "5", name: "David Wilson", role: "Kitchen Assistant", position: "Kitchen", status: "active", assignedHours: 40 },
    { id: "6", name: "Jessica Taylor", role: "Waiter", position: "Front of House", status: "active", assignedHours: 25 },
    { id: "7", name: "Robert Martinez", role: "Chef", position: "Kitchen", status: "active", assignedHours: 45 },
    { id: "8", name: "Lisa Anderson", role: "Bartender", position: "Bar", status: "active", assignedHours: 30 }
  ];

  // Calculate optimal staffing based on forecast data
  const calculateOptimalStaffing = () => {
    return forecastData.map(day => {
      const customers = day.customers || 0;
      // Calculate staff needed based on customer volume
      // This is a simplified model - in a real system this would be more complex
      const waiters = Math.ceil(customers / 15); // 1 waiter per 15 customers
      const kitchen = Math.ceil(customers / 20); // 1 kitchen staff per 20 customers
      const bar = Math.ceil(customers / 30); // 1 bar staff per 30 customers
      const host = Math.ceil(customers / 50); // 1 host per 50 customers
      
      return {
        day: day.day,
        customers,
        totalStaff: waiters + kitchen + bar + host,
        breakdown: { waiters, kitchen, bar, host }
      };
    });
  };

  const optimalStaffing = calculateOptimalStaffing();
  
  // Calculate average staff needed per day
  const averageStaffNeeded = Math.round(
    optimalStaffing.reduce((sum, day) => sum + day.totalStaff, 0) / optimalStaffing.length
  );
  
  // Calculate peak staffing day
  const peakStaffingDay = optimalStaffing.reduce(
    (max, day) => day.totalStaff > max.totalStaff ? day : max,
    optimalStaffing[0]
  );
  
  // Calculate labor cost estimate (£ per hour average wage * total staff hours)
  const averageHourlyWage = 12.50; // £12.50 per hour
  const estimatedWeeklyHours = optimalStaffing.reduce(
    (sum, day) => sum + (day.totalStaff * 8), // Assuming 8-hour shifts
    0
  );
  const estimatedWeeklyCost = estimatedWeeklyHours * averageHourlyWage;

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <CardTitle className="text-xl font-bold">Staff Scheduling Analytics</CardTitle>
            <CardDescription>
              AI-powered staff scheduling recommendations based on forecasted demand
            </CardDescription>
          </div>
          <Select value={timeframe} onValueChange={(value: "week" | "month" | "quarter") => setTimeframe(value)}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select timeframe" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="week">Next 7 Days</SelectItem>
              <SelectItem value="month">Next 30 Days</SelectItem>
              <SelectItem value="quarter">Next 90 Days</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Average Staff Needed</p>
                  <p className="text-2xl font-bold">{averageStaffNeeded}</p>
                </div>
                <Users className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Peak Day Staffing</p>
                  <p className="text-2xl font-bold">{peakStaffingDay.totalStaff}</p>
                  <p className="text-xs text-muted-foreground">{peakStaffingDay.day}</p>
                </div>
                <TrendingUp className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Weekly Labor Hours</p>
                  <p className="text-2xl font-bold">{estimatedWeeklyHours}</p>
                </div>
                <Clock className="h-8 w-8 text-amber-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Est. Weekly Cost</p>
                  <p className="text-2xl font-bold">£{estimatedWeeklyCost.toLocaleString(undefined, { maximumFractionDigits: 0 })}</p>
                </div>
                <span className="h-8 w-8 flex items-center justify-center text-2xl font-bold text-red-500">£</span>
              </div>
            </CardContent>
          </Card>
        </div>
        
        <Tabs defaultValue="utilization" className="w-full">
          <TabsList>
            <TabsTrigger value="utilization">Staff Utilization</TabsTrigger>
            <TabsTrigger value="costs">Labor Costs</TabsTrigger>
            <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
          </TabsList>
          
          <TabsContent value="utilization" className="space-y-4">
            <StaffUtilizationChart 
              forecastData={forecastData} 
              staffData={mockStaffData} 
              timeframe={timeframe}
            />
          </TabsContent>
          
          <TabsContent value="costs" className="space-y-4">
            <LaborCostAnalysis 
              forecastData={forecastData} 
              staffData={mockStaffData}
              timeframe={timeframe}
            />
          </TabsContent>
          
          <TabsContent value="recommendations" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">AI-Generated Scheduling Recommendations</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <h3 className="font-medium">Staffing Optimization</h3>
                  <p className="text-sm text-muted-foreground">
                    Based on forecasted customer traffic, we recommend adjusting your staffing levels to match demand patterns.
                    Increase staffing by {Math.round((peakStaffingDay.totalStaff / averageStaffNeeded - 1) * 100)}% on {peakStaffingDay.day} to handle the expected peak.
                  </p>
                </div>
                
                <div className="space-y-2">
                  <h3 className="font-medium">Role Distribution</h3>
                  <p className="text-sm text-muted-foreground">
                    Optimal staff distribution for peak days:
                  </p>
                  <ul className="text-sm text-muted-foreground list-disc pl-5">
                    <li>Front of House: {peakStaffingDay.breakdown.waiters + peakStaffingDay.breakdown.host} staff members</li>
                    <li>Kitchen: {peakStaffingDay.breakdown.kitchen} staff members</li>
                    <li>Bar: {peakStaffingDay.breakdown.bar} staff members</li>
                  </ul>
                </div>
                
                <div className="space-y-2">
                  <h3 className="font-medium">Cost Reduction Opportunities</h3>
                  <p className="text-sm text-muted-foreground">
                    Potential savings of £{Math.round(estimatedWeeklyCost * 0.15).toLocaleString()} per week by optimizing shift start/end times
                    to better match customer traffic patterns.
                  </p>
                </div>
                
                <Button className="w-full mt-4">
                  <CalendarDays className="mr-2 h-4 w-4" />
                  Generate Optimized Schedule
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default StaffScheduleAnalytics;
