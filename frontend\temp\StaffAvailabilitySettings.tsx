import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "@/components/ui/sonner";
import { Calendar, Clock, Save } from "lucide-react";

interface StaffAvailabilitySettingsProps {
  staffId: string;
  staffName: string;
  initialAssignedHours?: number;
  initialAvailableDays?: string[];
}

const daysOfWeek = [
  { id: "mon", label: "Monday" },
  { id: "tue", label: "Tuesday" },
  { id: "wed", label: "Wednesday" },
  { id: "thu", label: "Thursday" },
  { id: "fri", label: "Friday" },
  { id: "sat", label: "Saturday" },
  { id: "sun", label: "Sunday" },
];

const StaffAvailabilitySettings = ({ 
  staffId, 
  staffName, 
  initialAssignedHours = 35, 
  initialAvailableDays = ["mon", "tue", "wed", "thu", "fri"] 
}: StaffAvailabilitySettingsProps) => {
  const [assignedHours, setAssignedHours] = useState<number>(initialAssignedHours);
  const [availableDays, setAvailableDays] = useState<string[]>(initialAvailableDays);

  const handleDayToggle = (day: string) => {
    if (availableDays.includes(day)) {
      setAvailableDays(availableDays.filter(d => d !== day));
    } else {
      setAvailableDays([...availableDays, day]);
    }
  };

  const handleSave = () => {
    // In a real app, this would save to the database
    toast.success("Staff availability settings saved successfully");
    
    // You could also update the parent component or context here
    console.log("Saved settings:", {
      staffId,
      assignedHours,
      availableDays
    });
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-medium">Availability Settings</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="assigned-hours">Assigned Hours per Week</Label>
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <Input
                id="assigned-hours"
                type="number"
                min="0"
                max="168"
                value={assignedHours}
                onChange={(e) => setAssignedHours(parseInt(e.target.value) || 0)}
                className="w-24"
              />
              <span className="text-muted-foreground">hours</span>
            </div>
          </div>

          <div className="space-y-3">
            <Label>Available Days</Label>
            <div className="flex items-center gap-2 mb-1">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">Select the days when this staff member is available to work</span>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
              {daysOfWeek.map((day) => (
                <div key={day.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={`day-${day.id}`}
                    checked={availableDays.includes(day.id)}
                    onCheckedChange={() => handleDayToggle(day.id)}
                  />
                  <Label htmlFor={`day-${day.id}`} className="cursor-pointer">
                    {day.label}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <Button onClick={handleSave} className="w-full">
            <Save className="h-4 w-4 mr-2" /> Save Availability Settings
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default StaffAvailabilitySettings;
