"""
Discount and promo code models for RestroManage database.
Corresponds to app/models/discounts.py Pydantic models.
"""

from sqlalchemy import Column, String, Boolean, Float, Text, JSON, ForeignKey, Integer, DateTime
from sqlalchemy.orm import relationship
from .base import BaseModel, TimestampMixin, StatusMixin, SoftDeleteMixin

class PromoCode(BaseModel, TimestampMixin, StatusMixin, SoftDeleteMixin):
    """
    Promo code model for discount management.
    Corresponds to PromoCode Pydantic model in app/models/discounts.py
    """
    __tablename__ = "promo_codes"
    
    # Restaurant association
    restaurant_id = Column(String(36), ForeignKey("restaurants.id"), nullable=False, index=True)
    
    # Basic information
    code = Column(String(50), nullable=False, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    
    # Discount configuration
    discount_type = Column(String(20), nullable=False, index=True)
    # Types: percentage, fixed_amount, buy_x_get_y, free_item
    discount_value = Column(Float, nullable=False)
    
    # Scope and applicability
    scope = Column(String(20), nullable=False, default="order_total")
    # Scopes: order_total, specific_items, category, minimum_spend
    minimum_spend = Column(Float, nullable=True)
    maximum_discount = Column(Float, nullable=True)
    
    # Item and category restrictions
    applicable_items = Column(JSON, nullable=True)      # List of menu item IDs
    applicable_categories = Column(JSON, nullable=True) # List of category IDs
    
    # Validity period
    start_date = Column(DateTime(timezone=True), nullable=False, index=True)
    end_date = Column(DateTime(timezone=True), nullable=False, index=True)
    
    # Usage limits
    usage_limit = Column(Integer, nullable=True)              # Total usage limit
    usage_limit_per_customer = Column(Integer, nullable=True) # Per customer limit
    usage_count = Column(Integer, default=0, nullable=False)  # Current usage count
    
    # Stacking and combination rules
    is_stackable = Column(Boolean, default=False, nullable=False)
    cannot_combine_with = Column(JSON, nullable=True)  # List of promo code IDs
    
    # Target audience
    target_audience = Column(String(100), nullable=True)
    customer_segments = Column(JSON, nullable=True)  # List of customer segments
    
    # Special conditions
    day_of_week_restrictions = Column(JSON, nullable=True)  # List of allowed days
    time_restrictions = Column(JSON, nullable=True)         # Time ranges
    special_conditions = Column(JSON, nullable=True)        # Custom conditions
    
    # Campaign association
    campaign_id = Column(String(36), ForeignKey("campaigns.id"), nullable=True, index=True)
    
    # Analytics and performance
    total_discount_given = Column(Float, default=0.0, nullable=False)
    total_revenue_generated = Column(Float, default=0.0, nullable=False)
    conversion_rate = Column(Float, nullable=True)
    
    # Auto-generation settings (for AI-generated codes)
    is_auto_generated = Column(Boolean, default=False, nullable=False)
    generation_parameters = Column(JSON, nullable=True)
    
    # Relationships
    restaurant = relationship("Restaurant", back_populates="promo_codes")
    campaign = relationship("Campaign", back_populates="promo_codes")
    usage_records = relationship("PromoCodeUsage", back_populates="promo_code", cascade="all, delete-orphan")
    order_discounts = relationship("OrderDiscount", back_populates="promo_code")
    
    def __repr__(self):
        return f"<PromoCode(id={self.id}, code={self.code}, discount_type={self.discount_type})>"

class PromoCodeUsage(BaseModel, TimestampMixin):
    """
    Promo code usage tracking model.
    Corresponds to PromoCodeUsage Pydantic model in app/models/discounts.py
    """
    __tablename__ = "promo_code_usage"
    
    # Promo code association
    promo_code_id = Column(String(36), ForeignKey("promo_codes.id"), nullable=False, index=True)
    
    # Order association
    order_id = Column(String(36), ForeignKey("orders.id"), nullable=False, index=True)
    
    # Customer information
    customer_id = Column(String(36), nullable=True, index=True)
    customer_name = Column(String(255), nullable=True)
    customer_email = Column(String(255), nullable=True)
    customer_phone = Column(String(20), nullable=True)
    
    # Usage details
    discount_amount = Column(Float, nullable=False)
    order_total = Column(Float, nullable=False)
    discount_percentage = Column(Float, nullable=True)  # Calculated percentage
    
    # Context information
    usage_context = Column(JSON, nullable=True)  # Additional context data
    device_info = Column(String(255), nullable=True)
    location = Column(String(255), nullable=True)
    
    # Validation results
    validation_passed = Column(Boolean, default=True, nullable=False)
    validation_notes = Column(Text, nullable=True)
    
    # Relationships
    promo_code = relationship("PromoCode", back_populates="usage_records")
    order = relationship("Order")
    
    def __repr__(self):
        return f"<PromoCodeUsage(id={self.id}, promo_code_id={self.promo_code_id}, discount_amount={self.discount_amount})>"

class Campaign(BaseModel, TimestampMixin, StatusMixin, SoftDeleteMixin):
    """
    Marketing campaign model for organizing promo codes.
    Corresponds to Campaign Pydantic model in app/models/discounts.py
    """
    __tablename__ = "campaigns"
    
    # Restaurant association
    restaurant_id = Column(String(36), ForeignKey("restaurants.id"), nullable=False, index=True)
    
    # Basic information
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    campaign_type = Column(String(50), default="promotion", nullable=False)
    # Types: promotion, loyalty, seasonal, event, customer_acquisition
    
    # Campaign period
    start_date = Column(DateTime(timezone=True), nullable=False, index=True)
    end_date = Column(DateTime(timezone=True), nullable=False, index=True)
    
    # Target audience
    target_audience = Column(String(255), nullable=True)
    customer_segments = Column(JSON, nullable=True)
    geographic_targets = Column(JSON, nullable=True)
    
    # Budget and goals
    budget = Column(Float, nullable=True)
    target_revenue = Column(Float, nullable=True)
    target_customers = Column(Integer, nullable=True)
    target_orders = Column(Integer, nullable=True)
    
    # Performance tracking
    total_usage = Column(Integer, default=0, nullable=False)
    total_discount_given = Column(Float, default=0.0, nullable=False)
    total_revenue_generated = Column(Float, default=0.0, nullable=False)
    customer_acquisition_count = Column(Integer, default=0, nullable=False)
    
    # Campaign settings
    auto_generate_codes = Column(Boolean, default=False, nullable=False)
    code_generation_rules = Column(JSON, nullable=True)
    notification_settings = Column(JSON, nullable=True)
    
    # Analytics and insights
    performance_metrics = Column(JSON, nullable=True)
    roi_calculation = Column(Float, nullable=True)
    success_rate = Column(Float, nullable=True)
    
    # Relationships
    restaurant = relationship("Restaurant")
    promo_codes = relationship("PromoCode", back_populates="campaign")
    
    def __repr__(self):
        return f"<Campaign(id={self.id}, name={self.name}, campaign_type={self.campaign_type})>"
