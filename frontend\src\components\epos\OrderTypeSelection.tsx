import React from "react";
import { Di<PERSON>, Dialog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { UtensilsCrossed, ShoppingBag } from "lucide-react";

interface OrderTypeSelectionProps {
  isOpen: boolean;
  onOrderTypeSelect: (orderType: "dine-in" | "takeaway") => void;
  onClose?: () => void;
}

const OrderTypeSelection: React.FC<OrderTypeSelectionProps> = ({
  isOpen,
  onOrderTypeSelect,
  onClose
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-center text-xl font-bold">
            Select Order Type
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 gap-4 py-4">
          <Card
            className="cursor-pointer transition-all hover:shadow-lg hover:scale-105 border-2 hover:border-primary"
            onClick={() => onOrderTypeSelect("dine-in")}
          >
            <CardContent className="flex flex-col items-center justify-center p-8 space-y-4">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                <UtensilsCrossed className="w-8 h-8 text-blue-600" />
              </div>
              <div className="text-center">
                <h3 className="text-lg font-semibold">Dine In</h3>
                <p className="text-sm text-muted-foreground">
                  Customer will eat at the restaurant
                </p>
              </div>
            </CardContent>
          </Card>

          <Card
            className="cursor-pointer transition-all hover:shadow-lg hover:scale-105 border-2 hover:border-primary"
            onClick={() => onOrderTypeSelect("takeaway")}
          >
            <CardContent className="flex flex-col items-center justify-center p-8 space-y-4">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                <ShoppingBag className="w-8 h-8 text-green-600" />
              </div>
              <div className="text-center">
                <h3 className="text-lg font-semibold">Take Away</h3>
                <p className="text-sm text-muted-foreground">
                  Customer will take the order to go
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {onClose && (
          <DialogFooter>
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default OrderTypeSelection;
