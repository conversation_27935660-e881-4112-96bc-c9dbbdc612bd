"""
Refactored restaurant router using MVC architecture.
This demonstrates the new pattern with controllers handling business logic.
"""

from fastapi import APIRouter, HTTPException, status, Depends, BackgroundTasks, Query
from typing import List, Optional

from app.controllers.restaurant_controller import RestaurantController
from app.models.restaurants import (
    Restaurant, RestaurantCreate, RestaurantUpdate, RestaurantResponse,
    RestaurantUser, RestaurantUserCreate, RestaurantUserUpdate, RestaurantUserResponse
)
from app.utils.logging_config import logger

router = APIRouter(prefix="/restaurants", tags=["Restaurants (MVC)"])

# Dependency injection for controller
async def get_restaurant_controller() -> RestaurantController:
    """Dependency injection for restaurant controller"""
    return RestaurantController()

@router.get("/", response_model=List[Restaurant])
async def get_restaurants(
    skip: int = Query(0, ge=0, description="Number of restaurants to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of restaurants to return"),
    controller: RestaurantController = Depends(get_restaurant_controller)
):
    """
    Get all restaurants with pagination.
    
    This endpoint demonstrates the new MVC pattern:
    - Router handles HTTP concerns (validation, response formatting)
    - Controller handles business logic (caching, data processing)
    - Models handle data validation and serialization
    """
    try:
        restaurants = await controller.get_all_restaurants(skip=skip, limit=limit)
        return restaurants
    except Exception as e:
        logger.error(f"Failed to get restaurants: {e}", "RestaurantRouter")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve restaurants"
        )

@router.get("/{restaurant_id}", response_model=Restaurant)
async def get_restaurant(
    restaurant_id: str,
    controller: RestaurantController = Depends(get_restaurant_controller)
):
    """Get a restaurant by ID"""
    restaurant = await controller.get_restaurant_by_id(restaurant_id)
    if not restaurant:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Restaurant not found"
        )
    return restaurant

@router.get("/code/{restaurant_code}", response_model=Restaurant)
async def get_restaurant_by_code(
    restaurant_code: str,
    controller: RestaurantController = Depends(get_restaurant_controller)
):
    """Get a restaurant by code"""
    restaurant = await controller.get_restaurant_by_code(restaurant_code)
    if not restaurant:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Restaurant not found"
        )
    return restaurant

@router.post("/", response_model=RestaurantResponse)
async def create_restaurant(
    restaurant_data: RestaurantCreate,
    controller: RestaurantController = Depends(get_restaurant_controller)
):
    """Create a new restaurant"""
    try:
        created_restaurant = await controller.create_restaurant(restaurant_data)
        
        return RestaurantResponse(
            success=True,
            message="Restaurant created successfully",
            restaurant=Restaurant(**created_restaurant)
        )
    except HTTPException:
        # Re-raise HTTP exceptions from controller
        raise
    except Exception as e:
        logger.error(f"Unexpected error creating restaurant: {e}", "RestaurantRouter")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create restaurant"
        )

@router.put("/{restaurant_id}", response_model=RestaurantResponse)
async def update_restaurant(
    restaurant_id: str,
    restaurant_data: RestaurantUpdate,
    controller: RestaurantController = Depends(get_restaurant_controller)
):
    """Update a restaurant"""
    try:
        updated_restaurant = await controller.update_restaurant(restaurant_id, restaurant_data)
        
        return RestaurantResponse(
            success=True,
            message="Restaurant updated successfully",
            restaurant=Restaurant(**updated_restaurant)
        )
    except HTTPException:
        # Re-raise HTTP exceptions from controller
        raise
    except Exception as e:
        logger.error(f"Unexpected error updating restaurant: {e}", "RestaurantRouter")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update restaurant"
        )

@router.delete("/{restaurant_id}")
async def delete_restaurant(
    restaurant_id: str,
    controller: RestaurantController = Depends(get_restaurant_controller)
):
    """Soft delete a restaurant (mark as inactive)"""
    try:
        result = await controller.soft_delete_restaurant(restaurant_id)
        return result
    except HTTPException:
        # Re-raise HTTP exceptions from controller
        raise
    except Exception as e:
        logger.error(f"Unexpected error deleting restaurant: {e}", "RestaurantRouter")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete restaurant"
        )

@router.post("/{restaurant_id}/restore")
async def restore_restaurant(
    restaurant_id: str,
    controller: RestaurantController = Depends(get_restaurant_controller)
):
    """Restore a soft-deleted restaurant (mark as active)"""
    try:
        result = await controller.restore_restaurant(restaurant_id)
        return result
    except HTTPException:
        # Re-raise HTTP exceptions from controller
        raise
    except Exception as e:
        logger.error(f"Unexpected error restoring restaurant: {e}", "RestaurantRouter")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to restore restaurant"
        )

# Authentication endpoints
class LoginRequest:
    def __init__(self, code: str, password: str):
        self.code = code
        self.password = password

@router.post("/auth/login")
async def restaurant_login(
    login_request: dict,  # Using dict for simplicity in this example
    controller: RestaurantController = Depends(get_restaurant_controller)
):
    """Authenticate restaurant with code and password"""
    try:
        code = login_request.get("code")
        password = login_request.get("password")
        
        if not code or not password:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Code and password are required"
            )
        
        restaurant = await controller.authenticate_restaurant(code, password)
        
        if not restaurant:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid credentials"
            )
        
        return {
            "success": True,
            "message": "Authentication successful",
            "restaurant": restaurant
        }
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Unexpected error during login: {e}", "RestaurantRouter")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Authentication failed"
        )

@router.post("/auth/reset-password")
async def reset_restaurant_password(
    reset_request: dict,  # Using dict for simplicity in this example
    controller: RestaurantController = Depends(get_restaurant_controller)
):
    """Reset restaurant password"""
    try:
        code = reset_request.get("code")
        new_password = reset_request.get("new_password")
        
        if not code or not new_password:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Code and new password are required"
            )
        
        result = await controller.reset_restaurant_password(code, new_password)
        return result
    except HTTPException:
        # Re-raise HTTP exceptions from controller
        raise
    except Exception as e:
        logger.error(f"Unexpected error resetting password: {e}", "RestaurantRouter")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password reset failed"
        )

# Performance monitoring endpoint
@router.get("/{restaurant_id}/performance")
async def get_restaurant_performance(
    restaurant_id: str,
    controller: RestaurantController = Depends(get_restaurant_controller)
):
    """
    Get restaurant performance metrics.
    
    This endpoint demonstrates how the MVC pattern enables:
    - Easy addition of new features
    - Consistent error handling
    - Proper separation of concerns
    """
    try:
        # This would be implemented in the controller
        # For now, return a placeholder response
        return {
            "restaurant_id": restaurant_id,
            "performance_metrics": {
                "cache_hit_rate": "85%",
                "average_response_time": "120ms",
                "active_sessions": 5
            },
            "generated_at": "2024-01-01T00:00:00Z"
        }
    except Exception as e:
        logger.error(f"Failed to get performance metrics: {e}", "RestaurantRouter")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve performance metrics"
        )
