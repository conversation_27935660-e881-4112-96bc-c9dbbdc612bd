from fastapi import APIRouter, HTTPException, status
from typing import List, Optional
from datetime import datetime
import random
import string
from pydantic import BaseModel
from app.models.restaurants import (
    Restaurant, RestaurantCreate, RestaurantUpdate, RestaurantResponse,
    RestaurantUser, RestaurantUserCreate, RestaurantUserUpdate, RestaurantUserResponse,
    CompleteRegistrationRequest, CompleteRegistrationResponse
)
from app.utils.storage_async import get_all_async, get_by_id_async, create_async, update_async, delete_async, query_async
from app.utils.logging_config import logger

router = APIRouter(prefix="/restaurants", tags=["Restaurants"])

def generate_restaurant_code(name: str) -> str:
    """Generate a unique restaurant code based on the restaurant name"""
    # Take first letters of each word, up to 3 characters
    words = name.upper().split()
    code_base = ''.join([word[0] for word in words[:3]])

    # Pad with random letters if needed
    while len(code_base) < 2:
        code_base += random.choice(string.ascii_uppercase)

    # Add random numbers
    code_suffix = ''.join([str(random.randint(0, 9)) for _ in range(3)])

    return f"{code_base}{code_suffix}"

def generate_owner_pin() -> str:
    """Generate a 4-digit PIN for the owner"""
    return ''.join([str(random.randint(0, 9)) for _ in range(4)])

@router.get("/", response_model=List[Restaurant])
async def get_restaurants():
    """Get all restaurants"""
    restaurants = await get_all_async("restaurants")
    return restaurants

@router.get("/{restaurant_id}", response_model=Restaurant)
async def get_restaurant(restaurant_id: str):
    """Get a restaurant by ID"""
    restaurant = await get_by_id_async("restaurants", restaurant_id)
    if not restaurant:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Restaurant not found"
        )
    return restaurant

@router.get("/code/{restaurant_code}", response_model=Restaurant)
async def get_restaurant_by_code(restaurant_code: str):
    """Get a restaurant by code"""
    restaurants = await query_async("restaurants", lambda r: r.get("code") == restaurant_code)
    if not restaurants:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Restaurant not found"
        )
    return restaurants[0]

@router.post("/", response_model=RestaurantResponse)
async def create_restaurant(restaurant_data: RestaurantCreate):
    """Create a new restaurant"""
    try:
        # Check if restaurant code already exists
        existing = await query_async("restaurants", lambda r: r.get("code") == restaurant_data.code)
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Restaurant code already exists"
            )

        # Check if email already exists
        existing_email = await query_async("restaurants", lambda r: r.get("email") == restaurant_data.email)
        if existing_email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )

        # Create restaurant
        restaurant_dict = restaurant_data.model_dump()
        restaurant_dict["createdAt"] = datetime.now().isoformat()
        restaurant_dict["updatedAt"] = datetime.now().isoformat()

        created_restaurant = await create_async("restaurants", restaurant_dict)

        return RestaurantResponse(
            success=True,
            message="Restaurant created successfully",
            restaurant=Restaurant(**created_restaurant)
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create restaurant: {str(e)}"
        )

@router.put("/{restaurant_id}", response_model=RestaurantResponse)
async def update_restaurant(restaurant_id: str, restaurant_data: RestaurantUpdate):
    """Update a restaurant"""
    try:
        existing_restaurant = await get_by_id_async("restaurants", restaurant_id)
        if not existing_restaurant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Restaurant not found"
            )

        # Update restaurant
        update_dict = restaurant_data.model_dump(exclude_unset=True)
        update_dict["updatedAt"] = datetime.now().isoformat()

        updated_restaurant = await update_async("restaurants", restaurant_id, update_dict)

        return RestaurantResponse(
            success=True,
            message="Restaurant updated successfully",
            restaurant=Restaurant(**updated_restaurant)
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update restaurant: {str(e)}"
        )

@router.delete("/{restaurant_id}")
async def delete_restaurant(restaurant_id: str):
    """Soft delete a restaurant (mark as inactive)"""
    from app.utils.logging_config import logger

    try:
        logger.info("Restaurant deletion attempt", "RestaurantManagement", {"restaurant_id": restaurant_id})

        existing_restaurant = await get_by_id_async("restaurants", restaurant_id)
        if not existing_restaurant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Restaurant not found"
            )

        # Check for active staff sessions (users with recent activity)
        active_users = await query_async("restaurant_users", lambda u: (
            u.get("restaurant_id") == restaurant_id and
            u.get("status") == "active"
        ))

        if active_users:
            logger.warning("Cannot delete restaurant with active staff", "RestaurantManagement", {
                "restaurant_id": restaurant_id,
                "active_users_count": len(active_users)
            })
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Cannot delete restaurant with {len(active_users)} active staff members. Please deactivate all staff first."
            )

        # TODO: Add check for active orders when order system is implemented
        # active_orders = await query_async("orders", lambda o: (
        #     o.get("restaurant_id") == restaurant_id and
        #     o.get("status") in ["pending", "preparing", "ready"]
        # ))
        # if active_orders:
        #     raise HTTPException(
        #         status_code=status.HTTP_400_BAD_REQUEST,
        #         detail=f"Cannot delete restaurant with {len(active_orders)} active orders"
        #     )

        # Perform soft delete by marking as inactive
        update_data = {
            "isActive": False,
            "is_active": False,  # Handle both field naming conventions
            "deletedAt": datetime.now().isoformat(),
            "deleted_at": datetime.now().isoformat()
        }

        updated_restaurant = await update_async("restaurants", restaurant_id, update_data)

        logger.info("Restaurant soft deleted successfully", "RestaurantManagement", {
            "restaurant_id": restaurant_id,
            "restaurant_name": existing_restaurant.get("name")
        })

        return {
            "success": True,
            "message": "Restaurant deactivated successfully",
            "restaurant_id": restaurant_id,
            "restaurant_name": existing_restaurant.get("name")
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Restaurant deletion failed", "RestaurantManagement", {
            "restaurant_id": restaurant_id,
            "error": str(e)
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete restaurant: {str(e)}"
        )

@router.post("/{restaurant_id}/restore")
async def restore_restaurant(restaurant_id: str):
    """Restore a soft-deleted restaurant (mark as active)"""
    from app.utils.logging_config import logger

    try:
        logger.info("Restaurant restoration attempt", "RestaurantManagement", {"restaurant_id": restaurant_id})

        existing_restaurant = await get_by_id_async("restaurants", restaurant_id)
        if not existing_restaurant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Restaurant not found"
            )

        # Restore by marking as active
        update_data = {
            "isActive": True,
            "is_active": True,  # Handle both field naming conventions
            "restoredAt": datetime.now().isoformat(),
            "restored_at": datetime.now().isoformat()
        }

        await update_async("restaurants", restaurant_id, update_data)

        logger.info("Restaurant restored successfully", "RestaurantManagement", {
            "restaurant_id": restaurant_id,
            "restaurant_name": existing_restaurant.get("name")
        })

        return {
            "success": True,
            "message": "Restaurant restored successfully",
            "restaurant_id": restaurant_id,
            "restaurant_name": existing_restaurant.get("name")
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Restaurant restoration failed", "RestaurantManagement", {
            "restaurant_id": restaurant_id,
            "error": str(e)
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to restore restaurant: {str(e)}"
        )

# Restaurant Users endpoints
@router.get("/users/all", response_model=List[RestaurantUser])
async def get_all_restaurant_users():
    """Get all restaurant users across all restaurants"""
    users = await get_all_async("restaurant_users")
    return users

@router.get("/{restaurant_id}/users", response_model=List[RestaurantUser])
async def get_restaurant_users(restaurant_id: str):
    """Get all users for a specific restaurant"""
    # Verify restaurant exists first
    restaurant = await get_by_id_async("restaurants", restaurant_id)
    if not restaurant:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Restaurant not found"
        )

    users = await query_async("restaurant_users", lambda u: u.get("restaurant_id") == restaurant_id)

    # Convert database models to Pydantic models
    converted_users = []
    for user in users:
        user_data = user.copy()
        user_data["restaurant_name"] = restaurant.get("name", "Unknown")

        # Convert database field names to Pydantic field names
        if "hire_date" in user_data:
            user_data["hireDate"] = user_data.pop("hire_date")
        if "access_level" in user_data:
            user_data["accessLevel"] = user_data.pop("access_level")

        converted_users.append(RestaurantUser(**user_data))

    return converted_users

@router.post("/{restaurant_id}/users", response_model=RestaurantUserResponse)
async def create_restaurant_user(restaurant_id: str, user_data: RestaurantUserCreate):
    """Create a new user for a restaurant"""
    try:
        # Verify restaurant exists
        restaurant = await get_by_id_async("restaurants", restaurant_id)
        if not restaurant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Restaurant not found"
            )

        # Check if email already exists
        existing_user = await query_async("restaurant_users", lambda u: u.get("email") == user_data.email)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )

        # Create user
        user_dict = user_data.model_dump()
        created_user = await create_async("restaurant_users", user_dict)

        return RestaurantUserResponse(
            success=True,
            message="User created successfully",
            user=RestaurantUser(**created_user)
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create user: {str(e)}"
        )

# Staff PIN Authentication models and endpoints
class PinVerificationRequest(BaseModel):
    pin: str

class PinVerificationResponse(BaseModel):
    success: bool
    message: str
    user: Optional[RestaurantUser] = None

@router.post("/{restaurant_id}/staff/verify-pin", response_model=PinVerificationResponse)
async def verify_staff_pin(restaurant_id: str, request: PinVerificationRequest):
    """Verify staff PIN for a specific restaurant"""
    from app.utils.logging_config import logger

    try:
        logger.info("Staff PIN verification attempt", "StaffAuth", {
            "restaurant_id": restaurant_id,
            "pin": "****"
        })

        # Verify restaurant exists
        restaurant = await get_by_id_async("restaurants", restaurant_id)
        if not restaurant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Restaurant not found"
            )

        # Find user with matching PIN in this restaurant
        users = await query_async("restaurant_users", lambda u: (
            u.get("restaurant_id") == restaurant_id and
            u.get("pin") == request.pin and
            u.get("status") == "active"
        ))

        # Also check for system admin users (restaurant_id = "0")
        if not users:
            admin_users = await query_async("restaurant_users", lambda u: (
                u.get("restaurant_id") == "0" and
                u.get("pin") == request.pin and
                u.get("status") == "active"
            ))
            users = admin_users

        if not users:
            logger.warning("Invalid PIN for restaurant", "StaffAuth", {
                "restaurant_id": restaurant_id,
                "pin": "****"
            })
            return PinVerificationResponse(
                success=False,
                message="Invalid PIN"
            )

        user = users[0]
        logger.info("Staff PIN verification successful", "StaffAuth", {
            "restaurant_id": restaurant_id,
            "user_name": user.get("name"),
            "user_role": user.get("role")
        })

        # Convert database model to Pydantic model
        user_data = user.copy()
        user_data["restaurant_name"] = restaurant.get("name", "Unknown")

        # Convert database field names to Pydantic field names
        if "hire_date" in user_data:
            user_data["hireDate"] = user_data.pop("hire_date")
        if "access_level" in user_data:
            user_data["accessLevel"] = user_data.pop("access_level")

        return PinVerificationResponse(
            success=True,
            message="PIN verified successfully",
            user=RestaurantUser(**user_data)
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Staff PIN verification error", "StaffAuth", {
            "restaurant_id": restaurant_id,
            "error": str(e)
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"PIN verification failed: {str(e)}"
        )

# Authentication endpoints
class LoginRequest(BaseModel):
    code: str
    password: str

class PasswordResetRequest(BaseModel):
    code: str
    new_password: str

@router.post("/auth/reset-password")
async def reset_restaurant_password(reset_request: PasswordResetRequest):
    """Reset restaurant password (for testing/debugging purposes)"""
    from app.utils.logging_config import logger

    try:
        logger.info("Password reset attempt", "RestaurantAuth", {"code": reset_request.code})

        # Find restaurant by code
        restaurant = await query_async("restaurants", lambda r: r.get("code") == reset_request.code)
        if not restaurant:
            logger.warning("Restaurant not found for password reset", "RestaurantAuth", {"code": reset_request.code})
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Restaurant not found"
            )

        restaurant_data = restaurant[0]

        # Hash the new password
        from app.utils.auth import get_password_hash
        hashed_password = get_password_hash(reset_request.new_password)

        # Update the password in database
        update_data = {"password": hashed_password}
        updated_restaurant = await update_async("restaurants", restaurant_data["id"], update_data)

        logger.info("Password reset successful", "RestaurantAuth", {
            "code": reset_request.code,
            "restaurant_id": restaurant_data["id"],
            "new_password_length": len(reset_request.new_password),
            "hashed_password_length": len(hashed_password)
        })

        return {
            "success": True,
            "message": "Password reset successful",
            "restaurant_code": reset_request.code,
            "restaurant_name": restaurant_data.get("name"),
            "new_password": reset_request.new_password  # Only for testing - remove in production
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Password reset failed", "RestaurantAuth", {"code": reset_request.code, "error": str(e)})
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password reset failed"
        )

@router.post("/auth/login")
async def restaurant_login(login_request: LoginRequest):
    """Authenticate restaurant with code and password"""
    from app.utils.logging_config import logger

    try:
        logger.info("Restaurant login attempt", "RestaurantAuth", {"code": login_request.code})

        # Find restaurant by code
        restaurant = await query_async("restaurants", lambda r: r.get("code") == login_request.code)
        if not restaurant:
            logger.warning("Restaurant not found", "RestaurantAuth", {"code": login_request.code})
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Restaurant not found"
            )

        restaurant_data = restaurant[0]

        # Check password - handle both plain text (legacy) and hashed passwords
        stored_password = restaurant_data.get("password")
        if not stored_password:
            logger.warning("No password found for restaurant", "RestaurantAuth", {"code": login_request.code})
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid password"
            )

        # Try hashed password verification first (for new restaurants)
        from app.utils.auth import verify_password
        password_valid = False

        # Log password details for debugging (remove in production)
        logger.info("Password verification attempt", "RestaurantAuth", {
            "code": login_request.code,
            "stored_password_type": "hashed" if stored_password.startswith('$2b$') else "plain",
            "stored_password_length": len(stored_password),
            "input_password_length": len(login_request.password)
        })

        # Check if it's a hashed password (bcrypt hashes start with $2b$)
        if stored_password.startswith('$2b$'):
            password_valid = verify_password(login_request.password, stored_password)
            logger.info("Using hashed password verification", "RestaurantAuth", {
                "code": login_request.code,
                "result": password_valid
            })
        else:
            # Fallback to plain text comparison (for legacy restaurants)
            password_valid = (stored_password == login_request.password)
            logger.info("Using plain text password verification", "RestaurantAuth", {
                "code": login_request.code,
                "result": password_valid
            })

        if not password_valid:
            logger.warning("Invalid password for restaurant", "RestaurantAuth", {"code": login_request.code})
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid password"
            )

        # Return restaurant data without password
        safe_restaurant = {k: v for k, v in restaurant_data.items() if k != "password"}

        logger.info("Restaurant login successful", "RestaurantAuth", {
            "code": login_request.code,
            "restaurant_name": safe_restaurant.get("name")
        })

        return {
            "success": True,
            "message": "Login successful",
            "restaurant": safe_restaurant
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Restaurant login error", "RestaurantAuth", {
            "code": login_request.code,
            "error": str(e)
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Login failed: {str(e)}"
        )

class GenerateCodeRequest(BaseModel):
    name: str

@router.post("/auth/generate-code")
async def generate_unique_restaurant_code(request: GenerateCodeRequest):
    """Generate a unique restaurant code for registration"""
    try:
        max_attempts = 10
        for _ in range(max_attempts):
            code = generate_restaurant_code(request.name)
            # Check if code already exists
            existing = await query_async("restaurants", lambda r: r.get("code") == code)
            if not existing:
                return {"code": code}

        # If we can't generate a unique code, add timestamp
        timestamp_code = f"{generate_restaurant_code(request.name)[:2]}{int(datetime.now().timestamp()) % 1000:03d}"
        return {"code": timestamp_code}

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate code: {str(e)}"
        )

# Complete registration endpoint
@router.post("/register", response_model=CompleteRegistrationResponse)
async def complete_restaurant_registration(registration_request: CompleteRegistrationRequest):
    """Complete restaurant registration with setup data"""
    try:
        reg_data = registration_request.registrationData
        setup_data = registration_request.setupData
        credentials = registration_request.credentials

        # Check if restaurant code already exists
        existing = await query_async("restaurants", lambda r: r.get("code") == credentials["restaurantCode"])
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Restaurant code already exists"
            )

        # Check if email already exists
        existing_email = await query_async("restaurants", lambda r: r.get("email") == reg_data.email)
        if existing_email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )

        # Hash the password for security
        from app.utils.auth import get_password_hash
        hashed_password = get_password_hash(reg_data.password)

        # Log password hashing for debugging (remove in production)
        logger.info("Password hashing during registration", "Registration", {
            "code": credentials["restaurantCode"],
            "original_password_length": len(reg_data.password),
            "hashed_password_length": len(hashed_password),
            "hashed_password_prefix": hashed_password[:10] + "..."
        })

        # Create restaurant with proper database schema
        restaurant_dict = {
            "name": reg_data.restaurantName,
            "code": credentials["restaurantCode"],
            "logo": setup_data.logoUrl or "/logos/default-restaurant.png",
            "address": f"{reg_data.address['street']}, {reg_data.address['city']}, {reg_data.address['zipCode']}",
            "phone": reg_data.phone,
            "email": reg_data.email,
            "vat_rate": 20.0,
            "currency": "GBP",
            "is_active": True,
            "owner_name": reg_data.ownerName,
            "business_license_number": reg_data.businessLicenseNumber,
            "restaurant_type": reg_data.restaurantType,
            "password": hashed_password,  # Store hashed password
            "setup_data": setup_data.model_dump()
        }

        created_restaurant = await create_async("restaurants", restaurant_dict)

        # Log what was actually stored for debugging
        logger.info("Restaurant created in database", "Registration", {
            "restaurant_id": created_restaurant["id"],
            "code": created_restaurant["code"],
            "stored_password_type": "hashed" if created_restaurant["password"].startswith('$2b$') else "plain",
            "stored_password_length": len(created_restaurant["password"])
        })

        # Create owner user with proper database schema
        user_dict = {
            "name": reg_data.ownerName,
            "email": reg_data.email,
            "phone": reg_data.phone,
            "restaurant_id": created_restaurant["id"],
            "role": "admin",
            "position": "Owner/Manager",
            "pin": credentials["ownerPin"],
            "status": "active",
            "hire_date": datetime.now().date().isoformat(),
            "performance": 100,
            "access_level": "full"
        }

        created_user = await create_async("restaurant_users", user_dict)

        # Log successful registration
        logger.info("Restaurant registration completed", "Registration", {
            "restaurant_id": created_restaurant["id"],
            "restaurant_name": reg_data.restaurantName,
            "restaurant_code": credentials["restaurantCode"],
            "owner_email": reg_data.email
        })

        # Add restaurant_name to user data for Pydantic model
        user_for_response = created_user.copy()
        user_for_response["restaurant_name"] = reg_data.restaurantName

        # Convert database field names to Pydantic field names
        if "hire_date" in user_for_response:
            user_for_response["hireDate"] = user_for_response.pop("hire_date")
        if "access_level" in user_for_response:
            user_for_response["accessLevel"] = user_for_response.pop("access_level")



        return CompleteRegistrationResponse(
            success=True,
            message="Restaurant registration completed successfully",
            restaurant=Restaurant(**created_restaurant),
            user=RestaurantUser(**user_for_response),
            credentials=credentials
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to complete registration: {str(e)}"
        )
