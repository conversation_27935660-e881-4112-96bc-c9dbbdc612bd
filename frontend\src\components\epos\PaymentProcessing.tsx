import { useState, useEffect } from "react";
import { <PERSON><PERSON>ard, PoundSterling, X } from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";

interface CartItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
}

interface PaymentProcessingProps {
  isOpen: boolean;
  onClose: () => void;
  cart: CartItem[];
  total: number;
  paymentMethod: "card" | "cash";
  onPaymentMethodChange: (method: "card" | "cash") => void;
  onProcessPayment: () => void;
  isProcessing: boolean;
  paymentComplete: boolean;
  orderNumber: string;
  onComplete: () => void;
}

const PaymentProcessing = ({
  isOpen,
  onClose,
  cart,
  total,
  paymentMethod,
  onPaymentMethodChange,
  onProcessPayment,
  isProcessing,
  paymentComplete,
  orderNumber,
  onComplete
}: PaymentProcessingProps) => {
  const [progress, setProgress] = useState(0);
  const [status, setStatus] = useState("Initializing payment...");

  useEffect(() => {
    if (isProcessing) {
      setProgress(0);
      setStatus("Initializing payment...");

      if (paymentMethod === "card") {
        // Simulate card payment processing steps
        const steps = [
          { progress: 20, status: "Connecting to payment gateway..." },
          { progress: 40, status: "Reading card information..." },
          { progress: 60, status: "Verifying card details..." },
          { progress: 80, status: "Processing transaction..." },
          { progress: 100, status: "Payment approved!" }
        ];

        let currentStep = 0;
        const interval = setInterval(() => {
          if (currentStep < steps.length) {
            setProgress(steps[currentStep].progress);
            setStatus(steps[currentStep].status);
            currentStep++;
          } else {
            clearInterval(interval);
          }
        }, 600);

        return () => clearInterval(interval);
      } else {
        // Simulate cash payment processing steps
        const steps = [
          { progress: 25, status: "Calculating change..." },
          { progress: 50, status: "Opening cash drawer..." },
          { progress: 75, status: "Finalizing transaction..." },
          { progress: 100, status: "Payment complete!" }
        ];

        let currentStep = 0;
        const interval = setInterval(() => {
          if (currentStep < steps.length) {
            setProgress(steps[currentStep].progress);
            setStatus(steps[currentStep].status);
            currentStep++;
          } else {
            clearInterval(interval);
          }
        }, 750);

        return () => clearInterval(interval);
      }
    }
  }, [paymentMethod, isProcessing]);

  if (paymentComplete) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-md" aria-describedby="payment-success-description">
          <DialogHeader>
            <DialogTitle className="text-center text-green-600">Payment Successful!</DialogTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </Button>
          </DialogHeader>
          <div id="payment-success-description" className="sr-only">
            Payment has been processed successfully for order {orderNumber}. Total amount: £{total.toFixed(2)}.
          </div>
          <div className="text-center py-6">
            <div className="w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
              <CreditCard className="h-8 w-8 text-green-600 dark:text-green-400" />
            </div>
            <h3 className="text-lg font-semibold mb-2">Order #{orderNumber}</h3>
            <p className="text-muted-foreground mb-4">Total: £{total.toFixed(2)}</p>
            <Button onClick={onComplete} className="w-full">
              Complete Order
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  if (isProcessing) {
    return (
      <Dialog open={isOpen} onOpenChange={() => {}}>
        <DialogContent className="max-w-md" aria-describedby="payment-processing-description">
          <DialogHeader>
            <DialogTitle className="text-center">
              {paymentMethod === "card" ? "Processing Card Payment" : "Processing Cash Payment"}
            </DialogTitle>
          </DialogHeader>
          <div id="payment-processing-description" className="sr-only">
            Payment is currently being processed. Please wait and do not close this window.
          </div>
          <div className="flex flex-col items-center justify-center py-8">
            <div className="w-16 h-16 bg-primary/10 dark:bg-primary/20 rounded-full flex items-center justify-center mb-6">
              {paymentMethod === "card" ? (
                <CreditCard className="h-8 w-8 text-primary animate-pulse" />
              ) : (
                <PoundSterling className="h-8 w-8 text-primary animate-pulse" />
              )}
            </div>

            <p className="text-center mb-6 text-muted-foreground">
              {status}
            </p>

            <div className="w-full max-w-md mb-4">
              <Progress value={progress} className="h-2" />
            </div>

            <p className="text-sm text-muted-foreground">
              Please do not close this window...
            </p>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl" aria-describedby="payment-details-description">
        <DialogHeader>
          <DialogTitle>Payment Details</DialogTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </Button>
        </DialogHeader>

        <div id="payment-details-description" className="sr-only">
          Payment details for your order. Review order summary, select payment method, and process payment.
        </div>

        <div className="space-y-6">
          {/* Order Summary */}
          <div>
            <h3 className="font-semibold mb-3">Order Summary</h3>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {cart.map((item, index) => (
                <div key={index} className="flex justify-between text-sm">
                  <span>{item.quantity}x {item.name}</span>
                  <span>£{(item.price * item.quantity).toFixed(2)}</span>
                </div>
              ))}
            </div>
            <Separator className="my-3" />
            <div className="flex justify-between font-semibold">
              <span>Total:</span>
              <span>£{total.toFixed(2)}</span>
            </div>
          </div>

          {/* Payment Method Selection */}
          <div>
            <h3 className="font-semibold mb-3">Payment Method</h3>
            <div className="flex gap-3">
              <Button
                variant={paymentMethod === "card" ? "default" : "outline"}
                onClick={() => onPaymentMethodChange("card")}
                className="flex-1"
              >
                <CreditCard className="w-4 h-4 mr-2" />
                Card
              </Button>
              <Button
                variant={paymentMethod === "cash" ? "default" : "outline"}
                onClick={() => onPaymentMethodChange("cash")}
                className="flex-1"
              >
                <PoundSterling className="w-4 h-4 mr-2" />
                Cash
              </Button>
            </div>
          </div>

          {/* Process Payment Button */}
          <div className="flex gap-3">
            <Button variant="outline" onClick={onClose} className="flex-1">
              Cancel
            </Button>
            <Button onClick={onProcessPayment} className="flex-1 bg-green-600 hover:bg-green-700">
              Process Payment
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PaymentProcessing;
