import { NotificationType, NotificationPriority } from '@/types/notification';

// Helper function to create notification data
export const createNotification = (
  title: string,
  message: string,
  type: NotificationType = 'info',
  priority: NotificationPriority = 'medium',
  link?: string,
  relatedId?: string
) => {
  return {
    title,
    message,
    type,
    priority,
    link,
    relatedId
  };
};

// Inventory notification helpers
export const createInventoryLowNotification = (itemName: string, currentStock: number) => {
  return createNotification(
    'Low inventory alert',
    `${itemName} is running low. Current stock: ${currentStock} units`,
    'inventory',
    'high',
    '/inventory'
  );
};

export const createInventoryExpiringNotification = (itemName: string, expiryDate: string) => {
  return createNotification(
    'Inventory expiring soon',
    `${itemName} will expire on ${expiryDate}`,
    'inventory',
    'medium',
    '/inventory'
  );
};

// Staff notification helpers
export const createStaffScheduleNotification = (message: string) => {
  return createNotification(
    'Staff schedule updated',
    message,
    'staff',
    'medium',
    '/staff'
  );
};

export const createStaffClockInNotification = (staffName: string, time: string) => {
  return createNotification(
    'Staff clocked in',
    `${staffName} clocked in at ${time}`,
    'staff',
    'low'
  );
};

// Order notification helpers
export const createNewOrderNotification = (orderNumber: string, tableNumber: string) => {
  return createNotification(
    'New order received',
    `Order #${orderNumber} from Table ${tableNumber}`,
    'order',
    'high',
    '/orders',
    orderNumber
  );
};

// Reservation notification helpers
export const createNewReservationNotification = (name: string, time: string, guests: number) => {
  return createNotification(
    'New reservation',
    `New reservation for ${name} party of ${guests} at ${time}`,
    'reservation',
    'medium',
    '/reservations'
  );
};
