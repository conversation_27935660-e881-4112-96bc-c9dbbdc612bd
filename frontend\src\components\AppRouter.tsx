import React, { useEffect } from 'react';
import { Routes, Route, Navigate, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import InitializingLoader from '@/components/auth/InitializingLoader';
import logger from '@/utils/logger';

// Import components
import ProtectedRoute from '@/components/auth/ProtectedRoute';

// Import pages - matching the current App.tsx import structure
import Index from "../pages/Index";
import NotFound from "../pages/NotFound";
import Dashboard from "../pages/admin/Dashboard";
import AdminDashboard from "../pages/AdminDashboard";
import Inventory from "../pages/Inventory";
import Analytics from "../pages/Analytics";
import TableTurnover from "../pages/TableTurnover";
import Staff from "../pages/Staff";
import StaffDetails from "../pages/StaffDetails";
import Schedule from "../pages/Schedule";
import Settings from "../pages/Settings";
import EPOS from "../pages/EPOS";
import StaffDashboard from "../pages/StaffDashboard";
import StaffSettings from "../pages/StaffSettings";
import StaffTasksPage from "../pages/StaffTasksPage";
import MenuManagement from "../pages/admin/MenuManagement";
import TaskManagement from "../pages/admin/TaskManagement";
import DiscountManagement from "../pages/admin/DiscountManagement";
import Assistant from "../pages/Assistant";
import Notifications from "../pages/Notifications";
import SignUp from "../pages/SignUp";
// Marketing Pages
import Home from "../pages/marketing/Home";
import Features from "../pages/marketing/Features";
import Pricing from "../pages/marketing/Pricing";
import Contact from "../pages/marketing/Contact";
// Customer Pages
import Menu from "../pages/customer/Menu";
import Reservation from "../pages/customer/Reservation";
// Auth Pages
import Login from "../pages/auth/Login";
import Register from "../pages/auth/Register";
import StaffLogin from "../pages/auth/StaffLogin";
import RestaurantLogin from "../pages/auth/RestaurantLogin";
import RestaurantSelect from "../pages/auth/RestaurantSelect";
import RestaurantSetup from "../pages/auth/RestaurantSetup";
import RestaurantSetupTest from "../pages/auth/RestaurantSetupTest";
import RegistrationSuccess from "../pages/auth/RegistrationSuccess";
import StaffPIN from "../pages/auth/StaffPIN";
import MainDashboard from "../pages/Dashboard";
import NewRestaurantOnboarding from "../pages/onboarding/NewRestaurantOnboarding";

const AppRouter: React.FC = () => {
  const { isLoading, isInitialized, isAuthenticated, isRestaurantAuthenticated, user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Initialize component logging
  logger.setComponent("AppRouter");

  // First check: If still loading or not initialized, show loading screen
  if (isLoading || !isInitialized) {
    logger.debug('Showing loading screen during authentication check', 'AppRouter', {
      isLoading,
      isInitialized
    });
    return <InitializingLoader message="Initializing Session..." />;
  }

  // Second check: Handle STRICT TWO-STEP authentication redirects BEFORE rendering routes
  // This enforces Restaurant Login → Staff Login → Dashboard sequence
  if (location.pathname === '/') {
    logger.debug('Checking STRICT TWO-STEP authentication state for root path redirect', 'AppRouter', {
      isAuthenticated,
      isRestaurantAuthenticated,
      userRole: user?.role,
      currentPath: location.pathname
    });

    // STRICT TWO-STEP AUTH: If restaurant is authenticated but no staff user, ALWAYS redirect to staff login
    if (isRestaurantAuthenticated && !isAuthenticated) {
      logger.info('🔒 STRICT AUTH: Restaurant authenticated, redirecting to mandatory staff login', 'AppRouter');
      return <Navigate to="/staff-login" replace />;
    }

    // STRICT TWO-STEP AUTH: Only allow dashboard access when BOTH authentications are complete
    if (isRestaurantAuthenticated && isAuthenticated && user) {
      const targetPath = user.role === 'admin' || user.accessLevel === 'full'
        ? '/admin'
        : '/staff-dashboard';

      logger.info('✅ STRICT AUTH: Both authentications complete, redirecting to dashboard', 'AppRouter', {
        userRole: user.role,
        accessLevel: user.accessLevel,
        targetPath
      });

      // Use Navigate component for immediate redirect without flash
      return <Navigate to={targetPath} replace />;
    }

    // If neither is authenticated, continue to show restaurant login (handled in routes below)
    logger.debug('🔒 STRICT AUTH: No authentication found, showing restaurant login', 'AppRouter');
  }

  // Third check: Render routes based on authentication state
  return (
    <Routes>
      {/* Root Route - Restaurant Login (Primary Entry Point) */}
      <Route path="/" element={<RestaurantLogin />} />
      {/* Redirect all login routes to root */}
      <Route path="/restaurant-login" element={<Navigate to="/" replace />} />
      <Route path="/login" element={<Navigate to="/" replace />} />

      {/* Marketing/Public Routes */}
      <Route path="/home" element={<Home />} />
      <Route path="/features" element={<Features />} />
      <Route path="/pricing" element={<Pricing />} />
      <Route path="/contact" element={<Contact />} />
      <Route path="/menu" element={<Menu />} />
      <Route path="/reservation" element={<Reservation />} />

      {/* Auth Routes - with authentication checks */}
      <Route
        path="/staff-login"
        element={
          isRestaurantAuthenticated && isAuthenticated && user ? (
            <Navigate
              to={user.role === 'admin' || user.accessLevel === 'full' ? '/admin' : '/staff-dashboard'}
              replace
            />
          ) : (
            <StaffLogin />
          )
        }
      />
      <Route path="/signup" element={<SignUp />} />
      <Route path="/restaurant-select" element={<RestaurantSelect />} />
      <Route path="/register" element={<Register />} />
      <Route path="/auth/register" element={<Register />} />
      <Route path="/auth/restaurant-setup" element={<RestaurantSetup />} />
      <Route path="/auth/registration-success" element={<RegistrationSuccess />} />
      <Route path="/restaurant-setup-test" element={<RestaurantSetupTest />} />
      <Route path="/onboarding/new-restaurant" element={<NewRestaurantOnboarding />} />
      <Route path="/staff-pin" element={<StaffPIN />} />

      {/* Admin Routes */}
      <Route path="/admin" element={<ProtectedRoute requiresFullAccess><Dashboard /></ProtectedRoute>} />
      <Route path="/admin/dashboard" element={<ProtectedRoute allowedRoles={["admin", "manager"]} requiresFullAccess><AdminDashboard /></ProtectedRoute>} />
      <Route path="/admin/inventory" element={<ProtectedRoute><Inventory /></ProtectedRoute>} />
      <Route path="/admin/analytics" element={<ProtectedRoute allowedRoles={["admin"]} requiresFullAccess><Analytics /></ProtectedRoute>} />
      <Route path="/admin/table-turnover" element={<ProtectedRoute requiresFullAccess><TableTurnover /></ProtectedRoute>} />
      <Route path="/admin/staff" element={<ProtectedRoute allowedRoles={["admin"]} requiresFullAccess><Staff /></ProtectedRoute>} />
      <Route path="/staff" element={<Staff />} />
      <Route path="/admin/staff/:id" element={<ProtectedRoute allowedRoles={["admin"]} requiresFullAccess><StaffDetails /></ProtectedRoute>} />
      <Route path="/admin/schedule" element={<ProtectedRoute requiresFullAccess><Schedule /></ProtectedRoute>} />
      <Route path="/admin/settings" element={<ProtectedRoute allowedRoles={["admin"]} requiresFullAccess><Settings /></ProtectedRoute>} />
      <Route path="/admin/discounts" element={<ProtectedRoute allowedRoles={["admin", "manager"]} requiresFullAccess><DiscountManagement /></ProtectedRoute>} />
      <Route path="/admin/epos" element={<ProtectedRoute><EPOS /></ProtectedRoute>} />
      <Route path="/epos" element={<ProtectedRoute><EPOS /></ProtectedRoute>} />
      <Route path="/admin/menu" element={<ProtectedRoute allowedRoles={["admin"]} requiresFullAccess><MenuManagement /></ProtectedRoute>} />
      <Route path="/admin/tasks" element={<ProtectedRoute allowedRoles={["admin"]} requiresFullAccess><TaskManagement /></ProtectedRoute>} />
      <Route path="/admin/notifications" element={<ProtectedRoute allowedRoles={["admin", "manager"]} requiresFullAccess><Notifications /></ProtectedRoute>} />
      <Route path="/admin/notification-test" element={<ProtectedRoute allowedRoles={["admin", "manager"]} requiresFullAccess><Notifications /></ProtectedRoute>} />
      <Route path="/admin/assistant" element={<ProtectedRoute><Assistant /></ProtectedRoute>} />
      <Route path="/staff/assistant" element={<ProtectedRoute allowedRoles={["staff", "waiter", "chef", "hostess", "bartender"]}><Assistant /></ProtectedRoute>} />

      {/* Main Dashboard Route */}
      <Route path="/dashboard" element={<ProtectedRoute><MainDashboard /></ProtectedRoute>} />

      {/* Staff Routes */}
      <Route path="/staff-dashboard" element={<ProtectedRoute><StaffDashboard /></ProtectedRoute>} />
      <Route path="/staff/settings" element={<ProtectedRoute allowedRoles={["staff", "waiter", "chef", "hostess", "bartender"]}><StaffSettings /></ProtectedRoute>} />
      <Route path="/staff/tasks" element={<ProtectedRoute allowedRoles={["staff", "waiter", "chef", "hostess", "bartender", "admin", "manager"]}><StaffTasksPage /></ProtectedRoute>} />

      {/* Catch-all Route */}
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
};

export default AppRouter;
