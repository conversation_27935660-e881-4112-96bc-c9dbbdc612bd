import { format, parseISO, isWithinInterval, isSameDay, startOfWeek, endOfWeek } from 'date-fns';
import { 
  TimeOffRequest, 
  StaffUnavailability, 
  SchedulingConflict,
  UnavailabilityType
} from '@/types/staffAvailability';
import { ForecastShift } from '@/services/forecastScheduleService';

// Local storage keys
const TIME_OFF_REQUESTS_KEY = 'staff_time_off_requests';
const STAFF_UNAVAILABILITY_KEY = 'staff_unavailability';
const SCHEDULING_CONFLICTS_KEY = 'scheduling_conflicts';

// Helper function to format date in UK format (DD/MM/YYYY)
export const formatUKDate = (date: Date | string): string => {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  return format(dateObj, 'dd/MM/yyyy');
};

// Get all time off requests
export const getAllTimeOffRequests = (): TimeOffRequest[] => {
  const storedRequests = localStorage.getItem(TIME_OFF_REQUESTS_KEY);
  return storedRequests ? JSON.parse(storedRequests) : [];
};

// Get time off requests for a specific staff member
export const getStaffTimeOffRequests = (staffId: string): TimeOffRequest[] => {
  const allRequests = getAllTimeOffRequests();
  return allRequests.filter(request => request.staffId === staffId);
};

// Get time off requests for a specific date range
export const getTimeOffRequestsInRange = (startDate: Date, endDate: Date): TimeOffRequest[] => {
  const allRequests = getAllTimeOffRequests();
  return allRequests.filter(request => {
    const requestStart = parseISO(request.startDate);
    const requestEnd = parseISO(request.endDate);
    
    return (
      isWithinInterval(requestStart, { start: startDate, end: endDate }) ||
      isWithinInterval(requestEnd, { start: startDate, end: endDate }) ||
      (requestStart <= startDate && requestEnd >= endDate)
    );
  });
};

// Create a new time off request
export const createTimeOffRequest = (request: Omit<TimeOffRequest, 'id' | 'createdAt' | 'updatedAt' | 'status'>): TimeOffRequest => {
  const allRequests = getAllTimeOffRequests();
  
  const newRequest: TimeOffRequest = {
    ...request,
    id: Date.now().toString(),
    status: 'pending',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  
  allRequests.push(newRequest);
  localStorage.setItem(TIME_OFF_REQUESTS_KEY, JSON.stringify(allRequests));
  
  // Create unavailability entries for each day in the request
  createUnavailabilityFromRequest(newRequest);
  
  return newRequest;
};

// Update a time off request
export const updateTimeOffRequest = (requestId: string, updates: Partial<TimeOffRequest>): TimeOffRequest | null => {
  const allRequests = getAllTimeOffRequests();
  const requestIndex = allRequests.findIndex(req => req.id === requestId);
  
  if (requestIndex === -1) return null;
  
  const updatedRequest = {
    ...allRequests[requestIndex],
    ...updates,
    updatedAt: new Date().toISOString()
  };
  
  allRequests[requestIndex] = updatedRequest;
  localStorage.setItem(TIME_OFF_REQUESTS_KEY, JSON.stringify(allRequests));
  
  // If the request is approved, create unavailability entries
  if (updates.status === 'approved') {
    createUnavailabilityFromRequest(updatedRequest);
  }
  
  // If the request is rejected, remove any pending unavailability entries
  if (updates.status === 'rejected') {
    removeUnavailabilityByRequestId(requestId);
  }
  
  return updatedRequest;
};

// Delete a time off request
export const deleteTimeOffRequest = (requestId: string): boolean => {
  const allRequests = getAllTimeOffRequests();
  const filteredRequests = allRequests.filter(req => req.id !== requestId);
  
  if (filteredRequests.length === allRequests.length) return false;
  
  localStorage.setItem(TIME_OFF_REQUESTS_KEY, JSON.stringify(filteredRequests));
  
  // Remove any unavailability entries associated with this request
  removeUnavailabilityByRequestId(requestId);
  
  return true;
};

// Get all staff unavailability records
export const getAllStaffUnavailability = (): StaffUnavailability[] => {
  const storedUnavailability = localStorage.getItem(STAFF_UNAVAILABILITY_KEY);
  return storedUnavailability ? JSON.parse(storedUnavailability) : [];
};

// Get unavailability for a specific staff member
export const getStaffUnavailability = (staffId: string): StaffUnavailability[] => {
  const allUnavailability = getAllStaffUnavailability();
  return allUnavailability.filter(unavail => unavail.staffId === staffId);
};

// Get unavailability for a specific date
export const getUnavailabilityForDate = (date: Date): StaffUnavailability[] => {
  const allUnavailability = getAllStaffUnavailability();
  return allUnavailability.filter(unavail => 
    isSameDay(parseISO(unavail.date), date)
  );
};

// Create unavailability from a time off request
const createUnavailabilityFromRequest = (request: TimeOffRequest): void => {
  const allUnavailability = getAllStaffUnavailability();
  const startDate = parseISO(request.startDate);
  const endDate = parseISO(request.endDate);
  
  // Create unavailability entries for each day in the range
  const currentDate = new Date(startDate);
  while (currentDate <= endDate) {
    const unavailability: StaffUnavailability = {
      id: `${request.id}_${format(currentDate, 'yyyy-MM-dd')}`,
      staffId: request.staffId,
      date: format(currentDate, 'yyyy-MM-dd'),
      type: request.type,
      reason: request.reason,
      isFullDay: request.isFullDay,
      startTime: request.startTime,
      endTime: request.endTime,
      requestId: request.id,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    allUnavailability.push(unavailability);
    currentDate.setDate(currentDate.getDate() + 1);
  }
  
  localStorage.setItem(STAFF_UNAVAILABILITY_KEY, JSON.stringify(allUnavailability));
};

// Remove unavailability by request ID
const removeUnavailabilityByRequestId = (requestId: string): void => {
  const allUnavailability = getAllStaffUnavailability();
  const filteredUnavailability = allUnavailability.filter(unavail => unavail.requestId !== requestId);
  localStorage.setItem(STAFF_UNAVAILABILITY_KEY, JSON.stringify(filteredUnavailability));
};

// Get color for unavailability type
export const getUnavailabilityColor = (type: UnavailabilityType): string => {
  const colors = {
    holiday: '#ef4444', // red-500
    sick: '#f97316', // orange-500
    personal: '#8b5cf6', // violet-500
    training: '#06b6d4', // cyan-500
    other: '#6b7280' // gray-500
  };
  
  return colors[type] || colors.other;
};

// Get all scheduling conflicts
export const getAllSchedulingConflicts = (): SchedulingConflict[] => {
  const storedConflicts = localStorage.getItem(SCHEDULING_CONFLICTS_KEY);
  return storedConflicts ? JSON.parse(storedConflicts) : [];
};

// Detect scheduling conflicts
export const detectSchedulingConflicts = (shifts: ForecastShift[]): SchedulingConflict[] => {
  const conflicts: SchedulingConflict[] = [];
  const unavailability = getAllStaffUnavailability();
  
  shifts.forEach(shift => {
    shift.assignedStaff.forEach(staff => {
      // Check if staff is unavailable during this shift
      const staffUnavailability = unavailability.filter(unavail => 
        unavail.staffId === staff.id && 
        isSameDay(parseISO(unavail.date), parseISO(shift.date))
      );
      
      staffUnavailability.forEach(unavail => {
        if (unavail.isFullDay) {
          conflicts.push({
            id: `conflict_${shift.id}_${staff.id}_${unavail.id}`,
            type: 'unavailability',
            staffId: staff.id,
            staffName: staff.name,
            shiftId: shift.id,
            date: shift.date,
            description: `${staff.name} is unavailable (${unavail.type}: ${unavail.reason})`,
            severity: 'high',
            createdAt: new Date().toISOString()
          });
        } else if (unavail.startTime && unavail.endTime) {
          // Check for time overlap
          const shiftStart = parseISO(`${shift.date}T${shift.startTime}`);
          const shiftEnd = parseISO(`${shift.date}T${shift.endTime}`);
          const unavailStart = parseISO(`${unavail.date}T${unavail.startTime}`);
          const unavailEnd = parseISO(`${unavail.date}T${unavail.endTime}`);
          
          if (
            (shiftStart >= unavailStart && shiftStart < unavailEnd) ||
            (shiftEnd > unavailStart && shiftEnd <= unavailEnd) ||
            (shiftStart <= unavailStart && shiftEnd >= unavailEnd)
          ) {
            conflicts.push({
              id: `conflict_${shift.id}_${staff.id}_${unavail.id}`,
              type: 'unavailability',
              staffId: staff.id,
              staffName: staff.name,
              shiftId: shift.id,
              date: shift.date,
              description: `${staff.name} is unavailable from ${unavail.startTime} to ${unavail.endTime} (${unavail.type}: ${unavail.reason})`,
              severity: 'medium',
              createdAt: new Date().toISOString()
            });
          }
        }
      });
    });
  });
  
  // Save conflicts to localStorage
  localStorage.setItem(SCHEDULING_CONFLICTS_KEY, JSON.stringify(conflicts));
  
  return conflicts;
};

// Resolve a scheduling conflict
export const resolveSchedulingConflict = (conflictId: string): boolean => {
  const allConflicts = getAllSchedulingConflicts();
  const filteredConflicts = allConflicts.filter(conflict => conflict.id !== conflictId);
  
  if (filteredConflicts.length === allConflicts.length) return false;
  
  localStorage.setItem(SCHEDULING_CONFLICTS_KEY, JSON.stringify(filteredConflicts));
  return true;
};

// Get conflicts for a specific date
export const getConflictsForDate = (date: Date): SchedulingConflict[] => {
  const allConflicts = getAllSchedulingConflicts();
  const dateString = format(date, 'yyyy-MM-dd');
  return allConflicts.filter(conflict => conflict.date === dateString);
};

// Get conflicts for a specific staff member
export const getStaffConflicts = (staffId: string): SchedulingConflict[] => {
  const allConflicts = getAllSchedulingConflicts();
  return allConflicts.filter(conflict => conflict.staffId === staffId);
};

// Get unavailability for a specific month
export const getUnavailabilityForMonth = (year: number, month: number): StaffUnavailability[] => {
  const allUnavailability = getAllStaffUnavailability();
  return allUnavailability.filter(unavail => {
    const unavailDate = parseISO(unavail.date);
    return unavailDate.getFullYear() === year && unavailDate.getMonth() === month;
  });
};

// Get unavailability for a specific staff member in a month
export const getStaffUnavailabilityForMonth = (staffId: string, year: number, month: number): StaffUnavailability[] => {
  const monthUnavailability = getUnavailabilityForMonth(year, month);
  return monthUnavailability.filter(unavail => unavail.staffId === staffId);
};

// Get unavailability for a specific week
export const getUnavailabilityForWeek = (startDate: Date): StaffUnavailability[] => {
  const allUnavailability = getAllStaffUnavailability();
  const weekStart = startOfWeek(startDate, { weekStartsOn: 1 }); // Monday as start of week
  const weekEnd = endOfWeek(startDate, { weekStartsOn: 1 }); // Sunday as end of week
  
  return allUnavailability.filter(unavail => {
    const unavailDate = parseISO(unavail.date);
    return unavailDate >= weekStart && unavailDate <= weekEnd;
  });
};

// Get unavailability for a specific staff member in a week
export const getStaffUnavailabilityForWeek = (staffId: string, startDate: Date): StaffUnavailability[] => {
  const weekUnavailability = getUnavailabilityForWeek(startDate);
  return weekUnavailability.filter(unavail => unavail.staffId === staffId);
};

// Get unresolved scheduling conflicts
export const getUnresolvedConflicts = (): SchedulingConflict[] => {
  const allConflicts = getAllSchedulingConflicts();
  return allConflicts.filter(conflict => !conflict.resolved);
};

// Get unresolved conflicts for a specific date
export const getUnresolvedConflictsForDate = (date: Date): SchedulingConflict[] => {
  const unresolvedConflicts = getUnresolvedConflicts();
  const dateString = format(date, 'yyyy-MM-dd');
  return unresolvedConflicts.filter(conflict => conflict.date === dateString);
};
