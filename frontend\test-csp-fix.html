<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSP Fix Verification - RestroManage</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-title {
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 1.1em;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .code {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .test-result {
            margin: 10px 0;
            padding: 8px;
            border-radius: 4px;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛡️ CSP Fix Verification - RestroManage</h1>
        
        <div class="status info">
            <strong>Purpose:</strong> Verify that the Content Security Policy (CSP) violation error has been fixed and the application can properly connect to localhost:5174 and the backend on localhost:5001.
        </div>

        <div class="test-section">
            <div class="test-title">🔧 Changes Applied</div>
            <div class="grid">
                <div>
                    <h4>1. Vite Configuration (vite.config.ts)</h4>
                    <div class="code">• Changed primary port from 5173 → 5174
• Updated HMR port to 5174
• Added CSP headers for both ports
• Updated preview port to 4174</div>
                </div>
                <div>
                    <h4>2. HTML CSP Meta Tag (index.html)</h4>
                    <div class="code">• Added http://localhost:5174 to connect-src
• Added ws://localhost:5174 for WebSocket
• Kept 5173 for fallback compatibility</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🧪 Automated Tests</div>
            <button onclick="runAllTests()" id="runTestsBtn">Run All Tests</button>
            <button onclick="clearResults()">Clear Results</button>
            
            <div id="testResults"></div>
        </div>

        <div class="test-section">
            <div class="test-title">🎯 Manual Test Instructions</div>
            <ol>
                <li><strong>Navigate to Restaurant Setup:</strong>
                    <button onclick="window.open('http://localhost:5174/auth/register', '_blank')">Open Registration</button>
                </li>
                <li><strong>Complete Registration Form</strong> and proceed to setup</li>
                <li><strong>Click "Complete Setup" button</strong> - this should NOT trigger CSP violations</li>
                <li><strong>Check Browser Console</strong> for any CSP violation errors</li>
                <li><strong>Verify Backend Connectivity</strong> during the setup process</li>
            </ol>
        </div>

        <div class="test-section">
            <div class="test-title">📊 Expected Results</div>
            <div class="grid">
                <div class="status success">
                    <strong>✅ Before Fix Issues:</strong><br>
                    • CSP violation: "Refused to connect to localhost:5173"<br>
                    • Complete Setup button failing<br>
                    • WebSocket connection errors
                </div>
                <div class="status success">
                    <strong>✅ After Fix Expected:</strong><br>
                    • No CSP violations in console<br>
                    • Complete Setup works properly<br>
                    • Smooth frontend-backend communication
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🔗 Quick Links</div>
            <button onclick="window.open('http://localhost:5174', '_blank')">Main Application</button>
            <button onclick="window.open('http://localhost:5174/auth/register', '_blank')">Registration</button>
            <button onclick="window.open('http://localhost:5001/docs', '_blank')">Backend API Docs</button>
            <button onclick="window.open('http://localhost:5001/health', '_blank')">Backend Health</button>
        </div>

        <div id="finalStatus" class="status info">
            <strong>Status:</strong> Ready to test. Click "Run All Tests" to verify the CSP fix.
        </div>
    </div>

    <script>
        let testResults = [];

        async function testConnection(url, description) {
            try {
                const response = await fetch(url, { 
                    method: 'GET',
                    mode: 'cors'
                });
                
                if (response.ok) {
                    return { success: true, message: `✅ ${description}: Connected successfully` };
                } else {
                    return { success: false, message: `❌ ${description}: HTTP ${response.status}` };
                }
            } catch (error) {
                return { success: false, message: `❌ ${description}: ${error.message}` };
            }
        }

        async function testCSPViolations() {
            return new Promise((resolve) => {
                let violationCount = 0;
                
                // Listen for CSP violations
                document.addEventListener('securitypolicyviolation', (e) => {
                    violationCount++;
                    console.log('CSP Violation detected:', e);
                });

                // Test a connection that should be allowed
                fetch('http://localhost:5174/')
                    .then(() => {
                        setTimeout(() => {
                            resolve({
                                success: violationCount === 0,
                                message: violationCount === 0 
                                    ? '✅ CSP Test: No violations detected'
                                    : `❌ CSP Test: ${violationCount} violations detected`
                            });
                        }, 1000);
                    })
                    .catch((error) => {
                        resolve({
                            success: false,
                            message: `❌ CSP Test: Connection failed - ${error.message}`
                        });
                    });
            });
        }

        async function runAllTests() {
            const button = document.getElementById('runTestsBtn');
            button.disabled = true;
            button.textContent = 'Running Tests...';
            
            testResults = [];
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<div class="info">Running tests...</div>';

            // Test 1: Frontend Server
            testResults.push(await testConnection('http://localhost:5174/', 'Frontend Server (Port 5174)'));
            
            // Test 2: Backend Server
            testResults.push(await testConnection('http://localhost:5001/health', 'Backend Server (Port 5001)'));
            
            // Test 3: API Proxy
            testResults.push(await testConnection('http://localhost:5174/api/restaurants', 'API Proxy (Frontend → Backend)'));
            
            // Test 4: CSP Violations
            testResults.push(await testCSPViolations());

            // Display results
            displayResults();
            
            button.disabled = false;
            button.textContent = 'Run All Tests';
        }

        function displayResults() {
            const resultsDiv = document.getElementById('testResults');
            const finalStatusDiv = document.getElementById('finalStatus');
            
            let html = '<h4>Test Results:</h4>';
            let allPassed = true;
            
            testResults.forEach(result => {
                const statusClass = result.success ? 'success' : 'error';
                html += `<div class="test-result ${statusClass}">${result.message}</div>`;
                if (!result.success) allPassed = false;
            });
            
            resultsDiv.innerHTML = html;
            
            if (allPassed) {
                finalStatusDiv.className = 'status success';
                finalStatusDiv.innerHTML = '<strong>🎉 All Tests Passed!</strong> The CSP fix is working correctly. You can now test the Complete Setup functionality.';
            } else {
                finalStatusDiv.className = 'status error';
                finalStatusDiv.innerHTML = '<strong>⚠️ Some Tests Failed!</strong> Please check the results above and ensure both servers are running.';
            }
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('finalStatus').className = 'status info';
            document.getElementById('finalStatus').innerHTML = '<strong>Status:</strong> Ready to test. Click "Run All Tests" to verify the CSP fix.';
        }

        // Auto-run basic connectivity test on page load
        window.addEventListener('load', () => {
            console.log('🛡️ CSP Fix Verification Page Loaded');
            console.log('Frontend should be running on: http://localhost:5174');
            console.log('Backend should be running on: http://localhost:5001');
        });
    </script>
</body>
</html>
