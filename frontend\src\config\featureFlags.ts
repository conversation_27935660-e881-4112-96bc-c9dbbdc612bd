/**
 * Feature Flags Configuration
 * Controls system-wide feature availability and subscription enforcement
 */

// Environment-based feature flags
const FEATURE_FLAGS = {
  // SUBSCRIPTION SYSTEM CONTROL
  ENABLE_SUBSCRIPTION_GATING: false, // 🔓 TEMPORARILY DISABLED - Set to true to re-enable subscription restrictions
  ENABLE_SUBSCRIPTION_UI: true,      // Keep subscription UI visible but non-functional
  ENABLE_ONBOARDING_SUBSCRIPTION: true, // Keep subscription step in onboarding

  // DEVELOPMENT FLAGS
  ENABLE_DEBUG_LOGGING: import.meta.env.DEV,
  ENABLE_SUBSCRIPTION_TESTING: import.meta.env.DEV,

  // FUTURE ENHANCEMENT FLAGS
  ENABLE_BACKEND_SUBSCRIPTION_API: false, // For future backend integration
  ENABLE_REAL_TIME_SUBSCRIPTION_SYNC: false, // For future real-time updates
  ENABLE_SUBSCRIPTION_ANALYTICS: false, // For future subscription analytics
};

/**
 * Check if subscription gating is enabled
 * @returns boolean - true if subscription restrictions should be enforced
 */
export const isSubscriptionGatingEnabled = (): boolean => {
  return FEATURE_FLAGS.ENABLE_SUBSCRIPTION_GATING;
};

/**
 * Check if subscription UI should be shown
 * @returns boolean - true if subscription UI elements should be visible
 */
export const isSubscriptionUIEnabled = (): boolean => {
  return FEATURE_FLAGS.ENABLE_SUBSCRIPTION_UI;
};

/**
 * Check if onboarding subscription step should be shown
 * @returns boolean - true if subscription selection should be in onboarding
 */
export const isOnboardingSubscriptionEnabled = (): boolean => {
  return FEATURE_FLAGS.ENABLE_ONBOARDING_SUBSCRIPTION;
};

/**
 * Check if debug logging is enabled
 * @returns boolean - true if debug logs should be shown
 */
export const isDebugLoggingEnabled = (): boolean => {
  return FEATURE_FLAGS.ENABLE_DEBUG_LOGGING;
};

/**
 * Get all feature flags (for debugging)
 * @returns object - all current feature flag values
 */
export const getAllFeatureFlags = () => {
  return { ...FEATURE_FLAGS };
};

/**
 * Override feature flags (for testing)
 * @param overrides - partial feature flag overrides
 */
export const overrideFeatureFlags = (overrides: Partial<typeof FEATURE_FLAGS>) => {
  Object.assign(FEATURE_FLAGS, overrides);
  console.log('🔧 Feature flags overridden:', overrides);
};

// Make feature flags available globally in development
if (process.env.NODE_ENV === 'development' && typeof window !== 'undefined') {
  (window as any).featureFlags = {
    get: getAllFeatureFlags,
    override: overrideFeatureFlags,
    enableSubscriptions: () => overrideFeatureFlags({ ENABLE_SUBSCRIPTION_GATING: true }),
    disableSubscriptions: () => overrideFeatureFlags({ ENABLE_SUBSCRIPTION_GATING: false }),
  };
  
  // Feature flags available in development mode
}

export default FEATURE_FLAGS;
