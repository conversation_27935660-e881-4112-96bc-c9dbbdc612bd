import logger from "@/utils/logger";

import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { toast } from "@/components/ui/sonner";
import { ChefHat, X, Delete, LogOut, Store } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";

const StaffLogin = () => {
  // Initialize component logging
  logger.setComponent("StaffLogin");
  logger.info("Component initialized", "StaffLogin");
  const navigate = useNavigate();
  const { loginWithPin, currentRestaurant, isRestaurantAuthenticated, logoutRestaurant, restaurantUsers } = useAuth();
  const [pin, setPin] = useState<string>("");
  const [error, setError] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // Redirect if no restaurant is authenticated
  useEffect(() => {
    logger.debug('Checking restaurant authentication', 'StaffLogin');
    logger.debug('Restaurant authentication status', 'StaffLogin', { isAuthenticated: isRestaurantAuthenticated });
    logger.debug('Current restaurant', 'StaffLogin', { restaurant: currentRestaurant?.name });

    if (!isRestaurantAuthenticated) {
      logger.userAction('redirect to restaurant login - no auth', 'StaffLogin');
      // Add delay to prevent race conditions
      setTimeout(() => {
        navigate('/');
      }, 100);
    } else {
      logger.debug('Restaurant authenticated', 'StaffLogin', { restaurantName: currentRestaurant?.name });
    }
  }, [isRestaurantAuthenticated, navigate]);

  // Separate useEffect for logging restaurant users to avoid unnecessary re-renders
  useEffect(() => {
    if (isRestaurantAuthenticated && restaurantUsers.length > 0) {
      logger.debug('Restaurant users loaded', 'StaffLogin', { count: restaurantUsers.length });
      restaurantUsers.forEach(user => {
        logger.debug('Available user', 'StaffLogin', { name: user.name, role: user.role, pin: '****' });
      });
    }
  }, [restaurantUsers, isRestaurantAuthenticated]);

  const handleNumberClick = (number: number) => {
    logger.userAction(`PIN digit entered: ${number}`, "StaffLogin", { pinLength: pin.length + 1 });
    if (pin.length < 4) {
      setPin(prev => prev + number);
      setError(false);
    }
  };

  const handleClearClick = () => {
    logger.userAction("PIN cleared", "StaffLogin");
    setPin("");
    setError(false);
  };

  const handleDeleteClick = () => {
    logger.userAction("PIN digit deleted", "StaffLogin", { newPinLength: pin.length - 1 });
    setPin(prev => prev.slice(0, -1));
    setError(false);
  };

  const handleSubmit = async () => {
    logger.formSubmit("staff PIN login", "StaffLogin", { pinLength: pin.length });
    logger.formSubmit('staff PIN login', 'StaffLogin', { pinLength: pin.length });

    if (pin.length === 4) {
      setIsLoading(true);
      try {
        logger.debug('Calling loginWithPin', 'StaffLogin', { pin: '****' });
        const result = await loginWithPin(pin);
        logger.debug('Login result', 'StaffLogin', { success: result.success });

        if (result.success) {
          logger.authentication("staff PIN login", "success", "StaffLogin", { role: result.role, accessLevel: result.accessLevel });
          toast.success(`Welcome, ${result.userName}!`);

          // Navigate based on user role and access level
          logger.authentication('staff PIN login', 'success', 'StaffLogin', { role: result.role, accessLevel: result.accessLevel });

          // Store login state in sessionStorage to prevent automatic logout
          sessionStorage.setItem('staffLoggedIn', 'true');

          // Add a small delay to ensure state is updated before navigation
          setTimeout(() => {
            // Make sure we have a role from the result
            const userRole = result.role || 'staff';
            logger.userAction('navigation based on role', 'StaffLogin', { userRole });

            // Check role in a case-insensitive way
            const lowerRole = userRole.toLowerCase();
            const isAdminOrManager = lowerRole === 'admin' || lowerRole === 'manager';

            if (isAdminOrManager) {
              // Admin/Manager - show full dashboard
              logger.userAction('navigate to admin dashboard', 'StaffLogin');
              navigate('/admin');
            } else {
              // Staff - show limited interface
              logger.userAction('navigate to staff dashboard', 'StaffLogin');
              navigate('/staff-dashboard');
            }
          }, 300); // Increased delay to ensure state updates
        } else {
          logger.authentication("staff PIN login", "failure", "StaffLogin", { reason: result.message });
          logger.authentication('staff PIN login', 'failure', 'StaffLogin', { reason: result.message });
          setError(true);
          toast.error(result.message || "Invalid PIN. Please try again.");
        }
      } catch (err) {
        logger.logError(err, "staff PIN login", "StaffLogin");
        logger.logError(err, 'staff PIN login', 'StaffLogin');
        setError(true);
        toast.error("An error occurred. Please try again.");
      } finally {
        setIsLoading(false);
      }
    } else {
      logger.warn('Invalid PIN length', 'StaffLogin', { length: pin.length });
      setError(true);
      toast.error("Please enter a 4-digit PIN.");
    }
  };

  const handleRestaurantLogout = () => {
    logger.userAction("restaurant logout", "StaffLogin");
    logoutRestaurant();
    navigate('/');
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-background p-4">
      <div className="w-full max-w-md">
        <div className="text-center mb-6 md:mb-8">
          <div className="inline-flex items-center gap-2">
            <ChefHat size={30} className="text-primary" />
            <span className="text-xl md:text-2xl font-bold">Promith</span>
          </div>

          {currentRestaurant && (
            <div className="mt-4 flex flex-col items-center">
              <div className="h-14 w-14 md:h-16 md:w-16 rounded-md bg-primary/10 flex items-center justify-center mb-2">
                {currentRestaurant.logo ? (
                  <img
                    src={currentRestaurant.logo}
                    alt={currentRestaurant.name}
                    className="h-12 w-12 md:h-14 md:w-14 object-contain"
                  />
                ) : (
                  <Store className="h-7 w-7 md:h-8 md:w-8 text-primary" />
                )}
              </div>
              <h2 className="text-lg md:text-xl font-semibold">{currentRestaurant.name}</h2>
            </div>
          )}

          <h1 className="text-2xl md:text-3xl font-bold mt-5 md:mt-6 mb-2">Staff Login</h1>
          <p className="text-sm md:text-base text-muted-foreground">
            Enter your PIN to access the system
          </p>
        </div>

        <Card className={`mb-5 md:mb-6 ${error ? 'border-red-500' : ''}`}>
          <CardContent className="pt-5 md:pt-6">
            {/* PIN Display */}
            <div className="flex justify-center mb-5 md:mb-6">
              <div className="flex gap-2 md:gap-3">
                {[1, 2, 3, 4].map((_, index) => (
                  <div
                    key={index}
                    className={`w-10 h-10 md:w-12 md:h-12 flex items-center justify-center rounded-md border-2 ${
                      index < pin.length
                        ? 'border-primary bg-primary/10'
                        : 'border-muted-foreground/20'
                    }`}
                  >
                    {index < pin.length && (
                      <div className="w-3 h-3 md:w-4 md:h-4 rounded-full bg-primary"></div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Keypad */}
            <div className="grid grid-cols-3 gap-2 md:gap-3 mb-4">
              {[1, 2, 3, 4, 5, 6, 7, 8, 9].map(number => (
                <Button
                  key={number}
                  variant="outline"
                  className="h-14 md:h-16 text-xl font-medium"
                  onClick={() => handleNumberClick(number)}
                  disabled={isLoading}
                >
                  {number}
                </Button>
              ))}
              <Button
                variant="outline"
                className="h-14 md:h-16 text-xl font-medium"
                onClick={handleClearClick}
                disabled={isLoading}
              >
                <X className="h-5 w-5 md:h-6 md:w-6" />
              </Button>
              <Button
                variant="outline"
                className="h-14 md:h-16 text-xl font-medium"
                onClick={() => handleNumberClick(0)}
                disabled={isLoading}
              >
                0
              </Button>
              <Button
                variant="outline"
                className="h-14 md:h-16 text-xl font-medium"
                onClick={handleDeleteClick}
                disabled={isLoading}
              >
                <Delete className="h-5 w-5 md:h-6 md:w-6" />
              </Button>
            </div>

            {/* Submit Button */}
            <Button
              className="w-full h-11 md:h-12 text-lg"
              onClick={handleSubmit}
              disabled={pin.length !== 4 || isLoading}
            >
              {isLoading ? "Verifying..." : "Login"}
            </Button>
          </CardContent>
        </Card>

        <div className="text-center">
          <Button
            variant="outline"
            className="gap-2"
            onClick={handleRestaurantLogout}
            disabled={isLoading}
          >
            <LogOut className="h-4 w-4" />
            Restaurant Logout
          </Button>
        </div>

        {restaurantUsers && restaurantUsers.length > 0 && (
          <div className="mt-5 md:mt-6 bg-blue-50 border border-blue-200 text-blue-700 px-3 md:px-4 py-2 md:py-3 rounded-md text-xs md:text-sm">
            <p className="font-medium mb-1">Available Staff PINs:</p>
            <ul className="list-disc list-inside space-y-1">
              {restaurantUsers.map(user => (
                <li key={user.id} className="truncate">
                  <strong>{user.name}</strong> ({user.position}): <span
                    className="font-mono bg-blue-100 px-1.5 py-0.5 rounded cursor-pointer hover:bg-blue-200"
                    onClick={() => setPin(user.pin)}
                  >{user.pin}</span>
                </li>
              ))}
            </ul>
            <p className="mt-2 text-xs text-blue-600">Click on a PIN to use it</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default StaffLogin;
