#!/usr/bin/env python3
"""
Enhanced Menu System Creator for RestroManage Analytics
Creates comprehensive menu categories and items for advanced analytics testing.
"""

import sqlite3
import uuid
from datetime import datetime
import json

def get_restaurant_id():
    """Get the first restaurant ID from the database"""
    conn = sqlite3.connect("restro_manage.db")
    cursor = conn.cursor()
    
    cursor.execute("SELECT id, name FROM restaurants LIMIT 1;")
    restaurant = cursor.fetchone()
    
    conn.close()
    
    if not restaurant:
        print("❌ No restaurants found in database")
        return None
    
    return restaurant[0]

def create_enhanced_menu_categories(restaurant_id):
    """Create comprehensive menu categories"""
    conn = sqlite3.connect("restro_manage.db")
    cursor = conn.cursor()
    
    categories = [
        {
            "name": "Appetizers & Starters",
            "description": "Light bites and appetizers to start your meal",
            "display_order": 1,
            "profit_margin": 0.75  # High margin items
        },
        {
            "name": "Soups & Salads", 
            "description": "Fresh salads and hearty soups",
            "display_order": 2,
            "profit_margin": 0.70
        },
        {
            "name": "Main Courses",
            "description": "Hearty main dishes and signature entrees",
            "display_order": 3,
            "profit_margin": 0.60  # Moderate margin
        },
        {
            "name": "Pasta & Risotto",
            "description": "Italian classics and comfort food",
            "display_order": 4,
            "profit_margin": 0.65
        },
        {
            "name": "Desserts",
            "description": "Sweet endings to your perfect meal",
            "display_order": 5,
            "profit_margin": 0.80  # Very high margin
        },
        {
            "name": "Beverages",
            "description": "Hot and cold drinks, wines, and cocktails",
            "display_order": 6,
            "profit_margin": 0.85  # Highest margin
        }
    ]
    
    created_categories = []
    
    for cat in categories:
        category_id = str(uuid.uuid4())
        now = datetime.now().isoformat()
        
        cursor.execute("""
            INSERT INTO menu_categories (
                id, restaurant_id, name, description, display_order, is_active, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            category_id,
            restaurant_id,
            cat["name"],
            cat["description"],
            cat["display_order"],
            True,
            now,
            now
        ))
        
        created_categories.append({
            "id": category_id,
            "name": cat["name"],
            "profit_margin": cat["profit_margin"]
        })
    
    conn.commit()
    conn.close()
    
    return created_categories

def create_enhanced_menu_items(restaurant_id, categories):
    """Create comprehensive menu items across all categories"""
    conn = sqlite3.connect("restro_manage.db")
    cursor = conn.cursor()
    
    # Menu items by category with realistic pricing and profit margins
    menu_items_data = {
        "Appetizers & Starters": [
            {"name": "Crispy Calamari", "price": 8.95, "prep_time": 12, "popularity": 0.85},
            {"name": "Buffalo Wings", "price": 9.50, "prep_time": 15, "popularity": 0.90},
            {"name": "Bruschetta Trio", "price": 7.50, "prep_time": 8, "popularity": 0.70},
            {"name": "Loaded Nachos", "price": 10.95, "prep_time": 10, "popularity": 0.80},
            {"name": "Garlic Bread", "price": 5.50, "prep_time": 5, "popularity": 0.95},
            {"name": "Mozzarella Sticks", "price": 7.95, "prep_time": 8, "popularity": 0.75},
            {"name": "Spinach & Artichoke Dip", "price": 8.50, "prep_time": 10, "popularity": 0.65}
        ],
        "Soups & Salads": [
            {"name": "Caesar Salad", "price": 9.95, "prep_time": 8, "popularity": 0.85},
            {"name": "Greek Salad", "price": 10.50, "prep_time": 10, "popularity": 0.70},
            {"name": "Tomato Basil Soup", "price": 6.95, "prep_time": 5, "popularity": 0.80},
            {"name": "Chicken Noodle Soup", "price": 7.50, "prep_time": 5, "popularity": 0.75},
            {"name": "Garden Fresh Salad", "price": 8.95, "prep_time": 8, "popularity": 0.60},
            {"name": "Mushroom Bisque", "price": 7.95, "prep_time": 5, "popularity": 0.55},
            {"name": "Quinoa Power Bowl", "price": 12.95, "prep_time": 12, "popularity": 0.65}
        ],
        "Main Courses": [
            {"name": "Grilled Salmon", "price": 18.95, "prep_time": 20, "popularity": 0.80},
            {"name": "Ribeye Steak", "price": 24.95, "prep_time": 25, "popularity": 0.75},
            {"name": "Chicken Parmesan", "price": 16.95, "prep_time": 18, "popularity": 0.85},
            {"name": "Fish & Chips", "price": 14.95, "prep_time": 15, "popularity": 0.70},
            {"name": "BBQ Ribs", "price": 19.95, "prep_time": 30, "popularity": 0.65},
            {"name": "Vegetarian Burger", "price": 13.95, "prep_time": 12, "popularity": 0.60},
            {"name": "Lamb Shank", "price": 22.95, "prep_time": 35, "popularity": 0.50},
            {"name": "Grilled Chicken Breast", "price": 15.95, "prep_time": 18, "popularity": 0.75}
        ],
        "Pasta & Risotto": [
            {"name": "Spaghetti Carbonara", "price": 14.95, "prep_time": 15, "popularity": 0.85},
            {"name": "Fettuccine Alfredo", "price": 13.95, "prep_time": 12, "popularity": 0.80},
            {"name": "Penne Arrabbiata", "price": 12.95, "prep_time": 12, "popularity": 0.70},
            {"name": "Mushroom Risotto", "price": 15.95, "prep_time": 20, "popularity": 0.65},
            {"name": "Seafood Linguine", "price": 17.95, "prep_time": 18, "popularity": 0.60},
            {"name": "Lasagna", "price": 16.95, "prep_time": 25, "popularity": 0.75},
            {"name": "Gnocchi Pesto", "price": 14.50, "prep_time": 15, "popularity": 0.55}
        ],
        "Desserts": [
            {"name": "Tiramisu", "price": 7.95, "prep_time": 5, "popularity": 0.80},
            {"name": "Chocolate Lava Cake", "price": 8.50, "prep_time": 8, "popularity": 0.85},
            {"name": "Cheesecake", "price": 6.95, "prep_time": 5, "popularity": 0.75},
            {"name": "Gelato Selection", "price": 5.95, "prep_time": 3, "popularity": 0.70},
            {"name": "Apple Pie", "price": 6.50, "prep_time": 5, "popularity": 0.65},
            {"name": "Panna Cotta", "price": 7.50, "prep_time": 5, "popularity": 0.55},
            {"name": "Chocolate Mousse", "price": 7.95, "prep_time": 5, "popularity": 0.60}
        ],
        "Beverages": [
            {"name": "House Wine (Glass)", "price": 6.95, "prep_time": 2, "popularity": 0.80},
            {"name": "Craft Beer", "price": 5.50, "prep_time": 2, "popularity": 0.85},
            {"name": "Espresso", "price": 3.50, "prep_time": 3, "popularity": 0.90},
            {"name": "Cappuccino", "price": 4.50, "prep_time": 5, "popularity": 0.85},
            {"name": "Fresh Orange Juice", "price": 4.95, "prep_time": 3, "popularity": 0.70},
            {"name": "Soft Drinks", "price": 2.95, "prep_time": 1, "popularity": 0.95},
            {"name": "Sparkling Water", "price": 3.95, "prep_time": 1, "popularity": 0.60},
            {"name": "Cocktail of the Day", "price": 8.95, "prep_time": 8, "popularity": 0.65}
        ]
    }
    
    created_items = []
    
    for category in categories:
        category_name = category["name"]
        profit_margin = category["profit_margin"]
        
        if category_name in menu_items_data:
            items = menu_items_data[category_name]
            
            for item in items:
                item_id = str(uuid.uuid4())
                now = datetime.now().isoformat()
                
                # Calculate cost based on profit margin
                cost_price = item["price"] * (1 - profit_margin)
                
                cursor.execute("""
                    INSERT INTO menu_items (
                        id, restaurant_id, name, description, price,
                        category, available, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    item_id,
                    restaurant_id,
                    item["name"],
                    f"Delicious {item['name'].lower()} prepared fresh",
                    item["price"],
                    category_name,
                    True,
                    now,
                    now
                ))
                
                created_items.append({
                    "id": item_id,
                    "name": item["name"],
                    "category": category_name,
                    "price": item["price"],
                    "cost_price": round(cost_price, 2),
                    "profit_margin": profit_margin,
                    "popularity": item["popularity"],
                    "prep_time": item["prep_time"]
                })
    
    conn.commit()
    conn.close()
    
    return created_items

def main():
    """Main function to create enhanced menu system"""
    print("🍽️  Creating Enhanced Menu System for Analytics")
    print("=" * 60)
    
    restaurant_id = get_restaurant_id()
    if not restaurant_id:
        return False
    
    # Clear existing menu data
    conn = sqlite3.connect("restro_manage.db")
    cursor = conn.cursor()
    
    cursor.execute("DELETE FROM menu_items WHERE restaurant_id = ?", (restaurant_id,))
    cursor.execute("DELETE FROM menu_categories WHERE restaurant_id = ?", (restaurant_id,))
    
    conn.commit()
    conn.close()
    
    print("🗑️  Cleared existing menu data")
    
    # Create enhanced categories
    print("📂 Creating menu categories...")
    categories = create_enhanced_menu_categories(restaurant_id)
    print(f"✅ Created {len(categories)} menu categories")
    
    # Create enhanced menu items
    print("🍕 Creating menu items...")
    items = create_enhanced_menu_items(restaurant_id, categories)
    print(f"✅ Created {len(items)} menu items")
    
    # Display summary
    print("\n📊 Menu System Summary:")
    for category in categories:
        category_items = [item for item in items if item["category"] == category["name"]]
        avg_price = sum(item["price"] for item in category_items) / len(category_items)
        avg_margin = sum(item["profit_margin"] for item in category_items) / len(category_items)
        
        print(f"  {category['name']}: {len(category_items)} items, avg £{avg_price:.2f}, {avg_margin:.1%} margin")
    
    print(f"\n🎉 Enhanced menu system created successfully!")
    print(f"Total items: {len(items)} across {len(categories)} categories")
    
    return True

if __name__ == "__main__":
    main()
