"""
Table controller implementing business logic for table operations.
Handles table management, status tracking, and reservations.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta

from app.controllers.base import BaseController
from app.utils.storage_async import get_all_async, get_by_id_async, create_async, update_async, delete_async, query_async
from app.utils.logging_config import logger

class TableController(BaseController):
    """Controller for table business logic and operations"""

    def __init__(self):
        super().__init__()
        self.cache_prefix = "table"
        self.default_cache_ttl = 300  # 5 minutes for table data
        self.valid_statuses = ["available", "occupied", "reserved", "cleaning", "out_of_service"]

    async def get_tables(
        self,
        restaurant_id: Optional[str] = None,
        status: Optional[str] = None,
        capacity: Optional[int] = None,
        location: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Get tables with filtering and caching"""
        # Validate pagination
        skip, limit = self.validate_pagination(skip, limit)

        # Create cache key based on filters
        filter_key = f"{restaurant_id}_{status}_{capacity}_{location}_{skip}_{limit}"
        cache_key = f"{self.cache_prefix}_filtered_{hash(filter_key)}"

        # Check cache first
        cached_result = self.get_cached_result(cache_key)
        if cached_result:
            return cached_result

        # Fetch tables
        tables = await self.handle_async_operation(
            get_all_async,
            "tables",
            error_message="Failed to fetch tables"
        )

        # Apply filters
        filtered_tables = self._apply_filters(tables, restaurant_id, status, capacity, location)

        # Apply pagination
        paginated_tables = filtered_tables[skip:skip + limit]

        # Cache the result
        self.cache_result(cache_key, paginated_tables, self.default_cache_ttl)

        logger.info(
            f"Retrieved {len(paginated_tables)} tables",
            "TableController",
            {"total_filtered": len(filtered_tables), "restaurant_id": restaurant_id}
        )

        return paginated_tables

    def _apply_filters(
        self,
        tables: List[Dict[str, Any]],
        restaurant_id: Optional[str] = None,
        status: Optional[str] = None,
        capacity: Optional[int] = None,
        location: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Apply filters to tables"""
        filtered_tables = tables.copy()

        if restaurant_id:
            filtered_tables = [
                table for table in filtered_tables
                if table.get("restaurant_id") == restaurant_id
            ]

        if status:
            filtered_tables = [
                table for table in filtered_tables
                if table.get("status") == status
            ]

        if capacity:
            filtered_tables = [
                table for table in filtered_tables
                if table.get("capacity", 0) >= capacity
            ]

        if location:
            filtered_tables = [
                table for table in filtered_tables
                if table.get("location", "").lower() == location.lower()
            ]

        return filtered_tables

    async def get_table_by_id(self, table_id: str) -> Optional[Dict[str, Any]]:
        """Get table by ID with caching"""
        cache_key = f"{self.cache_prefix}_{table_id}"
        cached_result = self.get_cached_result(cache_key)

        if cached_result:
            return cached_result

        table = await self.handle_async_operation(
            get_by_id_async,
            "tables",
            table_id,
            error_message=f"Failed to fetch table {table_id}"
        )

        if table:
            self.cache_result(cache_key, table, self.default_cache_ttl)
            logger.info(f"Retrieved table: {table.get('number', 'Unknown')}", "TableController")

        return table

    async def create_table(self, table_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new table with validation"""
        # Validate required fields
        if "number" not in table_data or "capacity" not in table_data:
            raise self.handle_validation_error(
                "Table number and capacity are required",
                {"missing_fields": ["number", "capacity"]}
            )

        # Check if table number already exists for this restaurant
        restaurant_id = table_data.get("restaurant_id")
        if restaurant_id:
            existing_tables = await self.handle_async_operation(
                query_async,
                "tables",
                lambda t: (
                    t.get("restaurant_id") == restaurant_id and
                    t.get("number") == table_data["number"]
                ),
                error_message="Failed to check existing table numbers"
            )

            if existing_tables:
                raise self.handle_validation_error(
                    f"Table number {table_data['number']} already exists",
                    {"table_number": table_data["number"]}
                )

        # Set default values
        table_data["status"] = table_data.get("status", "available")
        table_data["current_order_id"] = None
        table_data["reserved_until"] = None
        table_data["last_cleaned"] = None
        table_data["created_at"] = datetime.now().isoformat()
        table_data["updated_at"] = datetime.now().isoformat()

        # Create table
        created_table = await self.handle_async_operation(
            create_async,
            "tables",
            table_data,
            error_message="Failed to create table"
        )

        # Invalidate relevant caches
        self.invalidate_cache_pattern(self.cache_prefix)

        logger.info(
            f"Created table: {created_table.get('number')}",
            "TableController",
            {"table_id": created_table.get("id"), "restaurant_id": restaurant_id}
        )

        return created_table

    async def update_table_status(
        self,
        table_id: str,
        status: str,
        notes: Optional[str] = None,
        order_id: Optional[str] = None,
        updated_by: Optional[str] = None
    ) -> Dict[str, Any]:
        """Update table status with validation and business logic"""
        # Validate status
        if status not in self.valid_statuses:
            raise self.handle_validation_error(
                f"Invalid table status: {status}",
                {"valid_statuses": self.valid_statuses}
            )

        # Get existing table
        existing_table = await self.get_table_by_id(table_id)
        if not existing_table:
            raise self.handle_not_found("Table", table_id)

        # Prepare update data
        update_data = {
            "status": status,
            "updated_at": datetime.now().isoformat()
        }

        # Handle status-specific logic
        if status == "cleaning":
            update_data["last_cleaned"] = datetime.now().isoformat()
        elif status == "available":
            # Clear order and reservation data when table becomes available
            update_data["current_order_id"] = None
            update_data["reserved_until"] = None
            if existing_table.get("status") == "cleaning":
                update_data["last_cleaned"] = datetime.now().isoformat()
        elif status == "occupied" and order_id:
            update_data["current_order_id"] = order_id

        if notes:
            update_data["notes"] = notes

        if updated_by:
            update_data["updated_by"] = updated_by

        # Update table
        updated_table = await self.handle_async_operation(
            update_async,
            "tables",
            table_id,
            update_data,
            error_message=f"Failed to update table status {table_id}"
        )

        # Invalidate relevant caches
        self.invalidate_cache(f"{self.cache_prefix}_{table_id}")
        self.invalidate_cache_pattern(f"{self.cache_prefix}_filtered")

        logger.info(
            f"Updated table status: {table_id} -> {status}",
            "TableController",
            {"previous_status": existing_table.get("status"), "order_id": order_id}
        )

        return updated_table

    async def reserve_table(
        self,
        table_id: str,
        reserved_until: datetime,
        customer_name: Optional[str] = None,
        party_size: Optional[int] = None,
        notes: Optional[str] = None
    ) -> Dict[str, Any]:
        """Reserve a table with validation"""
        # Get existing table
        existing_table = await self.get_table_by_id(table_id)
        if not existing_table:
            raise self.handle_not_found("Table", table_id)

        # Check if table is available for reservation
        current_status = existing_table.get("status")
        if current_status not in ["available"]:
            raise self.handle_validation_error(
                f"Table is not available for reservation (current status: {current_status})",
                {"current_status": current_status, "table_number": existing_table.get("number")}
            )

        # Validate party size against table capacity
        if party_size and party_size > existing_table.get("capacity", 0):
            raise self.handle_validation_error(
                f"Party size ({party_size}) exceeds table capacity ({existing_table.get('capacity')})",
                {"party_size": party_size, "table_capacity": existing_table.get("capacity")}
            )

        # Prepare reservation data
        reservation_notes = f"Reserved for {customer_name}" if customer_name else "Reserved"
        if party_size:
            reservation_notes += f" (Party of {party_size})"
        if notes:
            reservation_notes += f" - {notes}"

        update_data = {
            "status": "reserved",
            "reserved_until": reserved_until.isoformat(),
            "notes": reservation_notes,
            "updated_at": datetime.now().isoformat()
        }

        # Update table
        updated_table = await self.handle_async_operation(
            update_async,
            "tables",
            table_id,
            update_data,
            error_message=f"Failed to reserve table {table_id}"
        )

        # Invalidate relevant caches
        self.invalidate_cache(f"{self.cache_prefix}_{table_id}")
        self.invalidate_cache_pattern(f"{self.cache_prefix}_filtered")

        logger.info(
            f"Reserved table: {existing_table.get('number')} until {reserved_until}",
            "TableController",
            {"customer_name": customer_name, "party_size": party_size}
        )

        return {
            "success": True,
            "message": "Table reserved successfully",
            "table": updated_table,
            "reservation_details": {
                "customer_name": customer_name,
                "party_size": party_size,
                "reserved_until": reserved_until.isoformat()
            }
        }

    async def clear_reservation(self, table_id: str) -> Dict[str, Any]:
        """Clear table reservation"""
        # Get existing table
        existing_table = await self.get_table_by_id(table_id)
        if not existing_table:
            raise self.handle_not_found("Table", table_id)

        # Check if table is actually reserved
        if existing_table.get("status") != "reserved":
            raise self.handle_validation_error(
                f"Table is not currently reserved (status: {existing_table.get('status')})",
                {"current_status": existing_table.get("status")}
            )

        update_data = {
            "status": "available",
            "reserved_until": None,
            "notes": None,
            "updated_at": datetime.now().isoformat()
        }

        # Update table
        updated_table = await self.handle_async_operation(
            update_async,
            "tables",
            table_id,
            update_data,
            error_message=f"Failed to clear reservation for table {table_id}"
        )

        # Invalidate relevant caches
        self.invalidate_cache(f"{self.cache_prefix}_{table_id}")
        self.invalidate_cache_pattern(f"{self.cache_prefix}_filtered")

        logger.info(
            f"Cleared reservation for table: {existing_table.get('number')}",
            "TableController"
        )

        return {
            "success": True,
            "message": "Reservation cleared successfully",
            "table": updated_table
        }

    async def get_available_tables(
        self,
        restaurant_id: str,
        capacity: Optional[int] = None,
        location: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Get available tables for EPOS integration"""
        return await self.get_tables(
            restaurant_id=restaurant_id,
            status="available",
            capacity=capacity,
            location=location
        )

    async def get_table_utilization(self, restaurant_id: str) -> Dict[str, Any]:
        """Get table utilization analytics for restaurant"""
        cache_key = f"{self.cache_prefix}_utilization_{restaurant_id}"
        cached_result = self.get_cached_result(cache_key)

        if cached_result:
            return cached_result

        # Get all tables for restaurant
        tables = await self.get_tables(restaurant_id=restaurant_id, limit=1000)

        if not tables:
            return {
                "total_tables": 0,
                "message": "No tables found for this restaurant"
            }

        # Calculate utilization metrics
        total_tables = len(tables)
        status_counts = {}

        for status in self.valid_statuses:
            status_counts[status] = len([t for t in tables if t.get("status") == status])

        utilization_rate = (status_counts.get("occupied", 0) / total_tables) * 100 if total_tables > 0 else 0

        utilization_data = {
            "restaurant_id": restaurant_id,
            "total_tables": total_tables,
            "status_breakdown": status_counts,
            "utilization_rate": round(utilization_rate, 2),
            "available_tables": status_counts.get("available", 0),
            "occupied_tables": status_counts.get("occupied", 0),
            "reserved_tables": status_counts.get("reserved", 0),
            "cleaning_tables": status_counts.get("cleaning", 0),
            "out_of_service_tables": status_counts.get("out_of_service", 0),
            "generated_at": datetime.now().isoformat()
        }

        # Cache the result
        self.cache_result(cache_key, utilization_data, 180)  # 3 minutes cache for analytics

        return utilization_data

    async def update_table(
        self,
        table_id: str,
        table_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Update table information"""
        # Get existing table
        existing_table = await self.get_table_by_id(table_id)
        if not existing_table:
            raise self.handle_not_found("Table", table_id)

        # Check if table number is being changed and validate uniqueness
        if "number" in table_data and table_data["number"] != existing_table.get("number"):
            restaurant_id = existing_table.get("restaurant_id")
            existing_tables = await self.handle_async_operation(
                query_async,
                "tables",
                lambda t: (
                    t.get("restaurant_id") == restaurant_id and
                    t.get("number") == table_data["number"] and
                    t.get("id") != table_id
                ),
                error_message="Failed to check table number uniqueness"
            )

            if existing_tables:
                raise self.handle_validation_error(
                    f"Table number {table_data['number']} already exists",
                    {"table_number": table_data["number"]}
                )

        # Add update timestamp
        table_data["updated_at"] = datetime.now().isoformat()

        # Update table
        updated_table = await self.handle_async_operation(
            update_async,
            "tables",
            table_id,
            table_data,
            error_message=f"Failed to update table {table_id}"
        )

        # Invalidate relevant caches
        self.invalidate_cache(f"{self.cache_prefix}_{table_id}")
        self.invalidate_cache_pattern(f"{self.cache_prefix}_filtered")
        self.invalidate_cache_pattern(f"{self.cache_prefix}_utilization")

        logger.info(
            f"Updated table: {updated_table.get('number')}",
            "TableController",
            {"table_id": table_id}
        )

        return updated_table

    async def delete_table(self, table_id: str) -> Dict[str, Any]:
        """Delete a table with validation"""
        # Get existing table
        existing_table = await self.get_table_by_id(table_id)
        if not existing_table:
            raise self.handle_not_found("Table", table_id)

        # Check if table has an active order
        if existing_table.get("current_order_id"):
            raise self.handle_validation_error(
                "Cannot delete table with active order",
                {
                    "table_number": existing_table.get("number"),
                    "order_id": existing_table.get("current_order_id")
                }
            )

        # Check if table is reserved
        if existing_table.get("status") == "reserved":
            raise self.handle_validation_error(
                "Cannot delete reserved table. Clear reservation first.",
                {"table_number": existing_table.get("number")}
            )

        # Delete table
        success = await self.handle_async_operation(
            delete_async,
            "tables",
            table_id,
            error_message=f"Failed to delete table {table_id}"
        )

        if success:
            # Invalidate relevant caches
            self.invalidate_cache(f"{self.cache_prefix}_{table_id}")
            self.invalidate_cache_pattern(f"{self.cache_prefix}_filtered")
            self.invalidate_cache_pattern(f"{self.cache_prefix}_utilization")

            logger.info(
                f"Deleted table: {existing_table.get('number')}",
                "TableController",
                {"table_id": table_id}
            )

        return {
            "success": success,
            "message": "Table deleted successfully" if success else "Failed to delete table",
            "table_number": existing_table.get("number")
        }
