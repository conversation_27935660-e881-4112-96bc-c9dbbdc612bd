import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { toast } from "@/components/ui/sonner";
import { ChefHat, X, Delete, LogOut, Store } from "lucide-react";
import { useStaffPIN } from "@/contexts/StaffPINContext";
import { useRestaurant } from "@/contexts/RestaurantContext";

const StaffPIN = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { verifyPIN, activeStaff } = useStaffPIN();
  const { currentRestaurant, isRestaurantAuthenticated, logoutRestaurant } = useRestaurant();
  const [pin, setPin] = useState<string>("");
  const [error, setError] = useState<boolean>(false);

  // Redirect if no restaurant is authenticated
  useEffect(() => {
    if (!isRestaurantAuthenticated) {
      navigate('/');
    }
  }, [isRestaurantAuthenticated, navigate]);

  const handleNumberClick = (number: number) => {
    if (pin.length < 4) {
      setPin(prev => prev + number);
      setError(false);
    }
  };

  const handleClearClick = () => {
    setPin("");
    setError(false);
  };

  const handleDeleteClick = () => {
    setPin(prev => prev.slice(0, -1));
    setError(false);
  };

  const handleSubmit = async () => {
    if (pin.length === 4) {
      try {
        const isValid = await verifyPIN(pin);
        if (isValid) {
          // Check if we came from the EPOS page
          const fromLocation = location.state?.from?.pathname;
          console.log("From location:", fromLocation);

          if (fromLocation === '/admin/epos') {
            // If we came from EPOS, go back there
            navigate('/admin/epos');
          } else {
            // Otherwise, redirect based on access level
            if (activeStaff?.accessLevel === 'full') {
              // Admin/Manager - show full dashboard
              navigate('/dashboard');
            } else {
              // Staff - show limited interface (for now, same dashboard)
              navigate('/dashboard');
            }
          }
        } else {
          setError(true);
          toast.error("Invalid PIN. Please try again.");
        }
      } catch (error) {
        console.error('PIN verification error:', error);
        setError(true);
        toast.error("PIN verification failed. Please try again.");
      }
    } else {
      setError(true);
      toast.error("Please enter a 4-digit PIN.");
    }
  };

  const handleRestaurantLogout = () => {
    logoutRestaurant();
    navigate('/');
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-background p-4">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <div className="inline-flex items-center gap-2">
            <ChefHat size={36} className="text-primary" />
            <span className="text-2xl font-bold">Promith</span>
          </div>

          {currentRestaurant && (
            <div className="mt-4 flex flex-col items-center">
              <div className="h-16 w-16 rounded-md bg-primary/10 flex items-center justify-center mb-2">
                {currentRestaurant.logo ? (
                  <img
                    src={currentRestaurant.logo}
                    alt={currentRestaurant.name}
                    className="h-14 w-14 object-contain"
                  />
                ) : (
                  <Store className="h-8 w-8 text-primary" />
                )}
              </div>
              <h2 className="text-xl font-semibold">{currentRestaurant.name}</h2>
            </div>
          )}

          <h1 className="text-3xl font-bold mt-6 mb-2">Staff Login</h1>
          <p className="text-muted-foreground">
            Enter your PIN to access the system
          </p>
        </div>

        <Card className={`mb-6 ${error ? 'border-red-500' : ''}`}>
          <CardContent className="pt-6">
            {/* PIN Display */}
            <div className="flex justify-center mb-6">
              <div className="flex gap-3">
                {[1, 2, 3, 4].map((_, index) => (
                  <div
                    key={index}
                    className={`w-12 h-12 flex items-center justify-center rounded-md border-2 ${
                      index < pin.length
                        ? 'border-primary bg-primary/10'
                        : 'border-muted-foreground/20'
                    }`}
                  >
                    {index < pin.length && (
                      <div className="w-4 h-4 rounded-full bg-primary"></div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Keypad */}
            <div className="grid grid-cols-3 gap-3 mb-4">
              {[1, 2, 3, 4, 5, 6, 7, 8, 9].map(number => (
                <Button
                  key={number}
                  variant="outline"
                  className="h-16 text-xl font-medium"
                  onClick={() => handleNumberClick(number)}
                >
                  {number}
                </Button>
              ))}
              <Button
                variant="outline"
                className="h-16 text-xl font-medium"
                onClick={handleClearClick}
              >
                <X className="h-6 w-6" />
              </Button>
              <Button
                variant="outline"
                className="h-16 text-xl font-medium"
                onClick={() => handleNumberClick(0)}
              >
                0
              </Button>
              <Button
                variant="outline"
                className="h-16 text-xl font-medium"
                onClick={handleDeleteClick}
              >
                <Delete className="h-6 w-6" />
              </Button>
            </div>

            {/* Submit Button */}
            <Button
              className="w-full h-12 text-lg"
              onClick={handleSubmit}
              disabled={pin.length !== 4}
            >
              Login
            </Button>
          </CardContent>
        </Card>

        <div className="text-center">
          <Button
            variant="outline"
            className="gap-2"
            onClick={handleRestaurantLogout}
          >
            <LogOut className="h-4 w-4" />
            Restaurant Logout
          </Button>
        </div>
      </div>
    </div>
  );
};

export default StaffPIN;
