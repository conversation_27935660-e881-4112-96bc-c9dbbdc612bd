from fastapi import APIRouter, Depends, HTTPException, status
from typing import List, Optional
from app.models.split_bills import (
    SplitBill, SplitBillCreate, SplitBillUpdate, PaymentRequest, PaymentResponse,
    ReceiptData, SplitBillSummary
)
from app.services.split_bill_service import SplitBillService
from app.utils.storage import get_all, get_by_id, create, update, delete, query
from app.utils.auth import get_current_active_user

router = APIRouter(prefix="/split-bills", tags=["Split Bills"])

@router.get("/", response_model=List[SplitBill])
async def get_split_bills(current_user = Depends(get_current_active_user)):
    """Get all split bills"""
    return get_all("split_bills")

@router.get("/{split_bill_id}", response_model=SplitBill)
async def get_split_bill(split_bill_id: str, current_user = Depends(get_current_active_user)):
    """Get a split bill by ID"""
    split_bill = get_by_id("split_bills", split_bill_id)
    if not split_bill:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Split bill not found"
        )
    return split_bill

@router.post("/", response_model=SplitBill)
async def create_split_bill(
    split_bill: SplitBillCreate,
    current_user = Depends(get_current_active_user)
):
    """Create a new split bill"""
    try:
        result = SplitBillService.create_split_bill(split_bill)
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.put("/{split_bill_id}", response_model=SplitBill)
async def update_split_bill(
    split_bill_id: str,
    split_bill_update: SplitBillUpdate,
    current_user = Depends(get_current_active_user)
):
    """Update a split bill"""
    existing_split_bill = get_by_id("split_bills", split_bill_id)
    if not existing_split_bill:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Split bill not found"
        )
    
    # Check if split bill is already completed
    if existing_split_bill.get("status") == "completed":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot update completed split bill"
        )
    
    update_data = split_bill_update.dict(exclude_unset=True)
    updated_split_bill = update("split_bills", split_bill_id, update_data)
    return updated_split_bill

@router.delete("/{split_bill_id}")
async def delete_split_bill(
    split_bill_id: str,
    current_user = Depends(get_current_active_user)
):
    """Delete a split bill (only if not paid)"""
    split_bill = get_by_id("split_bills", split_bill_id)
    if not split_bill:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Split bill not found"
        )
    
    # Check if any portion is paid
    if any(portion.get("payment_status") == "paid" for portion in split_bill.get("portions", [])):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete split bill with paid portions"
        )
    
    # Reset original order split status
    update("orders", split_bill["original_order_id"], {
        "is_split_bill": False,
        "split_bill_id": None
    })
    
    delete("split_bills", split_bill_id)
    return {"message": "Split bill deleted successfully"}

@router.post("/process-payment", response_model=PaymentResponse)
async def process_payment(
    payment_request: PaymentRequest,
    current_user = Depends(get_current_active_user)
):
    """Process payment for a split bill portion"""
    try:
        result = SplitBillService.process_payment(payment_request)
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.get("/{split_bill_id}/summary")
async def get_split_bill_summary(
    split_bill_id: str,
    current_user = Depends(get_current_active_user)
):
    """Get split bill summary"""
    try:
        result = SplitBillService.get_split_bill_summary(split_bill_id)
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.get("/{split_bill_id}/portions/{portion_id}/receipt", response_model=ReceiptData)
async def generate_receipt(
    split_bill_id: str,
    portion_id: str,
    current_user = Depends(get_current_active_user)
):
    """Generate receipt for a paid portion"""
    try:
        result = SplitBillService.generate_receipt(split_bill_id, portion_id)
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.get("/order/{order_id}")
async def get_split_bill_by_order(
    order_id: str,
    current_user = Depends(get_current_active_user)
):
    """Get split bill for an order"""
    split_bills = query("split_bills", lambda x: x.get("original_order_id") == order_id)
    if not split_bills:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No split bill found for this order"
        )
    return split_bills[0]

@router.get("/{split_bill_id}/portions/{portion_id}")
async def get_portion_details(
    split_bill_id: str,
    portion_id: str,
    current_user = Depends(get_current_active_user)
):
    """Get details of a specific portion"""
    split_bill = get_by_id("split_bills", split_bill_id)
    if not split_bill:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Split bill not found"
        )
    
    portion = None
    for p in split_bill.get("portions", []):
        if p["id"] == portion_id:
            portion = p
            break
    
    if not portion:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Portion not found"
        )
    
    return portion

@router.put("/{split_bill_id}/portions/{portion_id}/tip")
async def update_portion_tip(
    split_bill_id: str,
    portion_id: str,
    tip_amount: float,
    current_user = Depends(get_current_active_user)
):
    """Update tip amount for a portion"""
    split_bill = get_by_id("split_bills", split_bill_id)
    if not split_bill:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Split bill not found"
        )
    
    # Find and update the portion
    portions = split_bill.get("portions", [])
    portion_found = False
    
    for portion in portions:
        if portion["id"] == portion_id:
            if portion.get("payment_status") == "paid":
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Cannot update tip for paid portion"
                )
            
            portion["tip_amount"] = tip_amount
            portion["total_amount"] = portion["amount"] + tip_amount
            portion_found = True
            break
    
    if not portion_found:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Portion not found"
        )
    
    # Update split bill
    updated_split_bill = update("split_bills", split_bill_id, {"portions": portions})
    return {"message": "Tip updated successfully", "portion": portion}

@router.get("/analytics/split-bill-summary")
async def get_split_bill_analytics(current_user = Depends(get_current_active_user)):
    """Get split bill analytics"""
    split_bills = get_all("split_bills")
    
    total_split_bills = len(split_bills)
    completed_split_bills = len([sb for sb in split_bills if sb.get("status") == "completed"])
    pending_split_bills = len([sb for sb in split_bills if sb.get("status") == "pending"])
    partially_paid_split_bills = len([sb for sb in split_bills if sb.get("status") == "partially_paid"])
    
    total_amount = sum(sb.get("original_total", 0) for sb in split_bills)
    total_paid = sum(sb.get("total_paid", 0) for sb in split_bills)
    
    # Average split size
    total_portions = sum(len(sb.get("portions", [])) for sb in split_bills)
    average_split_size = total_portions / total_split_bills if total_split_bills > 0 else 0
    
    return {
        "total_split_bills": total_split_bills,
        "completed_split_bills": completed_split_bills,
        "pending_split_bills": pending_split_bills,
        "partially_paid_split_bills": partially_paid_split_bills,
        "total_amount": total_amount,
        "total_paid": total_paid,
        "outstanding_balance": total_amount - total_paid,
        "average_split_size": round(average_split_size, 2),
        "completion_rate": (completed_split_bills / total_split_bills * 100) if total_split_bills > 0 else 0
    }
