# Connection Issues & Proxy Configuration Fix

## 🚨 **CRITICAL ISSUE IDENTIFIED: Port Mismatch**

### Problem Summary
The frontend Vite development server cannot connect to the backend API, resulting in `ECONNREFUSED` errors for all API endpoints.

### Root Cause Analysis

#### 1. **Port Configuration Mismatch**
```
Frontend Proxy Target: http://localhost:5001
Backend Server Running: http://localhost:5002
Result: Connection Refused ❌
```

#### 2. **Configuration Files Analysis**

**Frontend (`frontend/vite.config.ts`)**:
```typescript
proxy: {
  "/api": "http://localhost:5001",      // ❌ Wrong port
  "/health": "http://localhost:5001"   // ❌ Wrong port
}
```

**Backend (`Backend/main.py`)**:
```python
port = int(os.getenv("PORT", "5002"))  # ✅ Actually runs on 5002
```

#### 3. **CORS Configuration Mismatch**
```python
# Backend allows these origins:
"http://localhost:5176",  # ❌ Frontend runs on 5175
"http://localhost:5175",  # ✅ Correct
```

## 🔧 **Immediate Fix Solutions**

### Option 1: Update Frontend Proxy (Recommended)
Update `frontend/vite.config.ts`:

```typescript
proxy: {
  "/api": "http://localhost:5002",      // ✅ Correct port
  "/health": "http://localhost:5002"   // ✅ Correct port
}
```

### Option 2: Change Backend Port
Set environment variable:
```bash
# In Backend directory
export PORT=5001
python main.py
```

### Option 3: Use Consistent Port Configuration
Create `.env` file in Backend:
```env
PORT=5001
HOST=0.0.0.0
RELOAD=true
```

## 🔍 **Verification Steps**

### 1. Check Backend Status
```bash
# Check if backend is running
netstat -an | findstr :5002

# Test health endpoint directly
curl http://localhost:5002/health
```

### 2. Check Frontend Proxy
```bash
# In frontend directory
npm run dev
# Should show: Local: http://localhost:5175
```

### 3. Test API Connection
```bash
# Test through proxy
curl http://localhost:5175/api/restaurants
curl http://localhost:5175/health
```

## 📋 **Complete Fix Checklist**

- [ ] **Backend Running**: Verify backend is running on correct port
- [ ] **Frontend Proxy**: Update proxy configuration to match backend port
- [ ] **CORS Origins**: Ensure backend allows frontend origin
- [ ] **Environment Variables**: Set consistent PORT values
- [ ] **Health Check**: Test `/health` endpoint
- [ ] **API Endpoints**: Test `/api/restaurants`, `/api/notifications`

## 🚀 **Quick Fix Commands**

```bash
# 1. Stop any running servers
# Ctrl+C in terminals

# 2. Start backend on port 5001
cd Backend
export PORT=5001
python main.py

# 3. In new terminal, start frontend
cd frontend
npm run dev

# 4. Test connection
curl http://localhost:5175/health
```

## 🔧 **Advanced Configuration**

### Environment-Based Configuration
Create `Backend/.env`:
```env
# Development Configuration
PORT=5001
HOST=0.0.0.0
RELOAD=true
ENVIRONMENT=development

# CORS Origins
ALLOWED_ORIGINS=http://localhost:5175,http://localhost:5173,http://localhost:3000
```

### Docker Configuration
Update `docker-compose.yml`:
```yaml
backend:
  ports:
    - "5001:5001"  # Map to consistent port
  environment:
    PORT: 5001
```

## 📊 **Port Usage Summary**

| Service | Current Port | Recommended Port | Status |
|---------|-------------|------------------|---------|
| Frontend Dev | 5175 | 5175 | ✅ Correct |
| Backend API | 5002 | 5001 | ❌ Needs Change |
| Frontend HMR | 5177 | 5177 | ✅ Correct |
| Preview | 4174 | 4174 | ✅ Correct |

## 🔍 **Debugging Tools**

### Check Running Processes
```bash
# Windows
netstat -an | findstr :5001
netstat -an | findstr :5002

# Linux/Mac
lsof -i :5001
lsof -i :5002
```

### Test API Endpoints
```bash
# Health check
curl -v http://localhost:5002/health

# Restaurants API
curl -v http://localhost:5002/api/restaurants

# Notifications API
curl -v "http://localhost:5002/api/notifications?restaurant_id=test&limit=10"
```

---

**Next Steps**: After fixing the port configuration, refer to [API Documentation](./api-documentation.md) for endpoint details.
