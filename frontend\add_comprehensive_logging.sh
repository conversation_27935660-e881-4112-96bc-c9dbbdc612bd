#!/bin/bash

echo "=== ADDING COMPREHENSIVE LOGGING TO REGISTRATION FLOW ==="

# Function to add logging to a React component
add_component_logging() {
    local file=$1
    local component=$2
    
    echo "Adding logging to $file ($component)..."
    
    # Add logger import if not already present
    if ! grep -q "import logger" "$file"; then
        sed -i '2i import logger from "@/utils/logger";' "$file"
    fi
    
    # Add component initialization logging
    sed -i "/const $component.*= () => {/a\\
\\  // Initialize component logging\\
\\  logger.setComponent('$component');\\
\\  logger.info('Component initialized', '$component');" "$file"
    
    # Add form submission logging
    sed -i 's/const handleSubmit = async (e: React.FormEvent) => {/const handleSubmit = async (e: React.FormEvent) => {\
    logger.formSubmit("registration form", "Register", { step: currentStep });/g' "$file"
    
    # Add step navigation logging
    sed -i 's/setCurrentStep(prev => Math.min(3, prev + 1));/setCurrentStep(prev => {\
      const nextStep = Math.min(3, prev + 1);\
      logger.userAction(`step navigation: ${prev} → ${nextStep}`, "Register");\
      return nextStep;\
    });/g' "$file"
    
    sed -i 's/setCurrentStep(prev => Math.max(1, prev - 1));/setCurrentStep(prev => {\
      const prevStep = Math.max(1, prev - 1);\
      logger.userAction(`step navigation: ${prev} → ${prevStep}`, "Register");\
      return prevStep;\
    });/g' "$file"
    
    # Add validation logging
    sed -i 's/const validateStep = (step: number): boolean => {/const validateStep = (step: number): boolean => {\
    logger.validation(`step ${step}`, "started", "Register");/g' "$file"
    
    # Add error logging
    sed -i 's/setErrors(newErrors);/setErrors(newErrors);\
    if (Object.keys(newErrors).length > 0) {\
      logger.validation(`step ${step}`, "failure", "Register", { errors: Object.keys(newErrors) });\
    } else {\
      logger.validation(`step ${step}`, "success", "Register");\
    }/g' "$file"
    
    # Add form data update logging
    sed -i 's/const updateFormData = (field: string, value: any) => {/const updateFormData = (field: string, value: any) => {\
    logger.userAction(`form field update: ${field}`, "Register", { field, hasValue: !!value });/g' "$file"
}

# Function to add logging to RestaurantSetup
add_setup_logging() {
    local file=$1
    
    echo "Adding logging to $file (RestaurantSetup)..."
    
    # Add logger import if not already present
    if ! grep -q "import logger" "$file"; then
        sed -i '2i import logger from "@/utils/logger";' "$file"
    fi
    
    # Add component initialization
    sed -i '/const RestaurantSetup.*= () => {/a\\
\\  // Initialize component logging\\
\\  logger.setComponent("RestaurantSetup");\\
\\  logger.info("Component initialized", "RestaurantSetup");' "$file"
    
    # Add setup data update logging
    sed -i 's/const updateSetupData = (field: string, value: any) => {/const updateSetupData = (field: string, value: any) => {\
    logger.userAction(`setup field update: ${field}`, "RestaurantSetup", { field, hasValue: !!value });/g' "$file"
    
    # Add table configuration logging
    sed -i 's/const handleTablesChange = (tables: TableConfig\[\], totalCapacity: number) => {/const handleTablesChange = (tables: TableConfig[], totalCapacity: number) => {\
    logger.dataOperation("update", "table configuration", "RestaurantSetup", { tableCount: tables.length, totalCapacity });/g' "$file"
    
    # Add logo change logging
    sed -i 's/const handleLogoChange = (file: File | null, url: string | null) => {/const handleLogoChange = (file: File | null, url: string | null) => {\
    logger.userAction("logo change", "RestaurantSetup", { hasFile: !!file, hasUrl: !!url });/g' "$file"
    
    # Add operating hours update logging
    sed -i 's/const updateOperatingHours = (day: string, field: string, value: any) => {/const updateOperatingHours = (day: string, field: string, value: any) => {\
    logger.userAction(`operating hours update: ${day}.${field}`, "RestaurantSetup", { day, field, value });/g' "$file"
}

# Function to add logging to Login pages
add_login_logging() {
    local file=$1
    local component=$2
    
    echo "Adding logging to $file ($component)..."
    
    # Add logger import if not already present
    if ! grep -q "import logger" "$file"; then
        sed -i '2i import logger from "@/utils/logger";' "$file"
    fi
    
    # Add component initialization
    sed -i "/const $component.*= () => {/a\\
\\  // Initialize component logging\\
\\  logger.setComponent('$component');\\
\\  logger.info('Component initialized', '$component');" "$file"
    
    # Add form submission logging
    sed -i 's/const handleSubmit = async (e: React.FormEvent) => {/const handleSubmit = async (e: React.FormEvent) => {\
    logger.formSubmit("login form", "'$component'", { email: formData.email });/g' "$file"
    
    # Add form data update logging
    sed -i 's/const updateFormData = (field: string, value: string) => {/const updateFormData = (field: string, value: string) => {\
    logger.userAction(`form field update: ${field}`, "'$component'", { field, hasValue: !!value });/g' "$file"
}

# Apply logging to all registration flow files
add_component_logging "src/pages/auth/Register.tsx" "Register"
add_setup_logging "src/pages/auth/RestaurantSetup.tsx"
add_login_logging "src/pages/auth/Login.tsx" "Login"
add_login_logging "src/pages/auth/RestaurantLogin.tsx" "RestaurantLogin"

echo "=== COMPREHENSIVE LOGGING ADDITION COMPLETED ==="
