"""
Enhanced AI Service for RestroManage
Integrates Google Generative AI with function calling capabilities
for intelligent database querying and restaurant management insights.
"""

import os
import json
import asyncio
import time
from typing import Dict, Any, List, Optional, Callable, Union
from datetime import datetime, timedelta
import google.generativeai as genai
from google.generativeai.types import FunctionDeclaration, Tool

from app.services.ai_database_queries import ai_data_service
from app.services.ai_metrics_service import ai_metrics_service
from app.utils.logging_config import logger, log_ai_model_usage
from app.utils.auth import get_current_restaurant_id
import uuid


class EnhancedAIService:
    """Enhanced AI service with function calling and database integration"""
    
    def __init__(self):
        self.api_key = os.getenv("GOOGLE_API_KEY")
        self.model_name = os.getenv("GEMINI_MODEL", "gemini-1.5-flash")
        self.temperature = float(os.getenv("GOOGLE_AI_TEMPERATURE", "0.7"))
        self.max_tokens = int(os.getenv("GOOGLE_AI_MAX_TOKENS", "2000"))
        
        self.enabled = False
        self.model = None
        self.tools = None
        
        # Initialize the service
        self._initialize_service()
    
    def _initialize_service(self):
        """Initialize the AI service with function calling capabilities"""
        logger.info("Initializing Enhanced AI Service", "EnhancedAI", {
            "model": self.model_name,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "api_key_configured": bool(self.api_key and self.api_key != "your-google-api-key-here")
        })
        
        if self.api_key and self.api_key != "your-google-api-key-here":
            try:
                genai.configure(api_key=self.api_key)
                
                # Define function declarations for AI to call
                self.tools = [Tool(function_declarations=[
                    FunctionDeclaration(
                        name="get_daily_sales",
                        description="Get daily sales data for a specific restaurant and date",
                        parameters={
                            "type": "object",
                            "properties": {
                                "restaurant_id": {
                                    "type": "string",
                                    "description": "Restaurant ID for multi-tenant filtering"
                                },
                                "target_date": {
                                    "type": "string",
                                    "description": "Date in YYYY-MM-DD format (optional, defaults to today)"
                                },
                                "include_items": {
                                    "type": "boolean",
                                    "description": "Whether to include item-level breakdown"
                                }
                            },
                            "required": ["restaurant_id"]
                        }
                    ),
                    FunctionDeclaration(
                        name="get_inventory_levels",
                        description="Get current inventory levels for a restaurant",
                        parameters={
                            "type": "object",
                            "properties": {
                                "restaurant_id": {
                                    "type": "string",
                                    "description": "Restaurant ID for multi-tenant filtering"
                                },
                                "item_name": {
                                    "type": "string",
                                    "description": "Specific item name to query (optional)"
                                },
                                "low_stock_only": {
                                    "type": "boolean",
                                    "description": "Only return items below reorder level"
                                }
                            },
                            "required": ["restaurant_id"]
                        }
                    ),
                    FunctionDeclaration(
                        name="get_customer_analytics",
                        description="Get customer analytics and behavior patterns",
                        parameters={
                            "type": "object",
                            "properties": {
                                "restaurant_id": {
                                    "type": "string",
                                    "description": "Restaurant ID for multi-tenant filtering"
                                },
                                "period_days": {
                                    "type": "integer",
                                    "description": "Number of days to analyze (default 30)"
                                }
                            },
                            "required": ["restaurant_id"]
                        }
                    ),
                    FunctionDeclaration(
                        name="get_menu_performance",
                        description="Get menu item performance analytics",
                        parameters={
                            "type": "object",
                            "properties": {
                                "restaurant_id": {
                                    "type": "string",
                                    "description": "Restaurant ID for multi-tenant filtering"
                                },
                                "period_days": {
                                    "type": "integer",
                                    "description": "Number of days to analyze"
                                },
                                "sort_by": {
                                    "type": "string",
                                    "description": "Sort criteria: 'revenue', 'quantity', or 'orders'"
                                }
                            },
                            "required": ["restaurant_id"]
                        }
                    ),
                    FunctionDeclaration(
                        name="get_operational_insights",
                        description="Get operational insights including table utilization and performance",
                        parameters={
                            "type": "object",
                            "properties": {
                                "restaurant_id": {
                                    "type": "string",
                                    "description": "Restaurant ID for multi-tenant filtering"
                                },
                                "period_days": {
                                    "type": "integer",
                                    "description": "Number of days to analyze"
                                }
                            },
                            "required": ["restaurant_id"]
                        }
                    ),
                    FunctionDeclaration(
                        name="get_revenue_trends",
                        description="Get revenue trends over time",
                        parameters={
                            "type": "object",
                            "properties": {
                                "restaurant_id": {
                                    "type": "string",
                                    "description": "Restaurant ID for multi-tenant filtering"
                                },
                                "period_days": {
                                    "type": "integer",
                                    "description": "Number of days to analyze"
                                },
                                "group_by": {
                                    "type": "string",
                                    "description": "Grouping period: 'day', 'week', or 'month'"
                                }
                            },
                            "required": ["restaurant_id"]
                        }
                    )
                ])]
                
                # Initialize model with tools
                self.model = genai.GenerativeModel(
                    model_name=self.model_name,
                    tools=self.tools
                )
                
                self.enabled = True
                log_ai_model_usage(self.model_name, "initialization", True, {
                    "temperature": self.temperature,
                    "max_tokens": self.max_tokens,
                    "function_calling": True
                })
                logger.info("Enhanced AI Service initialized successfully", "EnhancedAI")
                
            except Exception as e:
                self.enabled = False
                logger.error("Failed to initialize Enhanced AI Service", "EnhancedAI", {"error": str(e)})
                log_ai_model_usage(self.model_name, "initialization", False, {"error": str(e)})
        else:
            self.enabled = False
            logger.warning("Google AI API key not configured. Enhanced AI features will be disabled.", "EnhancedAI")
    
    def is_enabled(self) -> bool:
        """Check if Enhanced AI is properly configured"""
        return self.enabled
    
    async def _execute_function_call(self, function_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a function call from the AI"""
        try:
            logger.info("Executing AI function call", "EnhancedAI", {
                "function": function_name,
                "arguments": arguments
            })
            
            # Map function names to actual service methods
            function_map = {
                "get_daily_sales": ai_data_service.get_daily_sales,
                "get_inventory_levels": ai_data_service.get_inventory_levels,
                "get_customer_analytics": ai_data_service.get_customer_analytics,
                "get_menu_performance": ai_data_service.get_menu_performance,
                "get_operational_insights": ai_data_service.get_operational_insights,
                "get_revenue_trends": ai_data_service.get_revenue_trends
            }
            
            if function_name not in function_map:
                return {"error": f"Unknown function: {function_name}", "success": False}
            
            # Execute the function
            result = await function_map[function_name](**arguments)
            
            logger.info("Function call executed successfully", "EnhancedAI", {
                "function": function_name,
                "success": result.get("success", False)
            })
            
            return result
            
        except Exception as e:
            logger.error("Error executing function call", "EnhancedAI", {
                "function": function_name,
                "error": str(e)
            })
            return {"error": str(e), "success": False}


    async def process_intelligent_query(
        self,
        query: str,
        restaurant_id: str,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Process an intelligent query using function calling capabilities.

        Args:
            query: User's natural language query
            restaurant_id: Restaurant ID for multi-tenant filtering
            context: Additional context for the query

        Returns:
            Dictionary with AI response and any function call results
        """
        # Generate interaction ID for tracking
        interaction_id = str(uuid.uuid4())
        start_time = time.time()
        db_query_time = 0.0
        ai_response_time = 0.0
        function_calls = []
        success = False
        error = None
        response_text = ""

        if not self.enabled:
            error = "AI service disabled"
            response_text = "AI service is not available. Please check the configuration."

            # Record metrics even for disabled service
            await ai_metrics_service.record_interaction(
                interaction_id=interaction_id,
                restaurant_id=restaurant_id,
                user_id=context.get("user_id") if context else None,
                session_id=context.get("session_id") if context else None,
                query=query,
                response=response_text,
                function_calls=[],
                processing_time_ms=(time.time() - start_time) * 1000,
                database_query_time_ms=0.0,
                ai_response_time_ms=0.0,
                success=False,
                error=error
            )

            return {
                "success": False,
                "response": response_text,
                "ai_enabled": False,
                "error": error
            }

        try:
            # Create a restaurant-aware prompt
            system_prompt = f"""
            You are an intelligent restaurant management assistant for restaurant ID: {restaurant_id}.
            You have access to real-time restaurant data through function calls.

            When users ask about:
            - Sales data, revenue, or daily performance → use get_daily_sales or get_revenue_trends
            - Inventory levels, stock, or supplies → use get_inventory_levels
            - Customer behavior, analytics, or patterns → use get_customer_analytics
            - Menu performance, popular items, or item analysis → use get_menu_performance
            - Operations, table utilization, or efficiency → use get_operational_insights

            Always use the restaurant_id: {restaurant_id} when calling functions.
            Provide helpful, actionable insights based on the data you retrieve.
            If you need to call multiple functions to answer a question, do so.

            User Query: {query}
            """

            # Start conversation with the model
            chat = self.model.start_chat()

            # Send the query
            response = chat.send_message(
                system_prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=self.temperature,
                    max_output_tokens=self.max_tokens
                )
            )

            # Process function calls if any
            function_results = []
            if response.candidates[0].content.parts:
                for part in response.candidates[0].content.parts:
                    if hasattr(part, 'function_call') and part.function_call:
                        func_call = part.function_call
                        func_name = func_call.name
                        func_args = dict(func_call.args)

                        # Ensure restaurant_id is always included
                        func_args['restaurant_id'] = restaurant_id

                        # Execute the function call
                        func_result = await self._execute_function_call(func_name, func_args)
                        function_results.append({
                            "function": func_name,
                            "arguments": func_args,
                            "result": func_result
                        })

                        # Send function result back to the model
                        response = chat.send_message(
                            genai.types.Part.from_function_response(
                                name=func_name,
                                response=func_result
                            )
                        )

            # Get the final response text
            final_response = response.text if response.text else "I apologize, but I couldn't generate a response."

            logger.info("Intelligent query processed successfully", "EnhancedAI", {
                "query_length": len(query),
                "function_calls": len(function_results),
                "response_length": len(final_response)
            })

            return {
                "success": True,
                "response": final_response,
                "ai_enabled": True,
                "function_calls": function_results,
                "restaurant_id": restaurant_id,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error("Error processing intelligent query", "EnhancedAI", {
                "query": query[:100],  # Log first 100 chars
                "restaurant_id": restaurant_id,
                "error": str(e)
            })

            return {
                "success": False,
                "response": f"I encountered an error while processing your query: {str(e)}",
                "ai_enabled": self.enabled,
                "error": str(e),
                "restaurant_id": restaurant_id,
                "timestamp": datetime.now().isoformat()
            }

    async def get_restaurant_summary(self, restaurant_id: str) -> Dict[str, Any]:
        """
        Get a comprehensive summary of restaurant performance.

        Args:
            restaurant_id: Restaurant ID for multi-tenant filtering

        Returns:
            Dictionary with comprehensive restaurant insights
        """
        try:
            # Gather data from multiple sources
            tasks = [
                ai_data_service.get_daily_sales(restaurant_id),
                ai_data_service.get_inventory_levels(restaurant_id, low_stock_only=True),
                ai_data_service.get_customer_analytics(restaurant_id, period_days=7),
                ai_data_service.get_menu_performance(restaurant_id, period_days=7),
                ai_data_service.get_operational_insights(restaurant_id, period_days=7)
            ]

            results = await asyncio.gather(*tasks, return_exceptions=True)

            daily_sales, low_stock, customer_analytics, menu_performance, operations = results

            # Create summary prompt
            summary_data = {
                "daily_sales": daily_sales if not isinstance(daily_sales, Exception) else None,
                "low_stock_items": low_stock if not isinstance(low_stock, Exception) else None,
                "customer_analytics": customer_analytics if not isinstance(customer_analytics, Exception) else None,
                "menu_performance": menu_performance if not isinstance(menu_performance, Exception) else None,
                "operational_insights": operations if not isinstance(operations, Exception) else None
            }

            prompt = f"""
            Based on the following restaurant data, provide a comprehensive business summary with key insights and recommendations:

            Data Summary:
            {json.dumps(summary_data, indent=2, default=str)}

            Please provide:
            1. Key performance highlights
            2. Areas of concern that need attention
            3. Specific actionable recommendations
            4. Trends and patterns observed

            Format your response in a clear, executive summary style.
            """

            if self.enabled:
                response = self.model.generate_content(
                    prompt,
                    generation_config=genai.types.GenerationConfig(
                        temperature=self.temperature,
                        max_output_tokens=self.max_tokens
                    )
                )

                summary_text = response.text
            else:
                summary_text = "AI summary not available. Please check the configuration."

            return {
                "success": True,
                "summary": summary_text,
                "data": summary_data,
                "restaurant_id": restaurant_id,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error("Error generating restaurant summary", "EnhancedAI", {
                "restaurant_id": restaurant_id,
                "error": str(e)
            })

            return {
                "success": False,
                "error": str(e),
                "restaurant_id": restaurant_id,
                "timestamp": datetime.now().isoformat()
            }


# Global instance
enhanced_ai_service = EnhancedAIService()
