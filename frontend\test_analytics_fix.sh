#!/bin/bash

echo "=== Testing Analytics Page Fix ==="
echo

echo "1. Testing Frontend Server Status:"
if curl -s http://localhost:5173/ | head -c 50 | grep -q "<!DOCTYPE html>"; then
    echo "✅ Frontend server is running and serving content"
else
    echo "❌ Frontend server issues"
fi
echo

echo "2. Testing Analytics API Endpoints:"
echo "Sales endpoint:"
if curl -s http://localhost:5173/api/sales | head -c 50 | grep -q "date"; then
    echo "✅ Sales API working"
else
    echo "❌ Sales API issues"
fi

echo "Forecast endpoint:"
if curl -s http://localhost:5173/api/forecast | head -c 50 | grep -q "day"; then
    echo "✅ Forecast API working"
else
    echo "❌ Forecast API issues"
fi

echo "Advanced forecast endpoint:"
if curl -s http://localhost:5173/api/advanced-forecast | head -c 50 | grep -q "day"; then
    echo "✅ Advanced forecast API working"
else
    echo "❌ Advanced forecast API issues"
fi
echo

echo "3. Testing Analytics Page Route:"
analytics_response=$(curl -s "http://localhost:5173/admin/analytics" | head -c 100)
if [[ $analytics_response == *"<!DOCTYPE html>"* ]]; then
    echo "✅ Analytics page route is serving HTML content"
else
    echo "❌ Analytics page route issues"
fi
echo

echo "4. Checking for Missing Variables:"
if grep -q "mockStaffData" src/pages/Analytics.tsx; then
    echo "✅ mockStaffData variable exists"
else
    echo "❌ mockStaffData variable missing"
fi

if grep -q "mockMenuItems" src/pages/Analytics.tsx; then
    echo "✅ mockMenuItems variable exists"
else
    echo "❌ mockMenuItems variable missing"
fi

if grep -q "mockReviews" src/pages/Analytics.tsx; then
    echo "✅ mockReviews variable exists"
else
    echo "❌ mockReviews variable missing"
fi
echo

echo "5. Checking Required Service Functions:"
if grep -q "export const getUnavailabilityForWeek" src/services/staffAvailabilityService.ts; then
    echo "✅ getUnavailabilityForWeek function exported"
else
    echo "❌ getUnavailabilityForWeek function missing"
fi

if grep -q "export const getUnavailabilityForMonth" src/services/staffAvailabilityService.ts; then
    echo "✅ getUnavailabilityForMonth function exported"
else
    echo "❌ getUnavailabilityForMonth function missing"
fi

if grep -q "export const getUnresolvedConflicts" src/services/staffAvailabilityService.ts; then
    echo "✅ getUnresolvedConflicts function exported"
else
    echo "❌ getUnresolvedConflicts function missing"
fi
echo

echo "=== Summary ==="
echo "Frontend: http://localhost:5173"
echo "Analytics Page: http://localhost:5173/admin/analytics"
echo "Backend API: http://localhost:5001"
echo "API Documentation: http://localhost:5001/docs"
echo
echo "If all checks passed, the Analytics page should now load without errors!"
echo "The 'Error Loading App' and 'Fallback Application' messages should be resolved."
