# Subscription Models for RestroManage FastAPI Backend
# Migrated from backend/models/subscription.py

from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime
from decimal import Decimal

# Pydantic models for API requests/responses
class SubscriptionPlanResponse(BaseModel):
    id: str
    name: str
    description: Optional[str]
    price_monthly: float
    price_yearly: Optional[float]
    features: List[str]
    is_active: bool

class RestaurantSubscriptionResponse(BaseModel):
    restaurant_id: str
    plan_id: str
    plan_name: str
    status: str
    starts_at: datetime
    expires_at: datetime
    trial_ends_at: Optional[datetime]
    custom_features: List[str]
    days_remaining: int
    is_trial: bool
    is_active: bool

class FeatureAccessResponse(BaseModel):
    restaurant_id: str
    plan_id: str
    available_features: List[str]
    total_features: int
    access_level: str  # 'basic', 'pro', 'customized', 'trial_expired'

class FeatureCheckRequest(BaseModel):
    restaurant_id: str
    feature_id: str
    user_id: Optional[str] = None

class FeatureCheckResponse(BaseModel):
    feature_id: str
    has_access: bool
    reason: str
    plan_id: str
    timestamp: datetime

class SubscriptionPlanCreate(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    price_monthly: float
    price_yearly: Optional[float] = None
    features: List[str]
    is_active: bool = True

class RestaurantSubscriptionCreate(BaseModel):
    restaurant_id: str
    plan_id: str
    status: str = "trial"
    starts_at: datetime
    expires_at: datetime
    trial_ends_at: Optional[datetime] = None
    custom_features: List[str] = []
    billing_cycle: str = "monthly"
    auto_renew: bool = True

class FeatureDefinitionResponse(BaseModel):
    id: str
    name: str
    description: Optional[str]
    category: str
    is_active: bool

# Subscription plan definitions for the system
SUBSCRIPTION_PLANS = {
    "basic": {
        "id": "basic",
        "name": "Basic Plan",
        "description": "Essential restaurant management features",
        "price_monthly": 29.0,
        "price_yearly": 290.0,
        "features": [
            "analytics",
            "dashboard", 
            "inventory",
            "staff_management",
            "menu_management"
        ],
        "is_active": True
    },
    "pro": {
        "id": "pro", 
        "name": "Pro Plan",
        "description": "Complete restaurant management solution",
        "price_monthly": 69.0,
        "price_yearly": 690.0,
        "features": ["all"],  # Pro plan gets all features
        "is_active": True
    },
    "customized": {
        "id": "customized",
        "name": "Customized Plan", 
        "description": "Tailored solution with add-ons",
        "price_monthly": 99.0,
        "price_yearly": 990.0,
        "features": ["all"],  # Customized plan gets all features plus custom ones
        "is_active": True
    }
}

# Feature definitions for the system
FEATURE_DEFINITIONS = {
    "analytics": {
        "id": "analytics",
        "name": "Analytics Dashboard",
        "description": "Sales analytics and reporting",
        "category": "core"
    },
    "dashboard": {
        "id": "dashboard", 
        "name": "Management Dashboard",
        "description": "Restaurant overview and metrics",
        "category": "core"
    },
    "inventory": {
        "id": "inventory",
        "name": "Inventory Management", 
        "description": "Stock tracking and management",
        "category": "core"
    },
    "staff_management": {
        "id": "staff_management",
        "name": "Staff Management",
        "description": "Employee scheduling and management",
        "category": "core"
    },
    "menu_management": {
        "id": "menu_management",
        "name": "Menu Management",
        "description": "Menu item and category management", 
        "category": "core"
    },
    "epos": {
        "id": "epos",
        "name": "EPOS System",
        "description": "Electronic Point of Sale system",
        "category": "advanced"
    },
    "table_analytics": {
        "id": "table_analytics", 
        "name": "Table Analytics",
        "description": "Table turnover and utilization analytics",
        "category": "analytics"
    },
    "advanced_promotions": {
        "id": "advanced_promotions",
        "name": "Advanced Promotions",
        "description": "Complex discount and promotion management",
        "category": "advanced"
    },
    "llm_integration": {
        "id": "llm_integration",
        "name": "AI Integration", 
        "description": "Large Language Model integration for insights",
        "category": "ai"
    }
}
