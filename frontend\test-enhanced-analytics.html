<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Analytics System Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border-left: 4px solid #007bff;
            background-color: #f8f9fa;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .code {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            margin: 10px 0;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .metric-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .metric-title {
            font-weight: bold;
            color: #495057;
            margin-bottom: 5px;
        }
        .metric-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Enhanced Analytics System Test</h1>
        
        <div class="status info">
            <strong>Purpose:</strong> Test the comprehensive analytics enhancements including 90-day data, category analytics, and advanced forecasting.
        </div>

        <div class="test-section">
            <h3>📊 System Overview</h3>
            <div class="grid">
                <div class="metric-card">
                    <div class="metric-title">Menu Categories</div>
                    <div class="metric-value" id="categories-count">Loading...</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">Menu Items</div>
                    <div class="metric-value" id="items-count">Loading...</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">Historical Orders</div>
                    <div class="metric-value" id="orders-count">Loading...</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">Total Revenue</div>
                    <div class="metric-value" id="total-revenue">Loading...</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 API Endpoint Tests</h3>
            <button onclick="testAllEndpoints()">Test All Enhanced Endpoints</button>
            <button onclick="testCategoryPerformance()">Test Category Performance</button>
            <button onclick="testPeakHours()">Test Peak Hours Analysis</button>
            <button onclick="testProfitMargins()">Test Profit Margins</button>
            <div id="api-results"></div>
        </div>

        <div class="test-section">
            <h3>📈 Data Quality Analysis</h3>
            <button onclick="analyzeDataQuality()">Analyze Data Quality</button>
            <div id="data-analysis"></div>
        </div>

        <div class="test-section">
            <h3>🔮 Forecasting Accuracy</h3>
            <button onclick="testForecastingAccuracy()">Test Advanced Forecasting</button>
            <div id="forecasting-results"></div>
        </div>

        <div class="test-section">
            <h3>🎯 Performance Metrics</h3>
            <div id="performance-metrics"></div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:5001';
        
        function addResult(message, type = 'info', containerId = 'api-results') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `status ${type}`;
            resultDiv.innerHTML = message;
            container.appendChild(resultDiv);
        }

        function addCode(data, containerId = 'api-results') {
            const container = document.getElementById(containerId);
            const codeDiv = document.createElement('div');
            codeDiv.className = 'code';
            codeDiv.textContent = JSON.stringify(data, null, 2);
            container.appendChild(codeDiv);
        }

        async function testCategoryPerformance() {
            try {
                addResult('🔄 Testing category performance endpoint...', 'info');
                const response = await fetch(`${API_BASE_URL}/api/analytics/category-performance?days=30`);
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ Category performance: ${data.length} categories analyzed`, 'success');
                    
                    if (data.length > 0) {
                        const topCategory = data.reduce((max, cat) => 
                            cat.totalRevenue > max.totalRevenue ? cat : max, data[0]
                        );
                        addResult(`🏆 Top category: ${topCategory.category} (£${topCategory.totalRevenue.toFixed(2)})`, 'info');
                    }
                    
                    addCode(data.slice(0, 3)); // Show first 3 categories
                } else {
                    addResult(`❌ Category performance failed: ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Category performance error: ${error.message}`, 'error');
            }
        }

        async function testPeakHours() {
            try {
                addResult('🔄 Testing peak hours analysis...', 'info');
                const response = await fetch(`${API_BASE_URL}/api/analytics/peak-hours?days=30`);
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ Peak hours analysis: ${data.length} hours analyzed`, 'success');
                    
                    if (data.length > 0) {
                        const peakHour = data.reduce((max, hour) => 
                            hour.totalRevenue > max.totalRevenue ? hour : max, data[0]
                        );
                        addResult(`⏰ Peak hour: ${peakHour.hour}:00 (£${peakHour.totalRevenue.toFixed(2)})`, 'info');
                    }
                    
                    addCode(data.slice(0, 5)); // Show first 5 hours
                } else {
                    addResult(`❌ Peak hours analysis failed: ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Peak hours error: ${error.message}`, 'error');
            }
        }

        async function testProfitMargins() {
            try {
                addResult('🔄 Testing profit margin analysis...', 'info');
                const response = await fetch(`${API_BASE_URL}/api/analytics/profit-margins?days=30`);
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ Profit margin analysis: ${data.length} categories analyzed`, 'success');
                    
                    if (data.length > 0) {
                        const avgMargin = data.reduce((sum, cat) => sum + cat.marginPercentage, 0) / data.length;
                        addResult(`💰 Average profit margin: ${avgMargin.toFixed(1)}%`, 'info');
                    }
                    
                    addCode(data.slice(0, 3)); // Show first 3 categories
                } else {
                    addResult(`❌ Profit margin analysis failed: ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Profit margin error: ${error.message}`, 'error');
            }
        }

        async function testAllEndpoints() {
            document.getElementById('api-results').innerHTML = '';
            addResult('🚀 Testing all enhanced analytics endpoints...', 'info');
            
            await testCategoryPerformance();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testPeakHours();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testProfitMargins();
            
            addResult('🎉 All endpoint tests completed!', 'success');
        }

        async function analyzeDataQuality() {
            try {
                document.getElementById('data-analysis').innerHTML = '';
                addResult('🔍 Analyzing data quality...', 'info', 'data-analysis');
                
                // Test sales data
                const salesResponse = await fetch(`${API_BASE_URL}/api/analytics/sales`);
                const salesData = await salesResponse.json();
                
                // Test category data
                const categoryResponse = await fetch(`${API_BASE_URL}/api/analytics/category-performance?days=90`);
                const categoryData = await categoryResponse.json();
                
                addResult(`📊 Sales data points: ${salesData.length}`, 'info', 'data-analysis');
                addResult(`📂 Categories with data: ${categoryData.length}`, 'info', 'data-analysis');
                
                if (salesData.length >= 50) {
                    addResult('✅ Sufficient sales data for accurate forecasting', 'success', 'data-analysis');
                } else {
                    addResult('⚠️ Limited sales data - forecasting may be less accurate', 'warning', 'data-analysis');
                }
                
                if (categoryData.length >= 4) {
                    addResult('✅ Good category diversity for analysis', 'success', 'data-analysis');
                } else {
                    addResult('⚠️ Limited category data', 'warning', 'data-analysis');
                }
                
            } catch (error) {
                addResult(`❌ Data quality analysis failed: ${error.message}`, 'error', 'data-analysis');
            }
        }

        async function testForecastingAccuracy() {
            try {
                document.getElementById('forecasting-results').innerHTML = '';
                addResult('🔮 Testing advanced forecasting...', 'info', 'forecasting-results');
                
                const response = await fetch(`${API_BASE_URL}/api/analytics/forecast`);
                const forecastData = await response.json();
                
                if (forecastData.length > 0) {
                    addResult(`✅ Generated ${forecastData.length} forecast periods`, 'success', 'forecasting-results');
                    
                    // Check for advanced forecast features
                    const hasConfidence = forecastData.some(f => f.confidence !== undefined);
                    const hasSeasonalFactors = forecastData.some(f => f.seasonalFactor !== undefined);
                    const hasGrowthFactors = forecastData.some(f => f.growthFactor !== undefined);
                    
                    if (hasConfidence) {
                        addResult('✅ Confidence intervals included', 'success', 'forecasting-results');
                    }
                    if (hasSeasonalFactors) {
                        addResult('✅ Seasonal adjustments applied', 'success', 'forecasting-results');
                    }
                    if (hasGrowthFactors) {
                        addResult('✅ Growth trends incorporated', 'success', 'forecasting-results');
                    }
                    
                    addCode(forecastData.slice(0, 3), 'forecasting-results');
                } else {
                    addResult('❌ No forecast data generated', 'error', 'forecasting-results');
                }
                
            } catch (error) {
                addResult(`❌ Forecasting test failed: ${error.message}`, 'error', 'forecasting-results');
            }
        }

        async function loadSystemOverview() {
            try {
                // Load basic metrics
                const [salesResponse, categoryResponse] = await Promise.all([
                    fetch(`${API_BASE_URL}/api/analytics/sales`),
                    fetch(`${API_BASE_URL}/api/analytics/category-performance?days=90`)
                ]);
                
                const salesData = await salesResponse.json();
                const categoryData = await categoryResponse.json();
                
                // Update metrics
                document.getElementById('categories-count').textContent = categoryData.length;
                document.getElementById('items-count').textContent = categoryData.reduce((sum, cat) => sum + cat.uniqueItems, 0);
                document.getElementById('orders-count').textContent = salesData.length;
                
                const totalRevenue = categoryData.reduce((sum, cat) => sum + cat.totalRevenue, 0);
                document.getElementById('total-revenue').textContent = `£${totalRevenue.toFixed(0)}`;
                
                // Performance metrics
                const performanceContainer = document.getElementById('performance-metrics');
                performanceContainer.innerHTML = `
                    <div class="grid">
                        <div class="metric-card">
                            <div class="metric-title">Data Coverage</div>
                            <div class="metric-value">${salesData.length >= 50 ? '✅ Excellent' : '⚠️ Limited'}</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-title">Category Diversity</div>
                            <div class="metric-value">${categoryData.length >= 4 ? '✅ Good' : '⚠️ Limited'}</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-title">Forecast Accuracy</div>
                            <div class="metric-value">📈 Enhanced</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-title">System Status</div>
                            <div class="metric-value">🟢 Operational</div>
                        </div>
                    </div>
                `;
                
            } catch (error) {
                console.error('Failed to load system overview:', error);
                document.getElementById('categories-count').textContent = 'Error';
                document.getElementById('items-count').textContent = 'Error';
                document.getElementById('orders-count').textContent = 'Error';
                document.getElementById('total-revenue').textContent = 'Error';
            }
        }

        // Auto-load system overview on page load
        window.addEventListener('load', () => {
            loadSystemOverview();
            addResult('🔄 Enhanced analytics system test page loaded', 'info');
        });
    </script>
</body>
</html>
