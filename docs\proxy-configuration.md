# Proxy Configuration Guide

## 🔧 Vite Proxy Configuration

The frontend uses <PERSON><PERSON>'s built-in proxy to route API requests to the backend server during development.

### Current Configuration (`frontend/vite.config.ts`)

```typescript
export default defineConfig({
  server: {
    host: "0.0.0.0",
    port: 5175,
    strictPort: false,
    proxy: {
      "/api": "http://localhost:5001",      // API endpoints
      "/health": "http://localhost:5001"   // Health check
    },
    cors: true,
    hmr: {
      host: "localhost",
      port: 5177
    }
  }
});
```

## 🚨 **CRITICAL FIX REQUIRED**

### Problem
The proxy is configured to route to port `5001`, but the backend runs on port `5002` by default.

### Solution Options

#### Option 1: Update Proxy Configuration (Recommended)
```typescript
// frontend/vite.config.ts
proxy: {
  "/api": "http://localhost:5002",      // ✅ Match backend port
  "/health": "http://localhost:5002"   // ✅ Match backend port
}
```

#### Option 2: Change Backend Port
```bash
# Set environment variable before starting backend
export PORT=5001
cd Backend
python main.py
```

#### Option 3: Use Environment Variable
```typescript
// frontend/vite.config.ts
const backendUrl = process.env.VITE_BACKEND_URL || "http://localhost:5002";

export default defineConfig({
  server: {
    proxy: {
      "/api": backendUrl,
      "/health": backendUrl
    }
  }
});
```

## 🔄 Proxy Routing Rules

### How Vite Proxy Works
```
Frontend Request: http://localhost:5175/api/restaurants
                     ↓ (Vite Proxy)
Backend Request:  http://localhost:5002/api/restaurants
                     ↓ (FastAPI)
Response:         JSON data
                     ↓ (Vite Proxy)
Frontend:         Receives response
```

### Proxy Path Mapping
| Frontend URL | Backend URL | Description |
|-------------|-------------|-------------|
| `/api/*` | `http://localhost:5002/api/*` | All API endpoints |
| `/health` | `http://localhost:5002/health` | Health check |
| `/docs` | `http://localhost:5002/docs` | API documentation |
| `/redoc` | `http://localhost:5002/redoc` | Alternative API docs |

## 🔧 Advanced Proxy Configuration

### With Request/Response Logging
```typescript
export default defineConfig({
  server: {
    proxy: {
      "/api": {
        target: "http://localhost:5002",
        changeOrigin: true,
        secure: false,
        configure: (proxy, options) => {
          proxy.on('error', (err, req, res) => {
            console.log('❌ Proxy Error:', err);
          });
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('📤 Proxy Request:', req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, res) => {
            console.log('📥 Proxy Response:', proxyRes.statusCode, req.url);
          });
        }
      },
      "/health": {
        target: "http://localhost:5002",
        changeOrigin: true
      }
    }
  }
});
```

### With Custom Headers
```typescript
proxy: {
  "/api": {
    target: "http://localhost:5002",
    changeOrigin: true,
    headers: {
      "X-Forwarded-For": "localhost",
      "X-Forwarded-Proto": "http"
    }
  }
}
```

### Environment-Based Configuration
```typescript
// frontend/vite.config.ts
const isDevelopment = process.env.NODE_ENV === 'development';
const backendUrl = process.env.VITE_BACKEND_URL || 
  (isDevelopment ? "http://localhost:5002" : "https://api.yourdomain.com");

export default defineConfig({
  server: {
    proxy: isDevelopment ? {
      "/api": backendUrl,
      "/health": backendUrl
    } : undefined
  }
});
```

## 🔍 Testing Proxy Configuration

### 1. Direct Backend Test
```bash
# Test backend directly
curl http://localhost:5002/health
curl http://localhost:5002/api/restaurants
```

### 2. Through Proxy Test
```bash
# Test through Vite proxy
curl http://localhost:5175/health
curl http://localhost:5175/api/restaurants
```

### 3. Browser Network Tab
1. Open browser DevTools (F12)
2. Go to Network tab
3. Navigate to `http://localhost:5175`
4. Make API calls and check:
   - Request URL shows `localhost:5175`
   - Response comes from backend
   - No CORS errors

## 🚫 Common Proxy Issues

### Issue 1: ECONNREFUSED
```
Error: connect ECONNREFUSED 127.0.0.1:5001
```
**Solution**: Backend not running or wrong port in proxy config.

### Issue 2: CORS Errors
```
Access to fetch at 'http://localhost:5002/api/...' from origin 'http://localhost:5175' has been blocked by CORS policy
```
**Solution**: Ensure backend CORS allows frontend origin.

### Issue 3: 404 Not Found
```
GET http://localhost:5175/api/restaurants 404 (Not Found)
```
**Solution**: Check proxy path mapping and backend route definitions.

### Issue 4: Timeout Errors
```
Error: timeout of 5000ms exceeded
```
**Solution**: Increase proxy timeout or check backend performance.

## 🔧 Backend CORS Configuration

Ensure backend allows frontend origin:

```python
# Backend/app/api.py
origins = [
    "http://localhost:5173",
    "http://localhost:5174", 
    "http://localhost:5175",  # ✅ Current frontend port
    "http://localhost:5176",
    "http://127.0.0.1:5175",  # ✅ Alternative localhost
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)
```

## 🐳 Docker Proxy Configuration

### Development with Docker
```yaml
# docker-compose.yml
services:
  frontend:
    build: ./frontend
    ports:
      - "5175:5175"
    environment:
      - VITE_BACKEND_URL=http://backend:5002
    depends_on:
      - backend
  
  backend:
    build: ./Backend
    ports:
      - "5002:5002"
    environment:
      - PORT=5002
```

### Production Nginx Proxy
```nginx
# nginx.conf
server {
    listen 80;
    
    location / {
        root /usr/share/nginx/html;
        try_files $uri $uri/ /index.html;
    }
    
    location /api/ {
        proxy_pass http://backend:8000/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location /health {
        proxy_pass http://backend:8000/health;
    }
}
```

## 📋 Proxy Configuration Checklist

- [ ] **Backend Port**: Verify actual backend port (check `netstat`)
- [ ] **Proxy Target**: Update Vite config to match backend port
- [ ] **CORS Origins**: Ensure backend allows frontend origin
- [ ] **Path Mapping**: Verify all required paths are proxied
- [ ] **Error Handling**: Add proxy error logging
- [ ] **Environment Variables**: Use env vars for different environments
- [ ] **Testing**: Test both direct and proxied requests

## 🔧 Quick Fix Commands

```bash
# 1. Check current backend port
netstat -an | findstr :5002

# 2. Update frontend proxy (if backend on 5002)
# Edit frontend/vite.config.ts:
# Change "http://localhost:5001" to "http://localhost:5002"

# 3. Restart frontend
cd frontend
npm run dev

# 4. Test proxy
curl http://localhost:5175/health
```

---

**Next Steps**:
- Apply the proxy fix from [Connection Issues](./connection-issues.md)
- Test with [API Documentation](./api-documentation.md) endpoints
- Review [Troubleshooting Guide](./troubleshooting-guide.md) for other issues
