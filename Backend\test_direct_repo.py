"""
Test script to directly test repository without going through the service layer
"""

import asyncio
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.database import AsyncSessionLocal
from app.repositories.restaurant import RestaurantRepository
from app.models.database_models import Restaurant
from sqlalchemy import select, text, func

async def test_direct_repository():
    """Test repository directly"""
    try:
        print("Testing repository directly...")
        
        async with AsyncSessionLocal() as session:
            print(f"Session created: {session}")
            
            # Test raw count first
            print("\n1. Testing raw count...")
            result = await session.execute(text("SELECT COUNT(*) FROM restaurants"))
            raw_count = result.scalar()
            print(f"Raw count: {raw_count}")
            
            # Test repository
            print("\n2. Testing repository...")
            repo = RestaurantRepository()
            print(f"Repository created: {repo}")
            
            # Test repository get_all method
            print("\n3. Testing repository.get_all()...")
            restaurants = await repo.get_all(session)
            print(f"Repository returned {len(restaurants)} restaurants")
            
            for restaurant in restaurants[:3]:
                print(f"  - {restaurant.name} (ID: {restaurant.id}, Code: {restaurant.code})")
            
            # Test repository get_all with different parameters
            print("\n4. Testing repository.get_all(skip=0, limit=5)...")
            restaurants_limited = await repo.get_all(session, skip=0, limit=5)
            print(f"Repository returned {len(restaurants_limited)} restaurants (limited)")
            
            # Test direct SQLAlchemy query
            print("\n5. Testing direct SQLAlchemy query...")
            result = await session.execute(select(Restaurant).limit(3))
            direct_restaurants = result.scalars().all()
            print(f"Direct SQLAlchemy query returned {len(direct_restaurants)} restaurants")
            
            for restaurant in direct_restaurants:
                print(f"  - {restaurant.name} (ID: {restaurant.id}, Code: {restaurant.code})")
        
        print("\n✅ Direct repository test completed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Direct repository test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_direct_repository())
    sys.exit(0 if success else 1)
