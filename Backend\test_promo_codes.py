#!/usr/bin/env python3
"""
Test script to check promo codes and their validity.
"""

import sys
import os
from datetime import datetime

# Add the Backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.utils.storage import get_all

def test_promo_codes():
    """Test promo codes and their validity"""
    
    print("🔍 Testing promo codes...")
    
    # Get all promo codes
    all_codes = get_all("promo_codes")
    print(f"📊 Found {len(all_codes)} total promo codes")
    
    if not all_codes:
        print("❌ No promo codes found!")
        return
    
    now = datetime.now()
    print(f"🕐 Current time: {now}")
    
    for i, code in enumerate(all_codes, 1):
        print(f"\n--- Promo Code {i} ---")
        print(f"Code: {code.get('code')}")
        print(f"Name: {code.get('name')}")
        print(f"Active: {code.get('is_active')}")
        print(f"Start Date: {code.get('start_date')}")
        print(f"End Date: {code.get('end_date')}")
        
        try:
            start_date = datetime.fromisoformat(code["start_date"])
            end_date = datetime.fromisoformat(code["end_date"])
            
            print(f"Parsed Start: {start_date}")
            print(f"Parsed End: {end_date}")
            
            is_time_valid = start_date <= now <= end_date
            is_active = code.get("is_active", True)
            
            print(f"Time Valid: {is_time_valid}")
            print(f"Is Active: {is_active}")
            print(f"Overall Valid: {is_active and is_time_valid}")
            
        except Exception as e:
            print(f"❌ Error parsing dates: {e}")
    
    # Filter for active codes
    active_codes = []
    for code in all_codes:
        try:
            is_active = code.get("is_active", True)
            start_date = datetime.fromisoformat(code["start_date"])
            end_date = datetime.fromisoformat(code["end_date"])
            is_time_valid = start_date <= now <= end_date
            
            if is_active and is_time_valid:
                active_codes.append(code)
        except Exception as e:
            print(f"❌ Error processing code {code.get('code')}: {e}")
    
    print(f"\n✅ Active promo codes: {len(active_codes)}")
    for code in active_codes:
        print(f"  - {code.get('code')}: {code.get('name')}")

if __name__ == "__main__":
    test_promo_codes()
