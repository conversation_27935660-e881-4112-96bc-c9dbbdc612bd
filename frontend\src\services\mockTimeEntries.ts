import { TimeEntry } from './timeEntryService';

// Generate mock time entries for the past 30 days
export const generateMockTimeEntries = (staffData: any[]): TimeEntry[] => {
  const entries: TimeEntry[] = [];
  const now = new Date();
  const startDate = new Date(now);
  startDate.setDate(now.getDate() - 30);

  let currentDate = new Date(startDate);
  let id = 1;

  while (currentDate <= now) {
    // For each staff member
    staffData.forEach(staff => {
      // Skip some days randomly to make it more realistic
      if (Math.random() > 0.8) {
        return;
      }

      // Skip weekends for some staff members
      const day = currentDate.getDay();
      if (parseInt(staff.id) % 2 === 0 && (day === 0 || day === 6)) {
        return;
      }

      // Random clock in time between 8:00 and 10:00
      const clockInHour = 8 + Math.floor(Math.random() * 2);
      const clockInMinute = Math.floor(Math.random() * 30);
      const clockIn = `${clockInHour.toString().padStart(2, '0')}:${clockInMinute.toString().padStart(2, '0')}`;

      // Random shift length between 6 and 9 hours
      const shiftLength = 6 + Math.floor(Math.random() * 3);
      
      // Random break time between 30 and 60 minutes
      const breakTime = 30 + Math.floor(Math.random() * 31);

      // Calculate clock out time
      const clockOutHour = clockInHour + shiftLength;
      const clockOutMinute = clockInMinute;
      const clockOut = `${clockOutHour.toString().padStart(2, '0')}:${clockOutMinute.toString().padStart(2, '0')}`;

      // Calculate total hours (shift length minus break time in hours)
      const totalHours = shiftLength - (breakTime / 60);

      entries.push({
        id: id.toString(),
        staffId: staff.id,
        date: currentDate.toISOString().split('T')[0],
        clockIn,
        clockOut,
        breakStart: null,
        breakEnd: null,
        breakTime,
        totalHours
      });

      id++;
    });

    currentDate.setDate(currentDate.getDate() + 1);
  }

  return entries;
};

// Initialize mock time entries in localStorage
export const initializeMockTimeEntries = (staffData: any[]): void => {
  const TIME_ENTRIES_KEY = 'timeEntries';
  
  try {
    // Only initialize if no entries exist yet
    if (typeof window !== 'undefined' && window.localStorage) {
      if (!localStorage.getItem(TIME_ENTRIES_KEY)) {
        const mockEntries = generateMockTimeEntries(staffData);
        localStorage.setItem(TIME_ENTRIES_KEY, JSON.stringify(mockEntries));
        console.log(`Initialized ${mockEntries.length} mock time entries`);
      } else {
        console.log('Time entries already exist in localStorage');
      }
    } else {
      console.error('localStorage is not available');
    }
  } catch (error) {
    console.error('Error initializing mock time entries:', error);
  }
};
