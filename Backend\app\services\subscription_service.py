# Subscription Service - Core Business Logic
# Migrated from backend/services/subscription_service.py

from typing import List, Dict, Optional
from datetime import datetime, timedelta
import logging

from app.models.subscription import (
    RestaurantSubscriptionResponse, FeatureAccessResponse, FeatureCheckResponse,
    SUBSCRIPTION_PLANS, FEATURE_DEFINITIONS
)
from app.utils.storage import get_all, get_by_id, create, update, query

logger = logging.getLogger(__name__)

class SubscriptionService:
    """Service for managing restaurant subscriptions and feature access"""
    
    def __init__(self):
        self.plans = SUBSCRIPTION_PLANS
        self.features = FEATURE_DEFINITIONS

    async def get_restaurant_subscription(self, restaurant_id: str) -> Optional[RestaurantSubscriptionResponse]:
        """Get restaurant subscription with calculated fields"""
        # Get restaurant data to determine subscription
        restaurants = get_all("restaurants")
        restaurant = next((r for r in restaurants if r.get("id") == restaurant_id), None)
        
        if not restaurant:
            return None
        
        # Get subscription plan from restaurant data
        plan_id = restaurant.get("subscription_plan", "basic")
        plan = self.plans.get(plan_id, self.plans["basic"])
        
        now = datetime.utcnow()
        
        # Calculate subscription details
        starts_at = datetime.fromisoformat(restaurant.get("createdAt", now.isoformat()))
        expires_at = starts_at + timedelta(days=365)  # Default 1 year
        trial_ends_at = starts_at + timedelta(days=30)  # 30 day trial
        
        days_remaining = max(0, (expires_at - now).days)
        is_trial = (now - starts_at).days <= 30
        is_active = expires_at > now
        
        return RestaurantSubscriptionResponse(
            restaurant_id=restaurant_id,
            plan_id=plan_id,
            plan_name=plan["name"],
            status="active" if is_active else "expired",
            starts_at=starts_at,
            expires_at=expires_at,
            trial_ends_at=trial_ends_at,
            custom_features=[],
            days_remaining=days_remaining,
            is_trial=is_trial,
            is_active=is_active
        )

    async def get_available_features(self, restaurant_id: str) -> FeatureAccessResponse:
        """Get all available features for a restaurant"""
        subscription = await self.get_restaurant_subscription(restaurant_id)
        
        if not subscription:
            # Default to basic plan for new restaurants
            return FeatureAccessResponse(
                restaurant_id=restaurant_id,
                plan_id="basic",
                available_features=list(self.plans["basic"]["features"]),
                total_features=len(self.plans["basic"]["features"]),
                access_level="basic"
            )
        
        plan_id = subscription.plan_id
        plan = self.plans.get(plan_id, self.plans["basic"])
        
        # Calculate available features
        if plan_id == "pro":
            # PRO PLAN: All features available
            available_features = list(self.features.keys())
            access_level = "pro"
        elif plan_id == "customized":
            # CUSTOMIZED PLAN: All features + custom additions
            available_features = list(self.features.keys())
            access_level = "customized"
        else:
            # BASIC PLAN: Limited features
            available_features = plan["features"]
            access_level = "basic"
        
        # Check if trial expired
        if subscription.is_trial and not subscription.is_active:
            available_features = ["dashboard"]  # Only dashboard for expired trials
            access_level = "trial_expired"
        
        return FeatureAccessResponse(
            restaurant_id=restaurant_id,
            plan_id=plan_id,
            available_features=available_features,
            total_features=len(available_features),
            access_level=access_level
        )

    async def check_feature_access(
        self, 
        restaurant_id: str, 
        feature_id: str, 
        user_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> FeatureCheckResponse:
        """Check access to a specific feature and log the attempt"""
        
        subscription = await self.get_restaurant_subscription(restaurant_id)
        has_access = False
        reason = "no_subscription"
        plan_id = "none"
        
        if subscription:
            plan_id = subscription.plan_id
            features_response = await self.get_available_features(restaurant_id)
            has_access = feature_id in features_response.available_features
            
            if has_access:
                if subscription.plan_id == "pro":
                    reason = "pro_plan_access"
                elif subscription.plan_id == "customized":
                    reason = "customized_plan_access"
                else:
                    reason = "basic_plan_access"
            else:
                if features_response.access_level == "trial_expired":
                    reason = "trial_expired"
                else:
                    reason = "insufficient_plan"
        
        # Log the access attempt (simplified for JSON storage)
        access_log = {
            "id": f"log_{datetime.utcnow().timestamp()}",
            "restaurant_id": restaurant_id,
            "feature_id": feature_id,
            "access_granted": has_access,
            "access_reason": reason,
            "user_id": user_id,
            "ip_address": ip_address,
            "user_agent": user_agent,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # Store access log
        logs = get_all("feature_access_logs")
        logs.append(access_log)
        
        return FeatureCheckResponse(
            feature_id=feature_id,
            has_access=has_access,
            reason=reason,
            plan_id=plan_id,
            timestamp=datetime.utcnow()
        )

    async def bulk_check_access(self, restaurant_id: str, feature_ids: List[str]) -> Dict[str, bool]:
        """Check access for multiple features efficiently"""
        features_response = await self.get_available_features(restaurant_id)
        available_features = set(features_response.available_features)
        
        return {feature_id: feature_id in available_features for feature_id in feature_ids}

    async def update_subscription(self, restaurant_id: str, plan_id: str) -> RestaurantSubscriptionResponse:
        """Update restaurant subscription plan"""
        restaurants = get_all("restaurants")
        restaurant = next((r for r in restaurants if r.get("id") == restaurant_id), None)
        
        if not restaurant:
            raise ValueError(f"Restaurant {restaurant_id} not found")
        
        if plan_id not in self.plans:
            raise ValueError(f"Plan {plan_id} not found")
        
        # Update restaurant subscription plan
        restaurant["subscription_plan"] = plan_id
        restaurant["updatedAt"] = datetime.utcnow().isoformat()
        
        # Update the restaurant in storage
        update("restaurants", restaurant_id, restaurant)
        
        return await self.get_restaurant_subscription(restaurant_id)

    async def get_all_plans(self) -> List[Dict]:
        """Get all active subscription plans"""
        return [plan for plan in self.plans.values() if plan.get("is_active", True)]

    async def get_access_logs(self, restaurant_id: str, limit: int = 100) -> List[Dict]:
        """Get recent access logs for a restaurant"""
        logs = get_all("feature_access_logs")
        restaurant_logs = [log for log in logs if log.get("restaurant_id") == restaurant_id]
        
        # Sort by timestamp descending and limit
        restaurant_logs.sort(key=lambda x: x.get("timestamp", ""), reverse=True)
        return restaurant_logs[:limit]

    def has_feature_access(self, restaurant_id: str, feature_id: str) -> bool:
        """Synchronous feature access check for performance"""
        restaurants = get_all("restaurants")
        restaurant = next((r for r in restaurants if r.get("id") == restaurant_id), None)
        
        if not restaurant:
            return False
        
        plan_id = restaurant.get("subscription_plan", "basic")
        plan = self.plans.get(plan_id, self.plans["basic"])
        
        # Pro plan gets all features
        if plan_id == "pro":
            return True
        
        # Check if feature is in plan
        return feature_id in plan.get("features", [])
