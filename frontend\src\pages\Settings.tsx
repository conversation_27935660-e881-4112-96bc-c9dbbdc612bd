import React, { useState, useEffect, useRef } from "react";
import Layout from "@/components/layout/Layout";
import { useTheme } from "next-themes";
import { toast } from "@/components/ui/sonner";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { ExternalLink, Mail, Phone, Github, Upload, Image, Table, Save, CreditCard, Building2 } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import RestaurantManagement from "@/components/admin/RestaurantManagement";

const Settings = () => {
  const [activeTab, setActiveTab] = useState("setup");

  // Theme handling
  const { theme, setTheme } = useTheme();

  // System settings
  const [systemSettings, setSystemSettings] = useState({
    theme: "light",
    maxDiscountPercent: 40
  });

  // Promo code management state
  const [showPromoForm, setShowPromoForm] = useState(false);
  const [editingPromo, setEditingPromo] = useState(null);
  const [promoForm, setPromoForm] = useState({
    code: "",
    name: "",
    description: "",
    discount_type: "percentage",
    discount_value: 0,
    min_order_value: 0,
    max_discount: 0,
    usage_limit: 100,
    valid_from: "",
    valid_until: "",
    is_active: true
  });

  // Gift card management state
  const [showGiftCardForm, setShowGiftCardForm] = useState(false);
  const [giftCardAmount, setGiftCardAmount] = useState(0);

  // Restaurant setup settings
  const [restaurantSettings, setRestaurantSettings] = useState({
    name: "Promith",
    address: "",
    phone: "",
    email: "",
    website: "",
    logo: null as File | null,
    logoPreview: "",
    tableCount: 10,
    openingHours: {
      monday: { open: "09:00", close: "22:00", closed: false },
      tuesday: { open: "09:00", close: "22:00", closed: false },
      wednesday: { open: "09:00", close: "22:00", closed: false },
      thursday: { open: "09:00", close: "22:00", closed: false },
      friday: { open: "09:00", close: "23:00", closed: false },
      saturday: { open: "10:00", close: "23:00", closed: false },
      sunday: { open: "10:00", close: "22:00", closed: false }
    }
  });

  // Sync theme from provider
  useEffect(() => {
    if (theme) {
      setSystemSettings(prev => ({
        ...prev,
        theme: theme
      }));
    }
  }, [theme]);

  // Load saved restaurant settings from localStorage
  useEffect(() => {
    const savedSettings = localStorage.getItem('restaurantSettings');
    if (savedSettings) {
      try {
        const parsedSettings = JSON.parse(savedSettings);
        // If there's a logo preview, restore it
        if (parsedSettings.logoPreview) {
          setRestaurantSettings(prev => ({
            ...prev,
            ...parsedSettings
          }));
        } else {
          // Don't overwrite the logo preview if it's not in saved settings
          setRestaurantSettings(prev => ({
            ...prev,
            ...parsedSettings,
            logoPreview: prev.logoPreview
          }));
        }
      } catch (error) {
        console.error("Error parsing saved restaurant settings:", error);
      }
    }
  }, []);

  // Handle theme change
  const handleThemeChange = (value) => {
    setSystemSettings({...systemSettings, theme: value});
    setTheme(value);
    toast.success(`Theme changed to ${value}`);
  };

  // Handle restaurant settings change
  const handleRestaurantSettingChange = (field, value) => {
    setRestaurantSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle opening hours change
  const handleOpeningHoursChange = (day, field, value) => {
    setRestaurantSettings(prev => ({
      ...prev,
      openingHours: {
        ...prev.openingHours,
        [day]: {
          ...prev.openingHours[day],
          [field]: value
        }
      }
    }));
  };

  // Handle logo upload
  const handleLogoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Create a preview URL
      const previewUrl = URL.createObjectURL(file);

      setRestaurantSettings(prev => ({
        ...prev,
        logo: file,
        logoPreview: previewUrl
      }));

      toast.success("Logo uploaded successfully");
    }
  };

  // Save restaurant settings
  const saveRestaurantSettings = () => {
    // Save to localStorage (in a real app, this would be saved to a database)
    const settingsToSave = {
      ...restaurantSettings,
      // Don't save the actual File object, just the preview URL
      logo: null
    };

    localStorage.setItem('restaurantSettings', JSON.stringify(settingsToSave));
    toast.success("Restaurant settings saved successfully");
  };

  // Promo code management functions
  const updatePromoForm = (field, value) => {
    setPromoForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const savePromoCode = () => {
    if (!promoForm.code || !promoForm.name) {
      toast.error("Please fill in required fields");
      return;
    }

    // In a real app, this would save to the database
    toast.success(editingPromo ? "Promo code updated successfully" : "Promo code created successfully");
    setShowPromoForm(false);
    setEditingPromo(null);
    setPromoForm({
      code: "",
      name: "",
      description: "",
      discount_type: "percentage",
      discount_value: 0,
      min_order_value: 0,
      max_discount: 0,
      usage_limit: 100,
      valid_from: "",
      valid_until: "",
      is_active: true
    });
  };

  // Gift card management functions
  const generateGiftCard = () => {
    if (!giftCardAmount || giftCardAmount <= 0) {
      toast.error("Please enter a valid amount");
      return;
    }

    // Generate a unique gift card code
    const giftCardCode = `GC-${Math.random().toString(36).substr(2, 6).toUpperCase()}`;

    // In a real app, this would save to the database
    toast.success(`Gift card generated successfully! Code: ${giftCardCode}`);
    setShowGiftCardForm(false);
    setGiftCardAmount(0);
  };



  return (
    <Layout title="Settings">
      <div className="p-4">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="setup">Restaurant Setup</TabsTrigger>
            <TabsTrigger value="management">Restaurant Management</TabsTrigger>
            <TabsTrigger value="theme">Theme</TabsTrigger>
            <TabsTrigger value="about">About</TabsTrigger>
            <TabsTrigger value="help">Help</TabsTrigger>
          </TabsList>

          <TabsContent value="setup">
            <Card>
              <CardHeader>
                <CardTitle>Restaurant Setup</CardTitle>
                <CardDescription>Configure your restaurant details and preferences</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Basic Information */}
                    <div className="space-y-4">
                      <div>
                        <h3 className="text-lg font-medium mb-4">Basic Information</h3>
                        <div className="space-y-4">
                          <div className="space-y-2">
                            <Label htmlFor="restaurant-name">Restaurant Name</Label>
                            <Input
                              id="restaurant-name"
                              value={restaurantSettings.name}
                              onChange={(e) => handleRestaurantSettingChange('name', e.target.value)}
                              placeholder="Enter restaurant name"
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="restaurant-address">Address</Label>
                            <Textarea
                              id="restaurant-address"
                              value={restaurantSettings.address}
                              onChange={(e) => handleRestaurantSettingChange('address', e.target.value)}
                              placeholder="Enter restaurant address"
                              rows={3}
                            />
                          </div>

                          <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label htmlFor="restaurant-phone">Phone</Label>
                              <Input
                                id="restaurant-phone"
                                value={restaurantSettings.phone}
                                onChange={(e) => handleRestaurantSettingChange('phone', e.target.value)}
                                placeholder="Phone number"
                              />
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor="restaurant-email">Email</Label>
                              <Input
                                id="restaurant-email"
                                value={restaurantSettings.email}
                                onChange={(e) => handleRestaurantSettingChange('email', e.target.value)}
                                placeholder="Email address"
                              />
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="restaurant-website">Website</Label>
                            <Input
                              id="restaurant-website"
                              value={restaurantSettings.website}
                              onChange={(e) => handleRestaurantSettingChange('website', e.target.value)}
                              placeholder="Website URL"
                            />
                          </div>
                        </div>
                      </div>

                      {/* Table Configuration */}
                      <div>
                        <h3 className="text-lg font-medium mb-4">Table Configuration</h3>
                        <div className="space-y-4">
                          <div className="space-y-2">
                            <Label htmlFor="table-count">Number of Tables</Label>
                            <div className="flex items-center gap-4">
                              <Input
                                id="table-count"
                                type="number"
                                min="1"
                                value={restaurantSettings.tableCount}
                                onChange={(e) => handleRestaurantSettingChange('tableCount', parseInt(e.target.value) || 1)}
                                className="w-24"
                              />
                              <div className="flex items-center gap-2">
                                <Table className="h-4 w-4 text-muted-foreground" />
                                <span className="text-sm text-muted-foreground">
                                  {restaurantSettings.tableCount} tables configured
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Logo and Hours */}
                    <div className="space-y-6">
                      {/* Logo Upload */}
                      <div>
                        <h3 className="text-lg font-medium mb-4">Restaurant Logo</h3>
                        <div className="space-y-4">
                          <div className="border rounded-md p-4 flex flex-col items-center justify-center">
                            {restaurantSettings.logoPreview ? (
                              <div className="space-y-4">
                                <div className="w-40 h-40 mx-auto overflow-hidden rounded-md">
                                  <img
                                    src={restaurantSettings.logoPreview}
                                    alt="Restaurant Logo"
                                    className="w-full h-full object-contain"
                                  />
                                </div>
                                <Button
                                  variant="outline"
                                  onClick={() => handleRestaurantSettingChange('logoPreview', '')}
                                  className="w-full"
                                >
                                  Remove Logo
                                </Button>
                              </div>
                            ) : (
                              <div className="w-full h-40 flex flex-col items-center justify-center border-2 border-dashed rounded-md">
                                <Image className="h-10 w-10 text-muted-foreground mb-2" />
                                <p className="text-sm text-muted-foreground mb-2">
                                  Upload your restaurant logo
                                </p>
                                <Input
                                  id="logo-upload"
                                  type="file"
                                  accept="image/*"
                                  onChange={handleLogoUpload}
                                  className="hidden"
                                />
                                <Label htmlFor="logo-upload" asChild>
                                  <Button variant="outline" className="cursor-pointer">
                                    <Upload className="h-4 w-4 mr-2" />
                                    Select Image
                                  </Button>
                                </Label>
                              </div>
                            )}
                          </div>
                          <p className="text-xs text-muted-foreground">
                            Recommended size: 400x400 pixels. Max file size: 2MB.
                          </p>
                        </div>
                      </div>

                      {/* Opening Hours */}
                      <div>
                        <h3 className="text-lg font-medium mb-4">Opening Hours</h3>
                        <div className="space-y-3">
                          {Object.entries(restaurantSettings.openingHours).map(([day, hours]) => (
                            <div key={day} className="flex items-center justify-between">
                              <div className="w-1/4">
                                <span className="capitalize">{day}</span>
                              </div>
                              <div className="flex items-center gap-2 w-3/4">
                                <Switch
                                  checked={!hours.closed}
                                  onCheckedChange={(checked) =>
                                    handleOpeningHoursChange(day, 'closed', !checked)
                                  }
                                />
                                {!hours.closed ? (
                                  <div className="flex items-center gap-2">
                                    <Input
                                      type="time"
                                      value={hours.open}
                                      onChange={(e) =>
                                        handleOpeningHoursChange(day, 'open', e.target.value)
                                      }
                                      className="w-24"
                                    />
                                    <span>to</span>
                                    <Input
                                      type="time"
                                      value={hours.close}
                                      onChange={(e) =>
                                        handleOpeningHoursChange(day, 'close', e.target.value)
                                      }
                                      className="w-24"
                                    />
                                  </div>
                                ) : (
                                  <span className="text-muted-foreground">Closed</span>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-end">
                <Button onClick={saveRestaurantSettings}>
                  <Save className="h-4 w-4 mr-2" />
                  Save Restaurant Settings
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
          <TabsContent value="management">
            <RestaurantManagement />
          </TabsContent>







          <TabsContent value="theme">
            <Card>
              <CardHeader>
                <CardTitle>Theme Settings</CardTitle>
                <CardDescription>Change the application theme</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid gap-2">
                    <Label htmlFor="theme">Theme</Label>
                    <Select
                      value={systemSettings.theme}
                      onValueChange={handleThemeChange}
                    >
                      <SelectTrigger id="theme">
                        <SelectValue placeholder="Select theme" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="light">Light</SelectItem>
                        <SelectItem value="dark">Dark</SelectItem>
                        <SelectItem value="system">System</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <p>Current theme: {theme}</p>
                  </div>

                  <Button onClick={() => toast.success("Theme settings saved!")}>
                    Save Settings
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="about">
            <Card>
              <CardHeader>
                <CardTitle>About RestroManage</CardTitle>
                <CardDescription>
                  Information about the application and its developers
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="text-center mb-6">
                    <h2 className="text-2xl font-bold">Promith</h2>
                    <p className="text-muted-foreground">Version 1.0.0</p>
                  </div>

                  <div className="space-y-2">
                    <h3 className="text-lg font-medium">About the Application</h3>
                    <p>
                      Promith is a comprehensive restaurant management system designed to streamline
                      operations for restaurants of all sizes. It provides tools for order management,
                      staff scheduling, inventory control, and reporting.
                    </p>
                  </div>

                  <Separator />

                  <div className="space-y-2">
                    <h3 className="text-lg font-medium">Features</h3>
                    <ul className="list-disc pl-5 space-y-1">
                      <li>Order management and POS system</li>
                      <li>Staff scheduling and time tracking</li>
                      <li>Inventory management</li>
                      <li>Financial reporting and analytics</li>
                      <li>Customer relationship management</li>
                      <li>Table management and reservations</li>
                    </ul>
                  </div>

                  <Separator />

                  <div className="space-y-2">
                    <h3 className="text-lg font-medium">Contact Information</h3>
                    <div className="space-y-2">
                      <div className="flex items-center">
                        <Mail className="h-4 w-4 mr-2" />
                        <span><EMAIL></span>
                      </div>
                      <div className="flex items-center">
                        <Phone className="h-4 w-4 mr-2" />
                        <span>+****************</span>
                      </div>
                      <div className="flex items-center">
                        <Github className="h-4 w-4 mr-2" />
                        <a href="https://github.com/promith" className="text-blue-600 hover:underline" target="_blank" rel="noopener noreferrer">
                          github.com/promith
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="help">
            <Card>
              <CardHeader>
                <CardTitle>Help & Support</CardTitle>
                <CardDescription>
                  Get help with using the application and troubleshooting issues
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <h3 className="text-lg font-medium">Frequently Asked Questions</h3>
                    <div className="space-y-4">
                      <div className="space-y-1">
                        <h4 className="font-medium">How do I create a new order?</h4>
                        <p className="text-sm text-muted-foreground">
                          Navigate to the Orders page and click the "New Order" button. Select a table,
                          add menu items, and submit the order.
                        </p>
                      </div>

                      <div className="space-y-1">
                        <h4 className="font-medium">How do I add a new staff member?</h4>
                        <p className="text-sm text-muted-foreground">
                          Go to the Staff page and click "Add Staff". Fill in the required information
                          and assign appropriate permissions.
                        </p>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-2">
                    <h3 className="text-lg font-medium">Getting Support</h3>
                    <p>
                      If you need additional help or encounter issues not covered in the FAQs,
                      please contact our support team:
                    </p>

                    <div className="mt-4 space-y-2">
                      <div className="flex items-center">
                        <Mail className="h-4 w-4 mr-2" />
                        <span><EMAIL></span>
                      </div>
                      <div className="flex items-center">
                        <Phone className="h-4 w-4 mr-2" />
                        <span>+****************</span>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-2">
                    <h3 className="text-lg font-medium">Documentation</h3>
                    <p>
                      For detailed instructions and guides, please refer to our documentation:
                    </p>

                    <Button variant="outline" className="mt-2">
                      <ExternalLink className="h-4 w-4 mr-2" />
                      View Documentation
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Promo Code Form Dialog */}
      <Dialog open={showPromoForm} onOpenChange={setShowPromoForm}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>
              {editingPromo ? "Edit Promo Code" : "Add New Promo Code"}
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="promo-code">Code *</Label>
                <Input
                  id="promo-code"
                  value={promoForm.code}
                  onChange={(e) => updatePromoForm('code', e.target.value.toUpperCase())}
                  placeholder="SAVE10"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="promo-name">Name *</Label>
                <Input
                  id="promo-name"
                  value={promoForm.name}
                  onChange={(e) => updatePromoForm('name', e.target.value)}
                  placeholder="Save 10%"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="promo-description">Description</Label>
              <Input
                id="promo-description"
                value={promoForm.description}
                onChange={(e) => updatePromoForm('description', e.target.value)}
                placeholder="10% off your order"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="discount-type">Discount Type</Label>
                <Select
                  value={promoForm.discount_type}
                  onValueChange={(value) => updatePromoForm('discount_type', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="percentage">Percentage</SelectItem>
                    <SelectItem value="fixed">Fixed Amount</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="discount-value">
                  Discount Value {promoForm.discount_type === "percentage" ? "(%)" : "(£)"}
                </Label>
                <Input
                  id="discount-value"
                  type="number"
                  min="0"
                  max={promoForm.discount_type === "percentage" ? systemSettings.maxDiscountPercent : undefined}
                  value={promoForm.discount_value}
                  onChange={(e) => updatePromoForm('discount_value', parseFloat(e.target.value) || 0)}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="min-order">Min Order Value (£)</Label>
                <Input
                  id="min-order"
                  type="number"
                  min="0"
                  value={promoForm.min_order_value}
                  onChange={(e) => updatePromoForm('min_order_value', parseFloat(e.target.value) || 0)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="usage-limit">Usage Limit</Label>
                <Input
                  id="usage-limit"
                  type="number"
                  min="1"
                  value={promoForm.usage_limit}
                  onChange={(e) => updatePromoForm('usage_limit', parseInt(e.target.value) || 100)}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="valid-from">Valid From</Label>
                <Input
                  id="valid-from"
                  type="date"
                  value={promoForm.valid_from}
                  onChange={(e) => updatePromoForm('valid_from', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="valid-until">Valid Until</Label>
                <Input
                  id="valid-until"
                  type="date"
                  value={promoForm.valid_until}
                  onChange={(e) => updatePromoForm('valid_until', e.target.value)}
                />
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="is-active"
                checked={promoForm.is_active}
                onCheckedChange={(checked) => updatePromoForm('is_active', checked)}
              />
              <Label htmlFor="is-active">Active</Label>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowPromoForm(false);
                setEditingPromo(null);
                setPromoForm({
                  code: "",
                  name: "",
                  description: "",
                  discount_type: "percentage",
                  discount_value: 0,
                  min_order_value: 0,
                  max_discount: 0,
                  usage_limit: 100,
                  valid_from: "",
                  valid_until: "",
                  is_active: true
                });
              }}
            >
              Cancel
            </Button>
            <Button onClick={savePromoCode}>
              {editingPromo ? "Update" : "Create"} Promo Code
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Gift Card Generation Dialog */}
      <Dialog open={showGiftCardForm} onOpenChange={setShowGiftCardForm}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Generate Gift Card</DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="gift-card-amount">Gift Card Amount (£)</Label>
              <Input
                id="gift-card-amount"
                type="number"
                min="1"
                step="0.01"
                value={giftCardAmount}
                onChange={(e) => setGiftCardAmount(parseFloat(e.target.value) || 0)}
                placeholder="Enter amount"
              />
            </div>

            <div className="p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium mb-2">Gift Card Preview</h4>
              <div className="flex items-center gap-2">
                <CreditCard className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Code will be auto-generated (e.g., GC-ABC123)</span>
              </div>
              <div className="text-lg font-bold mt-2">
                Value: £{giftCardAmount.toFixed(2)}
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowGiftCardForm(false);
                setGiftCardAmount(0);
              }}
            >
              Cancel
            </Button>
            <Button onClick={generateGiftCard}>
              Generate Gift Card
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Layout>
  );
};

export default Settings;
