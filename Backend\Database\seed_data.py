"""
Sample data seeder for RestroManage database.
Provides functions to populate the database with test data.
"""

import sys
import logging
from pathlib import Path
from datetime import datetime, timedelta, timezone
from sqlalchemy.orm import Session

# Add the parent directory to the path so we can import from Database
sys.path.append(str(Path(__file__).parent.parent))

from Database.database import get_db_session
from Database.models.restaurants import Restaurant
from Database.models.auth import User
from Database.models.menu import MenuItem, MenuCategory
from Database.models.tables import Table
from Database.models.discounts import PromoCode

logger = logging.getLogger(__name__)

def create_sample_restaurant(db: Session) -> Restaurant:
    """Create a sample restaurant for testing."""
    restaurant = Restaurant(
        name="The Golden Spoon",
        code="TGS001",
        owner_name="<PERSON>",
        business_license_number="BL123456789",
        restaurant_type="restaurant",
        email="<EMAIL>",
        phone="+44 20 1234 5678",
        address="123 High Street, London",
        city="London",
        country="United Kingdom",
        postal_code="SW1A 1AA",
        vat_rate=20.0,
        currency="GBP",
        password_hash="$2b$12$hashed_password_here",
        seating_capacity=50,
        setup_completed=True,
        subscription_plan="premium",
        subscription_status="active"
    )
    
    db.add(restaurant)
    db.commit()
    db.refresh(restaurant)
    return restaurant

def create_sample_users(db: Session, restaurant: Restaurant) -> list[User]:
    """Create sample users for testing."""
    users = [
        User(
            username="admin",
            email="<EMAIL>",
            full_name="Admin User",
            password_hash="$2b$12$hashed_password_here",
            role="admin",
            restaurant_id=restaurant.id,
            phone="+44 20 1234 5679",
            position="Manager"
        ),
        User(
            username="waiter1",
            email="<EMAIL>",
            full_name="Alice Johnson",
            password_hash="$2b$12$hashed_password_here",
            role="waiter",
            restaurant_id=restaurant.id,
            phone="+44 20 1234 5680",
            position="Senior Waiter",
            pin="1234"
        ),
        User(
            username="chef1",
            email="<EMAIL>",
            full_name="Bob Wilson",
            password_hash="$2b$12$hashed_password_here",
            role="chef",
            restaurant_id=restaurant.id,
            phone="+44 20 1234 5681",
            position="Head Chef",
            pin="5678"
        )
    ]
    
    for user in users:
        db.add(user)
    
    db.commit()
    
    for user in users:
        db.refresh(user)
    
    return users

def create_sample_menu_categories(db: Session, restaurant: Restaurant) -> list[MenuCategory]:
    """Create sample menu categories."""
    categories = [
        MenuCategory(
            name="Starters",
            description="Delicious appetizers to start your meal",
            restaurant_id=restaurant.id,
            display_order=1,
            color="#FF6B6B"
        ),
        MenuCategory(
            name="Main Courses",
            description="Hearty main dishes",
            restaurant_id=restaurant.id,
            display_order=2,
            color="#4ECDC4"
        ),
        MenuCategory(
            name="Desserts",
            description="Sweet treats to end your meal",
            restaurant_id=restaurant.id,
            display_order=3,
            color="#45B7D1"
        ),
        MenuCategory(
            name="Beverages",
            description="Refreshing drinks",
            restaurant_id=restaurant.id,
            display_order=4,
            color="#96CEB4"
        )
    ]
    
    for category in categories:
        db.add(category)
    
    db.commit()
    
    for category in categories:
        db.refresh(category)
    
    return categories

def create_sample_menu_items(db: Session, restaurant: Restaurant, categories: list[MenuCategory]) -> list[MenuItem]:
    """Create sample menu items."""
    items = [
        # Starters
        MenuItem(
            name="Caesar Salad",
            description="Fresh romaine lettuce with parmesan cheese and croutons",
            price=8.50,
            restaurant_id=restaurant.id,
            category_id=categories[0].id,
            category_name=categories[0].name,
            allergens=["dairy", "gluten"],
            is_vegetarian=True,
            prep_time=10
        ),
        MenuItem(
            name="Garlic Bread",
            description="Toasted bread with garlic butter and herbs",
            price=5.50,
            restaurant_id=restaurant.id,
            category_id=categories[0].id,
            category_name=categories[0].name,
            allergens=["gluten", "dairy"],
            is_vegetarian=True,
            prep_time=5
        ),
        
        # Main Courses
        MenuItem(
            name="Grilled Salmon",
            description="Fresh Atlantic salmon with lemon and herbs",
            price=18.50,
            restaurant_id=restaurant.id,
            category_id=categories[1].id,
            category_name=categories[1].name,
            allergens=["fish"],
            prep_time=20,
            is_gluten_free=True
        ),
        MenuItem(
            name="Beef Burger",
            description="Juicy beef patty with lettuce, tomato, and fries",
            price=14.50,
            restaurant_id=restaurant.id,
            category_id=categories[1].id,
            category_name=categories[1].name,
            allergens=["gluten", "dairy"],
            prep_time=15
        ),
        
        # Desserts
        MenuItem(
            name="Chocolate Cake",
            description="Rich chocolate cake with vanilla ice cream",
            price=7.50,
            restaurant_id=restaurant.id,
            category_id=categories[2].id,
            category_name=categories[2].name,
            allergens=["dairy", "eggs", "gluten"],
            is_vegetarian=True,
            prep_time=5
        ),
        
        # Beverages
        MenuItem(
            name="Coffee",
            description="Freshly brewed coffee",
            price=3.50,
            restaurant_id=restaurant.id,
            category_id=categories[3].id,
            category_name=categories[3].name,
            allergens=[],
            is_vegan=True,
            prep_time=3
        )
    ]
    
    for item in items:
        db.add(item)
    
    db.commit()
    
    for item in items:
        db.refresh(item)
    
    return items

def create_sample_tables(db: Session, restaurant: Restaurant) -> list[Table]:
    """Create sample tables."""
    tables = []
    
    for i in range(1, 11):  # Create 10 tables
        table = Table(
            number=i,
            capacity=4 if i <= 6 else 6,  # Smaller tables for 1-6, larger for 7-10
            restaurant_id=restaurant.id,
            location="indoor",
            section="Main Dining" if i <= 8 else "VIP Section"
        )
        tables.append(table)
        db.add(table)
    
    db.commit()
    
    for table in tables:
        db.refresh(table)
    
    return tables

def create_sample_promo_codes(db: Session, restaurant: Restaurant) -> list[PromoCode]:
    """Create sample promo codes."""
    promo_codes = [
        PromoCode(
            code="WELCOME10",
            name="Welcome Discount",
            description="10% off for new customers",
            discount_type="percentage",
            discount_value=10.0,
            restaurant_id=restaurant.id,
            start_date=datetime.now(timezone.utc) - timedelta(days=30),
            end_date=datetime.now(timezone.utc) + timedelta(days=30),
            usage_limit=100,
            minimum_spend=20.0
        ),
        PromoCode(
            code="SAVE5",
            name="Save £5",
            description="£5 off orders over £25",
            discount_type="fixed_amount",
            discount_value=5.0,
            restaurant_id=restaurant.id,
            start_date=datetime.now(timezone.utc) - timedelta(days=7),
            end_date=datetime.now(timezone.utc) + timedelta(days=60),
            usage_limit=50,
            minimum_spend=25.0
        )
    ]
    
    for promo_code in promo_codes:
        db.add(promo_code)
    
    db.commit()
    
    for promo_code in promo_codes:
        db.refresh(promo_code)
    
    return promo_codes

def seed_sample_data():
    """
    Seed the database with sample data for testing.
    """
    try:
        logger.info("🌱 Seeding database with sample data...")
        
        db = get_db_session()
        
        # Create sample restaurant
        restaurant = create_sample_restaurant(db)
        logger.info(f"✅ Created restaurant: {restaurant.name}")
        
        # Create sample users
        users = create_sample_users(db, restaurant)
        logger.info(f"✅ Created {len(users)} users")
        
        # Create menu categories
        categories = create_sample_menu_categories(db, restaurant)
        logger.info(f"✅ Created {len(categories)} menu categories")
        
        # Create menu items
        items = create_sample_menu_items(db, restaurant, categories)
        logger.info(f"✅ Created {len(items)} menu items")
        
        # Create tables
        tables = create_sample_tables(db, restaurant)
        logger.info(f"✅ Created {len(tables)} tables")
        
        # Create promo codes
        promo_codes = create_sample_promo_codes(db, restaurant)
        logger.info(f"✅ Created {len(promo_codes)} promo codes")
        
        db.close()
        
        logger.info("🎉 Sample data seeding completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error seeding sample data: {e}")
        return False

if __name__ == "__main__":
    seed_sample_data()
