import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Search,
  Plus,
  Eye,
  Pencil,
  Trash2,
  Users,
  Calendar
} from "lucide-react";
import StaffAvailabilityCalendar from "@/components/analytics/scheduling/availability/StaffAvailabilityCalendar";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "@/components/ui/sonner";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import Layout from "@/components/layout/Layout";

// Staff member interface
interface StaffMember {
  id: string;
  name: string;
  role: string;
  position: string;
  email: string;
  phone: string;
  pin?: string;
  status: "active" | "inactive" | "on-leave";
  hireDate: string;
  performance: number;
  metrics?: {
    sales: number;
    tablesTurned: number;
    customerRating: number;
  };
  avatar?: string;
  workStatus?: "clocked-in" | "clocked-out" | "on-break";
  assignedHours?: number;
  availableDays?: string[];
}

const Staff = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState("");
  const [staffData, setStaffData] = useState<StaffMember[]>([
    {
      id: "1",
      name: "Michael Rodriguez",
      role: "waiter",
      position: "Head Waiter",
      email: "<EMAIL>",
      phone: "(*************",
      status: "active",
      hireDate: "2022-03-15",
      performance: 92,
      metrics: {
        sales: 1250,
        tablesTurned: 48,
        customerRating: 4.8
      },
      assignedHours: 35,
      availableDays: ["mon", "tue", "wed", "thu", "fri"]
    },
    {
      id: "2",
      name: "Jennifer Smith",
      role: "waiter",
      position: "Waiter",
      email: "<EMAIL>",
      phone: "(*************",
      status: "active",
      hireDate: "2022-05-20",
      performance: 78,
      metrics: {
        sales: 980,
        tablesTurned: 38,
        customerRating: 4.5
      },
      assignedHours: 25,
      availableDays: ["mon", "wed", "fri", "sat"]
    },
    {
      id: "3",
      name: "David Chen",
      role: "waiter",
      position: "Waiter",
      email: "<EMAIL>",
      phone: "(*************",
      status: "on-leave",
      hireDate: "2022-08-10",
      performance: 65,
      metrics: {
        sales: 750,
        tablesTurned: 30,
        customerRating: 3.9
      },
      assignedHours: 30,
      availableDays: ["thu", "fri", "sat", "sun"]
    },
    {
      id: "4",
      name: "Maria Lopez",
      role: "chef",
      position: "Head Chef",
      email: "<EMAIL>",
      phone: "(*************",
      status: "active",
      hireDate: "2021-11-05",
      performance: 88,
      assignedHours: 40,
      availableDays: ["wed", "thu", "fri", "sat", "sun"]
    },
    {
      id: "5",
      name: "Robert Johnson",
      role: "manager",
      position: "Floor Manager",
      email: "<EMAIL>",
      phone: "(*************",
      status: "active",
      hireDate: "2021-06-15",
      performance: 95,
      assignedHours: 40,
      availableDays: ["mon", "tue", "wed", "thu", "fri"]
    }
  ]);

  // Filter staff based on search query
  const filteredStaff = staffData.filter(staff =>
    staff.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    staff.position.toLowerCase().includes(searchQuery.toLowerCase()) ||
    staff.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Dialog states
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentStaff, setCurrentStaff] = useState<StaffMember | null>(null);
  const [newStaff, setNewStaff] = useState<Partial<StaffMember>>({
    name: "",
    role: "waiter",
    position: "",
    email: "",
    phone: "",
    pin: "",
    status: "active",
    hireDate: new Date().toISOString().split('T')[0],
    performance: 75,
    workStatus: "clocked-out",
    assignedHours: 35,
    availableDays: ["mon", "tue", "wed", "thu", "fri"]
  });

  // Handle adding new staff
  const handleAddStaff = () => {
    const id = (staffData.length + 1).toString();
    const staffToAdd = {
      ...newStaff,
      id
    } as StaffMember;

    setStaffData([...staffData, staffToAdd]);
    setIsAddDialogOpen(false);
    setNewStaff({
      name: "",
      role: "waiter",
      position: "",
      email: "",
      phone: "",
      pin: "",
      status: "active",
      hireDate: new Date().toISOString().split('T')[0],
      performance: 75,
      workStatus: "clocked-out",
      assignedHours: 35,
      availableDays: ["mon", "tue", "wed", "thu", "fri"]
    });
    toast.success("Staff member added successfully");
  };

  // Handle editing staff
  const handleEditStaff = () => {
    if (!currentStaff) return;

    const updatedStaffData = staffData.map(staff =>
      staff.id === currentStaff.id ? currentStaff : staff
    );

    setStaffData(updatedStaffData);
    setIsEditDialogOpen(false);
    setCurrentStaff(null);
    toast.success("Staff member updated successfully");
  };

  // Handle deleting staff
  const handleDeleteStaff = () => {
    if (!currentStaff) return;

    const updatedStaffData = staffData.filter(staff => staff.id !== currentStaff.id);
    setStaffData(updatedStaffData);
    setIsDeleteDialogOpen(false);
    setCurrentStaff(null);
    toast.success("Staff member deleted successfully");
  };

  // Get initials for avatar
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800";
      case "inactive":
        return "bg-gray-100 text-gray-800";
      case "on-leave":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <Layout title="Staff Management" requiredRoles={["admin"]}>
      <Tabs defaultValue="staff-list" className="space-y-4">
        <TabsList>
          <TabsTrigger value="staff-list">
            <Users className="h-4 w-4 mr-2" />
            Staff List
          </TabsTrigger>
          <TabsTrigger value="scheduling">
            <Calendar className="h-4 w-4 mr-2" />
            Scheduling
          </TabsTrigger>
        </TabsList>

        <TabsContent value="staff-list" className="space-y-4">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="relative w-full sm:w-72">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search staff..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Button onClick={() => setIsAddDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" /> Add Staff
            </Button>
          </div>

          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Position</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Hire Date</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredStaff.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                        No staff members found
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredStaff.map((staff) => (
                      <TableRow key={staff.id}>
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <Avatar className="h-8 w-8">
                              {staff.avatar && <img src={staff.avatar} alt={staff.name} />}
                              <AvatarFallback>{getInitials(staff.name)}</AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium">{staff.name}</div>
                              <div className="text-xs text-muted-foreground capitalize">{staff.role}</div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{staff.position}</TableCell>
                        <TableCell>{staff.email}</TableCell>
                        <TableCell>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium capitalize ${getStatusColor(staff.status)}`}>
                            {staff.status.replace('-', ' ')}
                          </span>
                        </TableCell>
                        <TableCell>{new Date(staff.hireDate).toLocaleDateString()}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => navigate(`/admin/staff/${staff.id}`)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => {
                                setCurrentStaff(staff);
                                setIsEditDialogOpen(true);
                              }}
                            >
                              <Pencil className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => {
                                setCurrentStaff(staff);
                                setIsDeleteDialogOpen(true);
                              }}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="scheduling" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Staff Scheduling & Availability</CardTitle>
              <CardDescription>
                Manage staff schedules, availability, and time-off requests
              </CardDescription>
            </CardHeader>
            <CardContent>
              <StaffAvailabilityCalendar staffData={staffData} forecastData={[]} />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Add Staff Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[500px] max-h-[90vh] flex flex-col">
          <DialogHeader>
            <DialogTitle>Add New Staff Member</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4 overflow-y-auto flex-1 pr-2">
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="name">Full Name</Label>
                <Input
                  id="name"
                  value={newStaff.name}
                  onChange={(e) => setNewStaff({ ...newStaff, name: e.target.value })}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="role">Role</Label>
                <Select
                  value={newStaff.role}
                  onValueChange={(value) => setNewStaff({ ...newStaff, role: value })}
                >
                  <SelectTrigger id="role">
                    <SelectValue placeholder="Select role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="waiter">Waiter</SelectItem>
                    <SelectItem value="chef">Chef</SelectItem>
                    <SelectItem value="hostess">Hostess</SelectItem>
                    <SelectItem value="bartender">Bartender</SelectItem>
                    <SelectItem value="cleaner">Cleaner</SelectItem>
                    <SelectItem value="manager">Manager</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="position">Position</Label>
              <Input
                id="position"
                value={newStaff.position}
                onChange={(e) => setNewStaff({ ...newStaff, position: e.target.value })}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={newStaff.email}
                  onChange={(e) => setNewStaff({ ...newStaff, email: e.target.value })}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="phone">Phone</Label>
                <Input
                  id="phone"
                  value={newStaff.phone}
                  onChange={(e) => setNewStaff({ ...newStaff, phone: e.target.value })}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="status">Status</Label>
                <Select
                  value={newStaff.status}
                  onValueChange={(value: "active" | "inactive" | "on-leave") => setNewStaff({ ...newStaff, status: value })}
                >
                  <SelectTrigger id="status">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                    <SelectItem value="on-leave">On Leave</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="hireDate">Hire Date</Label>
                <Input
                  id="hireDate"
                  type="date"
                  value={newStaff.hireDate}
                  onChange={(e) => setNewStaff({ ...newStaff, hireDate: e.target.value })}
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleAddStaff}>Add Staff</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Staff Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit Staff Member</DialogTitle>
          </DialogHeader>
          {currentStaff && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="edit-name">Full Name</Label>
                  <Input
                    id="edit-name"
                    value={currentStaff.name}
                    onChange={(e) => setCurrentStaff({ ...currentStaff, name: e.target.value })}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="edit-role">Role</Label>
                  <Select
                    value={currentStaff.role}
                    onValueChange={(value) => setCurrentStaff({ ...currentStaff, role: value })}
                  >
                    <SelectTrigger id="edit-role">
                      <SelectValue placeholder="Select role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="waiter">Waiter</SelectItem>
                      <SelectItem value="chef">Chef</SelectItem>
                      <SelectItem value="hostess">Hostess</SelectItem>
                      <SelectItem value="bartender">Bartender</SelectItem>
                      <SelectItem value="cleaner">Cleaner</SelectItem>
                      <SelectItem value="manager">Manager</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-position">Position</Label>
                <Input
                  id="edit-position"
                  value={currentStaff.position}
                  onChange={(e) => setCurrentStaff({ ...currentStaff, position: e.target.value })}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="edit-email">Email</Label>
                  <Input
                    id="edit-email"
                    type="email"
                    value={currentStaff.email}
                    onChange={(e) => setCurrentStaff({ ...currentStaff, email: e.target.value })}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="edit-phone">Phone</Label>
                  <Input
                    id="edit-phone"
                    value={currentStaff.phone}
                    onChange={(e) => setCurrentStaff({ ...currentStaff, phone: e.target.value })}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="edit-status">Status</Label>
                  <Select
                    value={currentStaff.status}
                    onValueChange={(value: "active" | "inactive" | "on-leave") => setCurrentStaff({ ...currentStaff, status: value })}
                  >
                    <SelectTrigger id="edit-status">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="inactive">Inactive</SelectItem>
                      <SelectItem value="on-leave">On Leave</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="edit-hireDate">Hire Date</Label>
                  <Input
                    id="edit-hireDate"
                    type="date"
                    value={currentStaff.hireDate}
                    onChange={(e) => setCurrentStaff({ ...currentStaff, hireDate: e.target.value })}
                  />
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleEditStaff}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>Are you sure you want to delete {currentStaff?.name}? This action cannot be undone.</p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>Cancel</Button>
            <Button variant="destructive" onClick={handleDeleteStaff}>Delete</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Layout>
  );
};

export default Staff;