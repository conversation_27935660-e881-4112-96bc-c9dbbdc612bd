import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Plus, 
  Minus, 
  Users, 
  Grid, 
  Calculator,
  Info,
  Trash2,
  Edit
} from "lucide-react";
import { 
  TableConfiguration as TableConfig, 
  TABLE_CAPACITIES, 
  TABLE_TYPES, 
  TABLE_SHAPES 
} from "@/types/restaurantSetup";

interface TableConfigurationProps {
  onTablesChange: (tables: TableConfig[], totalCapacity: number) => void;
  initialTables?: TableConfig[];
  className?: string;
}

const TableConfiguration: React.FC<TableConfigurationProps> = ({
  onTablesChange,
  initialTables = [],
  className = "",
}) => {
  const [tables, setTables] = useState<TableConfig[]>(initialTables);
  const [totalTables, setTotalTables] = useState(initialTables.length || 10);
  const [defaultCapacity, setDefaultCapacity] = useState<2 | 4 | 6 | 8>(4);
  const [namingSystem, setNamingSystem] = useState<'numbers' | 'letters' | 'custom'>('numbers');
  const [editingTable, setEditingTable] = useState<string | null>(null);

  // Calculate total seating capacity
  const totalCapacity = tables.reduce((sum, table) => sum + table.capacity, 0);

  // Generate tables based on total number and default settings
  const generateTables = () => {
    const newTables: TableConfig[] = [];
    
    for (let i = 0; i < totalTables; i++) {
      const tableNumber = namingSystem === 'numbers' 
        ? (i + 1).toString()
        : namingSystem === 'letters'
        ? String.fromCharCode(65 + i) // A, B, C...
        : `T${i + 1}`;

      newTables.push({
        id: `table-${i + 1}`,
        number: tableNumber,
        capacity: defaultCapacity,
        type: 'indoor',
        shape: 'round',
        isActive: true,
      });
    }
    
    setTables(newTables);
  };

  // Update a specific table
  const updateTable = (tableId: string, updates: Partial<TableConfig>) => {
    setTables(prev => prev.map(table => 
      table.id === tableId ? { ...table, ...updates } : table
    ));
  };

  // Add a new table
  const addTable = () => {
    const newTable: TableConfig = {
      id: `table-${Date.now()}`,
      number: tables.length + 1,
      capacity: defaultCapacity,
      type: 'indoor',
      shape: 'round',
      isActive: true,
    };
    setTables(prev => [...prev, newTable]);
    setTotalTables(prev => prev + 1);
  };

  // Remove a table
  const removeTable = (tableId: string) => {
    setTables(prev => prev.filter(table => table.id !== tableId));
    setTotalTables(prev => prev - 1);
  };

  // Quick setup presets
  const applyPreset = (preset: 'small' | 'medium' | 'large') => {
    let tableCount = 0;
    let capacity = 4;
    
    switch (preset) {
      case 'small':
        tableCount = 8;
        capacity = 4;
        break;
      case 'medium':
        tableCount = 15;
        capacity = 4;
        break;
      case 'large':
        tableCount = 25;
        capacity = 6;
        break;
    }
    
    setTotalTables(tableCount);
    setDefaultCapacity(capacity as 2 | 4 | 6 | 8);
  };

  // Effect to notify parent of changes
  useEffect(() => {
    onTablesChange(tables, totalCapacity);
  }, [tables, totalCapacity, onTablesChange]);

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Grid className="h-5 w-5" />
          Table Configuration
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="quick-setup" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="quick-setup">Quick Setup</TabsTrigger>
            <TabsTrigger value="detailed">Detailed Config</TabsTrigger>
            <TabsTrigger value="summary">Summary</TabsTrigger>
          </TabsList>

          {/* Quick Setup Tab */}
          <TabsContent value="quick-setup" className="space-y-6">
            {/* Preset Options */}
            <div className="space-y-4">
              <Label className="text-base font-medium">Choose a preset to get started:</Label>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center space-y-2"
                  onClick={() => applyPreset('small')}
                >
                  <div className="text-lg font-semibold">Small Restaurant</div>
                  <div className="text-sm text-gray-500">8 tables • 32 seats</div>
                  <Badge variant="secondary">Café/Bistro</Badge>
                </Button>
                
                <Button
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center space-y-2"
                  onClick={() => applyPreset('medium')}
                >
                  <div className="text-lg font-semibold">Medium Restaurant</div>
                  <div className="text-sm text-gray-500">15 tables • 60 seats</div>
                  <Badge variant="secondary">Family Restaurant</Badge>
                </Button>
                
                <Button
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center space-y-2"
                  onClick={() => applyPreset('large')}
                >
                  <div className="text-lg font-semibold">Large Restaurant</div>
                  <div className="text-sm text-gray-500">25 tables • 150 seats</div>
                  <Badge variant="secondary">Full Service</Badge>
                </Button>
              </div>
            </div>

            {/* Basic Configuration */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="total-tables">Total Number of Tables</Label>
                  <div className="flex items-center space-x-2 mt-1">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setTotalTables(Math.max(1, totalTables - 1))}
                      disabled={totalTables <= 1}
                    >
                      <Minus className="h-4 w-4" />
                    </Button>
                    <Input
                      id="total-tables"
                      type="number"
                      min="1"
                      max="200"
                      value={totalTables}
                      onChange={(e) => setTotalTables(Math.max(1, parseInt(e.target.value) || 1))}
                      className="text-center"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setTotalTables(Math.min(200, totalTables + 1))}
                      disabled={totalTables >= 200}
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div>
                  <Label htmlFor="default-capacity">Default Table Capacity</Label>
                  <Select value={defaultCapacity.toString()} onValueChange={(value) => setDefaultCapacity(parseInt(value) as 2 | 4 | 6 | 8)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {TABLE_CAPACITIES.slice(0, 4).map((capacity) => (
                        <SelectItem key={capacity} value={capacity.toString()}>
                          {capacity} seats
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <Label htmlFor="naming-system">Table Naming System</Label>
                  <Select value={namingSystem} onValueChange={(value: 'numbers' | 'letters' | 'custom') => setNamingSystem(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="numbers">Numbers (1, 2, 3...)</SelectItem>
                      <SelectItem value="letters">Letters (A, B, C...)</SelectItem>
                      <SelectItem value="custom">Custom (T1, T2, T3...)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Button
                  type="button"
                  onClick={generateTables}
                  className="w-full"
                >
                  Generate Tables
                </Button>
              </div>
            </div>
          </TabsContent>

          {/* Detailed Configuration Tab */}
          <TabsContent value="detailed" className="space-y-6">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium">Individual Table Settings</h3>
              <Button onClick={addTable} size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Table
              </Button>
            </div>

            <div className="space-y-4 max-h-96 overflow-y-auto">
              {tables.map((table) => (
                <Card key={table.id} className="p-4">
                  <div className="grid grid-cols-1 md:grid-cols-5 gap-4 items-center">
                    <div>
                      <Label className="text-sm">Table Number</Label>
                      <Input
                        value={table.number}
                        onChange={(e) => updateTable(table.id, { number: e.target.value })}
                        className="mt-1"
                      />
                    </div>

                    <div>
                      <Label className="text-sm">Capacity</Label>
                      <Select 
                        value={table.capacity.toString()} 
                        onValueChange={(value) => updateTable(table.id, { capacity: parseInt(value) as any })}
                      >
                        <SelectTrigger className="mt-1">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {TABLE_CAPACITIES.map((capacity) => (
                            <SelectItem key={capacity} value={capacity.toString()}>
                              {capacity}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label className="text-sm">Type</Label>
                      <Select 
                        value={table.type} 
                        onValueChange={(value: any) => updateTable(table.id, { type: value })}
                      >
                        <SelectTrigger className="mt-1">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {TABLE_TYPES.map((type) => (
                            <SelectItem key={type} value={type}>
                              {type.charAt(0).toUpperCase() + type.slice(1)}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label className="text-sm">Shape</Label>
                      <Select 
                        value={table.shape} 
                        onValueChange={(value: any) => updateTable(table.id, { shape: value })}
                      >
                        <SelectTrigger className="mt-1">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {TABLE_SHAPES.map((shape) => (
                            <SelectItem key={shape} value={shape}>
                              {shape.charAt(0).toUpperCase() + shape.slice(1)}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex justify-end">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => removeTable(table.id)}
                        disabled={tables.length <= 1}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Summary Tab */}
          <TabsContent value="summary" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card className="p-4">
                <div className="flex items-center space-x-2">
                  <Grid className="h-5 w-5 text-blue-500" />
                  <div>
                    <div className="text-2xl font-bold">{tables.length}</div>
                    <div className="text-sm text-gray-500">Total Tables</div>
                  </div>
                </div>
              </Card>

              <Card className="p-4">
                <div className="flex items-center space-x-2">
                  <Users className="h-5 w-5 text-green-500" />
                  <div>
                    <div className="text-2xl font-bold">{totalCapacity}</div>
                    <div className="text-sm text-gray-500">Total Seats</div>
                  </div>
                </div>
              </Card>

              <Card className="p-4">
                <div className="flex items-center space-x-2">
                  <Calculator className="h-5 w-5 text-purple-500" />
                  <div>
                    <div className="text-2xl font-bold">{Math.round(totalCapacity / tables.length)}</div>
                    <div className="text-sm text-gray-500">Avg. Table Size</div>
                  </div>
                </div>
              </Card>
            </div>

            {/* Table Distribution */}
            <Card className="p-4">
              <h4 className="font-medium mb-4">Table Distribution</h4>
              <div className="space-y-2">
                {TABLE_CAPACITIES.map((capacity) => {
                  const count = tables.filter(t => t.capacity === capacity).length;
                  if (count === 0) return null;
                  
                  return (
                    <div key={capacity} className="flex justify-between items-center">
                      <span>{capacity}-seat tables</span>
                      <Badge variant="secondary">{count} tables</Badge>
                    </div>
                  );
                })}
              </div>
            </Card>

            {/* Recommendations */}
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                <strong>Recommendations:</strong>
                <ul className="mt-2 space-y-1 text-sm">
                  <li>• Mix of 2-seat and 4-seat tables works well for most restaurants</li>
                  <li>• Consider 20% of tables as 2-seaters for couples</li>
                  <li>• Outdoor tables can increase capacity during good weather</li>
                  <li>• Bar seating is great for solo diners and quick service</li>
                </ul>
              </AlertDescription>
            </Alert>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default TableConfiguration;
