import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { LogLevel } from '@/utils/logger';
import logger from '@/utils/logger';

interface Log {
  timestamp: string;
  level: LogLevel;
  message: string;
  data?: any;
}

const LogViewer = () => {
  const [logs, setLogs] = useState<Log[]>([]);
  const [filter, setFilter] = useState<string>('');
  const [levelFilter, setLevelFilter] = useState<string>('all');
  const [autoRefresh, setAutoRefresh] = useState<boolean>(true);

  // Load logs
  const loadLogs = () => {
    const allLogs = logger.getLogs();
    setLogs(allLogs);
  };

  // Initial load and auto-refresh
  useEffect(() => {
    loadLogs();
    
    let interval: NodeJS.Timeout | null = null;
    
    if (autoRefresh) {
      interval = setInterval(loadLogs, 2000);
    }
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [autoRefresh]);

  // Clear logs
  const handleClearLogs = () => {
    logger.clearLogs();
    loadLogs();
  };

  // Get badge color based on log level
  const getBadgeVariant = (level: LogLevel): "default" | "destructive" | "outline" | "secondary" => {
    switch (level) {
      case LogLevel.DEBUG:
        return 'secondary';
      case LogLevel.INFO:
        return 'default';
      case LogLevel.WARN:
        return 'outline'; // Use outline instead of warning
      case LogLevel.ERROR:
        return 'destructive';
      default:
        return 'outline';
    }
  };

  // Filter logs
  const filteredLogs = logs.filter(log => {
    // Filter by level
    if (levelFilter !== 'all' && log.level !== levelFilter) {
      return false;
    }
    
    // Filter by text
    if (filter && !log.message.toLowerCase().includes(filter.toLowerCase())) {
      const dataString = log.data ? JSON.stringify(log.data) : '';
      if (!dataString.toLowerCase().includes(filter.toLowerCase())) {
        return false;
      }
    }
    
    return true;
  });

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Application Logs</CardTitle>
        <CardDescription>View and filter application logs</CardDescription>
        
        <div className="flex flex-col sm:flex-row gap-2 mt-4">
          <Input
            placeholder="Filter logs..."
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            className="flex-1"
          />
          
          <Select value={levelFilter} onValueChange={setLevelFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by level" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Levels</SelectItem>
              <SelectItem value={LogLevel.DEBUG}>Debug</SelectItem>
              <SelectItem value={LogLevel.INFO}>Info</SelectItem>
              <SelectItem value={LogLevel.WARN}>Warning</SelectItem>
              <SelectItem value={LogLevel.ERROR}>Error</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" onClick={loadLogs}>
            Refresh
          </Button>
          
          <Button variant="outline" onClick={() => setAutoRefresh(!autoRefresh)}>
            {autoRefresh ? 'Disable Auto-refresh' : 'Enable Auto-refresh'}
          </Button>
          
          <Button variant="destructive" onClick={handleClearLogs}>
            Clear Logs
          </Button>
        </div>
      </CardHeader>
      
      <CardContent>
        {filteredLogs.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No logs found
          </div>
        ) : (
          <div className="space-y-2 max-h-[500px] overflow-y-auto">
            {filteredLogs.map((log, index) => (
              <div key={index} className="border rounded-md p-3">
                <div className="flex justify-between items-start">
                  <div>
                    <Badge variant={getBadgeVariant(log.level)}>
                      {log.level.toUpperCase()}
                    </Badge>
                    <span className="ml-2 text-sm text-muted-foreground">
                      {new Date(log.timestamp).toLocaleString()}
                    </span>
                  </div>
                </div>
                
                <div className="mt-2 font-medium">{log.message}</div>
                
                {log.data && (
                  <pre className="mt-2 bg-gray-100 p-2 rounded text-xs overflow-auto">
                    {JSON.stringify(log.data, null, 2)}
                  </pre>
                )}
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default LogViewer;
