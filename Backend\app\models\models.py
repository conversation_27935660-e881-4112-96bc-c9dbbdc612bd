from pydantic import BaseModel, Field, EmailStr
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum

# User and Authentication Models
class UserRole(str, Enum):
    ADMIN = "admin"
    MANAGER = "manager"
    STAFF = "staff"
    WAITER = "waiter"
    CHEF = "chef"
    CASHIER = "cashier"

class UserBase(BaseModel):
    username: str
    email: EmailStr
    full_name: str
    role: UserRole = UserRole.STAFF

class UserCreate(UserBase):
    password: str

class UserLogin(BaseModel):
    username: str
    password: str

class User(UserBase):
    id: str
    is_active: bool = True
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)

class Token(BaseModel):
    access_token: str
    token_type: str = "bearer"
    expires_at: int
    user: User

# Menu Models
class MenuCategory(str, Enum):
    APPETIZER = "appetizer"
    MAIN_COURSE = "main_course"
    DESSERT = "dessert"
    BEVERAGE = "beverage"
    SPECIAL = "special"
    SIDE = "side"

class MenuItemBase(BaseModel):
    name: str
    price: float
    category: MenuCategory
    description: Optional[str] = None
    image_url: Optional[str] = None
    available: bool = True
    ingredients: Optional[List[str]] = None

class MenuItemCreate(MenuItemBase):
    pass

class MenuItem(MenuItemBase):
    id: str
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)

# Order Models
class OrderStatus(str, Enum):
    PENDING = "pending"
    PREPARING = "preparing"
    READY = "ready"
    DELIVERED = "delivered"
    COMPLETED = "completed"
    CANCELLED = "cancelled"

class OrderItemBase(BaseModel):
    menu_item_id: str
    quantity: int = 1
    special_instructions: Optional[str] = None
    price: float

class OrderItem(OrderItemBase):
    id: str
    name: str
    subtotal: float

class OrderBase(BaseModel):
    table_id: str
    items: List[OrderItemBase]
    customer_name: Optional[str] = None
    special_instructions: Optional[str] = None

class OrderCreate(OrderBase):
    pass

class Order(OrderBase):
    id: str
    items: List[OrderItem]
    status: OrderStatus = OrderStatus.PENDING
    total: float
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    completed_at: Optional[datetime] = None

# Staff Models
class StaffPosition(str, Enum):
    FRONT_OF_HOUSE = "front_of_house"
    BACK_OF_HOUSE = "back_of_house"
    MANAGEMENT = "management"

class StaffStatus(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    ON_LEAVE = "on_leave"

class StaffBase(BaseModel):
    name: str
    role: str
    position: StaffPosition
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    hourly_rate: Optional[float] = None

class StaffCreate(StaffBase):
    pass

class Staff(StaffBase):
    id: str
    status: StaffStatus = StaffStatus.ACTIVE
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)

# Table Models
class TableStatus(str, Enum):
    AVAILABLE = "available"
    OCCUPIED = "occupied"
    RESERVED = "reserved"
    CLEANING = "cleaning"

class TableBase(BaseModel):
    number: int
    capacity: int
    location: Optional[str] = None

class TableCreate(TableBase):
    pass

class Table(TableBase):
    id: str
    status: TableStatus = TableStatus.AVAILABLE
    current_order_id: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)

# Inventory Models
class InventoryItemBase(BaseModel):
    name: str
    quantity: float
    unit: str
    reorder_level: float
    price_per_unit: float
    category: Optional[str] = None
    supplier: Optional[str] = None

class InventoryItemCreate(InventoryItemBase):
    pass

class InventoryItem(InventoryItemBase):
    id: str
    last_restocked: Optional[datetime] = None
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)

# Analytics Models
class SalesData(BaseModel):
    date: str
    sales: float
    orders: int

class RevenueData(BaseModel):
    period: str
    revenue: float

class ForecastData(BaseModel):
    day: str
    customers: int
    projectedRevenue: float

class AnalyticsResponse(BaseModel):
    daily_sales: List[SalesData]
    weekly_revenue: List[RevenueData]
    monthly_revenue: List[RevenueData]
    forecast: List[ForecastData]
