/* UI/UX Improvements for RestroManage EPOS System */

/* Dialog and Modal Improvements */
.dialog-content-improved {
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  padding: 1.5rem;
  border-radius: 0.75rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.dialog-header-improved {
  padding-bottom: 1rem;
  border-bottom: 1px solid hsl(var(--border));
  margin-bottom: 1.5rem;
}

.dialog-title-improved {
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.5;
  color: hsl(var(--foreground));
  margin: 0;
}

.dialog-footer-improved {
  padding-top: 1rem;
  border-top: 1px solid hsl(var(--border));
  margin-top: 1.5rem;
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
}

/* Text Overflow and Truncation Fixes */
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-wrap-balance {
  text-wrap: balance;
}

.text-break-words {
  word-break: break-words;
  overflow-wrap: break-word;
}

/* Container Improvements */
.container-responsive {
  width: 100%;
  max-width: 100%;
  padding-left: 1rem;
  padding-right: 1rem;
  margin-left: auto;
  margin-right: auto;
}

@media (min-width: 640px) {
  .container-responsive {
    max-width: 640px;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 768px) {
  .container-responsive {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container-responsive {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container-responsive {
    max-width: 1280px;
  }
}

/* Card Improvements */
.card-improved {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease-in-out;
}

.card-improved:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px);
}

.card-header-improved {
  padding: 1.5rem;
  padding-bottom: 1rem;
}

.card-content-improved {
  padding: 0 1.5rem 1.5rem 1.5rem;
}

.card-title-improved {
  font-size: 1.125rem;
  font-weight: 600;
  line-height: 1.5;
  margin: 0;
  color: hsl(var(--foreground));
}

/* Form Improvements */
.form-group-improved {
  margin-bottom: 1.5rem;
}

.form-label-improved {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: hsl(var(--foreground));
}

.form-input-improved {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid hsl(var(--border));
  border-radius: 0.5rem;
  background: hsl(var(--background));
  color: hsl(var(--foreground));
  font-size: 0.875rem;
  transition: all 0.2s ease-in-out;
}

.form-input-improved:focus {
  outline: none;
  border-color: hsl(var(--ring));
  box-shadow: 0 0 0 3px hsl(var(--ring) / 0.1);
}

.form-error {
  color: hsl(var(--destructive));
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

/* Button Improvements */
.button-improved {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  border: none;
  text-decoration: none;
  white-space: nowrap;
}

.button-primary {
  background: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.button-primary:hover {
  background: hsl(var(--primary) / 0.9);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.button-secondary {
  background: hsl(var(--secondary));
  color: hsl(var(--secondary-foreground));
}

.button-outline {
  background: transparent;
  border: 1px solid hsl(var(--border));
  color: hsl(var(--foreground));
}

.button-outline:hover {
  background: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

/* Badge Improvements */
.badge-improved {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  white-space: nowrap;
}

.badge-default {
  background: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.badge-secondary {
  background: hsl(var(--secondary));
  color: hsl(var(--secondary-foreground));
}

.badge-success {
  background: hsl(142 76% 36%);
  color: hsl(355 7% 97%);
}

.badge-warning {
  background: hsl(38 92% 50%);
  color: hsl(0 0% 0%);
}

.badge-error {
  background: hsl(var(--destructive));
  color: hsl(var(--destructive-foreground));
}

/* Table Improvements */
.table-improved {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
}

.table-header-improved {
  background: hsl(var(--muted));
}

.table-header-improved th {
  padding: 0.75rem;
  text-align: left;
  font-weight: 600;
  font-size: 0.875rem;
  color: hsl(var(--muted-foreground));
  border-bottom: 1px solid hsl(var(--border));
}

.table-row-improved {
  border-bottom: 1px solid hsl(var(--border));
  transition: background-color 0.2s ease-in-out;
}

.table-row-improved:hover {
  background: hsl(var(--muted) / 0.5);
}

.table-cell-improved {
  padding: 0.75rem;
  font-size: 0.875rem;
  color: hsl(var(--foreground));
}

/* Spacing Utilities */
.space-y-1 > * + * {
  margin-top: 0.25rem;
}

.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.space-y-3 > * + * {
  margin-top: 0.75rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.space-x-2 > * + * {
  margin-left: 0.5rem;
}

.space-x-3 > * + * {
  margin-left: 0.75rem;
}

.space-x-4 > * + * {
  margin-left: 1rem;
}

/* Grid Improvements */
.grid-responsive {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .grid-responsive-sm-2 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) {
  .grid-responsive-md-3 {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1024px) {
  .grid-responsive-lg-4 {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Scroll Area Improvements */
.scroll-area-improved {
  overflow: auto;
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--border)) transparent;
}

.scroll-area-improved::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scroll-area-improved::-webkit-scrollbar-track {
  background: transparent;
}

.scroll-area-improved::-webkit-scrollbar-thumb {
  background: hsl(var(--border));
  border-radius: 3px;
}

.scroll-area-improved::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--border) / 0.8);
}

/* Loading States */
.loading-spinner {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid hsl(var(--border));
  border-radius: 50%;
  border-top-color: hsl(var(--primary));
  /* animation: spin 1s ease-in-out infinite; DISABLED FOR PERFORMANCE */
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-skeleton {
  background: linear-gradient(90deg, hsl(var(--muted)) 25%, hsl(var(--muted) / 0.5) 50%, hsl(var(--muted)) 75%);
  background-size: 200% 100%;
  /* animation: loading 1.5s infinite; DISABLED FOR PERFORMANCE */
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Focus Improvements */
.focus-visible:focus-visible {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* Animation Utilities */
.animate-fade-in {
  /* animation: fadeIn 0.3s ease-in-out; DISABLED FOR PERFORMANCE */
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-up {
  /* animation: slideUp 0.3s ease-out; DISABLED FOR PERFORMANCE */
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

/* Responsive Text */
.text-responsive {
  font-size: 0.875rem;
}

@media (min-width: 768px) {
  .text-responsive {
    font-size: 1rem;
  }
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
  
  .dialog-content-improved {
    box-shadow: none;
    border: 1px solid #000;
  }
}
