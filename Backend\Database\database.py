"""
Database configuration and connection management for RestroManage.
Supports both PostgreSQL (production) and SQLite (development).
"""

import os
from sqlalchemy import create_engine, MetaData, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database configuration
DATABASE_URL = os.getenv("DATABASE_URL")

# Default to SQLite for development if no DATABASE_URL is provided
if not DATABASE_URL:
    DATABASE_URL = "sqlite:///./restro_manage.db"
    print("Using SQLite database for development")
else:
    print(f"Using database: {DATABASE_URL.split('@')[0]}@***")

# Create engine with appropriate configuration
if DATABASE_URL.startswith("sqlite"):
    # SQLite configuration
    engine = create_engine(
        DATABASE_URL,
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
        echo=os.getenv("SQL_ECHO", "false").lower() == "true"
    )
else:
    # PostgreSQL configuration
    engine = create_engine(
        DATABASE_URL,
        pool_size=int(os.getenv("DB_POOL_SIZE", "10")),
        max_overflow=int(os.getenv("DB_MAX_OVERFLOW", "20")),
        pool_pre_ping=True,
        echo=os.getenv("SQL_ECHO", "false").lower() == "true"
    )

# Create SessionLocal class
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create Base class for declarative models
Base = declarative_base()

# Metadata for table operations
metadata = MetaData()

def get_db():
    """
    Dependency to get database session.
    Use this in FastAPI route dependencies.
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def get_db_session():
    """
    Get a database session for direct use.
    Remember to close the session when done.
    """
    return SessionLocal()

def test_connection():
    """
    Test database connection.
    Returns True if connection is successful, False otherwise.
    """
    try:
        db = SessionLocal()
        db.execute(text("SELECT 1"))
        db.close()
        return True
    except Exception as e:
        print(f"Database connection failed: {e}")
        return False

def get_database_info():
    """
    Get information about the current database configuration.
    """
    return {
        "database_url": DATABASE_URL.split('@')[0] + "@***" if '@' in DATABASE_URL else DATABASE_URL,
        "engine_name": engine.name,
        "pool_size": getattr(engine.pool, 'size', None),
        "max_overflow": getattr(engine.pool, 'max_overflow', None),
        "echo": engine.echo
    }
