import { useState, useRef, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { toast } from "@/components/ui/sonner";
import { <PERSON><PERSON>, User, Send, Mic, MicOff, X, Maximize2, Minimize2 } from "lucide-react";

// Define interfaces
interface Message {
  id: string;
  role: "user" | "assistant";
  content: string;
  timestamp: Date;
}

interface VirtualAssistantProps {
  inventoryData?: any[];
  salesData?: any[];
  staffData?: any[];
  forecastData?: any[];
  restaurantName?: string;
}

// Sample responses for different query types
const sampleResponses = {
  bestSelling: "Based on your recent sales data, your best-selling items are:\n1. Grilled Salmon (42 orders)\n2. Chicken Parmesan (38 orders)\n3. Margherita Pizza (35 orders)\n4. Beef Burger (30 orders)\n5. Caesar Salad (28 orders)",
  
  leastSelling: "Your least popular items in the past week are:\n1. Vegetable Soup (5 orders)\n2. Mushroom Risotto (7 orders)\n3. Seafood Platter (8 orders)\n4. Vegan Burger (10 orders)\n5. Chocolate Mousse (12 orders)",
  
  outOfStock: "The following items are currently out of stock or critically low:\n- Tomatoes (3kg remaining)\n- Olive Oil (2 liters remaining)\n- Chicken Breast (7kg remaining)\n\nI recommend placing an order soon, especially for these items.",
  
  staffSchedule: "For the upcoming week, you have:\n- Monday: 5 staff scheduled\n- Tuesday: 6 staff scheduled\n- Wednesday: 6 staff scheduled\n- Thursday: 7 staff scheduled\n- Friday: 8 staff scheduled\n- Saturday: 9 staff scheduled\n- Sunday: 7 staff scheduled\n\nMichael and Sarah have requested time off on Thursday.",
  
  forecast: "Based on historical data and current trends, I forecast:\n- 15% increase in customers this weekend compared to last\n- Projected revenue of £2,600 for Saturday\n- You should prepare for approximately 105 customers on Saturday\n- Peak hours will likely be 7-9 PM with 51 customers during that period",
  
  generalHelp: "I'm your restaurant management assistant. I can help with:\n- Sales analysis\n- Inventory management\n- Staff scheduling\n- Customer trends\n- Forecasting\n\nJust ask me what you'd like to know!",
  
  notUnderstood: "I'm sorry, I don't have enough information to answer that question. Could you please rephrase or ask something about your inventory, sales, staff scheduling, or forecasts?"
};

const VirtualAssistant = ({
  inventoryData = [],
  salesData = [],
  staffData = [],
  forecastData = [],
  restaurantName = "Your Restaurant"
}: VirtualAssistantProps) => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "welcome",
      role: "assistant",
      content: `Hello! I'm your restaurant management assistant for ${restaurantName}. How can I help you today?`,
      timestamp: new Date()
    }
  ]);
  const [input, setInput] = useState("");
  const [isListening, setIsListening] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Scroll to bottom of messages
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Focus input when component mounts
  useEffect(() => {
    if (!isMinimized) {
      inputRef.current?.focus();
    }
  }, [isMinimized]);

  // Handle speech recognition (mock implementation)
  const toggleListening = () => {
    if (!isListening) {
      setIsListening(true);
      toast.info("Listening... (This is a mock implementation)");
      
      // Simulate speech recognition after 3 seconds
      setTimeout(() => {
        setInput("What are my best selling items?");
        setIsListening(false);
        toast.success("Speech recognized!");
      }, 3000);
    } else {
      setIsListening(false);
      toast.info("Stopped listening");
    }
  };

  // Process user message and generate response
  const processMessage = async (userMessage: string) => {
    // Add user message to chat
    const newUserMessage: Message = {
      id: `user-${Date.now()}`,
      role: "user",
      content: userMessage,
      timestamp: new Date()
    };
    
    setMessages(prev => [...prev, newUserMessage]);
    setInput("");
    setIsLoading(true);
    
    // In a real implementation, this would call an LLM API
    // For this demo, we'll use sample responses based on keywords
    setTimeout(() => {
      let responseContent = "";
      
      // Simple keyword matching
      const lowerCaseMessage = userMessage.toLowerCase();
      
      if (lowerCaseMessage.includes("best sell") || lowerCaseMessage.includes("popular item") || lowerCaseMessage.includes("top sell")) {
        responseContent = sampleResponses.bestSelling;
      } else if (lowerCaseMessage.includes("least sell") || lowerCaseMessage.includes("worst sell") || lowerCaseMessage.includes("unpopular")) {
        responseContent = sampleResponses.leastSelling;
      } else if (lowerCaseMessage.includes("out of stock") || lowerCaseMessage.includes("low stock") || lowerCaseMessage.includes("inventory")) {
        responseContent = sampleResponses.outOfStock;
      } else if (lowerCaseMessage.includes("staff") || lowerCaseMessage.includes("schedule") || lowerCaseMessage.includes("employee")) {
        responseContent = sampleResponses.staffSchedule;
      } else if (lowerCaseMessage.includes("forecast") || lowerCaseMessage.includes("predict") || lowerCaseMessage.includes("future")) {
        responseContent = sampleResponses.forecast;
      } else if (lowerCaseMessage.includes("help") || lowerCaseMessage.includes("what can you do")) {
        responseContent = sampleResponses.generalHelp;
      } else {
        responseContent = sampleResponses.notUnderstood;
      }
      
      const assistantMessage: Message = {
        id: `assistant-${Date.now()}`,
        role: "assistant",
        content: responseContent,
        timestamp: new Date()
      };
      
      setMessages(prev => [...prev, assistantMessage]);
      setIsLoading(false);
    }, 1500);
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (input.trim() && !isLoading) {
      processMessage(input.trim());
    }
  };

  // Clear chat history
  const clearChat = () => {
    setMessages([
      {
        id: "welcome",
        role: "assistant",
        content: `Hello! I'm your restaurant management assistant for ${restaurantName}. How can I help you today?`,
        timestamp: new Date()
      }
    ]);
  };

  // Format timestamp
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  if (isMinimized) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button 
          className="rounded-full h-14 w-14 shadow-lg"
          onClick={() => setIsMinimized(false)}
        >
          <Bot className="h-6 w-6" />
        </Button>
      </div>
    );
  }

  return (
    <Card className="fixed bottom-4 right-4 w-80 sm:w-96 shadow-lg z-50 flex flex-col h-[500px]">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <Avatar className="h-8 w-8 bg-primary">
              <Bot className="h-4 w-4 text-primary-foreground" />
            </Avatar>
            <div>
              <CardTitle className="text-base">Virtual Assistant</CardTitle>
              <CardDescription className="text-xs">
                {isLoading ? "Thinking..." : "Online"}
              </CardDescription>
            </div>
          </div>
          <div className="flex items-center gap-1">
            <Button variant="ghost" size="icon" onClick={() => setIsMinimized(true)}>
              <Minimize2 className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon" onClick={clearChat}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="flex-grow overflow-hidden p-0">
        <ScrollArea className="h-full px-4">
          <div className="space-y-4 py-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${
                  message.role === "assistant" ? "justify-start" : "justify-end"
                }`}
              >
                <div
                  className={`flex gap-2 max-w-[80%] ${
                    message.role === "assistant" ? "flex-row" : "flex-row-reverse"
                  }`}
                >
                  {message.role === "assistant" && (
                    <Avatar className="h-8 w-8 mt-1 bg-primary">
                      <Bot className="h-4 w-4 text-primary-foreground" />
                    </Avatar>
                  )}
                  {message.role === "user" && (
                    <Avatar className="h-8 w-8 mt-1 bg-muted">
                      <User className="h-4 w-4" />
                    </Avatar>
                  )}
                  <div>
                    <div
                      className={`rounded-lg px-3 py-2 text-sm ${
                        message.role === "assistant"
                          ? "bg-muted"
                          : "bg-primary text-primary-foreground"
                      }`}
                    >
                      {message.content.split("\n").map((line, i) => (
                        <div key={i}>{line}</div>
                      ))}
                    </div>
                    <div
                      className={`text-xs text-muted-foreground mt-1 ${
                        message.role === "assistant" ? "text-left" : "text-right"
                      }`}
                    >
                      {formatTime(message.timestamp)}
                    </div>
                  </div>
                </div>
              </div>
            ))}
            {isLoading && (
              <div className="flex justify-start">
                <div className="flex gap-2 max-w-[80%]">
                  <Avatar className="h-8 w-8 mt-1 bg-primary">
                    <Bot className="h-4 w-4 text-primary-foreground" />
                  </Avatar>
                  <div className="rounded-lg px-3 py-2 text-sm bg-muted">
                    <div className="flex space-x-1">
                      <div className="h-2 w-2 bg-muted-foreground rounded-full animate-bounce"></div>
                      <div className="h-2 w-2 bg-muted-foreground rounded-full animate-bounce delay-75"></div>
                      <div className="h-2 w-2 bg-muted-foreground rounded-full animate-bounce delay-150"></div>
                    </div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>
      </CardContent>
      <CardFooter className="p-2">
        <form onSubmit={handleSubmit} className="flex w-full gap-2">
          <Button
            type="button"
            variant="outline"
            size="icon"
            onClick={toggleListening}
            className={isListening ? "bg-red-100" : ""}
          >
            {isListening ? (
              <MicOff className="h-4 w-4 text-red-500" />
            ) : (
              <Mic className="h-4 w-4" />
            )}
          </Button>
          <Input
            ref={inputRef}
            placeholder="Ask me anything..."
            value={input}
            onChange={(e) => setInput(e.target.value)}
            disabled={isLoading || isListening}
            className="flex-1"
          />
          <Button type="submit" size="icon" disabled={!input.trim() || isLoading}>
            <Send className="h-4 w-4" />
          </Button>
        </form>
      </CardFooter>
    </Card>
  );
};

export default VirtualAssistant;