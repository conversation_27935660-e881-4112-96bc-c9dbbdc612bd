import { useState } from 'react';
import { Notification } from '@/types/notification';
import { formatDistanceToNow } from 'date-fns';
import { 
  Bell, 
  Info, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  ShoppingBasket, 
  Users, 
  ShoppingCart, 
  Calendar,
  X,
  ExternalLink
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { useNotifications } from '@/contexts/NotificationContext';

interface NotificationItemProps {
  notification: Notification;
  onClose?: () => void;
}

const NotificationItem = ({ notification, onClose }: NotificationItemProps) => {
  const { markAsRead, deleteNotification } = useNotifications();
  const navigate = useNavigate();
  const [isHovered, setIsHovered] = useState(false);

  // Format the timestamp to relative time (e.g., "5 minutes ago")
  const formattedTime = formatDistanceToNow(new Date(notification.timestamp), { addSuffix: true });

  // Get the appropriate icon based on notification type
  const getIcon = () => {
    switch (notification.type) {
      case 'info':
        return <Info className="h-5 w-5 text-blue-500" />;
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-amber-500" />;
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'inventory':
        return <ShoppingBasket className="h-5 w-5 text-purple-500" />;
      case 'staff':
        return <Users className="h-5 w-5 text-indigo-500" />;
      case 'order':
        return <ShoppingCart className="h-5 w-5 text-teal-500" />;
      case 'reservation':
        return <Calendar className="h-5 w-5 text-orange-500" />;
      default:
        return <Bell className="h-5 w-5 text-gray-500" />;
    }
  };

  // Handle click on notification
  const handleClick = () => {
    // Mark as read
    if (!notification.isRead) {
      markAsRead(notification.id);
    }
    
    // Navigate to link if provided
    if (notification.link) {
      if (onClose) onClose();
      navigate(notification.link);
    }
  };

  // Handle delete notification
  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    deleteNotification(notification.id);
  };

  return (
    <div 
      className={cn(
        "p-3 border-b last:border-b-0 cursor-pointer transition-colors",
        notification.isRead ? "bg-background" : "bg-muted/30",
        notification.priority === 'high' ? "border-l-2 border-l-red-500" : "",
        isHovered ? "bg-muted/50" : ""
      )}
      onClick={handleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="flex items-start gap-3">
        <div className="flex-shrink-0 mt-0.5">
          {getIcon()}
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex justify-between items-start">
            <h4 className={cn(
              "text-sm font-medium",
              !notification.isRead && "font-semibold"
            )}>
              {notification.title}
            </h4>
            <div className="flex items-center gap-1 ml-2">
              {isHovered && (
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="h-5 w-5" 
                  onClick={handleDelete}
                >
                  <X className="h-3 w-3" />
                </Button>
              )}
            </div>
          </div>
          <p className="text-xs text-muted-foreground mt-1">{notification.message}</p>
          <div className="flex justify-between items-center mt-1">
            <span className="text-xs text-muted-foreground">{formattedTime}</span>
            {notification.link && (
              <ExternalLink className="h-3 w-3 text-muted-foreground" />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotificationItem;
