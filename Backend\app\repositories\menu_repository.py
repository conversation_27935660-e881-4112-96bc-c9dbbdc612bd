# Menu Repository - Database operations for menu items
# This replaces the JSON storage system with proper database queries

from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_
from sqlalchemy.orm import selectinload
import uuid
from datetime import datetime

from Database.models.menu import MenuItem as MenuItemModel
from app.models.menu import MenuItem, MenuItemCreate
from app.database import get_db_session_context

class MenuRepository:
    """Repository for menu item database operations"""
    
    def __init__(self):
        self.model = MenuItemModel

    async def get_all(
        self, 
        restaurant_id: Optional[str] = None,
        allergens: Optional[List[str]] = None,
        category: Optional[str] = None,
        available: Optional[bool] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Get all menu items with optional filtering"""
        async with get_db_session_context() as session:
            query = select(self.model)
            
            # Build filter conditions
            conditions = []
            
            if restaurant_id:
                conditions.append(self.model.restaurant_id == restaurant_id)
            
            if category:
                conditions.append(self.model.category.ilike(f"%{category}%"))
            
            if available is not None:
                conditions.append(self.model.available == available)
            
            # Apply allergen filtering if specified
            if allergens:
                allergen_conditions = []
                for allergen in allergens:
                    # Check if allergen is in the JSON array
                    allergen_conditions.append(
                        self.model.allergens.contains([allergen.lower()])
                    )
                conditions.append(or_(*allergen_conditions))
            
            # Apply all conditions
            if conditions:
                query = query.where(and_(*conditions))
            
            # Apply pagination
            query = query.offset(skip).limit(limit)
            
            # Execute query
            result = await session.execute(query)
            items = result.scalars().all()
            
            # Convert to dictionaries
            return [self._model_to_dict(item) for item in items]

    async def get_by_id(self, item_id: str) -> Optional[Dict[str, Any]]:
        """Get a menu item by ID"""
        async with get_db_session_context() as session:
            query = select(self.model).where(self.model.id == item_id)
            result = await session.execute(query)
            item = result.scalar_one_or_none()
            
            if item:
                return self._model_to_dict(item)
            return None

    async def create(self, item_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new menu item"""
        async with get_db_session_context() as session:
            # Generate ID if not provided
            if "id" not in item_data:
                item_data["id"] = str(uuid.uuid4())
            
            # Set timestamps
            now = datetime.utcnow()
            item_data["created_at"] = now
            item_data["updated_at"] = now
            
            # Create model instance
            item = self.model(**item_data)
            
            # Add to session and commit
            session.add(item)
            await session.commit()
            await session.refresh(item)
            
            return self._model_to_dict(item)

    async def update(self, item_id: str, item_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Update a menu item"""
        async with get_db_session_context() as session:
            # Get existing item
            query = select(self.model).where(self.model.id == item_id)
            result = await session.execute(query)
            item = result.scalar_one_or_none()
            
            if not item:
                return None
            
            # Update fields
            for key, value in item_data.items():
                if hasattr(item, key) and key != "id":  # Don't update ID
                    setattr(item, key, value)
            
            # Update timestamp
            item.updated_at = datetime.utcnow()
            
            # Commit changes
            await session.commit()
            await session.refresh(item)
            
            return self._model_to_dict(item)

    async def delete(self, item_id: str) -> bool:
        """Delete a menu item"""
        async with get_db_session_context() as session:
            # Get existing item
            query = select(self.model).where(self.model.id == item_id)
            result = await session.execute(query)
            item = result.scalar_one_or_none()
            
            if not item:
                return False
            
            # Delete item
            await session.delete(item)
            await session.commit()
            
            return True

    async def get_by_restaurant(self, restaurant_id: str) -> List[Dict[str, Any]]:
        """Get all menu items for a specific restaurant"""
        return await self.get_all(restaurant_id=restaurant_id)

    async def get_by_category(self, category: str, restaurant_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get menu items by category"""
        return await self.get_all(restaurant_id=restaurant_id, category=category)

    async def get_available_items(self, restaurant_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get only available menu items"""
        return await self.get_all(restaurant_id=restaurant_id, available=True)

    async def search_by_allergens(self, allergens: List[str], restaurant_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Search menu items that contain specific allergens"""
        return await self.get_all(restaurant_id=restaurant_id, allergens=allergens)

    def _model_to_dict(self, item: MenuItemModel) -> Dict[str, Any]:
        """Convert SQLAlchemy model to dictionary"""
        return {
            "id": item.id,
            "restaurant_id": item.restaurant_id,
            "name": item.name,
            "description": item.description,
            "price": float(item.price) if item.price else 0.0,
            "category": item.category,
            "allergens": item.allergens or [],
            "ingredients": item.ingredients or [],
            "available": item.available,
            "image_url": item.image_url,
            "preparation_time": item.preparation_time,
            "calories": item.calories,
            "spice_level": item.spice_level,
            "dietary_info": item.dietary_info or [],
            "created_at": item.created_at.isoformat() if item.created_at else None,
            "updated_at": item.updated_at.isoformat() if item.updated_at else None
        }

# Global repository instance
menu_repository = MenuRepository()
