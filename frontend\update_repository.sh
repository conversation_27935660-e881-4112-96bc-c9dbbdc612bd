#!/bin/bash

# Set error handling
set -e

# Function to display status messages
echo_status() {
  echo "===> $1"
}

# Navigate to the repository root
REPO_ROOT=$(git rev-parse --show-toplevel)
echo_status "Navigating to repository root: $REPO_ROOT"
cd "$REPO_ROOT"

# Check current Git status
echo_status "Checking Git status"
git status

# Add the Inventory.tsx file
echo_status "Adding Inventory.tsx to staging area"
git add src/pages/Inventory.tsx

# Check if there are changes to commit
if git diff --cached --quiet; then
  echo_status "No changes to commit. The file might already be committed."
else
  # Commit the changes
  echo_status "Committing changes"
  git commit -m "Add inventory features: waste tracking, stock updates, and label printing"
fi

# Check if we need to pull changes first
echo_status "Checking if we need to pull changes"
if git rev-list HEAD..origin/main --count | grep -q "^0$"; then
  echo_status "Local branch is up to date with origin/main"
else
  echo_status "Need to pull changes from origin/main"
  git pull --rebase origin main
fi

# Push to the main branch
echo_status "Pushing changes to main branch"
git push origin main

echo_status "Changes pushed to the main repository!"
