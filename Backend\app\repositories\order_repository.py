# Order Repository - Database operations for orders
# This replaces the JSON storage system with proper database queries

from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc
from sqlalchemy.orm import selectinload
import uuid
from datetime import datetime
from decimal import Decimal

from Database.models.orders import Order as OrderModel, OrderItem as OrderItemModel
from app.database import get_db_session_context

class OrderRepository:
    """Repository for order database operations"""
    
    def __init__(self):
        self.model = OrderModel
        self.item_model = OrderItemModel

    async def get_all(
        self, 
        restaurant_id: Optional[str] = None,
        table_id: Optional[str] = None,
        status: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Get all orders with optional filtering"""
        async with get_db_session_context() as session:
            query = select(self.model).options(selectinload(self.model.items))

            # Build filter conditions
            conditions = []

            if restaurant_id:
                conditions.append(self.model.restaurant_id == restaurant_id)

            if table_id:
                conditions.append(self.model.table_id == table_id)

            if status:
                conditions.append(self.model.status == status)

            # Apply all conditions
            if conditions:
                query = query.where(and_(*conditions))

            # Order by created_at descending (newest first)
            query = query.order_by(desc(self.model.created_at))

            # Apply pagination
            query = query.offset(skip).limit(limit)

            # Execute query
            result = await session.execute(query)
            orders = result.scalars().all()

            # Convert to dictionaries
            return [self._model_to_dict(order) for order in orders]

    async def get_by_id(self, order_id: str) -> Optional[Dict[str, Any]]:
        """Get an order by ID with items"""
        async with get_db_session_context() as session:
            query = select(self.model).options(selectinload(self.model.items)).where(self.model.id == order_id)
            result = await session.execute(query)
            order = result.scalar_one_or_none()

            if order:
                return self._model_to_dict(order)
            return None

    async def create(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new order with items"""
        async with get_db_session_context() as session:
            # Generate ID if not provided
            if "id" not in order_data:
                order_data["id"] = str(uuid.uuid4())
            
            # Set timestamps
            now = datetime.utcnow()
            order_data["created_at"] = now
            order_data["updated_at"] = now
            
            # Set default status if not provided
            if "status" not in order_data:
                order_data["status"] = "pending"
            
            # Extract items data
            items_data = order_data.pop("items", [])
            
            # Calculate total from items
            total = Decimal("0.00")
            for item_data in items_data:
                item_total = Decimal(str(item_data.get("price", 0))) * item_data.get("quantity", 1)
                total += item_total
            
            order_data["total"] = total
            
            # Create order instance
            order = self.model(**order_data)
            
            # Add to session
            session.add(order)
            await session.flush()  # Get the order ID
            
            # Create order items
            for item_data in items_data:
                if "id" not in item_data:
                    item_data["id"] = str(uuid.uuid4())
                item_data["order_id"] = order.id
                item_data["created_at"] = now
                item_data["updated_at"] = now
                
                order_item = self.item_model(**item_data)
                session.add(order_item)
            
            # Commit all changes
            await session.commit()
            await session.refresh(order)
            
            # Reload with items
            return await self.get_by_id(order.id)

    async def update(self, order_id: str, order_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Update an order"""
        async with get_db_session_context() as session:
            # Get existing order
            query = select(self.model).where(self.model.id == order_id)
            result = await session.execute(query)
            order = result.scalar_one_or_none()
            
            if not order:
                return None
            
            # Update fields (excluding items for now)
            for key, value in order_data.items():
                if hasattr(order, key) and key not in ["id", "items"]:
                    setattr(order, key, value)
            
            # Update timestamp
            order.updated_at = datetime.utcnow()
            
            # Commit changes
            await session.commit()
            await session.refresh(order)
            
            return await self.get_by_id(order.id)

    async def delete(self, order_id: str) -> bool:
        """Delete an order and its items"""
        async with get_db_session_context() as session:
            # Get existing order
            query = select(self.model).where(self.model.id == order_id)
            result = await session.execute(query)
            order = result.scalar_one_or_none()
            
            if not order:
                return False
            
            # Delete order (items will be deleted by cascade)
            await session.delete(order)
            await session.commit()
            
            return True

    async def update_status(self, order_id: str, status: str) -> Optional[Dict[str, Any]]:
        """Update order status"""
        return await self.update(order_id, {"status": status})

    async def get_by_restaurant(self, restaurant_id: str) -> List[Dict[str, Any]]:
        """Get all orders for a specific restaurant"""
        return await self.get_all(restaurant_id=restaurant_id)

    async def get_by_table(self, table_id: str) -> List[Dict[str, Any]]:
        """Get all orders for a specific table"""
        return await self.get_all(table_id=table_id)

    async def get_active_orders(self, restaurant_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get active orders (pending, preparing, ready)"""
        async with get_db_session_context() as session:
            query = select(self.model).options(selectinload(self.model.items))
            
            conditions = [
                self.model.status.in_(["pending", "preparing", "ready"])
            ]
            
            if restaurant_id:
                conditions.append(self.model.restaurant_id == restaurant_id)
            
            query = query.where(and_(*conditions))
            query = query.order_by(desc(self.model.created_at))
            
            result = await session.execute(query)
            orders = result.scalars().all()
            
            return [self._model_to_dict(order) for order in orders]

    def _model_to_dict(self, order: OrderModel) -> Dict[str, Any]:
        """Convert SQLAlchemy model to dictionary"""
        order_dict = {
            "id": order.id,
            "restaurant_id": order.restaurant_id,
            "table_id": order.table_id,
            "customer_name": order.customer_name,
            "status": order.status,
            "total": float(order.total) if order.total else 0.0,
            "special_instructions": order.special_instructions,
            "created_at": order.created_at.isoformat() if order.created_at else None,
            "updated_at": order.updated_at.isoformat() if order.updated_at else None,
            "items": []
        }
        
        # Add order items if loaded
        if hasattr(order, 'items') and order.items:
            order_dict["items"] = [
                {
                    "id": item.id,
                    "menu_item_id": item.menu_item_id,
                    "quantity": item.quantity,
                    "price": float(item.price) if item.price else 0.0,
                    "special_instructions": item.special_instructions,
                    "created_at": item.created_at.isoformat() if item.created_at else None,
                    "updated_at": item.updated_at.isoformat() if item.updated_at else None
                }
                for item in order.items
            ]
        
        return order_dict

# Global repository instance
order_repository = OrderRepository()
