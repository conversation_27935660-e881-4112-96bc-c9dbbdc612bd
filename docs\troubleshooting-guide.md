# Troubleshooting Guide

## 🚨 Critical Issues

### 1. Frontend Cannot Connect to Backend (ECONNREFUSED)

**Symptoms**:
- `ECONNREFUSED` errors in browser console
- API calls fail with connection errors
- Health check endpoint not responding

**Root Cause**: Port mismatch between frontend proxy and backend server

**Solution**:
```bash
# Check backend port
netstat -an | findstr :5002

# Update frontend/vite.config.ts
proxy: {
  "/api": "http://localhost:5002",      # Match actual backend port
  "/health": "http://localhost:5002"
}

# Restart frontend
npm run dev
```

**Detailed Fix**: See [Connection Issues](./connection-issues.md)

### 2. CORS Policy Errors

**Symptoms**:
```
Access to fetch at 'http://localhost:5002/api/...' from origin 'http://localhost:5175' has been blocked by CORS policy
```

**Solution**:
```python
# Backend/app/api.py - Add frontend origin
origins = [
    "http://localhost:5175",  # Add current frontend port
    "http://127.0.0.1:5175",
    # ... other origins
]
```

### 3. Backend Not Starting

**Symptoms**:
- `python main.py` fails
- Import errors
- Database connection errors

**Solutions**:
```bash
# Check Python version
python --version  # Should be 3.11+

# Activate virtual environment
cd Backend
source venv/bin/activate  # Linux/Mac
# or
venv\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt

# Check for missing dependencies
pip check
```

## 🔧 Development Issues

### Frontend Issues

#### Vite Dev Server Won't Start
```bash
# Clear cache and reinstall
rm -rf node_modules package-lock.json
npm install

# Check for port conflicts
netstat -an | findstr :5175

# Use different port if needed
npm run dev -- --port 5176
```

#### TypeScript Compilation Errors
```bash
# Check TypeScript configuration
npx tsc --noEmit

# Clear TypeScript cache
rm -rf node_modules/.cache
rm tsconfig.tsbuildinfo

# Restart TypeScript server in VS Code
Ctrl+Shift+P → "TypeScript: Restart TS Server"
```

#### Hot Module Replacement (HMR) Not Working
```typescript
// vite.config.ts - Fix HMR configuration
export default defineConfig({
  server: {
    hmr: {
      host: "localhost",
      port: 5177
    }
  }
});
```

### Backend Issues

#### FastAPI Server Crashes
```bash
# Check logs for errors
python main.py

# Common issues:
# 1. Port already in use
netstat -an | findstr :5002
# Kill process: taskkill /F /PID <pid>

# 2. Database connection issues
# Check DATABASE_URL in config

# 3. Missing environment variables
# Create .env file with required vars
```

#### Database Errors
```bash
# Reset SQLite database
rm Backend/restro_manage.db

# Run database initialization
cd Backend
python -c "from app.database import init_database; init_database()"

# Check database file permissions
ls -la restro_manage.db
```

#### Import Errors
```bash
# Check Python path
python -c "import sys; print(sys.path)"

# Ensure you're in Backend directory
cd Backend

# Check if app module is importable
python -c "from app.api import app; print('Import successful')"
```

## 🌐 Network & Connectivity

### API Endpoints Not Responding

#### Test Backend Directly
```bash
# Health check
curl -v http://localhost:5002/health

# API endpoint
curl -v http://localhost:5002/api/restaurants

# Check response headers
curl -I http://localhost:5002/health
```

#### Test Through Proxy
```bash
# Through Vite proxy
curl -v http://localhost:5175/health
curl -v http://localhost:5175/api/restaurants
```

#### Debug Proxy Issues
```typescript
// Add proxy debugging to vite.config.ts
proxy: {
  "/api": {
    target: "http://localhost:5002",
    configure: (proxy, options) => {
      proxy.on('error', (err, req, res) => {
        console.log('Proxy error:', err);
      });
      proxy.on('proxyReq', (proxyReq, req, res) => {
        console.log('Sending Request:', req.method, req.url);
      });
    }
  }
}
```

### Firewall & Security Issues

#### Windows Firewall
```bash
# Check if ports are blocked
netsh advfirewall firewall show rule name="Node.js Server"

# Allow Node.js through firewall
netsh advfirewall firewall add rule name="Node.js Server" dir=in action=allow protocol=TCP localport=5175
```

#### Antivirus Software
- Temporarily disable real-time protection
- Add project directory to exclusions
- Check if development servers are being blocked

## 🗄️ Database Issues

### SQLite Issues
```bash
# Check database file exists
ls -la Backend/restro_manage.db

# Check database permissions
chmod 664 Backend/restro_manage.db

# Test database connection
cd Backend
python -c "
from app.config import settings
from sqlalchemy import create_engine
engine = create_engine(settings.DATABASE_URL)
with engine.connect() as conn:
    result = conn.execute('SELECT 1')
    print('Database connection successful')
"
```

### Migration Issues
```bash
# Check Alembic configuration
cd Backend
alembic current

# Run migrations
alembic upgrade head

# Create new migration
alembic revision --autogenerate -m "description"
```

## 🔐 Authentication Issues

### JWT Token Problems
```bash
# Test token generation
curl -X POST http://localhost:5002/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}'

# Verify token format
# Should return: {"access_token": "...", "token_type": "bearer"}
```

### Session Issues
```bash
# Clear browser storage
# In browser console:
localStorage.clear();
sessionStorage.clear();

# Check cookie settings
# Ensure secure: false for development
```

## 📦 Dependency Issues

### Python Dependencies
```bash
# Check for conflicts
pip check

# Update dependencies
pip install --upgrade -r requirements.txt

# Create fresh environment
python -m venv fresh_venv
source fresh_venv/bin/activate
pip install -r requirements.txt
```

### Node.js Dependencies
```bash
# Check for vulnerabilities
npm audit

# Fix vulnerabilities
npm audit fix

# Update dependencies
npm update

# Clear npm cache
npm cache clean --force
```

## 🔍 Debugging Tools

### Backend Debugging
```python
# Add debug logging to main.py
import logging
logging.basicConfig(level=logging.DEBUG)

# Add request logging
@app.middleware("http")
async def log_requests(request: Request, call_next):
    print(f"Request: {request.method} {request.url}")
    response = await call_next(request)
    print(f"Response: {response.status_code}")
    return response
```

### Frontend Debugging
```typescript
// Add API call logging
const apiCall = async (url: string, options?: RequestInit) => {
  console.log('API Call:', url, options);
  const response = await fetch(url, options);
  console.log('API Response:', response.status, response.statusText);
  return response;
};
```

### Network Debugging
```bash
# Monitor network traffic
# Windows:
netstat -an | findstr :5002

# Check DNS resolution
nslookup localhost

# Test with different tools
# PowerShell:
Invoke-WebRequest http://localhost:5002/health

# curl with verbose output
curl -v http://localhost:5002/health
```

## 📋 Diagnostic Checklist

### Quick Health Check
- [ ] **Backend Running**: `curl http://localhost:5002/health`
- [ ] **Frontend Running**: Browser loads `http://localhost:5175`
- [ ] **Proxy Working**: `curl http://localhost:5175/health`
- [ ] **Database Connected**: No DB errors in backend logs
- [ ] **CORS Configured**: No CORS errors in browser console

### Environment Check
- [ ] **Python Version**: 3.11+
- [ ] **Node.js Version**: 18+
- [ ] **Virtual Environment**: Activated for backend
- [ ] **Dependencies**: All installed without conflicts
- [ ] **Ports Available**: 5002 (backend), 5175 (frontend)

### Configuration Check
- [ ] **Proxy Configuration**: Points to correct backend port
- [ ] **CORS Origins**: Includes frontend URL
- [ ] **Environment Variables**: Set correctly
- [ ] **Database URL**: Valid and accessible
- [ ] **Secret Keys**: Set for JWT tokens

## 🆘 Getting Help

### Log Collection
```bash
# Backend logs
cd Backend
python main.py > backend.log 2>&1

# Frontend logs
cd frontend
npm run dev > frontend.log 2>&1

# System information
# Windows:
systeminfo > system.log
# Linux:
uname -a > system.log
```

### Issue Reporting Template
```
**Environment**:
- OS: [Windows/Linux/Mac]
- Python Version: [3.11.x]
- Node.js Version: [18.x.x]
- Browser: [Chrome/Firefox/Safari]

**Issue Description**:
[Describe the problem]

**Steps to Reproduce**:
1. [Step 1]
2. [Step 2]
3. [Step 3]

**Expected Behavior**:
[What should happen]

**Actual Behavior**:
[What actually happens]

**Error Messages**:
[Copy exact error messages]

**Logs**:
[Attach relevant log files]
```

---

**Quick Links**:
- [Connection Issues Fix](./connection-issues.md)
- [Development Setup](./development-setup.md)
- [API Documentation](./api-documentation.md)
