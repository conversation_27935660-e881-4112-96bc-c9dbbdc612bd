#!/usr/bin/env python3
"""
Test script to verify database integration and order creation functionality.
This script tests the critical EPOS system database operations.
"""

import asyncio
import json
import aiohttp
import sys
from datetime import datetime

# API base URL
BASE_URL = "http://127.0.0.1:8000"

async def test_api_endpoint(session, method, endpoint, data=None):
    """Test an API endpoint and return the response"""
    url = f"{BASE_URL}{endpoint}"
    try:
        if method.upper() == "GET":
            async with session.get(url) as response:
                result = await response.json()
                return response.status, result
        elif method.upper() == "POST":
            headers = {"Content-Type": "application/json"}
            async with session.post(url, json=data, headers=headers) as response:
                result = await response.json()
                return response.status, result
    except Exception as e:
        return None, str(e)

async def main():
    """Main test function"""
    print("🔍 Testing Database Integration for EPOS System")
    print("=" * 60)
    
    async with aiohttp.ClientSession() as session:
        
        # Test 1: Health Check
        print("\n1. Testing Health Check...")
        status, result = await test_api_endpoint(session, "GET", "/health")
        if status == 200:
            print(f"✅ Health Check: {result['status']}")
            print(f"   Database: {result['database']['status']}")
        else:
            print(f"❌ Health Check Failed: {result}")
            return
        
        # Test 2: Get Restaurants
        print("\n2. Testing Restaurants API...")
        status, restaurants = await test_api_endpoint(session, "GET", "/api/restaurants")
        if status == 200:
            print(f"✅ Restaurants API: Found {len(restaurants)} restaurants")
            if restaurants:
                restaurant_id = restaurants[0].get('id')
                print(f"   First restaurant: {restaurants[0].get('name')} (ID: {restaurant_id})")
        else:
            print(f"❌ Restaurants API Failed: {restaurants}")
            return
        
        # Test 3: Get Menu Items
        print("\n3. Testing Menu API...")
        status, result = await test_api_endpoint(session, "GET", "/api/menu")
        if status == 200:
            print(f"✅ Menu API: Found {len(result)} menu items")
            if result:
                menu_item = result[0]
                print(f"   First item: {menu_item.get('name')} - £{menu_item.get('price')}")
        else:
            print(f"❌ Menu API Failed: {result}")
        
        # Test 4: Get Tables
        print("\n4. Testing Tables API...")
        status, result = await test_api_endpoint(session, "GET", "/api/tables")
        if status == 200:
            print(f"✅ Tables API: Found {len(result)} tables")
            if result:
                table = result[0]
                print(f"   First table: Table {table.get('number')} - {table.get('status')}")
        else:
            print(f"❌ Tables API Failed: {result}")
        
        # Test 5: Get Staff
        print("\n5. Testing Staff API...")
        status, result = await test_api_endpoint(session, "GET", "/api/staff")
        if status == 200:
            print(f"✅ Staff API: Found {len(result)} staff members")
            if result:
                staff = result[0]
                print(f"   First staff: {staff.get('name')} - {staff.get('role')}")
        else:
            print(f"❌ Staff API Failed: {result}")
        
        # Test 6: Create Test Order (if we have menu items and tables)
        print("\n6. Testing Order Creation...")
        
        # Get menu items for order
        menu_status, menu_items = await test_api_endpoint(session, "GET", "/api/menu")
        table_status, tables = await test_api_endpoint(session, "GET", "/api/tables")
        
        if menu_status == 200 and table_status == 200 and menu_items and tables and restaurants:
            # Create a test order
            test_order = {
                "restaurant_id": restaurants[0]["id"],  # Add required restaurant_id
                "table_id": tables[0]["id"],
                "customer_name": "Test Customer",
                "special_instructions": "Test order for database integration",
                "status": "pending",  # Add default status
                "total": menu_items[0]["price"] * 2,  # Calculate total
                "items": [
                    {
                        "menu_item_id": menu_items[0]["id"],
                        "quantity": 2,
                        "price": menu_items[0]["price"],
                        "special_instructions": "No onions"
                    }
                ]
            }
            
            status, result = await test_api_endpoint(session, "POST", "/api/orders", test_order)
            if status == 200:
                print(f"✅ Order Creation: Successfully created order {result.get('id')}")
                print(f"   Total: £{result.get('total')}")
                print(f"   Status: {result.get('status')}")
            else:
                print(f"❌ Order Creation Failed: {result}")
        else:
            print("⚠️  Skipping order creation - no menu items or tables available")
        
        # Test 7: Get Orders
        print("\n7. Testing Orders API...")
        status, result = await test_api_endpoint(session, "GET", "/api/orders")
        if status == 200:
            print(f"✅ Orders API: Found {len(result)} orders")
            if result:
                order = result[0]
                print(f"   Latest order: {order.get('id')} - £{order.get('total')} - {order.get('status')}")
        else:
            print(f"❌ Orders API Failed: {result}")

    print("\n" + "=" * 60)
    print("🎯 Database Integration Test Complete")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️  Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        sys.exit(1)
