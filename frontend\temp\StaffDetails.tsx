import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import Layout from "@/components/layout/Layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { toast } from "@/components/ui/sonner";
import { ArrowLeft, Edit, Trash2, Calendar, Mail, Phone, Clock } from "lucide-react";
import StaffPerformanceDetails from "@/components/staff/StaffPerformanceDetails";
import StaffSchedule from "@/components/staff/StaffSchedule";
import StaffAvailabilitySettings from "@/components/staff/StaffAvailabilitySettings";

interface StaffMember {
  id: string;
  name: string;
  role: string;
  position: string;
  email: string;
  phone: string;
  pin?: string;
  status: "active" | "inactive" | "on-leave";
  hireDate: string;
  performance: number;
  metrics?: {
    sales: number;
    tablesTurned: number;
    customerRating: number;
  };
  avatar?: string;
  workStatus?: "clocked-in" | "clocked-out" | "on-break";
  assignedHours?: number;
  availableDays?: string[];
}

// Mock staff data - in a real app, this would come from an API or context
const mockStaffData: StaffMember[] = [
  {
    id: "1",
    name: "Michael Rodriguez",
    role: "waiter",
    position: "Head Waiter",
    email: "<EMAIL>",
    phone: "(*************",
    status: "active",
    hireDate: "2022-03-15",
    performance: 92,
    metrics: {
      sales: 1250,
      tablesTurned: 48,
      customerRating: 4.8
    },
    assignedHours: 35,
    availableDays: ["mon", "tue", "wed", "thu", "fri"]
  },
  {
    id: "2",
    name: "Jennifer Smith",
    role: "waiter",
    position: "Waiter",
    email: "<EMAIL>",
    phone: "(*************",
    status: "active",
    hireDate: "2022-05-20",
    performance: 78,
    metrics: {
      sales: 980,
      tablesTurned: 38,
      customerRating: 4.5
    },
    assignedHours: 25,
    availableDays: ["mon", "wed", "fri", "sat"]
  },
  {
    id: "3",
    name: "David Chen",
    role: "waiter",
    position: "Waiter",
    email: "<EMAIL>",
    phone: "(*************",
    status: "on-leave",
    hireDate: "2022-08-10",
    performance: 65,
    metrics: {
      sales: 750,
      tablesTurned: 30,
      customerRating: 3.9
    },
    assignedHours: 30,
    availableDays: ["thu", "fri", "sat", "sun"]
  },
  {
    id: "4",
    name: "Maria Lopez",
    role: "chef",
    position: "Head Chef",
    email: "<EMAIL>",
    phone: "(*************",
    status: "active",
    hireDate: "2021-11-05",
    performance: 88,
    assignedHours: 40,
    availableDays: ["wed", "thu", "fri", "sat", "sun"]
  },
  {
    id: "5",
    name: "Robert Johnson",
    role: "manager",
    position: "Floor Manager",
    email: "<EMAIL>",
    phone: "(*************",
    status: "active",
    hireDate: "2021-06-15",
    performance: 95,
    assignedHours: 40,
    availableDays: ["mon", "tue", "wed", "thu", "fri"]
  }
];

const StaffDetails = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [staff, setStaff] = useState<StaffMember | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState("overview");

  useEffect(() => {
    // In a real app, this would be an API call
    const foundStaff = mockStaffData.find(s => s.id === id);
    if (foundStaff) {
      setStaff(foundStaff);
    } else {
      toast.error("Staff member not found");
      navigate("/staff");
    }
  }, [id, navigate]);

  const handleDeleteStaff = () => {
    // In a real app, this would be an API call
    toast.success(`${staff?.name} has been deleted`);
    navigate("/staff");
  };

  // Get initials for avatar
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800";
      case "inactive":
        return "bg-gray-100 text-gray-800";
      case "on-leave":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (!staff) {
    return (
      <Layout title="Staff Details" requiredRoles={["admin"]}>
        <div className="flex items-center justify-center h-[calc(100vh-200px)]">
          <p>Loading...</p>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title={`Staff Details - ${staff.name}`} requiredRoles={["admin"]}>
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <Button variant="outline" onClick={() => navigate("/staff")}>
            <ArrowLeft className="mr-2 h-4 w-4" /> Back to Staff
          </Button>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => navigate(`/staff/edit/${staff.id}`)}>
              <Edit className="mr-2 h-4 w-4" /> Edit
            </Button>
            <Button variant="destructive" onClick={() => setIsDeleteDialogOpen(true)}>
              <Trash2 className="mr-2 h-4 w-4" /> Delete
            </Button>
          </div>
        </div>

        <div className="flex flex-col md:flex-row gap-6">
          <Card className="w-full md:w-1/3">
            <CardContent className="pt-6">
              <div className="flex flex-col items-center text-center mb-6">
                <Avatar className="h-24 w-24 mb-4">
                  {staff.avatar && <img src={staff.avatar} alt={staff.name} />}
                  <AvatarFallback className="text-2xl">{getInitials(staff.name)}</AvatarFallback>
                </Avatar>
                <h2 className="text-2xl font-bold">{staff.name}</h2>
                <p className="text-muted-foreground capitalize">{staff.position}</p>
                <span className={`mt-2 px-3 py-1 rounded-full text-xs font-medium capitalize ${getStatusColor(staff.status)}`}>
                  {staff.status.replace('-', ' ')}
                </span>
              </div>

              <div className="space-y-4">
                <div className="flex items-center">
                  <Mail className="h-4 w-4 mr-2 text-muted-foreground" />
                  <span>{staff.email}</span>
                </div>
                <div className="flex items-center">
                  <Phone className="h-4 w-4 mr-2 text-muted-foreground" />
                  <span>{staff.phone}</span>
                </div>
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                  <span>Hired: {new Date(staff.hireDate).toLocaleDateString()}</span>
                </div>
                <div className="flex items-center">
                  <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
                  <span>Role: <span className="capitalize">{staff.role}</span></span>
                </div>
                <div className="flex items-center">
                  <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
                  <span>Assigned Hours: <span className="font-medium">{staff.assignedHours || 0}h / week</span></span>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="w-full md:w-2/3">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="mb-4">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="schedule">Schedule</TabsTrigger>
                <TabsTrigger value="availability">Availability</TabsTrigger>
                <TabsTrigger value="performance">Performance</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg font-medium">Staff Summary</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <h3 className="font-medium mb-2">About</h3>
                        <p className="text-muted-foreground">
                          {staff.name} is a {staff.position.toLowerCase()} who has been with the restaurant since {new Date(staff.hireDate).toLocaleDateString()}.
                          {staff.performance >= 85 ?
                            " They are one of our top performers with excellent customer service skills." :
                            staff.performance >= 70 ?
                            " They are a reliable team member who consistently meets expectations." :
                            " They are still developing their skills and receiving additional training."}
                        </p>
                      </div>

                      <div>
                        <h3 className="font-medium mb-2">Performance Overview</h3>
                        <div className="h-2 w-full bg-gray-200 rounded-full overflow-hidden">
                          <div
                            className={`h-full ${
                              staff.performance >= 85 ? "bg-green-500" :
                              staff.performance >= 70 ? "bg-yellow-500" :
                              "bg-red-500"
                            }`}
                            style={{ width: `${staff.performance}%` }}
                          ></div>
                        </div>
                        <div className="flex justify-between mt-1">
                          <span className="text-xs text-muted-foreground">0%</span>
                          <span className="text-xs font-medium">{staff.performance}%</span>
                          <span className="text-xs text-muted-foreground">100%</span>
                        </div>
                      </div>

                      {staff.metrics && (
                        <div>
                          <h3 className="font-medium mb-2">Key Metrics</h3>
                          <div className="grid grid-cols-3 gap-4">
                            <div className="bg-muted p-3 rounded-lg text-center">
                              <p className="text-sm text-muted-foreground">Sales</p>
                              <p className="text-lg font-semibold">${staff.metrics.sales}</p>
                            </div>
                            <div className="bg-muted p-3 rounded-lg text-center">
                              <p className="text-sm text-muted-foreground">Tables</p>
                              <p className="text-lg font-semibold">{staff.metrics.tablesTurned}</p>
                            </div>
                            <div className="bg-muted p-3 rounded-lg text-center">
                              <p className="text-sm text-muted-foreground">Rating</p>
                              <p className="text-lg font-semibold">{staff.metrics.customerRating}/5</p>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="schedule">
                <StaffSchedule staffId={staff.id} staffName={staff.name} />
              </TabsContent>

              <TabsContent value="availability">
                <StaffAvailabilitySettings 
                  staffId={staff.id} 
                  staffName={staff.name} 
                  initialAssignedHours={staff.assignedHours} 
                  initialAvailableDays={staff.availableDays}
                />
              </TabsContent>

              <TabsContent value="performance">
                <StaffPerformanceDetails
                  name={staff.name}
                  performance={staff.performance}
                  metrics={staff.metrics}
                />
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>Are you sure you want to delete {staff.name}? This action cannot be undone.</p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>Cancel</Button>
            <Button variant="destructive" onClick={handleDeleteStaff}>Delete</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Layout>
  );
};

export default StaffDetails;
