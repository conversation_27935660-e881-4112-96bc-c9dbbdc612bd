"""
Forecasting controller implementing business logic for forecasting operations.
Handles heavy forecasting computations with background processing and caching.
"""

from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from fastapi import BackgroundTasks

from app.controllers.base import BaseController
from app.services.forecasting_service import ForecastingService
from app.models.forecasting import (
    ForecastPeriod, SalesForecast, CustomerTrafficForecast,
    InventoryDemandForecast, StaffSchedulingForecast,
    SeasonalTrendsForecast, ComprehensiveForecast
)
from app.utils.logging_config import logger

class ForecastingController(BaseController):
    """Controller for forecasting business logic and operations"""
    
    def __init__(self):
        super().__init__()
        self.cache_prefix = "forecast"
        self.default_cache_ttl = 1800  # 30 minutes for forecast data
        self.forecasting_service = ForecastingService()
        self._background_tasks = {}  # Track background tasks
    
    async def get_sales_forecast(
        self,
        period: ForecastPeriod,
        days_ahead: int = 30,
        restaurant_id: Optional[str] = None,
        background_tasks: Optional[BackgroundTasks] = None
    ) -> Dict[str, Any]:
        """
        Get sales forecast with caching and optional background processing.
        
        Args:
            period: Forecast period
            days_ahead: Number of days to forecast
            restaurant_id: Restaurant ID for filtering
            background_tasks: FastAPI background tasks for async processing
            
        Returns:
            Sales forecast data or task status
        """
        # Create cache key
        cache_key = f"{self.cache_prefix}_sales_{period.value}_{days_ahead}_{restaurant_id or 'all'}"
        
        # Check cache first
        cached_result = self.get_cached_result(cache_key)
        if cached_result:
            logger.info("Returning cached sales forecast", "ForecastingController")
            return self._create_forecast_response(cached_result, from_cache=True)
        
        # For heavy computations, use background processing if available
        if background_tasks and days_ahead > 7:
            task_id = f"sales_forecast_{datetime.now().timestamp()}"
            
            await self.execute_background_task(
                background_tasks,
                self._generate_sales_forecast_background,
                task_id,
                cache_key,
                period,
                days_ahead,
                restaurant_id
            )
            
            return {
                "status": "processing",
                "task_id": task_id,
                "message": "Forecast generation started in background",
                "estimated_completion": (datetime.now() + timedelta(minutes=2)).isoformat()
            }
        
        # Generate forecast synchronously for quick requests
        forecast = await self.handle_async_operation(
            self.forecasting_service.generate_sales_forecast,
            period,
            days_ahead,
            error_message="Failed to generate sales forecast"
        )
        
        # Cache the result
        self.cache_result(cache_key, forecast.model_dump(), self.default_cache_ttl)
        
        logger.info(
            f"Generated sales forecast for {days_ahead} days",
            "ForecastingController",
            {"period": period.value, "restaurant_id": restaurant_id}
        )
        
        return self._create_forecast_response(forecast.model_dump())
    
    async def _generate_sales_forecast_background(
        self,
        task_id: str,
        cache_key: str,
        period: ForecastPeriod,
        days_ahead: int,
        restaurant_id: Optional[str]
    ):
        """Background task for generating sales forecast"""
        try:
            logger.info(f"Starting background sales forecast generation: {task_id}")
            
            forecast = await self.forecasting_service.generate_sales_forecast(period, days_ahead)
            
            # Cache the result
            self.cache_result(cache_key, forecast.model_dump(), self.default_cache_ttl)
            
            # Store completion status
            self._background_tasks[task_id] = {
                "status": "completed",
                "result": forecast.model_dump(),
                "completed_at": datetime.now().isoformat()
            }
            
            logger.info(f"Completed background sales forecast generation: {task_id}")
            
        except Exception as e:
            logger.error(f"Background sales forecast failed: {task_id} - {e}")
            self._background_tasks[task_id] = {
                "status": "failed",
                "error": str(e),
                "failed_at": datetime.now().isoformat()
            }
    
    async def get_customer_traffic_forecast(
        self,
        period: ForecastPeriod,
        days_ahead: int = 30,
        restaurant_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get customer traffic forecast with caching.
        
        Args:
            period: Forecast period
            days_ahead: Number of days to forecast
            restaurant_id: Restaurant ID for filtering
            
        Returns:
            Customer traffic forecast data
        """
        # Create cache key
        cache_key = f"{self.cache_prefix}_traffic_{period.value}_{days_ahead}_{restaurant_id or 'all'}"
        
        # Check cache first
        cached_result = self.get_cached_result(cache_key)
        if cached_result:
            return self._create_forecast_response(cached_result, from_cache=True)
        
        # Generate forecast
        forecast = await self.handle_async_operation(
            self.forecasting_service.generate_customer_traffic_forecast,
            period,
            days_ahead,
            error_message="Failed to generate customer traffic forecast"
        )
        
        # Cache the result
        self.cache_result(cache_key, forecast.model_dump(), self.default_cache_ttl)
        
        logger.info(
            f"Generated customer traffic forecast for {days_ahead} days",
            "ForecastingController",
            {"period": period.value, "restaurant_id": restaurant_id}
        )
        
        return self._create_forecast_response(forecast.model_dump())
    
    async def get_inventory_demand_forecast(
        self,
        period: ForecastPeriod,
        days_ahead: int = 30,
        restaurant_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get inventory demand forecast with caching.
        
        Args:
            period: Forecast period
            days_ahead: Number of days to forecast
            restaurant_id: Restaurant ID for filtering
            
        Returns:
            Inventory demand forecast data
        """
        # Create cache key
        cache_key = f"{self.cache_prefix}_inventory_{period.value}_{days_ahead}_{restaurant_id or 'all'}"
        
        # Check cache first
        cached_result = self.get_cached_result(cache_key)
        if cached_result:
            return self._create_forecast_response(cached_result, from_cache=True)
        
        # Generate forecast
        forecast = await self.handle_async_operation(
            self.forecasting_service.generate_inventory_demand_forecast,
            period,
            days_ahead,
            error_message="Failed to generate inventory demand forecast"
        )
        
        # Cache the result
        self.cache_result(cache_key, forecast.model_dump(), self.default_cache_ttl)
        
        logger.info(
            f"Generated inventory demand forecast for {days_ahead} days",
            "ForecastingController",
            {"period": period.value, "restaurant_id": restaurant_id}
        )
        
        return self._create_forecast_response(forecast.model_dump())
    
    async def get_staff_scheduling_forecast(
        self,
        period: ForecastPeriod,
        days_ahead: int = 30,
        restaurant_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get staff scheduling forecast with caching.
        
        Args:
            period: Forecast period
            days_ahead: Number of days to forecast
            restaurant_id: Restaurant ID for filtering
            
        Returns:
            Staff scheduling forecast data
        """
        # Create cache key
        cache_key = f"{self.cache_prefix}_staff_{period.value}_{days_ahead}_{restaurant_id or 'all'}"
        
        # Check cache first
        cached_result = self.get_cached_result(cache_key)
        if cached_result:
            return self._create_forecast_response(cached_result, from_cache=True)
        
        # Generate forecast
        forecast = await self.handle_async_operation(
            self.forecasting_service.generate_staff_scheduling_forecast,
            period,
            days_ahead,
            error_message="Failed to generate staff scheduling forecast"
        )
        
        # Cache the result
        self.cache_result(cache_key, forecast.model_dump(), self.default_cache_ttl)
        
        logger.info(
            f"Generated staff scheduling forecast for {days_ahead} days",
            "ForecastingController",
            {"period": period.value, "restaurant_id": restaurant_id}
        )
        
        return self._create_forecast_response(forecast.model_dump())
    
    async def get_seasonal_trends_forecast(
        self,
        period: ForecastPeriod,
        days_ahead: int = 30,
        restaurant_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get seasonal trends forecast with caching.
        
        Args:
            period: Forecast period
            days_ahead: Number of days to forecast
            restaurant_id: Restaurant ID for filtering
            
        Returns:
            Seasonal trends forecast data
        """
        # Create cache key
        cache_key = f"{self.cache_prefix}_seasonal_{period.value}_{days_ahead}_{restaurant_id or 'all'}"
        
        # Check cache first
        cached_result = self.get_cached_result(cache_key)
        if cached_result:
            return self._create_forecast_response(cached_result, from_cache=True)
        
        # Generate forecast
        forecast = await self.handle_async_operation(
            self.forecasting_service.generate_seasonal_trends_forecast,
            period,
            days_ahead,
            error_message="Failed to generate seasonal trends forecast"
        )
        
        # Cache the result
        self.cache_result(cache_key, forecast.model_dump(), self.default_cache_ttl)
        
        logger.info(
            f"Generated seasonal trends forecast for {days_ahead} days",
            "ForecastingController",
            {"period": period.value, "restaurant_id": restaurant_id}
        )
        
        return self._create_forecast_response(forecast.model_dump())
    
    def _create_forecast_response(
        self,
        forecast_data: Dict[str, Any],
        from_cache: bool = False
    ) -> Dict[str, Any]:
        """
        Create standardized forecast response.
        
        Args:
            forecast_data: Forecast data
            from_cache: Whether data came from cache
            
        Returns:
            Standardized response
        """
        return self.create_response(
            data=forecast_data,
            message="Forecast generated successfully",
            metadata={
                "from_cache": from_cache,
                "generated_at": datetime.now().isoformat(),
                "cache_ttl": self.default_cache_ttl
            }
        )
    
    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """
        Get status of background task.
        
        Args:
            task_id: Task ID
            
        Returns:
            Task status information
        """
        if task_id not in self._background_tasks:
            raise self.handle_not_found("Task", task_id)
        
        return self._background_tasks[task_id]
    
    async def invalidate_forecasts(self, restaurant_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Invalidate forecast caches.
        
        Args:
            restaurant_id: Restaurant ID to invalidate (all if None)
            
        Returns:
            Success response
        """
        if restaurant_id:
            self.invalidate_cache_pattern(f"{self.cache_prefix}_{restaurant_id}")
        else:
            self.invalidate_cache_pattern(self.cache_prefix)
        
        logger.info(
            f"Invalidated forecast caches",
            "ForecastingController",
            {"restaurant_id": restaurant_id or "all"}
        )
        
        return {
            "success": True,
            "message": "Forecast caches invalidated",
            "restaurant_id": restaurant_id
        }
