import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle, CardDescription } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "@/components/ui/sonner";
import { format, addDays, startOfWeek, endOfWeek } from "date-fns";
import { ForecastData } from "@/components/dashboard/ForecastCard";
import { AlertTriangle, Calendar, TrendingUp, ShoppingCart, Download } from "lucide-react";

// Define interfaces
interface InventoryItem {
  id: string;
  name: string;
  category: string;
  stock: number;
  unit: string;
  reorderLevel: number;
  cost: number;
  expiryDate?: string;
  isLow: boolean;
  isExpiring: boolean;
}

interface IngredientUsage {
  ingredientId: string;
  name: string;
  quantityPerCustomer: number;
  unit: string;
}

interface ForecastedInventoryItem extends InventoryItem {
  forecastedUsage: number;
  forecastedRemaining: number;
  needsRestock: boolean;
  restockAmount: number;
}

interface ForecastedInventoryProps {
  inventoryData: InventoryItem[];
  forecastData: ForecastData[];
  onGenerateOrder?: (items: { id: string; name: string; amount: number; unit: string }[]) => void;
}

// Mock ingredient usage data (in a real app, this would come from a database)
const ingredientUsageData: IngredientUsage[] = [
  { ingredientId: "1", name: "Tomatoes", quantityPerCustomer: 0.05, unit: "kg" },
  { ingredientId: "2", name: "Chicken Breast", quantityPerCustomer: 0.15, unit: "kg" },
  { ingredientId: "3", name: "Olive Oil", quantityPerCustomer: 0.01, unit: "liters" },
  { ingredientId: "4", name: "Rice", quantityPerCustomer: 0.08, unit: "kg" },
  { ingredientId: "5", name: "Onions", quantityPerCustomer: 0.03, unit: "kg" },
  { ingredientId: "6", name: "Salmon Fillet", quantityPerCustomer: 0.12, unit: "kg" },
  { ingredientId: "7", name: "Heavy Cream", quantityPerCustomer: 0.02, unit: "liters" }
];

const ForecastedInventory = ({ 
  inventoryData, 
  forecastData,
  onGenerateOrder 
}: ForecastedInventoryProps) => {
  const [forecastedInventory, setForecastedInventory] = useState<ForecastedInventoryItem[]>([]);
  const [selectedTimeframe, setSelectedTimeframe] = useState<"week" | "twoWeeks" | "month">("week");
  const [isLoading, setIsLoading] = useState(false);
  const [dateRange, setDateRange] = useState<{ start: Date; end: Date }>({
    start: startOfWeek(new Date(), { weekStartsOn: 1 }),
    end: endOfWeek(new Date(), { weekStartsOn: 1 })
  });

  // Calculate forecasted inventory usage based on customer forecast
  useEffect(() => {
    if (inventoryData.length === 0 || forecastData.length === 0) return;

    setIsLoading(true);

    // Calculate total forecasted customers for the selected timeframe
    let totalCustomers = 0;
    
    // Get the date range based on the selected timeframe
    let endDate = new Date(dateRange.end);
    if (selectedTimeframe === "twoWeeks") {
      endDate = addDays(dateRange.start, 13);
    } else if (selectedTimeframe === "month") {
      endDate = addDays(dateRange.start, 29);
    }
    
    setDateRange(prev => ({ ...prev, end: endDate }));

    // Sum up customers from forecast data
    // In a real app, you would filter by date range
    forecastData.forEach(day => {
      totalCustomers += day.customers;
    });

    // If we're looking at more than a week, multiply accordingly
    if (selectedTimeframe === "twoWeeks") {
      totalCustomers *= 2;
    } else if (selectedTimeframe === "month") {
      totalCustomers *= 4;
    }

    // Calculate forecasted usage for each inventory item
    const forecasted = inventoryData.map(item => {
      // Find the ingredient usage data for this item
      const usageData = ingredientUsageData.find(usage => usage.ingredientId === item.id);
      
      // Calculate forecasted usage
      const forecastedUsage = usageData 
        ? Math.round(totalCustomers * usageData.quantityPerCustomer * 100) / 100
        : 0;
      
      // Calculate forecasted remaining
      const forecastedRemaining = Math.max(0, item.stock - forecastedUsage);
      
      // Determine if restock is needed
      const needsRestock = forecastedRemaining < item.reorderLevel;
      
      // Calculate restock amount (to reach reorder level + 50% buffer)
      const restockAmount = needsRestock 
        ? Math.ceil((item.reorderLevel * 1.5) - forecastedRemaining) 
        : 0;
      
      return {
        ...item,
        forecastedUsage,
        forecastedRemaining,
        needsRestock,
        restockAmount
      };
    });

    setForecastedInventory(forecasted);
    setIsLoading(false);
  }, [inventoryData, forecastData, selectedTimeframe, dateRange.start]);

  // Handle timeframe change
  const handleTimeframeChange = (value: string) => {
    setSelectedTimeframe(value as "week" | "twoWeeks" | "month");
  };

  // Generate order for items that need restocking
  const handleGenerateOrder = () => {
    const itemsToOrder = forecastedInventory
      .filter(item => item.needsRestock)
      .map(item => ({
        id: item.id,
        name: item.name,
        amount: item.restockAmount,
        unit: item.unit
      }));

    if (itemsToOrder.length === 0) {
      toast.info("No items need restocking at this time");
      return;
    }

    if (onGenerateOrder) {
      onGenerateOrder(itemsToOrder);
    }

    toast.success(`Generated order for ${itemsToOrder.length} items`);
  };

  // Export forecast to CSV
  const handleExportForecast = () => {
    // Create CSV content
    const headers = ["Item", "Current Stock", "Unit", "Forecasted Usage", "Forecasted Remaining", "Needs Restock", "Restock Amount"];
    const rows = forecastedInventory.map(item => [
      item.name,
      item.stock,
      item.unit,
      item.forecastedUsage,
      item.forecastedRemaining,
      item.needsRestock ? "Yes" : "No",
      item.restockAmount
    ]);

    const csvContent = [
      headers.join(","),
      ...rows.map(row => row.join(","))
    ].join("\n");

    // Create a blob and download link
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", `inventory-forecast-${format(new Date(), "yyyy-MM-dd")}.csv`);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast.success("Forecast exported to CSV");
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <CardTitle className="text-lg font-medium">Forecasted Inventory Requirements</CardTitle>
            <CardDescription>
              Projected inventory usage based on customer forecast
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Select value={selectedTimeframe} onValueChange={handleTimeframeChange}>
              <SelectTrigger className="w-[180px]">
                <Calendar className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Select timeframe" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="week">Next Week</SelectItem>
                <SelectItem value="twoWeeks">Next 2 Weeks</SelectItem>
                <SelectItem value="month">Next Month</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" onClick={handleExportForecast}>
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button onClick={handleGenerateOrder}>
              <ShoppingCart className="h-4 w-4 mr-2" />
              Generate Order
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border mb-4 p-3 bg-muted/50">
          <div className="flex items-center gap-2 text-sm">
            <TrendingUp className="h-4 w-4" />
            <span>
              Forecast for {format(dateRange.start, "MMM d")} - {format(dateRange.end, "MMM d, yyyy")}
            </span>
          </div>
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center h-40">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : forecastedInventory.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No inventory data available for forecasting
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Item</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead className="text-right">Current Stock</TableHead>
                  <TableHead className="text-right">Forecasted Usage</TableHead>
                  <TableHead className="text-right">Forecasted Remaining</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Restock Amount</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {forecastedInventory.map(item => (
                  <TableRow key={item.id}>
                    <TableCell className="font-medium">{item.name}</TableCell>
                    <TableCell>{item.category}</TableCell>
                    <TableCell className="text-right">
                      {item.stock} {item.unit}
                    </TableCell>
                    <TableCell className="text-right">
                      {item.forecastedUsage} {item.unit}
                    </TableCell>
                    <TableCell className="text-right">
                      {item.forecastedRemaining} {item.unit}
                    </TableCell>
                    <TableCell>
                      {item.needsRestock ? (
                        <Badge variant="destructive" className="flex items-center gap-1 w-fit">
                          <AlertTriangle className="h-3 w-3" />
                          Restock Needed
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="w-fit">Sufficient</Badge>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      {item.needsRestock ? (
                        <span className="font-medium">
                          {item.restockAmount} {item.unit}
                        </span>
                      ) : (
                        "-"
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ForecastedInventory;