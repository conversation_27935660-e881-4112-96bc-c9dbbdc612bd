#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to force the backend to use JSON storage and load promo codes.
"""

import sys
import os

# Add the Backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.utils.storage import initialize_storage, get_all, storage

def force_json_storage():
    """Force load JSON storage and display promo codes"""
    
    print("🔄 Forcing JSON storage initialization...")
    
    # Initialize storage from JSON file
    initialize_storage()
    
    # Check promo codes
    promo_codes = get_all("promo_codes")
    print(f"📊 Found {len(promo_codes)} promo codes in JSON storage")
    
    if promo_codes:
        print("\n📋 Promo codes found:")
        for i, code in enumerate(promo_codes, 1):
            print(f"  {i}. {code.get('code')} - {code.get('name')}")
            print(f"     Type: {code.get('discount_type')}, Value: {code.get('discount_value')}")
            print(f"     Active: {code.get('is_active')}")
            print(f"     Valid: {code.get('start_date')} to {code.get('end_date')}")
            print()
    else:
        print("❌ No promo codes found in JSON storage")
    
    # Display storage contents
    print(f"📈 Storage summary:")
    for key, value in storage.items():
        if isinstance(value, list):
            print(f"  {key}: {len(value)} items")
    
    return len(promo_codes)

if __name__ == "__main__":
    try:
        count = force_json_storage()
        print(f"\n✨ JSON storage loaded with {count} promo codes!")
    except Exception as e:
        print(f"💥 Error: {e}")
        sys.exit(1)
