/**
 * Test component to verify React Query configuration is working correctly
 * This component should render without errors if QueryClientProvider is properly configured
 */

import React from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, AlertCircle } from 'lucide-react';

// Simple test query
const useTestQuery = () => {
  return useQuery({
    queryKey: ['test-query'],
    queryFn: async () => {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      return {
        message: 'React Query is working correctly!',
        timestamp: new Date().toISOString(),
        status: 'success'
      };
    },
    staleTime: 30 * 1000, // 30 seconds
  });
};

export const ReactQueryTest: React.FC = () => {
  const queryClient = useQueryClient();
  const { data, isLoading, error, isSuccess } = useTestQuery();

  // Check if QueryClient is available
  const hasQueryClient = !!queryClient;

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {hasQueryClient ? (
            <CheckCircle className="h-5 w-5 text-green-500" />
          ) : (
            <XCircle className="h-5 w-5 text-red-500" />
          )}
          React Query Test
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* QueryClient Status */}
        <div className="flex items-center justify-between">
          <span>QueryClient Available:</span>
          <Badge variant={hasQueryClient ? "default" : "destructive"}>
            {hasQueryClient ? "Yes" : "No"}
          </Badge>
        </div>

        {/* Query Status */}
        <div className="flex items-center justify-between">
          <span>Query Status:</span>
          <Badge variant={
            isLoading ? "secondary" : 
            error ? "destructive" : 
            isSuccess ? "default" : "outline"
          }>
            {isLoading ? "Loading" : 
             error ? "Error" : 
             isSuccess ? "Success" : "Idle"}
          </Badge>
        </div>

        {/* Query Data */}
        {isLoading && (
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <AlertCircle className="h-4 w-4" />
            Testing React Query...
          </div>
        )}

        {error && (
          <div className="text-sm text-red-600">
            Error: {error instanceof Error ? error.message : 'Unknown error'}
          </div>
        )}

        {data && (
          <div className="space-y-2">
            <div className="text-sm font-medium text-green-600">
              {data.message}
            </div>
            <div className="text-xs text-muted-foreground">
              Timestamp: {new Date(data.timestamp).toLocaleTimeString()}
            </div>
          </div>
        )}

        {/* QueryClient Info */}
        {hasQueryClient && (
          <div className="text-xs text-muted-foreground">
            QueryClient instance detected. React Query is properly configured.
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ReactQueryTest;
