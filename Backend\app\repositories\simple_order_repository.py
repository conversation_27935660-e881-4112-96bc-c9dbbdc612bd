"""
Simplified order repository using raw SQL queries.
This bypasses SQLAlchemy ORM issues and works directly with the database.
"""

import aiosqlite
from typing import List, Dict, Any, Optional
from pathlib import Path
import json

class SimpleOrderRepository:
    """Simple order repository using raw SQL"""
    
    def __init__(self):
        self.db_path = Path("restro_manage.db")
    
    async def get_all(
        self,
        restaurant_id: Optional[str] = None,
        table_id: Optional[str] = None,
        status: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Get all orders with optional filtering"""
        try:
            async with aiosqlite.connect(str(self.db_path)) as db:
                # Build query with filters
                query = "SELECT * FROM orders WHERE 1=1"
                params = []
                
                if restaurant_id:
                    query += " AND restaurant_id = ?"
                    params.append(restaurant_id)
                
                if table_id:
                    query += " AND table_id = ?"
                    params.append(table_id)
                
                if status:
                    query += " AND status = ?"
                    params.append(status)
                
                query += " ORDER BY created_at DESC LIMIT ? OFFSET ?"
                params.extend([limit, skip])
                
                cursor = await db.execute(query, params)
                rows = await cursor.fetchall()
                
                # Get column names
                columns = [description[0] for description in cursor.description]
                
                # Convert to dictionaries and fetch items for each order
                orders = []
                for row in rows:
                    order = dict(zip(columns, row))

                    # Get order items for this order
                    items_cursor = await db.execute("SELECT * FROM order_items WHERE order_id = ?", (order['id'],))
                    items_rows = await items_cursor.fetchall()

                    if items_rows:
                        # Get item column names
                        items_columns = [description[0] for description in items_cursor.description]

                        # Convert items to dictionaries
                        items = []
                        for item_row in items_rows:
                            item = dict(zip(items_columns, item_row))
                            items.append(item)

                        order['items'] = items
                    else:
                        order['items'] = []

                    orders.append(order)

                return orders
                
        except Exception as e:
            print(f"Error getting orders: {e}")
            return []
    
    async def get_by_id(self, order_id: str) -> Optional[Dict[str, Any]]:
        """Get an order by ID with its items"""
        try:
            async with aiosqlite.connect(str(self.db_path)) as db:
                # Get order details
                cursor = await db.execute("SELECT * FROM orders WHERE id = ?", (order_id,))
                row = await cursor.fetchone()

                if not row:
                    return None

                # Get column names
                columns = [description[0] for description in cursor.description]
                order = dict(zip(columns, row))

                # Convert applied_discounts JSON string back to list for Pydantic model
                if 'applied_discounts' in order and order['applied_discounts'] is not None:
                    import json
                    try:
                        order['applied_discounts'] = json.loads(order['applied_discounts'])
                    except (json.JSONDecodeError, TypeError):
                        order['applied_discounts'] = []

                # Get order items
                items_cursor = await db.execute("SELECT * FROM order_items WHERE order_id = ?", (order_id,))
                items_rows = await items_cursor.fetchall()

                # Get item column names
                items_columns = [description[0] for description in items_cursor.description]

                # Convert items to dictionaries
                items = []
                for item_row in items_rows:
                    item = dict(zip(items_columns, item_row))
                    items.append(item)

                order['items'] = items

                return order

        except Exception as e:
            print(f"Error getting order {order_id}: {e}")
            return None
    
    async def create(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new order with separate order items"""
        try:
            async with aiosqlite.connect(str(self.db_path)) as db:
                # Generate ID if not provided
                if 'id' not in order_data:
                    import uuid
                    order_data['id'] = str(uuid.uuid4())

                # Extract items for separate table
                items = order_data.pop('items', [])

                # Filter order data to only include fields that exist in orders table
                valid_order_fields = {
                    'id', 'restaurant_id', 'table_id', 'customer_name',
                    'special_instructions', 'status', 'total', 'subtotal',
                    'payment_status', 'tax_amount', 'tip_amount', 'discount_amount',
                    'applied_discounts', 'is_split_bill', 'split_bill_id',
                    'created_at', 'updated_at', 'completed_at'
                }
                filtered_order_data = {k: v for k, v in order_data.items() if k in valid_order_fields}

                # Convert applied_discounts list to JSON string for database storage
                if 'applied_discounts' in filtered_order_data and filtered_order_data['applied_discounts'] is not None:
                    import json
                    filtered_order_data['applied_discounts'] = json.dumps(filtered_order_data['applied_discounts'])

                # Add timestamps
                from datetime import datetime
                now = datetime.now().isoformat()
                filtered_order_data['created_at'] = now
                filtered_order_data['updated_at'] = now

                # Insert order into orders table
                order_columns = list(filtered_order_data.keys())
                order_placeholders = ', '.join(['?' for _ in order_columns])
                order_query = f"INSERT INTO orders ({', '.join(order_columns)}) VALUES ({order_placeholders})"

                await db.execute(order_query, list(filtered_order_data.values()))

                # Insert order items into order_items table
                for item in items:
                    item_data = {
                        'id': str(uuid.uuid4()),
                        'order_id': filtered_order_data['id'],
                        'menu_item_id': item.get('menu_item_id'),
                        'name': item.get('name'),
                        'quantity': item.get('quantity', 1),
                        'price': item.get('price', 0.0),
                        'special_instructions': item.get('special_instructions', ''),
                        'subtotal': item.get('subtotal', item.get('price', 0.0) * item.get('quantity', 1)),
                        'created_at': now,
                        'updated_at': now
                    }

                    item_columns = list(item_data.keys())
                    item_placeholders = ', '.join(['?' for _ in item_columns])
                    item_query = f"INSERT INTO order_items ({', '.join(item_columns)}) VALUES ({item_placeholders})"

                    await db.execute(item_query, list(item_data.values()))

                await db.commit()

                return await self.get_by_id(filtered_order_data['id'])

        except Exception as e:
            print(f"Error creating order: {e}")
            raise
    
    async def update(self, order_id: str, order_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Update an order"""
        try:
            async with aiosqlite.connect(str(self.db_path)) as db:
                # Convert JSON fields to strings
                if 'items' in order_data and isinstance(order_data['items'], list):
                    order_data['items'] = json.dumps(order_data['items'])
                
                # Add updated timestamp
                from datetime import datetime
                order_data['updated_at'] = datetime.now().isoformat()
                
                # Build update query
                set_clauses = [f"{key} = ?" for key in order_data.keys()]
                query = f"UPDATE orders SET {', '.join(set_clauses)} WHERE id = ?"
                params = list(order_data.values()) + [order_id]
                
                await db.execute(query, params)
                await db.commit()
                
                return await self.get_by_id(order_id)
                
        except Exception as e:
            print(f"Error updating order {order_id}: {e}")
            return None
    
    async def delete(self, order_id: str) -> bool:
        """Delete an order"""
        try:
            async with aiosqlite.connect(str(self.db_path)) as db:
                await db.execute("DELETE FROM orders WHERE id = ?", (order_id,))
                await db.commit()
                return True
                
        except Exception as e:
            print(f"Error deleting order {order_id}: {e}")
            return False

# Create global instance
simple_order_repository = SimpleOrderRepository()
