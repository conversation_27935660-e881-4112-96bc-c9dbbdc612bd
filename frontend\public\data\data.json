{"restaurants": [{"id": "1", "code": "GK001", "name": "The Gourmet Kitchen", "logo": "/logos/gourmet-kitchen.png", "address": "123 Main Street, Anytown, UK", "phone": "(*************", "email": "<EMAIL>", "vatRate": 20, "currency": "GBP", "isActive": true, "ownerName": "<PERSON>", "businessLicenseNumber": "BL-001", "restaurantType": "restaurant", "password": "gourmet123", "hasData": true, "subscriptionPlan": "pro", "subscriptionStatus": "active", "subscriptionExpiresAt": "2025-12-31T23:59:59.000Z", "billingInfo": {"monthlyPrice": 69, "currency": "GBP", "nextBillingDate": "2025-07-10T00:00:00.000Z"}, "createdAt": "2023-01-01T00:00:00.000Z", "updatedAt": "2023-01-01T00:00:00.000Z"}, {"id": "2", "code": "PP002", "name": "Pasta Paradise", "logo": "/logos/pasta-paradise.png", "address": "456 Elm Avenue, Somewhere, UK", "phone": "(*************", "email": "<EMAIL>", "vatRate": 20, "currency": "GBP", "isActive": true, "ownerName": "<PERSON>", "businessLicenseNumber": "BL-002", "restaurantType": "restaurant", "password": "pasta123", "hasData": true, "subscriptionPlan": "basic", "subscriptionStatus": "active", "subscriptionExpiresAt": "2025-12-31T23:59:59.000Z", "billingInfo": {"monthlyPrice": 29, "currency": "GBP", "nextBillingDate": "2025-07-10T00:00:00.000Z"}, "createdAt": "2023-01-01T00:00:00.000Z", "updatedAt": "2023-01-01T00:00:00.000Z"}, {"id": "3", "code": "SS003", "name": "Sushi <PERSON>", "logo": "/logos/sushi-paradise.png", "address": "789 Sushi Street, Japan Town, UK", "phone": "(*************", "email": "<EMAIL>", "vatRate": 20, "currency": "GBP", "isActive": true, "ownerName": "<PERSON><PERSON>", "businessLicenseNumber": "BL-003", "restaurantType": "restaurant", "password": "sushi123", "hasData": true, "subscriptionPlan": "basic", "subscriptionStatus": "active", "subscriptionExpiresAt": "2025-12-31T23:59:59.000Z", "billingInfo": {"monthlyPrice": 29, "currency": "GBP", "nextBillingDate": "2025-07-10T00:00:00.000Z"}, "createdAt": "2023-01-01T00:00:00.000Z", "updatedAt": "2023-01-01T00:00:00.000Z"}], "users": [{"id": "1", "name": "<PERSON>", "email": "<EMAIL>", "phone": "(*************", "restaurant_id": "1", "restaurant_name": "The Gourmet Kitchen", "role": "waiter", "position": "Head Waiter", "pin": "1234", "status": "active", "hireDate": "2022-03-15", "performance": 92, "accessLevel": "limited"}, {"id": "2", "name": "<PERSON>", "email": "<EMAIL>", "phone": "(*************", "restaurant_id": "1", "restaurant_name": "The Gourmet Kitchen", "role": "waiter", "position": "Waiter", "pin": "2345", "status": "active", "hireDate": "2022-05-20", "performance": 78, "accessLevel": "limited"}, {"id": "3", "name": "<PERSON>", "email": "<EMAIL>", "phone": "(*************", "restaurant_id": "1", "restaurant_name": "The Gourmet Kitchen", "role": "manager", "position": "Floor Manager", "pin": "5678", "status": "active", "hireDate": "2021-06-15", "performance": 95, "accessLevel": "full"}, {"id": "4", "name": "Admin User", "email": "<EMAIL>", "phone": "(*************", "restaurant_id": "0", "restaurant_name": "All Restaurants", "role": "admin", "position": "System Admin", "pin": "0000", "status": "active", "hireDate": "2020-01-01", "performance": 100, "accessLevel": "full"}, {"id": "5", "name": "<PERSON>", "email": "<EMAIL>", "phone": "(*************", "restaurant_id": "2", "restaurant_name": "Pasta Paradise", "role": "manager", "position": "Restaurant Manager", "pin": "1111", "status": "active", "hireDate": "2021-08-10", "performance": 90, "accessLevel": "full"}, {"id": "6", "name": "<PERSON>", "email": "<EMAIL>", "phone": "(*************", "restaurant_id": "2", "restaurant_name": "Pasta Paradise", "role": "waiter", "position": "Senior Waiter", "pin": "2222", "status": "active", "hireDate": "2022-01-15", "performance": 85, "accessLevel": "limited"}, {"id": "7", "name": "<PERSON>", "email": "<EMAIL>", "phone": "(*************", "restaurant_id": "3", "restaurant_name": "Sushi <PERSON>", "role": "manager", "position": "Floor Manager", "pin": "3333", "status": "active", "hireDate": "2021-05-20", "performance": 88, "accessLevel": "full"}, {"id": "8", "name": "<PERSON>", "email": "<EMAIL>", "phone": "(*************", "restaurant_id": "3", "restaurant_name": "Sushi <PERSON>", "role": "chef", "position": "<PERSON>shi Chef", "pin": "4444", "status": "active", "hireDate": "2021-09-15", "performance": 95, "accessLevel": "limited"}]}