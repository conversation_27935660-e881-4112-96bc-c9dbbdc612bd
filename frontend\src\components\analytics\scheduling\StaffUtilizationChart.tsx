import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  LineChart,
  Line
} from "recharts";
import { ForecastData } from "@/components/dashboard/ForecastCard";

interface StaffMember {
  id: string;
  name: string;
  role: string;
  position: string;
  status?: 'active' | 'inactive';
  availableDays?: string[];
  assignedHours?: number;
}

interface StaffUtilizationChartProps {
  forecastData: ForecastData[];
  staffData: StaffMember[];
  timeframe: "week" | "month" | "quarter";
}

export const StaffUtilizationChart = ({
  forecastData,
  staffData,
  timeframe
}: StaffUtilizationChartProps) => {
  // Filter data based on timeframe
  const filteredData = forecastData.slice(0, timeframe === "week" ? 7 : timeframe === "month" ? 30 : 90);
  
  // Calculate staff utilization data
  const utilizationData = filteredData.map(day => {
    const customers = day.customers || 0;
    
    // Calculate optimal staff needed based on customer volume
    const optimalStaff = Math.ceil(customers / 10); // 1 staff per 10 customers
    
    // Count available staff for this day
    const availableStaff = staffData.length;
    
    // Calculate utilization percentage
    const utilizationPercentage = Math.min(100, Math.round((optimalStaff / availableStaff) * 100));
    
    // Calculate staff efficiency (higher when utilization is between 70-90%)
    let efficiency = 0;
    if (utilizationPercentage < 70) {
      efficiency = utilizationPercentage; // Underutilized
    } else if (utilizationPercentage <= 90) {
      efficiency = 100; // Optimal utilization
    } else {
      efficiency = 100 - (utilizationPercentage - 90) * 2; // Overutilized, efficiency drops
    }
    
    return {
      day: day.day,
      customers,
      optimalStaff,
      availableStaff,
      utilization: utilizationPercentage,
      efficiency
    };
  });
  
  // Calculate staff distribution by role
  const roleDistribution = [
    { name: "Waiters", value: staffData.filter(staff => staff.role === "Waiter").length },
    { name: "Kitchen", value: staffData.filter(staff => staff.role === "Chef" || staff.role === "Kitchen Assistant").length },
    { name: "Bar", value: staffData.filter(staff => staff.role === "Bartender").length },
    { name: "Host", value: staffData.filter(staff => staff.role === "Host").length },
    { name: "Other", value: staffData.filter(staff => !["Waiter", "Chef", "Kitchen Assistant", "Bartender", "Host"].includes(staff.role)).length }
  ];
  
  // Custom tooltip formatter
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border rounded-md shadow-sm p-2 text-sm">
          <p className="font-medium">{label}</p>
          <p className="text-blue-500">Customers: {payload[0].payload.customers}</p>
          <p className="text-green-500">Optimal Staff: {payload[0].payload.optimalStaff}</p>
          <p className="text-amber-500">Available Staff: {payload[0].payload.availableStaff}</p>
          <p className="text-purple-500">Utilization: {payload[0].payload.utilization}%</p>
          <p className="text-red-500">Efficiency: {payload[0].payload.efficiency}%</p>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Staff Utilization & Efficiency</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={utilizationData}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="day" />
                <YAxis yAxisId="left" orientation="left" />
                <YAxis yAxisId="right" orientation="right" />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                <Line
                  yAxisId="left"
                  type="monotone"
                  dataKey="utilization"
                  name="Utilization %"
                  stroke="#8884d8"
                  activeDot={{ r: 8 }}
                />
                <Line
                  yAxisId="right"
                  type="monotone"
                  dataKey="efficiency"
                  name="Efficiency %"
                  stroke="#82ca9d"
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Staff vs. Customer Ratio</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-[250px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={utilizationData}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="day" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="customers" name="Customers" fill="#8884d8" />
                  <Bar dataKey="optimalStaff" name="Optimal Staff" fill="#82ca9d" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Staff Distribution by Role</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-[250px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={roleDistribution}
                  layout="vertical"
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" />
                  <YAxis type="category" dataKey="name" />
                  <Tooltip formatter={(value) => [`${value} staff`, 'Count']} />
                  <Bar dataKey="value" name="Staff Count" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default StaffUtilizationChart;
