# RestroManage Database Infrastructure

This directory contains the complete SQL database infrastructure for the RestroManage system, designed to replace the current JSON file storage system.

## 📁 Directory Structure

```
Database/
├── __init__.py              # Package initialization
├── database.py              # Database configuration and connection
├── migrations.py            # Database migration utilities
├── init_db.py              # Database initialization script
├── seed_data.py            # Sample data seeder
├── README.md               # This file
└── models/                 # SQLAlchemy models
    ├── __init__.py         # Models package initialization
    ├── base.py             # Base models and mixins
    ├── auth.py             # Authentication and user models
    ├── restaurants.py      # Restaurant models
    ├── menu.py             # Menu and menu item models
    ├── orders.py           # Order management models
    ├── discounts.py        # Discount and promo code models
    ├── staff.py            # Staff management models
    ├── tables.py           # Table management models
    ├── inventory.py        # Inventory management models
    ├── split_bills.py      # Split bill functionality models
    ├── analytics.py        # Analytics and reporting models
    ├── forecasting.py      # Forecasting and trend analysis models
    └── ai_models.py        # AI service integration models
```

## 🗄️ Database Models

### Core Models
- **User**: Authentication and user management
- **Restaurant**: Restaurant information and settings
- **MenuItem**: Menu items with allergen information
- **Order**: Order management with status tracking
- **PromoCode**: Discount and promotion management

### Advanced Models
- **SplitBill**: Split payment functionality
- **Staff**: Employee management and scheduling
- **Table**: Table management with real-time status
- **Inventory**: Stock management and tracking
- **Analytics**: Sales and customer analytics
- **Forecasting**: Predictive analytics and trends
- **AIModels**: AI service integration tracking

## 🚀 Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Configure Database
Set your database URL in environment variables:
```bash
# For PostgreSQL (Production)
export DATABASE_URL="postgresql://username:password@localhost/restro_manage"

# For SQLite (Development) - Default if not set
# Uses: sqlite:///./restro_manage.db
```

### 3. Initialize Database
```bash
cd Database
python init_db.py
```

### 4. Seed Sample Data (Optional)
```bash
python seed_data.py
```

## 🔧 Database Configuration

### Supported Databases
- **SQLite**: Default for development
- **PostgreSQL**: Recommended for production

### Environment Variables
- `DATABASE_URL`: Database connection string
- `SQL_ECHO`: Enable SQL query logging (true/false)
- `DB_POOL_SIZE`: Connection pool size (PostgreSQL only)
- `DB_MAX_OVERFLOW`: Max overflow connections (PostgreSQL only)

## 📊 Model Relationships

### Core Relationships
```
Restaurant (1) ←→ (N) User
Restaurant (1) ←→ (N) MenuItem
Restaurant (1) ←→ (N) Order
Restaurant (1) ←→ (N) Table
Order (1) ←→ (N) OrderItem
Order (1) ←→ (1) SplitBill
PromoCode (1) ←→ (N) PromoCodeUsage
```

### Advanced Relationships
```
Staff (1) ←→ (N) StaffSchedule
Table (1) ←→ (N) TableReservation
InventoryItem (1) ←→ (N) InventoryTransaction
SplitBill (1) ←→ (N) SplitPortion
SplitPortion (1) ←→ (N) SplitBillPayment
```

## 🛠️ Utility Functions

### Database Management
```python
from Database import create_tables, drop_tables, reset_database

# Create all tables
create_tables()

# Reset database (WARNING: Deletes all data)
reset_database()

# Check table existence
check_table_exists("users")
```

### Database Session
```python
from Database import get_db, get_db_session

# For FastAPI dependency injection
def my_endpoint(db: Session = Depends(get_db)):
    pass

# For direct use
db = get_db_session()
# ... use db
db.close()
```

## 🔍 Model Features

### Base Model Features
- **UUID Primary Keys**: All models use UUID for primary keys
- **Timestamps**: Automatic created_at and updated_at tracking
- **Soft Delete**: Optional soft deletion with is_deleted flag
- **Status Management**: Active/inactive status tracking
- **Audit Trail**: Created by and updated by tracking

### Advanced Features
- **JSON Fields**: Flexible data storage for complex structures
- **Relationship Management**: Comprehensive foreign key relationships
- **Index Optimization**: Strategic indexing for performance
- **Validation**: Built-in data validation and constraints

## 📈 Migration from JSON Storage

### Current JSON Files → SQL Tables
- `menu_items.json` → `menu_items` table
- `orders.json` → `orders` + `order_items` tables
- `promo_codes.json` → `promo_codes` table
- `restaurants.json` → `restaurants` table
- `users.json` → `users` table

### Migration Benefits
- **ACID Compliance**: Reliable transactions
- **Concurrent Access**: Multiple users safely
- **Data Integrity**: Foreign key constraints
- **Performance**: Indexed queries
- **Scalability**: Handle large datasets
- **Backup/Recovery**: Standard database tools

## 🔒 Security Features

### Authentication Models
- **Password Hashing**: Secure password storage
- **Session Management**: Token-based authentication
- **Role-Based Access**: Admin, manager, staff roles
- **Two-Factor Auth**: Optional 2FA support

### Data Protection
- **Soft Deletion**: Preserve data integrity
- **Audit Logging**: Track all changes
- **Access Control**: User-level permissions
- **Data Encryption**: Sensitive field protection

## 📝 Development Guidelines

### Adding New Models
1. Create model in appropriate file under `models/`
2. Add to `models/__init__.py`
3. Run `create_tables()` to update schema
4. Add sample data to `seed_data.py`

### Model Conventions
- Use descriptive field names
- Add appropriate indexes
- Include relationships
- Add validation constraints
- Document complex fields

## 🧪 Testing

### Sample Data
The `seed_data.py` script creates:
- 1 sample restaurant
- 3 sample users (admin, waiter, chef)
- 4 menu categories
- 6 menu items with allergen info
- 10 tables
- 2 promo codes

### Database Reset
```bash
python init_db.py --reset
```

## 🚀 Next Steps (Phase 2)

After Phase 1 completion, implement:
1. User registration flow
2. Restaurant setup wizard
3. Data import/export functionality
4. Integration with existing FastAPI endpoints
5. Frontend integration with new database

## 📞 Support

For database-related issues:
1. Check database connection with `test_connection()`
2. Verify schema with `verify_database_schema()`
3. Review logs for error details
4. Consult model documentation
