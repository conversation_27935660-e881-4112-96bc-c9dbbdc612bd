#!/bin/sh

# Railway entrypoint script for RestroManage Frontend
set -e

echo "🚀 Starting RestroManage Frontend on Railway..."

# Set Railway-specific environment variables
export PORT=${PORT:-80}

# Create runtime config file with Railway environment variables
echo "🔧 Configuring runtime environment..."
cat > /usr/share/nginx/html/config.js << EOF
window.ENV = {
  REACT_APP_API_URL: '${REACT_APP_API_URL:-http://localhost:8000}',
  REACT_APP_ENVIRONMENT: '${REACT_APP_ENVIRONMENT:-production}',
  RAILWAY_ENVIRONMENT: '${RAILWAY_ENVIRONMENT:-production}',
  RAILWAY_PUBLIC_DOMAIN: '${RAILWAY_PUBLIC_DOMAIN:-localhost}'
};
EOF

# Update nginx configuration with Railway-specific settings
echo "🌐 Configuring nginx for Railway..."
sed -i "s/listen 80;/listen ${PORT};/g" /etc/nginx/nginx.conf

# Create necessary directories
mkdir -p /var/cache/nginx /var/log/nginx

# Set proper permissions
chown -R nginx:nginx /var/cache/nginx /var/log/nginx /usr/share/nginx/html

echo "✅ Frontend configuration completed"
echo "🌐 Starting nginx on port ${PORT}"

# Execute the main command
exec "$@"
