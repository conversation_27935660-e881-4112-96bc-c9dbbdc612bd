/**
 * Example component demonstrating how to use the new state management system
 * Shows integration with React Query and the simplified store hooks
 */

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  useDashboardStore, 
  useAnalyticsStore, 
  useStaffStore, 
  useOrdersStore, 
  useSettingsStore,
  useAppStore 
} from '@/hooks/useAppStore';
import { Loader2, RefreshCw, TrendingUp, Users, ShoppingBag, Settings } from 'lucide-react';

const StateManagementExample: React.FC = () => {
  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">State Management Example</h1>
        <GlobalSyncButton />
      </div>

      <Tabs defaultValue="dashboard" className="space-y-4">
        <TabsList>
          <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="staff">Staff</TabsTrigger>
          <TabsTrigger value="orders">Orders</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="dashboard">
          <DashboardExample />
        </TabsContent>

        <TabsContent value="analytics">
          <AnalyticsExample />
        </TabsContent>

        <TabsContent value="staff">
          <StaffExample />
        </TabsContent>

        <TabsContent value="orders">
          <OrdersExample />
        </TabsContent>

        <TabsContent value="settings">
          <SettingsExample />
        </TabsContent>
      </Tabs>
    </div>
  );
};

// Global sync component
const GlobalSyncButton: React.FC = () => {
  const { syncWithBackend, lastSyncTime } = useAppStore();
  const [isSyncing, setIsSyncing] = React.useState(false);

  const handleSync = async () => {
    setIsSyncing(true);
    try {
      await syncWithBackend();
    } catch (error) {
      console.error('Sync failed:', error);
    } finally {
      setIsSyncing(false);
    }
  };

  return (
    <div className="flex items-center gap-2">
      {lastSyncTime && (
        <span className="text-sm text-muted-foreground">
          Last sync: {lastSyncTime.toLocaleTimeString()}
        </span>
      )}
      <Button onClick={handleSync} disabled={isSyncing} size="sm">
        {isSyncing ? (
          <Loader2 className="h-4 w-4 animate-spin mr-2" />
        ) : (
          <RefreshCw className="h-4 w-4 mr-2" />
        )}
        Sync All Data
      </Button>
    </div>
  );
};

// Dashboard example
const DashboardExample: React.FC = () => {
  const {
    metrics,
    quickStats,
    recentOrders,
    alerts,
    isLoading,
    error,
    refreshMetrics,
    addAlert,
    clearAlerts,
    toggleRealTime,
    isRealTimeEnabled,
  } = useDashboardStore();

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-6">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          Loading dashboard data...
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-red-500">Error: {error.message}</div>
          <Button onClick={() => refreshMetrics()} className="mt-2">
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Dashboard State</h2>
        <div className="flex gap-2">
          <Button
            variant={isRealTimeEnabled ? "default" : "outline"}
            size="sm"
            onClick={toggleRealTime}
          >
            Real-time: {isRealTimeEnabled ? "ON" : "OFF"}
          </Button>
          <Button onClick={() => refreshMetrics()} size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {quickStats.map((stat) => (
          <Card key={stat.id}>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">{stat.label}</p>
                  <p className="text-2xl font-bold">{stat.value}</p>
                </div>
                <TrendingUp className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Alerts */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Alerts ({alerts.length})</CardTitle>
            <div className="flex gap-2">
              <Button
                size="sm"
                onClick={() => addAlert({
                  type: 'info',
                  title: 'Test Alert',
                  message: 'This is a test alert',
                  priority: 'medium',
                })}
              >
                Add Test Alert
              </Button>
              <Button size="sm" variant="outline" onClick={clearAlerts}>
                Clear All
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {alerts.length === 0 ? (
            <p className="text-muted-foreground">No alerts</p>
          ) : (
            <div className="space-y-2">
              {alerts.map((alert) => (
                <div key={alert.id} className="flex items-center justify-between p-2 border rounded">
                  <div>
                    <p className="font-medium">{alert.title}</p>
                    <p className="text-sm text-muted-foreground">{alert.message}</p>
                  </div>
                  <Badge variant={alert.priority === 'high' ? 'destructive' : 'secondary'}>
                    {alert.priority}
                  </Badge>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recent Orders */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Orders ({recentOrders.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {recentOrders.length === 0 ? (
            <p className="text-muted-foreground">No recent orders</p>
          ) : (
            <div className="space-y-2">
              {recentOrders.slice(0, 5).map((order: any) => (
                <div key={order.id} className="flex items-center justify-between p-2 border rounded">
                  <div>
                    <p className="font-medium">Table {order.tableId}</p>
                    <p className="text-sm text-muted-foreground">
                      {order.items?.length || 0} items - £{order.totalAmount?.toFixed(2) || '0.00'}
                    </p>
                  </div>
                  <Badge>{order.status}</Badge>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

// Analytics example
const AnalyticsExample: React.FC = () => {
  const {
    data,
    filters,
    isLoading,
    error,
    refreshData,
    updateFilters,
    exportData,
  } = useAnalyticsStore();

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Analytics State</h2>
        <div className="flex gap-2">
          <Button onClick={() => exportData('csv')} size="sm" variant="outline">
            Export CSV
          </Button>
          <Button onClick={() => refreshData()} size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {isLoading && (
        <Card>
          <CardContent className="flex items-center justify-center p-6">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            Loading analytics data...
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Data Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div>
              <p className="text-sm text-muted-foreground">Sales Data Points</p>
              <p className="text-2xl font-bold">{data.salesData?.length || 0}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Popular Items</p>
              <p className="text-2xl font-bold">{data.popularItems?.length || 0}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Categories</p>
              <p className="text-2xl font-bold">{data.categoryPerformance?.length || 0}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Forecasts</p>
              <p className="text-2xl font-bold">{data.forecastData?.length || 0}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Staff example
const StaffExample: React.FC = () => {
  const {
    members,
    activeStaff,
    isLoading,
    isCreating,
    addStaffMember,
    refreshData,
  } = useStaffStore();

  const handleAddStaff = async () => {
    try {
      await addStaffMember({
        name: 'New Staff Member',
        email: '<EMAIL>',
        role: 'waiter',
        position: 'Waiter',
      });
    } catch (error) {
      console.error('Failed to add staff:', error);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Staff State</h2>
        <div className="flex gap-2">
          <Button onClick={handleAddStaff} disabled={isCreating} size="sm">
            {isCreating ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Users className="h-4 w-4 mr-2" />}
            Add Staff
          </Button>
          <Button onClick={() => refreshData()} size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle>All Staff ({members.length})</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center p-4">
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Loading...
              </div>
            ) : (
              <div className="space-y-2">
                {members.slice(0, 5).map((member: any) => (
                  <div key={member.id} className="flex items-center justify-between p-2 border rounded">
                    <div>
                      <p className="font-medium">{member.name}</p>
                      <p className="text-sm text-muted-foreground">{member.role}</p>
                    </div>
                    <Badge variant={member.status === 'active' ? 'default' : 'secondary'}>
                      {member.status}
                    </Badge>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Active Staff ({activeStaff.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {activeStaff.slice(0, 5).map((member: any) => (
                <div key={member.id} className="flex items-center justify-between p-2 border rounded">
                  <div>
                    <p className="font-medium">{member.name}</p>
                    <p className="text-sm text-muted-foreground">{member.position}</p>
                  </div>
                  <Badge variant="default">{member.status}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

// Orders example
const OrdersExample: React.FC = () => {
  const {
    activeOrders,
    ordersQueue,
    kitchenDisplay,
    isLoading,
    isCreating,
    createOrder,
    refreshData,
  } = useOrdersStore();

  const handleCreateOrder = async () => {
    try {
      await createOrder({
        tableId: 'table_1',
        items: [
          {
            menuItemId: 'item_1',
            name: 'Test Item',
            quantity: 1,
            price: 10.00,
            modifications: [],
          }
        ],
        totalAmount: 10.00,
        tax: 2.00,
        discount: 0,
        staffId: 'staff_1',
      });
    } catch (error) {
      console.error('Failed to create order:', error);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Orders State</h2>
        <div className="flex gap-2">
          <Button onClick={handleCreateOrder} disabled={isCreating} size="sm">
            {isCreating ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <ShoppingBag className="h-4 w-4 mr-2" />}
            Create Order
          </Button>
          <Button onClick={() => refreshData()} size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader>
            <CardTitle>Active Orders ({activeOrders.length})</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center p-4">
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Loading...
              </div>
            ) : (
              <div className="space-y-2">
                {activeOrders.slice(0, 5).map((order: any) => (
                  <div key={order.id} className="flex items-center justify-between p-2 border rounded">
                    <div>
                      <p className="font-medium">Table {order.tableId}</p>
                      <p className="text-sm text-muted-foreground">£{order.totalAmount?.toFixed(2)}</p>
                    </div>
                    <Badge>{order.status}</Badge>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Queue ({ordersQueue.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {ordersQueue.slice(0, 5).map((order: any) => (
                <div key={order.id} className="flex items-center justify-between p-2 border rounded">
                  <div>
                    <p className="font-medium">Table {order.tableId}</p>
                    <p className="text-sm text-muted-foreground">{order.items?.length} items</p>
                  </div>
                  <Badge variant="secondary">{order.status}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Kitchen Display ({kitchenDisplay.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {kitchenDisplay.slice(0, 5).map((order: any) => (
                <div key={order.orderId} className="flex items-center justify-between p-2 border rounded">
                  <div>
                    <p className="font-medium">Table {order.tableId}</p>
                    <p className="text-sm text-muted-foreground">{order.estimatedTime}min</p>
                  </div>
                  <Badge variant={order.priority === 'high' ? 'destructive' : 'default'}>
                    {order.priority}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

// Settings example
const SettingsExample: React.FC = () => {
  const {
    restaurantSettings,
    userPreferences,
    isDirty,
    isLoading,
    isSaving,
    updateUserPreferences,
    updateRestaurantSettings,
    refreshData,
  } = useSettingsStore();

  const handleUpdateTheme = () => {
    const themes = ['light', 'dark', 'system'];
    const currentIndex = themes.indexOf(userPreferences.theme);
    const nextTheme = themes[(currentIndex + 1) % themes.length];
    updateUserPreferences({ theme: nextTheme });
  };

  const handleSaveSettings = async () => {
    try {
      await updateRestaurantSettings(restaurantSettings);
    } catch (error) {
      console.error('Failed to save settings:', error);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Settings State</h2>
        <div className="flex gap-2">
          {isDirty && (
            <Button onClick={handleSaveSettings} disabled={isSaving} size="sm">
              {isSaving ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Settings className="h-4 w-4 mr-2" />}
              Save Changes
            </Button>
          )}
          <Button onClick={() => refreshData()} size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle>Restaurant Settings</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center p-4">
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Loading...
              </div>
            ) : (
              <div className="space-y-2">
                <div>
                  <p className="text-sm text-muted-foreground">Name</p>
                  <p className="font-medium">{restaurantSettings.name || 'Not set'}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Email</p>
                  <p className="font-medium">{restaurantSettings.email || 'Not set'}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Currency</p>
                  <p className="font-medium">{restaurantSettings.currency || 'GBP'}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">VAT Rate</p>
                  <p className="font-medium">{restaurantSettings.vatRate || 20}%</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>User Preferences</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">Theme</p>
                  <p className="text-sm text-muted-foreground">Current: {userPreferences.theme}</p>
                </div>
                <Button onClick={handleUpdateTheme} size="sm" variant="outline">
                  Change Theme
                </Button>
              </div>
              <div>
                <p className="font-medium">Language</p>
                <p className="text-sm text-muted-foreground">{userPreferences.language}</p>
              </div>
              <div>
                <p className="font-medium">Notifications</p>
                <div className="flex gap-2 mt-1">
                  <Badge variant={userPreferences.notifications.email ? 'default' : 'secondary'}>
                    Email
                  </Badge>
                  <Badge variant={userPreferences.notifications.push ? 'default' : 'secondary'}>
                    Push
                  </Badge>
                  <Badge variant={userPreferences.notifications.inApp ? 'default' : 'secondary'}>
                    In-App
                  </Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default StateManagementExample;
