from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import List, Optional
from datetime import datetime
from app.models.orders import Order, OrderCreate, OrderStatus, PaymentStatus
from app.services.discount_service import DiscountService
from app.repositories.simple_order_repository import simple_order_repository
from app.repositories.simple_menu_repository import simple_menu_repository
from app.repositories.simple_table_repository import simple_table_repository
from app.utils.auth import get_current_active_user

router = APIRouter(prefix="/orders", tags=["Orders"])

@router.get("/", response_model=List[Order])
async def get_orders(
    restaurant_id: Optional[str] = Query(None, description="Filter by restaurant ID"),
    table_id: Optional[str] = Query(None, description="Filter by table ID"),
    status: Optional[str] = Query(None, description="Filter by status"),
    current_user = Depends(get_current_active_user)
):
    """Get all orders with optional filtering"""
    return await simple_order_repository.get_all(
        restaurant_id=restaurant_id,
        table_id=table_id,
        status=status
    )

@router.get("/{order_id}", response_model=Order)
async def get_order(
    order_id: str,
    current_user = Depends(get_current_active_user)
):
    """Get an order by ID"""
    order = await simple_order_repository.get_by_id(order_id)
    if not order:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Order not found"
        )
    return order

@router.post("/", response_model=Order)
async def create_order(order: OrderCreate):
    """Create a new order"""
    # Calculate subtotal and items
    subtotal = 0
    items = []

    for item in order.items:
        # Get menu item to get name
        menu_item = await simple_menu_repository.get_by_id(item.menu_item_id)
        if not menu_item:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Menu item with ID {item.menu_item_id} not found"
            )

        # Calculate item subtotal
        item_subtotal = item.price * item.quantity
        subtotal += item_subtotal

        # Create order item
        order_item = item.model_dump()
        order_item["id"] = f"orderitem{len(items) + 1}"
        order_item["name"] = menu_item["name"]
        order_item["subtotal"] = item_subtotal

        items.append(order_item)

    # Calculate tax (20% VAT)
    tax_rate = 0.20
    tax_amount = subtotal * tax_rate / (1 + tax_rate)

    # Initialize totals
    discount_amount = 0.0
    applied_discounts = []

    # Apply promo codes if provided
    if order.promo_codes:
        for promo_code in order.promo_codes:
            try:
                validation_result = DiscountService.validate_promo_code(
                    promo_code, subtotal, items
                )
                if validation_result.is_valid:
                    discount_amount += validation_result.discount_amount
                    applied_discounts.append({
                        "promo_code": promo_code,
                        "discount_amount": validation_result.discount_amount,
                        "applied_to_items": validation_result.applicable_items
                    })
            except Exception as e:
                # Log error but don't fail order creation
                print(f"Error applying promo code {promo_code}: {e}")

    # Calculate final total
    total = subtotal - discount_amount

    # Create order dict
    order_dict = order.model_dump()
    order_dict["items"] = items
    order_dict["status"] = OrderStatus.PENDING
    order_dict["payment_status"] = "unpaid"
    order_dict["subtotal"] = subtotal
    order_dict["tax_amount"] = tax_amount
    order_dict["tip_amount"] = 0.0
    order_dict["discount_amount"] = discount_amount
    order_dict["total"] = total
    order_dict["applied_discounts"] = applied_discounts
    order_dict["is_split_bill"] = False
    order_dict["split_bill_id"] = None
    
    # Update table status
    table = await simple_table_repository.get_by_id(order.table_id)
    if not table:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Table with ID {order.table_id} not found"
        )

    # Create order
    new_order = await simple_order_repository.create(order_dict)

    # Update table status to occupied and set current order ID
    await simple_table_repository.update(order.table_id, {
        "status": "occupied",
        "current_order_id": new_order["id"]
    })

    return new_order

@router.put("/{order_id}/status", response_model=Order)
async def update_order_status(
    order_id: str,
    status: OrderStatus,
    current_user = Depends(get_current_active_user)
):
    """Update an order's status"""
    # Check if order exists
    order = await simple_order_repository.get_by_id(order_id)
    if not order:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Order not found"
        )

    updates = {"status": status}

    # If status is completed, set completed_at
    if status == OrderStatus.COMPLETED:
        updates["completed_at"] = datetime.now().isoformat()

        # Update table status to available
        await simple_table_repository.update(order["table_id"], {
            "status": "available",
            "current_order_id": None
        })

    # Update order
    updated_order = await simple_order_repository.update(order_id, updates)
    return updated_order

@router.delete("/{order_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_order(
    order_id: str,
    current_user = Depends(get_current_active_user)
):
    """Delete an order"""
    # Check if order exists
    order = await simple_order_repository.get_by_id(order_id)
    if not order:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Order not found"
        )

    # Update table status if this is the current order
    table = await simple_table_repository.get_by_id(order["table_id"])
    if table and table.get("current_order_id") == order_id:
        await simple_table_repository.update(order["table_id"], {
            "status": "available",
            "current_order_id": None
        })

    # Delete order
    success = await simple_order_repository.delete(order_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete order"
        )
