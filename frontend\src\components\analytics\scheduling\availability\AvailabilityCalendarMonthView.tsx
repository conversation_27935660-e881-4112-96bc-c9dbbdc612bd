import { useState, useEffect } from "react";
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameMonth, isSameDay, parseISO } from "date-fns";
import { Calendar } from "@/components/ui/calendar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ChevronLeft, ChevronRight, Calendar as CalendarIcon } from "lucide-react";
import { StaffUnavailability } from "@/types/staffAvailability";
import { getUnavailabilityForMonth, getUnavailabilityColor } from "@/services/staffAvailabilityService";
import AvailabilityLegend from "./AvailabilityLegend";

interface AvailabilityCalendarMonthViewProps {
  staffData: { id: string; name: string }[];
  onDateSelect?: (date: Date) => void;
}

const AvailabilityCalendarMonthView = ({
  staffData,
  onDateSelect,
}: AvailabilityCalendarMonthViewProps) => {
  const [currentMonth, setCurrentMonth] = useState<Date>(new Date());
  const [unavailability, setUnavailability] = useState<StaffUnavailability[]>([]);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);

  // Date validation function to prevent selecting past dates
  const handleDateSelect = (date: Date | undefined) => {
    if (date) {
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Reset time to start of day
      const selectedDateOnly = new Date(date);
      selectedDateOnly.setHours(0, 0, 0, 0);
      
      // Only allow selection of today or future dates
      if (selectedDateOnly >= today) {
        setSelectedDate(date);
        if (onDateSelect) {
          onDateSelect(date);
        }
      }
    } else {
      setSelectedDate(undefined);
    }
  };
  const [staffUnavailableOnSelectedDate, setStaffUnavailableOnSelectedDate] = useState<
    { staffId: string; staffName: string; type: string }[]
  >([]);

  useEffect(() => {
    // Load unavailability data for the current month
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();
    const monthUnavailability = getUnavailabilityForMonth(year, month);
    setUnavailability(monthUnavailability);
  }, [currentMonth]);

  useEffect(() => {
    if (selectedDate) {
      // Find staff who are unavailable on the selected date
      const dateString = format(selectedDate, 'yyyy-MM-dd');
      const unavailableStaff = unavailability
        .filter(u => u.date.startsWith(dateString))
        .map(u => {
          const staff = staffData.find(s => s.id === u.staffId);
          return {
            staffId: u.staffId,
            staffName: staff?.name || 'Unknown',
            type: u.type
          };
        });
      
      setStaffUnavailableOnSelectedDate(unavailableStaff);
      onDateSelect?.(selectedDate);
    } else {
      setStaffUnavailableOnSelectedDate([]);
    }
  }, [selectedDate, unavailability, staffData, onDateSelect]);

  const handlePreviousMonth = () => {
    setCurrentMonth(prevMonth => {
      const newMonth = new Date(prevMonth);
      newMonth.setMonth(newMonth.getMonth() - 1);
      return newMonth;
    });
  };

  const handleNextMonth = () => {
    setCurrentMonth(prevMonth => {
      const newMonth = new Date(prevMonth);
      newMonth.setMonth(newMonth.getMonth() + 1);
      return newMonth;
    });
  };

  // Custom day rendering to show unavailability
  const renderDay = (day: Date) => {
    // Only process days in the current month
    if (!isSameMonth(day, currentMonth)) return null;

    const dateString = format(day, 'yyyy-MM-dd');
    const dayUnavailability = unavailability.filter(u => u.date.startsWith(dateString));
    
    // Count unavailability by type
    const countByType: Record<string, number> = {};
    dayUnavailability.forEach(u => {
      countByType[u.type] = (countByType[u.type] || 0) + 1;
    });

    const isSelected = selectedDate ? isSameDay(day, selectedDate) : false;

    return (
      <div className={`relative w-full h-full min-h-[40px] flex flex-col items-center justify-center ${isSelected ? 'bg-primary text-primary-foreground rounded-md' : ''}`}>
        <span>{format(day, 'd')}</span>
        {Object.entries(countByType).length > 0 && (
          <div className="flex gap-1 mt-1">
            {Object.entries(countByType).map(([type, count]) => (
              <Badge
                key={type}
                variant="outline"
                className="text-[10px] h-4 px-1"
                style={{ 
                  backgroundColor: getUnavailabilityColor(type as any),
                  color: '#fff',
                  borderColor: 'transparent'
                }}
              >
                {count}
              </Badge>
            ))}
          </div>
        )}
      </div>
    );
  };

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-medium">Staff Availability Calendar</CardTitle>
          <div className="flex items-center gap-1">
            <Button variant="outline" size="icon" onClick={handlePreviousMonth}>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <div className="w-28 text-center">
              {format(currentMonth, 'MMMM yyyy')}
            </div>
            <Button variant="outline" size="icon" onClick={handleNextMonth}>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <Calendar
            mode="single"
            selected={selectedDate}
            onSelect={handleDateSelect}
            disabled={(date) => {
              const today = new Date();
              today.setHours(0, 0, 0, 0);
              const checkDate = new Date(date);
              checkDate.setHours(0, 0, 0, 0);
              return checkDate < today;
            }}
            month={currentMonth}
            onMonthChange={setCurrentMonth}
            className="w-full"
            components={{
              Day: ({ date, ...props }) => (
                <div {...props} className="w-full p-0">
                  {renderDay(date)}
                </div>
              ),
            }}
          />

          <AvailabilityLegend className="mt-4" />

          {selectedDate && staffUnavailableOnSelectedDate.length > 0 && (
            <div className="mt-4 p-3 border rounded-md">
              <h4 className="font-medium flex items-center gap-2 mb-2">
                <CalendarIcon className="h-4 w-4" />
                Staff Unavailable on {format(selectedDate, 'dd/MM/yyyy')}
              </h4>
              <div className="space-y-2">
                {staffUnavailableOnSelectedDate.map((staff) => (
                  <div key={staff.staffId} className="flex items-center gap-2">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: getUnavailabilityColor(staff.type as any) }}
                    />
                    <span>{staff.staffName}</span>
                    <Badge variant="outline" className="ml-auto">
                      {staff.type.replace('-', ' ')}
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default AvailabilityCalendarMonthView;
