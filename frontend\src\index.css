
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 20% 98%;
    --foreground: 215 25% 27%;

    --card: 0 0% 100%;
    --card-foreground: 215 25% 27%;

    --popover: 0 0% 100%;
    --popover-foreground: 215 25% 27%;

    --primary: 210 70% 35%;
    --primary-foreground: 210 20% 98%;

    --secondary: 6 78% 57%;
    --secondary-foreground: 0 0% 100%;

    --muted: 213 20% 93%;
    --muted-foreground: 215 16% 47%;

    --accent: 204 70% 53%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 210 70% 35%;

    --radius: 0.5rem;

    --sidebar-background: 215 32% 27%;
    --sidebar-foreground: 0 0% 0%;
    --sidebar-primary: 204 70% 53%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 215 38% 34%;
    --sidebar-accent-foreground: 0 0% 0%;
    --sidebar-border: 215 38% 34%;
    --sidebar-ring: 204 70% 53%;
  }

  .dark {
    --background: 215 32% 17%;
    --foreground: 210 20% 98%;

    --card: 215 28% 20%;
    --card-foreground: 210 20% 98%;

    --popover: 215 28% 20%;
    --popover-foreground: 210 20% 98%;

    --primary: 204 70% 53%;
    --primary-foreground: 0 0% 100%;

    --secondary: 6 78% 57%;
    --secondary-foreground: 0 0% 100%;

    --muted: 215 25% 30%;
    --muted-foreground: 215 16% 80%;

    --accent: 210 70% 35%;
    --accent-foreground: 210 20% 98%;

    --destructive: 0 62% 30%;
    --destructive-foreground: 210 20% 98%;

    --border: 215 25% 27%;
    --input: 215 25% 27%;
    --ring: 204 70% 53%;

    --sidebar-background: 215 32% 17%;
    --sidebar-foreground: 0 0% 100%;
    --sidebar-primary: 204 70% 53%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 215 25% 27%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 215 25% 27%;
    --sidebar-ring: 204 70% 53%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}
