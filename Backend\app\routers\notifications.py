"""
Notification router for RestroManage.
Provides REST API endpoints for notification management with restaurant ID isolation.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import List, Optional, Dict, Any
from datetime import datetime
import uuid

from app.models.notifications import (
    NotificationCreate,
    NotificationUpdate,
    NotificationResponse,
    NotificationFilter,
    NotificationStats,
    NotificationMarkRead,
    NotificationType,
    NotificationPriority
)
from app.utils.storage import get_all, get_by_id, create, update, delete, query
from app.utils.auth import get_current_restaurant_id
from app.utils.logging_config import logger

router = APIRouter(prefix="/notifications", tags=["Notifications"])

# Helper functions for notification management
def create_notification_dict(notification_data: NotificationCreate, restaurant_id: str) -> Dict[str, Any]:
    """Create a notification dictionary for storage"""
    return {
        "id": str(uuid.uuid4()),
        "restaurant_id": restaurant_id,
        "title": notification_data.title,
        "message": notification_data.message,
        "type": notification_data.type.value if hasattr(notification_data.type, 'value') else notification_data.type,
        "priority": notification_data.priority.value if hasattr(notification_data.priority, 'value') else notification_data.priority,
        "is_read": False,
        "link": notification_data.link,
        "related_id": notification_data.related_id,
        "created_at": datetime.utcnow().isoformat(),
        "updated_at": datetime.utcnow().isoformat(),
    }

def filter_notifications_by_restaurant(notifications: List[Dict[str, Any]], restaurant_id: str) -> List[Dict[str, Any]]:
    """Filter notifications by restaurant ID"""
    return [n for n in notifications if n.get("restaurant_id") == restaurant_id]

def dict_to_notification_response(notification_dict: Dict[str, Any]) -> NotificationResponse:
    """Convert dictionary to NotificationResponse"""
    return NotificationResponse(
        id=notification_dict["id"],
        restaurant_id=notification_dict["restaurant_id"],
        title=notification_dict["title"],
        message=notification_dict["message"],
        type=notification_dict["type"],
        priority=notification_dict["priority"],
        is_read=notification_dict["is_read"],
        link=notification_dict.get("link"),
        related_id=notification_dict.get("related_id"),
        created_at=datetime.fromisoformat(notification_dict["created_at"]),
        updated_at=datetime.fromisoformat(notification_dict["updated_at"]),
    )


@router.post("/", response_model=NotificationResponse, status_code=status.HTTP_201_CREATED)
async def create_notification(
    notification_data: NotificationCreate,
    restaurant_id: str = Depends(get_current_restaurant_id)
):
    """Create a new notification for the restaurant"""
    try:
        logger.info(
            f"Creating notification for restaurant {restaurant_id}",
            "NotificationRouter",
            {"type": notification_data.type, "priority": notification_data.priority}
        )

        # Create notification dictionary
        notification_dict = create_notification_dict(notification_data, restaurant_id)

        # Store in notifications collection
        created_notification = create("notifications", notification_dict)

        # Convert to response model
        notification_response = dict_to_notification_response(created_notification)

        logger.info(
            f"Notification created successfully: {notification_response.id}",
            "NotificationRouter",
            {"notification_id": notification_response.id}
        )

        return notification_response

    except Exception as e:
        logger.error(
            f"Failed to create notification: {str(e)}",
            "NotificationRouter",
            {"restaurant_id": restaurant_id, "error": str(e)}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create notification"
        )


@router.get("/", response_model=List[NotificationResponse])
async def get_notifications(
    type: Optional[str] = Query(None, description="Filter by notification type"),
    priority: Optional[str] = Query(None, description="Filter by notification priority"),
    is_read: Optional[bool] = Query(None, description="Filter by read status"),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of notifications to return"),
    restaurant_id: str = Depends(get_current_restaurant_id)
):
    """Get notifications for the restaurant with optional filtering"""
    try:
        # Get all notifications
        all_notifications = get_all("notifications")

        # Filter by restaurant ID
        restaurant_notifications = filter_notifications_by_restaurant(all_notifications, restaurant_id)

        # Apply additional filters
        filtered_notifications = restaurant_notifications
        if type:
            filtered_notifications = [n for n in filtered_notifications if n.get("type") == type]
        if priority:
            filtered_notifications = [n for n in filtered_notifications if n.get("priority") == priority]
        if is_read is not None:
            filtered_notifications = [n for n in filtered_notifications if n.get("is_read") == is_read]

        # Sort by created_at (newest first)
        filtered_notifications.sort(key=lambda x: x.get("created_at", ""), reverse=True)

        # Apply limit
        limited_notifications = filtered_notifications[:limit]

        # Convert to response models
        response_notifications = [dict_to_notification_response(n) for n in limited_notifications]

        logger.debug(
            f"Retrieved {len(response_notifications)} notifications for restaurant {restaurant_id}",
            "NotificationRouter",
            {"count": len(response_notifications)}
        )

        return response_notifications

    except Exception as e:
        logger.error(
            f"Failed to get notifications: {str(e)}",
            "NotificationRouter",
            {"restaurant_id": restaurant_id, "error": str(e)}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve notifications"
        )


@router.get("/stats", response_model=NotificationStats)
async def get_notification_stats(
    restaurant_id: str = Depends(get_current_restaurant_id)
):
    """Get notification statistics for the restaurant"""
    try:
        # Get all notifications for the restaurant
        all_notifications = get_all("notifications")
        restaurant_notifications = filter_notifications_by_restaurant(all_notifications, restaurant_id)

        # Calculate stats
        total_count = len(restaurant_notifications)
        unread_count = len([n for n in restaurant_notifications if not n.get("is_read", False)])

        # Count by type
        by_type = {}
        for notification in restaurant_notifications:
            notification_type = notification.get("type", "info")
            by_type[notification_type] = by_type.get(notification_type, 0) + 1

        # Count by priority
        by_priority = {}
        for notification in restaurant_notifications:
            priority = notification.get("priority", "medium")
            by_priority[priority] = by_priority.get(priority, 0) + 1

        stats = NotificationStats(
            total_count=total_count,
            unread_count=unread_count,
            by_type=by_type,
            by_priority=by_priority
        )

        logger.debug(
            f"Retrieved notification stats for restaurant {restaurant_id}",
            "NotificationRouter",
            {"total": stats.total_count, "unread": stats.unread_count}
        )

        return stats

    except Exception as e:
        logger.error(
            f"Failed to get notification stats: {str(e)}",
            "NotificationRouter",
            {"restaurant_id": restaurant_id, "error": str(e)}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve notification statistics"
        )


@router.patch("/mark-read", status_code=status.HTTP_200_OK)
async def mark_notifications_as_read(
    mark_read_data: NotificationMarkRead,
    restaurant_id: str = Depends(get_current_restaurant_id)
):
    """Mark notifications as read for the restaurant"""
    try:
        logger.info(
            f"Marking notifications as read for restaurant {restaurant_id}",
            "NotificationRouter",
            {"notification_ids": mark_read_data.notification_ids}
        )

        # Get all notifications
        all_notifications = get_all("notifications")

        # Filter by restaurant ID and notification IDs
        restaurant_notifications = filter_notifications_by_restaurant(all_notifications, restaurant_id)

        updated_count = 0
        for notification in restaurant_notifications:
            if notification.get("id") in mark_read_data.notification_ids:
                notification["is_read"] = True
                updated_count += 1

        # Save updated notifications back to storage
        # Note: This is a simplified approach. In a real database, you'd update specific records
        logger.info(f"Marked {updated_count} notifications as read", "NotificationRouter")

        return {"updated_count": updated_count, "message": f"Marked {updated_count} notifications as read"}

    except Exception as e:
        logger.error(
            f"Failed to mark notifications as read: {str(e)}",
            "NotificationRouter",
            {"restaurant_id": restaurant_id, "error": str(e)}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to mark notifications as read"
        )


@router.put("/mark-all-read", status_code=status.HTTP_200_OK)
async def mark_all_notifications_as_read(
    restaurant_id: str = Depends(get_current_restaurant_id)
):
    """Mark all notifications as read for the restaurant"""
    try:
        logger.info(f"Marking all notifications as read for restaurant {restaurant_id}", "NotificationRouter")

        # Get all notifications
        all_notifications = get_all("notifications")

        # Filter by restaurant ID
        restaurant_notifications = filter_notifications_by_restaurant(all_notifications, restaurant_id)

        updated_count = 0
        for notification in restaurant_notifications:
            if not notification.get("is_read", False):
                notification["is_read"] = True
                updated_count += 1

        logger.info(f"Marked {updated_count} notifications as read", "NotificationRouter")

        return {"updated_count": updated_count, "message": f"Marked {updated_count} notifications as read"}

    except Exception as e:
        logger.error(
            f"Failed to mark all notifications as read: {str(e)}",
            "NotificationRouter",
            {"restaurant_id": restaurant_id, "error": str(e)}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to mark all notifications as read"
        )


@router.delete("/{notification_id}", status_code=status.HTTP_200_OK)
async def delete_notification(
    notification_id: str,
    restaurant_id: str = Depends(get_current_restaurant_id)
):
    """Delete a specific notification for the restaurant"""
    try:
        logger.info(
            f"Deleting notification {notification_id} for restaurant {restaurant_id}",
            "NotificationRouter"
        )

        # Get all notifications
        all_notifications = get_all("notifications")

        # Find and remove the notification
        notification_found = False
        for i, notification in enumerate(all_notifications):
            if (notification.get("id") == notification_id and
                notification.get("restaurant_id") == restaurant_id):
                all_notifications.pop(i)
                notification_found = True
                break

        if not notification_found:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Notification not found"
            )

        logger.info(f"Notification {notification_id} deleted successfully", "NotificationRouter")

        return {"message": "Notification deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Failed to delete notification: {str(e)}",
            "NotificationRouter",
            {"notification_id": notification_id, "restaurant_id": restaurant_id, "error": str(e)}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete notification"
        )


@router.delete("/", status_code=status.HTTP_200_OK)
async def delete_all_notifications(
    restaurant_id: str = Depends(get_current_restaurant_id)
):
    """Delete all notifications for the restaurant"""
    try:
        logger.info(f"Deleting all notifications for restaurant {restaurant_id}", "NotificationRouter")

        # Get all notifications
        all_notifications = get_all("notifications")

        # Remove notifications for this restaurant
        initial_count = len(all_notifications)
        all_notifications[:] = [
            notification for notification in all_notifications
            if notification.get("restaurant_id") != restaurant_id
        ]
        deleted_count = initial_count - len(all_notifications)

        logger.info(f"Deleted {deleted_count} notifications", "NotificationRouter")

        return {"deleted_count": deleted_count, "message": f"Deleted {deleted_count} notifications"}

    except Exception as e:
        logger.error(
            f"Failed to delete all notifications: {str(e)}",
            "NotificationRouter",
            {"restaurant_id": restaurant_id, "error": str(e)}
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete all notifications"
        )
