import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON>Header, <PERSON><PERSON><PERSON><PERSON>le, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { toast } from "@/components/ui/sonner";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  FileText,
  Check,
  X,
  AlertTriangle,
  Loader2
} from "lucide-react";

interface FileImportDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onImport: (data: any[]) => void;
}

export const FileImportDialog = ({ isOpen, onClose, onImport }: FileImportDialogProps) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [results, setResults] = useState<any[] | null>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setResults(null);
      
      // Simulate processing the file
      setIsProcessing(true);
      setTimeout(() => {
        // Mock results based on file type
        const fileExt = file.name.split('.').pop()?.toLowerCase();
        let mockResults = [];
        
        if (fileExt === 'csv' || fileExt === 'xlsx') {
          mockResults = [
            { name: "Tomatoes", quantity: 5, unit: "kg", status: "matched" },
            { name: "Chicken Breast", quantity: 2.5, unit: "kg", status: "matched" },
            { name: "Olive Oil", quantity: 3, unit: "liters", status: "matched" }
          ];
        } else if (fileExt === 'pdf') {
          mockResults = [
            { name: "Tomatoes", quantity: 5, unit: "kg", status: "matched" },
            { name: "Unknown Item", quantity: 1, unit: "kg", status: "not_found" },
            { name: "Chicken Breast", quantity: 2.5, unit: "kg", status: "matched" }
          ];
        }
        
        setResults(mockResults);
        setIsProcessing(false);
      }, 1500);
    }
  };

  const handleImport = () => {
    if (results && results.length > 0) {
      const matchedItems = results.filter(item => item.status === "matched");
      if (matchedItems.length > 0) {
        onImport(matchedItems);
        toast.success(`Imported ${matchedItems.length} items successfully`);
        onClose();
      } else {
        toast.error("No matched items to import");
      }
    } else {
      toast.error("No data to import");
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[700px]">
        <DialogHeader>
          <DialogTitle>Import Inventory</DialogTitle>
          <p className="text-sm text-muted-foreground mt-1">
            Upload a file to automatically update inventory
          </p>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="fileUpload">Select File</Label>
            <div className="flex items-center gap-2">
              <Input
                id="fileUpload"
                type="file"
                accept=".csv,.xlsx,.pdf"
                onChange={handleFileChange}
                disabled={isProcessing}
              />
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Supported file types: CSV, XLSX, PDF
            </p>
          </div>

          {isProcessing && (
            <div className="flex items-center justify-center py-4">
              <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
              <span className="ml-2">Processing file data...</span>
            </div>
          )}

          {results && results.length > 0 && (
            <div className="border rounded-md overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Quantity</TableHead>
                    <TableHead>Unit</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {results.map((result, index) => (
                    <TableRow key={index}>
                      <TableCell>{result.name}</TableCell>
                      <TableCell>{result.quantity}</TableCell>
                      <TableCell>{result.unit}</TableCell>
                      <TableCell>
                        <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs ${
                          result.status === "matched" ? "bg-green-100 text-green-800" :
                          result.status === "not_found" ? "bg-yellow-100 text-yellow-800" :
                          "bg-red-100 text-red-800"
                        }`}>
                          {result.status === "matched" ? (
                            <><Check className="h-3 w-3 mr-1" /> Matched</>
                          ) : result.status === "not_found" ? (
                            <><AlertTriangle className="h-3 w-3 mr-1" /> Not Found</>
                          ) : (
                            <><X className="h-3 w-3 mr-1" /> Error</>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </div>
        <DialogFooter className="flex items-center justify-between">
          <div>
            {results && (
              <div className="text-sm">
                <span className="font-medium">
                  {results.filter(r => r.status === "matched").length} of {results.length} items matched
                </span>
              </div>
            )}
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            {results && results.length > 0 && (
              <Button onClick={handleImport} disabled={!results.some(r => r.status === "matched")}>
                <FileText className="mr-2 h-4 w-4" />
                Apply Changes
              </Button>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default FileImportDialog;
