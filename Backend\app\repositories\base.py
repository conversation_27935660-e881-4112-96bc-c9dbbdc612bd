"""
Base repository class providing common database operations.
All specific repositories inherit from this base class.
"""

from typing import Generic, TypeVar, Type, List, Optional, Dict, Any, Callable
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, and_, or_, func
from sqlalchemy.orm import selectinload
from app.database import Base
import logging

logger = logging.getLogger(__name__)

ModelType = TypeVar("ModelType", bound=Base)

class BaseRepository(Generic[ModelType]):
    """Base repository with common CRUD operations"""
    
    def __init__(self, model: Type[ModelType]):
        self.model = model
    
    async def get_by_id(self, db: AsyncSession, id: str) -> Optional[ModelType]:
        """Get a single record by ID"""
        try:
            result = await db.execute(select(self.model).where(self.model.id == id))
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting {self.model.__name__} by ID {id}: {e}")
            raise
    
    async def get_all(self, db: AsyncSession, skip: int = 0, limit: int = 100) -> List[ModelType]:
        """Get all records with pagination"""
        try:
            result = await db.execute(
                select(self.model)
                .offset(skip)
                .limit(limit)
                .order_by(self.model.created_at.desc())
            )
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting all {self.model.__name__}: {e}")
            raise
    
    async def create(self, db: AsyncSession, obj_in: Dict[str, Any]) -> ModelType:
        """Create a new record"""
        try:
            db_obj = self.model(**obj_in)
            db.add(db_obj)
            await db.flush()
            await db.refresh(db_obj)
            return db_obj
        except Exception as e:
            logger.error(f"Error creating {self.model.__name__}: {e}")
            await db.rollback()
            raise
    
    async def update(self, db: AsyncSession, id: str, obj_in: Dict[str, Any]) -> Optional[ModelType]:
        """Update an existing record"""
        try:
            # Remove None values to avoid overwriting with null
            update_data = {k: v for k, v in obj_in.items() if v is not None}
            
            if not update_data:
                return await self.get_by_id(db, id)
            
            # Add updated_at timestamp
            update_data['updated_at'] = func.now()
            
            result = await db.execute(
                update(self.model)
                .where(self.model.id == id)
                .values(**update_data)
                .returning(self.model)
            )
            
            updated_obj = result.scalar_one_or_none()
            if updated_obj:
                await db.refresh(updated_obj)
            return updated_obj
        except Exception as e:
            logger.error(f"Error updating {self.model.__name__} {id}: {e}")
            await db.rollback()
            raise
    
    async def delete(self, db: AsyncSession, id: str) -> bool:
        """Delete a record by ID"""
        try:
            result = await db.execute(
                delete(self.model).where(self.model.id == id)
            )
            return result.rowcount > 0
        except Exception as e:
            logger.error(f"Error deleting {self.model.__name__} {id}: {e}")
            await db.rollback()
            raise
    
    async def query(self, db: AsyncSession, filters: Dict[str, Any] = None, 
                   custom_filter: Callable = None, skip: int = 0, limit: int = 100) -> List[ModelType]:
        """Query records with filters"""
        try:
            query = select(self.model)
            
            # Apply dictionary filters
            if filters:
                conditions = []
                for key, value in filters.items():
                    if hasattr(self.model, key):
                        if isinstance(value, list):
                            conditions.append(getattr(self.model, key).in_(value))
                        else:
                            conditions.append(getattr(self.model, key) == value)
                
                if conditions:
                    query = query.where(and_(*conditions))
            
            # Apply custom filter function
            if custom_filter:
                query = query.where(custom_filter(self.model))
            
            # Apply pagination and ordering
            query = query.offset(skip).limit(limit).order_by(self.model.created_at.desc())
            
            result = await db.execute(query)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error querying {self.model.__name__}: {e}")
            raise
    
    async def count(self, db: AsyncSession, filters: Dict[str, Any] = None) -> int:
        """Count records with optional filters"""
        try:
            query = select(func.count(self.model.id))
            
            if filters:
                conditions = []
                for key, value in filters.items():
                    if hasattr(self.model, key):
                        if isinstance(value, list):
                            conditions.append(getattr(self.model, key).in_(value))
                        else:
                            conditions.append(getattr(self.model, key) == value)
                
                if conditions:
                    query = query.where(and_(*conditions))
            
            result = await db.execute(query)
            return result.scalar()
        except Exception as e:
            logger.error(f"Error counting {self.model.__name__}: {e}")
            raise
    
    async def exists(self, db: AsyncSession, id: str) -> bool:
        """Check if a record exists by ID"""
        try:
            result = await db.execute(
                select(func.count(self.model.id)).where(self.model.id == id)
            )
            return result.scalar() > 0
        except Exception as e:
            logger.error(f"Error checking existence of {self.model.__name__} {id}: {e}")
            raise
    
    async def bulk_create(self, db: AsyncSession, objects: List[Dict[str, Any]]) -> List[ModelType]:
        """Create multiple records in bulk"""
        try:
            db_objects = [self.model(**obj) for obj in objects]
            db.add_all(db_objects)
            await db.flush()
            
            # Refresh all objects to get generated IDs
            for obj in db_objects:
                await db.refresh(obj)
            
            return db_objects
        except Exception as e:
            logger.error(f"Error bulk creating {self.model.__name__}: {e}")
            await db.rollback()
            raise
    
    async def bulk_update(self, db: AsyncSession, updates: List[Dict[str, Any]]) -> int:
        """Update multiple records in bulk"""
        try:
            if not updates:
                return 0
            
            # Group updates by ID
            update_count = 0
            for update_data in updates:
                if 'id' not in update_data:
                    continue
                
                record_id = update_data.pop('id')
                update_data['updated_at'] = func.now()
                
                result = await db.execute(
                    update(self.model)
                    .where(self.model.id == record_id)
                    .values(**update_data)
                )
                update_count += result.rowcount
            
            return update_count
        except Exception as e:
            logger.error(f"Error bulk updating {self.model.__name__}: {e}")
            await db.rollback()
            raise
    
    async def bulk_delete(self, db: AsyncSession, ids: List[str]) -> int:
        """Delete multiple records in bulk"""
        try:
            result = await db.execute(
                delete(self.model).where(self.model.id.in_(ids))
            )
            return result.rowcount
        except Exception as e:
            logger.error(f"Error bulk deleting {self.model.__name__}: {e}")
            await db.rollback()
            raise
