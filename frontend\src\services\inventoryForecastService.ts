// Inventory Forecast Service for RestroManage-v0
import { ForecastData } from "@/components/dashboard/ForecastCard";
import { 
  InventoryForecastItem, 
  WeeklyInventoryForecast, 
  CategoryForecast, 
  PurchaseRecommendation,
  InventoryConsumptionPattern 
} from "@/types/inventoryForecast";
import { format, addDays, startOfWeek } from "date-fns";

// Mock consumption patterns based on historical data
const mockConsumptionPatterns: InventoryConsumptionPattern[] = [
  // Food items
  { itemId: "1", averageDailyUsage: 15, peakDayUsage: 25, seasonalMultiplier: 1.2, revenueCorrelation: 0.85 },
  { itemId: "2", averageDailyUsage: 8, peakDayUsage: 12, seasonalMultiplier: 1.0, revenueCorrelation: 0.75 },
  { itemId: "3", averageDailyUsage: 20, peakDayUsage: 35, seasonalMultiplier: 1.1, revenueCorrelation: 0.90 },
  { itemId: "4", averageDailyUsage: 12, peakDayUsage: 18, seasonalMultiplier: 0.9, revenueCorrelation: 0.70 },
  { itemId: "5", averageDailyUsage: 6, peakDayUsage: 10, seasonalMultiplier: 1.3, revenueCorrelation: 0.80 },
  
  // Beverages
  { itemId: "6", averageDailyUsage: 25, peakDayUsage: 40, seasonalMultiplier: 1.4, revenueCorrelation: 0.95 },
  { itemId: "7", averageDailyUsage: 18, peakDayUsage: 30, seasonalMultiplier: 1.2, revenueCorrelation: 0.85 },
  { itemId: "8", averageDailyUsage: 30, peakDayUsage: 50, seasonalMultiplier: 1.1, revenueCorrelation: 0.90 },
  
  // Supplies
  { itemId: "9", averageDailyUsage: 5, peakDayUsage: 8, seasonalMultiplier: 1.0, revenueCorrelation: 0.60 },
  { itemId: "10", averageDailyUsage: 3, peakDayUsage: 5, seasonalMultiplier: 1.0, revenueCorrelation: 0.50 },
];

// Mock inventory items with current stock
const mockInventoryItems = [
  // Food
  { id: "1", name: "Chicken Breast", category: "food", currentStock: 45, unit: "kg", unitCost: 8.50 },
  { id: "2", name: "Salmon Fillet", category: "food", currentStock: 20, unit: "kg", unitCost: 15.00 },
  { id: "3", name: "Ground Beef", category: "food", currentStock: 60, unit: "kg", unitCost: 12.00 },
  { id: "4", name: "Fresh Vegetables", category: "food", currentStock: 35, unit: "kg", unitCost: 4.50 },
  { id: "5", name: "Pasta", category: "food", currentStock: 25, unit: "kg", unitCost: 3.20 },
  
  // Beverages
  { id: "6", name: "Coffee Beans", category: "beverages", currentStock: 80, unit: "kg", unitCost: 18.00 },
  { id: "7", name: "Wine Selection", category: "beverages", currentStock: 45, unit: "bottles", unitCost: 25.00 },
  { id: "8", name: "Soft Drinks", category: "beverages", currentStock: 120, unit: "cans", unitCost: 1.50 },
  
  // Supplies
  { id: "9", name: "Napkins", category: "supplies", currentStock: 15, unit: "packs", unitCost: 12.00 },
  { id: "10", name: "Cleaning Supplies", category: "supplies", currentStock: 8, unit: "bottles", unitCost: 8.50 },
];

export function calculateInventoryForecast(
  forecastData: ForecastData[], 
  currentInventory: any[]
): WeeklyInventoryForecast {
  const weekStart = startOfWeek(new Date(), { weekStartsOn: 1 });
  
  // Calculate total predicted metrics for the week
  const totalPredictedRevenue = forecastData.reduce((sum, day) => sum + day.projectedRevenue, 0);
  const totalPredictedCustomers = forecastData.reduce((sum, day) => sum + day.customers, 0);
  
  // Calculate average revenue multiplier compared to baseline
  const baselineRevenue = 2000; // Daily baseline
  const revenueMultiplier = (totalPredictedRevenue / 7) / baselineRevenue;
  
  // Generate forecast for each inventory item
  const forecastItems: InventoryForecastItem[] = mockInventoryItems.map(item => {
    const pattern = mockConsumptionPatterns.find(p => p.itemId === item.id);
    if (!pattern) {
      return createDefaultForecastItem(item);
    }
    
    // Calculate predicted usage based on revenue correlation and seasonal factors
    const baseUsage = pattern.averageDailyUsage * 7; // Weekly base usage
    const revenueAdjustment = 1 + (revenueMultiplier - 1) * pattern.revenueCorrelation;
    const predictedUsageWeekly = Math.round(baseUsage * revenueAdjustment * pattern.seasonalMultiplier);
    
    // Calculate days until stockout
    const dailyUsage = predictedUsageWeekly / 7;
    const daysUntilStockout = dailyUsage > 0 ? Math.floor(item.currentStock / dailyUsage) : 999;
    
    // Determine alert level
    let alertLevel: 'sufficient' | 'reorder-soon' | 'critical';
    if (daysUntilStockout <= 3) alertLevel = 'critical';
    else if (daysUntilStockout <= 7) alertLevel = 'reorder-soon';
    else alertLevel = 'sufficient';
    
    // Calculate recommended stock (2 weeks supply)
    const recommendedStock = Math.ceil(predictedUsageWeekly * 2);
    const reorderQuantity = Math.max(0, recommendedStock - item.currentStock);
    
    return {
      id: item.id,
      name: item.name,
      category: item.category as 'food' | 'beverages' | 'supplies',
      currentStock: item.currentStock,
      unit: item.unit,
      predictedUsage: Math.round(predictedUsageWeekly / 7), // Daily
      predictedUsageWeekly,
      recommendedStock,
      reorderQuantity,
      alertLevel,
      daysUntilStockout,
      unitCost: item.unitCost,
      totalCostRecommended: reorderQuantity * item.unitCost,
      forecastConfidence: Math.round(pattern.revenueCorrelation * 100),
      historicalAccuracy: Math.round(85 + Math.random() * 10), // Mock accuracy
    };
  });
  
  // Calculate category forecasts
  const categoryForecasts = {
    food: calculateCategoryForecast(forecastItems, 'food'),
    beverages: calculateCategoryForecast(forecastItems, 'beverages'),
    supplies: calculateCategoryForecast(forecastItems, 'supplies'),
  };
  
  // Generate purchase recommendations
  const recommendedOrders = generatePurchaseRecommendations(forecastItems);
  
  return {
    weekStartDate: format(weekStart, 'yyyy-MM-dd'),
    totalPredictedRevenue,
    totalPredictedCustomers,
    categoryForecasts,
    items: forecastItems,
    recommendedOrders,
  };
}

function createDefaultForecastItem(item: any): InventoryForecastItem {
  return {
    id: item.id,
    name: item.name,
    category: item.category,
    currentStock: item.currentStock,
    unit: item.unit,
    predictedUsage: 5,
    predictedUsageWeekly: 35,
    recommendedStock: 70,
    reorderQuantity: Math.max(0, 70 - item.currentStock),
    alertLevel: 'sufficient',
    daysUntilStockout: 14,
    unitCost: item.unitCost,
    totalCostRecommended: Math.max(0, 70 - item.currentStock) * item.unitCost,
    forecastConfidence: 70,
    historicalAccuracy: 80,
  };
}

function calculateCategoryForecast(
  items: InventoryForecastItem[], 
  category: 'food' | 'beverages' | 'supplies'
): CategoryForecast {
  const categoryItems = items.filter(item => item.category === category);
  
  return {
    category,
    totalCurrentValue: categoryItems.reduce((sum, item) => sum + (item.currentStock * item.unitCost), 0),
    totalPredictedUsage: categoryItems.reduce((sum, item) => sum + item.predictedUsageWeekly, 0),
    totalRecommendedPurchase: categoryItems.reduce((sum, item) => sum + item.totalCostRecommended, 0),
    itemsNeedingReorder: categoryItems.filter(item => item.reorderQuantity > 0).length,
    criticalItems: categoryItems.filter(item => item.alertLevel === 'critical').length,
  };
}

function generatePurchaseRecommendations(items: InventoryForecastItem[]): PurchaseRecommendation[] {
  // Group items by supplier (mock suppliers)
  const suppliers = ['Fresh Foods Co.', 'Beverage Distributors', 'Restaurant Supplies Inc.'];
  
  return suppliers.map((supplier, index) => {
    const supplierItems = items.filter(item => {
      if (index === 0) return item.category === 'food' && item.reorderQuantity > 0;
      if (index === 1) return item.category === 'beverages' && item.reorderQuantity > 0;
      return item.category === 'supplies' && item.reorderQuantity > 0;
    });
    
    const totalOrderValue = supplierItems.reduce((sum, item) => sum + item.totalCostRecommended, 0);
    const criticalItems = supplierItems.filter(item => item.alertLevel === 'critical').length;
    
    let priority: 'high' | 'medium' | 'low';
    if (criticalItems > 0) priority = 'high';
    else if (totalOrderValue > 500) priority = 'medium';
    else priority = 'low';
    
    return {
      supplier,
      items: supplierItems.map(item => ({
        id: item.id,
        name: item.name,
        quantity: item.reorderQuantity,
        unit: item.unit,
        unitCost: item.unitCost,
        totalCost: item.totalCostRecommended,
      })),
      totalOrderValue,
      priority,
      recommendedOrderDate: format(addDays(new Date(), criticalItems > 0 ? 1 : 3), 'yyyy-MM-dd'),
    };
  }).filter(order => order.items.length > 0);
}

// Mock forecast data generator
export function getMockForecastData(): ForecastData[] {
  const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
  
  return days.map((day, index) => ({
    day,
    actualRevenue: 1800 + Math.random() * 600,
    projectedRevenue: 2000 + Math.random() * 800 + (index >= 4 ? 400 : 0), // Weekend boost
    customers: 80 + Math.random() * 40 + (index >= 4 ? 20 : 0),
    confidence: 85 + Math.random() * 10,
    tableOccupancy: 70 + Math.random() * 25,
    staffNeeded: 8 + Math.floor(Math.random() * 4),
    seasonalTrend: 5 + Math.random() * 10,
    historicalComparison: -2 + Math.random() * 8,
  }));
}
