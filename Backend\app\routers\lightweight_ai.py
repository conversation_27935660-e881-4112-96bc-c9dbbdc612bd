"""
Lightweight AI Router for RestroManage
Optimized for performance with minimal overhead
Uses functional AI service for basic conversational AI
"""

from fastapi import APIRouter, HTTPException, status, Depends, Query
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field
from app.services.functional_ai_service import (
    chat_with_ai,
    get_ai_status,
    reload_ai_configuration,
    get_cache_stats,
    clear_cache
)
from app.utils.logging_config import logger
from app.routers.auth import get_current_active_user
import uuid


router = APIRouter(prefix="/ai", tags=["AI Assistant"])


# Request/Response models
class ChatRequest(BaseModel):
    """Simple chat request model"""
    message: str = Field(..., min_length=1, max_length=2000, description="User message")
    context: Optional[Dict[str, Any]] = Field(default=None, description="Optional context")


class ChatResponse(BaseModel):
    """Chat response model"""
    success: bool
    response: str
    ai_enabled: bool
    cached: bool = False
    timestamp: str
    error: Optional[str] = None


class StatusResponse(BaseModel):
    """AI status response model"""
    ai_enabled: bool
    model: Optional[str] = None
    features: list[str] = []
    message: str


# Public endpoints (no authentication required for basic chat)
@router.get("/status", response_model=StatusResponse)
async def get_status():
    """Get AI service status (public endpoint)"""
    try:
        status_data = await get_ai_status()
        return StatusResponse(**status_data)
    except Exception as e:
        logger.error("Failed to get AI status", "LightweightAI", {"error": str(e)})
        return StatusResponse(
            ai_enabled=False,
            model=None,
            features=[],
            message="AI service unavailable"
        )


@router.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """
    Chat with AI assistant (public endpoint)
    Lightweight conversational AI without restaurant-specific context
    """
    try:
        # Generate unique request ID
        request_id = str(uuid.uuid4())
        
        logger.info("Processing AI chat request", "LightweightAI", {
            "request_id": request_id,
            "message_length": len(request.message)
        })
        
        # Call functional AI service
        response_data = await chat_with_ai(request.message, request_id)
        
        return ChatResponse(**response_data)
        
    except Exception as e:
        error_msg = f"Chat request failed: {str(e)}"
        logger.error("AI chat failed", "LightweightAI", {
            "error": str(e),
            "message_length": len(request.message)
        })
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=error_msg
        )


@router.get("/chat")
async def chat_get(
    query: str = Query(..., min_length=1, max_length=2000, description="User query")
):
    """
    Chat with AI via GET request (for simple integrations)
    """
    try:
        request_id = str(uuid.uuid4())
        
        logger.info("Processing AI chat GET request", "LightweightAI", {
            "request_id": request_id,
            "query_length": len(query)
        })
        
        response_data = await chat_with_ai(query, request_id)
        return response_data
        
    except Exception as e:
        error_msg = f"Chat GET request failed: {str(e)}"
        logger.error("AI chat GET failed", "LightweightAI", {
            "error": str(e),
            "query_length": len(query)
        })
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=error_msg
        )


# Admin endpoints (require authentication)
@router.post("/admin/reload")
async def reload_configuration(current_user = Depends(get_current_active_user)):
    """Reload AI configuration (admin only)"""
    try:
        result = await reload_ai_configuration()
        logger.info("AI configuration reloaded by admin", "LightweightAI", {
            "user_id": current_user.get("id", "unknown"),
            "success": result.get("success", False)
        })
        return result
    except Exception as e:
        logger.error("Failed to reload AI configuration", "LightweightAI", {
            "error": str(e),
            "user_id": current_user.get("id", "unknown")
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to reload configuration: {str(e)}"
        )


@router.get("/admin/cache/stats")
async def get_cache_statistics(current_user = Depends(get_current_active_user)):
    """Get cache statistics (admin only)"""
    try:
        stats = get_cache_stats()
        return {
            "success": True,
            "cache_stats": stats
        }
    except Exception as e:
        logger.error("Failed to get cache stats", "LightweightAI", {
            "error": str(e),
            "user_id": current_user.get("id", "unknown")
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get cache stats: {str(e)}"
        )


@router.post("/admin/cache/clear")
async def clear_response_cache(current_user = Depends(get_current_active_user)):
    """Clear response cache (admin only)"""
    try:
        result = clear_cache()
        logger.info("AI cache cleared by admin", "LightweightAI", {
            "user_id": current_user.get("id", "unknown")
        })
        return result
    except Exception as e:
        logger.error("Failed to clear cache", "LightweightAI", {
            "error": str(e),
            "user_id": current_user.get("id", "unknown")
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to clear cache: {str(e)}"
        )


# Health check endpoint
@router.get("/health")
async def health_check():
    """Health check for AI service"""
    try:
        status_data = await get_ai_status()
        cache_stats = get_cache_stats()
        
        return {
            "status": "healthy",
            "ai_enabled": status_data["ai_enabled"],
            "model": status_data.get("model"),
            "cache_size": cache_stats["cache_size"],
            "timestamp": "2024-01-01T00:00:00Z"  # In production, use actual timestamp
        }
    except Exception as e:
        logger.error("AI health check failed", "LightweightAI", {"error": str(e)})
        return {
            "status": "unhealthy",
            "ai_enabled": False,
            "error": str(e),
            "timestamp": "2024-01-01T00:00:00Z"
        }


# Backward compatibility endpoint
@router.post("/insights/custom-analysis")
async def custom_analysis_compat(
    query: str = Query(..., description="User query"),
    context: Optional[Dict[str, Any]] = None,
    current_user = Depends(get_current_active_user)
):
    """
    Backward compatibility endpoint for existing frontend
    Maps to the new lightweight chat functionality
    """
    try:
        request_id = str(uuid.uuid4())
        
        logger.info("Processing backward compatibility request", "LightweightAI", {
            "request_id": request_id,
            "query_length": len(query),
            "user_id": current_user.get("id", "unknown")
        })
        
        response_data = await chat_with_ai(query, request_id)
        
        # Format response to match old API
        return {
            "success": response_data["success"],
            "response": response_data["response"],
            "ai_enabled": response_data["ai_enabled"],
            "query": query
        }
        
    except Exception as e:
        logger.error("Backward compatibility request failed", "LightweightAI", {
            "error": str(e),
            "query_length": len(query),
            "user_id": current_user.get("id", "unknown")
        })
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process request: {str(e)}"
        )
