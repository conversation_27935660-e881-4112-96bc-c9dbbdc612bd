# Multi-stage build for React frontend
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files (package.json and package-lock.json/yarn.lock)
COPY frontend/package*.json ./

# Install ALL dependencies needed for the build process (including devDependencies)
# Removed --only=production so tsc is installed.
RUN npm ci --silent

# Copy source code (only frontend directory)
COPY frontend/ .

# Build the application
RUN npm run build

# Production stage with nginx
FROM nginx:alpine AS production

# Install curl for health checks (alpine package manager: apk)
RUN apk add --no-cache curl

# Copy custom nginx configuration
# Ensure this nginx.conf is correctly configured to serve your React static files
# and proxy requests to your backend.
COPY frontend/nginx.conf /etc/nginx/nginx.conf

# Copy built application from builder stage
# This directory (/app/dist) is where 'vite build' typically outputs your static files.
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy environment configuration script (e.g., for setting dynamic Nginx variables)
COPY frontend/docker-entrypoint.sh /docker-entrypoint.sh
RUN chmod +x /docker-entrypoint.sh

# Create nginx user and set permissions for security best practices
# This ensures Nginx runs as a non-root user and has access to necessary directories.
RUN addgroup -g 1001 -S nginx && \
    adduser -S -D -H -u 1001 -h /var/cache/nginx -s /sbin/nologin -G nginx -g nginx nginx && \
    chown -R nginx:nginx /usr/share/nginx/html /var/cache/nginx /var/log/nginx /etc/nginx/conf.d

# Switch to non-root user for enhanced security
USER nginx

# Expose port 80 (standard HTTP port for Nginx)
# Railway will handle external mapping based on its own PORT variable.
EXPOSE 80

# Health check (Nginx typically serves on / by default)
# Changed to check the root path '/' unless you have a specific /health endpoint in Nginx config.
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/ || exit 1

# Use your custom entrypoint script
ENTRYPOINT ["/docker-entrypoint.sh"]
# Default command for nginx, which will be executed by your entrypoint script
CMD ["nginx", "-g", "daemon off;"]