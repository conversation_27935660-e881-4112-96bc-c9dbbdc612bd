// Inventory Forecast Types for RestroManage-v0
import { ForecastData } from "@/components/dashboard/ForecastCard";

export interface InventoryForecastItem {
  id: string;
  name: string;
  category: 'food' | 'beverages' | 'supplies';
  currentStock: number;
  unit: string;
  
  // Forecast data
  predictedUsage: number;
  predictedUsageWeekly: number;
  recommendedStock: number;
  reorderQuantity: number;
  
  // Alert levels
  alertLevel: 'sufficient' | 'reorder-soon' | 'critical';
  daysUntilStockout: number;
  
  // Cost analysis
  unitCost: number;
  totalCostRecommended: number;
  
  // Confidence metrics
  forecastConfidence: number; // 0-100%
  historicalAccuracy: number; // 0-100%
}

export interface WeeklyInventoryForecast {
  weekStartDate: string;
  totalPredictedRevenue: number;
  totalPredictedCustomers: number;
  
  // Category summaries
  categoryForecasts: {
    food: CategoryForecast;
    beverages: CategoryForecast;
    supplies: CategoryForecast;
  };
  
  // Individual items
  items: InventoryForecastItem[];
  
  // Purchase recommendations
  recommendedOrders: PurchaseRecommendation[];
}

export interface CategoryForecast {
  category: 'food' | 'beverages' | 'supplies';
  totalCurrentValue: number;
  totalPredictedUsage: number;
  totalRecommendedPurchase: number;
  itemsNeedingReorder: number;
  criticalItems: number;
}

export interface PurchaseRecommendation {
  supplier: string;
  items: {
    id: string;
    name: string;
    quantity: number;
    unit: string;
    unitCost: number;
    totalCost: number;
  }[];
  totalOrderValue: number;
  priority: 'high' | 'medium' | 'low';
  recommendedOrderDate: string;
}

export interface InventoryConsumptionPattern {
  itemId: string;
  averageDailyUsage: number;
  peakDayUsage: number;
  seasonalMultiplier: number;
  revenueCorrelation: number; // How much usage correlates with revenue
}
