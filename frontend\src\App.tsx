
import React from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter } from "react-router-dom";
import { ThemeProvider } from "next-themes";
import { AuthProvider } from "@/contexts/AuthContext";
import { StaffPINProvider } from "@/contexts/StaffPINContext";
import { RestaurantProvider } from "@/contexts/RestaurantContext";
import { NotificationProvider } from "@/contexts/NotificationContext";
import AppRouter from "@/components/AppRouter";


const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes (was cacheTime)
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      refetchOnReconnect: false,
      retry: 1
    },
    mutations: {
      retry: 1
    }
  }
});

const App = () => (
  <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <RestaurantProvider>
          <StaffPINProvider>
            <NotificationProvider>
              <TooltipProvider>
                <Toaster />
                <Sonner />
                <BrowserRouter>
                  <AppRouter />
                </BrowserRouter>
              </TooltipProvider>
            </NotificationProvider>
          </StaffPINProvider>
        </RestaurantProvider>
      </AuthProvider>
    </QueryClientProvider>
  </ThemeProvider>
);

export default App;