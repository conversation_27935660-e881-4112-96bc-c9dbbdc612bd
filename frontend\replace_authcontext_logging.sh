#!/bin/bash

echo "Replacing console statements in AuthContext.tsx with logger calls..."

# Replace common AuthContext console patterns
sed -i "s/console\.log('\[AuthContext\] /logger.debug('/g" src/contexts/AuthContext.tsx
sed -i "s/console\.warn('\[AuthContext\] /logger.warn('/g" src/contexts/AuthContext.tsx
sed -i "s/console\.error('\[AuthContext\] /logger.error('/g" src/contexts/AuthContext.tsx
sed -i "s/console\.info('\[AuthContext\] /logger.info('/g" src/contexts/AuthContext.tsx

# Replace specific authentication-related logs
sed -i "s/logger\.debug('Login called with email:', email);/logger.authentication('login attempt', 'started', 'AuthContext', { email });/" src/contexts/AuthContext.tsx
sed -i "s/logger\.debug('Login validation successful');/logger.authentication('login', 'success', 'AuthContext');/" src/contexts/AuthContext.tsx
sed -i "s/logger\.debug('Login validation failed');/logger.authentication('login', 'failure', 'AuthContext');/" src/contexts/AuthContext.tsx

# Replace restaurant login patterns
sed -i "s/logger\.debug('Login restaurant called with code:', code);/logger.authentication('restaurant login attempt', 'started', 'AuthContext', { code });/" src/contexts/AuthContext.tsx
sed -i "s/logger\.debug('Restaurant not found');/logger.authentication('restaurant login', 'failure', 'AuthContext', { reason: 'restaurant not found' });/" src/contexts/AuthContext.tsx

# Replace PIN login patterns
sed -i "s/logger\.debug('Login with PIN called:', pin);/logger.authentication('PIN login attempt', 'started', 'AuthContext', { pin: '[REDACTED]' });/" src/contexts/AuthContext.tsx

# Replace data loading patterns
sed -i "s/logger\.debug('Using mock data directly instead of trying to load CSV files');/logger.info('Initializing with mock data', 'AuthContext');/" src/contexts/AuthContext.tsx
sed -i "s/logger\.debug('Using mock restaurant data:', MOCK_RESTAURANTS);/logger.dataOperation('load', 'mock restaurants', 'AuthContext', { count: MOCK_RESTAURANTS.length });/" src/contexts/AuthContext.tsx
sed -i "s/logger\.debug('Setting mock users:', MOCK_USERS\.length);/logger.dataOperation('load', 'mock users', 'AuthContext', { count: MOCK_USERS.length });/" src/contexts/AuthContext.tsx

# Replace session management patterns
sed -i "s/logger\.debug('Checking for existing session\.\.\.');/logger.info('Checking for existing session', 'AuthContext');/" src/contexts/AuthContext.tsx
sed -i "s/logger\.debug('Stored data:', {/logger.debug('Stored session data', 'AuthContext', {/" src/contexts/AuthContext.tsx

# Add component context to remaining logger calls
sed -i "s/logger\.debug('/logger.debug('/g" src/contexts/AuthContext.tsx
sed -i "s/logger\.info('/logger.info('/g" src/contexts/AuthContext.tsx
sed -i "s/logger\.warn('/logger.warn('/g" src/contexts/AuthContext.tsx
sed -i "s/logger\.error('/logger.error('/g" src/contexts/AuthContext.tsx

# Add AuthContext component parameter to logger calls that don't have it
sed -i "s/logger\.debug(\([^,]*\));/logger.debug(\1, 'AuthContext');/g" src/contexts/AuthContext.tsx
sed -i "s/logger\.info(\([^,]*\));/logger.info(\1, 'AuthContext');/g" src/contexts/AuthContext.tsx
sed -i "s/logger\.warn(\([^,]*\));/logger.warn(\1, 'AuthContext');/g" src/contexts/AuthContext.tsx
sed -i "s/logger\.error(\([^,]*\));/logger.error(\1, 'AuthContext');/g" src/contexts/AuthContext.tsx

echo "AuthContext logging replacement completed"
