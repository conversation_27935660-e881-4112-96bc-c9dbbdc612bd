import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, Di<PERSON>T<PERSON>le, DialogFooter } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { toast } from '@/components/ui/sonner';
import { <PERSON>rk<PERSON>, Brain, TrendingUp, Target, Lightbulb, Plus, RefreshCw } from 'lucide-react';

interface PromoSuggestion {
  code: string;
  name: string;
  description: string;
  discount_type: 'percentage' | 'fixed_amount';
  discount_value: number;
  minimum_spend?: number;
  target_audience: string;
  reasoning: string;
  confidence: 'low' | 'medium' | 'high';
  expected_impact: string;
}

interface BusinessContext {
  type: string;
  avg_order_value: number;
  peak_hours: string;
  cuisine_type?: string;
  location_type?: string;
}

const AIPromoSuggestions: React.FC = () => {
  const [suggestions, setSuggestions] = useState<PromoSuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedSuggestion, setSelectedSuggestion] = useState<PromoSuggestion | null>(null);
  const [aiEnabled, setAiEnabled] = useState(false);
  const [businessContext, setBusinessContext] = useState<BusinessContext>({
    type: 'casual dining',
    avg_order_value: 25.0,
    peak_hours: '12-2pm, 6-9pm',
    cuisine_type: 'international',
    location_type: 'urban'
  });

  useEffect(() => {
    checkAIStatus();
  }, []);

  const checkAIStatus = async () => {
    try {
      const response = await fetch('/api/ai/status');
      if (response.ok) {
        const data = await response.json();
        setAiEnabled(data.ai_enabled);
      }
    } catch (error) {
      console.error('Failed to check AI status:', error);
    }
  };

  const generateSuggestions = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/ai/promo-suggestions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          business_context: businessContext,
          target_audience: 'general',
          campaign_goal: 'increase_sales'
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setSuggestions(data.suggestions || []);
        toast.success(`Generated ${data.suggestions?.length || 0} AI-powered promo suggestions!`);
      } else {
        toast.error('Failed to generate suggestions');
      }
    } catch (error) {
      console.error('Error generating suggestions:', error);
      toast.error('Failed to generate suggestions');
    } finally {
      setIsLoading(false);
    }
  };

  const createPromoCode = async (suggestion: PromoSuggestion) => {
    try {
      const promoData = {
        code: suggestion.code,
        name: suggestion.name,
        description: suggestion.description,
        discount_type: suggestion.discount_type,
        discount_value: suggestion.discount_value,
        scope: 'order_total',
        minimum_spend: suggestion.minimum_spend,
        start_date: new Date().toISOString(),
        end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
        usage_limit: 100,
        is_active: true
      };

      const response = await fetch('/api/discounts/promo-codes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(promoData),
      });

      if (response.ok) {
        toast.success('Promo code created successfully!');
        setIsDialogOpen(false);
        setSelectedSuggestion(null);
      } else {
        const error = await response.json();
        toast.error(error.detail || 'Failed to create promo code');
      }
    } catch (error) {
      console.error('Error creating promo code:', error);
      toast.error('Failed to create promo code');
    }
  };

  const getConfidenceColor = (confidence: string) => {
    switch (confidence) {
      case 'high':
        return 'bg-green-100 text-green-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getImpactIcon = (impact: string) => {
    if (impact.toLowerCase().includes('high')) return <TrendingUp className="w-4 h-4 text-green-600" />;
    if (impact.toLowerCase().includes('medium')) return <Target className="w-4 h-4 text-yellow-600" />;
    return <Lightbulb className="w-4 h-4 text-blue-600" />;
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Brain className="w-5 h-5 text-purple-600" />
            AI Promo Suggestions
          </h3>
          <p className="text-sm text-muted-foreground">
            Get AI-powered promo code suggestions based on your business data
          </p>
        </div>
        <div className="flex items-center gap-2">
          {!aiEnabled && (
            <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
              AI Disabled
            </Badge>
          )}
          <Button
            onClick={generateSuggestions}
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            {isLoading ? (
              <RefreshCw className="w-4 h-4 animate-spin" />
            ) : (
              <Sparkles className="w-4 h-4" />
            )}
            {isLoading ? 'Generating...' : 'Generate Suggestions'}
          </Button>
        </div>
      </div>

      {!aiEnabled && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-3">
              <Brain className="w-8 h-8 text-yellow-600" />
              <div>
                <h4 className="font-medium text-yellow-800">AI Features Not Available</h4>
                <p className="text-sm text-yellow-700">
                  Configure your Google AI API key to enable AI-powered promo suggestions.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {suggestions.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {suggestions.map((suggestion, index) => (
            <Card key={index} className="hover:shadow-md transition-shadow cursor-pointer">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <Badge variant="outline" className="font-mono text-xs">
                    {suggestion.code}
                  </Badge>
                  <Badge className={getConfidenceColor(suggestion.confidence)}>
                    {suggestion.confidence} confidence
                  </Badge>
                </div>
                <CardTitle className="text-base">{suggestion.name}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <p className="text-sm text-muted-foreground">
                  {suggestion.description}
                </p>
                
                <div className="flex items-center justify-between">
                  <div className="text-lg font-bold text-green-600">
                    {suggestion.discount_type === 'percentage' 
                      ? `${suggestion.discount_value}% OFF`
                      : `£${suggestion.discount_value} OFF`
                    }
                  </div>
                  {getImpactIcon(suggestion.expected_impact)}
                </div>

                {suggestion.minimum_spend && (
                  <p className="text-xs text-muted-foreground">
                    Min spend: £{suggestion.minimum_spend}
                  </p>
                )}

                <div className="space-y-2">
                  <div className="text-xs">
                    <span className="font-medium">Target:</span> {suggestion.target_audience}
                  </div>
                  <div className="text-xs">
                    <span className="font-medium">Impact:</span> {suggestion.expected_impact}
                  </div>
                </div>

                <Button
                  onClick={() => {
                    setSelectedSuggestion(suggestion);
                    setIsDialogOpen(true);
                  }}
                  className="w-full"
                  size="sm"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Create Promo Code
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {suggestions.length === 0 && !isLoading && aiEnabled && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <Sparkles className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
              <h4 className="font-medium mb-2">No Suggestions Yet</h4>
              <p className="text-sm text-muted-foreground mb-4">
                Click "Generate Suggestions" to get AI-powered promo code ideas
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Create Promo Code Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Create Promo Code from AI Suggestion</DialogTitle>
          </DialogHeader>

          {selectedSuggestion && (
            <div className="space-y-4">
              <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                <h4 className="font-medium text-blue-900 mb-2">AI Reasoning</h4>
                <p className="text-sm text-blue-800">{selectedSuggestion.reasoning}</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Promo Code</Label>
                  <Input value={selectedSuggestion.code} readOnly className="font-mono" />
                </div>
                <div>
                  <Label>Name</Label>
                  <Input value={selectedSuggestion.name} readOnly />
                </div>
              </div>

              <div>
                <Label>Description</Label>
                <Textarea value={selectedSuggestion.description} readOnly rows={2} />
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label>Discount Type</Label>
                  <Input 
                    value={selectedSuggestion.discount_type === 'percentage' ? 'Percentage' : 'Fixed Amount'} 
                    readOnly 
                  />
                </div>
                <div>
                  <Label>Discount Value</Label>
                  <Input 
                    value={`${selectedSuggestion.discount_value}${selectedSuggestion.discount_type === 'percentage' ? '%' : ' £'}`} 
                    readOnly 
                  />
                </div>
                <div>
                  <Label>Minimum Spend</Label>
                  <Input 
                    value={selectedSuggestion.minimum_spend ? `£${selectedSuggestion.minimum_spend}` : 'None'} 
                    readOnly 
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Target Audience</Label>
                  <Input value={selectedSuggestion.target_audience} readOnly />
                </div>
                <div>
                  <Label>Expected Impact</Label>
                  <Input value={selectedSuggestion.expected_impact} readOnly />
                </div>
              </div>

              <div className="p-3 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-600">
                  <strong>Note:</strong> This promo code will be created with a 30-day validity period and a usage limit of 100. 
                  You can modify these settings after creation.
                </p>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={() => selectedSuggestion && createPromoCode(selectedSuggestion)}>
              Create Promo Code
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AIPromoSuggestions;
