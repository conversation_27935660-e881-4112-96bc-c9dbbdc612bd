import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { toast } from '@/components/ui/sonner';
import { Plus, Edit, Trash2, CreditCard, Tag, Shield } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { getAuthHeaders, isManagerOrAdmin } from '@/utils/authUtils';
import PromoCodeManagement from './PromoCodeManagement';

interface PromoCode {
  id: string;
  code: string;
  name: string;
  description?: string;
  discount_type: 'percentage' | 'fixed_amount';
  discount_value: number;
  scope: string;
  minimum_spend?: number;
  maximum_discount?: number;
  start_date: string;
  end_date: string;
  usage_limit?: number;
  usage_count: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface GiftCard {
  id: string;
  code: string;
  amount: number;
  balance: number;
  status: 'active' | 'used' | 'deactivated';
  created_at: string;
  used_at?: string;
  used_by?: string;
}

const AdminDiscountManagement: React.FC = () => {
  const { user } = useAuth();
  const [promoCodes, setPromoCodes] = useState<PromoCode[]>([]);
  const [giftCards, setGiftCards] = useState<GiftCard[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showGiftCardForm, setShowGiftCardForm] = useState(false);
  const [giftCardAmount, setGiftCardAmount] = useState(0);

  // Check if user has admin/manager permissions
  if (!isManagerOrAdmin(user)) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-8">
          <div className="text-center">
            <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Access Restricted</h3>
            <p className="text-gray-500">
              You need manager or admin privileges to access discount management.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    setIsLoading(true);
    try {
      await Promise.all([fetchPromoCodes(), fetchGiftCards()]);
    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('Failed to load discount data');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchPromoCodes = async () => {
    try {
      const response = await fetch('/api/discounts/promo-codes', {
        headers: getAuthHeaders(user)
      });
      if (response.ok) {
        const codes = await response.json();
        setPromoCodes(codes);
      } else if (response.status === 401) {
        toast.error('Authentication required');
      }
    } catch (error) {
      console.error('Error fetching promo codes:', error);
    }
  };

  const fetchGiftCards = async () => {
    try {
      const response = await fetch('http://localhost:5001/api/discounts/gift-cards', {
        headers: getAuthHeaders(user)
      });
      if (response.ok) {
        const cards = await response.json();
        setGiftCards(cards);
      } else if (response.status === 401) {
        toast.error('Authentication required');
        // Set fallback gift cards for demo purposes
        setGiftCards(getFallbackGiftCards());
      } else {
        // Set fallback gift cards for demo purposes
        setGiftCards(getFallbackGiftCards());
      }
    } catch (error) {
      console.error('Error fetching gift cards:', error);
      // Set fallback gift cards for demo purposes
      setGiftCards(getFallbackGiftCards());
    }
  };

  const getFallbackGiftCards = (): GiftCard[] => {
    return [
      {
        id: 'fallback-gc-1',
        code: 'GC-ABC123',
        amount: 50.00,
        balance: 50.00,
        status: 'active',
        created_at: new Date().toISOString(),
        used_at: undefined,
        used_by: undefined
      },
      {
        id: 'fallback-gc-2',
        code: 'GC-XYZ789',
        amount: 25.00,
        balance: 0.00,
        status: 'used',
        created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        used_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        used_by: '<EMAIL>'
      },
      {
        id: 'fallback-gc-3',
        code: 'GC-DEF456',
        amount: 100.00,
        balance: 75.50,
        status: 'active',
        created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
        used_at: undefined,
        used_by: undefined
      }
    ];
  };

  const generateGiftCard = async () => {
    if (!giftCardAmount || giftCardAmount <= 0) {
      toast.error('Please enter a valid amount');
      return;
    }

    try {
      const response = await fetch('http://localhost:5001/api/discounts/gift-cards', {
        method: 'POST',
        headers: getAuthHeaders(user),
        body: JSON.stringify({ amount: giftCardAmount })
      });

      if (response.ok) {
        const newCard = await response.json();
        setGiftCards(prev => [...prev, newCard]);
        setShowGiftCardForm(false);
        setGiftCardAmount(0);
        toast.success(`Gift card ${newCard.code} generated successfully!`);
      } else if (response.status === 401) {
        toast.error('Authentication required');
      } else {
        toast.error('Failed to generate gift card');
      }
    } catch (error) {
      console.error('Error generating gift card:', error);
      toast.error('Failed to generate gift card');
    }
  };

  const deactivateGiftCard = async (cardId: string) => {
    try {
      const response = await fetch(`http://localhost:5001/api/discounts/gift-cards/${cardId}/deactivate`, {
        method: 'POST',
        headers: getAuthHeaders(user)
      });

      if (response.ok) {
        setGiftCards(prev => prev.map(card => 
          card.id === cardId 
            ? { ...card, status: 'deactivated' as const, used_at: new Date().toISOString() }
            : card
        ));
        toast.success('Gift card deactivated successfully');
      } else if (response.status === 401) {
        toast.error('Authentication required');
      } else {
        toast.error('Failed to deactivate gift card');
      }
    } catch (error) {
      console.error('Error deactivating gift card:', error);
      toast.error('Failed to deactivate gift card');
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p>Loading discount management...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Discount Management</h2>
          <p className="text-muted-foreground">
            Manage promo codes, gift cards, and discount configurations
          </p>
        </div>
        <Badge variant="secondary" className="flex items-center gap-1">
          <Shield className="h-3 w-3" />
          Admin Access
        </Badge>
      </div>

      <Tabs defaultValue="promo-codes" className="space-y-4">
        <TabsList>
          <TabsTrigger value="promo-codes" className="flex items-center gap-2">
            <Tag className="h-4 w-4" />
            Promo Codes
          </TabsTrigger>
          <TabsTrigger value="gift-cards" className="flex items-center gap-2">
            <CreditCard className="h-4 w-4" />
            Gift Cards
          </TabsTrigger>
        </TabsList>

        <TabsContent value="promo-codes">
          <PromoCodeManagement />
        </TabsContent>

        <TabsContent value="gift-cards">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Gift Card Management</CardTitle>
                  <p className="text-sm text-muted-foreground">
                    Generate and manage gift cards for your restaurant
                  </p>
                </div>
                <Button onClick={() => setShowGiftCardForm(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Generate Gift Card
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {giftCards.map((card) => (
                  <div key={card.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-2">
                          <CreditCard className="h-4 w-4 text-muted-foreground" />
                          <code className="bg-muted px-2 py-1 rounded text-sm font-mono">
                            {card.code}
                          </code>
                        </div>
                        <div>
                          <h4 className="font-medium">£{card.amount.toFixed(2)} Gift Card</h4>
                          <p className="text-sm text-muted-foreground">
                            Balance: £{card.balance.toFixed(2)}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant={
                          card.status === "active" ? "default" : 
                          card.status === "used" ? "secondary" : "destructive"
                        }>
                          {card.status === "active" ? "Active" : 
                           card.status === "used" ? "Used" : "Deactivated"}
                        </Badge>
                        {card.status === "active" && (
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => deactivateGiftCard(card.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                    <div className="mt-3 grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Created:</span>
                        <div className="font-medium">
                          {new Date(card.created_at).toLocaleDateString()}
                        </div>
                      </div>
                      {card.used_at && (
                        <div>
                          <span className="text-muted-foreground">Used:</span>
                          <div className="font-medium">
                            {new Date(card.used_at).toLocaleDateString()}
                          </div>
                        </div>
                      )}
                      {card.used_by && (
                        <div>
                          <span className="text-muted-foreground">Used by:</span>
                          <div className="font-medium">{card.used_by}</div>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Gift Card Generation Dialog */}
      <Dialog open={showGiftCardForm} onOpenChange={setShowGiftCardForm}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Generate New Gift Card</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="amount">Gift Card Amount (£)</Label>
              <Input
                id="amount"
                type="number"
                min="1"
                step="0.01"
                value={giftCardAmount || ''}
                onChange={(e) => setGiftCardAmount(parseFloat(e.target.value) || 0)}
                placeholder="Enter amount..."
              />
            </div>
            <div className="p-3 bg-blue-50 rounded-md border border-blue-200">
              <p className="text-sm text-blue-800">
                <strong>Note:</strong> Gift cards will be generated with a unique code and can be used for future orders.
                The gift card will be automatically deactivated after use.
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowGiftCardForm(false)}>
              Cancel
            </Button>
            <Button onClick={generateGiftCard} disabled={!giftCardAmount || giftCardAmount <= 0}>
              Generate Gift Card
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdminDiscountManagement;
