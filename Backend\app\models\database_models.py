"""
SQLAlchemy database models for RestroManage.
These models define the database schema and relationships.
"""

from sqlalchemy import Column, String, Integer, Float, Boolean, DateTime, Text, JSON, ForeignKey, Index, Enum
from sqlalchemy.orm import relationship, declarative_base
from sqlalchemy.sql import func
from datetime import datetime
import uuid
import enum

from app.database import Base

class Restaurant(Base):
    """Restaurant model - main tenant entity"""
    __tablename__ = "restaurants"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    code = Column(String(10), unique=True, nullable=False, index=True)
    name = Column(String(255), nullable=False)
    logo = Column(String(500))
    address = Column(Text, nullable=False)
    phone = Column(String(50), nullable=False)
    email = Column(String(255), nullable=False)
    vat_rate = Column(Float, default=20.0)
    currency = Column(String(3), default="GBP")
    is_active = Column(Boolean, default=True)
    owner_name = Column(String(255), nullable=False)
    business_license_number = Column(String(100), nullable=False)
    restaurant_type = Column(String(50), default="restaurant")
    password = Column(String(255), nullable=False)  # Hashed password
    setup_data = Column(JSON)
    has_data = Column(Boolean, default=False)  # Indicates if restaurant has operational data
    
    # Timestamps
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationships
    users = relationship("RestaurantUser", back_populates="restaurant", cascade="all, delete-orphan")
    menu_items = relationship("MenuItem", back_populates="restaurant", cascade="all, delete-orphan")
    tables = relationship("Table", back_populates="restaurant", cascade="all, delete-orphan")
    orders = relationship("Order", back_populates="restaurant", cascade="all, delete-orphan")
    inventory_items = relationship("InventoryItem", back_populates="restaurant", cascade="all, delete-orphan")
    promo_codes = relationship("PromoCode", back_populates="restaurant", cascade="all, delete-orphan")
    campaigns = relationship("Campaign", back_populates="restaurant", cascade="all, delete-orphan")
    split_bills = relationship("SplitBill", back_populates="restaurant", cascade="all, delete-orphan")
    gift_cards = relationship("GiftCard", back_populates="restaurant", cascade="all, delete-orphan")
    notifications = relationship("Notification", back_populates="restaurant", cascade="all, delete-orphan")
    
    # Indexes
    __table_args__ = (
        Index('idx_restaurant_code', 'code'),
        Index('idx_restaurant_active', 'is_active'),
    )

class RestaurantUser(Base):
    """Restaurant user/staff model"""
    __tablename__ = "restaurant_users"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    restaurant_id = Column(String, ForeignKey("restaurants.id"), nullable=False)
    name = Column(String(255), nullable=False)
    email = Column(String(255), nullable=False)
    phone = Column(String(50))
    role = Column(String(50), nullable=False)  # admin, manager, waiter, chef, etc.
    position = Column(String(100))
    pin = Column(String(10), nullable=False)  # 4-digit PIN for quick access
    status = Column(String(20), default="active")  # active, inactive, on-leave
    hire_date = Column(String(20))  # Keep as string for compatibility
    performance = Column(Integer, default=100)
    access_level = Column(String(20), default="limited")  # full, limited
    
    # Timestamps
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationships
    restaurant = relationship("Restaurant", back_populates="users")
    
    # Indexes
    __table_args__ = (
        Index('idx_user_restaurant', 'restaurant_id'),
        Index('idx_user_pin', 'restaurant_id', 'pin'),
        Index('idx_user_status', 'status'),
    )

class MenuItem(Base):
    """Menu item model"""
    __tablename__ = "menu_items"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    restaurant_id = Column(String, ForeignKey("restaurants.id"), nullable=False)
    name = Column(String(255), nullable=False)
    price = Column(Float, nullable=False)
    category = Column(String(100), nullable=False)
    description = Column(Text)
    image_url = Column(String(500))
    available = Column(Boolean, default=True)
    ingredients = Column(JSON)  # List of ingredients
    allergens = Column(JSON)    # List of allergens
    
    # Timestamps
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationships
    restaurant = relationship("Restaurant", back_populates="menu_items")
    order_items = relationship("OrderItem", back_populates="menu_item")
    
    # Indexes
    __table_args__ = (
        Index('idx_menu_restaurant', 'restaurant_id'),
        Index('idx_menu_category', 'category'),
        Index('idx_menu_available', 'available'),
    )

class Table(Base):
    """Table model"""
    __tablename__ = "tables"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    restaurant_id = Column(String, ForeignKey("restaurants.id"), nullable=False)
    number = Column(Integer, nullable=False)
    capacity = Column(Integer, nullable=False)
    location = Column(String(100))
    status = Column(String(20), default="available")  # available, occupied, reserved, maintenance
    current_order_id = Column(String, ForeignKey("orders.id"))
    
    # Timestamps
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationships
    restaurant = relationship("Restaurant", back_populates="tables")
    orders = relationship("Order", back_populates="table", foreign_keys="Order.table_id")
    current_order = relationship("Order", foreign_keys=[current_order_id])
    
    # Indexes
    __table_args__ = (
        Index('idx_table_restaurant', 'restaurant_id'),
        Index('idx_table_number', 'restaurant_id', 'number'),
        Index('idx_table_status', 'status'),
    )

class Order(Base):
    """Order model"""
    __tablename__ = "orders"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    restaurant_id = Column(String, ForeignKey("restaurants.id"), nullable=False)
    table_id = Column(String, ForeignKey("tables.id"))
    customer_name = Column(String(255))
    special_instructions = Column(Text)
    status = Column(String(20), default="pending")  # pending, preparing, ready, completed, cancelled
    total = Column(Float, nullable=False, default=0.0)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    completed_at = Column(DateTime)
    
    # Relationships
    restaurant = relationship("Restaurant", back_populates="orders")
    table = relationship("Table", back_populates="orders", foreign_keys=[table_id])
    items = relationship("OrderItem", back_populates="order", cascade="all, delete-orphan")
    
    # Indexes
    __table_args__ = (
        Index('idx_order_restaurant', 'restaurant_id'),
        Index('idx_order_table', 'table_id'),
        Index('idx_order_status', 'status'),
        Index('idx_order_created', 'created_at'),
    )

class OrderItem(Base):
    """Order item model"""
    __tablename__ = "order_items"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    order_id = Column(String, ForeignKey("orders.id"), nullable=False)
    menu_item_id = Column(String, ForeignKey("menu_items.id"), nullable=False)
    name = Column(String(255), nullable=False)  # Snapshot of menu item name
    quantity = Column(Integer, nullable=False, default=1)
    price = Column(Float, nullable=False)  # Snapshot of menu item price
    special_instructions = Column(Text)
    subtotal = Column(Float, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationships
    order = relationship("Order", back_populates="items")
    menu_item = relationship("MenuItem", back_populates="order_items")
    
    # Indexes
    __table_args__ = (
        Index('idx_order_item_order', 'order_id'),
        Index('idx_order_item_menu', 'menu_item_id'),
    )

class InventoryItem(Base):
    """Inventory item model"""
    __tablename__ = "inventory_items"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    restaurant_id = Column(String, ForeignKey("restaurants.id"), nullable=False)
    name = Column(String(255), nullable=False)
    quantity = Column(Float, nullable=False, default=0.0)
    unit = Column(String(50), nullable=False)  # kg, liters, pieces, etc.
    reorder_level = Column(Float, default=0.0)
    price_per_unit = Column(Float, nullable=False)
    category = Column(String(100))
    supplier = Column(String(255))
    allergens = Column(JSON)  # List of allergens
    last_restocked = Column(DateTime)

    # Timestamps
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)

    # Relationships
    restaurant = relationship("Restaurant", back_populates="inventory_items")

    # Indexes
    __table_args__ = (
        Index('idx_inventory_restaurant', 'restaurant_id'),
        Index('idx_inventory_category', 'category'),
        Index('idx_inventory_reorder', 'reorder_level'),
    )

class PromoCode(Base):
    """Promotional code model"""
    __tablename__ = "promo_codes"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    restaurant_id = Column(String, ForeignKey("restaurants.id"), nullable=False)
    code = Column(String(50), nullable=False)
    description = Column(Text)
    discount_type = Column(String(20), nullable=False)  # percentage, fixed_amount
    discount_value = Column(Float, nullable=False)
    min_order_amount = Column(Float, default=0.0)
    max_discount_amount = Column(Float)
    usage_limit = Column(Integer)
    usage_count = Column(Integer, default=0)
    is_active = Column(Boolean, default=True)
    valid_from = Column(DateTime)
    valid_until = Column(DateTime)

    # Timestamps
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)

    # Relationships
    restaurant = relationship("Restaurant", back_populates="promo_codes")
    usage_records = relationship("PromoCodeUsage", back_populates="promo_code", cascade="all, delete-orphan")

    # Indexes
    __table_args__ = (
        Index('idx_promo_restaurant', 'restaurant_id'),
        Index('idx_promo_code', 'code'),
        Index('idx_promo_active', 'is_active'),
    )

class PromoCodeUsage(Base):
    """Promotional code usage tracking"""
    __tablename__ = "promo_code_usage"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    promo_code_id = Column(String, ForeignKey("promo_codes.id"), nullable=False)
    order_id = Column(String)  # Reference to order where used
    customer_identifier = Column(String(255))  # Email, phone, or customer ID
    discount_amount = Column(Float, nullable=False)

    # Timestamps
    created_at = Column(DateTime, default=func.now(), nullable=False)

    # Relationships
    promo_code = relationship("PromoCode", back_populates="usage_records")

    # Indexes
    __table_args__ = (
        Index('idx_promo_usage_code', 'promo_code_id'),
        Index('idx_promo_usage_order', 'order_id'),
    )

class Campaign(Base):
    """Marketing campaign model"""
    __tablename__ = "campaigns"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    restaurant_id = Column(String, ForeignKey("restaurants.id"), nullable=False)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    campaign_type = Column(String(50), nullable=False)  # email, sms, social_media
    status = Column(String(20), default="draft")  # draft, active, paused, completed
    target_audience = Column(JSON)  # Targeting criteria
    content = Column(JSON)  # Campaign content and settings
    metrics = Column(JSON)  # Campaign performance metrics
    start_date = Column(DateTime)
    end_date = Column(DateTime)

    # Timestamps
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)

    # Relationships
    restaurant = relationship("Restaurant", back_populates="campaigns")

    # Indexes
    __table_args__ = (
        Index('idx_campaign_restaurant', 'restaurant_id'),
        Index('idx_campaign_status', 'status'),
        Index('idx_campaign_type', 'campaign_type'),
    )

class SplitBill(Base):
    """Split bill model for group orders"""
    __tablename__ = "split_bills"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    restaurant_id = Column(String, ForeignKey("restaurants.id"), nullable=False)
    order_id = Column(String, nullable=False)  # Reference to original order
    total_amount = Column(Float, nullable=False)
    split_type = Column(String(20), nullable=False)  # equal, custom, by_item
    participants = Column(JSON, nullable=False)  # List of participants and their shares
    payment_status = Column(String(20), default="pending")  # pending, partial, completed
    payments = Column(JSON)  # Payment tracking

    # Timestamps
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)

    # Relationships
    restaurant = relationship("Restaurant", back_populates="split_bills")

    # Indexes
    __table_args__ = (
        Index('idx_split_restaurant', 'restaurant_id'),
        Index('idx_split_order', 'order_id'),
        Index('idx_split_status', 'payment_status'),
    )

class GiftCard(Base):
    """Gift card model"""
    __tablename__ = "gift_cards"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    restaurant_id = Column(String, ForeignKey("restaurants.id"), nullable=False)
    code = Column(String(50), unique=True, nullable=False)
    initial_amount = Column(Float, nullable=False)
    current_balance = Column(Float, nullable=False)
    status = Column(String(20), default="active")  # active, used, expired, cancelled
    purchaser_email = Column(String(255))
    recipient_email = Column(String(255))
    message = Column(Text)
    expiry_date = Column(DateTime)

    # Timestamps
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)

    # Relationships
    restaurant = relationship("Restaurant", back_populates="gift_cards")
    transactions = relationship("GiftCardTransaction", back_populates="gift_card", cascade="all, delete-orphan")

    # Indexes
    __table_args__ = (
        Index('idx_gift_card_restaurant', 'restaurant_id'),
        Index('idx_gift_card_code', 'code'),
        Index('idx_gift_card_status', 'status'),
    )

class GiftCardTransaction(Base):
    """Gift card transaction history"""
    __tablename__ = "gift_card_transactions"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    gift_card_id = Column(String, ForeignKey("gift_cards.id"), nullable=False)
    transaction_type = Column(String(20), nullable=False)  # purchase, redemption, refund
    amount = Column(Float, nullable=False)
    order_id = Column(String)  # Reference to order if used for payment
    description = Column(Text)

    # Timestamps
    created_at = Column(DateTime, default=func.now(), nullable=False)

    # Relationships
    gift_card = relationship("GiftCard", back_populates="transactions")

    # Indexes
    __table_args__ = (
        Index('idx_gift_transaction_card', 'gift_card_id'),
        Index('idx_gift_transaction_type', 'transaction_type'),
        Index('idx_gift_transaction_order', 'order_id'),
    )

# Additional models for system functionality
class SystemUser(Base):
    """System admin users (separate from restaurant users)"""
    __tablename__ = "system_users"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    username = Column(String(100), unique=True, nullable=False)
    email = Column(String(255), unique=True, nullable=False)
    full_name = Column(String(255), nullable=False)
    password_hash = Column(String(255), nullable=False)
    role = Column(String(50), default="admin")  # admin, super_admin
    is_active = Column(Boolean, default=True)
    last_login = Column(DateTime)

    # Timestamps
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)

    # Indexes
    __table_args__ = (
        Index('idx_system_user_username', 'username'),
        Index('idx_system_user_email', 'email'),
        Index('idx_system_user_active', 'is_active'),
    )

class AuditLog(Base):
    """Audit log for tracking changes"""
    __tablename__ = "audit_logs"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    restaurant_id = Column(String, ForeignKey("restaurants.id"))  # Nullable for system-wide events
    user_id = Column(String)  # Can be restaurant_user or system_user
    user_type = Column(String(20))  # restaurant_user, system_user
    action = Column(String(100), nullable=False)  # create, update, delete, login, etc.
    entity_type = Column(String(100))  # table name or entity type
    entity_id = Column(String)  # ID of the affected entity
    old_values = Column(JSON)  # Previous values (for updates)
    new_values = Column(JSON)  # New values
    ip_address = Column(String(45))  # IPv4 or IPv6
    user_agent = Column(Text)

    # Timestamps
    created_at = Column(DateTime, default=func.now(), nullable=False)

    # Indexes
    __table_args__ = (
        Index('idx_audit_restaurant', 'restaurant_id'),
        Index('idx_audit_user', 'user_id'),
        Index('idx_audit_action', 'action'),
        Index('idx_audit_entity', 'entity_type', 'entity_id'),
        Index('idx_audit_created', 'created_at'),
    )


# Notification enums
class NotificationType(enum.Enum):
    """Notification type enumeration"""
    INFO = "info"
    SUCCESS = "success"
    WARNING = "warning"
    ERROR = "error"
    INVENTORY = "inventory"
    STAFF = "staff"
    ORDER = "order"
    RESERVATION = "reservation"


class NotificationPriority(enum.Enum):
    """Notification priority enumeration"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"


class Notification(Base):
    """Notification model for restaurant-specific notifications"""
    __tablename__ = "notifications"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    restaurant_id = Column(String, ForeignKey("restaurants.id"), nullable=False)
    title = Column(String(255), nullable=False)
    message = Column(Text, nullable=False)
    type = Column(Enum(NotificationType), nullable=False, default=NotificationType.INFO)
    priority = Column(Enum(NotificationPriority), nullable=False, default=NotificationPriority.MEDIUM)
    is_read = Column(Boolean, default=False, nullable=False)
    link = Column(String(500), nullable=True)  # Optional navigation link
    related_id = Column(String(255), nullable=True)  # Optional related entity ID

    # Timestamps
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)

    # Relationships
    restaurant = relationship("Restaurant", back_populates="notifications")

    # Indexes
    __table_args__ = (
        Index('idx_notification_restaurant', 'restaurant_id'),
        Index('idx_notification_type', 'type'),
        Index('idx_notification_priority', 'priority'),
        Index('idx_notification_read', 'is_read'),
        Index('idx_notification_created', 'created_at'),
        Index('idx_notification_restaurant_read', 'restaurant_id', 'is_read'),
    )
