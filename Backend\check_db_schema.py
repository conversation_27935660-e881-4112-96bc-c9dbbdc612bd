#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to check the actual database schema and compare with models.
"""

import sqlite3
import os

def check_database_schema():
    """Check the actual database schema"""
    db_path = "restro_manage.db"
    
    if not os.path.exists(db_path):
        print(f"❌ Database file {db_path} not found!")
        return
    
    print(f"🔍 Checking database schema: {db_path}")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print(f"📊 Found {len(tables)} tables:")
        for table in tables:
            print(f"   - {table[0]}")
        
        # Check restaurants table specifically
        print("\n🏪 Restaurants table schema:")
        cursor.execute("PRAGMA table_info(restaurants);")
        columns = cursor.fetchall()
        
        if columns:
            print("   Columns:")
            for col in columns:
                print(f"   - {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'}")
        else:
            print("   ❌ Restaurants table not found!")
        
        # Check menu_items table
        print("\n🍽️ Menu Items table schema:")
        cursor.execute("PRAGMA table_info(menu_items);")
        columns = cursor.fetchall()
        
        if columns:
            print("   Columns:")
            for col in columns:
                print(f"   - {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'}")
        else:
            print("   ❌ Menu Items table not found!")
        
        # Check tables table
        print("\n🪑 Tables table schema:")
        cursor.execute("PRAGMA table_info(tables);")
        columns = cursor.fetchall()
        
        if columns:
            print("   Columns:")
            for col in columns:
                print(f"   - {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'}")
        else:
            print("   ❌ Tables table not found!")
        
        # Check data counts
        print("\n📈 Data counts:")
        for table_name in ['restaurants', 'menu_items', 'tables', 'restaurant_users', 'orders']:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
                count = cursor.fetchone()[0]
                print(f"   {table_name}: {count} records")
            except sqlite3.OperationalError as e:
                print(f"   {table_name}: Error - {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error checking database: {e}")

if __name__ == "__main__":
    check_database_schema()
