import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useRestaurant } from '@/contexts/RestaurantContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  ChefHat, 
  Users, 
  UtensilsCrossed, 
  BarChart3, 
  Settings, 
  Calendar,
  MapPin,
  Phone,
  Mail,
  Clock,
  Table,
  LogOut,
  Loader2
} from 'lucide-react';
import { toast } from '@/components/ui/sonner';
import logger from '@/utils/logger';

const RestaurantDashboard = () => {
  const navigate = useNavigate();
  const { currentRestaurant: restaurant, isRestaurantAuthenticated: isAuthenticated, logoutRestaurant: logout } = useRestaurant();
  const [isLoading, setIsLoading] = useState(false);
  const [dashboardStats, setDashboardStats] = useState({
    totalTables: 0,
    activeStaff: 0,
    todayOrders: 0,
    revenue: 0
  });

  useEffect(() => {
    logger.info('Restaurant Dashboard loaded', 'RestaurantDashboard', {
      restaurantId: restaurant?.id,
      restaurantCode: restaurant?.code
    });

    // Redirect to login if not authenticated
    if (!isLoading && !isAuthenticated) {
      logger.warn('Unauthenticated access to dashboard, redirecting to login', 'RestaurantDashboard');
      navigate('/restaurant-login');
      return;
    }

    // Load dashboard statistics
    if (restaurant) {
      loadDashboardStats();
    }
  }, [restaurant, isAuthenticated, isLoading, navigate]);

  const loadDashboardStats = async () => {
    try {
      // Extract stats from restaurant setup data
      const setupData = (restaurant as any)?.setupData || {};
      
      setDashboardStats({
        totalTables: setupData.totalTables || setupData.tables?.length || 0,
        activeStaff: 1, // At least the owner
        todayOrders: 0, // Will be loaded from orders API
        revenue: 0 // Will be calculated from orders
      });

      logger.debug('Dashboard stats loaded', 'RestaurantDashboard', {
        restaurantId: restaurant?.id,
        stats: dashboardStats
      });
    } catch (error) {
      logger.error('Failed to load dashboard stats', 'RestaurantDashboard', {
        error: error.message
      });
    }
  };

  const handleLogout = () => {
    logger.userAction('restaurant logout', 'RestaurantDashboard', {
      restaurantCode: restaurant?.code
    });
    logout();
    navigate('/restaurant-login');
  };

  const navigateToSection = (section: string) => {
    logger.userAction('dashboard navigation', 'RestaurantDashboard', {
      section,
      restaurantCode: restaurant?.code
    });
    navigate(`/${section}`);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Loading restaurant dashboard...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated || !restaurant) {
    return null; // Will redirect in useEffect
  }

  const operatingHours = (restaurant as any)?.setupData?.operatingHours || [];
  const todayHours = operatingHours.find(h => h.day === new Date().toLocaleDateString('en-US', { weekday: 'long' }));

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <ChefHat className="h-8 w-8 text-blue-600" />
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">{restaurant.name}</h1>
                  <p className="text-sm text-gray-500">Restaurant Code: {restaurant.code}</p>
                </div>
              </div>
              <Badge variant={restaurant.isActive ? "default" : "secondary"}>
                {restaurant.isActive ? "Active" : "Inactive"}
              </Badge>
            </div>
            <Button variant="outline" onClick={handleLogout}>
              <LogOut className="h-4 w-4 mr-2" />
              Logout
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            Welcome to {restaurant.name}!
          </h2>
          <p className="text-gray-600">
            Manage your restaurant operations from this central dashboard.
          </p>
        </div>

        {/* Restaurant Info Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium text-gray-600">Restaurant Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center text-sm">
                  <MapPin className="h-4 w-4 mr-2 text-gray-400" />
                  <span>{restaurant.address}</span>
                </div>
                <div className="flex items-center text-sm">
                  <Phone className="h-4 w-4 mr-2 text-gray-400" />
                  <span>{restaurant.phone}</span>
                </div>
                <div className="flex items-center text-sm">
                  <Mail className="h-4 w-4 mr-2 text-gray-400" />
                  <span>{restaurant.email}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium text-gray-600">Operating Hours</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {todayHours ? (
                  <div className="flex items-center text-sm">
                    <Clock className="h-4 w-4 mr-2 text-gray-400" />
                    <span>
                      Today: {todayHours.isOpen 
                        ? `${todayHours.openTime} - ${todayHours.closeTime}`
                        : 'Closed'
                      }
                    </span>
                  </div>
                ) : (
                  <p className="text-sm text-gray-500">Operating hours not configured</p>
                )}
                <div className="flex items-center text-sm">
                  <Table className="h-4 w-4 mr-2 text-gray-400" />
                  <span>{dashboardStats.totalTables} tables configured</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium text-gray-600">Quick Stats</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Active Staff:</span>
                  <span className="font-medium">{dashboardStats.activeStaff}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Today's Orders:</span>
                  <span className="font-medium">{dashboardStats.todayOrders}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Revenue:</span>
                  <span className="font-medium">£{dashboardStats.revenue}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Navigation Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card className="hover:shadow-lg transition-shadow cursor-pointer" onClick={() => navigateToSection('menu')}>
            <CardHeader>
              <CardTitle className="flex items-center">
                <UtensilsCrossed className="h-5 w-5 mr-2 text-blue-600" />
                Menu Management
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Manage your restaurant's menu items, categories, and pricing.</p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow cursor-pointer" onClick={() => navigateToSection('staff')}>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="h-5 w-5 mr-2 text-green-600" />
                Staff Management
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Manage staff members, schedules, and performance tracking.</p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow cursor-pointer" onClick={() => navigateToSection('tables')}>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Table className="h-5 w-5 mr-2 text-purple-600" />
                Table Management
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Configure tables, seating arrangements, and reservations.</p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow cursor-pointer" onClick={() => navigateToSection('analytics')}>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="h-5 w-5 mr-2 text-orange-600" />
                Analytics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">View sales reports, performance metrics, and business insights.</p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow cursor-pointer" onClick={() => navigateToSection('orders')}>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calendar className="h-5 w-5 mr-2 text-red-600" />
                Orders
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Track current orders, order history, and customer management.</p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow cursor-pointer" onClick={() => navigateToSection('settings')}>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Settings className="h-5 w-5 mr-2 text-gray-600" />
                Settings
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Configure restaurant settings, preferences, and system options.</p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default RestaurantDashboard;
