"""
Base controller class providing common functionality for all controllers.
Implements caching, error handling, and async operation management.
"""

from abc import ABC
from typing import Any, Dict, List, Optional, Union, Callable
from fastapi import HTTPException, status, BackgroundTasks
from app.utils.logging_config import logger
import asyncio
import time
import json
from functools import wraps
from datetime import datetime, timedelta

class CacheManager:
    """Simple in-memory cache manager with TTL support"""
    
    def __init__(self):
        self._cache: Dict[str, Dict[str, Any]] = {}
    
    def set(self, key: str, value: Any, ttl: int = 300) -> None:
        """Set cache value with TTL in seconds"""
        expiry = datetime.now() + timedelta(seconds=ttl)
        self._cache[key] = {
            'value': value,
            'expiry': expiry
        }
    
    def get(self, key: str) -> Optional[Any]:
        """Get cache value if not expired"""
        if key not in self._cache:
            return None
        
        cache_entry = self._cache[key]
        if datetime.now() > cache_entry['expiry']:
            del self._cache[key]
            return None
        
        return cache_entry['value']
    
    def delete(self, key: str) -> None:
        """Delete cache entry"""
        if key in self._cache:
            del self._cache[key]
    
    def clear(self) -> None:
        """Clear all cache entries"""
        self._cache.clear()
    
    def invalidate_pattern(self, pattern: str) -> None:
        """Invalidate all cache keys matching pattern"""
        keys_to_delete = [key for key in self._cache.keys() if pattern in key]
        for key in keys_to_delete:
            del self._cache[key]

# Global cache instance
cache_manager = CacheManager()

class BaseController(ABC):
    """
    Base controller with common functionality for all controllers.
    Provides caching, error handling, and async operation management.
    """
    
    def __init__(self):
        self.cache = cache_manager
        self.default_cache_ttl = 300  # 5 minutes
        self.logger = logger
    
    async def handle_async_operation(
        self, 
        operation: Callable, 
        *args, 
        error_message: str = "Operation failed",
        **kwargs
    ) -> Any:
        """
        Handle async operations with comprehensive error handling and logging.
        
        Args:
            operation: The async function to execute
            *args: Positional arguments for the operation
            error_message: Custom error message for logging
            **kwargs: Keyword arguments for the operation
            
        Returns:
            Result of the operation
            
        Raises:
            HTTPException: On operation failure
        """
        start_time = time.time()
        operation_name = getattr(operation, '__name__', str(operation))
        
        try:
            self.logger.info(f"Starting operation: {operation_name}")
            result = await operation(*args, **kwargs)
            
            execution_time = time.time() - start_time
            self.logger.info(
                f"Operation completed: {operation_name}",
                "Performance",
                {"execution_time": execution_time}
            )
            
            return result
            
        except HTTPException:
            # Re-raise HTTP exceptions as-is
            raise
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(
                f"{error_message}: {operation_name}",
                "ControllerError",
                {
                    "error": str(e),
                    "operation": operation_name,
                    "execution_time": execution_time,
                    "args": str(args)[:200],  # Limit log size
                    "kwargs": str(kwargs)[:200]
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"{error_message}: {str(e)}"
            )
    
    def cache_result(
        self, 
        key: str, 
        result: Any, 
        ttl: int = None
    ) -> None:
        """
        Cache operation result with optional TTL.
        
        Args:
            key: Cache key
            result: Result to cache
            ttl: Time to live in seconds (uses default if None)
        """
        cache_ttl = ttl or self.default_cache_ttl
        try:
            self.cache.set(key, result, cache_ttl)
            self.logger.debug(f"Cached result for key: {key}", "Cache")
        except Exception as e:
            self.logger.warning(f"Failed to cache result for key {key}: {e}", "Cache")
    
    def get_cached_result(self, key: str) -> Optional[Any]:
        """
        Get cached result by key.
        
        Args:
            key: Cache key
            
        Returns:
            Cached result or None if not found/expired
        """
        try:
            result = self.cache.get(key)
            if result is not None:
                self.logger.debug(f"Cache hit for key: {key}", "Cache")
            else:
                self.logger.debug(f"Cache miss for key: {key}", "Cache")
            return result
        except Exception as e:
            self.logger.warning(f"Failed to get cached result for key {key}: {e}", "Cache")
            return None
    
    def invalidate_cache(self, key: str) -> None:
        """
        Invalidate specific cache entry.
        
        Args:
            key: Cache key to invalidate
        """
        try:
            self.cache.delete(key)
            self.logger.debug(f"Invalidated cache for key: {key}", "Cache")
        except Exception as e:
            self.logger.warning(f"Failed to invalidate cache for key {key}: {e}", "Cache")
    
    def invalidate_cache_pattern(self, pattern: str) -> None:
        """
        Invalidate all cache entries matching pattern.
        
        Args:
            pattern: Pattern to match in cache keys
        """
        try:
            self.cache.invalidate_pattern(pattern)
            self.logger.debug(f"Invalidated cache for pattern: {pattern}", "Cache")
        except Exception as e:
            self.logger.warning(f"Failed to invalidate cache for pattern {pattern}: {e}", "Cache")
    
    async def execute_background_task(
        self, 
        background_tasks: BackgroundTasks, 
        task_func: Callable, 
        *args, 
        **kwargs
    ) -> None:
        """
        Execute a task in the background.
        
        Args:
            background_tasks: FastAPI BackgroundTasks instance
            task_func: Function to execute in background
            *args: Positional arguments for the task
            **kwargs: Keyword arguments for the task
        """
        try:
            background_tasks.add_task(task_func, *args, **kwargs)
            self.logger.info(f"Added background task: {getattr(task_func, '__name__', str(task_func))}")
        except Exception as e:
            self.logger.error(f"Failed to add background task: {e}")
    
    def validate_pagination(
        self, 
        skip: int = 0, 
        limit: int = 100, 
        max_limit: int = 1000
    ) -> tuple[int, int]:
        """
        Validate and normalize pagination parameters.
        
        Args:
            skip: Number of items to skip
            limit: Number of items to return
            max_limit: Maximum allowed limit
            
        Returns:
            Tuple of (validated_skip, validated_limit)
            
        Raises:
            HTTPException: If parameters are invalid
        """
        if skip < 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Skip parameter must be non-negative"
            )
        
        if limit <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Limit parameter must be positive"
            )
        
        if limit > max_limit:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Limit parameter cannot exceed {max_limit}"
            )
        
        return skip, limit
    
    def create_response(
        self, 
        data: Any, 
        message: str = "Success", 
        status_code: int = 200,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create standardized response format.
        
        Args:
            data: Response data
            message: Response message
            status_code: HTTP status code
            metadata: Additional metadata
            
        Returns:
            Standardized response dictionary
        """
        response = {
            "success": 200 <= status_code < 300,
            "message": message,
            "data": data,
            "timestamp": datetime.now().isoformat()
        }
        
        if metadata:
            response["metadata"] = metadata
        
        return response
    
    def handle_not_found(self, resource_name: str, resource_id: str = None) -> HTTPException:
        """
        Create standardized not found exception.
        
        Args:
            resource_name: Name of the resource
            resource_id: ID of the resource (optional)
            
        Returns:
            HTTPException with 404 status
        """
        detail = f"{resource_name} not found"
        if resource_id:
            detail += f" with ID: {resource_id}"
        
        return HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=detail
        )
    
    def handle_validation_error(self, message: str, details: Optional[Dict] = None) -> HTTPException:
        """
        Create standardized validation error exception.
        
        Args:
            message: Error message
            details: Additional error details
            
        Returns:
            HTTPException with 400 status
        """
        error_detail = {"message": message}
        if details:
            error_detail["details"] = details
        
        return HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_detail
        )
