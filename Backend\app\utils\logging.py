"""
Production-ready logging configuration for RestroManage
"""

import logging
import logging.config
import sys
import json
from datetime import datetime
from typing import Dict, Any
import structlog
from app.config import settings

class JSONFormatter(logging.Formatter):
    """Custom JSON formatter for structured logging"""
    
    def format(self, record: logging.LogRecord) -> str:
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }
        
        # Add extra fields if present
        if hasattr(record, 'restaurant_id'):
            log_entry['restaurant_id'] = record.restaurant_id
        
        if hasattr(record, 'user_id'):
            log_entry['user_id'] = record.user_id
        
        if hasattr(record, 'request_id'):
            log_entry['request_id'] = record.request_id
        
        # Add exception info if present
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        return json.dumps(log_entry)

def setup_logging():
    """Configure logging for the application"""
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Configure standard logging
    logging_config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "json": {
                "()": JSONFormatter,
            },
            "standard": {
                "format": "%(asctime)s [%(levelname)s] %(name)s: %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S",
            },
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "level": settings.LOG_LEVEL,
                "formatter": "json" if settings.LOG_FORMAT == "json" else "standard",
                "stream": sys.stdout,
            },
            "file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": settings.LOG_LEVEL,
                "formatter": "json" if settings.LOG_FORMAT == "json" else "standard",
                "filename": "logs/app.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
            },
        },
        "loggers": {
            "app": {
                "level": settings.LOG_LEVEL,
                "handlers": ["console", "file"],
                "propagate": False,
            },
            "uvicorn": {
                "level": "INFO",
                "handlers": ["console"],
                "propagate": False,
            },
            "sqlalchemy.engine": {
                "level": "INFO" if settings.ENABLE_SQL_LOGGING else "WARNING",
                "handlers": ["console"],
                "propagate": False,
            },
        },
        "root": {
            "level": settings.LOG_LEVEL,
            "handlers": ["console"],
        },
    }
    
    # Create logs directory if it doesn't exist
    import os
    os.makedirs("logs", exist_ok=True)
    
    logging.config.dictConfig(logging_config)

def get_logger(name: str) -> logging.Logger:
    """Get a logger instance with the given name"""
    return logging.getLogger(f"app.{name}")

def log_request(request_id: str, method: str, path: str, **kwargs):
    """Log HTTP request"""
    logger = get_logger("requests")
    logger.info(
        "HTTP request",
        extra={
            "request_id": request_id,
            "method": method,
            "path": path,
            **kwargs
        }
    )

def log_response(request_id: str, status_code: int, duration: float, **kwargs):
    """Log HTTP response"""
    logger = get_logger("requests")
    logger.info(
        "HTTP response",
        extra={
            "request_id": request_id,
            "status_code": status_code,
            "duration_ms": round(duration * 1000, 2),
            **kwargs
        }
    )

def log_database_query(query: str, duration: float, **kwargs):
    """Log database query"""
    logger = get_logger("database")
    logger.debug(
        "Database query",
        extra={
            "query": query,
            "duration_ms": round(duration * 1000, 2),
            **kwargs
        }
    )

def log_analytics_request(restaurant_id: str, endpoint: str, **kwargs):
    """Log analytics request"""
    logger = get_logger("analytics")
    logger.info(
        "Analytics request",
        extra={
            "restaurant_id": restaurant_id,
            "endpoint": endpoint,
            **kwargs
        }
    )

def log_error(error: Exception, context: Dict[str, Any] = None):
    """Log error with context"""
    logger = get_logger("errors")
    logger.error(
        f"Error occurred: {str(error)}",
        exc_info=True,
        extra=context or {}
    )

def log_security_event(event_type: str, **kwargs):
    """Log security-related events"""
    logger = get_logger("security")
    logger.warning(
        f"Security event: {event_type}",
        extra=kwargs
    )

# Initialize logging when module is imported
setup_logging()
