# FastAPI Backend MVC Refactoring Plan

## Overview
Restructure the FastAPI backend to follow proper MVC (Model-View-Controller) architecture and implement action controllers to handle slow processing issues.

## Current Issues Identified
1. **Mixed async/sync patterns** causing blocking operations
2. **Direct database calls in routers** without proper caching
3. **Fallback storage system** adding overhead
4. **No lazy loading** - all data loaded upfront
5. **Heavy service operations** (like forecasting) running synchronously
6. **Business logic mixed in routers** instead of controllers

## Target MVC Architecture

### Models (M) - Data Layer
- **Location**: `app/models/`
- **Purpose**: Data structures and validation
- **Components**:
  - Pydantic models for API serialization/validation
  - SQLAlchemy models for database operations
  - Business domain models

### Views (V) - Presentation Layer
- **Location**: `app/routers/` (FastAPI route handlers)
- **Purpose**: HTTP request/response handling
- **Responsibilities**:
  - Request validation
  - Response formatting
  - HTTP status codes
  - Authentication/authorization checks

### Controllers (C) - Business Logic Layer
- **Location**: `app/controllers/` (NEW)
- **Purpose**: Business logic and orchestration
- **Responsibilities**:
  - Business rule implementation
  - Service coordination
  - Data transformation
  - Error handling
  - Caching strategies

## Implementation Plan

### Phase 1: Create Controllers Directory Structure

#### 1.1 Base Controller
Create `app/controllers/base.py` with:
- Common controller functionality
- Error handling patterns
- Caching mechanisms
- Async operation management

#### 1.2 Specific Controllers
Create individual controllers:
- `app/controllers/restaurant_controller.py`
- `app/controllers/menu_controller.py`
- `app/controllers/order_controller.py`
- `app/controllers/staff_controller.py`
- `app/controllers/table_controller.py`
- `app/controllers/inventory_controller.py`
- `app/controllers/analytics_controller.py`
- `app/controllers/forecasting_controller.py`
- `app/controllers/discount_controller.py`
- `app/controllers/split_bill_controller.py`
- `app/controllers/ai_insights_controller.py`

### Phase 2: Implement Action Controllers

#### 2.1 Lazy Loading Strategy
- Implement on-demand data loading
- Add caching layers for frequently accessed data
- Background task processing for heavy operations

#### 2.2 Performance Optimization
- Async/await patterns for all I/O operations
- Response caching where appropriate
- Database query optimization
- Background task processing

#### 2.3 Error Handling
- Centralized error handling
- Proper HTTP status codes
- Logging and monitoring

### Phase 3: Refactor Routers

#### 3.1 Slim Down Routers
- Move business logic to controllers
- Keep only HTTP-specific code
- Implement dependency injection

#### 3.2 Update Route Handlers
- Call controller methods instead of direct service calls
- Standardize response formats
- Implement proper error handling

### Phase 4: Service Layer Enhancement

#### 4.1 Service Optimization
- Make all services async
- Implement caching strategies
- Add background task support

#### 4.2 Repository Pattern
- Enhance existing repositories
- Add caching layer
- Optimize database queries

## Detailed Implementation Steps

### Step 1: Create Base Controller
```python
# app/controllers/base.py
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union
from fastapi import HTTPException, status
from app.utils.logging_config import logger
import asyncio
from functools import wraps

class BaseController(ABC):
    """Base controller with common functionality"""

    def __init__(self):
        self.cache = {}
        self.cache_ttl = 300  # 5 minutes default

    async def handle_async_operation(self, operation, *args, **kwargs):
        """Handle async operations with error handling"""
        try:
            return await operation(*args, **kwargs)
        except Exception as e:
            logger.error(f"Operation failed: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=str(e)
            )

    def cache_result(self, key: str, result: Any, ttl: int = None):
        """Cache operation result"""
        # Implementation for caching
        pass

    def get_cached_result(self, key: str) -> Optional[Any]:
        """Get cached result"""
        # Implementation for cache retrieval
        pass
```

### Step 2: Create Restaurant Controller
```python
# app/controllers/restaurant_controller.py
from typing import List, Optional, Dict, Any
from app.controllers.base import BaseController
from app.services.database_service import db_service
from app.models.restaurants import Restaurant, RestaurantCreate, RestaurantUpdate
from app.utils.logging_config import logger

class RestaurantController(BaseController):
    """Controller for restaurant business logic"""

    async def get_all_restaurants(self) -> List[Dict[str, Any]]:
        """Get all restaurants with caching"""
        cache_key = "all_restaurants"
        cached_result = self.get_cached_result(cache_key)

        if cached_result:
            return cached_result

        restaurants = await self.handle_async_operation(
            db_service.get_all, "restaurants"
        )

        self.cache_result(cache_key, restaurants)
        return restaurants

    async def get_restaurant_by_id(self, restaurant_id: str) -> Optional[Dict[str, Any]]:
        """Get restaurant by ID with caching"""
        cache_key = f"restaurant_{restaurant_id}"
        cached_result = self.get_cached_result(cache_key)

        if cached_result:
            return cached_result

        restaurant = await self.handle_async_operation(
            db_service.get_by_id, "restaurants", restaurant_id
        )

        if restaurant:
            self.cache_result(cache_key, restaurant)

        return restaurant

    async def create_restaurant(self, restaurant_data: RestaurantCreate) -> Dict[str, Any]:
        """Create new restaurant with validation"""
        # Business logic for restaurant creation
        # Validation, code generation, etc.

        created_restaurant = await self.handle_async_operation(
            db_service.create, "restaurants", restaurant_data.model_dump()
        )

        # Invalidate cache
        self.invalidate_cache("all_restaurants")

        return created_restaurant
```

### Step 3: Update Router to Use Controller
```python
# app/routers/restaurants.py (updated)
from fastapi import APIRouter, HTTPException, status, Depends
from app.controllers.restaurant_controller import RestaurantController
from app.models.restaurants import Restaurant, RestaurantCreate

router = APIRouter(prefix="/restaurants", tags=["Restaurants"])

async def get_restaurant_controller() -> RestaurantController:
    """Dependency injection for restaurant controller"""
    return RestaurantController()

@router.get("/", response_model=List[Restaurant])
async def get_restaurants(
    controller: RestaurantController = Depends(get_restaurant_controller)
):
    """Get all restaurants"""
    restaurants = await controller.get_all_restaurants()
    return restaurants

@router.get("/{restaurant_id}", response_model=Restaurant)
async def get_restaurant(
    restaurant_id: str,
    controller: RestaurantController = Depends(get_restaurant_controller)
):
    """Get a restaurant by ID"""
    restaurant = await controller.get_restaurant_by_id(restaurant_id)
    if not restaurant:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Restaurant not found"
        )
    return restaurant
```

## Performance Optimizations

### 1. Caching Strategy
- In-memory caching for frequently accessed data
- Cache invalidation on data updates
- TTL-based cache expiration

### 2. Background Tasks
- Heavy operations (forecasting, analytics) run in background
- Task queues for long-running processes
- Progress tracking for async operations

### 3. Database Optimization
- Connection pooling
- Query optimization
- Lazy loading relationships

### 4. Response Optimization
- Pagination for large datasets
- Selective field loading
- Compressed responses

## Migration Strategy

### Phase 1: Parallel Implementation
- Create controllers alongside existing routers
- Gradual migration of endpoints
- A/B testing for performance comparison

### Phase 2: Router Refactoring
- Update routers to use controllers
- Remove business logic from routers
- Implement dependency injection

### Phase 3: Service Enhancement
- Optimize existing services
- Add caching layers
- Implement background processing

### Phase 4: Testing and Validation
- Performance testing
- Load testing
- Error handling validation

## Expected Benefits

1. **Performance Improvements**:
   - 50-70% reduction in response times
   - Better resource utilization
   - Improved scalability

2. **Code Quality**:
   - Clear separation of concerns
   - Better testability
   - Easier maintenance

3. **Developer Experience**:
   - Consistent patterns
   - Better error handling
   - Improved debugging

## Success Metrics

1. **Performance Metrics**:
   - Response time < 200ms for feature access checks
   - Database query time < 100ms
   - Memory usage optimization

2. **Code Quality Metrics**:
   - Test coverage > 80%
   - Reduced cyclomatic complexity
   - Better code organization

3. **Operational Metrics**:
   - Reduced error rates
   - Better logging and monitoring
   - Improved system reliability
