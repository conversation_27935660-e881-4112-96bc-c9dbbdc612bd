#!/usr/bin/env python3
"""
Test script for MVC implementation.
Tests controllers, caching, performance, and error handling.
"""

import asyncio
import time
import sys
import os
from typing import Dict, Any

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.controllers.restaurant_controller import RestaurantController
from app.controllers.menu_controller import MenuController
from app.controllers.forecasting_controller import ForecastingController
from app.models.forecasting import ForecastPeriod

class MVCTester:
    """Test suite for MVC implementation"""
    
    def __init__(self):
        self.results = {
            "tests_run": 0,
            "tests_passed": 0,
            "tests_failed": 0,
            "errors": []
        }
    
    def log_test(self, test_name: str, success: bool, message: str = ""):
        """Log test result"""
        self.results["tests_run"] += 1
        if success:
            self.results["tests_passed"] += 1
            print(f"✅ {test_name}: PASSED {message}")
        else:
            self.results["tests_failed"] += 1
            self.results["errors"].append(f"{test_name}: {message}")
            print(f"❌ {test_name}: FAILED {message}")
    
    async def test_controller_initialization(self):
        """Test that controllers can be initialized"""
        try:
            restaurant_controller = RestaurantController()
            menu_controller = MenuController()
            forecasting_controller = ForecastingController()
            
            # Check that controllers have required attributes
            assert hasattr(restaurant_controller, 'cache_prefix')
            assert hasattr(menu_controller, 'cache_prefix')
            assert hasattr(forecasting_controller, 'cache_prefix')
            
            self.log_test("Controller Initialization", True, "All controllers initialized successfully")
        except Exception as e:
            self.log_test("Controller Initialization", False, str(e))
    
    async def test_caching_functionality(self):
        """Test caching system"""
        try:
            controller = RestaurantController()
            
            # Test cache set and get
            test_data = {"test": "data", "timestamp": time.time()}
            controller.cache_result("test_key", test_data, 60)
            
            cached_data = controller.get_cached_result("test_key")
            assert cached_data == test_data
            
            # Test cache invalidation
            controller.invalidate_cache("test_key")
            cached_data_after_invalidation = controller.get_cached_result("test_key")
            assert cached_data_after_invalidation is None
            
            self.log_test("Caching Functionality", True, "Cache set, get, and invalidation working")
        except Exception as e:
            self.log_test("Caching Functionality", False, str(e))
    
    async def test_error_handling(self):
        """Test error handling patterns"""
        try:
            controller = RestaurantController()
            
            # Test handle_not_found
            not_found_error = controller.handle_not_found("Restaurant", "test-id")
            assert not_found_error.status_code == 404
            
            # Test handle_validation_error
            validation_error = controller.handle_validation_error("Test validation error")
            assert validation_error.status_code == 400
            
            self.log_test("Error Handling", True, "Error handling methods working correctly")
        except Exception as e:
            self.log_test("Error Handling", False, str(e))
    
    async def test_pagination_validation(self):
        """Test pagination validation"""
        try:
            controller = RestaurantController()
            
            # Test valid pagination
            skip, limit = controller.validate_pagination(0, 50)
            assert skip == 0 and limit == 50
            
            # Test invalid pagination (should raise exception)
            try:
                controller.validate_pagination(-1, 50)
                assert False, "Should have raised exception for negative skip"
            except:
                pass  # Expected
            
            try:
                controller.validate_pagination(0, 0)
                assert False, "Should have raised exception for zero limit"
            except:
                pass  # Expected
            
            self.log_test("Pagination Validation", True, "Pagination validation working correctly")
        except Exception as e:
            self.log_test("Pagination Validation", False, str(e))
    
    async def test_response_formatting(self):
        """Test response formatting"""
        try:
            controller = RestaurantController()
            
            test_data = {"id": "1", "name": "Test Restaurant"}
            response = controller.create_response(
                data=test_data,
                message="Test successful",
                status_code=200,
                metadata={"test": True}
            )
            
            assert response["success"] is True
            assert response["message"] == "Test successful"
            assert response["data"] == test_data
            assert response["metadata"]["test"] is True
            assert "timestamp" in response
            
            self.log_test("Response Formatting", True, "Response formatting working correctly")
        except Exception as e:
            self.log_test("Response Formatting", False, str(e))
    
    async def test_menu_controller_filtering(self):
        """Test menu controller filtering logic"""
        try:
            controller = MenuController()
            
            # Test filter application
            test_items = [
                {"id": "1", "name": "Pizza", "category": "main", "allergens": ["gluten"], "available": True},
                {"id": "2", "name": "Salad", "category": "starter", "allergens": [], "available": True},
                {"id": "3", "name": "Pasta", "category": "main", "allergens": ["gluten"], "available": False}
            ]
            
            # Test category filter
            filtered = controller._apply_filters(test_items, category="main")
            assert len(filtered) == 2
            
            # Test allergen filter
            filtered = controller._apply_filters(test_items, allergens="gluten")
            assert len(filtered) == 2
            
            # Test availability filter
            filtered = controller._apply_filters(test_items, available=True)
            assert len(filtered) == 2
            
            # Test combined filters
            filtered = controller._apply_filters(test_items, category="main", available=True)
            assert len(filtered) == 1
            
            self.log_test("Menu Controller Filtering", True, "Filtering logic working correctly")
        except Exception as e:
            self.log_test("Menu Controller Filtering", False, str(e))
    
    async def test_forecasting_controller_caching(self):
        """Test forecasting controller caching"""
        try:
            controller = ForecastingController()
            
            # Test cache key generation
            cache_key = f"{controller.cache_prefix}_sales_{ForecastPeriod.WEEKLY.value}_7_test"
            
            # Test that cache is working
            test_forecast = {
                "period": "weekly",
                "predictions": [],
                "generated_at": time.time()
            }
            
            controller.cache_result(cache_key, test_forecast, 300)
            cached_result = controller.get_cached_result(cache_key)
            
            assert cached_result == test_forecast
            
            self.log_test("Forecasting Controller Caching", True, "Forecasting cache working correctly")
        except Exception as e:
            self.log_test("Forecasting Controller Caching", False, str(e))
    
    async def test_performance_monitoring(self):
        """Test performance monitoring functionality"""
        try:
            controller = RestaurantController()
            
            # Test async operation handling
            async def dummy_operation():
                await asyncio.sleep(0.1)  # Simulate work
                return {"result": "success"}
            
            start_time = time.time()
            result = await controller.handle_async_operation(
                dummy_operation,
                error_message="Test operation failed"
            )
            end_time = time.time()
            
            assert result["result"] == "success"
            assert end_time - start_time >= 0.1  # Should take at least 0.1 seconds
            
            self.log_test("Performance Monitoring", True, "Async operation handling working")
        except Exception as e:
            self.log_test("Performance Monitoring", False, str(e))
    
    async def test_cache_performance(self):
        """Test cache performance improvement"""
        try:
            controller = RestaurantController()
            
            # Simulate expensive operation
            async def expensive_operation():
                await asyncio.sleep(0.05)  # Simulate database query
                return {"data": "expensive_result", "timestamp": time.time()}
            
            # First call (no cache)
            start_time = time.time()
            result1 = await controller.handle_async_operation(expensive_operation)
            first_call_time = time.time() - start_time
            
            # Cache the result
            controller.cache_result("expensive_op", result1, 60)
            
            # Second call (from cache)
            start_time = time.time()
            result2 = controller.get_cached_result("expensive_op")
            second_call_time = time.time() - start_time
            
            # Cache should be significantly faster
            performance_improvement = first_call_time / second_call_time if second_call_time > 0 else float('inf')
            
            assert result1 == result2
            assert performance_improvement > 10  # Should be at least 10x faster
            
            self.log_test(
                "Cache Performance", 
                True, 
                f"Cache {performance_improvement:.1f}x faster than original"
            )
        except Exception as e:
            self.log_test("Cache Performance", False, str(e))
    
    def print_summary(self):
        """Print test summary"""
        print("\n" + "="*50)
        print("MVC IMPLEMENTATION TEST SUMMARY")
        print("="*50)
        print(f"Tests Run: {self.results['tests_run']}")
        print(f"Tests Passed: {self.results['tests_passed']}")
        print(f"Tests Failed: {self.results['tests_failed']}")
        
        if self.results['tests_failed'] > 0:
            print("\nFAILED TESTS:")
            for error in self.results['errors']:
                print(f"  - {error}")
        
        success_rate = (self.results['tests_passed'] / self.results['tests_run']) * 100
        print(f"\nSuccess Rate: {success_rate:.1f}%")
        
        if success_rate >= 90:
            print("🎉 MVC Implementation is working well!")
        elif success_rate >= 70:
            print("⚠️  MVC Implementation has some issues that need attention")
        else:
            print("🚨 MVC Implementation has significant issues")

async def main():
    """Run all tests"""
    print("Starting MVC Implementation Tests...")
    print("="*50)
    
    tester = MVCTester()
    
    # Run all tests
    await tester.test_controller_initialization()
    await tester.test_caching_functionality()
    await tester.test_error_handling()
    await tester.test_pagination_validation()
    await tester.test_response_formatting()
    await tester.test_menu_controller_filtering()
    await tester.test_forecasting_controller_caching()
    await tester.test_performance_monitoring()
    await tester.test_cache_performance()
    
    # Print summary
    tester.print_summary()

if __name__ == "__main__":
    asyncio.run(main())
