"""
Production Configuration for RestroManage
"""

import os
from typing import List, Optional
try:
    from pydantic_settings import BaseSettings
    from pydantic import field_validator
except ImportError:
    from pydantic import BaseSettings
    # Fallback for older pydantic versions
    try:
        from pydantic import field_validator
    except ImportError:
        from pydantic import validator as field_validator
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Settings(BaseSettings):
    """Application settings with environment variable support"""
    
    # API Configuration
    API_V1_STR: str = "/api"
    PROJECT_NAME: str = "RestroManage"
    VERSION: str = "1.0.0"
    DESCRIPTION: str = "RestroManage API - A comprehensive restaurant management system"
    
    # Database Configuration
    DATABASE_URL: str = os.getenv("DATABASE_URL", "sqlite+aiosqlite:///./restro_manage.db")
    
    # Redis Configuration
    REDIS_URL: str = os.getenv("REDIS_URL", "redis://localhost:6379/0")
    
    # Security Configuration
    SECRET_KEY: str = os.getenv("SECRET_KEY", "09d25e094faa6ca2556c818166b7a9563b93f7099f6f0f4caa6cf63b88e8d3e7")
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # CORS Configuration
    ALLOWED_ORIGINS: List[str] = ["http://localhost:3000", "http://localhost:5173"]
    ALLOWED_METHODS: List[str] = ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    ALLOWED_HEADERS: List[str] = ["*"]
    
    @field_validator("ALLOWED_ORIGINS", mode="before")
    @classmethod
    def assemble_cors_origins(cls, v):
        if isinstance(v, str):
            return [i.strip() for i in v.split(",")]
        return v

    @field_validator("ALLOWED_METHODS", mode="before")
    @classmethod
    def assemble_cors_methods(cls, v):
        if isinstance(v, str):
            return [i.strip() for i in v.split(",")]
        return v

    @field_validator("ALLOWED_HEADERS", mode="before")
    @classmethod
    def assemble_cors_headers(cls, v):
        if isinstance(v, str):
            return [i.strip() for i in v.split(",")]
        return v

    @field_validator("ALLOWED_FILE_TYPES", mode="before")
    @classmethod
    def assemble_file_types(cls, v):
        if isinstance(v, str):
            return [i.strip() for i in v.split(",")]
        return v
    
    # Logging Configuration
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "json"
    ENABLE_SQL_LOGGING: bool = False
    
    # File Upload Configuration
    MAX_FILE_SIZE: int = 10485760  # 10MB
    UPLOAD_DIR: str = "./uploads"
    ALLOWED_FILE_TYPES: List[str] = ["image/jpeg", "image/png", "image/svg+xml", "application/pdf"]
    
    # Email Configuration
    SMTP_HOST: Optional[str] = None
    SMTP_PORT: int = 587
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    EMAIL_FROM: str = "<EMAIL>"
    
    # LLM Integration
    OPENAI_API_KEY: Optional[str] = None
    ANTHROPIC_API_KEY: Optional[str] = None
    GOOGLE_AI_API_KEY: Optional[str] = None
    GOOGLE_API_KEY: Optional[str] = None  # Legacy support
    GOOGLE_AI_MODEL: str = "gemini-1.5-flash"
    GEMINI_MODEL: Optional[str] = None  # Legacy support
    GOOGLE_AI_TEMPERATURE: Optional[str] = None  # Legacy support
    GOOGLE_AI_MAX_TOKENS: Optional[str] = None  # Legacy support
    
    # Restaurant Configuration
    DEFAULT_RESTAURANT_NAME: str = "RestroManage Demo"
    DEFAULT_TAX_RATE: float = 0.20
    DEFAULT_CURRENCY: str = "GBP"
    
    # Monitoring and Analytics
    SENTRY_DSN: Optional[str] = None
    ENABLE_METRICS: bool = True
    METRICS_PORT: int = 8000
    
    # Rate Limiting
    RATE_LIMIT_REQUESTS: int = 100
    RATE_LIMIT_WINDOW: int = 60  # seconds
    
    # Cache Configuration
    CACHE_TTL: int = 900  # 15 minutes
    ENABLE_CACHE: bool = True
    
    # Development Settings
    DEBUG: bool = False
    RELOAD: bool = False
    WORKERS: int = 4
    ENVIRONMENT: str = "production"
    
    # Health Check Configuration
    HEALTH_CHECK_INTERVAL: int = 30  # seconds
    HEALTH_CHECK_TIMEOUT: int = 10   # seconds
    
    # Backup Configuration
    BACKUP_ENABLED: bool = True
    BACKUP_INTERVAL: int = 24  # hours
    BACKUP_RETENTION_DAYS: int = 30
    
    class Config:
        case_sensitive = True
        env_file = ".env"
        extra = "allow"  # Allow extra fields for backward compatibility

# Global settings instance
settings = Settings()

# Database URL validation
def get_database_url() -> str:
    """Get the appropriate database URL for the environment"""
    if settings.ENVIRONMENT == "production":
        if not settings.DATABASE_URL.startswith("postgresql"):
            raise ValueError("Production environment requires PostgreSQL database")
    return settings.DATABASE_URL

# Security validation
def validate_production_security():
    """Validate security settings for production"""
    if settings.ENVIRONMENT == "production":
        if settings.SECRET_KEY == "09d25e094faa6ca2556c818166b7a9563b93f7099f6f0f4caa6cf63b88e8d3e7":
            raise ValueError("Default SECRET_KEY must be changed in production")
        if settings.DEBUG:
            raise ValueError("DEBUG must be False in production")
        if len(settings.SECRET_KEY) < 32:
            raise ValueError("SECRET_KEY must be at least 32 characters long")

# CORS configuration
def get_cors_config() -> dict:
    """Get CORS configuration"""
    return {
        "allow_origins": settings.ALLOWED_ORIGINS,
        "allow_credentials": True,
        "allow_methods": settings.ALLOWED_METHODS,
        "allow_headers": settings.ALLOWED_HEADERS,
    }

# Logging configuration
def get_logging_config() -> dict:
    """Get logging configuration"""
    return {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "default": {
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            },
            "json": {
                "format": "%(asctime)s %(name)s %(levelname)s %(message)s",
                "class": "pythonjsonlogger.jsonlogger.JsonFormatter",
            },
        },
        "handlers": {
            "default": {
                "formatter": "json" if settings.LOG_FORMAT == "json" else "default",
                "class": "logging.StreamHandler",
                "stream": "ext://sys.stdout",
            },
        },
        "root": {
            "level": settings.LOG_LEVEL,
            "handlers": ["default"],
        },
    }
