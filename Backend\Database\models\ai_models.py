"""
AI models for RestroManage database.
Corresponds to app/models/ai_models.py Pydantic models.
"""

from sqlalchemy import Column, String, <PERSON>olean, Float, Text, JSON, ForeignKey, Integer, DateTime
from sqlalchemy.orm import relationship
from .base import BaseModel, TimestampMixin, StatusMixin

class AIRequest(BaseModel, TimestampMixin, StatusMixin):
    """
    AI request model for tracking AI service usage.
    Corresponds to AIRequest Pydantic model in app/models/ai_models.py
    """
    __tablename__ = "ai_requests"
    
    # Restaurant and user association
    restaurant_id = Column(String(36), ForeignKey("restaurants.id"), nullable=False, index=True)
    user_id = Column(String(36), ForeignKey("users.id"), nullable=True, index=True)
    
    # Request identification
    request_id = Column(String(100), nullable=False, unique=True, index=True)
    session_id = Column(String(100), nullable=True, index=True)
    
    # AI service details
    service_type = Column(String(50), nullable=False, index=True)
    # Types: menu_optimization, demand_forecasting, customer_insights, inventory_management
    model_name = Column(String(100), nullable=False)
    model_version = Column(String(20), nullable=True)
    
    # Request details
    request_type = Column(String(20), nullable=False)  # analysis, prediction, recommendation, generation
    input_data = Column(JSON, nullable=False)          # Input parameters and data
    input_size = Column(Integer, nullable=True)        # Size of input data in bytes
    
    # Processing information
    processing_status = Column(String(20), default="pending", nullable=False, index=True)
    # Status: pending, processing, completed, failed, cancelled
    processing_start = Column(DateTime(timezone=True), nullable=True)
    processing_end = Column(DateTime(timezone=True), nullable=True)
    processing_duration = Column(Float, nullable=True)  # seconds
    
    # Resource usage
    compute_units_used = Column(Float, nullable=True)
    memory_usage_mb = Column(Float, nullable=True)
    api_calls_made = Column(Integer, default=1, nullable=False)
    
    # Cost tracking
    estimated_cost = Column(Float, nullable=True)
    actual_cost = Column(Float, nullable=True)
    billing_tier = Column(String(20), nullable=True)
    
    # Quality metrics
    confidence_score = Column(Float, nullable=True)
    accuracy_score = Column(Float, nullable=True)
    relevance_score = Column(Float, nullable=True)
    
    # Error handling
    error_code = Column(String(50), nullable=True)
    error_message = Column(Text, nullable=True)
    retry_count = Column(Integer, default=0, nullable=False)
    
    # Context and metadata
    request_context = Column(JSON, nullable=True)      # Additional context information
    user_agent = Column(String(255), nullable=True)
    ip_address = Column(String(45), nullable=True)
    
    # Relationships
    restaurant = relationship("Restaurant")
    user = relationship("User", back_populates="ai_requests")
    responses = relationship("AIResponse", back_populates="request", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<AIRequest(id={self.id}, service_type={self.service_type}, status={self.processing_status})>"

class AIResponse(BaseModel, TimestampMixin):
    """
    AI response model for storing AI service results.
    Corresponds to AIResponse Pydantic model in app/models/ai_models.py
    """
    __tablename__ = "ai_responses"
    
    # Request association
    request_id = Column(String(36), ForeignKey("ai_requests.id"), nullable=False, index=True)
    
    # Response identification
    response_id = Column(String(100), nullable=False, unique=True, index=True)
    response_type = Column(String(50), nullable=False)
    # Types: analysis_result, prediction, recommendation, generated_content
    
    # Response data
    response_data = Column(JSON, nullable=False)        # Main response content
    response_size = Column(Integer, nullable=True)      # Size in bytes
    response_format = Column(String(20), nullable=False) # json, text, image, csv
    
    # Quality and confidence
    confidence_score = Column(Float, nullable=True)
    quality_score = Column(Float, nullable=True)
    completeness_score = Column(Float, nullable=True)
    
    # Metadata
    model_metadata = Column(JSON, nullable=True)        # Model-specific metadata
    processing_metadata = Column(JSON, nullable=True)   # Processing information
    
    # Validation and feedback
    is_validated = Column(Boolean, default=False, nullable=False)
    validation_score = Column(Float, nullable=True)
    user_feedback = Column(JSON, nullable=True)
    
    # Usage tracking
    times_accessed = Column(Integer, default=0, nullable=False)
    last_accessed = Column(DateTime(timezone=True), nullable=True)
    
    # Expiration and caching
    expires_at = Column(DateTime(timezone=True), nullable=True)
    is_cached = Column(Boolean, default=True, nullable=False)
    cache_key = Column(String(255), nullable=True, index=True)
    
    # Relationships
    request = relationship("AIRequest", back_populates="responses")
    
    def __repr__(self):
        return f"<AIResponse(id={self.id}, response_type={self.response_type}, confidence_score={self.confidence_score})>"

class AIUsageStats(BaseModel, TimestampMixin):
    """
    AI usage statistics model for tracking and billing.
    Corresponds to AIUsageStats Pydantic model in app/models/ai_models.py
    """
    __tablename__ = "ai_usage_stats"
    
    # Restaurant association
    restaurant_id = Column(String(36), ForeignKey("restaurants.id"), nullable=False, index=True)
    
    # Time period
    period_start = Column(DateTime(timezone=True), nullable=False, index=True)
    period_end = Column(DateTime(timezone=True), nullable=False, index=True)
    period_type = Column(String(20), nullable=False)  # hourly, daily, weekly, monthly
    
    # Service usage breakdown
    total_requests = Column(Integer, default=0, nullable=False)
    successful_requests = Column(Integer, default=0, nullable=False)
    failed_requests = Column(Integer, default=0, nullable=False)
    
    # Service type breakdown
    menu_optimization_requests = Column(Integer, default=0, nullable=False)
    forecasting_requests = Column(Integer, default=0, nullable=False)
    analytics_requests = Column(Integer, default=0, nullable=False)
    recommendation_requests = Column(Integer, default=0, nullable=False)
    
    # Resource consumption
    total_compute_units = Column(Float, default=0.0, nullable=False)
    total_processing_time = Column(Float, default=0.0, nullable=False)  # seconds
    average_processing_time = Column(Float, nullable=True)
    peak_processing_time = Column(Float, nullable=True)
    
    # Data volume
    total_input_size = Column(Integer, default=0, nullable=False)   # bytes
    total_output_size = Column(Integer, default=0, nullable=False)  # bytes
    average_request_size = Column(Float, nullable=True)
    
    # Cost tracking
    total_cost = Column(Float, default=0.0, nullable=False)
    cost_by_service = Column(JSON, nullable=True)
    billing_tier = Column(String(20), nullable=True)
    
    # Performance metrics
    average_confidence_score = Column(Float, nullable=True)
    average_quality_score = Column(Float, nullable=True)
    success_rate = Column(Float, nullable=True)
    
    # Usage patterns
    peak_usage_hour = Column(Integer, nullable=True)
    most_used_service = Column(String(50), nullable=True)
    user_adoption_rate = Column(Float, nullable=True)
    
    # Quota and limits
    quota_limit = Column(Integer, nullable=True)
    quota_used = Column(Integer, default=0, nullable=False)
    quota_remaining = Column(Integer, nullable=True)
    quota_reset_date = Column(DateTime(timezone=True), nullable=True)
    
    # Trends and insights
    usage_trend = Column(String(20), nullable=True)     # increasing, decreasing, stable
    cost_trend = Column(String(20), nullable=True)
    efficiency_trend = Column(String(20), nullable=True)
    
    # Relationships
    restaurant = relationship("Restaurant")
    
    def __repr__(self):
        return f"<AIUsageStats(id={self.id}, period_type={self.period_type}, total_requests={self.total_requests})>"
