import logger from "@/utils/logger";
/**
 * Mock Data Service for Registration Integration
 * Manages registered user data and integrates with existing AuthContext
 */

import { RestaurantRegistrationData, RestaurantSetupData } from "@/types/restaurantSetup";
import fileStorageService, { StoredRestaurantData } from "./fileStorageService";
import dataService, { RestaurantData, UserData } from "./dataService";

// Extended interfaces for mock data integration
export interface RegisteredUser {
  id: string;
  email: string;
  password: string; // In real app, this would be hashed
  registrationData: RestaurantRegistrationData;
  setupData?: RestaurantSetupData;
  isSetupComplete: boolean;
  createdAt: string;
  updatedAt: string;
  status: 'pending' | 'active' | 'suspended';
}

export interface MockRestaurant {
  id: string;
  code: string;
  name: string;
  logo: string;
  address: string;
  phone: string;
  email: string;
  vatRate: number;
  currency: string;
  isActive: boolean;
  // Extended fields from registration
  ownerName?: string;
  businessLicenseNumber?: string;
  restaurantType?: string;
  setupData?: RestaurantSetupData;
  hasData?: boolean;
  // Subscription fields
  subscriptionPlan: "basic" | "pro" | "customized";
  subscriptionStatus: "active" | "trial" | "expired" | "cancelled";
  subscriptionExpiresAt?: string;
  customizedFeatures?: string[];
  billingInfo?: {
    monthlyPrice: number;
    currency: string;
    nextBillingDate: string;
  };
}

export interface MockUser {
  id: string;
  name: string;
  email: string;
  phone: string;
  restaurant_id: string;
  restaurant_name: string;
  role: 'admin' | 'manager' | 'waiter' | 'chef' | 'hostess' | 'bartender' | 'staff';
  position: string;
  pin: string;
  status: string;
  hireDate?: string;
  performance?: number;
  accessLevel?: 'full' | 'limited';
}

// In-memory storage for registered users
let registeredUsers: RegisteredUser[] = [];
let mockRestaurants: MockRestaurant[] = [];
let mockUsers: MockUser[] = [];

// Initialize with existing mock data and load from persistent storage
export const initializeMockData = async (existingRestaurants: any[], existingUsers: any[]) => {
  logger.info('Initializing with existing data', 'MockDataService');

  // Load data from persistent storage first
  try {
    const persistentRestaurants = await dataService.getRestaurants();
    const persistentUsers = await dataService.getUsers();

    logger.info('Loaded persistent data', 'MockDataService', {
      persistentRestaurants: persistentRestaurants.length,
      persistentUsers: persistentUsers.length
    });

    // Convert persistent restaurants to mock format
    mockRestaurants = persistentRestaurants.map(restaurant => ({
      id: restaurant.id,
      code: restaurant.code,
      name: restaurant.name,
      logo: restaurant.logo,
      address: restaurant.address,
      phone: restaurant.phone,
      email: restaurant.email,
      vatRate: restaurant.vatRate,
      currency: restaurant.currency,
      isActive: restaurant.isActive,
      ownerName: restaurant.ownerName,
      businessLicenseNumber: restaurant.businessLicenseNumber,
      restaurantType: restaurant.restaurantType,
      setupData: restaurant.setupData,
      hasData: restaurant.hasData,
      // Include subscription fields
      subscriptionPlan: restaurant.subscriptionPlan || "basic",
      subscriptionStatus: restaurant.subscriptionStatus || "trial",
      subscriptionExpiresAt: restaurant.subscriptionExpiresAt,
      customizedFeatures: restaurant.customizedFeatures,
      billingInfo: restaurant.billingInfo,
    }));

    // Convert persistent users to mock format
    mockUsers = persistentUsers.map(user => ({
      id: user.id,
      name: user.name,
      email: user.email,
      phone: user.phone,
      restaurant_id: user.restaurant_id,
      restaurant_name: user.restaurant_name,
      role: user.role as 'admin' | 'manager' | 'waiter' | 'chef' | 'hostess' | 'bartender' | 'staff',
      position: user.position,
      pin: user.pin,
      status: user.status,
      hireDate: user.hireDate,
      performance: user.performance,
      accessLevel: user.accessLevel,
    }));

  } catch (error) {
    logger.logError(error, 'loading persistent data', 'MockDataService');

    // Fallback to existing data if persistent storage fails
    mockRestaurants = existingRestaurants.map(restaurant => ({
      ...restaurant,
      ownerName: restaurant.ownerName || 'Restaurant Owner',
      businessLicenseNumber: restaurant.businessLicenseNumber || 'BL-' + restaurant.id,
      restaurantType: restaurant.restaurantType || 'restaurant',
      // Ensure subscription fields are included
      subscriptionPlan: restaurant.subscriptionPlan || "basic",
      subscriptionStatus: restaurant.subscriptionStatus || "trial",
      subscriptionExpiresAt: restaurant.subscriptionExpiresAt,
      customizedFeatures: restaurant.customizedFeatures,
      billingInfo: restaurant.billingInfo,
    }));

    mockUsers = [...existingUsers];
  }

  logger.info('Initialized with combined data', 'MockDataService', {
    restaurants: mockRestaurants.length,
    users: mockUsers.length,
    registeredUsers: registeredUsers.length
  });
};

// Generate unique IDs
const generateId = (): string => {
  return Date.now().toString() + Math.random().toString(36).substr(2, 9);
};

// Generate unique restaurant code with validation
const generateRestaurantCode = (restaurantName: string): string => {
  const prefix = restaurantName
    .split(' ')
    .map(word => word.charAt(0).toUpperCase())
    .join('')
    .substr(0, 3);

  let attempts = 0;
  const maxAttempts = 100;

  while (attempts < maxAttempts) {
    const suffix = Math.floor(Math.random() * 900) + 100;
    const code = `${prefix}${suffix}`;

    // Check if code already exists in mock restaurants
    const existsInMock = mockRestaurants.find(r => r.code === code);

    // Check if code exists in existing restaurants from AuthContext
    const existingRestaurants = getAllExistingRestaurants();
    const existsInExisting = existingRestaurants.find(r => r.code === code);

    if (!existsInMock && !existsInExisting) {
      logger.dataOperation('generate', 'unique restaurant code', 'MockDataService', {
        code,
        attempts: attempts + 1,
        restaurantName
      });
      return code;
    }

    attempts++;
  }

  // Fallback: use timestamp-based code if all attempts failed
  const fallbackCode = `${prefix}${Date.now().toString().slice(-3)}`;
  logger.warn('Using fallback restaurant code after max attempts', 'MockDataService', {
    fallbackCode,
    maxAttempts,
    restaurantName
  });
  return fallbackCode;
};

// Get existing restaurants from various sources for code validation
const getAllExistingRestaurants = () => {
  // This will be populated by AuthContext when available
  if (typeof window !== 'undefined' && (window as any).existingRestaurants) {
    return (window as any).existingRestaurants;
  }
  return [];
};

// Store registration data
export const storeRegistrationData = (
  registrationData: RestaurantRegistrationData,
  setupData?: RestaurantSetupData
): RegisteredUser => {
  logger.dataOperation('store', 'registration data', 'MockDataService', { email: registrationData.email });

  const userId = generateId();
  const now = new Date().toISOString();

  const registeredUser: RegisteredUser = {
    id: userId,
    email: registrationData.email,
    password: registrationData.password, // In real app, hash this
    registrationData,
    setupData,
    isSetupComplete: !!setupData,
    createdAt: now,
    updatedAt: now,
    status: 'active',
  };

  // Check if user already exists
  const existingIndex = registeredUsers.findIndex(user => user.email === registrationData.email);
  if (existingIndex >= 0) {
    registeredUsers[existingIndex] = registeredUser;
    logger.info('Updated existing user registration', 'MockDataService');
  } else {
    registeredUsers.push(registeredUser);
    logger.info('Added new user registration', 'MockDataService');
  }

  // If setup is complete, create restaurant and user entries
  if (setupData) {
    // Note: This is async but we'll handle it in the background
    createRestaurantAndUserEntries(registeredUser).then(credentials => {
      // Store credentials for later retrieval
      localStorage.setItem(`restaurant_credentials_${registrationData.email}`, JSON.stringify(credentials));
    }).catch(error => {
      logger.logError(error, 'creating restaurant entries in background', 'MockDataService');
    });
  }

  // Persist to localStorage
  localStorage.setItem('registeredUsers', JSON.stringify(registeredUsers));

  return registeredUser;
};

// Create restaurant and user entries in mock data
const createRestaurantAndUserEntries = async (registeredUser: RegisteredUser): Promise<{ restaurantCode: string; ownerPin: string }> => {
  const { registrationData, setupData } = registeredUser;

  if (!setupData) {
    throw new Error('Setup data is required to create restaurant entries');
  }

  logger.dataOperation('create', 'restaurant and user entries', 'MockDataService');

  // Generate credentials
  const restaurantCode = generateRestaurantCode(registrationData.restaurantName);
  const ownerPin = generateManagerPin();

  // Use the data service to add the restaurant persistently
  const result = await dataService.addRestaurant(registrationData, setupData, {
    restaurantCode,
    ownerPin
  });

  if (!result.success || !result.restaurant || !result.user) {
    throw new Error('Failed to create restaurant entries in persistent storage');
  }

  // Add to local mock data for immediate availability
  const newRestaurant: MockRestaurant = {
    id: result.restaurant.id,
    code: result.restaurant.code,
    name: result.restaurant.name,
    logo: result.restaurant.logo,
    address: result.restaurant.address,
    phone: result.restaurant.phone,
    email: result.restaurant.email,
    vatRate: result.restaurant.vatRate,
    currency: result.restaurant.currency,
    isActive: result.restaurant.isActive,
    ownerName: result.restaurant.ownerName,
    businessLicenseNumber: result.restaurant.businessLicenseNumber,
    restaurantType: result.restaurant.restaurantType,
    setupData: result.restaurant.setupData,
    // Include subscription fields with defaults for new restaurants
    subscriptionPlan: result.restaurant.subscriptionPlan || "basic",
    subscriptionStatus: result.restaurant.subscriptionStatus || "trial",
    subscriptionExpiresAt: result.restaurant.subscriptionExpiresAt,
    customizedFeatures: result.restaurant.customizedFeatures,
    billingInfo: result.restaurant.billingInfo,
  };

  const newOwner: MockUser = {
    id: result.user.id,
    name: result.user.name,
    email: result.user.email,
    phone: result.user.phone,
    restaurant_id: result.user.restaurant_id,
    restaurant_name: result.user.restaurant_name,
    role: result.user.role as 'admin' | 'manager' | 'waiter' | 'chef' | 'hostess' | 'bartender' | 'staff',
    position: result.user.position,
    pin: result.user.pin,
    status: result.user.status,
    hireDate: result.user.hireDate,
    performance: result.user.performance,
    accessLevel: result.user.accessLevel,
  };

  // Add to mock data arrays for immediate access
  mockRestaurants.push(newRestaurant);
  mockUsers.push(newOwner);

  logger.dataOperation('create', 'restaurant entry', 'MockDataService', {
    id: result.restaurant.id,
    code: result.restaurant.code,
    name: result.restaurant.name
  });

  logger.dataOperation('create', 'owner user entry', 'MockDataService', {
    id: result.user.id,
    name: result.user.name,
    pin: result.user.pin
  });

  // Store the generated credentials for the user
  const credentials = { restaurantCode, ownerPin };
  localStorage.setItem(`restaurant_credentials_${registrationData.email}`, JSON.stringify(credentials));

  return credentials;
};

// Generate manager PIN
const generateManagerPin = (): string => {
  return Math.floor(1000 + Math.random() * 9000).toString();
};

// Validate login credentials
export const validateLoginCredentials = (email: string, password: string): RegisteredUser | null => {
  logger.debug('Validating credentials attempt', 'MockDataService', { email });

  const user = registeredUsers.find(user =>
    user.email === email &&
    user.password === password &&
    user.status === 'active'
  );

  if (user) {
    logger.authentication('validate credentials', 'success', 'MockDataService');
    return user;
  }

  logger.authentication('validate credentials', 'failure', 'MockDataService');
  return null;
};

// Find restaurant by code
export const findRestaurantByCode = (code: string): MockRestaurant | null => {
  return mockRestaurants.find(restaurant => restaurant.code === code) || null;
};

// Find user by email
export const findUserByEmail = (email: string): MockUser | null => {
  return mockUsers.find(user => user.email === email) || null;
};

// Get all restaurants (for AuthContext)
export const getAllRestaurants = (): MockRestaurant[] => {
  return [...mockRestaurants];
};

// Get all users (for AuthContext)
export const getAllUsers = (): MockUser[] => {
  return [...mockUsers];
};

// Get users for specific restaurant
export const getRestaurantUsers = (restaurantId: string): MockUser[] => {
  return mockUsers.filter(user => user.restaurant_id === restaurantId);
};

// Load persisted data from localStorage
export const loadPersistedData = () => {
  try {
    const stored = localStorage.getItem('registeredUsers');
    if (stored) {
      registeredUsers = JSON.parse(stored);
      logger.dataOperation('load', 'persisted users', 'MockDataService', { count: registeredUsers.length });

      // Recreate restaurant and user entries for completed setups
      registeredUsers.forEach(user => {
        if (user.isSetupComplete && user.setupData) {
          // Check if restaurant already exists
          const existingRestaurant = mockRestaurants.find(r => r.email === user.email);
          if (!existingRestaurant) {
            // Handle async operation in background
            createRestaurantAndUserEntries(user).catch(error => {
              logger.logError(error, 'recreating restaurant entries from persisted data', 'MockDataService');
            });
          }
        }
      });
    }
  } catch (error) {
    logger.logError(error, 'loading persisted data', 'MockDataService');
  }
};

// Update setup data for existing user
export const updateUserSetupData = async (email: string, setupData: RestaurantSetupData): Promise<{ success: boolean; credentials?: { restaurantCode: string; ownerPin: string } }> => {
  logger.dataOperation('update', 'setup data', 'MockDataService', { email });

  const userIndex = registeredUsers.findIndex(user => user.email === email);
  if (userIndex >= 0) {
    registeredUsers[userIndex].setupData = setupData;
    registeredUsers[userIndex].isSetupComplete = true;
    registeredUsers[userIndex].updatedAt = new Date().toISOString();

    try {
      // Create restaurant and user entries and get credentials
      const credentials = await createRestaurantAndUserEntries(registeredUsers[userIndex]);

      // Persist to localStorage
      localStorage.setItem('registeredUsers', JSON.stringify(registeredUsers));

      logger.dataOperation('update', 'setup data', 'MockDataService', {
        result: 'success',
        restaurantCode: credentials.restaurantCode
      });
      return { success: true, credentials };
    } catch (error) {
      logger.logError(error, 'updating setup data', 'MockDataService');
      return { success: false };
    }
  }

  logger.warn('User not found for setup update', 'MockDataService');
  return { success: false };
};

// Get registered user by email
export const getRegisteredUser = (email: string): RegisteredUser | null => {
  return registeredUsers.find(user => user.email === email) || null;
};

// Get restaurant credentials for a user
export const getRestaurantCredentials = (email: string): { restaurantCode: string; ownerPin: string } | null => {
  try {
    const stored = localStorage.getItem(`restaurant_credentials_${email}`);
    if (stored) {
      return JSON.parse(stored);
    }
  } catch (error) {
    logger.logError(error, 'getting restaurant credentials', 'MockDataService');
  }
  return null;
};

// Set existing restaurants for code validation (called by AuthContext)
export const setExistingRestaurants = (restaurants: any[]) => {
  if (typeof window !== 'undefined') {
    (window as any).existingRestaurants = restaurants;
  }
};

// Debug function to log current state
export const debugMockData = () => {
  logger.debug('Current state', 'MockDataService', {
    registeredUsers: registeredUsers.length,
    mockRestaurants: mockRestaurants.length,
    mockUsers: mockUsers.length,
    registeredUserEmails: registeredUsers.map(u => u.email),
    restaurantCodes: mockRestaurants.map(r => r.code),
    userPins: mockUsers.map(u => ({ name: u.name, pin: u.pin, role: u.role }))
  });
};

// Export for external access
export const getMockDataState = () => ({
  registeredUsers: [...registeredUsers],
  mockRestaurants: [...mockRestaurants],
  mockUsers: [...mockUsers],
});
