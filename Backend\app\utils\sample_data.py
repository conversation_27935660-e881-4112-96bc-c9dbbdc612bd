"""
Sample data for testing EPOS enhancements including discounts, split bills, and enhanced table management
"""

from datetime import datetime, timedelta
from app.models.discounts import PromoCode, DiscountType, DiscountScope, Campaign
from app.models.tables import Table, TableStatus, TableLocation
from app.utils.storage import create

def create_sample_promo_codes():
    """Create sample promo codes for testing"""
    
    # Percentage discount on total order
    promo_code_1 = {
        "id": "promo_1",
        "code": "SAVE10",
        "name": "10% Off Total Order",
        "description": "Get 10% off your entire order",
        "discount_type": DiscountType.PERCENTAGE,
        "discount_value": 10.0,
        "scope": DiscountScope.ORDER_TOTAL,
        "minimum_spend": 20.0,
        "maximum_discount": 50.0,
        "start_date": (datetime.now() - timedelta(days=30)).isoformat(),
        "end_date": (datetime.now() + timedelta(days=30)).isoformat(),
        "usage_limit": 100,
        "usage_limit_per_customer": 1,
        "is_stackable": False,
        "is_active": True,
        "usage_count": 5,
        "created_at": datetime.now().isoformat(),
        "updated_at": datetime.now().isoformat()
    }
    
    # Fixed amount discount
    promo_code_2 = {
        "id": "promo_2",
        "code": "WELCOME5",
        "name": "£5 Off Welcome Offer",
        "description": "Get £5 off your first order",
        "discount_type": DiscountType.FIXED_AMOUNT,
        "discount_value": 5.0,
        "scope": DiscountScope.ORDER_TOTAL,
        "minimum_spend": 15.0,
        "start_date": (datetime.now() - timedelta(days=60)).isoformat(),
        "end_date": (datetime.now() + timedelta(days=60)).isoformat(),
        "usage_limit": 500,
        "usage_limit_per_customer": 1,
        "is_stackable": True,
        "is_active": True,
        "usage_count": 23,
        "created_at": datetime.now().isoformat(),
        "updated_at": datetime.now().isoformat()
    }
    
    # Category-specific discount
    promo_code_3 = {
        "id": "promo_3",
        "code": "DESSERT20",
        "name": "20% Off Desserts",
        "description": "Get 20% off all desserts",
        "discount_type": DiscountType.PERCENTAGE,
        "discount_value": 20.0,
        "scope": DiscountScope.CATEGORY,
        "applicable_categories": ["dessert"],
        "start_date": (datetime.now() - timedelta(days=7)).isoformat(),
        "end_date": (datetime.now() + timedelta(days=14)).isoformat(),
        "usage_limit": 50,
        "is_stackable": True,
        "is_active": True,
        "usage_count": 8,
        "created_at": datetime.now().isoformat(),
        "updated_at": datetime.now().isoformat()
    }
    
    # Happy hour discount
    promo_code_4 = {
        "id": "promo_4",
        "code": "HAPPYHOUR",
        "name": "Happy Hour 15% Off",
        "description": "15% off during happy hour (3-6 PM)",
        "discount_type": DiscountType.PERCENTAGE,
        "discount_value": 15.0,
        "scope": DiscountScope.ORDER_TOTAL,
        "minimum_spend": 10.0,
        "maximum_discount": 25.0,
        "start_date": datetime.now().isoformat(),
        "end_date": (datetime.now() + timedelta(days=365)).isoformat(),
        "usage_limit": 1000,
        "usage_limit_per_customer": 5,
        "is_stackable": False,
        "is_active": True,
        "usage_count": 45,
        "created_at": datetime.now().isoformat(),
        "updated_at": datetime.now().isoformat()
    }
    
    # Expired promo code for testing
    promo_code_5 = {
        "id": "promo_5",
        "code": "EXPIRED",
        "name": "Expired Discount",
        "description": "This discount has expired",
        "discount_type": DiscountType.PERCENTAGE,
        "discount_value": 25.0,
        "scope": DiscountScope.ORDER_TOTAL,
        "start_date": (datetime.now() - timedelta(days=60)).isoformat(),
        "end_date": (datetime.now() - timedelta(days=30)).isoformat(),
        "usage_limit": 100,
        "is_stackable": False,
        "is_active": True,
        "usage_count": 12,
        "created_at": datetime.now().isoformat(),
        "updated_at": datetime.now().isoformat()
    }
    
    promo_codes = [promo_code_1, promo_code_2, promo_code_3, promo_code_4, promo_code_5]
    
    for promo_code in promo_codes:
        create("promo_codes", promo_code)
    
    print(f"Created {len(promo_codes)} sample promo codes")

def create_sample_campaigns():
    """Create sample campaigns for testing"""
    
    campaign_1 = {
        "id": "campaign_1",
        "name": "Summer Promotion 2024",
        "description": "Summer discount campaign with multiple promo codes",
        "campaign_type": "seasonal",
        "start_date": (datetime.now() - timedelta(days=30)).isoformat(),
        "end_date": (datetime.now() + timedelta(days=60)).isoformat(),
        "target_audience": "All customers",
        "budget": 1000.0,
        "promo_codes": ["promo_1", "promo_3"],
        "is_active": True,
        "total_usage": 13,
        "total_discount_given": 127.50,
        "created_at": datetime.now().isoformat(),
        "updated_at": datetime.now().isoformat()
    }
    
    campaign_2 = {
        "id": "campaign_2",
        "name": "New Customer Welcome",
        "description": "Welcome campaign for new customers",
        "campaign_type": "loyalty",
        "start_date": (datetime.now() - timedelta(days=90)).isoformat(),
        "end_date": (datetime.now() + timedelta(days=90)).isoformat(),
        "target_audience": "New customers",
        "budget": 2500.0,
        "promo_codes": ["promo_2"],
        "is_active": True,
        "total_usage": 23,
        "total_discount_given": 115.0,
        "created_at": datetime.now().isoformat(),
        "updated_at": datetime.now().isoformat()
    }
    
    campaigns = [campaign_1, campaign_2]
    
    for campaign in campaigns:
        create("campaigns", campaign)
    
    print(f"Created {len(campaigns)} sample campaigns")

def create_enhanced_sample_tables():
    """Create enhanced sample tables with new features"""
    
    tables = [
        {
            "id": "table_1",
            "number": 1,
            "capacity": 4,
            "location": TableLocation.INDOOR,
            "section": "Main Dining",
            "shape": "round",
            "status": TableStatus.AVAILABLE,
            "current_order_id": None,
            "reserved_until": None,
            "last_cleaned": (datetime.now() - timedelta(hours=2)).isoformat(),
            "notes": None,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        },
        {
            "id": "table_2",
            "number": 2,
            "capacity": 2,
            "location": TableLocation.INDOOR,
            "section": "Main Dining",
            "shape": "square",
            "status": TableStatus.OCCUPIED,
            "current_order_id": "order_1",
            "reserved_until": None,
            "last_cleaned": (datetime.now() - timedelta(hours=3)).isoformat(),
            "notes": None,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        },
        {
            "id": "table_3",
            "number": 3,
            "capacity": 6,
            "location": TableLocation.OUTDOOR,
            "section": "Terrace",
            "shape": "rectangular",
            "status": TableStatus.RESERVED,
            "current_order_id": None,
            "reserved_until": (datetime.now() + timedelta(hours=2)).isoformat(),
            "last_cleaned": (datetime.now() - timedelta(hours=1)).isoformat(),
            "notes": "Reserved for Johnson party",
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        },
        {
            "id": "table_4",
            "number": 4,
            "capacity": 4,
            "location": TableLocation.INDOOR,
            "section": "Main Dining",
            "shape": "round",
            "status": TableStatus.CLEANING,
            "current_order_id": None,
            "reserved_until": None,
            "last_cleaned": datetime.now().isoformat(),
            "notes": "Deep cleaning in progress",
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        },
        {
            "id": "table_5",
            "number": 5,
            "capacity": 8,
            "location": TableLocation.PRIVATE_ROOM,
            "section": "VIP Room",
            "shape": "rectangular",
            "status": TableStatus.OUT_OF_SERVICE,
            "current_order_id": None,
            "reserved_until": None,
            "last_cleaned": (datetime.now() - timedelta(days=1)).isoformat(),
            "notes": "Chair needs repair",
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        },
        {
            "id": "table_6",
            "number": 6,
            "capacity": 2,
            "location": TableLocation.BAR,
            "section": "Bar Area",
            "shape": "round",
            "status": TableStatus.AVAILABLE,
            "current_order_id": None,
            "reserved_until": None,
            "last_cleaned": (datetime.now() - timedelta(minutes=30)).isoformat(),
            "notes": None,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
    ]
    
    for table in tables:
        create("tables", table)
    
    print(f"Created {len(tables)} enhanced sample tables")

def create_sample_promo_usage():
    """Create sample promo code usage records"""
    
    usage_records = [
        {
            "id": "usage_1",
            "promo_code_id": "promo_1",
            "order_id": "order_1",
            "customer_id": "customer_1",
            "discount_amount": 3.50,
            "used_at": (datetime.now() - timedelta(days=5)).isoformat()
        },
        {
            "id": "usage_2",
            "promo_code_id": "promo_2",
            "order_id": "order_2",
            "customer_id": "customer_2",
            "discount_amount": 5.00,
            "used_at": (datetime.now() - timedelta(days=3)).isoformat()
        },
        {
            "id": "usage_3",
            "promo_code_id": "promo_3",
            "order_id": "order_3",
            "customer_id": "customer_1",
            "discount_amount": 2.40,
            "used_at": (datetime.now() - timedelta(days=1)).isoformat()
        }
    ]
    
    for usage in usage_records:
        create("promo_code_usage", usage)
    
    print(f"Created {len(usage_records)} sample usage records")

def initialize_sample_data():
    """Initialize all sample data for EPOS enhancements"""
    print("Initializing sample data for EPOS enhancements...")
    
    create_sample_promo_codes()
    create_sample_campaigns()
    create_enhanced_sample_tables()
    create_sample_promo_usage()
    
    print("Sample data initialization complete!")

if __name__ == "__main__":
    initialize_sample_data()
