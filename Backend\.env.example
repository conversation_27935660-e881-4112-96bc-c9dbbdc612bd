# RestroManage Production Environment Configuration

# Database Configuration
# For production, use PostgreSQL:
DATABASE_URL=postgresql://restro_user:restro_password@localhost:5432/restro_manage
# For development, use SQLite:
# DATABASE_URL=sqlite+aiosqlite:///./restro_manage.db

# Redis Configuration (for caching and sessions)
REDIS_URL=redis://localhost:6379/0

# Security Configuration
SECRET_KEY=restro-manage-production-secret-key-2024-secure-32-chars-minimum
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173,https://yourdomain.com
ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
ALLOWED_HEADERS=*

# API Configuration
API_V1_STR=/api
PROJECT_NAME=RestroManage
VERSION=1.0.0
DESCRIPTION=RestroManage API - A comprehensive restaurant management system

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json
ENABLE_SQL_LOGGING=false

# File Upload Configuration
MAX_FILE_SIZE=10485760  # 10MB in bytes
UPLOAD_DIR=./uploads
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/svg+xml,application/pdf

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>

# LLM Integration
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key
GOOGLE_AI_API_KEY=your-google-ai-api-key
GOOGLE_AI_MODEL=gemini-1.5-flash

# Restaurant Configuration
DEFAULT_RESTAURANT_NAME=RestroManage Demo
DEFAULT_TAX_RATE=0.20
DEFAULT_CURRENCY=GBP

# Monitoring and Analytics
SENTRY_DSN=your-sentry-dsn-for-error-tracking
ENABLE_METRICS=true
METRICS_PORT=8000

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60  # seconds

# Cache Configuration
CACHE_TTL=900  # 15 minutes
ENABLE_CACHE=true

# Development Settings
DEBUG=false
RELOAD=false
WORKERS=4
ENVIRONMENT=production

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30  # seconds
HEALTH_CHECK_TIMEOUT=10   # seconds

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_INTERVAL=24  # hours
BACKUP_RETENTION_DAYS=30
