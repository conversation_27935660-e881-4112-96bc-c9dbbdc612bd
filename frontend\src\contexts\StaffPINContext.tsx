import logger from "@/utils/logger";
import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { toast } from "@/components/ui/sonner";
import { fetchCSV } from "@/utils/csvParser";
import { getClockStatus } from "@/services/clockStatusService";
import { useAuth } from "@/contexts/AuthContext";
import { checkBackendConnectivity, userApi } from "@/services/apiService";

// Define staff types
export type StaffWorkStatus = 'clocked-out' | 'clocked-in' | 'on-break';
export type StaffRole = 'admin' | 'manager' | 'staff' | 'waiter' | 'chef' | 'hostess' | 'bartender';

export interface StaffMember {
  id: string;
  name: string;
  role: StaffRole;
  position: string;
  pin: string;
  workStatus: StaffWorkStatus;
  email: string;
  phone: string;
  status: "active" | "inactive" | "on-leave";
  hireDate: string;
  performance: number;
  metrics?: {
    sales: number;
    tablesTurned: number;
    customerRating: number;
  };
  avatar?: string;
  currentShift?: {
    clockInTime: string;
    breakStartTime?: string;
    breakEndTime?: string;
    clockOutTime?: string;
  };
  timeLog?: {
    date: string;
    hoursWorked: number;
    breakTime: number;
  }[];
  accessLevel: 'full' | 'limited'; // full = admin/manager, limited = staff
}

// Define context type
interface StaffPINContextType {
  activeStaff: StaffMember | null;
  staffMembers: StaffMember[];
  verifyPIN: (pin: string) => Promise<boolean>;
  clockIn: (staffId: string) => void;
  clockOut: (staffId: string) => void;
  startBreak: (staffId: string) => void;
  endBreak: (staffId: string) => void;
  updateStaffPIN: (staffId: string, pin: string) => void;
  getStaffById: (staffId: string) => StaffMember | undefined;
  logoutStaff: () => void;
  getActiveStaffMembers: () => StaffMember[];
}

// Mock staff data with PINs
const MOCK_STAFF_MEMBERS: StaffMember[] = [
  {
    id: "1",
    name: "Michael Rodriguez",
    role: "waiter",
    position: "Head Waiter",
    pin: "1234",
    workStatus: "clocked-out",
    email: "<EMAIL>",
    phone: "(*************",
    status: "active",
    hireDate: "2022-03-15",
    performance: 92,
    metrics: {
      sales: 1250,
      tablesTurned: 48,
      customerRating: 4.8
    },
    accessLevel: "limited"
  },
  {
    id: "2",
    name: "Jennifer Smith",
    role: "waiter",
    position: "Waiter",
    pin: "2345",
    workStatus: "clocked-out",
    email: "<EMAIL>",
    phone: "(*************",
    status: "active",
    hireDate: "2022-05-20",
    performance: 78,
    metrics: {
      sales: 980,
      tablesTurned: 38,
      customerRating: 4.5
    },
    accessLevel: "limited"
  },
  {
    id: "3",
    name: "David Chen",
    role: "waiter",
    position: "Waiter",
    pin: "3456",
    workStatus: "clocked-out",
    email: "<EMAIL>",
    phone: "(*************",
    status: "on-leave",
    hireDate: "2022-08-10",
    performance: 65,
    metrics: {
      sales: 750,
      tablesTurned: 30,
      customerRating: 3.9
    },
    accessLevel: "limited"
  },
  {
    id: "4",
    name: "Maria Lopez",
    role: "chef",
    position: "Head Chef",
    pin: "4567",
    workStatus: "clocked-out",
    email: "<EMAIL>",
    phone: "(*************",
    status: "active",
    hireDate: "2021-11-05",
    performance: 88,
    accessLevel: "limited"
  },
  {
    id: "5",
    name: "Robert Johnson",
    role: "manager",
    position: "Floor Manager",
    pin: "5678",
    workStatus: "clocked-out",
    email: "<EMAIL>",
    phone: "(*************",
    status: "active",
    hireDate: "2021-06-15",
    performance: 95,
    accessLevel: "full"
  },
  {
    id: "6",
    name: "Admin User",
    role: "admin",
    position: "Restaurant Owner",
    pin: "0000",
    workStatus: "clocked-out",
    email: "<EMAIL>",
    phone: "(*************",
    status: "active",
    hireDate: "2020-01-01",
    performance: 100,
    accessLevel: "full"
  }
];

// Create context with default values
const StaffPINContext = createContext<StaffPINContextType>({
  activeStaff: null,
  staffMembers: [],
  verifyPIN: async () => false,
  clockIn: () => {},
  clockOut: () => {},
  startBreak: () => {},
  endBreak: () => {},
  updateStaffPIN: () => {},
  getStaffById: () => undefined,
  logoutStaff: () => {},
  getActiveStaffMembers: () => [],
});

interface StaffPINProviderProps {
  children: ReactNode;
}

export const StaffPINProvider = ({ children }: StaffPINProviderProps) => {
  const [staffMembers, setStaffMembers] = useState<StaffMember[]>([]);
  const [activeStaff, setActiveStaff] = useState<StaffMember | null>(null);
  const { restaurantUsers, currentRestaurant, isRestaurantAuthenticated } = useAuth();

  // Convert AuthContext users to StaffMembers and filter by current restaurant
  useEffect(() => {
    if (isRestaurantAuthenticated && currentRestaurant && restaurantUsers.length > 0) {
      logger.debug('Converting restaurant users to staff members', 'StaffPINContext', {
        restaurantId: currentRestaurant.id,
        userCount: restaurantUsers.length
      });

      // Convert users to staff members format
      const convertedStaff: StaffMember[] = restaurantUsers.map(user => ({
        id: user.id,
        name: user.name,
        role: user.role as StaffRole,
        position: user.position || user.role,
        pin: user.pin,
        workStatus: 'clocked-out' as StaffWorkStatus,
        email: user.email,
        phone: user.phone,
        status: user.status as "active" | "inactive" | "on-leave",
        hireDate: user.hireDate,
        performance: user.performance,
        accessLevel: user.accessLevel
      }));

      logger.debug('Converted staff members', 'StaffPINContext', {
        count: convertedStaff.length,
        staff: convertedStaff.map(s => ({ id: s.id, name: s.name, pin: s.pin, status: s.status }))
      });

      setStaffMembers(convertedStaff);

      // Check for active staff session
      const storedActiveStaff = localStorage.getItem('activeStaff');
      if (storedActiveStaff) {
        try {
          const parsedActiveStaff = JSON.parse(storedActiveStaff);
          // Verify the active staff belongs to current restaurant
          const staffExists = convertedStaff.find(s => s.id === parsedActiveStaff.id);
          if (staffExists) {
            logger.dataOperation('restore', 'active staff from localStorage', 'StaffPINContext');
            setActiveStaff(parsedActiveStaff);
          } else {
            logger.debug('Active staff not in current restaurant, clearing', 'StaffPINContext');
            localStorage.removeItem('activeStaff');
            setActiveStaff(null);
          }
        } catch (error) {
          logger.logError(error, 'parsing active staff from localStorage', 'StaffPINContext');
          localStorage.removeItem('activeStaff');
          setActiveStaff(null);
        }
      }
    } else {
      logger.debug('No restaurant authenticated or no users, clearing staff', 'StaffPINContext');
      setStaffMembers([]);
      setActiveStaff(null);
    }
  }, [restaurantUsers, currentRestaurant, isRestaurantAuthenticated]);

  // Note: We no longer save staff data to localStorage since it comes from AuthContext

  // Save active staff to localStorage whenever it changes
  useEffect(() => {
    if (activeStaff) {
      localStorage.setItem('activeStaff', JSON.stringify(activeStaff));

      // Also update the staff member in the staffMembers array to keep it in sync
      setStaffMembers(prev =>
        prev.map(staff => staff.id === activeStaff.id ? activeStaff : staff)
      );
    } else {
      localStorage.removeItem('activeStaff');
    }
  }, [activeStaff]);

  // Verify staff PIN
  const verifyPIN = async (pin: string): Promise<boolean> => {
    logger.authentication('PIN verification', 'success', 'StaffPINContext', { pin: '****' });

    if (!currentRestaurant) {
      logger.authentication('PIN verification', 'failure', 'StaffPINContext', {
        reason: 'no current restaurant'
      });
      return false;
    }

    try {
      // Try backend API first
      const isBackendAvailable = await checkBackendConnectivity();

      if (isBackendAvailable) {
        const response = await userApi.verifyStaffPin(currentRestaurant.id, pin);

        const typedResponse = response as { success: boolean; user?: any };
        if (typedResponse.success && typedResponse.user) {
          logger.authentication('PIN verification', 'success', 'StaffPINContext', {
            staffName: typedResponse.user.name,
            method: 'backend_api'
          });

          // Convert backend user to staff member format
          const staff: StaffMember = {
            id: typedResponse.user.id,
            name: typedResponse.user.name,
            role: typedResponse.user.role as StaffRole,
            position: typedResponse.user.position || typedResponse.user.role,
            pin: typedResponse.user.pin,
            workStatus: 'clocked-out' as StaffWorkStatus,
            email: typedResponse.user.email,
            phone: typedResponse.user.phone,
            status: typedResponse.user.status as "active" | "inactive" | "on-leave",
            hireDate: typedResponse.user.hireDate,
            performance: typedResponse.user.performance,
            accessLevel: typedResponse.user.accessLevel
          };

          // Handle clock status restoration
          const savedClockStatus = getClockStatus(staff.id);
          if (savedClockStatus && savedClockStatus.status) {
            let workStatus: StaffWorkStatus = 'clocked-out';
            if (savedClockStatus.status === 'clocked_in') {
              workStatus = 'clocked-in';
            } else if (savedClockStatus.status === 'on_break') {
              workStatus = 'on-break';
            }

            staff.workStatus = workStatus;
            toast.success(`Welcome back, ${staff.name}! Your ${workStatus} status has been restored.`);
          } else {
            toast.success(`Welcome, ${staff.name}!`);
          }

          setActiveStaff(staff);
          localStorage.setItem('activeStaff', JSON.stringify(staff));
          return true;
        } else {
          logger.authentication('PIN verification', 'failure', 'StaffPINContext', {
            reason: 'invalid PIN',
            method: 'backend_api'
          });
          return false;
        }
      }
    } catch (error) {
      logger.logError(error, 'backend PIN verification', 'StaffPINContext');
    }

    // Fallback to frontend verification
    logger.warn('Falling back to frontend PIN verification', 'StaffPINContext');

    const staff = staffMembers.find(s => s.pin === pin && s.status === "active");
    if (staff) {
      logger.authentication('PIN verification', 'success', 'StaffPINContext', {
        staffName: staff.name,
        method: 'frontend_fallback'
      });

      // Handle clock status restoration
      const savedClockStatus = getClockStatus(staff.id);
      if (savedClockStatus && savedClockStatus.status) {
        let workStatus: StaffWorkStatus = 'clocked-out';
        if (savedClockStatus.status === 'clocked_in') {
          workStatus = 'clocked-in';
        } else if (savedClockStatus.status === 'on_break') {
          workStatus = 'on-break';
        }

        const updatedStaff = { ...staff, workStatus };
        setStaffMembers(prev => prev.map(s => s.id === staff.id ? updatedStaff : s));
        setActiveStaff(updatedStaff);
        localStorage.setItem('activeStaff', JSON.stringify(updatedStaff));
        toast.success(`Welcome back, ${staff.name}! Your ${workStatus} status has been restored.`);
      } else {
        setActiveStaff(staff);
        localStorage.setItem('activeStaff', JSON.stringify(staff));
        toast.success(`Welcome, ${staff.name}!`);
      }

      return true;
    }

    logger.authentication('PIN verification', 'failure', 'StaffPINContext', {
      reason: 'no staff found',
      method: 'frontend_fallback'
    });
    return false;
  };

  // Get staff by ID
  const getStaffById = (staffId: string): StaffMember | undefined => {
    return staffMembers.find(s => s.id === staffId);
  };

  // Update staff PIN
  const updateStaffPIN = (staffId: string, pin: string) => {
    setStaffMembers(prev =>
      prev.map(staff =>
        staff.id === staffId ? { ...staff, pin } : staff
      )
    );
    toast.success("PIN updated successfully");
  };

  // Clock in staff member
  const clockIn = (staffId: string) => {
    const now = new Date().toISOString();
    setStaffMembers(prev =>
      prev.map(staff => {
        if (staff.id === staffId) {
          const updatedStaff = {
            ...staff,
            workStatus: 'clocked-in' as StaffWorkStatus,
            currentShift: {
              clockInTime: now
            }
          };

          // If this is the active staff, update that too
          if (activeStaff && activeStaff.id === staffId) {
            setActiveStaff(updatedStaff);
          }

          return updatedStaff;
        }
        return staff;
      })
    );
    toast.success("Clocked in successfully");
  };

  // Clock out staff member
  const clockOut = (staffId: string) => {
    const now = new Date().toISOString();
    setStaffMembers(prev =>
      prev.map(staff => {
        if (staff.id === staffId && staff.currentShift) {
          // Calculate hours worked
          const clockInTime = new Date(staff.currentShift.clockInTime);
          const clockOutTime = new Date(now);

          // Calculate total break time
          let breakTimeMinutes = 0;
          if (staff.currentShift.breakStartTime && staff.currentShift.breakEndTime) {
            const breakStart = new Date(staff.currentShift.breakStartTime);
            const breakEnd = new Date(staff.currentShift.breakEndTime);
            breakTimeMinutes = (breakEnd.getTime() - breakStart.getTime()) / (1000 * 60);
          }

          // Calculate total work time in hours (excluding breaks)
          const totalMinutesWorked =
            (clockOutTime.getTime() - clockInTime.getTime()) / (1000 * 60) - breakTimeMinutes;
          const hoursWorked = parseFloat((totalMinutesWorked / 60).toFixed(2));

          // Create or update time log
          const today = new Date().toISOString().split('T')[0];
          const timeLog = staff.timeLog || [];
          const todayLog = timeLog.find(log => log.date === today);

          let updatedTimeLog;
          if (todayLog) {
            updatedTimeLog = timeLog.map(log =>
              log.date === today
                ? {
                    ...log,
                    hoursWorked: log.hoursWorked + hoursWorked,
                    breakTime: log.breakTime + (breakTimeMinutes / 60)
                  }
                : log
            );
          } else {
            updatedTimeLog = [
              ...timeLog,
              {
                date: today,
                hoursWorked,
                breakTime: breakTimeMinutes / 60
              }
            ];
          }

          const updatedStaff = {
            ...staff,
            workStatus: 'clocked-out' as StaffWorkStatus,
            currentShift: {
              ...staff.currentShift,
              clockOutTime: now
            },
            timeLog: updatedTimeLog
          };

          // If this is the active staff, update that too
          if (activeStaff && activeStaff.id === staffId) {
            setActiveStaff(updatedStaff);
          }

          return updatedStaff;
        }
        return staff;
      })
    );
    toast.success("Clocked out successfully");
  };

  // Start break
  const startBreak = (staffId: string) => {
    const now = new Date().toISOString();
    setStaffMembers(prev =>
      prev.map(staff => {
        if (staff.id === staffId && staff.currentShift) {
          const updatedStaff = {
            ...staff,
            workStatus: 'on-break' as StaffWorkStatus,
            currentShift: {
              ...staff.currentShift,
              breakStartTime: now
            }
          };

          // If this is the active staff, update that too
          if (activeStaff && activeStaff.id === staffId) {
            setActiveStaff(updatedStaff);
          }

          return updatedStaff;
        }
        return staff;
      })
    );
    toast.success("Break started");
  };

  // End break
  const endBreak = (staffId: string) => {
    const now = new Date().toISOString();
    setStaffMembers(prev =>
      prev.map(staff => {
        if (staff.id === staffId && staff.currentShift) {
          const updatedStaff = {
            ...staff,
            workStatus: 'clocked-in' as StaffWorkStatus,
            currentShift: {
              ...staff.currentShift,
              breakEndTime: now
            }
          };

          // If this is the active staff, update that too
          if (activeStaff && activeStaff.id === staffId) {
            setActiveStaff(updatedStaff);
          }

          return updatedStaff;
        }
        return staff;
      })
    );
    toast.success("Break ended");
  };

  // Logout active staff
  const logoutStaff = () => {
    // Important: We're only removing the active staff from the context
    // but NOT changing their clock status

    // Store the current staff member's clock status before logging out
    if (activeStaff) {
      const staffId = activeStaff.id;
      const currentWorkStatus = activeStaff.workStatus;

      logger.userAction('staff logout', 'StaffPINContext', { staffName: activeStaff.name });
      logger.debug('Current work status', 'StaffPINContext', { status: currentWorkStatus });

      // Save the current work status to localStorage so we can restore it later
      if (currentWorkStatus === 'clocked-in') {
        localStorage.setItem(`staff_${staffId}_clockStatus`, 'clocked-in');
        logger.dataOperation('save', 'clocked-in status for restoration', 'StaffPINContext');
      }
    }

    // Clear the active staff from the context
    setActiveStaff(null);

    // We're NOT removing from localStorage to allow restoration when they log back in
    // localStorage.removeItem('activeStaff');
  };

  // Get all staff members who are clocked in or on break
  const getActiveStaffMembers = (): StaffMember[] => {
    return staffMembers.filter(staff =>
      staff.workStatus === 'clocked-in' || staff.workStatus === 'on-break'
    );
  };

  return (
    <StaffPINContext.Provider
      value={{
        activeStaff,
        staffMembers,
        verifyPIN,
        clockIn,
        clockOut,
        startBreak,
        endBreak,
        updateStaffPIN,
        getStaffById,
        logoutStaff,
        getActiveStaffMembers
      }}
    >
      {children}
    </StaffPINContext.Provider>
  );
};

// Custom hook for using staff PIN context
export const useStaffPIN = () => useContext(StaffPINContext);
