"""
Health check endpoints for RestroManage
"""

from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
import redis
import asyncio
from datetime import datetime
from typing import Dict, Any
import psutil
import os

from app.database import get_db_session
from app.config import settings
from app.utils.logging import get_logger

router = APIRouter()
logger = get_logger("health")

async def check_database() -> Dict[str, Any]:
    """Check database connectivity and performance"""
    try:
        start_time = datetime.utcnow()
        async with get_db_session() as session:
            # Simple query to test connectivity
            result = await session.execute(text("SELECT 1"))
            result.fetchone()
            
            # Check table count
            table_result = await session.execute(
                text("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public'")
                if settings.DATABASE_URL.startswith("postgresql") 
                else text("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
            )
            table_count = table_result.fetchone()[0]
        
        duration = (datetime.utcnow() - start_time).total_seconds()
        
        return {
            "status": "healthy",
            "response_time_ms": round(duration * 1000, 2),
            "table_count": table_count,
            "database_type": "postgresql" if settings.DATABASE_URL.startswith("postgresql") else "sqlite"
        }
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e)
        }

async def check_redis() -> Dict[str, Any]:
    """Check Redis connectivity"""
    if not settings.REDIS_URL:
        return {"status": "not_configured"}
    
    try:
        # Parse Redis URL
        import redis.asyncio as aioredis
        redis_client = aioredis.from_url(settings.REDIS_URL)
        
        start_time = datetime.utcnow()
        await redis_client.ping()
        duration = (datetime.utcnow() - start_time).total_seconds()
        
        # Get Redis info
        info = await redis_client.info()
        await redis_client.close()
        
        return {
            "status": "healthy",
            "response_time_ms": round(duration * 1000, 2),
            "version": info.get("redis_version"),
            "memory_usage": info.get("used_memory_human")
        }
    except Exception as e:
        logger.error(f"Redis health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e)
        }

def check_system_resources() -> Dict[str, Any]:
    """Check system resource usage"""
    try:
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        return {
            "status": "healthy",
            "cpu_usage_percent": cpu_percent,
            "memory_usage_percent": memory.percent,
            "memory_available_gb": round(memory.available / (1024**3), 2),
            "disk_usage_percent": disk.percent,
            "disk_free_gb": round(disk.free / (1024**3), 2)
        }
    except Exception as e:
        logger.error(f"System resource check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e)
        }

@router.get("/health")
async def health_check():
    """Basic health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": settings.VERSION,
        "environment": settings.ENVIRONMENT
    }

@router.get("/health/detailed")
async def detailed_health_check():
    """Detailed health check with all dependencies"""
    start_time = datetime.utcnow()
    
    # Run all health checks concurrently
    database_check, redis_check = await asyncio.gather(
        check_database(),
        check_redis(),
        return_exceptions=True
    )
    
    system_check = check_system_resources()
    
    # Determine overall status
    checks = {
        "database": database_check if not isinstance(database_check, Exception) else {"status": "error", "error": str(database_check)},
        "redis": redis_check if not isinstance(redis_check, Exception) else {"status": "error", "error": str(redis_check)},
        "system": system_check
    }
    
    # Overall status is healthy if all critical services are healthy
    overall_status = "healthy"
    if checks["database"]["status"] != "healthy":
        overall_status = "unhealthy"
    elif checks["system"]["status"] != "healthy":
        overall_status = "degraded"
    elif checks["redis"]["status"] not in ["healthy", "not_configured"]:
        overall_status = "degraded"
    
    duration = (datetime.utcnow() - start_time).total_seconds()
    
    response = {
        "status": overall_status,
        "timestamp": datetime.utcnow().isoformat(),
        "version": settings.VERSION,
        "environment": settings.ENVIRONMENT,
        "response_time_ms": round(duration * 1000, 2),
        "checks": checks
    }
    
    # Log health check result
    logger.info(f"Health check completed: {overall_status}", extra={"health_check": response})
    
    return response

@router.get("/health/ready")
async def readiness_check():
    """Kubernetes readiness probe endpoint"""
    try:
        # Check critical dependencies
        db_check = await check_database()
        
        if db_check["status"] != "healthy":
            raise HTTPException(status_code=503, detail="Database not ready")
        
        return {"status": "ready", "timestamp": datetime.utcnow().isoformat()}
    
    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        raise HTTPException(status_code=503, detail="Service not ready")

@router.get("/health/live")
async def liveness_check():
    """Kubernetes liveness probe endpoint"""
    return {
        "status": "alive",
        "timestamp": datetime.utcnow().isoformat(),
        "uptime_seconds": round((datetime.utcnow() - datetime.fromtimestamp(psutil.Process().create_time())).total_seconds())
    }
