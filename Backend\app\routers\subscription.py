# FastAPI Subscription Endpoints
# Migrated from backend/routers/subscription.py

from fastapi import APIRouter, HTTPException, Request
from typing import List, Dict
from datetime import datetime
import logging

from app.models.subscription import (
    RestaurantSubscriptionResponse, FeatureAccessResponse, 
    FeatureCheckRequest, FeatureCheckResponse, SubscriptionPlanResponse
)
from app.services.subscription_service import SubscriptionService

router = APIRouter(prefix="/subscription", tags=["subscription"])
logger = logging.getLogger(__name__)

@router.get("/restaurants/{restaurant_id}/subscription", response_model=RestaurantSubscriptionResponse)
async def get_restaurant_subscription(restaurant_id: str):
    """Get current subscription details for a restaurant"""
    try:
        service = SubscriptionService()
        subscription = await service.get_restaurant_subscription(restaurant_id)
        
        if not subscription:
            raise HTTPException(status_code=404, detail="Restaurant subscription not found")
        
        return subscription
    except Exception as e:
        logger.error(f"Error fetching subscription for {restaurant_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/restaurants/{restaurant_id}/features", response_model=FeatureAccessResponse)
async def get_restaurant_features(restaurant_id: str):
    """Get available features for a restaurant based on subscription"""
    try:
        service = SubscriptionService()
        features = await service.get_available_features(restaurant_id)
        return features
    except Exception as e:
        logger.error(f"Error fetching features for {restaurant_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post("/check-access", response_model=FeatureCheckResponse)
async def check_feature_access(
    request: FeatureCheckRequest,
    http_request: Request
):
    """Check if restaurant has access to specific feature"""
    try:
        service = SubscriptionService()
        
        # Perform access check
        access_result = await service.check_feature_access(
            restaurant_id=request.restaurant_id,
            feature_id=request.feature_id,
            user_id=request.user_id,
            ip_address=http_request.client.host if http_request.client else None,
            user_agent=http_request.headers.get("user-agent", "")
        )
        
        return access_result
    except Exception as e:
        logger.error(f"Error checking access for {request.restaurant_id}/{request.feature_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/plans")
async def get_subscription_plans():
    """Get all available subscription plans"""
    try:
        service = SubscriptionService()
        plans = await service.get_all_plans()
        return plans
    except Exception as e:
        logger.error(f"Error fetching subscription plans: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.put("/restaurants/{restaurant_id}/subscription")
async def update_restaurant_subscription(
    restaurant_id: str,
    plan_id: str
):
    """Update restaurant subscription plan (Admin only)"""
    try:
        service = SubscriptionService()
        updated_subscription = await service.update_subscription(restaurant_id, plan_id)
        
        return {"message": "Subscription updated successfully", "subscription": updated_subscription}
    except Exception as e:
        logger.error(f"Error updating subscription for {restaurant_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/restaurants/{restaurant_id}/access-logs")
async def get_access_logs(
    restaurant_id: str,
    limit: int = 100
):
    """Get feature access logs for a restaurant (Admin only)"""
    try:
        service = SubscriptionService()
        logs = await service.get_access_logs(restaurant_id, limit)
        return logs
    except Exception as e:
        logger.error(f"Error fetching access logs for {restaurant_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

# Bulk operations for admin
@router.post("/bulk/check-access")
async def bulk_check_access(
    restaurant_id: str,
    feature_ids: List[str]
):
    """Check access for multiple features at once (optimization)"""
    try:
        service = SubscriptionService()
        results = await service.bulk_check_access(restaurant_id, feature_ids)
        return {"restaurant_id": restaurant_id, "access_results": results}
    except Exception as e:
        logger.error(f"Error in bulk access check for {restaurant_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

# Health check endpoint
@router.get("/health")
async def subscription_health_check():
    """Health check for subscription service"""
    try:
        return {
            "status": "healthy",
            "timestamp": datetime.utcnow(),
            "service": "subscription",
            "version": "1.0.0"
        }
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        raise HTTPException(status_code=503, detail="Service unavailable")

# Feature definitions endpoint
@router.get("/features")
async def get_feature_definitions():
    """Get all available feature definitions"""
    try:
        service = SubscriptionService()
        return service.features
    except Exception as e:
        logger.error(f"Error fetching feature definitions: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

# Quick access check for frontend
@router.get("/restaurants/{restaurant_id}/has-feature/{feature_id}")
async def quick_feature_check(restaurant_id: str, feature_id: str):
    """Quick feature access check for frontend components"""
    try:
        service = SubscriptionService()
        has_access = service.has_feature_access(restaurant_id, feature_id)
        return {"has_access": has_access, "feature_id": feature_id, "restaurant_id": restaurant_id}
    except Exception as e:
        logger.error(f"Error in quick feature check: {str(e)}")
        return {"has_access": False, "feature_id": feature_id, "restaurant_id": restaurant_id}
