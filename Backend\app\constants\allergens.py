"""
Allergen constants for the restaurant management system.
Defines the valid allergen types that can be used throughout the application.
"""

# Valid allergen types based on common food allergens
VALID_ALLERGENS = [
    "nuts",
    "dairy", 
    "gluten",
    "shellfish",
    "eggs",
    "soy",
    "fish",
    "sesame"
]

# Allergen descriptions for documentation
ALLERGEN_DESCRIPTIONS = {
    "nuts": "Tree nuts (almonds, walnuts, pecans, etc.)",
    "dairy": "Milk and dairy products",
    "gluten": "Wheat, barley, rye, and other gluten-containing grains",
    "shellfish": "Crustaceans and mollusks",
    "eggs": "Chicken eggs and egg products",
    "soy": "Soybeans and soy products",
    "fish": "Fish and fish products",
    "sesame": "Sesame seeds and sesame products"
}

def validate_allergen(allergen: str) -> bool:
    """
    Validate if an allergen is in the list of valid allergens.
    
    Args:
        allergen: The allergen string to validate
        
    Returns:
        bool: True if valid, False otherwise
    """
    return allergen.lower() in [a.lower() for a in VALID_ALLERGENS]

def validate_allergens(allergens: list) -> tuple[bool, list]:
    """
    Validate a list of allergens.
    
    Args:
        allergens: List of allergen strings to validate
        
    Returns:
        tuple: (is_valid, invalid_allergens)
    """
    if not allergens:
        return True, []
    
    invalid_allergens = []
    for allergen in allergens:
        if not validate_allergen(allergen):
            invalid_allergens.append(allergen)
    
    return len(invalid_allergens) == 0, invalid_allergens
