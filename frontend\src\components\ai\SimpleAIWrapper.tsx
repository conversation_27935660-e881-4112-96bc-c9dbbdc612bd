/**
 * Simple AI Wrapper - Minimal wrapper for the AI chat
 * Replaces the complex lazy loading system
 */

import React, { useState } from 'react';
import MinimalAIChat from './MinimalAIChat';

interface SimpleAIWrapperProps {
  className?: string;
}

const SimpleAIWrapper: React.FC<SimpleAIWrapperProps> = ({ className }) => {
  const [isVisible, setIsVisible] = useState(false);

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        style={{
          position: 'fixed',
          bottom: '20px',
          right: '20px',
          width: '60px',
          height: '60px',
          borderRadius: '50%',
          backgroundColor: '#3b82f6',
          color: 'white',
          border: 'none',
          cursor: 'pointer',
          fontSize: '24px',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
          zIndex: 1000
        }}
        className={className}
      >
        💬
      </button>
    );
  }

  return <MinimalAIChat onClose={() => setIsVisible(false)} />;
};

export default SimpleAIWrapper;
