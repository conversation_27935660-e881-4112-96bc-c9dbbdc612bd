/**
 * Comprehensive Error Boundary and Loading States component for RestroManage
 * Provides consistent error handling and loading UI across all tabs
 */

import React, { Component, ReactNode } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  AlertCircle, 
  RefreshCw, 
  Wifi, 
  WifiOff, 
  Loader2, 
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle
} from 'lucide-react';
import { useNetworkStatus } from '@/hooks/useErrorHandling';
import logger from '@/utils/logger';

// Error Boundary Props
interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  context?: string;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
}

// Error Boundary Class Component
export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    });

    // Log error
    logger.error('React Error Boundary caught error', this.props.context || 'ErrorBoundary', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
    });

    // Call custom error handler
    this.props.onError?.(error, errorInfo);
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <ErrorFallback
          error={this.state.error}
          onRetry={this.handleRetry}
          context={this.props.context}
        />
      );
    }

    return this.props.children;
  }
}

// Error Fallback Component
interface ErrorFallbackProps {
  error: Error | null;
  onRetry: () => void;
  context?: string;
}

const ErrorFallback: React.FC<ErrorFallbackProps> = ({ error, onRetry, context }) => {
  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <div className="flex items-center gap-2">
          <XCircle className="h-5 w-5 text-red-500" />
          <CardTitle className="text-red-700">Something went wrong</CardTitle>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error in {context || 'Application'}</AlertTitle>
          <AlertDescription>
            {error?.message || 'An unexpected error occurred'}
          </AlertDescription>
        </Alert>

        <div className="flex gap-2">
          <Button onClick={onRetry} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
          <Button 
            variant="ghost" 
            onClick={() => window.location.reload()}
          >
            Reload Page
          </Button>
        </div>

        {process.env.NODE_ENV === 'development' && error && (
          <details className="mt-4">
            <summary className="cursor-pointer text-sm font-medium">
              Error Details (Development)
            </summary>
            <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto">
              {error.stack}
            </pre>
          </details>
        )}
      </CardContent>
    </Card>
  );
};

// Loading Component
interface LoadingProps {
  message?: string;
  progress?: number;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'spinner' | 'skeleton' | 'progress';
}

export const Loading: React.FC<LoadingProps> = ({ 
  message = 'Loading...', 
  progress, 
  size = 'md',
  variant = 'spinner'
}) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8',
  };

  if (variant === 'progress' && progress !== undefined) {
    return (
      <div className="flex flex-col items-center justify-center p-6 space-y-4">
        <Loader2 className={`${sizeClasses[size]} animate-spin text-blue-500`} />
        <div className="text-center space-y-2">
          <p className="text-sm text-muted-foreground">{message}</p>
          <Progress value={progress} className="w-48" />
          <p className="text-xs text-muted-foreground">{progress}%</p>
        </div>
      </div>
    );
  }

  if (variant === 'skeleton') {
    return (
      <div className="space-y-4 p-4">
        <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
        <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4"></div>
        <div className="h-4 bg-gray-200 rounded animate-pulse w-1/2"></div>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center justify-center p-6 space-y-2">
      <Loader2 className={`${sizeClasses[size]} animate-spin text-blue-500`} />
      <p className="text-sm text-muted-foreground">{message}</p>
    </div>
  );
};

// Network Status Component
export const NetworkStatus: React.FC = () => {
  const { isOnline, connectionQuality } = useNetworkStatus();

  if (isOnline && connectionQuality === 'good') {
    return null; // Don't show anything when connection is good
  }

  return (
    <Alert variant={isOnline ? 'default' : 'destructive'} className="mb-4">
      {isOnline ? (
        <Wifi className="h-4 w-4" />
      ) : (
        <WifiOff className="h-4 w-4" />
      )}
      <AlertTitle>
        {isOnline ? 'Poor Connection' : 'No Internet Connection'}
      </AlertTitle>
      <AlertDescription>
        {isOnline 
          ? 'Your connection is slow. Some features may be limited.'
          : 'Please check your internet connection and try again.'
        }
      </AlertDescription>
    </Alert>
  );
};

// Data Status Component
interface DataStatusProps {
  isLoading: boolean;
  error: Error | null;
  lastUpdated?: Date | null;
  onRetry?: () => void;
  isEmpty?: boolean;
  emptyMessage?: string;
}

export const DataStatus: React.FC<DataStatusProps> = ({
  isLoading,
  error,
  lastUpdated,
  onRetry,
  isEmpty = false,
  emptyMessage = 'No data available',
}) => {
  if (isLoading) {
    return <Loading message="Loading data..." />;
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Failed to load data</AlertTitle>
        <AlertDescription className="flex items-center justify-between">
          <span>{error.message}</span>
          {onRetry && (
            <Button onClick={onRetry} size="sm" variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          )}
        </AlertDescription>
      </Alert>
    );
  }

  if (isEmpty) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <div className="rounded-full bg-gray-100 p-3 mb-4">
          <AlertTriangle className="h-6 w-6 text-gray-400" />
        </div>
        <p className="text-muted-foreground">{emptyMessage}</p>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-between text-xs text-muted-foreground mb-2">
      <div className="flex items-center gap-2">
        <CheckCircle className="h-3 w-3 text-green-500" />
        <span>Data loaded successfully</span>
      </div>
      {lastUpdated && (
        <div className="flex items-center gap-1">
          <Clock className="h-3 w-3" />
          <span>Updated {lastUpdated.toLocaleTimeString()}</span>
        </div>
      )}
    </div>
  );
};

// Sync Status Component
interface SyncStatusProps {
  isSync: boolean;
  lastSync: Date | null;
  failedSyncs: string[];
  onRetrySync?: () => void;
}

export const SyncStatus: React.FC<SyncStatusProps> = ({
  isSync,
  lastSync,
  failedSyncs,
  onRetrySync,
}) => {
  if (isSync) {
    return (
      <Badge variant="secondary" className="animate-pulse">
        <Loader2 className="h-3 w-3 mr-1 animate-spin" />
        Syncing...
      </Badge>
    );
  }

  if (failedSyncs.length > 0) {
    return (
      <div className="flex items-center gap-2">
        <Badge variant="destructive">
          <XCircle className="h-3 w-3 mr-1" />
          Sync Failed
        </Badge>
        {onRetrySync && (
          <Button onClick={onRetrySync} size="sm" variant="outline">
            <RefreshCw className="h-3 w-3 mr-1" />
            Retry
          </Button>
        )}
      </div>
    );
  }

  if (lastSync) {
    return (
      <Badge variant="outline">
        <CheckCircle className="h-3 w-3 mr-1 text-green-500" />
        Synced {lastSync.toLocaleTimeString()}
      </Badge>
    );
  }

  return null;
};

// Tab Loading Wrapper
interface TabLoadingWrapperProps {
  children: ReactNode;
  isLoading: boolean;
  error: Error | null;
  onRetry?: () => void;
  loadingMessage?: string;
  context?: string;
}

export const TabLoadingWrapper: React.FC<TabLoadingWrapperProps> = ({
  children,
  isLoading,
  error,
  onRetry,
  loadingMessage,
  context,
}) => {
  return (
    <ErrorBoundary context={context}>
      <div className="space-y-4">
        <NetworkStatus />
        <DataStatus
          isLoading={isLoading}
          error={error}
          onRetry={onRetry}
          emptyMessage={`No ${context?.toLowerCase()} data available`}
        />
        {!isLoading && !error && children}
      </div>
    </ErrorBoundary>
  );
};

export default ErrorBoundary;
