from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import List, Optional
from datetime import datetime, date

from app.models.forecasting import (
    SalesForecast, CustomerTrafficForecast, InventoryDemandForecast,
    StaffSchedulingForecast, SeasonalTrendsForecast, ComprehensiveForecast,
    ForecastRequest, ForecastType, ForecastPeriod
)
from app.services.forecasting_service import ForecastingService
from app.utils.auth import get_current_active_user

router = APIRouter(prefix="/forecasting", tags=["Forecasting"])

# Initialize forecasting service
forecasting_service = ForecastingService()

@router.get("/health")
async def forecasting_health_check():
    """Health check for forecasting service"""
    return {
        "status": "healthy",
        "service": "forecasting",
        "algorithms": ["linear_regression", "random_forest", "time_series"],
        "forecast_types": ["sales", "customer_traffic", "inventory_demand", "staff_scheduling", "seasonal_trends"]
    }

@router.get("/sales", response_model=SalesForecast)
async def get_sales_forecast(
    period: ForecastPeriod = Query(ForecastPeriod.DAILY, description="Forecast period"),
    days_ahead: int = Query(30, ge=1, le=365, description="Number of days to forecast"),
    current_user = Depends(get_current_active_user)
):
    """
    Generate sales forecast with revenue predictions, trend analysis, and seasonal patterns
    """
    try:
        forecast = forecasting_service.generate_sales_forecast(period, days_ahead)
        return forecast
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate sales forecast: {str(e)}"
        )

@router.get("/customer-traffic", response_model=CustomerTrafficForecast)
async def get_customer_traffic_forecast(
    period: ForecastPeriod = Query(ForecastPeriod.DAILY, description="Forecast period"),
    days_ahead: int = Query(30, ge=1, le=365, description="Number of days to forecast"),
    current_user = Depends(get_current_active_user)
):
    """
    Generate customer traffic forecast with peak hours and customer count predictions
    """
    try:
        forecast = forecasting_service.generate_customer_traffic_forecast(period, days_ahead)
        return forecast
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate customer traffic forecast: {str(e)}"
        )

@router.get("/inventory-demand", response_model=InventoryDemandForecast)
async def get_inventory_demand_forecast(
    period: ForecastPeriod = Query(ForecastPeriod.DAILY, description="Forecast period"),
    days_ahead: int = Query(30, ge=1, le=365, description="Number of days to forecast"),
    current_user = Depends(get_current_active_user)
):
    """
    Generate inventory demand forecast with usage predictions and reorder recommendations
    """
    try:
        forecast = forecasting_service.generate_inventory_demand_forecast(period, days_ahead)
        return forecast
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate inventory demand forecast: {str(e)}"
        )

@router.get("/staff-scheduling", response_model=StaffSchedulingForecast)
async def get_staff_scheduling_forecast(
    period: ForecastPeriod = Query(ForecastPeriod.DAILY, description="Forecast period"),
    days_ahead: int = Query(30, ge=1, le=365, description="Number of days to forecast"),
    current_user = Depends(get_current_active_user)
):
    """
    Generate staff scheduling forecast with optimal staffing recommendations and cost optimization
    """
    try:
        forecast = forecasting_service.generate_staff_scheduling_forecast(period, days_ahead)
        return forecast
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate staff scheduling forecast: {str(e)}"
        )

@router.get("/seasonal-trends", response_model=SeasonalTrendsForecast)
async def get_seasonal_trends_forecast(
    period: ForecastPeriod = Query(ForecastPeriod.MONTHLY, description="Forecast period"),
    days_ahead: int = Query(90, ge=1, le=365, description="Number of days to forecast"),
    current_user = Depends(get_current_active_user)
):
    """
    Generate seasonal trends forecast with holiday impact analysis and marketing opportunities
    """
    try:
        forecast = forecasting_service.generate_seasonal_trends_forecast(period, days_ahead)
        return forecast
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate seasonal trends forecast: {str(e)}"
        )

@router.get("/comprehensive", response_model=ComprehensiveForecast)
async def get_comprehensive_forecast(
    period: ForecastPeriod = Query(ForecastPeriod.DAILY, description="Forecast period"),
    days_ahead: int = Query(30, ge=1, le=365, description="Number of days to forecast"),
    current_user = Depends(get_current_active_user)
):
    """
    Generate comprehensive forecast including all forecast types with summary and recommendations
    """
    try:
        forecast = forecasting_service.generate_comprehensive_forecast(period, days_ahead)
        return forecast
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate comprehensive forecast: {str(e)}"
        )

@router.post("/custom", response_model=ComprehensiveForecast)
async def get_custom_forecast(
    request: ForecastRequest,
    current_user = Depends(get_current_active_user)
):
    """
    Generate custom forecast based on specific requirements
    """
    try:
        # For now, return comprehensive forecast
        # In the future, this could be customized based on request.forecast_types
        forecast = forecasting_service.generate_comprehensive_forecast(
            request.period, 
            request.days_ahead
        )
        return forecast
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate custom forecast: {str(e)}"
        )

@router.get("/summary")
async def get_forecast_summary(
    days_ahead: int = Query(7, ge=1, le=30, description="Number of days for summary")
):
    """
    Get a quick forecast summary for dashboard display
    """
    try:
        # Generate quick forecasts
        sales_forecast = forecasting_service.generate_sales_forecast(ForecastPeriod.DAILY, days_ahead)
        traffic_forecast = forecasting_service.generate_customer_traffic_forecast(ForecastPeriod.DAILY, days_ahead)
        inventory_forecast = forecasting_service.generate_inventory_demand_forecast(ForecastPeriod.DAILY, days_ahead)
        
        # Create summary
        summary = {
            "period": f"Next {days_ahead} days",
            "sales": {
                "predicted_revenue": sum(point.value for point in sales_forecast.predictions),
                "trend": sales_forecast.trend_analysis.direction.value,
                "confidence": sales_forecast.metrics.accuracy
            },
            "customers": {
                "average_daily": traffic_forecast.average_daily_customers,
                "peak_hours": traffic_forecast.peak_hours,
                "trend": traffic_forecast.trend_analysis.direction.value
            },
            "inventory": {
                "critical_items": len(inventory_forecast.critical_items),
                "total_cost": inventory_forecast.total_cost_prediction,
                "reorder_needed": len([item for item in inventory_forecast.items if item.reorder_recommendation])
            },
            "recommendations": [
                f"Sales trend: {sales_forecast.trend_analysis.description}",
                f"Inventory: {len(inventory_forecast.critical_items)} items need attention",
                f"Peak customer hours: {', '.join(traffic_forecast.peak_hours)}"
            ]
        }
        
        return summary
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate forecast summary: {str(e)}"
        )

@router.get("/chart-data/{forecast_type}")
async def get_chart_data(
    forecast_type: ForecastType,
    days_ahead: int = Query(30, ge=1, le=365, description="Number of days to forecast")
):
    """
    Get forecast data formatted for frontend charts
    """
    try:
        if forecast_type == ForecastType.SALES:
            forecast = forecasting_service.generate_sales_forecast(ForecastPeriod.DAILY, days_ahead)
            chart_data = {
                "labels": [point.date.strftime("%Y-%m-%d") for point in forecast.predictions],
                "datasets": [
                    {
                        "label": "Predicted Revenue",
                        "data": [point.value for point in forecast.predictions],
                        "borderColor": "rgb(75, 192, 192)",
                        "backgroundColor": "rgba(75, 192, 192, 0.2)"
                    },
                    {
                        "label": "Confidence Upper",
                        "data": [point.confidence_interval_upper for point in forecast.predictions],
                        "borderColor": "rgba(75, 192, 192, 0.3)",
                        "fill": False,
                        "borderDash": [5, 5]
                    },
                    {
                        "label": "Confidence Lower",
                        "data": [point.confidence_interval_lower for point in forecast.predictions],
                        "borderColor": "rgba(75, 192, 192, 0.3)",
                        "fill": False,
                        "borderDash": [5, 5]
                    }
                ]
            }
        elif forecast_type == ForecastType.CUSTOMER_TRAFFIC:
            forecast = forecasting_service.generate_customer_traffic_forecast(ForecastPeriod.DAILY, days_ahead)
            chart_data = {
                "labels": [point.date.strftime("%Y-%m-%d") for point in forecast.predictions],
                "datasets": [
                    {
                        "label": "Predicted Customers",
                        "data": [point.value for point in forecast.predictions],
                        "borderColor": "rgb(255, 99, 132)",
                        "backgroundColor": "rgba(255, 99, 132, 0.2)"
                    }
                ]
            }
        else:
            # Default to sales for other types
            forecast = forecasting_service.generate_sales_forecast(ForecastPeriod.DAILY, days_ahead)
            chart_data = {
                "labels": [point.date.strftime("%Y-%m-%d") for point in forecast.predictions],
                "datasets": [
                    {
                        "label": "Forecast Data",
                        "data": [point.value for point in forecast.predictions],
                        "borderColor": "rgb(54, 162, 235)",
                        "backgroundColor": "rgba(54, 162, 235, 0.2)"
                    }
                ]
            }
        
        return chart_data
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate chart data: {str(e)}"
        )
