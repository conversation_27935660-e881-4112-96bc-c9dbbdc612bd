<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Notifications System Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            line-height: 1.6;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #28a745;
        }
        .success-section {
            border-left-color: #28a745;
            background: #f0fff4;
        }
        .error-section {
            border-left-color: #dc3545;
            background: #fff5f5;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .button:hover {
            background: #0056b3;
        }
        .button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .button.danger {
            background: #dc3545;
        }
        .button.danger:hover {
            background: #c82333;
        }
        .button.success {
            background: #28a745;
        }
        .button.success:hover {
            background: #218838;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: 500;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .notification-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
        .notification-item.unread {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        .notification-item.high-priority {
            border-left-color: #dc3545;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .stat-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            color: #6c757d;
            font-size: 0.9em;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: #007bff;
            transition: width 0.3s ease;
        }
        .test-results {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            max-height: 400px;
            overflow-y: auto;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔔 Complete Notifications System Test</h1>
        <p>Comprehensive testing of the RestroManage-v0 notification system including frontend, backend, and React Query integration.</p>
        
        <div class="test-section">
            <h3>🏥 System Health Check</h3>
            <button class="button" onclick="runHealthChecks()">Run Health Checks</button>
            <div id="health-status" class="status info">Click button to check system health...</div>
            
            <div class="stats-grid" id="health-stats" style="display: none;">
                <div class="stat-card">
                    <div class="stat-number" id="backend-status">❓</div>
                    <div class="stat-label">Backend Status</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="frontend-status">❓</div>
                    <div class="stat-label">Frontend Status</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="api-status">❓</div>
                    <div class="stat-label">API Status</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 Current Notification Stats</h3>
            <button class="button" onclick="fetchCurrentStats()">Fetch Stats</button>
            <div id="stats-status" class="status info">Click button to fetch current statistics...</div>
            
            <div class="stats-grid" id="notification-stats" style="display: none;">
                <div class="stat-card">
                    <div class="stat-number" id="total-count">0</div>
                    <div class="stat-label">Total Notifications</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="unread-count">0</div>
                    <div class="stat-label">Unread Notifications</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="read-percentage">0%</div>
                    <div class="stat-label">Read Percentage</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 Comprehensive Testing Suite</h3>
            <button class="button success" onclick="runFullTestSuite()">Run Full Test Suite</button>
            <button class="button" onclick="createTestData()">Create Test Data</button>
            <button class="button danger" onclick="cleanupTestData()">Cleanup Test Data</button>
            
            <div id="test-progress" style="display: none;">
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill" style="width: 0%;"></div>
                </div>
                <div id="test-status" class="status info">Preparing tests...</div>
            </div>
            
            <div class="test-results" id="test-results" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📋 Manual Testing</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
                <button class="button" onclick="createSingleNotification('info', 'low')">Create Info (Low)</button>
                <button class="button" onclick="createSingleNotification('success', 'medium')">Create Success (Medium)</button>
                <button class="button" onclick="createSingleNotification('warning', 'high')">Create Warning (High)</button>
                <button class="button" onclick="createSingleNotification('error', 'high')">Create Error (High)</button>
                <button class="button" onclick="fetchAllNotifications()">Fetch All</button>
                <button class="button" onclick="markAllAsRead()">Mark All Read</button>
                <button class="button danger" onclick="deleteAllNotifications()">Delete All</button>
            </div>
            <div id="manual-status" class="status info">Use buttons above for manual testing...</div>
        </div>

        <div class="test-section">
            <h3>📱 Frontend Integration Test</h3>
            <p>Test the notifications page directly in the application:</p>
            <button class="button success" onclick="openNotificationsPage()">Open Notifications Page</button>
            <button class="button" onclick="openTestPage()">Open API Test Page</button>
            <div id="integration-status" class="status info">Click buttons to test frontend integration...</div>
        </div>

        <div class="test-section">
            <h3>📝 Current Notifications</h3>
            <button class="button" onclick="displayCurrentNotifications()">Refresh List</button>
            <div id="notifications-display" class="test-results" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🔧 API Endpoints Reference</h3>
            <div style="background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 8px; font-family: monospace; font-size: 12px;">
Base URL: http://localhost:5001/api<br><br>
<strong>Endpoints:</strong><br>
• GET /notifications - Get all notifications<br>
• POST /notifications - Create notification<br>
• GET /notifications/stats - Get notification statistics<br>
• PATCH /notifications/mark-read - Mark notifications as read<br>
• PUT /notifications/mark-all-read - Mark all as read<br>
• DELETE /notifications/{id} - Delete specific notification<br>
• DELETE /notifications - Delete all notifications<br><br>
<strong>Headers:</strong><br>
• X-Restaurant-ID: 1<br>
• Content-Type: application/json
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5001/api';
        const RESTAURANT_ID = '1';
        let testResults = [];

        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = message;
                element.className = `status ${type}`;
            }
        }

        function updateProgress(percentage, message) {
            const progressFill = document.getElementById('progress-fill');
            const testStatus = document.getElementById('test-status');
            
            if (progressFill) progressFill.style.width = `${percentage}%`;
            if (testStatus) {
                testStatus.textContent = message;
                testStatus.className = `status ${percentage === 100 ? 'success' : 'info'}`;
            }
        }

        function addTestResult(test, status, message, details = null) {
            testResults.push({
                test,
                status,
                message,
                details,
                timestamp: new Date().toLocaleTimeString()
            });
            
            const resultsContainer = document.getElementById('test-results');
            if (resultsContainer) {
                resultsContainer.style.display = 'block';
                resultsContainer.innerHTML = testResults.map(result => `
                    <div class="notification-item ${result.status === 'success' ? '' : 'high-priority'}">
                        <strong>[${result.timestamp}] ${result.test}</strong>
                        <p>${result.message}</p>
                        ${result.details ? `<small>Details: ${result.details}</small>` : ''}
                    </div>
                `).join('');
                resultsContainer.scrollTop = resultsContainer.scrollHeight;
            }
        }

        async function runHealthChecks() {
            updateStatus('health-status', 'Running health checks...', 'info');
            document.getElementById('health-stats').style.display = 'grid';
            
            try {
                // Backend health check
                const backendResponse = await fetch('http://localhost:5001/health');
                document.getElementById('backend-status').textContent = backendResponse.ok ? '✅' : '❌';
                
                // API health check
                const apiResponse = await fetch(`${API_BASE}/notifications/stats?restaurant_id=${RESTAURANT_ID}`);
                document.getElementById('api-status').textContent = apiResponse.ok ? '✅' : '❌';
                
                // Frontend status (always OK if this script is running)
                document.getElementById('frontend-status').textContent = '✅';
                
                if (backendResponse.ok && apiResponse.ok) {
                    updateStatus('health-status', '✅ All systems healthy!', 'success');
                } else {
                    updateStatus('health-status', '⚠️ Some systems have issues', 'warning');
                }
            } catch (error) {
                updateStatus('health-status', `❌ Health check failed: ${error.message}`, 'error');
                document.getElementById('backend-status').textContent = '❌';
                document.getElementById('api-status').textContent = '❌';
            }
        }

        async function fetchCurrentStats() {
            try {
                updateStatus('stats-status', 'Fetching notification statistics...', 'info');
                
                const response = await fetch(`${API_BASE}/notifications/stats?restaurant_id=${RESTAURANT_ID}`);
                
                if (response.ok) {
                    const stats = await response.json();
                    
                    document.getElementById('total-count').textContent = stats.total_count;
                    document.getElementById('unread-count').textContent = stats.unread_count;
                    
                    const readPercentage = stats.total_count > 0 
                        ? Math.round(((stats.total_count - stats.unread_count) / stats.total_count) * 100)
                        : 0;
                    document.getElementById('read-percentage').textContent = `${readPercentage}%`;
                    
                    document.getElementById('notification-stats').style.display = 'grid';
                    updateStatus('stats-status', '✅ Statistics fetched successfully', 'success');
                } else {
                    updateStatus('stats-status', `❌ Failed to fetch stats: ${response.status}`, 'error');
                }
            } catch (error) {
                updateStatus('stats-status', `❌ Error fetching stats: ${error.message}`, 'error');
            }
        }

        async function createSingleNotification(type, priority) {
            try {
                const notification = {
                    title: `${type.charAt(0).toUpperCase() + type.slice(1)} Notification`,
                    message: `Test ${type} notification with ${priority} priority created at ${new Date().toLocaleTimeString()}`,
                    type: type,
                    priority: priority
                };

                const response = await fetch(`${API_BASE}/notifications`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Restaurant-ID': RESTAURANT_ID
                    },
                    body: JSON.stringify(notification)
                });

                if (response.ok) {
                    const data = await response.json();
                    updateStatus('manual-status', `✅ Created ${type} notification (ID: ${data.id})`, 'success');
                    fetchCurrentStats(); // Refresh stats
                } else {
                    updateStatus('manual-status', `❌ Failed to create notification: ${response.status}`, 'error');
                }
            } catch (error) {
                updateStatus('manual-status', `❌ Error creating notification: ${error.message}`, 'error');
            }
        }

        async function fetchAllNotifications() {
            try {
                updateStatus('manual-status', 'Fetching all notifications...', 'info');
                
                const response = await fetch(`${API_BASE}/notifications?restaurant_id=${RESTAURANT_ID}`);
                
                if (response.ok) {
                    const notifications = await response.json();
                    updateStatus('manual-status', `✅ Fetched ${notifications.length} notifications`, 'success');
                    displayNotifications(notifications);
                } else {
                    updateStatus('manual-status', `❌ Failed to fetch notifications: ${response.status}`, 'error');
                }
            } catch (error) {
                updateStatus('manual-status', `❌ Error fetching notifications: ${error.message}`, 'error');
            }
        }

        async function markAllAsRead() {
            try {
                updateStatus('manual-status', 'Marking all notifications as read...', 'info');
                
                const response = await fetch(`${API_BASE}/notifications/mark-all-read`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Restaurant-ID': RESTAURANT_ID
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    updateStatus('manual-status', `✅ ${data.message}`, 'success');
                    fetchCurrentStats(); // Refresh stats
                } else {
                    updateStatus('manual-status', `❌ Failed to mark as read: ${response.status}`, 'error');
                }
            } catch (error) {
                updateStatus('manual-status', `❌ Error marking as read: ${error.message}`, 'error');
            }
        }

        async function deleteAllNotifications() {
            if (!confirm('Are you sure you want to delete ALL notifications? This cannot be undone.')) {
                return;
            }

            try {
                updateStatus('manual-status', 'Deleting all notifications...', 'info');
                
                const response = await fetch(`${API_BASE}/notifications`, {
                    method: 'DELETE',
                    headers: {
                        'X-Restaurant-ID': RESTAURANT_ID
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    updateStatus('manual-status', `✅ ${data.message}`, 'success');
                    fetchCurrentStats(); // Refresh stats
                    document.getElementById('notifications-display').innerHTML = '<p>No notifications found.</p>';
                } else {
                    updateStatus('manual-status', `❌ Failed to delete notifications: ${response.status}`, 'error');
                }
            } catch (error) {
                updateStatus('manual-status', `❌ Error deleting notifications: ${error.message}`, 'error');
            }
        }

        async function displayCurrentNotifications() {
            try {
                const response = await fetch(`${API_BASE}/notifications?restaurant_id=${RESTAURANT_ID}`);
                
                if (response.ok) {
                    const notifications = await response.json();
                    displayNotifications(notifications);
                } else {
                    updateStatus('manual-status', `❌ Failed to fetch notifications: ${response.status}`, 'error');
                }
            } catch (error) {
                updateStatus('manual-status', `❌ Error fetching notifications: ${error.message}`, 'error');
            }
        }

        function displayNotifications(notifications) {
            const container = document.getElementById('notifications-display');
            container.style.display = 'block';
            
            if (notifications.length === 0) {
                container.innerHTML = '<p>No notifications found.</p>';
                return;
            }

            const notificationHtml = notifications.map(notification => `
                <div class="notification-item ${!notification.is_read ? 'unread' : ''} ${notification.priority === 'high' ? 'high-priority' : ''}">
                    <strong>${notification.title}</strong>
                    <p>${notification.message}</p>
                    <small>
                        Type: ${notification.type} | Priority: ${notification.priority} | 
                        ${notification.is_read ? 'Read' : 'Unread'} | 
                        Created: ${new Date(notification.created_at).toLocaleString()}
                    </small>
                </div>
            `).join('');

            container.innerHTML = notificationHtml;
        }

        async function createTestData() {
            updateStatus('manual-status', 'Creating comprehensive test data...', 'info');
            
            const testNotifications = [
                { title: 'Order Received', message: 'New order #1001 from table 5', type: 'info', priority: 'medium' },
                { title: 'Low Stock Alert', message: 'Chicken breast running low (3 portions left)', type: 'warning', priority: 'high' },
                { title: 'Payment Successful', message: 'Payment of £45.50 processed successfully', type: 'success', priority: 'low' },
                { title: 'Kitchen Alert', message: 'Order #1002 ready for pickup', type: 'info', priority: 'medium' },
                { title: 'System Error', message: 'POS system connection lost', type: 'error', priority: 'high' },
                { title: 'Staff Clock-in', message: 'Sarah Johnson clocked in for evening shift', type: 'success', priority: 'low' },
                { title: 'Table Ready', message: 'Table 8 is ready for next guests', type: 'success', priority: 'medium' },
                { title: 'Inventory Alert', message: 'Daily inventory check required', type: 'warning', priority: 'medium' }
            ];

            let created = 0;
            for (const notification of testNotifications) {
                try {
                    const response = await fetch(`${API_BASE}/notifications`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Restaurant-ID': RESTAURANT_ID
                        },
                        body: JSON.stringify(notification)
                    });

                    if (response.ok) created++;
                    await new Promise(resolve => setTimeout(resolve, 100)); // Small delay
                } catch (error) {
                    console.error('Failed to create notification:', error);
                }
            }

            updateStatus('manual-status', `✅ Created ${created}/${testNotifications.length} test notifications`, 'success');
            fetchCurrentStats();
        }

        async function cleanupTestData() {
            if (!confirm('This will delete ALL notifications. Continue?')) return;
            
            try {
                const response = await fetch(`${API_BASE}/notifications`, {
                    method: 'DELETE',
                    headers: { 'X-Restaurant-ID': RESTAURANT_ID }
                });

                if (response.ok) {
                    updateStatus('manual-status', '✅ All test data cleaned up', 'success');
                    fetchCurrentStats();
                } else {
                    updateStatus('manual-status', '❌ Failed to cleanup test data', 'error');
                }
            } catch (error) {
                updateStatus('manual-status', `❌ Error during cleanup: ${error.message}`, 'error');
            }
        }

        function openNotificationsPage() {
            window.open('http://localhost:5176/admin/notifications', '_blank');
            updateStatus('integration-status', '✅ Opened notifications page in new tab', 'success');
        }

        function openTestPage() {
            window.open('http://localhost:5176/test-notifications-api.html', '_blank');
            updateStatus('integration-status', '✅ Opened API test page in new tab', 'success');
        }

        async function runFullTestSuite() {
            testResults = [];
            document.getElementById('test-progress').style.display = 'block';
            document.getElementById('test-results').style.display = 'block';
            
            const tests = [
                { name: 'Backend Health Check', fn: testBackendHealth },
                { name: 'API Connectivity', fn: testApiConnectivity },
                { name: 'Create Notification', fn: testCreateNotification },
                { name: 'Fetch Notifications', fn: testFetchNotifications },
                { name: 'Mark as Read', fn: testMarkAsRead },
                { name: 'Delete Notification', fn: testDeleteNotification },
                { name: 'Statistics Endpoint', fn: testStatistics },
                { name: 'Error Handling', fn: testErrorHandling }
            ];

            for (let i = 0; i < tests.length; i++) {
                const test = tests[i];
                const progress = ((i + 1) / tests.length) * 100;
                
                updateProgress(progress, `Running ${test.name}...`);
                
                try {
                    await test.fn();
                    addTestResult(test.name, 'success', 'Test passed');
                } catch (error) {
                    addTestResult(test.name, 'error', 'Test failed', error.message);
                }
                
                await new Promise(resolve => setTimeout(resolve, 500)); // Delay between tests
            }
            
            updateProgress(100, 'All tests completed!');
        }

        // Individual test functions
        async function testBackendHealth() {
            const response = await fetch('http://localhost:5001/health');
            if (!response.ok) throw new Error(`Backend health check failed: ${response.status}`);
        }

        async function testApiConnectivity() {
            const response = await fetch(`${API_BASE}/notifications/stats?restaurant_id=${RESTAURANT_ID}`);
            if (!response.ok) throw new Error(`API connectivity failed: ${response.status}`);
        }

        async function testCreateNotification() {
            const notification = {
                title: 'Test Notification',
                message: 'This is a test notification',
                type: 'info',
                priority: 'medium'
            };

            const response = await fetch(`${API_BASE}/notifications`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Restaurant-ID': RESTAURANT_ID
                },
                body: JSON.stringify(notification)
            });

            if (!response.ok) throw new Error(`Create notification failed: ${response.status}`);
        }

        async function testFetchNotifications() {
            const response = await fetch(`${API_BASE}/notifications?restaurant_id=${RESTAURANT_ID}`);
            if (!response.ok) throw new Error(`Fetch notifications failed: ${response.status}`);
            
            const data = await response.json();
            if (!Array.isArray(data)) throw new Error('Response is not an array');
        }

        async function testMarkAsRead() {
            const response = await fetch(`${API_BASE}/notifications/mark-all-read`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Restaurant-ID': RESTAURANT_ID
                }
            });

            if (!response.ok) throw new Error(`Mark as read failed: ${response.status}`);
        }

        async function testDeleteNotification() {
            const response = await fetch(`${API_BASE}/notifications`, {
                method: 'DELETE',
                headers: { 'X-Restaurant-ID': RESTAURANT_ID }
            });

            if (!response.ok) throw new Error(`Delete notifications failed: ${response.status}`);
        }

        async function testStatistics() {
            const response = await fetch(`${API_BASE}/notifications/stats?restaurant_id=${RESTAURANT_ID}`);
            if (!response.ok) throw new Error(`Statistics failed: ${response.status}`);
            
            const data = await response.json();
            if (typeof data.total_count === 'undefined') throw new Error('Invalid statistics response');
        }

        async function testErrorHandling() {
            // Test with invalid restaurant ID
            const response = await fetch(`${API_BASE}/notifications/stats?restaurant_id=invalid`);
            // This should still return 200 with empty stats, not an error
            if (!response.ok) throw new Error(`Error handling test failed: ${response.status}`);
        }

        // Auto-run health checks on page load
        window.addEventListener('load', () => {
            runHealthChecks();
            fetchCurrentStats();
        });
    </script>
</body>
</html>
