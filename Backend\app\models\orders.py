from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime
from enum import Enum

class OrderStatus(str, Enum):
    PENDING = "pending"
    PREPARING = "preparing"
    READY = "ready"
    DELIVERED = "delivered"
    COMPLETED = "completed"
    CANCELLED = "cancelled"

class PaymentStatus(str, Enum):
    UNPAID = "unpaid"
    PARTIALLY_PAID = "partially_paid"
    PAID = "paid"
    REFUNDED = "refunded"

class OrderItemBase(BaseModel):
    menu_item_id: str
    quantity: int = 1
    special_instructions: Optional[str] = None
    price: float

class OrderItem(OrderItemBase):
    id: str
    name: str
    subtotal: float

class DiscountApplication(BaseModel):
    promo_code_id: str
    promo_code: str
    discount_amount: float
    applied_to_items: Optional[List[str]] = None

class OrderBase(BaseModel):
    restaurant_id: str
    table_id: str
    items: List[OrderItemBase]
    customer_name: Optional[str] = None
    special_instructions: Optional[str] = None
    promo_codes: Optional[List[str]] = None

class OrderCreate(OrderBase):
    pass

class Order(OrderBase):
    id: str
    items: List[OrderItem]
    status: OrderStatus = OrderStatus.PENDING
    payment_status: PaymentStatus = PaymentStatus.UNPAID
    subtotal: float
    tax_amount: float = 0.0
    tip_amount: float = 0.0
    discount_amount: float = 0.0
    total: float
    applied_discounts: Optional[List[DiscountApplication]] = None
    is_split_bill: bool = False
    split_bill_id: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    completed_at: Optional[datetime] = None
