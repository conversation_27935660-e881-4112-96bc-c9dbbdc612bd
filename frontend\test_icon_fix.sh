#!/bin/bash

echo "=== Testing Analytics Page Icon Fix ==="
echo

echo "1. Checking Icon Imports in Analytics.tsx:"
if grep -q "import { TrendingUp, DollarSign, Users, ShoppingBag } from \"lucide-react\"" src/pages/Analytics.tsx; then
    echo "✅ All required icons imported from lucide-react"
else
    echo "❌ Icon imports missing or incorrect"
fi
echo

echo "2. Verifying Icon Usage:"
echo "Users icon:"
if grep -q "icon={<Users className=\"h-5 w-5\" />}" src/pages/Analytics.tsx; then
    echo "✅ Users icon properly used"
else
    echo "❌ Users icon usage issue"
fi

echo "ShoppingBag icon:"
if grep -q "icon={<ShoppingBag className=\"h-5 w-5\" />}" src/pages/Analytics.tsx; then
    echo "✅ ShoppingBag icon properly used"
else
    echo "❌ ShoppingBag icon usage issue"
fi

echo "TrendingUp icon:"
if grep -q "icon={<TrendingUp className=\"h-5 w-5\" />}" src/pages/Analytics.tsx; then
    echo "✅ TrendingUp icon properly used"
else
    echo "❌ TrendingUp icon usage issue"
fi
echo

echo "3. Testing Frontend Server:"
if curl -s http://localhost:5174/ | head -c 20 | grep -q "html\|HTML"; then
    echo "✅ Frontend server responding on port 5174"
else
    echo "❌ Frontend server not responding properly"
fi
echo

echo "4. Testing Analytics API Endpoints:"
if curl -s http://localhost:5174/api/sales | head -c 50 | grep -q "date\|sales"; then
    echo "✅ Analytics API endpoints accessible"
else
    echo "❌ Analytics API endpoints not accessible"
fi
echo

echo "=== Summary ==="
echo "The ReferenceError: Users is not defined should now be resolved!"
echo "Frontend: http://localhost:5174"
echo "Analytics Page: http://localhost:5174/admin/analytics"
echo "Backend: http://localhost:5001"
echo
echo "All lucide-react icons (Users, ShoppingBag, TrendingUp, DollarSign) are now properly imported."
