from fastapi import APIRouter, File, UploadFile, HTTPException, Form, status
from fastapi.responses import FileResponse
from fastapi.staticfiles import StaticFiles
import os
import json
import aiofiles
from pathlib import Path
from typing import Optional
import uuid
from datetime import datetime

router = APIRouter(prefix="/storage", tags=["File Storage"])

# Create upload directories
UPLOAD_BASE_DIR = Path("uploads")
LOGOS_DIR = UPLOAD_BASE_DIR / "logos"
DATA_DIR = UPLOAD_BASE_DIR / "data"

def create_upload_directories():
    """Create upload directories if they don't exist"""
    UPLOAD_BASE_DIR.mkdir(exist_ok=True)
    LOGOS_DIR.mkdir(exist_ok=True)
    DATA_DIR.mkdir(exist_ok=True)
    print(f"Upload directories created/verified: {UPLOAD_BASE_DIR}")

# Initialize directories on import
create_upload_directories()

@router.get("/health")
async def health_check():
    """Health check endpoint for file storage service"""
    return {
        "status": "ok", 
        "message": "File storage service is running",
        "upload_dirs": {
            "base": str(UPLOAD_BASE_DIR),
            "logos": str(LOGOS_DIR),
            "data": str(DATA_DIR)
        }
    }

@router.post("/logo")
async def upload_logo(
    logo: UploadFile = File(...),
    restaurant_code: str = Form(...)
):
    """Upload restaurant logo"""
    try:
        # Validate file type
        allowed_types = ["image/jpeg", "image/jpg", "image/png", "image/svg+xml"]
        if logo.content_type not in allowed_types:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid file type. Only PNG, JPG, and SVG files are allowed."
            )
        
        # Validate file size (2MB limit)
        if logo.size and logo.size > 2 * 1024 * 1024:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="File too large. Maximum size is 2MB."
            )
        
        # Generate filename
        timestamp = int(datetime.now().timestamp())
        file_extension = Path(logo.filename).suffix
        filename = f"{restaurant_code}_{timestamp}{file_extension}"
        file_path = LOGOS_DIR / filename
        
        # Save file
        async with aiofiles.open(file_path, 'wb') as f:
            content = await logo.read()
            await f.write(content)
        
        logo_url = f"/uploads/logos/{filename}"
        
        print(f"Logo uploaded: {filename}")
        return {
            "success": True,
            "message": "Logo uploaded successfully",
            "path": logo_url,
            "filename": filename
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error uploading logo: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to upload logo: {str(e)}"
        )

@router.post("/restaurant")
async def save_restaurant_data(restaurant_data: dict):
    """Save restaurant data to file"""
    try:
        restaurant_code = restaurant_data.get("restaurantCode")
        if not restaurant_code:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Restaurant code is required"
            )
        
        filename = f"restaurant_{restaurant_code}.json"
        file_path = DATA_DIR / filename
        
        # Save restaurant data
        async with aiofiles.open(file_path, 'w') as f:
            await f.write(json.dumps(restaurant_data, indent=2))
        
        print(f"Restaurant data saved: {filename}")
        return {
            "success": True,
            "message": "Restaurant data saved successfully",
            "filename": filename
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error saving restaurant data: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to save restaurant data: {str(e)}"
        )

@router.get("/restaurant/{code}")
async def load_restaurant_data(code: str):
    """Load restaurant data by code"""
    try:
        filename = f"restaurant_{code}.json"
        file_path = DATA_DIR / filename
        
        if not file_path.exists():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Restaurant data not found"
            )
        
        async with aiofiles.open(file_path, 'r') as f:
            content = await f.read()
            restaurant_data = json.loads(content)
        
        print(f"Restaurant data loaded: {filename}")
        return restaurant_data
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error loading restaurant data: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to load restaurant data: {str(e)}"
        )

@router.get("/restaurants")
async def load_all_restaurant_data():
    """Load all restaurant data files"""
    try:
        restaurants = []
        
        # Get all restaurant JSON files
        for file_path in DATA_DIR.glob("restaurant_*.json"):
            try:
                async with aiofiles.open(file_path, 'r') as f:
                    content = await f.read()
                    restaurant_data = json.loads(content)
                    restaurants.append(restaurant_data)
            except Exception as e:
                print(f"Error reading file {file_path}: {e}")
                continue
        
        print(f"Loaded {len(restaurants)} restaurant data files")
        return restaurants
        
    except Exception as e:
        print(f"Error loading all restaurant data: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to load restaurant data: {str(e)}"
        )
