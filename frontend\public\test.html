<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Browser Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2563eb;
        }
        .button {
            display: inline-block;
            background-color: #2563eb;
            color: white;
            padding: 10px 20px;
            border-radius: 4px;
            text-decoration: none;
            margin-top: 20px;
        }
        .button:hover {
            background-color: #1d4ed8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Browser Test Page</h1>
        <p>If you can see this page, your browser is working correctly with basic HTML rendering.</p>
        <p>This is a simple test page to check if your browser can render HTML content.</p>
        <a href="/" class="button">Go to Main Application</a>
    </div>
    <script>
        // Simple script to test JavaScript execution
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded successfully');
            alert('Test page loaded successfully');
        });
    </script>
</body>
</html>
