import numpy as np
import pandas as pd
from datetime import datetime, timedelta, date
from typing import List, Dict, Tuple, Any
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

from app.models.forecasting import (
    ForecastDataPoint, TrendAnalysis, TrendDirection, ConfidenceLevel,
    SeasonalPattern, ForecastMetrics
)

class ForecastingEngine:
    """Main forecasting engine with multiple algorithms"""
    
    def __init__(self):
        self.scaler = StandardScaler()
        self.models = {
            'linear_regression': LinearRegression(),
            'random_forest': RandomForestRegressor(n_estimators=100, random_state=42)
        }
    
    def prepare_time_series_features(self, dates: List[date], values: List[float]) -> pd.DataFrame:
        """Prepare time series features for machine learning models"""
        df = pd.DataFrame({
            'date': pd.to_datetime(dates),
            'value': values
        })
        
        # Sort by date
        df = df.sort_values('date').reset_index(drop=True)
        
        # Create time-based features
        df['day_of_week'] = df['date'].dt.dayofweek
        df['day_of_month'] = df['date'].dt.day
        df['month'] = df['date'].dt.month
        df['quarter'] = df['date'].dt.quarter
        df['is_weekend'] = (df['day_of_week'] >= 5).astype(int)
        df['is_month_start'] = (df['day_of_month'] <= 7).astype(int)
        df['is_month_end'] = (df['day_of_month'] >= 24).astype(int)
        
        # Create lag features
        for lag in [1, 7, 14, 30]:
            if len(df) > lag:
                df[f'lag_{lag}'] = df['value'].shift(lag)
        
        # Create rolling averages
        for window in [3, 7, 14]:
            if len(df) >= window:
                df[f'rolling_mean_{window}'] = df['value'].rolling(window=window).mean()
                df[f'rolling_std_{window}'] = df['value'].rolling(window=window).std()
        
        # Create trend features
        df['trend'] = range(len(df))
        
        return df
    
    def detect_seasonality(self, values: List[float], period: int = 7) -> Dict[str, Any]:
        """Detect seasonal patterns in the data"""
        if len(values) < period * 2:
            return {"has_seasonality": False, "strength": 0.0}
        
        # Calculate seasonal decomposition manually
        values_array = np.array(values)
        seasonal_means = []
        
        for i in range(period):
            seasonal_values = values_array[i::period]
            if len(seasonal_values) > 0:
                seasonal_means.append(np.mean(seasonal_values))
        
        # Calculate seasonality strength
        overall_mean = np.mean(values)
        seasonal_variance = np.var(seasonal_means) if seasonal_means else 0
        total_variance = np.var(values) if len(values) > 1 else 0
        
        seasonality_strength = seasonal_variance / total_variance if total_variance > 0 else 0
        
        return {
            "has_seasonality": seasonality_strength > 0.1,
            "strength": min(seasonality_strength, 1.0),
            "seasonal_means": seasonal_means,
            "period": period
        }
    
    def calculate_trend(self, values: List[float]) -> TrendAnalysis:
        """Calculate trend analysis for the data"""
        if len(values) < 2:
            return TrendAnalysis(
                direction=TrendDirection.STABLE,
                strength=0.0,
                description="Insufficient data for trend analysis",
                change_percentage=0.0
            )
        
        # Simple linear trend
        x = np.arange(len(values)).reshape(-1, 1)
        y = np.array(values)
        
        model = LinearRegression()
        model.fit(x, y)
        
        slope = model.coef_[0]
        r_squared = model.score(x, y)
        
        # Determine trend direction
        if abs(slope) < 0.01:
            direction = TrendDirection.STABLE
        elif slope > 0:
            direction = TrendDirection.INCREASING
        else:
            direction = TrendDirection.DECREASING
        
        # Calculate percentage change
        if len(values) >= 2:
            start_value = np.mean(values[:len(values)//3]) if len(values) > 6 else values[0]
            end_value = np.mean(values[-len(values)//3:]) if len(values) > 6 else values[-1]
            change_percentage = ((end_value - start_value) / start_value * 100) if start_value != 0 else 0
        else:
            change_percentage = 0
        
        # Generate description
        if direction == TrendDirection.INCREASING:
            description = f"Upward trend with {abs(change_percentage):.1f}% increase"
        elif direction == TrendDirection.DECREASING:
            description = f"Downward trend with {abs(change_percentage):.1f}% decrease"
        else:
            description = "Stable trend with minimal change"
        
        return TrendAnalysis(
            direction=direction,
            strength=min(abs(r_squared), 1.0),
            description=description,
            change_percentage=change_percentage
        )
    
    def forecast_linear_regression(self, df: pd.DataFrame, days_ahead: int) -> Tuple[List[float], List[float], ForecastMetrics]:
        """Generate forecast using linear regression"""
        # Prepare features
        feature_cols = [col for col in df.columns if col not in ['date', 'value'] and not df[col].isna().all()]
        
        if not feature_cols:
            # Fallback to simple trend if no features available
            return self.forecast_simple_trend(df['value'].tolist(), days_ahead)
        
        # Remove rows with NaN values
        clean_df = df[['value'] + feature_cols].dropna()
        
        if len(clean_df) < 3:
            return self.forecast_simple_trend(df['value'].tolist(), days_ahead)
        
        X = clean_df[feature_cols]
        y = clean_df['value']
        
        # Fit model
        model = self.models['linear_regression']
        model.fit(X, y)
        
        # Calculate metrics
        y_pred = model.predict(X)
        mae = mean_absolute_error(y, y_pred)
        mse = mean_squared_error(y, y_pred)
        r2 = r2_score(y, y_pred)
        
        metrics = ForecastMetrics(
            accuracy=max(0, min(1, r2)),
            mean_absolute_error=mae,
            mean_squared_error=mse,
            r_squared=r2,
            model_type="Linear Regression"
        )
        
        # Generate future features
        last_date = df['date'].max()
        future_dates = [last_date + timedelta(days=i+1) for i in range(days_ahead)]
        
        future_features = []
        for future_date in future_dates:
            features = {
                'day_of_week': future_date.weekday(),
                'day_of_month': future_date.day,
                'month': future_date.month,
                'quarter': (future_date.month - 1) // 3 + 1,
                'is_weekend': 1 if future_date.weekday() >= 5 else 0,
                'is_month_start': 1 if future_date.day <= 7 else 0,
                'is_month_end': 1 if future_date.day >= 24 else 0,
                'trend': len(df) + len(future_features)
            }
            
            # Use last known values for lag features
            for lag in [1, 7, 14, 30]:
                if f'lag_{lag}' in feature_cols:
                    if lag <= len(df):
                        features[f'lag_{lag}'] = df['value'].iloc[-lag]
                    else:
                        features[f'lag_{lag}'] = df['value'].mean()
            
            # Use last known values for rolling features
            for window in [3, 7, 14]:
                if f'rolling_mean_{window}' in feature_cols:
                    features[f'rolling_mean_{window}'] = df['value'].tail(window).mean()
                if f'rolling_std_{window}' in feature_cols:
                    features[f'rolling_std_{window}'] = df['value'].tail(window).std()
            
            future_features.append(features)
        
        # Create future DataFrame
        future_df = pd.DataFrame(future_features)
        
        # Ensure all required columns are present
        for col in feature_cols:
            if col not in future_df.columns:
                future_df[col] = df[col].mean()
        
        # Make predictions
        future_X = future_df[feature_cols]
        predictions = model.predict(future_X)
        
        # Calculate confidence intervals (simple approach)
        residuals = y - y_pred
        std_residual = np.std(residuals)
        confidence_intervals = [std_residual * 1.96] * len(predictions)  # 95% confidence
        
        return predictions.tolist(), confidence_intervals, metrics
    
    def forecast_simple_trend(self, values: List[float], days_ahead: int) -> Tuple[List[float], List[float], ForecastMetrics]:
        """Simple trend-based forecast as fallback"""
        if len(values) < 2:
            avg_value = values[0] if values else 0
            predictions = [avg_value] * days_ahead
            confidence_intervals = [avg_value * 0.1] * days_ahead
            metrics = ForecastMetrics(
                accuracy=0.5,
                mean_absolute_error=0,
                mean_squared_error=0,
                r_squared=0,
                model_type="Simple Average"
            )
            return predictions, confidence_intervals, metrics
        
        # Calculate simple linear trend
        x = np.arange(len(values))
        y = np.array(values)
        
        # Fit linear regression
        slope, intercept = np.polyfit(x, y, 1)
        
        # Generate predictions
        future_x = np.arange(len(values), len(values) + days_ahead)
        predictions = slope * future_x + intercept
        
        # Calculate confidence intervals
        residuals = y - (slope * x + intercept)
        std_residual = np.std(residuals)
        confidence_intervals = [std_residual * 1.96] * days_ahead
        
        # Calculate metrics
        y_pred = slope * x + intercept
        mae = mean_absolute_error(y, y_pred)
        mse = mean_squared_error(y, y_pred)
        r2 = r2_score(y, y_pred) if len(y) > 1 else 0
        
        metrics = ForecastMetrics(
            accuracy=max(0, min(1, r2)),
            mean_absolute_error=mae,
            mean_squared_error=mse,
            r_squared=r2,
            model_type="Simple Linear Trend"
        )
        
        return predictions.tolist(), confidence_intervals, metrics
    
    def generate_forecast_points(self, start_date: date, predictions: List[float], 
                               confidence_intervals: List[float]) -> List[ForecastDataPoint]:
        """Generate forecast data points with confidence intervals"""
        forecast_points = []
        
        for i, (pred, ci) in enumerate(zip(predictions, confidence_intervals)):
            forecast_date = start_date + timedelta(days=i+1)
            
            # Determine confidence level based on CI width
            ci_ratio = ci / abs(pred) if pred != 0 else 1
            if ci_ratio < 0.1:
                confidence_level = ConfidenceLevel.HIGH
            elif ci_ratio < 0.3:
                confidence_level = ConfidenceLevel.MEDIUM
            else:
                confidence_level = ConfidenceLevel.LOW
            
            forecast_points.append(ForecastDataPoint(
                date=forecast_date,
                value=max(0, pred),  # Ensure non-negative values
                confidence_interval_lower=max(0, pred - ci),
                confidence_interval_upper=pred + ci,
                confidence_level=confidence_level
            ))
        
        return forecast_points
