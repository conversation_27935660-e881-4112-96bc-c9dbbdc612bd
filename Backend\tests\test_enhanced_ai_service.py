"""
Unit tests for Enhanced AI Service
Tests the AI service with function calling capabilities
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

from app.services.enhanced_ai_service import EnhancedAIService, enhanced_ai_service


class TestEnhancedAIService:
    """Test suite for Enhanced AI Service"""
    
    @pytest.fixture
    def service(self):
        """Create service instance for testing"""
        service = EnhancedAIService()
        # Mock the initialization to avoid actual API calls
        service.enabled = True
        service.model = MagicMock()
        service.tools = MagicMock()
        return service
    
    @pytest.fixture
    def disabled_service(self):
        """Create disabled service instance for testing"""
        service = EnhancedAIService()
        service.enabled = False
        service.model = None
        return service
    
    @pytest.fixture
    def sample_restaurant_id(self):
        """Sample restaurant ID for testing"""
        return "restaurant_123"
    
    @pytest.fixture
    def sample_query(self):
        """Sample user query for testing"""
        return "What were today's sales?"
    
    @pytest.mark.asyncio
    async def test_service_initialization_success(self):
        """Test successful service initialization"""
        with patch.dict('os.environ', {'GOOGLE_API_KEY': 'test_key'}):
            with patch('google.generativeai.configure') as mock_configure:
                with patch('google.generativeai.GenerativeModel') as mock_model:
                    service = EnhancedAIService()
                    
                    mock_configure.assert_called_once_with(api_key='test_key')
                    mock_model.assert_called_once()
                    assert service.enabled is True
    
    @pytest.mark.asyncio
    async def test_service_initialization_no_api_key(self):
        """Test service initialization without API key"""
        with patch.dict('os.environ', {}, clear=True):
            service = EnhancedAIService()
            assert service.enabled is False
            assert service.model is None
    
    def test_is_enabled(self, service, disabled_service):
        """Test is_enabled method"""
        assert service.is_enabled() is True
        assert disabled_service.is_enabled() is False
    
    @pytest.mark.asyncio
    async def test_execute_function_call_success(self, service, sample_restaurant_id):
        """Test successful function call execution"""
        with patch('app.services.enhanced_ai_service.ai_data_service') as mock_data_service:
            mock_data_service.get_daily_sales.return_value = {
                "success": True,
                "total_revenue": 150.0,
                "total_orders": 10
            }
            
            result = await service._execute_function_call(
                "get_daily_sales",
                {"restaurant_id": sample_restaurant_id}
            )
            
            assert result["success"] is True
            assert result["total_revenue"] == 150.0
            mock_data_service.get_daily_sales.assert_called_once_with(
                restaurant_id=sample_restaurant_id
            )
    
    @pytest.mark.asyncio
    async def test_execute_function_call_unknown_function(self, service):
        """Test function call with unknown function name"""
        result = await service._execute_function_call(
            "unknown_function",
            {"restaurant_id": "test"}
        )
        
        assert result["success"] is False
        assert "Unknown function" in result["error"]
    
    @pytest.mark.asyncio
    async def test_execute_function_call_error(self, service, sample_restaurant_id):
        """Test function call with execution error"""
        with patch('app.services.enhanced_ai_service.ai_data_service') as mock_data_service:
            mock_data_service.get_daily_sales.side_effect = Exception("Database error")
            
            result = await service._execute_function_call(
                "get_daily_sales",
                {"restaurant_id": sample_restaurant_id}
            )
            
            assert result["success"] is False
            assert "Database error" in result["error"]
    
    @pytest.mark.asyncio
    async def test_process_intelligent_query_disabled_service(self, disabled_service, sample_query, sample_restaurant_id):
        """Test query processing with disabled service"""
        result = await disabled_service.process_intelligent_query(
            query=sample_query,
            restaurant_id=sample_restaurant_id
        )
        
        assert result["success"] is False
        assert result["ai_enabled"] is False
        assert "not available" in result["response"]
    
    @pytest.mark.asyncio
    async def test_process_intelligent_query_success(self, service, sample_query, sample_restaurant_id):
        """Test successful query processing"""
        # Mock the AI model response
        mock_chat = MagicMock()
        mock_response = MagicMock()
        mock_response.text = "Based on the sales data, you had 10 orders totaling £150 today."
        mock_response.candidates = [MagicMock()]
        mock_response.candidates[0].content.parts = []
        
        mock_chat.send_message.return_value = mock_response
        service.model.start_chat.return_value = mock_chat
        
        result = await service.process_intelligent_query(
            query=sample_query,
            restaurant_id=sample_restaurant_id
        )
        
        assert result["success"] is True
        assert result["ai_enabled"] is True
        assert result["restaurant_id"] == sample_restaurant_id
        assert "sales data" in result["response"]
        assert "timestamp" in result
    
    @pytest.mark.asyncio
    async def test_process_intelligent_query_with_function_calls(self, service, sample_query, sample_restaurant_id):
        """Test query processing with function calls"""
        # Mock function call in AI response
        mock_function_call = MagicMock()
        mock_function_call.name = "get_daily_sales"
        mock_function_call.args = {"restaurant_id": sample_restaurant_id}
        
        mock_part = MagicMock()
        mock_part.function_call = mock_function_call
        
        mock_chat = MagicMock()
        mock_response1 = MagicMock()
        mock_response1.candidates = [MagicMock()]
        mock_response1.candidates[0].content.parts = [mock_part]
        mock_response1.text = None
        
        mock_response2 = MagicMock()
        mock_response2.text = "Based on the sales data, you had excellent performance today."
        mock_response2.candidates = [MagicMock()]
        mock_response2.candidates[0].content.parts = []
        
        mock_chat.send_message.side_effect = [mock_response1, mock_response2]
        service.model.start_chat.return_value = mock_chat
        
        # Mock the function execution
        with patch.object(service, '_execute_function_call') as mock_execute:
            mock_execute.return_value = {
                "success": True,
                "total_revenue": 150.0,
                "total_orders": 10
            }
            
            result = await service.process_intelligent_query(
                query=sample_query,
                restaurant_id=sample_restaurant_id
            )
            
            assert result["success"] is True
            assert len(result["function_calls"]) == 1
            assert result["function_calls"][0]["function"] == "get_daily_sales"
            mock_execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_process_intelligent_query_error(self, service, sample_query, sample_restaurant_id):
        """Test query processing with error"""
        service.model.start_chat.side_effect = Exception("AI model error")
        
        result = await service.process_intelligent_query(
            query=sample_query,
            restaurant_id=sample_restaurant_id
        )
        
        assert result["success"] is False
        assert "error" in result
        assert "AI model error" in result["response"]
    
    @pytest.mark.asyncio
    async def test_get_restaurant_summary_success(self, service, sample_restaurant_id):
        """Test successful restaurant summary generation"""
        # Mock data service calls
        with patch('app.services.enhanced_ai_service.ai_data_service') as mock_data_service:
            mock_data_service.get_daily_sales.return_value = {"success": True, "total_revenue": 150.0}
            mock_data_service.get_inventory_levels.return_value = {"success": True, "low_stock_items": 2}
            mock_data_service.get_customer_analytics.return_value = {"success": True, "total_customers": 25}
            mock_data_service.get_menu_performance.return_value = {"success": True, "top_performers": []}
            mock_data_service.get_operational_insights.return_value = {"success": True, "total_orders": 10}
            
            # Mock AI response
            mock_response = MagicMock()
            mock_response.text = "Your restaurant is performing well with strong sales and customer engagement."
            service.model.generate_content.return_value = mock_response
            
            result = await service.get_restaurant_summary(sample_restaurant_id)
            
            assert result["success"] is True
            assert result["restaurant_id"] == sample_restaurant_id
            assert "performing well" in result["summary"]
            assert "data" in result
    
    @pytest.mark.asyncio
    async def test_get_restaurant_summary_disabled_service(self, disabled_service, sample_restaurant_id):
        """Test restaurant summary with disabled service"""
        with patch('app.services.enhanced_ai_service.ai_data_service') as mock_data_service:
            mock_data_service.get_daily_sales.return_value = {"success": True}
            mock_data_service.get_inventory_levels.return_value = {"success": True}
            mock_data_service.get_customer_analytics.return_value = {"success": True}
            mock_data_service.get_menu_performance.return_value = {"success": True}
            mock_data_service.get_operational_insights.return_value = {"success": True}
            
            result = await disabled_service.get_restaurant_summary(sample_restaurant_id)
            
            assert result["success"] is True
            assert "not available" in result["summary"]
    
    @pytest.mark.asyncio
    async def test_get_restaurant_summary_error(self, service, sample_restaurant_id):
        """Test restaurant summary with error"""
        with patch('app.services.enhanced_ai_service.ai_data_service') as mock_data_service:
            mock_data_service.get_daily_sales.side_effect = Exception("Data error")
            
            result = await service.get_restaurant_summary(sample_restaurant_id)
            
            assert result["success"] is False
            assert "error" in result


class TestEnhancedAIServiceIntegration:
    """Integration tests for Enhanced AI Service"""
    
    def test_global_service_instance(self):
        """Test that the global service instance exists"""
        assert enhanced_ai_service is not None
        assert isinstance(enhanced_ai_service, EnhancedAIService)
    
    def test_function_declarations_structure(self):
        """Test that function declarations are properly structured"""
        service = EnhancedAIService()
        
        # Even if disabled, the tools structure should be defined
        expected_functions = [
            "get_daily_sales",
            "get_inventory_levels", 
            "get_customer_analytics",
            "get_menu_performance",
            "get_operational_insights",
            "get_revenue_trends"
        ]
        
        # This test would need to be adapted based on actual implementation
        # For now, just check that the service has the expected structure
        assert hasattr(service, '_execute_function_call')
        assert hasattr(service, 'process_intelligent_query')
        assert hasattr(service, 'get_restaurant_summary')


class TestAIServiceConfiguration:
    """Test AI service configuration and environment handling"""
    
    def test_environment_variable_handling(self):
        """Test proper handling of environment variables"""
        with patch.dict('os.environ', {
            'GOOGLE_API_KEY': 'test_key',
            'GEMINI_MODEL': 'gemini-1.5-pro',
            'GOOGLE_AI_TEMPERATURE': '0.5',
            'GOOGLE_AI_MAX_TOKENS': '1500'
        }):
            service = EnhancedAIService()
            
            assert service.api_key == 'test_key'
            assert service.model_name == 'gemini-1.5-pro'
            assert service.temperature == 0.5
            assert service.max_tokens == 1500
    
    def test_default_configuration(self):
        """Test default configuration values"""
        with patch.dict('os.environ', {}, clear=True):
            service = EnhancedAIService()
            
            assert service.model_name == "gemini-1.5-flash"
            assert service.temperature == 0.7
            assert service.max_tokens == 2000


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
