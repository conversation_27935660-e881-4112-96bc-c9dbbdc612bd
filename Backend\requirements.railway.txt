# Core FastAPI dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
gunicorn==21.2.0

# Database dependencies
sqlalchemy>=2.0.0
alembic==1.13.1
psycopg2-binary==2.9.9
aiosqlite==0.19.0
databases==0.8.0

# Authentication and security
pydantic==2.5.0
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
bcrypt==4.1.2

# File handling
python-multipart==0.0.6
aiofiles==23.2.1

# Environment and configuration
python-dotenv==1.0.0

# Redis for caching
redis==5.0.1

# HTTP client
httpx==0.25.2
aiohttp>=3.8.0

# Data processing
pandas==2.1.4
openpyxl==3.1.2
pypdf==3.17.4
numpy==1.26.2
scikit-learn==1.3.2
matplotlib==3.8.2

# LLM Integration
openai>=1.0.0
anthropic>=0.7.0
google-generativeai>=0.3.2
tiktoken>=0.5.0

# Utilities
tenacity==8.2.3
email-validator==2.1.0
asyncio-throttle>=1.0.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1

# Production dependencies
structlog==23.2.0
prometheus-client==0.19.0

# Health checks and monitoring
psutil==5.9.6
