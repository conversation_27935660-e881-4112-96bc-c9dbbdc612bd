<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notifications API Test & Demo Data</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            line-height: 1.6;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #28a745;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .button:hover {
            background: #0056b3;
        }
        .button.danger {
            background: #dc3545;
        }
        .button.danger:hover {
            background: #c82333;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .notification-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
        .notification-item.unread {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        .notification-item.high-priority {
            border-left-color: #dc3545;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔔 Notifications API Test & Demo Data</h1>
        <p>Test the notification system and create demo data for the RestroManage-v0 application.</p>
        
        <div class="test-section">
            <h3>1. Backend Health Check</h3>
            <button class="button" onclick="testBackendHealth()">Test Backend Health</button>
            <div id="health-status" class="status info">Click button to test backend connectivity...</div>
        </div>

        <div class="test-section">
            <h3>2. Create Demo Notifications</h3>
            <button class="button" onclick="createDemoNotifications()">Create Demo Notifications</button>
            <button class="button" onclick="createSingleNotification()">Create Single Test Notification</button>
            <div id="create-status" class="status info">Click buttons to create test notifications...</div>
        </div>

        <div class="test-section">
            <h3>3. View Notifications</h3>
            <button class="button" onclick="fetchNotifications()">Fetch All Notifications</button>
            <button class="button" onclick="fetchNotificationStats()">Fetch Notification Stats</button>
            <div id="fetch-status" class="status info">Click buttons to fetch notifications...</div>
            <div id="notifications-list"></div>
        </div>

        <div class="test-section">
            <h3>4. Notification Management</h3>
            <button class="button" onclick="markAllAsRead()">Mark All as Read</button>
            <button class="button danger" onclick="deleteAllNotifications()">Delete All Notifications</button>
            <div id="management-status" class="status info">Click buttons to manage notifications...</div>
        </div>

        <div class="test-section">
            <h3>5. API Endpoints</h3>
            <div class="code-block">
Base URL: http://localhost:5001/api

Endpoints:
- GET /notifications - Get all notifications
- POST /notifications - Create notification
- GET /notifications/stats - Get notification statistics
- PUT /notifications/mark-read - Mark notifications as read
- DELETE /notifications/{id} - Delete notification
- DELETE /notifications - Delete all notifications

Headers required:
- X-Restaurant-ID: 1 (or your restaurant ID)
- Content-Type: application/json
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5001/api';
        const RESTAURANT_ID = '1'; // Default restaurant ID for testing

        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }

        async function testBackendHealth() {
            try {
                updateStatus('health-status', 'Testing backend health...', 'info');
                
                const response = await fetch('http://localhost:5001/health');
                const data = await response.json();
                
                if (response.ok) {
                    updateStatus('health-status', `✅ Backend is healthy! Status: ${data.status}`, 'success');
                } else {
                    updateStatus('health-status', `❌ Backend health check failed: ${response.status}`, 'error');
                }
            } catch (error) {
                updateStatus('health-status', `❌ Backend connection failed: ${error.message}`, 'error');
            }
        }

        async function createDemoNotifications() {
            try {
                updateStatus('create-status', 'Creating demo notifications...', 'info');
                
                const demoNotifications = [
                    {
                        title: 'New Order Received',
                        message: 'Order #1234 has been placed by table 5. Total: £45.50',
                        type: 'info',
                        priority: 'medium'
                    },
                    {
                        title: 'Low Stock Alert',
                        message: 'Chicken breast is running low (5 portions remaining)',
                        type: 'warning',
                        priority: 'high'
                    },
                    {
                        title: 'Staff Clock-In',
                        message: 'John Smith has clocked in for the evening shift',
                        type: 'success',
                        priority: 'low'
                    },
                    {
                        title: 'Payment Failed',
                        message: 'Payment for order #1230 failed. Please retry or use alternative method.',
                        type: 'error',
                        priority: 'high'
                    },
                    {
                        title: 'Table Ready',
                        message: 'Table 8 is ready for the next guests',
                        type: 'success',
                        priority: 'medium'
                    },
                    {
                        title: 'Kitchen Alert',
                        message: 'Order #1235 has been waiting for 15 minutes',
                        type: 'warning',
                        priority: 'high'
                    }
                ];

                let successCount = 0;
                for (const notification of demoNotifications) {
                    try {
                        const response = await fetch(`${API_BASE}/notifications`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-Restaurant-ID': RESTAURANT_ID
                            },
                            body: JSON.stringify(notification)
                        });

                        if (response.ok) {
                            successCount++;
                        }
                    } catch (error) {
                        console.error('Failed to create notification:', error);
                    }
                }

                updateStatus('create-status', `✅ Created ${successCount}/${demoNotifications.length} demo notifications`, 'success');
            } catch (error) {
                updateStatus('create-status', `❌ Failed to create demo notifications: ${error.message}`, 'error');
            }
        }

        async function createSingleNotification() {
            try {
                updateStatus('create-status', 'Creating test notification...', 'info');
                
                const notification = {
                    title: 'Test Notification',
                    message: `Test notification created at ${new Date().toLocaleTimeString()}`,
                    type: 'info',
                    priority: 'medium'
                };

                const response = await fetch(`${API_BASE}/notifications`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Restaurant-ID': RESTAURANT_ID
                    },
                    body: JSON.stringify(notification)
                });

                if (response.ok) {
                    const data = await response.json();
                    updateStatus('create-status', `✅ Test notification created with ID: ${data.id}`, 'success');
                } else {
                    updateStatus('create-status', `❌ Failed to create notification: ${response.status}`, 'error');
                }
            } catch (error) {
                updateStatus('create-status', `❌ Failed to create notification: ${error.message}`, 'error');
            }
        }

        async function fetchNotifications() {
            try {
                updateStatus('fetch-status', 'Fetching notifications...', 'info');
                
                const response = await fetch(`${API_BASE}/notifications?restaurant_id=${RESTAURANT_ID}`);
                
                if (response.ok) {
                    const notifications = await response.json();
                    updateStatus('fetch-status', `✅ Fetched ${notifications.length} notifications`, 'success');
                    displayNotifications(notifications);
                } else {
                    updateStatus('fetch-status', `❌ Failed to fetch notifications: ${response.status}`, 'error');
                }
            } catch (error) {
                updateStatus('fetch-status', `❌ Failed to fetch notifications: ${error.message}`, 'error');
            }
        }

        async function fetchNotificationStats() {
            try {
                updateStatus('fetch-status', 'Fetching notification stats...', 'info');
                
                const response = await fetch(`${API_BASE}/notifications/stats?restaurant_id=${RESTAURANT_ID}`);
                
                if (response.ok) {
                    const stats = await response.json();
                    updateStatus('fetch-status', `✅ Stats: ${stats.total_count} total, ${stats.unread_count} unread`, 'success');
                } else {
                    updateStatus('fetch-status', `❌ Failed to fetch stats: ${response.status}`, 'error');
                }
            } catch (error) {
                updateStatus('fetch-status', `❌ Failed to fetch stats: ${error.message}`, 'error');
            }
        }

        async function markAllAsRead() {
            try {
                updateStatus('management-status', 'Marking all notifications as read...', 'info');
                
                const response = await fetch(`${API_BASE}/notifications/mark-all-read`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Restaurant-ID': RESTAURANT_ID
                    }
                });

                if (response.ok) {
                    updateStatus('management-status', '✅ All notifications marked as read', 'success');
                } else {
                    updateStatus('management-status', `❌ Failed to mark as read: ${response.status}`, 'error');
                }
            } catch (error) {
                updateStatus('management-status', `❌ Failed to mark as read: ${error.message}`, 'error');
            }
        }

        async function deleteAllNotifications() {
            if (!confirm('Are you sure you want to delete ALL notifications? This cannot be undone.')) {
                return;
            }

            try {
                updateStatus('management-status', 'Deleting all notifications...', 'info');
                
                const response = await fetch(`${API_BASE}/notifications`, {
                    method: 'DELETE',
                    headers: {
                        'X-Restaurant-ID': RESTAURANT_ID
                    }
                });

                if (response.ok) {
                    updateStatus('management-status', '✅ All notifications deleted', 'success');
                    document.getElementById('notifications-list').innerHTML = '';
                } else {
                    updateStatus('management-status', `❌ Failed to delete notifications: ${response.status}`, 'error');
                }
            } catch (error) {
                updateStatus('management-status', `❌ Failed to delete notifications: ${error.message}`, 'error');
            }
        }

        function displayNotifications(notifications) {
            const container = document.getElementById('notifications-list');
            
            if (notifications.length === 0) {
                container.innerHTML = '<p>No notifications found.</p>';
                return;
            }

            const notificationHtml = notifications.map(notification => `
                <div class="notification-item ${!notification.is_read ? 'unread' : ''} ${notification.priority === 'high' ? 'high-priority' : ''}">
                    <strong>${notification.title}</strong>
                    <p>${notification.message}</p>
                    <small>
                        Type: ${notification.type} | Priority: ${notification.priority} | 
                        ${notification.is_read ? 'Read' : 'Unread'} | 
                        Created: ${new Date(notification.created_at).toLocaleString()}
                    </small>
                </div>
            `).join('');

            container.innerHTML = notificationHtml;
        }

        // Auto-test backend health on page load
        window.addEventListener('load', () => {
            testBackendHealth();
        });
    </script>
</body>
</html>
