import { useAuth } from "@/contexts/AuthContext";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";

const AuthDebug = () => {
  const { 
    user, 
    isAuthenticated, 
    isRestaurantAuthenticated, 
    currentRestaurant,
    logout,
    logoutRestaurant
  } = useAuth();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate('/staff-login');
  };

  const handleRestaurantLogout = () => {
    logoutRestaurant();
    navigate('/');
  };

  return (
    <Card className="mb-6 border-dashed border-yellow-500 bg-yellow-50">
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium text-yellow-800">Debug Authentication State</CardTitle>
      </CardHeader>
      <CardContent className="text-xs">
        <div className="space-y-2">
          <div>
            <span className="font-semibold">Restaurant Authenticated:</span> {isRestaurantAuthenticated ? 'Yes' : 'No'}
          </div>
          {currentRestaurant && (
            <div>
              <span className="font-semibold">Current Restaurant:</span> {currentRestaurant.name} (ID: {currentRestaurant.id}, Code: {currentRestaurant.code})
            </div>
          )}
          <div>
            <span className="font-semibold">User Authenticated:</span> {isAuthenticated ? 'Yes' : 'No'}
          </div>
          {user && (
            <div className="space-y-1">
              <div>
                <span className="font-semibold">User:</span> {user.name} (ID: {user.id})
              </div>
              <div>
                <span className="font-semibold">Role:</span> {user.role}
              </div>
              <div>
                <span className="font-semibold">Access Level:</span> {user.accessLevel}
              </div>
              <div>
                <span className="font-semibold">Position:</span> {user.position}
              </div>
            </div>
          )}
          <div className="pt-2 flex gap-2">
            {isAuthenticated && (
              <Button 
                size="sm" 
                variant="outline" 
                className="text-xs h-7 bg-white"
                onClick={handleLogout}
              >
                Logout User
              </Button>
            )}
            {isRestaurantAuthenticated && (
              <Button 
                size="sm" 
                variant="outline" 
                className="text-xs h-7 bg-white"
                onClick={handleRestaurantLogout}
              >
                Logout Restaurant
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default AuthDebug;
