{"restaurants": [{"id": "1", "code": "GK001", "name": "The Gourmet Kitchen", "logo": "/logos/gourmet-kitchen.png", "address": "123 Main Street, Anytown, UK", "phone": "(*************", "email": "<EMAIL>", "vatRate": 20, "currency": "GBP", "isActive": true, "ownerName": "<PERSON>", "businessLicenseNumber": "BL-001", "restaurantType": "restaurant", "password": "gourmet123", "createdAt": "2023-01-01T00:00:00.000Z", "updatedAt": "2023-01-01T00:00:00.000Z"}, {"id": "2", "code": "PP002", "name": "Pasta Paradise", "logo": "/logos/pasta-paradise.png", "address": "456 Elm Avenue, Somewhere, UK", "phone": "(*************", "email": "<EMAIL>", "vatRate": 20, "currency": "GBP", "isActive": true, "ownerName": "<PERSON>", "businessLicenseNumber": "BL-002", "restaurantType": "restaurant", "password": "pasta123", "createdAt": "2023-01-01T00:00:00.000Z", "updatedAt": "2023-01-01T00:00:00.000Z"}, {"id": "3", "code": "SS003", "name": "Sushi Sensation", "logo": "/logos/sushi-sensation.png", "address": "789 Oak Boulevard, Elsewhere, UK", "phone": "(*************", "email": "<EMAIL>", "vatRate": 20, "currency": "GBP", "isActive": true, "ownerName": "<PERSON><PERSON>", "businessLicenseNumber": "BL-003", "restaurantType": "restaurant", "password": "sushi123", "createdAt": "2023-01-01T00:00:00.000Z", "updatedAt": "2023-01-01T00:00:00.000Z"}, {"name": "Test Cafe", "code": "TR511", "logo": "/logos/default-restaurant.png", "address": "123 Test Street, London, SW1A 1AA", "phone": "+44 20 1234 5678", "email": "<EMAIL>", "vatRate": 20.0, "currency": "GBP", "isActive": true, "ownerName": "<PERSON>", "businessLicenseNumber": "BL-TEST-001", "restaurantType": "cafe", "password": "testpass123", "setupData": {"useDefaultLogo": true, "logoPosition": "center", "logoUrl": null, "totalTables": 8, "tables": [{"id": "1", "name": "Table 1", "capacity": 4, "location": "Main Floor"}, {"id": "2", "name": "Table 2", "capacity": 2, "location": "Main Floor"}], "defaultTableCapacity": 4, "tableNamingSystem": "numbers", "operatingHours": [{"day": "monday", "isOpen": true, "openTime": "08:00", "closeTime": "18:00"}, {"day": "tuesday", "isOpen": true, "openTime": "08:00", "closeTime": "18:00"}, {"day": "wednesday", "isOpen": true, "openTime": "08:00", "closeTime": "18:00"}, {"day": "thursday", "isOpen": true, "openTime": "08:00", "closeTime": "18:00"}, {"day": "friday", "isOpen": true, "openTime": "08:00", "closeTime": "19:00"}, {"day": "saturday", "isOpen": true, "openTime": "09:00", "closeTime": "19:00"}, {"day": "sunday", "isOpen": true, "openTime": "09:00", "closeTime": "17:00"}], "cuisineTypes": ["coffee", "pastries"], "priceRange": {"min": 5, "max": 15, "currency": "GBP"}, "averageServiceTime": 30, "totalSeatingCapacity": 24, "estimatedDailyCovers": 60}, "createdAt": "2025-05-26T00:38:41.653198", "updatedAt": "2025-05-26T00:38:41.653198", "id": "restaurant4", "created_at": "2025-05-26T00:38:41.653198", "updated_at": "2025-05-26T00:38:41.653198"}, {"name": "Integration Test Restaurant **********", "code": "ITR372", "logo": "/logos/default-restaurant.png", "address": "123 Test Street, London, SW1A 1AA", "phone": "+44 20 1234 5678", "email": "<EMAIL>", "vatRate": 20.0, "currency": "GBP", "isActive": true, "ownerName": "Test Owner **********", "businessLicenseNumber": "BL-TEST-**********", "restaurantType": "restaurant", "password": "testpass123", "setupData": {"useDefaultLogo": true, "logoPosition": "center", "logoUrl": null, "totalTables": 6, "tables": [{"id": "1", "name": "Table 1", "capacity": 4, "location": "Main Floor"}, {"id": "2", "name": "Table 2", "capacity": 2, "location": "Main Floor"}], "defaultTableCapacity": 4, "tableNamingSystem": "numbers", "operatingHours": [{"day": "monday", "isOpen": true, "openTime": "09:00", "closeTime": "17:00"}, {"day": "tuesday", "isOpen": true, "openTime": "09:00", "closeTime": "17:00"}, {"day": "wednesday", "isOpen": true, "openTime": "09:00", "closeTime": "17:00"}, {"day": "thursday", "isOpen": true, "openTime": "09:00", "closeTime": "17:00"}, {"day": "friday", "isOpen": true, "openTime": "09:00", "closeTime": "18:00"}, {"day": "saturday", "isOpen": true, "openTime": "10:00", "closeTime": "18:00"}, {"day": "sunday", "isOpen": false, "openTime": "", "closeTime": ""}], "cuisineTypes": ["international", "modern"], "priceRange": {"min": 15, "max": 35, "currency": "GBP"}, "averageServiceTime": 45, "totalSeatingCapacity": 24, "estimatedDailyCovers": 80}, "createdAt": "2025-05-26T00:42:27.995011", "updatedAt": "2025-05-26T00:42:27.995011", "id": "restaurant5", "created_at": "2025-05-26T00:42:27.995011", "updated_at": "2025-05-26T00:42:27.995011"}, {"name": "Debug Test Restaurant", "code": "DTR735", "logo": "/logos/default-restaurant.png", "address": "123 Debug Street, London, SW1A 1AA", "phone": "+44 20 1234 5678", "email": "<EMAIL>", "vatRate": 20.0, "currency": "GBP", "isActive": true, "ownerName": "Debug Owner", "businessLicenseNumber": "BL-DEBUG-001", "restaurantType": "restaurant", "password": "testpass123", "setupData": {"useDefaultLogo": true, "logoPosition": "center", "logoUrl": null, "totalTables": 4, "tables": [{"id": "1", "name": "Table 1", "capacity": 4, "location": "Main Floor"}, {"id": "2", "name": "Table 2", "capacity": 2, "location": "Main Floor"}], "defaultTableCapacity": 4, "tableNamingSystem": "numbers", "operatingHours": [{"day": "monday", "isOpen": true, "openTime": "09:00", "closeTime": "17:00"}, {"day": "tuesday", "isOpen": true, "openTime": "09:00", "closeTime": "17:00"}, {"day": "wednesday", "isOpen": true, "openTime": "09:00", "closeTime": "17:00"}, {"day": "thursday", "isOpen": true, "openTime": "09:00", "closeTime": "17:00"}, {"day": "friday", "isOpen": true, "openTime": "09:00", "closeTime": "18:00"}, {"day": "saturday", "isOpen": true, "openTime": "10:00", "closeTime": "18:00"}, {"day": "sunday", "isOpen": false, "openTime": "", "closeTime": ""}], "cuisineTypes": ["international"], "priceRange": {"min": 15, "max": 35, "currency": "GBP"}, "averageServiceTime": 45, "totalSeatingCapacity": 6, "estimatedDailyCovers": 15}, "createdAt": "2025-05-26T01:17:14.710480", "updatedAt": "2025-05-26T01:17:14.711480", "id": "restaurant6", "created_at": "2025-05-26T01:17:14.711480", "updated_at": "2025-05-26T01:17:14.711480"}, {"name": "<PERSON><PERSON><PERSON>", "code": "PZ536", "logo": "blob:http://localhost:5173/4120003d-a574-4ebd-8fed-9354c10fa515", "address": "188 shaktinagar society near shaktinath temple, London, SW1A AA", "phone": "+91 **********", "email": "<EMAIL>", "vatRate": 20.0, "currency": "GBP", "isActive": true, "ownerName": "<PERSON><PERSON><PERSON>", "businessLicenseNumber": "8891", "restaurantType": "restaurant", "password": "12345678", "setupData": {"useDefaultLogo": false, "logoPosition": "center", "logoUrl": "blob:http://localhost:5173/4120003d-a574-4ebd-8fed-9354c10fa515", "totalTables": 10, "tables": [{"id": "table-1", "name": "Table 1", "capacity": 4, "location": "Main Floor"}, {"id": "table-2", "name": "Table 2", "capacity": 4, "location": "Main Floor"}, {"id": "table-3", "name": "Table 3", "capacity": 4, "location": "Main Floor"}, {"id": "table-4", "name": "Table 4", "capacity": 4, "location": "Main Floor"}, {"id": "table-5", "name": "Table 5", "capacity": 4, "location": "Main Floor"}, {"id": "table-6", "name": "Table 6", "capacity": 4, "location": "Main Floor"}, {"id": "table-7", "name": "Table 7", "capacity": 4, "location": "Main Floor"}, {"id": "table-8", "name": "Table 8", "capacity": 4, "location": "Main Floor"}, {"id": "table-9", "name": "Table 9", "capacity": 4, "location": "Main Floor"}, {"id": "table-10", "name": "Table 10", "capacity": 4, "location": "Main Floor"}], "defaultTableCapacity": 4, "tableNamingSystem": "numbers", "operatingHours": [{"day": "monday", "isOpen": true, "openTime": "09:00", "closeTime": "22:00"}, {"day": "tuesday", "isOpen": true, "openTime": "09:00", "closeTime": "22:00"}, {"day": "wednesday", "isOpen": true, "openTime": "09:00", "closeTime": "22:00"}, {"day": "thursday", "isOpen": true, "openTime": "09:00", "closeTime": "22:00"}, {"day": "friday", "isOpen": true, "openTime": "09:00", "closeTime": "23:00"}, {"day": "saturday", "isOpen": true, "openTime": "10:00", "closeTime": "23:00"}, {"day": "sunday", "isOpen": true, "openTime": "10:00", "closeTime": "21:00"}], "cuisineTypes": ["british"], "priceRange": {"min": 15, "max": 45, "currency": "GBP"}, "averageServiceTime": 60, "totalSeatingCapacity": 40, "estimatedDailyCovers": 100}, "createdAt": "2025-05-26T01:17:15.907399", "updatedAt": "2025-05-26T01:17:15.907399", "id": "restaurant7", "created_at": "2025-05-26T01:17:15.907399", "updated_at": "2025-05-26T01:17:15.907399"}, {"name": "OP", "code": "OL651", "logo": "blob:http://localhost:5173/947b5943-0aed-4212-91d0-2eb6dedb4446", "address": "188 shaktinagar society near shaktinath temple, London, SW1A AA", "phone": "+91 **********", "email": "<EMAIL>", "vatRate": 20.0, "currency": "GBP", "isActive": true, "ownerName": "OP", "businessLicenseNumber": "8891", "restaurantType": "restaurant", "password": "123456789", "setupData": {"useDefaultLogo": false, "logoPosition": "center", "logoUrl": "blob:http://localhost:5173/947b5943-0aed-4212-91d0-2eb6dedb4446", "totalTables": 10, "tables": [{"id": "table-1", "name": "Table 1", "capacity": 10, "location": "Main Floor"}, {"id": "table-2", "name": "Table 2", "capacity": 4, "location": "Main Floor"}, {"id": "table-3", "name": "Table 3", "capacity": 4, "location": "Main Floor"}, {"id": "table-4", "name": "Table 4", "capacity": 2, "location": "Main Floor"}, {"id": "table-5", "name": "Table 5", "capacity": 4, "location": "Main Floor"}, {"id": "table-6", "name": "Table 6", "capacity": 4, "location": "Main Floor"}, {"id": "table-7", "name": "Table 7", "capacity": 4, "location": "Main Floor"}, {"id": "table-8", "name": "Table 8", "capacity": 4, "location": "Main Floor"}, {"id": "table-9", "name": "Table 9", "capacity": 4, "location": "Main Floor"}, {"id": "table-10", "name": "Table 10", "capacity": 4, "location": "Main Floor"}], "defaultTableCapacity": 4, "tableNamingSystem": "numbers", "operatingHours": [{"day": "monday", "isOpen": true, "openTime": "09:00", "closeTime": "22:00"}, {"day": "tuesday", "isOpen": true, "openTime": "09:00", "closeTime": "22:00"}, {"day": "wednesday", "isOpen": true, "openTime": "09:00", "closeTime": "22:00"}, {"day": "thursday", "isOpen": true, "openTime": "09:00", "closeTime": "22:00"}, {"day": "friday", "isOpen": true, "openTime": "09:00", "closeTime": "23:00"}, {"day": "saturday", "isOpen": true, "openTime": "10:00", "closeTime": "23:00"}, {"day": "sunday", "isOpen": true, "openTime": "10:00", "closeTime": "21:00"}], "cuisineTypes": ["indian", "french", "seafood"], "priceRange": {"min": 5, "max": 25, "currency": "GBP"}, "averageServiceTime": 50, "totalSeatingCapacity": 44, "estimatedDailyCovers": 110}, "createdAt": "2025-05-26T15:30:48.469967", "updatedAt": "2025-05-26T15:30:48.469967", "id": "restaurant8", "created_at": "2025-05-26T15:30:48.469967", "updated_at": "2025-05-26T15:30:48.469967"}], "users": [{"id": "1", "name": "<PERSON>", "email": "<EMAIL>", "phone": "(*************", "restaurant_id": "1", "restaurant_name": "The Gourmet Kitchen", "role": "waiter", "position": "Head Waiter", "pin": "1234", "status": "active", "hireDate": "2022-03-15", "performance": 92, "accessLevel": "limited"}, {"id": "2", "name": "<PERSON>", "email": "<EMAIL>", "phone": "(*************", "restaurant_id": "1", "restaurant_name": "The Gourmet Kitchen", "role": "waiter", "position": "Waiter", "pin": "2345", "status": "active", "hireDate": "2022-05-20", "performance": 78, "accessLevel": "limited"}, {"id": "3", "name": "<PERSON>", "email": "<EMAIL>", "phone": "(*************", "restaurant_id": "1", "restaurant_name": "The Gourmet Kitchen", "role": "manager", "position": "Floor Manager", "pin": "5678", "status": "active", "hireDate": "2021-06-15", "performance": 95, "accessLevel": "full"}, {"id": "4", "name": "Admin User", "email": "<EMAIL>", "phone": "(*************", "restaurant_id": "0", "restaurant_name": "All Restaurants", "role": "admin", "position": "System Admin", "pin": "0000", "status": "active", "hireDate": "2020-01-01", "performance": 100, "accessLevel": "full"}, {"id": "5", "name": "<PERSON>", "email": "<EMAIL>", "phone": "(*************", "restaurant_id": "2", "restaurant_name": "Pasta Paradise", "role": "manager", "position": "Restaurant Manager", "pin": "1111", "status": "active", "hireDate": "2021-08-10", "performance": 90, "accessLevel": "full"}, {"id": "6", "name": "<PERSON>", "email": "<EMAIL>", "phone": "(*************", "restaurant_id": "2", "restaurant_name": "Pasta Paradise", "role": "waiter", "position": "Senior Waiter", "pin": "2222", "status": "active", "hireDate": "2022-01-15", "performance": 85, "accessLevel": "limited"}, {"id": "7", "name": "<PERSON>", "email": "<EMAIL>", "phone": "(*************", "restaurant_id": "3", "restaurant_name": "Sushi Sensation", "role": "manager", "position": "Floor Manager", "pin": "3333", "status": "active", "hireDate": "2021-05-20", "performance": 88, "accessLevel": "full"}, {"id": "8", "name": "<PERSON>", "email": "<EMAIL>", "phone": "(*************", "restaurant_id": "3", "restaurant_name": "Sushi Sensation", "role": "chef", "position": "<PERSON>shi Chef", "pin": "4444", "status": "active", "hireDate": "2021-09-15", "performance": 95, "accessLevel": "limited"}, {"name": "<PERSON>", "email": "<EMAIL>", "phone": "+44 20 1234 5678", "restaurant_id": "restaurant4", "restaurant_name": "Test Cafe", "role": "admin", "position": "Owner/Manager", "pin": "1234", "status": "active", "hireDate": "2025-05-26", "performance": 100, "accessLevel": "full", "id": "restaurant_user9", "created_at": "2025-05-26T00:38:41.654755", "updated_at": "2025-05-26T00:38:41.654755"}, {"name": "Test Owner **********", "email": "<EMAIL>", "phone": "+44 20 1234 5678", "restaurant_id": "restaurant5", "restaurant_name": "Integration Test Restaurant **********", "role": "admin", "position": "Owner/Manager", "pin": "1234", "status": "active", "hireDate": "2025-05-26", "performance": 100, "accessLevel": "full", "id": "restaurant_user10", "created_at": "2025-05-26T00:42:27.998779", "updated_at": "2025-05-26T00:42:27.998779"}, {"name": "Debug Owner", "email": "<EMAIL>", "phone": "+44 20 1234 5678", "restaurant_id": "restaurant6", "restaurant_name": "Debug Test Restaurant", "role": "admin", "position": "Owner/Manager", "pin": "1234", "status": "active", "hireDate": "2025-05-26", "performance": 100, "accessLevel": "full", "id": "restaurant_user11", "created_at": "2025-05-26T01:17:14.714515", "updated_at": "2025-05-26T01:17:14.714515"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "+91 **********", "restaurant_id": "restaurant7", "restaurant_name": "<PERSON><PERSON><PERSON>", "role": "admin", "position": "Owner/Manager", "pin": "7846", "status": "active", "hireDate": "2025-05-26", "performance": 100, "accessLevel": "full", "id": "restaurant_user12", "created_at": "2025-05-26T01:17:15.910037", "updated_at": "2025-05-26T01:17:15.910037"}, {"name": "OP", "email": "<EMAIL>", "phone": "+91 **********", "restaurant_id": "restaurant8", "restaurant_name": "OP", "role": "admin", "position": "Owner/Manager", "pin": "1334", "status": "active", "hireDate": "2025-05-26", "performance": 100, "accessLevel": "full", "id": "restaurant_user13", "created_at": "2025-05-26T15:30:48.474178", "updated_at": "2025-05-26T15:30:48.474178"}], "menu_items": [], "orders": [], "staff": [], "tables": [], "inventory": [], "promo_codes": [{"id": "promo_001", "code": "WELCOME10", "name": "Welcome Discount", "description": "10% off for new customers", "discount_type": "percentage", "discount_value": 10, "scope": "order_total", "minimum_spend": 20.0, "maximum_discount": 5.0, "usage_limit": 100, "usage_count": 15, "is_active": true, "start_date": "2024-01-01T00:00:00Z", "end_date": "2024-12-31T23:59:59Z", "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}, {"id": "promo_002", "code": "LUNCH15", "name": "Lunch Special", "description": "15% off lunch orders", "discount_type": "percentage", "discount_value": 15, "scope": "order_total", "minimum_spend": 15.0, "maximum_discount": 10.0, "usage_limit": 50, "usage_count": 8, "is_active": true, "start_date": "2024-01-01T00:00:00Z", "end_date": "2025-06-30T23:59:59Z", "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}, {"id": "promo_003", "code": "DINNER20", "name": "Dinner Delight", "description": "20% off dinner orders over Â£30", "discount_type": "percentage", "discount_value": 20, "scope": "order_total", "minimum_spend": 30.0, "maximum_discount": 15.0, "usage_limit": 75, "usage_count": 23, "is_active": true, "start_date": "2024-01-01T00:00:00Z", "end_date": "2025-12-31T23:59:59Z", "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}, {"id": "promo_004", "code": "STUDENT25", "name": "Student Discount", "description": "25% off for students with valid ID", "discount_type": "percentage", "discount_value": 25, "scope": "order_total", "minimum_spend": 10.0, "maximum_discount": 12.0, "usage_limit": 200, "usage_count": 67, "is_active": true, "start_date": "2024-01-01T00:00:00Z", "end_date": "2025-12-31T23:59:59Z", "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}, {"id": "promo_005", "code": "FAMILY5OFF", "name": "Family Deal", "description": "Â£5 off family orders over Â£50", "discount_type": "fixed_amount", "discount_value": 5.0, "scope": "order_total", "minimum_spend": 50.0, "maximum_discount": 5.0, "usage_limit": 30, "usage_count": 12, "is_active": true, "start_date": "2024-01-01T00:00:00Z", "end_date": "2025-12-31T23:59:59Z", "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}, {"id": "promo_006", "code": "WEEKEND30", "name": "Weekend Special", "description": "30% off weekend orders (Fri-Sun)", "discount_type": "percentage", "discount_value": 30, "scope": "order_total", "minimum_spend": 25.0, "maximum_discount": 20.0, "usage_limit": 40, "usage_count": 18, "is_active": true, "start_date": "2024-01-01T00:00:00Z", "end_date": "2025-12-31T23:59:59Z", "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}, {"code": "SAVE12", "name": "12% off", "description": null, "discount_type": "percentage", "discount_value": 12.0, "scope": "order_total", "minimum_spend": 21.0, "maximum_discount": 233.0, "applicable_items": null, "applicable_categories": null, "start_date": "2025-06-25T00:00:00+00:00", "end_date": "2025-07-09T00:00:00+00:00", "usage_limit": 1, "usage_limit_per_customer": null, "is_stackable": false, "is_active": true, "usage_count": 0, "id": "promo_code7", "created_at": "2025-06-08T02:04:51.692576", "updated_at": "2025-06-08T02:04:51.692576"}, {"code": "MANAGER10", "name": "Manager's Reference", "description": "Whoever knows manager gets this discount", "discount_type": "percentage", "discount_value": 10.0, "scope": "order_total", "minimum_spend": 0.0, "maximum_discount": null, "applicable_items": null, "applicable_categories": null, "start_date": "2025-06-08T00:00:00+00:00", "end_date": "2025-07-12T00:00:00+00:00", "usage_limit": 1, "usage_limit_per_customer": null, "is_stackable": false, "is_active": true, "usage_count": 0, "id": "promo_code8", "created_at": "2025-06-08T02:13:47.050523", "updated_at": "2025-06-08T02:13:47.050523"}], "promo_code_usage": [], "campaigns": [], "split_bills": [], "gift_cards": []}