import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/components/ui/sonner';
import { 
  Trash2, 
  AlertTriangle, 
  Building2, 
  Users, 
  RefreshCw,
  Shield,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import apiService from '@/services/apiService';
import logger from '@/utils/logger';

interface Restaurant {
  id: string;
  name: string;
  code: string;
  email: string;
  phone: string;
  address: string;
  isActive: boolean;
  ownerName: string;
  restaurantType: string;
  createdAt: string;
  hasData?: boolean;
}

const RestaurantManagement = () => {
  const { user, currentRestaurant } = useAuth();
  const [restaurants, setRestaurants] = useState<Restaurant[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [deleteDialog, setDeleteDialog] = useState<{
    isOpen: boolean;
    restaurant: Restaurant | null;
    confirmationText: string;
  }>({
    isOpen: false,
    restaurant: null,
    confirmationText: ''
  });

  useEffect(() => {
    loadRestaurants();
  }, []);

  const loadRestaurants = async () => {
    try {
      setIsLoading(true);
      logger.info('Loading restaurants for management', 'RestaurantManagement');
      
      const restaurantList = await apiService.restaurant.getRestaurants() as Restaurant[];
      setRestaurants(restaurantList);

      logger.info('Restaurants loaded successfully', 'RestaurantManagement', {
        count: Array.isArray(restaurantList) ? restaurantList.length : 0
      });
    } catch (error) {
      logger.error('Failed to load restaurants', 'RestaurantManagement', {
        error: error.message
      });
      toast.error('Failed to load restaurants');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteClick = (restaurant: Restaurant) => {
    logger.userAction('delete restaurant clicked', 'RestaurantManagement', {
      restaurantId: restaurant.id,
      restaurantName: restaurant.name
    });
    
    setDeleteDialog({
      isOpen: true,
      restaurant,
      confirmationText: ''
    });
  };

  const handleDeleteConfirm = async () => {
    const { restaurant, confirmationText } = deleteDialog;
    
    if (!restaurant) return;
    
    // Verify confirmation text matches restaurant name
    if (confirmationText !== restaurant.name) {
      toast.error('Restaurant name confirmation does not match');
      return;
    }

    try {
      logger.info('Attempting to delete restaurant', 'RestaurantManagement', {
        restaurantId: restaurant.id,
        restaurantName: restaurant.name
      });

      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/restaurants/${restaurant.id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Failed to delete restaurant');
      }

      const result = await response.json();
      
      logger.info('Restaurant deleted successfully', 'RestaurantManagement', {
        restaurantId: restaurant.id,
        restaurantName: restaurant.name
      });

      toast.success(result.message || 'Restaurant deactivated successfully');
      
      // Close dialog and refresh list
      setDeleteDialog({ isOpen: false, restaurant: null, confirmationText: '' });
      loadRestaurants();
      
    } catch (error) {
      logger.error('Failed to delete restaurant', 'RestaurantManagement', {
        restaurantId: restaurant.id,
        error: error.message
      });
      toast.error(error.message || 'Failed to delete restaurant');
    }
  };

  const handleRestoreRestaurant = async (restaurant: Restaurant) => {
    try {
      logger.info('Attempting to restore restaurant', 'RestaurantManagement', {
        restaurantId: restaurant.id,
        restaurantName: restaurant.name
      });

      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/restaurants/${restaurant.id}/restore`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Failed to restore restaurant');
      }

      const result = await response.json();
      
      logger.info('Restaurant restored successfully', 'RestaurantManagement', {
        restaurantId: restaurant.id,
        restaurantName: restaurant.name
      });

      toast.success(result.message || 'Restaurant restored successfully');
      loadRestaurants();
      
    } catch (error) {
      logger.error('Failed to restore restaurant', 'RestaurantManagement', {
        restaurantId: restaurant.id,
        error: error.message
      });
      toast.error(error.message || 'Failed to restore restaurant');
    }
  };

  // Check if current user is admin
  const isAdmin = user?.role?.toLowerCase() === 'admin';

  if (!isAdmin) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center gap-2 text-amber-600">
            <Shield className="h-5 w-5" />
            <span>Admin access required for restaurant management</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              Restaurant Management
            </CardTitle>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={loadRestaurants}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Alert className="mb-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>Warning:</strong> Deleting a restaurant will deactivate it and prevent access. 
              This action can be reversed by restoring the restaurant.
            </AlertDescription>
          </Alert>

          {isLoading ? (
            <div className="text-center py-8">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2 text-gray-400" />
              <p className="text-gray-500">Loading restaurants...</p>
            </div>
          ) : (
            <div className="space-y-4">
              {restaurants.map((restaurant) => (
                <div 
                  key={restaurant.id} 
                  className={`border rounded-lg p-4 ${
                    restaurant.id === currentRestaurant?.id ? 'border-blue-500 bg-blue-50' : ''
                  } ${!restaurant.isActive ? 'opacity-60 bg-gray-50' : ''}`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="font-semibold text-lg">{restaurant.name}</h3>
                        <Badge variant="outline" className="text-xs">
                          {restaurant.code}
                        </Badge>
                        {restaurant.id === currentRestaurant?.id && (
                          <Badge variant="default" className="text-xs bg-blue-600">
                            Current
                          </Badge>
                        )}
                        <div className="flex items-center gap-1">
                          {restaurant.isActive ? (
                            <CheckCircle className="h-4 w-4 text-green-600" />
                          ) : (
                            <XCircle className="h-4 w-4 text-red-600" />
                          )}
                          <span className={`text-xs ${restaurant.isActive ? 'text-green-600' : 'text-red-600'}`}>
                            {restaurant.isActive ? 'Active' : 'Inactive'}
                          </span>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-sm text-gray-600">
                        <div>Owner: {restaurant.ownerName}</div>
                        <div>Email: {restaurant.email}</div>
                        <div>Type: {restaurant.restaurantType}</div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      {restaurant.isActive ? (
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => handleDeleteClick(restaurant)}
                          disabled={restaurant.id === currentRestaurant?.id}
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Deactivate
                        </Button>
                      ) : (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleRestoreRestaurant(restaurant)}
                          className="text-green-600 border-green-600 hover:bg-green-50"
                        >
                          <CheckCircle className="h-4 w-4 mr-2" />
                          Restore
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
              
              {restaurants.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  No restaurants found
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialog.isOpen} onOpenChange={(open) => 
        setDeleteDialog(prev => ({ ...prev, isOpen: open }))
      }>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-red-600">
              <AlertTriangle className="h-5 w-5" />
              Confirm Restaurant Deactivation
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                This will deactivate <strong>{deleteDialog.restaurant?.name}</strong> and prevent all access. 
                Staff will not be able to log in and the restaurant will be hidden from active lists.
              </AlertDescription>
            </Alert>
            
            <div className="space-y-2">
              <Label htmlFor="confirmation">
                Type the restaurant name <strong>"{deleteDialog.restaurant?.name}"</strong> to confirm:
              </Label>
              <Input
                id="confirmation"
                value={deleteDialog.confirmationText}
                onChange={(e) => setDeleteDialog(prev => ({ 
                  ...prev, 
                  confirmationText: e.target.value 
                }))}
                placeholder="Enter restaurant name"
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setDeleteDialog({ isOpen: false, restaurant: null, confirmationText: '' })}
            >
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleDeleteConfirm}
              disabled={deleteDialog.confirmationText !== deleteDialog.restaurant?.name}
            >
              Deactivate Restaurant
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default RestaurantManagement;
