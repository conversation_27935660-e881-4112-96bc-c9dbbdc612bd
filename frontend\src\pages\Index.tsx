
import { useState } from "react";
import Layout from "@/components/layout/Layout";
import StatCard from "@/components/dashboard/StatCard";
import InventoryAlertCard from "@/components/dashboard/InventoryAlertCard";
import StaffPerformanceCard from "@/components/dashboard/StaffPerformanceCard";
import ForecastCard from "@/components/dashboard/ForecastCard";
import TableStatusCard from "@/components/dashboard/TableStatusCard";
import PopularItemsCard from "@/components/dashboard/PopularItemsCard";
import CustomerFeedbackCard from "@/components/dashboard/CustomerFeedbackCard";
import DateRangeSelector from "@/components/dashboard/DateRangeSelector";
import {
  BarChart3,
  Users,
  ShoppingBasket,
  Calendar
} from "lucide-react";
import { TableStatus } from "@/types";

const Index = () => {
  const [dateRange, setDateRange] = useState<{ from: Date; to: Date }>({
    from: new Date(new Date().setDate(new Date().getDate() - 7)),
    to: new Date(),
  });

  const handleDateRangeChange = (range: { from: Date; to: Date }) => {
    setDateRange(range);
    // In a real app, this would trigger API calls to refresh data based on the new date range
    console.log("Date range changed:", range);
  };

  // Mock data for dashboard
  const inventoryAlerts = [
    {
      id: "1",
      name: "Tomatoes",
      stock: 3,
      maxStock: 30,
      category: "Vegetables",
      isLow: true,
      isExpiring: false
    },
    {
      id: "2",
      name: "Chicken Breast",
      stock: 7,
      maxStock: 20,
      expiryDate: "May 2, 2025",
      category: "Meat",
      isLow: false,
      isExpiring: true
    },
    {
      id: "3",
      name: "Olive Oil",
      stock: 2,
      maxStock: 10,
      category: "Grocery",
      isLow: true,
      isExpiring: false
    }
  ];

  const staffPerformance = [
    {
      id: "1",
      name: "Michael Rodriguez",
      role: "Waiter",
      performance: 92,
      metrics: {
        sales: 1250,
        tablesTurned: 48,
        customerRating: 4.8
      }
    },
    {
      id: "2",
      name: "Jennifer Smith",
      role: "Waiter",
      performance: 78,
      metrics: {
        sales: 980,
        tablesTurned: 38,
        customerRating: 4.5
      }
    },
    {
      id: "3",
      name: "David Chen",
      role: "Waiter",
      performance: 65,
      metrics: {
        sales: 750,
        tablesTurned: 30,
        customerRating: 3.9
      }
    }
  ];

  const forecastData = [
    { day: "Mon", actualRevenue: 2400, projectedRevenue: 2200, customers: 120 },
    { day: "Tue", actualRevenue: 1800, projectedRevenue: 2000, customers: 90 },
    { day: "Wed", actualRevenue: 3000, projectedRevenue: 2800, customers: 130 },
    { day: "Thu", actualRevenue: 2800, projectedRevenue: 3000, customers: 125 },
    { day: "Fri", actualRevenue: 0, projectedRevenue: 3800, customers: 180 },
    { day: "Sat", actualRevenue: 0, projectedRevenue: 4500, customers: 220 },
    { day: "Sun", actualRevenue: 0, projectedRevenue: 3200, customers: 150 }
  ];

  const tableStatusData: TableStatus[] = [
    { id: "1", number: "Table 1", capacity: 2, status: "occupied" as const, occupiedSince: "12:30 PM", waitTime: "45 min" },
    { id: "2", number: "Table 2", capacity: 4, status: "available" as const },
    { id: "3", number: "Table 3", capacity: 4, status: "reserved" as const, reservation: { time: "7:00 PM", name: "Johnson", guests: 3 } },
    { id: "4", number: "Table 4", capacity: 6, status: "occupied" as const, occupiedSince: "1:15 PM", waitTime: "30 min" },
    { id: "5", number: "Table 5", capacity: 8, status: "cleaning" as const },
    { id: "6", number: "Table 6", capacity: 2, status: "available" as const },
    { id: "7", number: "Table 7", capacity: 2, status: "occupied" as const, occupiedSince: "2:00 PM", waitTime: "15 min" },
    { id: "8", number: "Table 8", capacity: 4, status: "reserved" as const, reservation: { time: "8:30 PM", name: "Smith", guests: 4 } }
  ];

  const popularMenuItems = [
    { id: "1", name: "Grilled Salmon", category: "Main Course", price: 24.99, orderCount: 78, percentageOfSales: 15, trend: 5 },
    { id: "2", name: "Margherita Pizza", category: "Pizza", price: 18.50, orderCount: 65, percentageOfSales: 12, trend: 8 },
    { id: "3", name: "Chocolate Lava Cake", category: "Dessert", price: 9.99, orderCount: 52, percentageOfSales: 10, trend: -3 },
    { id: "4", name: "Caesar Salad", category: "Appetizer", price: 12.50, orderCount: 45, percentageOfSales: 8, trend: 2 },
    { id: "5", name: "Beef Burger", category: "Main Course", price: 16.99, orderCount: 42, percentageOfSales: 7, trend: -1 }
  ];

  const customerReviews = [
    { id: "1", customerName: "John D.", rating: 5, comment: "Excellent food and service! Will definitely come back.", date: "2023-05-15" },
    { id: "2", customerName: "Sarah M.", rating: 4, comment: "Great atmosphere and delicious food. Service was a bit slow.", date: "2023-05-12" },
    { id: "3", customerName: "Robert K.", rating: 5, comment: "Best Italian food in town. The pasta was amazing!", date: "2023-05-10" },
    { id: "4", customerName: "Emily L.", rating: 3, comment: "Food was good but portions were small for the price.", date: "2023-05-08" },
    { id: "5", customerName: "Michael P.", rating: 4, comment: "Friendly staff and nice ambiance. Will recommend to friends.", date: "2023-05-05" }
  ];

  return (
    <Layout title="Dashboard">
      <div className="space-y-6">
        <div className="flex justify-end mb-4">
          <DateRangeSelector onRangeChange={handleDateRangeChange} />
        </div>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
          <StatCard
            title="Today's Revenue"
            value="£3,245.80"
            icon={<BarChart3 className="h-4 w-4" />}
            trend={{ value: 12, isPositive: true }}
          />
          <StatCard
            title="Customers Today"
            value="142"
            icon={<Users className="h-4 w-4" />}
            trend={{ value: 8, isPositive: true }}
          />
          <StatCard
            title="Inventory Items"
            value="245"
            icon={<ShoppingBasket className="h-4 w-4" />}
            trend={{ value: 3, isPositive: false }}
          />
          <StatCard
            title="Staff on Duty"
            value="8"
            icon={<Calendar className="h-4 w-4" />}
          />
        </div>

        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          <ForecastCard
            data={forecastData}
            title="Weekly Revenue Forecast"
          />
          <div className="grid grid-cols-1 gap-6">
            <StaffPerformanceCard staff={staffPerformance} />
            <InventoryAlertCard items={inventoryAlerts} />
          </div>
        </div>

        <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
          <TableStatusCard tables={tableStatusData} />
          <PopularItemsCard items={popularMenuItems} />
          <CustomerFeedbackCard reviews={customerReviews} />
        </div>
      </div>
    </Layout>
  );
};

export default Index;
