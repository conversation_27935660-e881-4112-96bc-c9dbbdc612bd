{"timestamp": "2025-06-19T18:45:44.214365", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-19T18:45:44.217378", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-19T18:46:53.710116", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-19T18:46:53.716122", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-19T18:48:03.500631", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-19T18:48:03.500631", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-19T18:48:09.554663", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-19T18:48:09.557671", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-19T18:48:09.562663", "level": "WARNING", "logger": "app.utils.auth", "message": "No restaurant ID provided, using default for development", "module": "auth", "function": "get_current_restaurant_id", "line": 178}
{"timestamp": "2025-06-19T18:48:10.419320", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-19T18:48:10.425647", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-19T18:48:29.977900", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-19T18:48:29.977900", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-19T18:48:36.353521", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-19T18:48:36.356938", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-19T18:48:37.832802", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-19T18:48:37.834799", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-19T18:48:53.050513", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-19T18:48:53.053517", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-19T18:50:06.197362", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-19T18:50:06.197362", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-19T18:50:12.268016", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-19T18:50:12.271076", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-19T18:50:22.575702", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-19T18:50:22.577708", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-19T18:51:38.711633", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-19T18:51:38.715633", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-20T05:37:55.789914", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-20T05:37:55.792911", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-20T05:47:39.071508", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-20T05:47:39.073505", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-20T08:36:21.963063", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-20T08:36:21.964064", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-20T08:36:30.189083", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-20T08:36:30.192094", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-20T08:39:42.747219", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-20T08:39:42.747219", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-20T08:39:48.562565", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-20T08:39:48.564565", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-20T08:40:24.570408", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-20T08:40:24.572412", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-20T10:23:31.961429", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-20T10:23:31.962430", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-20T10:23:50.889615", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-20T10:23:50.892632", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-20T10:23:51.132717", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-20T10:23:51.134717", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-20T10:23:51.580790", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-20T10:23:51.582798", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-20T10:23:51.603712", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-20T10:23:51.605712", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-20T11:59:52.794750", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-20T11:59:52.797529", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-20T11:59:52.906440", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-20T11:59:52.907432", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-20T11:59:53.097615", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-20T11:59:53.098615", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-20T11:59:53.107731", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-20T11:59:53.108732", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-20T11:59:53.459475", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-20T11:59:53.460476", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-20T11:59:53.514545", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-20T11:59:53.515537", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-20T12:00:06.231639", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-20T12:00:06.233648", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-20T12:07:52.084305", "level": "WARNING", "logger": "app.utils.auth", "message": "No restaurant ID provided, using default for development", "module": "auth", "function": "get_current_restaurant_id", "line": 178}
{"timestamp": "2025-06-20T12:07:52.094294", "level": "WARNING", "logger": "app.utils.auth", "message": "No restaurant ID provided, using default for development", "module": "auth", "function": "get_current_restaurant_id", "line": 178}
{"timestamp": "2025-06-20T12:07:52.102853", "level": "WARNING", "logger": "app.utils.auth", "message": "No restaurant ID provided, using default for development", "module": "auth", "function": "get_current_restaurant_id", "line": 178}
{"timestamp": "2025-06-20T12:07:52.118421", "level": "WARNING", "logger": "app.utils.auth", "message": "No restaurant ID provided, using default for development", "module": "auth", "function": "get_current_restaurant_id", "line": 178}
{"timestamp": "2025-06-20T12:07:52.136008", "level": "WARNING", "logger": "app.utils.auth", "message": "No restaurant ID provided, using default for development", "module": "auth", "function": "get_current_restaurant_id", "line": 178}
{"timestamp": "2025-06-20T12:07:52.430137", "level": "WARNING", "logger": "app.utils.auth", "message": "No restaurant ID provided, using default for development", "module": "auth", "function": "get_current_restaurant_id", "line": 178}
{"timestamp": "2025-06-20T12:07:52.432130", "level": "WARNING", "logger": "app.utils.auth", "message": "No restaurant ID provided, using default for development", "module": "auth", "function": "get_current_restaurant_id", "line": 178}
{"timestamp": "2025-06-20T12:07:52.435402", "level": "WARNING", "logger": "app.utils.auth", "message": "No restaurant ID provided, using default for development", "module": "auth", "function": "get_current_restaurant_id", "line": 178}
{"timestamp": "2025-06-20T12:07:52.439424", "level": "WARNING", "logger": "app.utils.auth", "message": "No restaurant ID provided, using default for development", "module": "auth", "function": "get_current_restaurant_id", "line": 178}
{"timestamp": "2025-06-20T12:07:52.441427", "level": "WARNING", "logger": "app.utils.auth", "message": "No restaurant ID provided, using default for development", "module": "auth", "function": "get_current_restaurant_id", "line": 178}
{"timestamp": "2025-06-20T12:11:25.188878", "level": "WARNING", "logger": "app.utils.auth", "message": "No restaurant ID provided, using default for development", "module": "auth", "function": "get_current_restaurant_id", "line": 178}
{"timestamp": "2025-06-20T12:11:25.703124", "level": "WARNING", "logger": "app.utils.auth", "message": "No restaurant ID provided, using default for development", "module": "auth", "function": "get_current_restaurant_id", "line": 178}
{"timestamp": "2025-06-20T12:11:26.220559", "level": "WARNING", "logger": "app.utils.auth", "message": "No restaurant ID provided, using default for development", "module": "auth", "function": "get_current_restaurant_id", "line": 178}
{"timestamp": "2025-06-20T12:12:50.401494", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-20T12:12:50.404521", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-20T12:12:50.458629", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-20T12:12:50.460644", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-20T12:12:50.805530", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-20T12:12:50.806592", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-20T12:12:50.813554", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-20T12:12:50.814556", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-20T12:13:59.773483", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-20T12:13:59.775484", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-20T12:13:59.779484", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-20T12:13:59.780684", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-20T12:14:09.299238", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-20T12:14:09.303436", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-20T12:14:09.358432", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-20T12:14:09.360433", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-20T12:14:35.968995", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-20T12:14:35.968995", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-20T12:14:36.030008", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-20T12:14:36.030008", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-20T12:14:42.428178", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-20T12:14:42.432180", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-20T12:14:42.583455", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-20T12:14:42.586457", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-20T12:14:43.577316", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-20T12:14:43.577316", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-20T12:14:43.640236", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-20T12:14:43.641225", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-20T12:14:49.656986", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-20T12:14:49.660000", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-20T12:14:49.903534", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-20T12:14:49.905533", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-20T12:14:54.146004", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-20T12:14:54.146004", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-20T12:14:54.178539", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-20T12:14:54.178539", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-20T12:15:00.245264", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-20T12:15:00.247265", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-20T12:15:00.480324", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-20T12:15:00.482330", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-20T12:15:05.979101", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-20T12:15:05.979101", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-20T12:15:06.073641", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-20T12:15:06.073641", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-20T12:15:12.432889", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-20T12:15:12.436890", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-20T12:15:12.597996", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-20T12:15:12.600996", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-20T12:15:21.990626", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-20T12:15:21.993627", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-20T12:15:22.037378", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-20T12:15:22.042404", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-20T12:15:22.306894", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-20T12:15:22.312938", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-20T12:15:22.333173", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-20T12:15:22.338184", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-20T12:15:27.524759", "level": "WARNING", "logger": "app.utils.auth", "message": "No restaurant ID provided, using default for development", "module": "auth", "function": "get_current_restaurant_id", "line": 178}
{"timestamp": "2025-06-20T12:15:28.040813", "level": "WARNING", "logger": "app.utils.auth", "message": "No restaurant ID provided, using default for development", "module": "auth", "function": "get_current_restaurant_id", "line": 178}
{"timestamp": "2025-06-20T12:15:28.556399", "level": "WARNING", "logger": "app.utils.auth", "message": "No restaurant ID provided, using default for development", "module": "auth", "function": "get_current_restaurant_id", "line": 178}
{"timestamp": "2025-06-20T13:41:02.230407", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-20T13:41:02.232419", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-20T13:41:02.245943", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-20T13:41:02.249495", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-20T13:46:49.736405", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-20T13:46:49.736405", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-20T13:46:49.793430", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-20T13:46:49.793430", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-20T13:47:12.716917", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-20T13:47:12.721213", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-20T13:47:12.768033", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-20T13:47:12.770032", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-20T13:47:28.154661", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-20T13:47:28.156183", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-20T13:47:28.202209", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-20T13:47:28.202209", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-20T13:47:35.132277", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-20T13:47:35.136278", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-20T13:47:35.283431", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-20T13:47:35.287958", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-20T13:47:41.953788", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-20T13:47:41.953788", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-20T13:47:41.986328", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-20T13:47:41.986328", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-20T13:47:48.871962", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-20T13:47:48.874977", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-20T13:47:49.040778", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-20T13:47:49.043779", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-20T13:47:51.613895", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-20T13:47:51.614898", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-20T13:47:51.677927", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-20T13:47:51.677927", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-20T13:47:58.614388", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-20T13:47:58.617381", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-20T13:47:58.756507", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-20T13:47:58.760511", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-20T13:49:01.866566", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-20T13:49:01.866566", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-20T13:49:01.897094", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-20T13:49:01.898098", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-20T13:49:09.123850", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-20T13:49:09.127854", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-20T13:49:09.381907", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-20T13:49:09.388289", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-20T13:49:53.842937", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-20T13:49:53.843935", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-20T13:49:53.871929", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-20T13:49:53.872935", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-20T13:50:01.269042", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-20T13:50:01.273040", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-20T13:50:01.385981", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-20T13:50:01.391002", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-20T13:50:20.847195", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-20T13:50:20.847195", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-20T13:50:20.864223", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-20T13:50:20.865220", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-20T13:50:28.281897", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-20T13:50:28.283892", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-20T13:50:28.407467", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-20T13:50:28.410458", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-20T13:50:43.400756", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-20T13:50:43.400756", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-20T13:50:43.415760", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-20T13:50:43.416764", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-20T13:50:50.542896", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-20T13:50:50.546900", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-20T13:50:50.692922", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-20T13:50:50.695927", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-20T13:56:15.675255", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-20T13:56:15.683912", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-20T13:56:15.932905", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-20T13:56:15.936235", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-20T13:56:16.029656", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-20T13:56:16.031655", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-20T13:56:16.051203", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-20T13:56:16.055791", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-20T13:56:20.938633", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-20T13:56:20.941150", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-20T13:56:20.994680", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-20T13:56:20.999248", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-20T13:57:15.266758", "level": "WARNING", "logger": "app.utils.auth", "message": "No restaurant ID provided, using default for development", "module": "auth", "function": "get_current_restaurant_id", "line": 178}
{"timestamp": "2025-06-20T13:57:15.269758", "level": "WARNING", "logger": "app.utils.auth", "message": "No restaurant ID provided, using default for development", "module": "auth", "function": "get_current_restaurant_id", "line": 178}
{"timestamp": "2025-06-20T13:57:17.891053", "level": "WARNING", "logger": "app.utils.auth", "message": "No restaurant ID provided, using default for development", "module": "auth", "function": "get_current_restaurant_id", "line": 178}
{"timestamp": "2025-06-20T15:06:41.644798", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-20T15:06:41.647786", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-20T15:06:45.297247", "level": "WARNING", "logger": "app.utils.auth", "message": "No restaurant ID provided, using default for development", "module": "auth", "function": "get_current_restaurant_id", "line": 178}
{"timestamp": "2025-06-20T15:07:01.765542", "level": "WARNING", "logger": "app.utils.auth", "message": "No restaurant ID provided, using default for development", "module": "auth", "function": "get_current_restaurant_id", "line": 178}
{"timestamp": "2025-06-20T15:46:42.020295", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-20T15:46:42.025819", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-20T15:46:45.177961", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-20T15:46:45.185161", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-20T15:46:45.615210", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-20T15:46:45.629335", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-20T15:46:45.647492", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-20T15:46:45.657113", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-20T15:46:45.688754", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-20T15:46:45.714430", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-20T15:46:47.478163", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-20T15:46:47.482695", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-20T15:46:47.754917", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-20T15:46:47.757451", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-20T17:19:18.598655", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-20T17:19:18.599638", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T06:13:14.393960", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T06:13:14.397471", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T06:37:14.931638", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T06:37:14.936167", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T06:40:06.525249", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T06:40:06.526239", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T06:40:15.444752", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T06:40:15.445759", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T06:40:24.065528", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T06:40:24.083738", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T08:52:13.883866", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T08:52:13.893417", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T08:52:14.274240", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T08:52:14.276244", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T08:52:14.295515", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T08:52:14.301059", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T08:52:14.486416", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T08:52:14.492411", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T08:52:16.145626", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T08:52:16.148152", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T08:52:16.761372", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T08:52:16.764912", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T08:52:28.753587", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T08:52:28.754588", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T08:52:28.969932", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T08:52:28.971937", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T08:52:28.995943", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T08:52:28.997943", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T08:52:29.022489", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T08:52:29.029022", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T08:53:26.487250", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T08:53:26.492251", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T08:53:26.523332", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T08:53:26.527336", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T08:53:26.589055", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T08:53:26.592055", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T08:53:26.604378", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T08:53:26.606380", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T08:53:38.290305", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T08:53:38.294292", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T08:53:38.343303", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T08:53:38.345309", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T08:53:38.709160", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T08:53:38.710158", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T08:53:38.717683", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T08:53:38.718685", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T08:53:40.143348", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T08:53:40.145347", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T08:53:40.166349", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T08:53:40.169352", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T08:55:07.515630", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T08:55:07.519624", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T08:55:07.561486", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T08:55:07.564497", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T08:55:07.933354", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T08:55:07.935350", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T08:55:07.941876", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T08:55:07.942864", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T08:55:38.939494", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T08:55:38.939494", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T08:55:57.819268", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T08:55:57.822268", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T08:56:01.444838", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T08:56:01.445837", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T08:56:13.176401", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T08:56:13.180407", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T08:56:39.786842", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T08:56:39.790830", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T08:57:47.961023", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T08:57:47.963027", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T08:57:48.010091", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T08:57:48.013617", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T08:57:48.307024", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T08:57:48.308015", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T08:57:48.316546", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T08:57:48.318541", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T08:58:28.750147", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T08:58:28.750147", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T08:58:28.765150", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T08:58:28.766156", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T08:58:44.368629", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T08:58:44.373171", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T08:58:44.489320", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T08:58:44.491820", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T08:58:49.725554", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T08:58:49.725554", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T08:58:49.726548", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T08:58:58.075172", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T08:58:58.077168", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T08:58:58.144685", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T08:58:58.149872", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T08:59:01.140334", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T08:59:01.140334", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T08:59:01.204255", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T08:59:01.204255", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T08:59:08.597411", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T08:59:08.600744", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T08:59:08.733300", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T08:59:08.736308", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T09:29:01.836259", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T09:29:01.836259", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T09:29:01.897266", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T09:29:01.897266", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T09:29:12.475360", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T09:29:12.479108", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T09:29:12.483112", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T09:29:12.487107", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T09:29:13.136110", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T09:29:13.136110", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T09:29:13.137119", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T09:29:13.137119", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T09:29:22.079870", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T09:29:22.083873", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T09:29:42.423592", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T09:29:42.426587", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T09:30:24.344335", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T09:30:24.345338", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T09:30:24.376333", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T09:30:24.376333", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T09:30:30.852400", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T09:30:30.855399", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T09:30:31.005153", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T09:30:31.010174", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T09:30:53.899839", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T09:30:53.902840", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T09:30:53.948454", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T09:30:53.952454", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T09:30:54.246294", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T09:30:54.250289", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T09:30:54.274840", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T09:30:54.278847", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T09:31:00.593488", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T09:31:00.593488", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T09:31:00.628491", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T09:31:00.630496", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T09:31:11.668172", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T09:31:11.671172", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T09:31:11.691180", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T09:31:11.694176", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T09:32:14.766470", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T09:32:14.766470", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T09:32:14.783474", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T09:32:14.783474", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T09:32:22.232196", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T09:32:22.237384", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T09:32:22.373673", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T09:32:22.375673", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T09:32:57.777733", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T09:32:57.781148", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T09:33:43.642191", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T09:33:43.645190", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T09:33:43.648189", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T09:33:43.648189", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T09:34:33.288124", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T09:34:33.290122", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T09:35:03.406963", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T09:35:03.407964", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T09:35:03.421969", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T09:35:03.422967", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T09:35:11.527388", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T09:35:11.532386", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T09:35:11.560438", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T09:35:11.562950", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T09:35:17.206205", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T09:35:17.207197", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T09:35:17.239321", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T09:35:17.240319", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T09:35:26.161229", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T09:35:26.169249", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T09:35:26.187314", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T09:35:26.191481", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T10:16:51.110312", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T10:16:51.114311", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T10:16:51.190876", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T10:16:51.193403", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T10:16:51.389910", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T10:16:51.391896", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T10:16:51.411424", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T10:16:51.412939", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T15:50:44.748688", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T15:50:44.750685", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T15:53:42.000116", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T15:53:42.000116", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T15:53:42.014119", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T15:53:42.014119", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T15:53:49.392350", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T15:53:49.394349", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T15:53:52.042548", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T15:53:52.043528", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T15:54:00.122620", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T15:54:00.125618", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T15:54:00.256385", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T15:54:00.259393", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T15:54:48.011750", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T15:54:48.013743", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T15:55:31.691984", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T15:55:31.692985", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T15:55:31.739986", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T15:55:31.739986", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T15:55:38.939052", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T15:55:38.943063", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T15:55:40.956115", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T15:55:40.959446", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T15:55:41.182699", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T15:55:41.182699", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T15:55:41.244709", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T15:55:41.245701", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T15:55:47.954022", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T15:55:47.957027", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T15:55:48.130658", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T15:55:48.134650", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T15:55:51.671871", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T15:55:51.672913", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T15:55:51.737920", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T15:55:51.738927", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T15:55:58.173886", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T15:55:58.177891", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T15:55:58.463108", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T15:55:58.472136", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T15:56:00.866082", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T15:56:00.867189", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T15:56:00.898652", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T15:56:00.898652", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T15:56:08.601155", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T15:56:08.607162", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T15:56:08.640511", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T15:56:08.645526", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T16:24:09.600892", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T16:24:09.602406", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T16:24:15.433306", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T16:24:15.434312", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T16:24:34.227609", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T16:24:34.236715", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T16:24:47.350680", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:24:47.354216", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:24:47.758403", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:24:47.760928", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:24:47.783361", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:24:47.785357", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:24:47.818620", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:24:47.821162", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:24:50.714492", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:24:50.718489", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:24:50.743374", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:24:50.747557", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:25:40.541530", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:25:40.543547", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:25:40.585337", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:25:40.589537", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:25:40.749441", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:25:40.755065", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:25:40.827523", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:25:40.837261", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:25:41.061670", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:25:41.063691", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:25:41.117671", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:25:41.120726", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:25:50.330524", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:25:50.331520", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:30:05.727767", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:30:05.730302", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:30:05.879069", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:30:05.882074", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:34:58.447697", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T16:34:58.447697", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T16:35:16.224715", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T16:35:16.229245", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T16:35:28.357263", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:35:28.362259", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:35:28.412287", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:35:28.415306", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:35:28.700699", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:35:28.701708", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:35:28.733398", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:35:28.735400", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:35:31.276417", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:35:31.280431", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:35:31.374394", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:35:31.378938", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:37:11.089412", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T16:37:11.089412", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T16:37:18.383906", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T16:37:18.389449", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T16:38:25.456783", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T16:38:25.457778", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T16:38:34.970096", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T16:38:34.972103", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T16:39:34.819907", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:39:34.824461", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:39:35.215223", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:39:35.218258", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:40:08.814815", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T16:40:08.819373", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T16:44:52.852490", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:44:52.857151", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:44:52.915226", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:44:52.918709", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:44:53.296369", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:44:53.298377", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:44:53.351277", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:44:53.355840", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:44:55.264849", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:44:55.265845", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:44:55.289138", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:44:55.292127", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:48:32.043555", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:48:32.050747", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:48:32.104296", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:48:32.107834", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:48:32.443577", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:48:32.445816", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:48:32.519597", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:48:32.523457", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:51:20.822090", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T16:51:20.822090", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T16:51:20.914525", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T16:51:20.914525", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T16:51:35.434468", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T16:51:35.440016", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T16:51:35.520935", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T16:51:35.524448", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T16:51:55.151861", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T16:51:55.152852", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T16:51:55.233554", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T16:51:55.233554", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T16:52:03.309735", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T16:52:03.313736", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T16:52:03.324243", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T16:52:03.326259", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T16:56:18.290029", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:56:18.293031", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:56:18.631931", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:56:18.634930", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:56:18.665464", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:56:18.667462", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:56:18.698435", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:56:18.701437", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:56:22.727041", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:56:22.731037", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:56:22.830311", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:56:22.834307", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:56:22.929539", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:56:22.939539", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:56:23.075230", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:56:23.079231", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:56:25.241530", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:56:25.243532", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:56:25.274540", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:56:25.278112", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:57:35.986944", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:57:35.990938", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:57:36.045450", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:57:36.051450", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:57:36.439864", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:57:36.442898", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:57:36.502004", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:57:36.506001", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:58:07.031613", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:58:07.036604", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:58:07.077389", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:58:07.083455", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:58:07.551720", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:58:07.556649", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:58:07.583144", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:58:07.584778", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:58:32.372308", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:58:32.375328", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:58:32.389936", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:58:32.392937", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:58:32.448775", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:58:32.451244", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:58:32.489794", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:58:32.491489", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:58:32.934071", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:58:32.936414", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T16:58:32.955133", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-21T16:58:32.958205", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-21T17:03:27.673530", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T17:03:27.674518", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T17:03:38.106890", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T17:03:38.109889", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T17:04:02.967966", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T17:04:02.968958", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T17:04:09.058060", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T17:04:09.060058", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T17:04:28.810263", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T17:04:28.810263", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T17:04:35.337829", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T17:04:35.339836", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T17:05:10.810010", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T17:05:10.811005", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-21T17:05:17.024405", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-21T17:05:17.026411", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-21T17:05:51.771044", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-21T17:05:51.772037", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-23T10:23:14.667347", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-23T10:23:14.671347", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-23T14:16:49.649659", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-23T14:16:49.655668", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-23T14:18:58.946101", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-23T14:18:58.948114", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-23T14:20:17.169515", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-23T14:20:17.171042", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-23T14:20:17.877419", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-23T14:20:17.880144", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-23T14:20:30.737084", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-23T14:20:30.739094", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-23T14:20:30.779656", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-23T14:20:30.782669", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-23T14:20:30.839799", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-23T14:20:30.840807", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-23T14:20:30.875527", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-23T14:20:30.876709", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-23T14:20:43.461171", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-23T14:20:43.462171", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-23T14:21:07.477277", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-23T14:21:07.479793", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-23T14:21:07.841967", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-23T14:21:07.843972", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-23T14:21:16.275426", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-23T14:21:16.279530", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-23T14:21:16.347044", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-23T14:21:16.350566", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-23T14:21:16.410269", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-23T14:21:16.414289", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-23T14:21:16.456533", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-23T14:21:16.457533", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-23T14:21:18.864474", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-23T14:21:18.866839", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-23T14:21:18.896342", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-23T14:21:18.898843", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-23T14:21:40.482492", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-23T14:21:40.483492", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-23T14:22:45.967712", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-23T14:22:45.971626", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-23T14:22:46.330194", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-23T14:22:46.332201", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-23T14:22:46.405409", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-23T14:22:46.408395", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-23T14:22:46.436923", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-23T14:22:46.439922", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-23T14:22:46.458652", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-23T14:22:46.461241", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-23T14:22:46.519837", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-23T14:22:46.522874", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-23T14:22:46.631812", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-23T14:22:46.637332", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-23T14:22:46.639339", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-23T14:22:46.645334", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-23T14:23:43.408154", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-23T14:23:43.411139", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-23T14:23:43.460305", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-23T14:23:43.465301", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-23T14:23:43.511516", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-23T14:23:43.513518", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-23T14:23:43.581403", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-23T14:23:43.584421", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-23T14:23:45.693653", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-23T14:23:45.695666", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-23T14:23:45.722848", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-23T14:23:45.724846", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T11:47:40.487309", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-24T11:47:40.491811", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-24T11:48:03.120044", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-24T11:48:03.120044", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-24T11:48:30.330112", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-24T11:48:30.333113", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-24T11:49:11.895319", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-24T11:49:11.896321", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-24T11:49:28.415250", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-24T11:49:28.418240", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-24T11:49:41.872701", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T11:49:41.873700", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T11:49:42.012130", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T11:49:42.014127", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T11:49:42.229539", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T11:49:42.231539", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T11:49:42.242554", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T11:49:42.244552", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T11:52:18.051625", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T11:52:18.054623", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T11:52:18.092639", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T11:52:18.096624", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T11:52:18.439815", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T11:52:18.441827", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T11:52:18.459910", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T11:52:18.460919", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T11:57:46.843291", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-24T11:57:46.845305", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-24T11:59:28.589847", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-24T11:59:28.593118", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-24T12:00:40.996120", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-24T12:00:40.997117", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-24T12:00:49.330196", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-24T12:00:49.333201", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-24T12:03:19.314602", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-24T12:03:19.318608", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-24T12:05:05.373070", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-24T12:05:05.373070", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-24T12:05:11.842352", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-24T12:05:11.844350", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-24T12:05:58.413601", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-24T12:05:58.416607", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-24T12:07:32.836418", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-24T12:07:32.839408", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-24T12:08:45.730310", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-24T12:08:45.731314", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-24T12:08:45.808837", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-24T12:08:45.808837", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-24T12:08:51.881340", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-24T12:08:51.883341", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-24T12:08:53.953721", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-24T12:08:53.955721", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-24T12:09:10.860056", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-24T12:09:10.863068", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-24T12:10:55.340294", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-24T12:10:55.342823", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-24T12:11:21.506262", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-24T12:11:21.510268", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-24T12:13:39.481304", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-24T12:13:39.481304", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-24T12:13:39.545329", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-24T12:13:39.545329", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-24T12:13:45.626153", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-24T12:13:45.628155", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-24T12:13:45.779576", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-24T12:13:45.785576", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-24T12:14:05.527451", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-24T12:14:05.530448", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-24T12:18:24.334164", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-24T12:18:24.337173", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-24T13:20:56.430294", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-24T13:20:56.433286", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-24T13:21:57.558142", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-24T13:21:57.558142", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-24T13:22:12.673912", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-24T13:22:12.675930", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-24T13:22:25.146051", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T13:22:25.147053", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T13:22:25.169074", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T13:22:25.173096", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T13:22:25.453089", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T13:22:25.454090", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T13:22:25.478224", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T13:22:25.479228", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T13:22:26.696335", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T13:22:26.698339", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T13:22:26.721368", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T13:22:26.724362", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:07:40.880846", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:07:40.884563", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:07:40.925464", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:07:40.928471", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:07:41.328709", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:07:41.331717", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:07:41.373425", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:07:41.375422", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:08:02.851086", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:08:02.853091", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:08:02.912635", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:08:02.913639", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:08:02.967162", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:08:02.969162", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:08:03.001690", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:08:03.002690", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:08:15.512059", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:08:15.515061", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:08:15.596006", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:08:15.599997", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:08:15.672082", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:08:15.674085", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:08:15.710135", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:08:15.710135", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:08:48.403583", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:08:48.404583", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:08:48.441622", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:08:48.444612", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:08:48.813737", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:08:48.814730", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:08:48.838741", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:08:48.841747", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:09:32.501948", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:09:32.504951", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:09:32.547014", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:09:32.550016", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:09:32.598367", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:09:32.600363", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:09:32.646429", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:09:32.647427", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:09:52.885838", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:09:52.889844", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:09:52.938786", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:09:52.943784", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:09:53.298734", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:09:53.300721", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:09:53.330728", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:09:53.333725", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:10:47.437653", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:10:47.440642", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:10:47.490189", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:10:47.493177", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:10:47.554267", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:10:47.556260", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:10:47.593090", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:10:47.595100", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:11:19.565530", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:11:19.568534", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:11:19.603533", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:11:19.605542", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:11:19.971124", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:11:19.972124", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:11:19.995653", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:11:19.996667", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:11:34.455117", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:11:34.458137", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:11:34.497191", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:11:34.499189", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:11:34.546720", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:11:34.547718", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:11:34.580750", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:11:34.581747", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:11:55.785652", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:11:55.789656", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:11:55.866723", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:11:55.869749", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:11:55.946699", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:11:55.947699", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:11:55.979708", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:11:55.981716", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:12:10.838002", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:12:10.841005", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:12:10.910569", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:12:10.915099", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:12:11.027774", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:12:11.028777", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:12:11.077054", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:12:11.080063", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:12:29.718422", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:12:29.721434", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:12:29.787565", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:12:29.791577", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:12:30.176354", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:12:30.177354", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:12:30.198356", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:12:30.200354", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:12:47.410457", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:12:47.412466", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:12:47.458568", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:12:47.461572", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:12:47.528114", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:12:47.531108", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:12:47.570771", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:12:47.572777", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:13:01.087266", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:13:01.089268", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:13:01.132842", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:13:01.135829", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:13:01.198432", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:13:01.202433", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:13:01.240963", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:13:01.242964", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:16:03.337966", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:16:03.341955", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:16:03.459661", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:16:03.466650", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:16:03.813038", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:16:03.814041", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:16:03.838050", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:16:03.838050", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:28:16.906579", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:28:16.910567", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:28:17.266276", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:28:17.267268", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:28:17.340395", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:28:17.341397", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:28:17.367411", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:28:17.369399", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:29:15.877247", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:29:15.880255", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:29:15.978059", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:29:15.979047", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:29:16.221545", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:29:16.224545", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:29:16.244552", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:29:16.247550", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:29:34.273427", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:29:34.277429", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:29:34.295432", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:29:34.297427", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:29:34.593528", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:29:34.595522", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:29:34.633467", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:29:34.636469", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:29:46.668703", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:29:46.671711", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:29:46.711928", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:29:46.715940", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:29:46.776973", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:29:46.778975", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:29:46.813510", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:29:46.815517", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:30:25.632849", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:30:25.634854", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:30:25.675388", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:30:25.677381", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:30:25.729923", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:30:25.730926", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:30:25.763446", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:30:25.764445", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:30:59.397667", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:30:59.402665", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:30:59.758969", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:30:59.760974", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:30:59.807036", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:30:59.811033", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:30:59.842137", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:30:59.844144", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:38:26.680281", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:38:26.682802", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:38:26.716226", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:38:26.720225", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:38:27.092970", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:38:27.093965", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:38:27.117506", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:38:27.118506", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:38:46.117647", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:38:46.119649", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:38:46.166380", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:38:46.168642", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:38:46.211542", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:38:46.213536", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:38:46.256428", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:38:46.259438", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:40:49.036971", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:40:49.038516", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:40:49.054546", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:40:49.055539", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:40:49.097586", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:40:49.100580", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:40:49.165021", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:40:49.166023", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:41:42.546938", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:41:42.548942", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:41:42.567324", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:41:42.569325", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:41:42.592849", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:41:42.599846", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-24T14:41:42.621858", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-24T14:41:42.623862", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T06:17:30.828107", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T06:17:30.831091", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T06:17:30.881633", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T06:17:30.882633", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T06:17:31.184233", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T06:17:31.192773", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T06:17:31.209784", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T06:17:31.211780", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T06:17:32.565708", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T06:17:32.568703", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T06:17:32.973699", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T06:17:32.975710", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T06:27:40.030822", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T06:27:40.032822", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T06:27:40.041825", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T06:27:40.043829", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T06:27:41.540507", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T06:27:41.543505", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T06:27:41.998937", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T06:27:41.999935", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T06:54:45.618393", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-25T06:54:45.619537", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-25T06:55:09.170589", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-25T06:55:09.174107", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-25T06:57:23.404410", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-25T06:57:23.406408", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-25T06:58:09.597852", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-25T06:58:09.600851", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-25T07:13:27.324915", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:13:27.325922", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T07:13:27.579759", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:13:27.582757", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T07:13:27.789803", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:13:27.791811", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T07:13:27.844836", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:13:27.847848", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T07:13:30.947801", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:13:30.949800", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T07:13:30.964867", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:13:30.967857", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T07:18:21.115594", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:18:21.119726", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T07:18:21.181304", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:18:21.185301", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T07:18:21.615326", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:18:21.617322", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T07:18:21.651732", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:18:21.653731", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T07:18:24.007729", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:18:24.009920", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T07:18:24.032582", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:18:24.036978", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T07:38:39.580589", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:38:39.584582", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T07:38:40.024249", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:38:40.026760", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T07:38:50.588633", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:38:50.591241", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T07:38:50.921578", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:38:50.922578", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T07:39:09.633835", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:39:09.636774", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T07:39:09.682899", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:39:09.685893", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T07:39:10.027463", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:39:10.028539", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T07:39:10.051757", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:39:10.053758", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T07:39:23.298615", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:39:23.300087", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T07:39:23.704752", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:39:23.706753", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T07:40:36.279501", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:40:36.282058", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T07:40:36.845370", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:40:36.848951", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T07:41:57.185188", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:41:57.187728", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T07:41:57.643777", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:41:57.644774", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T07:42:12.518825", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:42:12.521826", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T07:42:12.548430", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:42:12.550426", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T07:42:32.568484", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:42:32.570483", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T07:42:32.604301", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:42:32.607390", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T07:43:33.664719", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:43:33.666725", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T07:43:33.975591", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:43:33.978718", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T07:44:53.494133", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:44:53.495132", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T07:44:53.844906", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:44:53.845912", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T07:46:59.165516", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:46:59.171222", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T07:46:59.237737", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:46:59.243796", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T07:46:59.649386", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:46:59.650386", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T07:46:59.674095", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:46:59.675100", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T07:47:28.721791", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:47:28.724311", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T07:47:28.764495", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:47:28.767585", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T07:47:28.848401", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:47:28.851412", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T07:47:28.920525", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T07:47:28.923526", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T08:08:42.839547", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T08:08:42.843093", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T08:08:43.630835", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T08:08:43.635304", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T08:08:43.965822", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T08:08:43.986352", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T08:08:44.008901", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T08:08:44.012904", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T08:09:11.917506", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T08:09:11.921505", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T08:09:11.988975", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T08:09:11.991965", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T08:09:12.810632", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T08:09:12.812630", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T08:09:12.869153", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T08:09:12.871144", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T08:10:14.314895", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T08:10:14.316889", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T08:10:14.675150", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T08:10:14.677141", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T08:10:15.577691", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T08:10:15.580239", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T08:10:15.628766", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T08:10:15.630276", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T08:10:32.843356", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T08:10:32.847363", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T08:10:32.908946", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T08:10:32.910955", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T08:10:33.715089", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T08:10:33.718112", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T08:10:33.775673", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T08:10:33.778668", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T08:11:30.966216", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T08:11:30.969206", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T08:11:31.333127", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T08:11:31.336127", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T08:11:31.794901", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T08:11:31.796910", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T08:11:31.881311", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T08:11:31.883308", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T08:11:48.041998", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T08:11:48.043975", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T08:11:48.102513", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T08:11:48.105519", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T08:11:48.705129", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T08:11:48.708119", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T08:11:48.763180", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T08:11:48.765192", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T10:48:15.281395", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T10:48:15.285386", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T10:48:15.321452", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T10:48:15.325441", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T10:48:15.786634", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T10:48:15.789156", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T10:48:15.839622", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T10:48:15.842649", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T11:52:52.157717", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-25T11:52:52.161233", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-25T11:57:43.266868", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-25T11:57:43.267872", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-25T11:57:57.223421", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-25T11:57:57.230427", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-25T11:58:04.735462", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-25T11:58:04.735462", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-25T11:58:15.326958", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-25T11:58:15.334976", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-25T11:58:22.517547", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-25T11:58:22.518539", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-25T11:58:37.329121", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-25T11:58:37.337125", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-25T11:58:45.451665", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-25T11:58:45.452661", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-25T11:58:56.655167", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-25T11:58:56.661170", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-25T11:59:24.526525", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-25T11:59:24.527520", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-25T11:59:41.664805", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-25T11:59:41.668804", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-25T11:59:44.075764", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-25T11:59:44.076774", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-25T11:59:50.822203", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-25T11:59:50.830228", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-25T11:59:51.380485", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-25T11:59:51.380485", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-25T11:59:58.056854", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-25T11:59:58.058851", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-25T12:00:00.255357", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-25T12:00:00.256357", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-25T12:00:07.066981", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-25T12:00:07.071977", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-25T12:00:07.948475", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-25T12:00:07.948475", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-25T12:00:14.766696", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-25T12:00:14.769699", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-25T12:00:15.652753", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-25T12:00:15.653754", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-25T12:00:29.002339", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-25T12:00:29.005343", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-25T12:00:49.502044", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-25T12:00:49.502044", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-25T12:00:56.078975", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-25T12:00:56.082978", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-25T12:01:01.358472", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-25T12:01:01.358472", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-25T12:01:08.246691", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-25T12:01:08.249210", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-25T12:01:45.507093", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-25T12:01:45.508093", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-25T12:01:52.626997", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-25T12:01:52.631998", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-25T12:01:54.596577", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-25T12:01:54.596577", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-25T12:37:25.509042", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-25T12:37:25.512049", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-25T12:37:25.541737", "level": "ERROR", "logger": "app.database", "message": "Database session error: 500: Failed to retrieve notification statistics", "module": "database", "function": "get_db_session", "line": 94}
{"timestamp": "2025-06-25T12:37:28.172111", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-25T12:37:28.172111", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-25T12:37:34.291250", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-25T12:37:34.293244", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-25T12:37:39.436050", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-25T12:37:39.436050", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-25T12:37:45.319957", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-25T12:37:45.322958", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-25T12:37:57.542916", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-25T12:37:57.542916", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-25T12:38:03.790112", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-25T12:38:03.792624", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-25T12:38:16.699557", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-25T12:38:16.699557", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-25T12:40:02.649086", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-25T12:40:02.652091", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-25T14:38:21.041814", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T14:38:21.043833", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T14:38:21.222966", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T14:38:21.225133", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T14:38:21.246679", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T14:38:21.247681", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T14:38:21.385288", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T14:38:21.387296", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T14:49:44.075381", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T14:49:44.078381", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T14:49:44.127988", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T14:49:44.132991", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T14:49:44.177595", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T14:49:44.179537", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T14:49:44.212591", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T14:49:44.213595", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T15:10:24.455138", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T15:10:24.461718", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T15:10:24.494520", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T15:10:24.498568", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T15:10:24.968024", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T15:10:24.972182", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T15:10:25.038552", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T15:10:25.043660", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T15:20:15.765062", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-25T15:20:15.766092", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-25T15:20:36.243319", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-25T15:20:36.247956", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-25T15:26:32.917886", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T15:26:32.920419", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T15:26:33.265821", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 13 items from restaurants", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T15:26:33.267265", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 13 items from restaurants via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T15:26:33.272785", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T15:26:33.277833", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T17:02:17.442275", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-25T17:02:17.446284", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-25T17:03:10.362904", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-25T17:03:10.365904", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-25T17:05:49.966251", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-25T17:05:49.971768", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-25T17:06:43.699529", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-25T17:06:43.704048", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-25T17:08:40.688243", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-25T17:08:40.692248", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-25T17:08:40.695247", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-25T17:08:40.695247", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-25T17:09:39.340299", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-25T17:09:39.344300", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-25T17:11:03.734587", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T17:11:03.737586", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T17:11:04.102233", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T17:11:04.105241", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T17:11:04.545273", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T17:11:04.548279", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T17:11:04.750691", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T17:11:04.754707", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T17:11:04.928490", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T17:11:04.931501", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T17:11:05.295209", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T17:11:05.298198", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T17:15:29.102886", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T17:15:29.106051", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T17:15:29.156727", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T17:15:29.159742", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T17:16:02.119828", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T17:16:02.124881", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T17:16:02.152818", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T17:16:02.154833", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T17:19:24.177453", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T17:19:24.178557", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T17:19:24.297011", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T17:19:24.300002", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T17:27:03.861046", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-25T17:27:03.861046", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-25T17:27:03.912374", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-06-25T17:27:03.913901", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-06-25T17:27:13.706644", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-25T17:27:13.709823", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-25T17:27:13.713821", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-06-25T17:27:13.716337", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-06-25T17:34:59.696912", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T17:34:59.703205", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-06-25T17:34:59.763475", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-06-25T17:34:59.768037", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-07-17T04:50:40.676072", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-07-17T04:50:40.682130", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-07-17T06:55:15.557853", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-07-17T06:55:15.564319", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
{"timestamp": "2025-07-17T06:55:33.208843", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-07-17T06:55:33.214851", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-07-17T06:55:33.407430", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 18 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-07-17T06:55:33.412479", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 18 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-07-17T07:09:39.668575", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 19 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-07-17T07:09:39.670588", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 19 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-07-17T07:09:39.994506", "level": "INFO", "logger": "app.services.database_service", "message": "Retrieved 19 items from restaurant_users", "module": "database_service", "function": "get_all", "line": 52}
{"timestamp": "2025-07-17T07:09:40.000339", "level": "INFO", "logger": "app.utils.storage_async", "message": "Successfully retrieved 19 items from restaurant_users via database", "module": "storage_async", "function": "get_all_async", "line": 48}
{"timestamp": "2025-07-17T10:56:31.044829", "level": "INFO", "logger": "app.database", "message": "Database connections closed", "module": "database", "function": "close", "line": 77}
{"timestamp": "2025-07-17T10:56:31.046826", "level": "INFO", "logger": "app.database", "message": "Database cleanup completed", "module": "database", "function": "cleanup_database", "line": 156}
{"timestamp": "2025-07-21T08:52:50.100389", "level": "INFO", "logger": "app.database", "message": "Database tables created successfully", "module": "database", "function": "create_tables", "line": 66}
{"timestamp": "2025-07-21T08:52:50.107340", "level": "INFO", "logger": "app.database", "message": "Database initialized successfully", "module": "database", "function": "init_database", "line": 144}
