import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Clock, Download } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import TimeEntryEditor from "@/components/admin/TimeEntryEditor";

interface StaffHoursTabProps {
  staffData: any[];
}

const StaffHoursTab = ({ staffData }: StaffHoursTabProps) => {
  const [selectedPeriod, setSelectedPeriod] = useState<"week" | "month">("week");
  const [selectedWeek, setSelectedWeek] = useState<string>("current");
  const [selected<PERSON><PERSON><PERSON>, setSelectedMonth] = useState<string>("current");
  const [activeTab, setActiveTab] = useState<"summary" | "editor">("summary");

  // Generate mock hours data for each staff member
  const generateStaffHoursData = (staff: any) => {
    const assignedHours = staff.assignedHours || 35;
    const weeklyHours = Math.round((assignedHours / 4) * (0.8 + Math.random() * 0.4));
    const monthlyHours = Math.round(assignedHours * (0.9 + Math.random() * 0.2));
    
    return {
      ...staff,
      weeklyHours,
      monthlyHours
    };
  };

  // Add hours data to staff
  const staffWithHours = staffData.map(generateStaffHoursData);
  
  // Sort by hours worked (descending)
  const sortedStaff = [...staffWithHours].sort((a, b) => {
    if (selectedPeriod === "week") {
      return b.weeklyHours - a.weeklyHours;
    } else {
      return b.monthlyHours - a.monthlyHours;
    }
  });

  // Calculate total hours
  const totalHours = sortedStaff.reduce((sum, staff) => {
    return sum + (selectedPeriod === "week" ? staff.weeklyHours : staff.monthlyHours);
  }, 0);

  // Format hours with color based on target
  const formatHours = (hours: number, target: number) => {
    const percentage = (hours / target) * 100;
    let colorClass = "text-gray-900";
    
    if (percentage >= 95) {
      colorClass = "text-green-600 font-medium";
    } else if (percentage >= 80) {
      colorClass = "text-blue-600";
    } else if (percentage < 70) {
      colorClass = "text-red-600";
    }
    
    return <span className={colorClass}>{hours}h</span>;
  };

  return (
    <div className="space-y-4">
      <Tabs value={activeTab} onValueChange={(value: "summary" | "editor") => setActiveTab(value)}>
        <div className="flex justify-between items-center mb-4">
          <TabsList>
            <TabsTrigger value="summary">Hours Summary</TabsTrigger>
            <TabsTrigger value="editor">Time Entry Management</TabsTrigger>
          </TabsList>
        </div>
        
        <TabsContent value="summary">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-lg font-medium">Staff Hours</CardTitle>
              <div className="flex items-center gap-2">
                <Tabs value={selectedPeriod} onValueChange={(value: "week" | "month") => setSelectedPeriod(value)}>
                  <TabsList>
                    <TabsTrigger value="week">Weekly</TabsTrigger>
                    <TabsTrigger value="month">Monthly</TabsTrigger>
                  </TabsList>
                </Tabs>
                
                <div className="w-[140px]">
                  {selectedPeriod === "week" ? (
                    <Select value={selectedWeek} onValueChange={setSelectedWeek}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select week" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="current">Current Week</SelectItem>
                        <SelectItem value="previous">Previous Week</SelectItem>
                        <SelectItem value="twoWeeksAgo">Two Weeks Ago</SelectItem>
                      </SelectContent>
                    </Select>
                  ) : (
                    <Select value={selectedMonth} onValueChange={setSelectedMonth}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select month" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="current">Current Month</SelectItem>
                        <SelectItem value="previous">Previous Month</SelectItem>
                        <SelectItem value="twoMonthsAgo">Two Months Ago</SelectItem>
                      </SelectContent>
                    </Select>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="mb-4 p-4 bg-muted rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Clock className="h-5 w-5 mr-2 text-muted-foreground" />
                    <span className="text-sm font-medium">Total Hours:</span>
                  </div>
                  <span className="text-xl font-bold">{totalHours} hours</span>
                </div>
              </div>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Staff</TableHead>
                    <TableHead>Position</TableHead>
                    <TableHead>Assigned</TableHead>
                    <TableHead className="text-right">Actual Hours</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sortedStaff.map((staff) => (
                    <TableRow key={staff.id}>
                      <TableCell className="font-medium">{staff.name}</TableCell>
                      <TableCell>{staff.position}</TableCell>
                      <TableCell>
                        {selectedPeriod === "week" 
                          ? Math.round(staff.assignedHours / 4) 
                          : staff.assignedHours}h
                      </TableCell>
                      <TableCell className="text-right">
                        {formatHours(
                          selectedPeriod === "week" ? staff.weeklyHours : staff.monthlyHours,
                          selectedPeriod === "week" ? Math.round(staff.assignedHours / 4) : staff.assignedHours
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              <Button 
                variant="outline" 
                className="w-full mt-4"
              >
                <Download className="mr-2 h-4 w-4" /> Export Hours Report
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="editor">
          <TimeEntryEditor staffData={staffData} />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default StaffHoursTab;
