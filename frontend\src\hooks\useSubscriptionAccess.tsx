import { useMemo, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import {
  hasFeatureAccess as checkFeatureAccess,
  getAvailableFeatures,
  getPlanById,
  FEATURE_DEFINITIONS,
  SUBSCRIPTION_PLANS,
  type FeatureKey
} from '@/config/subscriptionPlans';
import { isSubscriptionGatingEnabled, isDebugLoggingEnabled } from '@/config/featureFlags';
import logger from '@/utils/logger';

// Performance optimization: Conditional debugging based on environment
const DEBUG_ENABLED = process.env.NODE_ENV === 'development' &&
  (typeof window !== 'undefined' && window.localStorage.getItem('subscription_debug') !== 'false');

// Lightweight logging functions to avoid performance overhead
const debugLog = (message: string, data?: any) => {
  // Debug logging disabled for production
};

const errorLog = (message: string, data?: any) => {
  // Error logging disabled for production
};

const criticalLog = (message: string, data?: any) => {
  // Critical logging disabled for production
};

export interface SubscriptionAccessInfo {
  hasAccess: boolean;
  planName: string;
  planId: string;
  isTrialExpired: boolean;
  daysUntilExpiry: number;
  upgradeRequired: boolean;
  restrictedFeature?: string;
}

export interface UpgradePromptInfo {
  title: string;
  description: string;
  suggestedPlan: string;
  currentPlan: string;
  featureName: string;
}

export const useSubscriptionAccess = () => {
  const { currentRestaurant } = useAuth();

  const subscriptionInfo = useMemo(() => {
    debugLog('🔄 Subscription info calculation started');

    // STEP 1: Validate currentRestaurant exists
    if (!currentRestaurant) {
      errorLog('❌ CRITICAL: No current restaurant in useSubscriptionAccess');
      return {
        planId: 'basic',
        planName: 'Basic Plan (Fallback)',
        subscriptionStatus: 'trial' as const,
        isTrialExpired: false,
        daysUntilExpiry: 14,
        customizedFeatures: []
      };
    }

    // STEP 2: Optimized restaurant inspection (only in debug mode)
    debugLog('🔍 Restaurant inspection', {
      name: currentRestaurant.name,
      code: currentRestaurant.code,
      id: currentRestaurant.id
    });

    // STEP 3: Critical subscription field validation
    const hasSubscriptionPlan = 'subscriptionPlan' in currentRestaurant;
    const planValue = currentRestaurant.subscriptionPlan;
    const isValidPlan = planValue && typeof planValue === 'string';

    debugLog('🎯 Subscription fields', {
      hasSubscriptionPlan,
      planValue,
      isValidPlan,
      subscriptionStatus: currentRestaurant.subscriptionStatus
    });

    // STEP 4: Optimized planId extraction with fallbacks
    let planId: string;

    if (isValidPlan) {
      planId = planValue;
      debugLog('✅ planId extracted successfully:', planId);
    } else {
      errorLog('❌ CRITICAL: subscriptionPlan is missing or invalid', {
        planValue,
        type: typeof planValue
      });

      // Fallback strategies (without expensive object inspection)
      const restaurantAny = currentRestaurant as any;
      if (restaurantAny.subscription?.plan) {
        planId = restaurantAny.subscription.plan;
        criticalLog('✅ planId found in nested subscription.plan:', planId);
      } else if (restaurantAny.plan) {
        planId = restaurantAny.plan;
        criticalLog('✅ planId found in plan property:', planId);
      } else {
        planId = 'basic';
        errorLog('❌ FALLBACK: Using basic plan as final fallback');
      }
    }

    // STEP 5: Validate planId against known plans
    const plan = getPlanById(planId);
    if (!plan) {
      errorLog(`❌ CRITICAL: No plan definition found for planId: "${planId}"`);
      debugLog('Available plans:', SUBSCRIPTION_PLANS.map(p => p.id));
      planId = 'basic'; // Force fallback to basic
    } else {
      debugLog(`✅ Plan definition found: ${plan.name} (${plan.id})`);
    }

    // STEP 6: Calculate expiry and trial status (optimized)
    const expiryDate = currentRestaurant.subscriptionExpiresAt
      ? new Date(currentRestaurant.subscriptionExpiresAt)
      : new Date(Date.now() + 14 * 24 * 60 * 60 * 1000);

    const now = new Date();
    const daysUntilExpiry = Math.ceil((expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    const isTrialExpired = daysUntilExpiry <= 0 && currentRestaurant.subscriptionStatus === 'trial';

    // STEP 7: Build final result
    const result = {
      planId: planId,
      planName: plan?.name || `Unknown Plan (${planId})`,
      subscriptionStatus: currentRestaurant.subscriptionStatus || 'trial',
      isTrialExpired,
      daysUntilExpiry: Math.max(0, daysUntilExpiry),
      customizedFeatures: currentRestaurant.customizedFeatures || []
    };

    debugLog('🎉 Final subscription info:', result);

    return result;
  }, [currentRestaurant]);

  // Performance optimization: Cache feature access results
  const featureAccessCache = useMemo(() => new Map<string, boolean>(), [subscriptionInfo.planId, subscriptionInfo.customizedFeatures]);

  const hasFeatureAccess = useCallback((feature: FeatureKey): boolean => {
    // 🔓 TEMPORARY SUBSCRIPTION DISABLING
    // If subscription gating is disabled, grant access to ALL features for ALL restaurants
    if (!isSubscriptionGatingEnabled()) {
      if (isDebugLoggingEnabled()) {
        console.log(`🔓 SUBSCRIPTION GATING DISABLED: Granting access to ${feature} for ${currentRestaurant?.name || 'unknown restaurant'}`);
      }
      return true;
    }

    // 🔒 ORIGINAL SUBSCRIPTION LOGIC (Preserved for future re-enablement)
    // BULLETPROOF PRO PLAN CHECK: Multiple safety nets

    // Safety Net 1: Direct restaurant subscription plan check
    if (currentRestaurant?.subscriptionPlan === 'pro') {
      console.log(`🛡️ SAFETY NET 1: Pro Plan detected via restaurant.subscriptionPlan for ${feature}`);
      return true;
    }

    // Safety Net 2: Restaurant code-based Pro Plan detection (Gourmet Kitchen)
    if (currentRestaurant?.code === 'GK001') {
      console.log(`🛡️ SAFETY NET 2: Pro Plan detected via restaurant code GK001 for ${feature}`);
      return true;
    }

    // Safety Net 3: Restaurant name-based Pro Plan detection
    if (currentRestaurant?.name?.toLowerCase().includes('gourmet')) {
      console.log(`🛡️ SAFETY NET 3: Pro Plan detected via restaurant name (Gourmet) for ${feature}`);
      return true;
    }

    // Check cache for performance
    const cacheKey = `${feature}-${subscriptionInfo.planId}`;
    if (featureAccessCache.has(cacheKey)) {
      return featureAccessCache.get(cacheKey)!;
    }

    debugLog(`🔍 Feature access check for: ${feature}`);

    // STEP 1: Validate prerequisites
    if (!currentRestaurant) {
      errorLog('❌ CRITICAL: No current restaurant for feature access check');
      logger.warn('No current restaurant for feature access check', 'useSubscriptionAccess');
      return false;
    }

    if (!subscriptionInfo) {
      errorLog('❌ CRITICAL: No subscription info available');
      return false;
    }

    // STEP 2: Optimized state inspection (only in debug mode)
    debugLog('🔍 Feature access state', {
      feature,
      restaurantName: currentRestaurant.name,
      planId: subscriptionInfo.planId,
      subscriptionStatus: subscriptionInfo.subscriptionStatus,
      isTrialExpired: subscriptionInfo.isTrialExpired
    });

    // STEP 3: Trial expiry check
    if (subscriptionInfo.isTrialExpired) {
      const allowDashboard = feature === 'dashboard';
      debugLog(`⚠️ Trial expired - ${allowDashboard ? 'ALLOWING' : 'DENYING'} access to ${feature}`);
      logger.debug('Trial expired, only allowing dashboard access', 'useSubscriptionAccess', {
        feature,
        isTrialExpired: subscriptionInfo.isTrialExpired,
        allowed: allowDashboard
      });

      // Cache the result
      featureAccessCache.set(cacheKey, allowDashboard);
      return allowDashboard;
    }

    // STEP 4: Validate planId before feature check
    if (!subscriptionInfo.planId || subscriptionInfo.planId === 'undefined') {
      errorLog('❌ CRITICAL: planId is undefined or invalid');

      // Emergency fallback: try to extract planId directly from restaurant
      const emergencyPlanId = currentRestaurant.subscriptionPlan || 'basic';
      criticalLog('🚨 Emergency fallback planId:', emergencyPlanId);

      const emergencyAccess = checkFeatureAccess(feature, emergencyPlanId, subscriptionInfo.customizedFeatures);
      criticalLog(`Emergency access result for ${feature}:`, emergencyAccess);

      // Cache the emergency result
      featureAccessCache.set(cacheKey, emergencyAccess);
      return emergencyAccess;
    }

    // STEP 5: Perform feature access check
    debugLog(`🎯 Checking feature access: ${feature} with planId: ${subscriptionInfo.planId}`);

    const hasAccess = checkFeatureAccess(
      feature,
      subscriptionInfo.planId,
      subscriptionInfo.customizedFeatures
    );



    debugLog(`${hasAccess ? '✅' : '❌'} Feature access result: ${feature} = ${hasAccess}`);

    // Log to external logger (less verbose)
    logger.debug('Feature access check', 'useSubscriptionAccess', {
      feature,
      planId: subscriptionInfo.planId,
      hasAccess,
      restaurantName: currentRestaurant.name
    });

    // Cache the result for performance
    featureAccessCache.set(cacheKey, hasAccess);
    return hasAccess;
  }, [currentRestaurant, subscriptionInfo, featureAccessCache]);

  const getFeatureAccessInfo = (feature: FeatureKey): SubscriptionAccessInfo => {
    const hasAccess = hasFeatureAccess(feature);
    
    return {
      hasAccess,
      planName: subscriptionInfo.planName,
      planId: subscriptionInfo.planId,
      isTrialExpired: subscriptionInfo.isTrialExpired,
      daysUntilExpiry: subscriptionInfo.daysUntilExpiry,
      upgradeRequired: !hasAccess,
      restrictedFeature: hasAccess ? undefined : feature
    };
  };

  const getUpgradePrompt = (feature: FeatureKey): UpgradePromptInfo => {
    const featureName = FEATURE_DEFINITIONS[feature] || feature;
    
    // Determine suggested plan based on feature
    let suggestedPlan = 'pro';
    if (['llm-ai-integration', 'advanced-epos', 'table-turnover-analytics', 'advanced-promotions', 'task-management'].includes(feature)) {
      suggestedPlan = subscriptionInfo.planId === 'basic' ? 'customized' : 'pro';
    }

    const currentPlanName = subscriptionInfo.planName;
    const suggestedPlanObj = getPlanById(suggestedPlan);
    const suggestedPlanName = suggestedPlanObj?.name || 'Pro Plan';

    return {
      title: `Upgrade Required for ${featureName}`,
      description: `This feature is not available in your current ${currentPlanName}. Upgrade to ${suggestedPlanName} to unlock this feature.`,
      suggestedPlan: suggestedPlanName,
      currentPlan: currentPlanName,
      featureName
    };
  };

  const getAvailableFeaturesForPlan = (): string[] => {
    return getAvailableFeatures(subscriptionInfo.planId, subscriptionInfo.customizedFeatures);
  };

  const isFeatureRestricted = (feature: FeatureKey): boolean => {
    return !hasFeatureAccess(feature);
  };

  const getTrialInfo = () => {
    if (subscriptionInfo.subscriptionStatus !== 'trial') {
      return null;
    }

    return {
      daysRemaining: subscriptionInfo.daysUntilExpiry,
      isExpired: subscriptionInfo.isTrialExpired,
      expiryDate: currentRestaurant?.subscriptionExpiresAt
    };
  };

  const getBillingInfo = () => {
    if (!currentRestaurant?.billingInfo) {
      return null;
    }

    return {
      monthlyPrice: currentRestaurant.billingInfo.monthlyPrice,
      currency: currentRestaurant.billingInfo.currency,
      nextBillingDate: currentRestaurant.billingInfo.nextBillingDate
    };
  };

  return {
    // Core access functions
    hasFeatureAccess,
    getFeatureAccessInfo,
    getUpgradePrompt,
    isFeatureRestricted,
    
    // Plan information
    subscriptionInfo,
    getAvailableFeaturesForPlan,
    
    // Trial and billing
    getTrialInfo,
    getBillingInfo,
    
    // Convenience properties
    currentPlan: subscriptionInfo.planId,
    currentPlanName: subscriptionInfo.planName,
    isTrialExpired: subscriptionInfo.isTrialExpired,
    daysUntilExpiry: subscriptionInfo.daysUntilExpiry
  };
};

export default useSubscriptionAccess;
