"""
Database migration utilities for RestroManage.
Provides functions to create, drop, and manage database tables.
"""

import logging
from sqlalchemy import text, inspect
from sqlalchemy.exc import SQLAlchemyError
from .database import engine, get_db_session
from .models.base import Base
from .models import *

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_tables():
    """
    Create all database tables based on SQLAlchemy models.
    This is equivalent to running migrations.
    """
    try:
        logger.info("Creating database tables...")
        Base.metadata.create_all(bind=engine)
        logger.info("✅ All tables created successfully!")
        return True
    except SQLAlchemyError as e:
        logger.error(f"❌ Error creating tables: {e}")
        return False

def drop_tables():
    """
    Drop all database tables.
    WARNING: This will delete all data!
    """
    try:
        logger.info("Dropping all database tables...")
        Base.metadata.drop_all(bind=engine)
        logger.info("✅ All tables dropped successfully!")
        return True
    except SQLAlchemyError as e:
        logger.error(f"❌ Error dropping tables: {e}")
        return False

def reset_database():
    """
    Reset the database by dropping and recreating all tables.
    WARNING: This will delete all data!
    """
    try:
        logger.info("Resetting database...")
        drop_tables()
        create_tables()
        logger.info("✅ Database reset successfully!")
        return True
    except SQLAlchemyError as e:
        logger.error(f"❌ Error resetting database: {e}")
        return False

def check_table_exists(table_name: str) -> bool:
    """
    Check if a specific table exists in the database.
    """
    try:
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        return table_name in tables
    except SQLAlchemyError as e:
        logger.error(f"❌ Error checking table existence: {e}")
        return False

def get_table_info():
    """
    Get information about all tables in the database.
    """
    try:
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        
        table_info = {}
        for table in tables:
            columns = inspector.get_columns(table)
            table_info[table] = {
                'columns': len(columns),
                'column_names': [col['name'] for col in columns]
            }
        
        return table_info
    except SQLAlchemyError as e:
        logger.error(f"❌ Error getting table info: {e}")
        return {}

def verify_database_schema():
    """
    Verify that all expected tables exist with correct structure.
    """
    expected_tables = [
        'users', 'user_sessions', 'restaurants', 'restaurant_users',
        'menu_categories', 'menu_items', 'orders', 'order_items', 'order_discounts',
        'promo_codes', 'promo_code_usage', 'campaigns',
        'staff', 'staff_schedules', 'tables', 'table_reservations',
        'inventory_items', 'inventory_transactions',
        'split_bills', 'split_portions', 'split_bill_payments',
        'sales_records', 'customer_analytics',
        'forecast_data', 'trend_analysis',
        'ai_requests', 'ai_responses', 'ai_usage_stats'
    ]
    
    try:
        inspector = inspect(engine)
        existing_tables = inspector.get_table_names()
        
        missing_tables = [table for table in expected_tables if table not in existing_tables]
        extra_tables = [table for table in existing_tables if table not in expected_tables]
        
        verification_result = {
            'total_expected': len(expected_tables),
            'total_existing': len(existing_tables),
            'missing_tables': missing_tables,
            'extra_tables': extra_tables,
            'schema_valid': len(missing_tables) == 0
        }
        
        if verification_result['schema_valid']:
            logger.info("✅ Database schema verification passed!")
        else:
            logger.warning(f"⚠️ Schema verification issues found: {len(missing_tables)} missing tables")
            
        return verification_result
        
    except SQLAlchemyError as e:
        logger.error(f"❌ Error verifying database schema: {e}")
        return {'schema_valid': False, 'error': str(e)}

def get_database_stats():
    """
    Get statistics about the database.
    """
    try:
        db = get_db_session()
        
        stats = {}
        
        # Get table row counts
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        
        for table in tables:
            try:
                result = db.execute(text(f"SELECT COUNT(*) FROM {table}"))
                count = result.scalar()
                stats[table] = count
            except Exception as e:
                stats[table] = f"Error: {e}"
        
        db.close()
        return stats
        
    except SQLAlchemyError as e:
        logger.error(f"❌ Error getting database stats: {e}")
        return {}

def initialize_database():
    """
    Initialize the database with tables and basic setup.
    This is the main function to call for setting up a new database.
    """
    try:
        logger.info("🚀 Initializing RestroManage database...")
        
        # Create all tables
        if not create_tables():
            return False
            
        # Verify schema
        verification = verify_database_schema()
        if not verification['schema_valid']:
            logger.error("❌ Database schema verification failed!")
            return False
            
        # Get initial stats
        stats = get_database_stats()
        logger.info(f"📊 Database initialized with {len(stats)} tables")
        
        logger.info("✅ Database initialization completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Database initialization failed: {e}")
        return False

if __name__ == "__main__":
    # Run database initialization when script is executed directly
    initialize_database()
