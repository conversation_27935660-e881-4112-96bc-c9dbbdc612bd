#!/usr/bin/env python3
"""
Database initialization script for RestroManage.
Run this script to set up the database for the first time.
"""

import os
import sys
import logging
from pathlib import Path

# Add the parent directory to the path so we can import from the app
sys.path.append(str(Path(__file__).parent.parent))

from Database.database import test_connection, get_database_info
from Database.migrations import initialize_database, verify_database_schema, get_database_stats

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """
    Main function to initialize the database.
    """
    print("🚀 RestroManage Database Initialization")
    print("=" * 50)
    
    # Test database connection
    print("\n1. Testing database connection...")
    if not test_connection():
        print("❌ Database connection failed!")
        print("Please check your database configuration and try again.")
        return False
    
    print("✅ Database connection successful!")
    
    # Show database info
    print("\n2. Database configuration:")
    db_info = get_database_info()
    for key, value in db_info.items():
        print(f"   {key}: {value}")
    
    # Initialize database
    print("\n3. Initializing database tables...")
    if not initialize_database():
        print("❌ Database initialization failed!")
        return False
    
    # Verify schema
    print("\n4. Verifying database schema...")
    verification = verify_database_schema()
    
    if verification['schema_valid']:
        print("✅ Database schema verification passed!")
        print(f"   Total tables: {verification['total_existing']}")
    else:
        print("⚠️ Database schema verification issues:")
        if verification.get('missing_tables'):
            print(f"   Missing tables: {verification['missing_tables']}")
        if verification.get('extra_tables'):
            print(f"   Extra tables: {verification['extra_tables']}")
    
    # Show database statistics
    print("\n5. Database statistics:")
    stats = get_database_stats()
    for table, count in stats.items():
        print(f"   {table}: {count} records")
    
    print("\n" + "=" * 50)
    print("✅ Database initialization completed successfully!")
    print("\nNext steps:")
    print("1. Start the FastAPI server: uvicorn main:app --reload")
    print("2. Access the API documentation at: http://localhost:8000/docs")
    print("3. Begin Phase 2: User Registration Flow implementation")
    
    return True

def reset_database():
    """
    Reset the database (drop and recreate all tables).
    WARNING: This will delete all data!
    """
    print("⚠️  WARNING: This will delete ALL data in the database!")
    response = input("Are you sure you want to continue? (yes/no): ")
    
    if response.lower() != 'yes':
        print("Database reset cancelled.")
        return False
    
    print("🔄 Resetting database...")
    from Database.migrations import reset_database
    
    if reset_database():
        print("✅ Database reset completed successfully!")
        return True
    else:
        print("❌ Database reset failed!")
        return False

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="RestroManage Database Initialization")
    parser.add_argument(
        "--reset", 
        action="store_true", 
        help="Reset the database (WARNING: Deletes all data)"
    )
    
    args = parser.parse_args()
    
    if args.reset:
        reset_database()
    else:
        main()
