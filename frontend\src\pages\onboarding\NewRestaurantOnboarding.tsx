import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Progress } from '@/components/ui/progress';
import {
  ChefHat,
  Users,
  UtensilsCrossed,
  Settings,
  Calendar,
  MapPin,
  Phone,
  Mail,
  Clock,
  Table,
  ArrowRight,
  ArrowLeft,
  CheckCircle,
  AlertCircle,
  Building2,
  CreditCard,
  DollarSign,
  Save,
  Utensils,
  Plus,
  Trash2
} from 'lucide-react';
import { toast } from '@/components/ui/sonner';
import logger from '@/utils/logger';
import apiService from '@/services/apiService';
import { useAuth } from '@/contexts/AuthContext';
import {
  BasicInfoStep,
  TableSetupStep,
  MenuSetupStep,
  StaffSetupStep,
  SettingsStep
} from '@/components/onboarding/OnboardingSteps';
import { SubscriptionPlanStep } from '@/components/onboarding/SubscriptionPlanStep';

interface NewRestaurant {
  id: number;
  name: string;
  code: string;
  email: string;
  phone: string;
  address: string;
  owner_name: string;
  restaurant_type: string;
  setup_data?: any;
  createdAt: string;
  is_active: boolean;
}

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  completed: boolean;
}

interface OnboardingData {
  basicInfo: {
    name: string;
    address: string;
    phone: string;
    email: string;
    website: string;
    description: string;
  };
  tables: {
    tableCount: number;
    tables: Array<{ number: number; capacity: number; }>;
  };
  menu: {
    categories: Array<{ name: string; description: string; }>;
    sampleItems: boolean;
  };
  staff: {
    adminUser: {
      name: string;
      email: string;
      pin: string;
      role: string;
    };
  };
  settings: {
    currency: string;
    timezone: string;
    taxRate: number;
    operatingHours: {
      [key: string]: { open: string; close: string; closed: boolean; };
    };
    paymentMethods: string[];
  };
  subscription: {
    selectedPlan: string;
    customizedFeatures: string[];
    billingInterval: 'monthly' | 'yearly';
  };
}

const NewRestaurantOnboarding = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { loginRestaurant } = useAuth();
  const [restaurant, setRestaurant] = useState<NewRestaurant | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [currentStep, setCurrentStep] = useState(0);
  const [isSaving, setIsSaving] = useState(false);

  const [onboardingData, setOnboardingData] = useState<OnboardingData>({
    basicInfo: {
      name: '',
      address: '',
      phone: '',
      email: '',
      website: '',
      description: ''
    },
    tables: {
      tableCount: 10,
      tables: []
    },
    menu: {
      categories: [
        { name: 'Appetizers', description: 'Start your meal right' },
        { name: 'Main Courses', description: 'Our signature dishes' },
        { name: 'Desserts', description: 'Sweet endings' },
        { name: 'Beverages', description: 'Drinks and refreshments' }
      ],
      sampleItems: true
    },
    staff: {
      adminUser: {
        name: '',
        email: '',
        pin: '',
        role: 'admin'
      }
    },
    settings: {
      currency: 'GBP',
      timezone: 'Europe/London',
      taxRate: 20,
      operatingHours: {
        monday: { open: '09:00', close: '22:00', closed: false },
        tuesday: { open: '09:00', close: '22:00', closed: false },
        wednesday: { open: '09:00', close: '22:00', closed: false },
        thursday: { open: '09:00', close: '22:00', closed: false },
        friday: { open: '09:00', close: '23:00', closed: false },
        saturday: { open: '10:00', close: '23:00', closed: false },
        sunday: { open: '10:00', close: '22:00', closed: false }
      },
      paymentMethods: ['cash', 'card']
    },
    subscription: {
      selectedPlan: 'basic',
      customizedFeatures: [],
      billingInterval: 'monthly'
    }
  });

  const steps: OnboardingStep[] = [
    {
      id: 'basic',
      title: 'Basic Information',
      description: 'Restaurant details and contact information',
      icon: Building2,
      completed: false
    },
    {
      id: 'tables',
      title: 'Table Setup',
      description: 'Configure your seating arrangement',
      icon: Table,
      completed: false
    },
    {
      id: 'menu',
      title: 'Menu Categories',
      description: 'Set up your menu structure',
      icon: Utensils,
      completed: false
    },
    {
      id: 'staff',
      title: 'Admin User',
      description: 'Create your admin account',
      icon: Users,
      completed: false
    },
    {
      id: 'settings',
      title: 'System Settings',
      description: 'Configure operating hours and preferences',
      icon: Settings,
      completed: false
    },
    {
      id: 'subscription',
      title: 'Subscription Plan',
      description: 'Choose your plan and features',
      icon: CreditCard,
      completed: false
    }
  ];

  const restaurantCode = searchParams.get('code');

  useEffect(() => {
    if (!restaurantCode) {
      logger.warn('No restaurant code provided for onboarding', 'NewRestaurantOnboarding');
      navigate('/');
      return;
    }

    loadRestaurantData();
  }, [restaurantCode, navigate]);

  const loadRestaurantData = async () => {
    try {
      setIsLoading(true);
      logger.info('Loading restaurant data for onboarding', 'NewRestaurantOnboarding', { code: restaurantCode });

      const restaurants = await apiService.restaurant.getRestaurants() as any;
      const foundRestaurant = (restaurants as NewRestaurant[])?.find((r: NewRestaurant) => r.code === restaurantCode);

      if (!foundRestaurant) {
        toast.error('Restaurant not found');
        navigate('/');
        return;
      }

      setRestaurant(foundRestaurant);

      // Pre-populate basic info from restaurant data
      setOnboardingData(prev => ({
        ...prev,
        basicInfo: {
          name: foundRestaurant.name,
          address: foundRestaurant.address,
          phone: foundRestaurant.phone,
          email: foundRestaurant.email,
          website: '',
          description: ''
        }
      }));

      logger.info('Restaurant data loaded for onboarding', 'NewRestaurantOnboarding', {
        restaurantId: foundRestaurant.id,
        restaurantName: foundRestaurant.name
      });
    } catch (error) {
      logger.error('Failed to load restaurant data', 'NewRestaurantOnboarding', {
        error: error.message
      });
      toast.error('Failed to load restaurant data');
      navigate('/');
    } finally {
      setIsLoading(false);
    }
  };

  const updateOnboardingData = (step: string, data: any) => {
    setOnboardingData(prev => ({
      ...prev,
      [step]: { ...prev[step], ...data }
    }));
  };

  const generateTables = (count: number) => {
    const tables = [];
    for (let i = 1; i <= count; i++) {
      tables.push({
        number: i,
        capacity: i <= 4 ? 2 : i <= 8 ? 4 : 6 // Vary capacity based on table number
      });
    }
    return tables;
  };

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const validateCurrentStep = (): boolean => {
    switch (steps[currentStep].id) {
      case 'basic':
        return !!(onboardingData.basicInfo.name && onboardingData.basicInfo.address);
      case 'tables':
        return onboardingData.tables.tableCount > 0;
      case 'menu':
        return onboardingData.menu.categories.length > 0;
      case 'staff':
        return !!(onboardingData.staff.adminUser.name && onboardingData.staff.adminUser.pin);
      case 'settings':
        return true; // Settings have defaults
      case 'subscription':
        return !!(onboardingData.subscription.selectedPlan);
      default:
        return true;
    }
  };

  const completeOnboarding = async () => {
    if (!restaurant) return;

    try {
      setIsSaving(true);
      logger.info('Completing restaurant onboarding', 'NewRestaurantOnboarding', {
        restaurantCode: restaurant.code
      });

      // Generate tables based on count
      const tables = generateTables(onboardingData.tables.tableCount);

      // Prepare complete setup data
      const setupData = {
        ...onboardingData,
        tables: { ...onboardingData.tables, tables },
        completedAt: new Date().toISOString(),
        hasData: true
      };

      // Save setup data to restaurant (this would be an API call in real implementation)
      // For now, we'll simulate saving to localStorage
      const restaurantSetupKey = `restaurant_setup_${restaurant.code}`;
      localStorage.setItem(restaurantSetupKey, JSON.stringify(setupData));

      // Update the restaurant data in localStorage to mark hasData as true and save subscription
      const storedRestaurant = localStorage.getItem('currentRestaurant');
      if (storedRestaurant) {
        const restaurantData = JSON.parse(storedRestaurant);
        restaurantData.hasData = true;
        restaurantData.setupData = setupData;

        // Update subscription information
        restaurantData.subscriptionPlan = onboardingData.subscription.selectedPlan;
        restaurantData.customizedFeatures = onboardingData.subscription.customizedFeatures;
        restaurantData.subscriptionStatus = 'trial';
        restaurantData.subscriptionExpiresAt = new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(); // 14 days trial

        // Calculate billing info based on selected plan
        const { calculateCustomizedPlanPrice } = await import('@/config/subscriptionPlans');
        const monthlyPrice = onboardingData.subscription.selectedPlan === 'customized'
          ? calculateCustomizedPlanPrice(onboardingData.subscription.customizedFeatures)
          : onboardingData.subscription.selectedPlan === 'pro' ? 69 : 29;

        restaurantData.billingInfo = {
          monthlyPrice,
          currency: 'GBP',
          nextBillingDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString()
        };

        localStorage.setItem('currentRestaurant', JSON.stringify(restaurantData));
      }

      // Try to update the backend if available
      try {
        const backendAvailable = await apiService.checkBackendConnectivity();
        if (backendAvailable) {
          // Update restaurant hasData status in backend
          await apiService.restaurant.updateRestaurant(String(restaurant.id), {
            hasData: true,
            setupData: setupData
          });
          logger.info('Updated restaurant hasData status in backend', 'NewRestaurantOnboarding');
        }
      } catch (error) {
        logger.warn('Failed to update backend, using localStorage only', 'NewRestaurantOnboarding', {
          error: error.message
        });
      }

      logger.info('Restaurant onboarding completed', 'NewRestaurantOnboarding', {
        restaurantCode: restaurant.code,
        setupSteps: Object.keys(setupData)
      });

      toast.success('Restaurant setup completed successfully!');

      // Authenticate the restaurant and navigate to dashboard
      await loginRestaurant(restaurant.code, 'test123', false);
      navigate('/staff-login');

    } catch (error) {
      logger.error('Failed to complete onboarding', 'NewRestaurantOnboarding', {
        error: error.message
      });
      toast.error('Failed to complete setup. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleSkipOnboarding = async () => {
    if (!restaurant) return;

    logger.userAction('skip onboarding clicked', 'NewRestaurantOnboarding', {
      restaurantCode: restaurant.code
    });

    try {
      // Still authenticate the restaurant but skip setup
      await loginRestaurant(restaurant.code, 'test123', false);
      navigate('/staff-login');
    } catch (error) {
      logger.error('Failed to authenticate after skipping onboarding', 'NewRestaurantOnboarding', {
        error: error.message
      });
      navigate('/');
    }
  };

  const renderCurrentStep = () => {
    switch (steps[currentStep].id) {
      case 'basic':
        return <BasicInfoStep data={onboardingData} updateData={updateOnboardingData} />;
      case 'tables':
        return <TableSetupStep data={onboardingData} updateData={updateOnboardingData} />;
      case 'menu':
        return <MenuSetupStep data={onboardingData} updateData={updateOnboardingData} />;
      case 'staff':
        return <StaffSetupStep data={onboardingData} updateData={updateOnboardingData} />;
      case 'settings':
        return <SettingsStep data={onboardingData} updateData={updateOnboardingData} />;
      case 'subscription':
        return <SubscriptionPlanStep data={onboardingData.subscription} updateData={(data) => updateOnboardingData('subscription', data)} />;
      default:
        return null;
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <ChefHat className="h-12 w-12 animate-pulse mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Setting up your restaurant...</p>
        </div>
      </div>
    );
  }

  if (!restaurant) {
    return null;
  }

  const progressPercentage = ((currentStep + 1) / steps.length) * 100;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center space-x-3">
              <ChefHat className="h-8 w-8 text-blue-600" />
              <div>
                <h1 className="text-xl font-bold text-gray-900">Restaurant Setup</h1>
                <p className="text-sm text-gray-500">Setting up {restaurant.name}</p>
              </div>
            </div>
            <Badge variant="outline" className="text-blue-600 border-blue-200">
              Step {currentStep + 1} of {steps.length}
            </Badge>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Progress Section */}
        <div className="mb-8">
          <div className="flex justify-between text-sm text-gray-600 mb-2">
            <span>{steps[currentStep].title}</span>
            <span>Step {currentStep + 1} of {steps.length}</span>
          </div>
          <Progress value={progressPercentage} className="mb-4" />

          {/* Step Navigation */}
          <div className="flex justify-center space-x-4 mb-6">
            {steps.map((step, index) => {
              const StepIcon = step.icon;
              return (
                <div
                  key={step.id}
                  className={`flex items-center space-x-2 px-3 py-2 rounded-lg ${
                    index === currentStep
                      ? 'bg-blue-100 text-blue-700'
                      : index < currentStep
                      ? 'bg-green-100 text-green-700'
                      : 'bg-gray-100 text-gray-500'
                  }`}
                >
                  <StepIcon className="h-4 w-4" />
                  <span className="text-sm font-medium hidden sm:block">{step.title}</span>
                  {index < currentStep && <CheckCircle className="h-4 w-4" />}
                </div>
              );
            })}
          </div>
        </div>

        {/* Current Step Content */}
        <div className="mb-8">
          {renderCurrentStep()}
        </div>

        {/* Navigation Buttons */}
        <div className="flex justify-between items-center">
          <Button
            variant="outline"
            onClick={prevStep}
            disabled={currentStep === 0}
            className="flex items-center"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Previous
          </Button>

          <Button onClick={handleSkipOnboarding} variant="ghost" size="sm">
            Skip Setup
          </Button>

          {currentStep === steps.length - 1 ? (
            <Button
              onClick={completeOnboarding}
              disabled={!validateCurrentStep() || isSaving}
              className="flex items-center"
            >
              {isSaving ? (
                <>
                  <Save className="h-4 w-4 mr-2 animate-spin" />
                  Completing...
                </>
              ) : (
                <>
                  Complete Setup
                  <CheckCircle className="h-4 w-4 ml-2" />
                </>
              )}
            </Button>
          ) : (
            <Button
              onClick={nextStep}
              disabled={!validateCurrentStep()}
              className="flex items-center"
            >
              Next
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          )}
        </div>

        {/* Help Text */}
        <div className="mt-6 text-center">
          <p className="text-sm text-gray-600">
            {currentStep === steps.length - 1
              ? "Complete the setup to start using your restaurant management system."
              : "Fill in the required information to continue to the next step."
            }
          </p>
        </div>
      </div>
    </div>
  );
};

export default NewRestaurantOnboarding;
