"""
Frontend logging endpoint for collecting client-side logs
"""

from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel
from typing import Optional, Dict, Any, List
from datetime import datetime
import json
import os
from app.utils.logging_config import logger

router = APIRouter()

class FrontendLogEntry(BaseModel):
    id: str
    timestamp: str
    level: str
    message: str
    component: str
    data: Optional[Dict[str, Any]] = None
    url: str
    userAgent: str
    sessionId: str
    source: str = "frontend"
    browser: Optional[Dict[str, Any]] = None
    screen: Optional[Dict[str, Any]] = None
    viewport: Optional[Dict[str, Any]] = None

class FrontendLogBatch(BaseModel):
    logs: List[FrontendLogEntry]

# Ensure logs directory exists
FRONTEND_LOGS_DIR = "logs/frontend"
os.makedirs(FRONTEND_LOGS_DIR, exist_ok=True)

def write_log_to_file(log_entry: FrontendLogEntry):
    """Write frontend log entry to file"""
    try:
        # Create filename based on date and level
        date_str = datetime.now().strftime("%Y-%m-%d")
        filename = f"{FRONTEND_LOGS_DIR}/frontend_{date_str}_{log_entry.level.lower()}.log"
        
        # Format log entry
        log_line = {
            "timestamp": log_entry.timestamp,
            "level": log_entry.level,
            "component": log_entry.component,
            "message": log_entry.message,
            "sessionId": log_entry.sessionId,
            "url": log_entry.url,
            "data": log_entry.data,
            "browser": log_entry.browser,
            "screen": log_entry.screen,
            "viewport": log_entry.viewport
        }
        
        # Append to file
        with open(filename, "a", encoding="utf-8") as f:
            f.write(json.dumps(log_line) + "\n")
            
    except Exception as e:
        logger.error(f"Failed to write frontend log to file: {e}", "FrontendLogs")

@router.post("/frontend")
async def receive_frontend_log(log_entry: FrontendLogEntry):
    """Receive a single frontend log entry"""
    try:
        # Log to backend logger
        backend_message = f"[FRONTEND] {log_entry.component}: {log_entry.message}"
        
        if log_entry.level.upper() == "ERROR":
            logger.error(backend_message, "FrontendLogs", {
                "sessionId": log_entry.sessionId,
                "url": log_entry.url,
                "data": log_entry.data,
                "browser": log_entry.browser
            })
        elif log_entry.level.upper() == "WARN":
            logger.warning(backend_message, "FrontendLogs", {
                "sessionId": log_entry.sessionId,
                "url": log_entry.url,
                "data": log_entry.data
            })
        else:
            logger.info(backend_message, "FrontendLogs", {
                "sessionId": log_entry.sessionId,
                "url": log_entry.url
            })
        
        # Write to file
        write_log_to_file(log_entry)
        
        return {"success": True, "message": "Log received"}
        
    except Exception as e:
        logger.error(f"Failed to process frontend log: {e}", "FrontendLogs")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process log entry"
        )

@router.post("/frontend/batch")
async def receive_frontend_log_batch(log_batch: FrontendLogBatch):
    """Receive multiple frontend log entries"""
    try:
        processed_count = 0
        
        for log_entry in log_batch.logs:
            try:
                # Process each log entry
                backend_message = f"[FRONTEND] {log_entry.component}: {log_entry.message}"
                
                if log_entry.level.upper() == "ERROR":
                    logger.error(backend_message, "FrontendLogs", {
                        "sessionId": log_entry.sessionId,
                        "url": log_entry.url,
                        "data": log_entry.data
                    })
                elif log_entry.level.upper() == "WARN":
                    logger.warning(backend_message, "FrontendLogs", {
                        "sessionId": log_entry.sessionId,
                        "url": log_entry.url
                    })
                
                # Write to file
                write_log_to_file(log_entry)
                processed_count += 1
                
            except Exception as e:
                logger.error(f"Failed to process log entry in batch: {e}", "FrontendLogs")
                continue
        
        return {
            "success": True, 
            "message": f"Processed {processed_count} of {len(log_batch.logs)} log entries"
        }
        
    except Exception as e:
        logger.error(f"Failed to process frontend log batch: {e}", "FrontendLogs")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process log batch"
        )

@router.get("/frontend/health")
async def frontend_logs_health():
    """Health check for frontend logging endpoint"""
    return {
        "status": "healthy",
        "service": "frontend-logs",
        "timestamp": datetime.now().isoformat(),
        "logs_directory": FRONTEND_LOGS_DIR,
        "directory_exists": os.path.exists(FRONTEND_LOGS_DIR)
    }

@router.get("/frontend/stats")
async def get_frontend_log_stats():
    """Get statistics about frontend logs"""
    try:
        stats = {
            "total_files": 0,
            "files_by_level": {},
            "latest_logs": []
        }
        
        if os.path.exists(FRONTEND_LOGS_DIR):
            files = os.listdir(FRONTEND_LOGS_DIR)
            stats["total_files"] = len(files)
            
            for file in files:
                if file.endswith('.log'):
                    level = file.split('_')[-1].replace('.log', '')
                    stats["files_by_level"][level] = stats["files_by_level"].get(level, 0) + 1
        
        return stats
        
    except Exception as e:
        logger.error(f"Failed to get frontend log stats: {e}", "FrontendLogs")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get log statistics"
        )
