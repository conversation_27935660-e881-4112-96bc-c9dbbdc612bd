"""
AI Database Query Functions for RestroManage
Provides intelligent database querying capabilities for the AI assistant
with proper multi-tenant restaurant ID filtering and optimized SQL queries.
"""

import asyncio
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta, date
from sqlalchemy import select, func, and_, or_, desc, asc, text
from sqlalchemy.orm import selectinload
from app.database import get_db_session_context
from app.models.database_models import (
    Restaurant, MenuItem, Order, OrderItem, InventoryItem, 
    Table, RestaurantUser, PromoCode, PromoCodeUsage
)
from app.utils.logging_config import logger
import json


class AIDataQueryService:
    """Service for AI-driven database queries with multi-tenant support"""
    
    def __init__(self):
        self.logger = logger
    
    async def get_daily_sales(
        self, 
        restaurant_id: str, 
        target_date: Optional[str] = None,
        include_items: bool = True
    ) -> Dict[str, Any]:
        """
        Get daily sales data for a specific restaurant and date.
        
        Args:
            restaurant_id: Restaurant ID for multi-tenant filtering
            target_date: Date in YYYY-MM-DD format (defaults to today)
            include_items: Whether to include item-level breakdown
            
        Returns:
            Dictionary with sales metrics and item breakdown
        """
        try:
            if target_date:
                query_date = datetime.strptime(target_date, "%Y-%m-%d").date()
            else:
                query_date = date.today()
            
            async with get_db_session_context() as session:
                # Get orders for the specific date and restaurant
                query = select(Order).where(
                    and_(
                        Order.restaurant_id == restaurant_id,
                        func.date(Order.created_at) == query_date,
                        Order.status.in_(['completed', 'paid'])
                    )
                )
                
                if include_items:
                    query = query.options(selectinload(Order.items))
                
                result = await session.execute(query)
                orders = result.scalars().all()
                
                # Calculate metrics
                total_revenue = sum(order.total for order in orders)
                total_orders = len(orders)
                avg_order_value = total_revenue / total_orders if total_orders > 0 else 0
                
                # Item breakdown if requested
                item_breakdown = {}
                if include_items:
                    for order in orders:
                        for item in order.items:
                            item_name = item.name
                            if item_name not in item_breakdown:
                                item_breakdown[item_name] = {
                                    'quantity': 0,
                                    'revenue': 0,
                                    'orders': 0
                                }
                            item_breakdown[item_name]['quantity'] += item.quantity
                            item_breakdown[item_name]['revenue'] += item.subtotal
                            item_breakdown[item_name]['orders'] += 1
                
                return {
                    'date': query_date.isoformat(),
                    'restaurant_id': restaurant_id,
                    'total_revenue': round(total_revenue, 2),
                    'total_orders': total_orders,
                    'average_order_value': round(avg_order_value, 2),
                    'item_breakdown': item_breakdown,
                    'success': True
                }
                
        except Exception as e:
            self.logger.error("Error getting daily sales", "AIDataQuery", {
                'restaurant_id': restaurant_id,
                'target_date': target_date,
                'error': str(e)
            })
            return {
                'error': str(e),
                'success': False
            }
    
    async def get_inventory_levels(
        self, 
        restaurant_id: str,
        item_name: Optional[str] = None,
        low_stock_only: bool = False
    ) -> Dict[str, Any]:
        """
        Get current inventory levels for a restaurant.
        
        Args:
            restaurant_id: Restaurant ID for multi-tenant filtering
            item_name: Specific item name to query (optional)
            low_stock_only: Only return items below reorder level
            
        Returns:
            Dictionary with inventory data
        """
        try:
            async with get_db_session_context() as session:
                query = select(InventoryItem).where(
                    InventoryItem.restaurant_id == restaurant_id
                )
                
                if item_name:
                    query = query.where(
                        InventoryItem.name.ilike(f"%{item_name}%")
                    )
                
                if low_stock_only:
                    query = query.where(
                        InventoryItem.quantity <= InventoryItem.reorder_level
                    )
                
                result = await session.execute(query)
                inventory_items = result.scalars().all()
                
                items_data = []
                total_value = 0
                low_stock_count = 0
                
                for item in inventory_items:
                    item_value = item.quantity * item.price_per_unit
                    total_value += item_value
                    
                    is_low_stock = item.quantity <= item.reorder_level
                    if is_low_stock:
                        low_stock_count += 1
                    
                    items_data.append({
                        'name': item.name,
                        'quantity': item.quantity,
                        'unit': item.unit,
                        'reorder_level': item.reorder_level,
                        'price_per_unit': item.price_per_unit,
                        'total_value': round(item_value, 2),
                        'category': item.category,
                        'supplier': item.supplier,
                        'is_low_stock': is_low_stock,
                        'last_restocked': item.last_restocked.isoformat() if item.last_restocked else None
                    })
                
                return {
                    'restaurant_id': restaurant_id,
                    'total_items': len(items_data),
                    'total_inventory_value': round(total_value, 2),
                    'low_stock_items': low_stock_count,
                    'items': items_data,
                    'success': True
                }
                
        except Exception as e:
            self.logger.error("Error getting inventory levels", "AIDataQuery", {
                'restaurant_id': restaurant_id,
                'item_name': item_name,
                'error': str(e)
            })
            return {
                'error': str(e),
                'success': False
            }


    async def get_customer_analytics(
        self,
        restaurant_id: str,
        period_days: int = 30,
        include_trends: bool = True
    ) -> Dict[str, Any]:
        """
        Get customer analytics and behavior patterns.

        Args:
            restaurant_id: Restaurant ID for multi-tenant filtering
            period_days: Number of days to analyze (default 30)
            include_trends: Whether to include trend analysis

        Returns:
            Dictionary with customer analytics
        """
        try:
            start_date = datetime.now() - timedelta(days=period_days)

            async with get_db_session_context() as session:
                # Get orders within the period
                query = select(Order).where(
                    and_(
                        Order.restaurant_id == restaurant_id,
                        Order.created_at >= start_date,
                        Order.status.in_(['completed', 'paid'])
                    )
                ).options(selectinload(Order.items))

                result = await session.execute(query)
                orders = result.scalars().all()

                # Analyze customer patterns
                customer_data = {}
                total_revenue = 0
                order_times = []

                for order in orders:
                    customer_name = order.customer_name or "Anonymous"
                    order_hour = order.created_at.hour
                    order_times.append(order_hour)
                    total_revenue += order.total

                    if customer_name not in customer_data:
                        customer_data[customer_name] = {
                            'orders': 0,
                            'total_spent': 0,
                            'avg_order_value': 0,
                            'favorite_items': {}
                        }

                    customer_data[customer_name]['orders'] += 1
                    customer_data[customer_name]['total_spent'] += order.total

                    # Track favorite items
                    for item in order.items:
                        item_name = item.name
                        if item_name not in customer_data[customer_name]['favorite_items']:
                            customer_data[customer_name]['favorite_items'][item_name] = 0
                        customer_data[customer_name]['favorite_items'][item_name] += item.quantity

                # Calculate averages and segments
                for customer in customer_data.values():
                    customer['avg_order_value'] = customer['total_spent'] / customer['orders']

                # Peak hours analysis
                hour_counts = {}
                for hour in order_times:
                    hour_counts[hour] = hour_counts.get(hour, 0) + 1

                peak_hours = sorted(hour_counts.items(), key=lambda x: x[1], reverse=True)[:3]

                # Customer segments
                high_value_customers = [
                    name for name, data in customer_data.items()
                    if data['total_spent'] > (total_revenue / len(customer_data)) * 1.5
                ]

                return {
                    'restaurant_id': restaurant_id,
                    'analysis_period_days': period_days,
                    'total_customers': len(customer_data),
                    'total_orders': len(orders),
                    'total_revenue': round(total_revenue, 2),
                    'avg_revenue_per_customer': round(total_revenue / len(customer_data), 2) if customer_data else 0,
                    'peak_hours': [{'hour': hour, 'orders': count} for hour, count in peak_hours],
                    'high_value_customers': len(high_value_customers),
                    'customer_segments': {
                        'high_value': len(high_value_customers),
                        'regular': len(customer_data) - len(high_value_customers),
                        'total': len(customer_data)
                    },
                    'success': True
                }

        except Exception as e:
            self.logger.error("Error getting customer analytics", "AIDataQuery", {
                'restaurant_id': restaurant_id,
                'period_days': period_days,
                'error': str(e)
            })
            return {
                'error': str(e),
                'success': False
            }

    async def get_menu_performance(
        self,
        restaurant_id: str,
        period_days: int = 30,
        sort_by: str = 'revenue'
    ) -> Dict[str, Any]:
        """
        Get menu item performance analytics.

        Args:
            restaurant_id: Restaurant ID for multi-tenant filtering
            period_days: Number of days to analyze
            sort_by: Sort criteria ('revenue', 'quantity', 'orders')

        Returns:
            Dictionary with menu performance data
        """
        try:
            start_date = datetime.now() - timedelta(days=period_days)

            async with get_db_session_context() as session:
                # Get menu items and their order data
                menu_query = select(MenuItem).where(
                    MenuItem.restaurant_id == restaurant_id
                )
                menu_result = await session.execute(menu_query)
                menu_items = {item.id: item for item in menu_result.scalars().all()}

                # Get order items within the period
                order_items_query = select(OrderItem).join(Order).where(
                    and_(
                        Order.restaurant_id == restaurant_id,
                        Order.created_at >= start_date,
                        Order.status.in_(['completed', 'paid'])
                    )
                )
                order_items_result = await session.execute(order_items_query)
                order_items = order_items_result.scalars().all()

                # Analyze performance
                item_performance = {}
                total_revenue = 0
                total_quantity = 0

                for order_item in order_items:
                    item_id = order_item.menu_item_id
                    item_name = order_item.name

                    if item_name not in item_performance:
                        menu_item = menu_items.get(item_id)
                        item_performance[item_name] = {
                            'menu_item_id': item_id,
                            'name': item_name,
                            'category': menu_item.category if menu_item else 'Unknown',
                            'current_price': menu_item.price if menu_item else 0,
                            'total_quantity': 0,
                            'total_revenue': 0,
                            'order_count': 0,
                            'avg_quantity_per_order': 0
                        }

                    item_performance[item_name]['total_quantity'] += order_item.quantity
                    item_performance[item_name]['total_revenue'] += order_item.subtotal
                    item_performance[item_name]['order_count'] += 1

                    total_revenue += order_item.subtotal
                    total_quantity += order_item.quantity

                # Calculate averages and sort
                for item_data in item_performance.values():
                    item_data['avg_quantity_per_order'] = (
                        item_data['total_quantity'] / item_data['order_count']
                    )
                    item_data['revenue_percentage'] = (
                        (item_data['total_revenue'] / total_revenue * 100) if total_revenue > 0 else 0
                    )

                # Sort by specified criteria
                if sort_by == 'revenue':
                    sorted_items = sorted(
                        item_performance.values(),
                        key=lambda x: x['total_revenue'],
                        reverse=True
                    )
                elif sort_by == 'quantity':
                    sorted_items = sorted(
                        item_performance.values(),
                        key=lambda x: x['total_quantity'],
                        reverse=True
                    )
                else:  # orders
                    sorted_items = sorted(
                        item_performance.values(),
                        key=lambda x: x['order_count'],
                        reverse=True
                    )

                return {
                    'restaurant_id': restaurant_id,
                    'analysis_period_days': period_days,
                    'total_menu_items': len(menu_items),
                    'items_sold': len(item_performance),
                    'total_revenue': round(total_revenue, 2),
                    'total_quantity_sold': total_quantity,
                    'top_performers': sorted_items[:10],
                    'all_items': sorted_items,
                    'success': True
                }

        except Exception as e:
            self.logger.error("Error getting menu performance", "AIDataQuery", {
                'restaurant_id': restaurant_id,
                'period_days': period_days,
                'error': str(e)
            })
            return {
                'error': str(e),
                'success': False
            }


    async def get_operational_insights(
        self,
        restaurant_id: str,
        period_days: int = 7
    ) -> Dict[str, Any]:
        """
        Get operational insights including table utilization, staff performance, etc.

        Args:
            restaurant_id: Restaurant ID for multi-tenant filtering
            period_days: Number of days to analyze

        Returns:
            Dictionary with operational insights
        """
        try:
            start_date = datetime.now() - timedelta(days=period_days)

            async with get_db_session_context() as session:
                # Get tables
                tables_query = select(Table).where(
                    Table.restaurant_id == restaurant_id
                )
                tables_result = await session.execute(tables_query)
                tables = {table.id: table for table in tables_result.scalars().all()}

                # Get orders within period
                orders_query = select(Order).where(
                    and_(
                        Order.restaurant_id == restaurant_id,
                        Order.created_at >= start_date
                    )
                )
                orders_result = await session.execute(orders_query)
                orders = orders_result.scalars().all()

                # Analyze table utilization
                table_usage = {}
                order_status_counts = {}
                hourly_distribution = {}

                for order in orders:
                    # Table usage
                    if order.table_id and order.table_id in tables:
                        table_num = tables[order.table_id].number
                        if table_num not in table_usage:
                            table_usage[table_num] = 0
                        table_usage[table_num] += 1

                    # Order status distribution
                    status = order.status
                    order_status_counts[status] = order_status_counts.get(status, 0) + 1

                    # Hourly distribution
                    hour = order.created_at.hour
                    hourly_distribution[hour] = hourly_distribution.get(hour, 0) + 1

                # Calculate utilization rates
                total_tables = len(tables)
                total_orders = len(orders)
                avg_orders_per_table = total_orders / total_tables if total_tables > 0 else 0

                # Find peak hours
                peak_hours = sorted(
                    hourly_distribution.items(),
                    key=lambda x: x[1],
                    reverse=True
                )[:3]

                return {
                    'restaurant_id': restaurant_id,
                    'analysis_period_days': period_days,
                    'total_tables': total_tables,
                    'total_orders': total_orders,
                    'avg_orders_per_table': round(avg_orders_per_table, 2),
                    'table_utilization': table_usage,
                    'order_status_distribution': order_status_counts,
                    'peak_hours': [{'hour': hour, 'orders': count} for hour, count in peak_hours],
                    'hourly_distribution': hourly_distribution,
                    'success': True
                }

        except Exception as e:
            self.logger.error("Error getting operational insights", "AIDataQuery", {
                'restaurant_id': restaurant_id,
                'period_days': period_days,
                'error': str(e)
            })
            return {
                'error': str(e),
                'success': False
            }

    async def get_revenue_trends(
        self,
        restaurant_id: str,
        period_days: int = 30,
        group_by: str = 'day'
    ) -> Dict[str, Any]:
        """
        Get revenue trends over time.

        Args:
            restaurant_id: Restaurant ID for multi-tenant filtering
            period_days: Number of days to analyze
            group_by: Grouping period ('day', 'week', 'month')

        Returns:
            Dictionary with revenue trend data
        """
        try:
            start_date = datetime.now() - timedelta(days=period_days)

            async with get_db_session_context() as session:
                # Get orders within period
                query = select(Order).where(
                    and_(
                        Order.restaurant_id == restaurant_id,
                        Order.created_at >= start_date,
                        Order.status.in_(['completed', 'paid'])
                    )
                ).order_by(Order.created_at)

                result = await session.execute(query)
                orders = result.scalars().all()

                # Group by specified period
                revenue_data = {}

                for order in orders:
                    if group_by == 'day':
                        key = order.created_at.date().isoformat()
                    elif group_by == 'week':
                        week_start = order.created_at.date() - timedelta(days=order.created_at.weekday())
                        key = week_start.isoformat()
                    else:  # month
                        key = order.created_at.strftime('%Y-%m')

                    if key not in revenue_data:
                        revenue_data[key] = {
                            'revenue': 0,
                            'orders': 0,
                            'avg_order_value': 0
                        }

                    revenue_data[key]['revenue'] += order.total
                    revenue_data[key]['orders'] += 1

                # Calculate averages
                for data in revenue_data.values():
                    data['avg_order_value'] = data['revenue'] / data['orders']
                    data['revenue'] = round(data['revenue'], 2)
                    data['avg_order_value'] = round(data['avg_order_value'], 2)

                # Calculate trends
                sorted_periods = sorted(revenue_data.items())
                total_revenue = sum(data['revenue'] for data in revenue_data.values())
                total_orders = sum(data['orders'] for data in revenue_data.values())

                # Growth calculation (if we have at least 2 periods)
                growth_rate = 0
                if len(sorted_periods) >= 2:
                    first_period_revenue = sorted_periods[0][1]['revenue']
                    last_period_revenue = sorted_periods[-1][1]['revenue']
                    if first_period_revenue > 0:
                        growth_rate = ((last_period_revenue - first_period_revenue) / first_period_revenue) * 100

                return {
                    'restaurant_id': restaurant_id,
                    'analysis_period_days': period_days,
                    'group_by': group_by,
                    'total_revenue': round(total_revenue, 2),
                    'total_orders': total_orders,
                    'avg_order_value': round(total_revenue / total_orders, 2) if total_orders > 0 else 0,
                    'growth_rate_percent': round(growth_rate, 2),
                    'revenue_by_period': dict(sorted_periods),
                    'success': True
                }

        except Exception as e:
            self.logger.error("Error getting revenue trends", "AIDataQuery", {
                'restaurant_id': restaurant_id,
                'period_days': period_days,
                'error': str(e)
            })
            return {
                'error': str(e),
                'success': False
            }


# Global instance
ai_data_service = AIDataQueryService()
