import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";

// https://vitejs.dev/config/
export default defineConfig({
  server: {
    host: "0.0.0.0", // Allow external connections
    port: 5175, // Use 5175 temporarily to avoid conflicts
    strictPort: false, // Allow fallback to other ports
    proxy: {
      "/api": "http://localhost:5002",  // Point to FastAPI backend on correct port (5002)
      "/health": "http://localhost:5002"  // Health check endpoint
    },
    cors: true, // Enable CORS for all origins
    hmr: {
      host: "localhost", // Use localhost instead of true
      port: 5177
    },
    // Configure headers to allow necessary JavaScript execution
    headers: {
      "Content-Security-Policy": "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.gpteng.co; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: https:; connect-src 'self' http://localhost:5001 http://localhost:5177 http://localhost:5175 http://localhost:5174 http://localhost:5173 http://localhost:5176 ws://localhost:5177 ws://localhost:5175 ws://localhost:5174 ws://localhost:5173 ws://localhost:5176; font-src 'self' data:;"
    }
  },
  preview: {
    host: 'localhost', // Use localhost consistently
    port: 4174, // Update preview port to match dev server pattern
    strictPort: false,
    proxy: { "/api": "http://localhost:5002", "/health": "http://localhost:5002" },
    cors: true,
  },
  plugins: [
    react()
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          // Separate AI components into their own chunk for lazy loading
          'ai-components': [
            './src/components/ai/MinimalAIChat.tsx',
            './src/components/ai/SimpleAIWrapper.tsx',
            './src/components/ai/UltraMinimalChat.tsx',
            './src/services/lightweightAI.ts'
          ],
          // Keep vendor libraries separate
          'vendor': ['react', 'react-dom'],
          'ui-components': [
            '@radix-ui/react-dialog',
            '@radix-ui/react-avatar',
            '@radix-ui/react-scroll-area',
            '@radix-ui/react-toast'
          ]
        }
      }
    },
    // Optimize chunk size
    chunkSizeWarningLimit: 1000,
    // Enable source maps for debugging
    sourcemap: process.env.NODE_ENV === 'development'
  },
  // Performance optimizations
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'lucide-react'
    ],
    exclude: [
      // Exclude AI components from pre-bundling to enable lazy loading
      './src/components/ai/MinimalAIChat.tsx',
      './src/components/ai/SimpleAIWrapper.tsx',
      './src/components/ai/UltraMinimalChat.tsx'
    ]
  }
});
