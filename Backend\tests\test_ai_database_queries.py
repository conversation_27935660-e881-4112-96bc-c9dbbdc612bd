"""
Unit tests for AI Database Query Service
Tests the database query functions used by the AI assistant
"""

import pytest
import asyncio
from datetime import datetime, date, timedelta
from unittest.mock import AsyncMock, MagicMock, patch
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.ai_database_queries import AIDataQueryService, ai_data_service
from app.models.database_models import Restaurant, Order, OrderItem, MenuItem, InventoryItem


class TestAIDataQueryService:
    """Test suite for AI Data Query Service"""
    
    @pytest.fixture
    def service(self):
        """Create service instance for testing"""
        return AIDataQueryService()
    
    @pytest.fixture
    def mock_session(self):
        """Mock database session"""
        session = AsyncMock(spec=AsyncSession)
        return session
    
    @pytest.fixture
    def sample_restaurant_id(self):
        """Sample restaurant ID for testing"""
        return "restaurant_123"
    
    @pytest.fixture
    def sample_orders(self):
        """Sample order data for testing"""
        return [
            MagicMock(
                id="order_1",
                restaurant_id="restaurant_123",
                total=25.50,
                status="completed",
                created_at=datetime.now(),
                items=[
                    MagicMock(name="Burger", quantity=2, subtotal=20.00),
                    MagicMock(name="Fries", quantity=1, subtotal=5.50)
                ]
            ),
            MagicMock(
                id="order_2",
                restaurant_id="restaurant_123",
                total=15.75,
                status="completed",
                created_at=datetime.now(),
                items=[
                    MagicMock(name="Salad", quantity=1, subtotal=15.75)
                ]
            )
        ]
    
    @pytest.mark.asyncio
    async def test_get_daily_sales_success(self, service, sample_restaurant_id, sample_orders):
        """Test successful daily sales retrieval"""
        with patch('app.services.ai_database_queries.get_db_session_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session
            
            # Mock query result
            mock_result = AsyncMock()
            mock_result.scalars.return_value.all.return_value = sample_orders
            mock_session.execute.return_value = mock_result
            
            # Test the function
            result = await service.get_daily_sales(
                restaurant_id=sample_restaurant_id,
                target_date="2024-01-15",
                include_items=True
            )
            
            # Assertions
            assert result["success"] is True
            assert result["restaurant_id"] == sample_restaurant_id
            assert result["total_revenue"] == 41.25
            assert result["total_orders"] == 2
            assert result["average_order_value"] == 20.625
            assert "item_breakdown" in result
            assert "Burger" in result["item_breakdown"]
    
    @pytest.mark.asyncio
    async def test_get_daily_sales_no_orders(self, service, sample_restaurant_id):
        """Test daily sales with no orders"""
        with patch('app.services.ai_database_queries.get_db_session_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session
            
            # Mock empty result
            mock_result = AsyncMock()
            mock_result.scalars.return_value.all.return_value = []
            mock_session.execute.return_value = mock_result
            
            result = await service.get_daily_sales(
                restaurant_id=sample_restaurant_id,
                target_date="2024-01-15"
            )
            
            assert result["success"] is True
            assert result["total_revenue"] == 0
            assert result["total_orders"] == 0
            assert result["average_order_value"] == 0
    
    @pytest.mark.asyncio
    async def test_get_daily_sales_error(self, service, sample_restaurant_id):
        """Test daily sales with database error"""
        with patch('app.services.ai_database_queries.get_db_session_context') as mock_context:
            mock_context.side_effect = Exception("Database connection failed")
            
            result = await service.get_daily_sales(
                restaurant_id=sample_restaurant_id,
                target_date="2024-01-15"
            )
            
            assert result["success"] is False
            assert "error" in result
            assert "Database connection failed" in result["error"]
    
    @pytest.mark.asyncio
    async def test_get_inventory_levels_success(self, service, sample_restaurant_id):
        """Test successful inventory levels retrieval"""
        sample_inventory = [
            MagicMock(
                name="Beef Patty",
                quantity=50,
                unit="pieces",
                reorder_level=10,
                price_per_unit=2.0,
                category="meat",
                supplier="Local Butcher",
                last_restocked=datetime.now()
            ),
            MagicMock(
                name="Lettuce",
                quantity=5,  # Below reorder level
                unit="kg",
                reorder_level=10,
                price_per_unit=3.0,
                category="vegetable",
                supplier="Fresh Farms",
                last_restocked=datetime.now()
            )
        ]
        
        with patch('app.services.ai_database_queries.get_db_session_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session
            
            mock_result = AsyncMock()
            mock_result.scalars.return_value.all.return_value = sample_inventory
            mock_session.execute.return_value = mock_result
            
            result = await service.get_inventory_levels(
                restaurant_id=sample_restaurant_id,
                low_stock_only=False
            )
            
            assert result["success"] is True
            assert result["total_items"] == 2
            assert result["low_stock_items"] == 1
            assert result["total_inventory_value"] == 115.0  # (50*2) + (5*3)
    
    @pytest.mark.asyncio
    async def test_get_customer_analytics_success(self, service, sample_restaurant_id, sample_orders):
        """Test successful customer analytics retrieval"""
        # Add customer names to orders
        sample_orders[0].customer_name = "John Doe"
        sample_orders[1].customer_name = "Jane Smith"
        
        with patch('app.services.ai_database_queries.get_db_session_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session
            
            mock_result = AsyncMock()
            mock_result.scalars.return_value.all.return_value = sample_orders
            mock_session.execute.return_value = mock_result
            
            result = await service.get_customer_analytics(
                restaurant_id=sample_restaurant_id,
                period_days=30
            )
            
            assert result["success"] is True
            assert result["total_customers"] == 2
            assert result["total_orders"] == 2
            assert result["total_revenue"] == 41.25
            assert "peak_hours" in result
            assert "customer_segments" in result
    
    @pytest.mark.asyncio
    async def test_get_menu_performance_success(self, service, sample_restaurant_id):
        """Test successful menu performance retrieval"""
        sample_menu_items = {
            "item_1": MagicMock(id="item_1", name="Burger", category="main", price=10.0),
            "item_2": MagicMock(id="item_2", name="Fries", category="side", price=5.5)
        }
        
        sample_order_items = [
            MagicMock(
                menu_item_id="item_1",
                name="Burger",
                quantity=3,
                subtotal=30.0
            ),
            MagicMock(
                menu_item_id="item_2",
                name="Fries",
                quantity=2,
                subtotal=11.0
            )
        ]
        
        with patch('app.services.ai_database_queries.get_db_session_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session
            
            # Mock menu items query
            menu_result = AsyncMock()
            menu_result.scalars.return_value.all.return_value = list(sample_menu_items.values())
            
            # Mock order items query
            order_items_result = AsyncMock()
            order_items_result.scalars.return_value.all.return_value = sample_order_items
            
            mock_session.execute.side_effect = [menu_result, order_items_result]
            
            result = await service.get_menu_performance(
                restaurant_id=sample_restaurant_id,
                period_days=30,
                sort_by="revenue"
            )
            
            assert result["success"] is True
            assert result["total_revenue"] == 41.0
            assert result["total_quantity_sold"] == 5
            assert len(result["top_performers"]) <= 10
            assert result["top_performers"][0]["name"] == "Burger"  # Should be sorted by revenue
    
    @pytest.mark.asyncio
    async def test_get_operational_insights_success(self, service, sample_restaurant_id, sample_orders):
        """Test successful operational insights retrieval"""
        sample_tables = {
            "table_1": MagicMock(id="table_1", number=1),
            "table_2": MagicMock(id="table_2", number=2)
        }
        
        # Add table IDs to orders
        sample_orders[0].table_id = "table_1"
        sample_orders[1].table_id = "table_2"
        
        with patch('app.services.ai_database_queries.get_db_session_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session
            
            # Mock tables query
            tables_result = AsyncMock()
            tables_result.scalars.return_value.all.return_value = list(sample_tables.values())
            
            # Mock orders query
            orders_result = AsyncMock()
            orders_result.scalars.return_value.all.return_value = sample_orders
            
            mock_session.execute.side_effect = [tables_result, orders_result]
            
            result = await service.get_operational_insights(
                restaurant_id=sample_restaurant_id,
                period_days=7
            )
            
            assert result["success"] is True
            assert result["total_tables"] == 2
            assert result["total_orders"] == 2
            assert result["avg_orders_per_table"] == 1.0
            assert "table_utilization" in result
            assert "peak_hours" in result
    
    @pytest.mark.asyncio
    async def test_get_revenue_trends_success(self, service, sample_restaurant_id, sample_orders):
        """Test successful revenue trends retrieval"""
        with patch('app.services.ai_database_queries.get_db_session_context') as mock_context:
            mock_session = AsyncMock()
            mock_context.return_value.__aenter__.return_value = mock_session
            
            mock_result = AsyncMock()
            mock_result.scalars.return_value.all.return_value = sample_orders
            mock_session.execute.return_value = mock_result
            
            result = await service.get_revenue_trends(
                restaurant_id=sample_restaurant_id,
                period_days=30,
                group_by="day"
            )
            
            assert result["success"] is True
            assert result["total_revenue"] == 41.25
            assert result["total_orders"] == 2
            assert "revenue_by_period" in result
            assert "growth_rate_percent" in result


class TestAIDataServiceIntegration:
    """Integration tests for AI Data Service"""
    
    @pytest.mark.asyncio
    async def test_service_initialization(self):
        """Test that the global service instance is properly initialized"""
        assert ai_data_service is not None
        assert isinstance(ai_data_service, AIDataQueryService)
        assert hasattr(ai_data_service, 'logger')
    
    @pytest.mark.asyncio
    async def test_all_methods_exist(self):
        """Test that all required methods exist on the service"""
        required_methods = [
            'get_daily_sales',
            'get_inventory_levels',
            'get_customer_analytics',
            'get_menu_performance',
            'get_operational_insights',
            'get_revenue_trends'
        ]
        
        for method_name in required_methods:
            assert hasattr(ai_data_service, method_name)
            assert callable(getattr(ai_data_service, method_name))


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
