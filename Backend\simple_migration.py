"""
Simple migration script to transfer data from JSON to SQL database.
This script directly inserts data without using the repository layer.
"""

import json
import asyncio
import os
from datetime import datetime
import logging
from sqlalchemy import text

from app.database import engine, AsyncSessionLocal
from app.models.database_models import Base, Restaurant, RestaurantUser

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def create_tables():
    """Create all database tables"""
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    logger.info("Database tables created successfully")

async def load_json_data():
    """Load data from JSON file"""
    try:
        with open("data.json", "r", encoding="utf-8") as f:
            data = json.load(f)
        logger.info(f"Loaded JSON data with {len(data.get('restaurants', []))} restaurants and {len(data.get('users', []))} users")
        return data
    except FileNotFoundError:
        logger.error("data.json file not found")
        return None
    except json.JSONDecodeError as e:
        logger.error(f"Error parsing JSON: {e}")
        return None

async def migrate_restaurants(restaurants_data):
    """Migrate restaurant data"""
    async with AsyncSessionLocal() as db:
        migrated_count = 0
        for restaurant_data in restaurants_data:
            try:
                # Convert JSON data to database format
                restaurant = Restaurant(
                    id=restaurant_data.get("id"),
                    code=restaurant_data.get("code"),
                    name=restaurant_data.get("name"),
                    logo=restaurant_data.get("logo"),
                    address=restaurant_data.get("address"),
                    phone=restaurant_data.get("phone"),
                    email=restaurant_data.get("email"),
                    vat_rate=restaurant_data.get("vatRate", 20.0),
                    currency=restaurant_data.get("currency", "GBP"),
                    is_active=restaurant_data.get("isActive", True),
                    owner_name=restaurant_data.get("ownerName"),
                    business_license_number=restaurant_data.get("businessLicenseNumber"),
                    restaurant_type=restaurant_data.get("restaurantType", "restaurant"),
                    password=restaurant_data.get("password"),  # TODO: Hash passwords
                    setup_data=restaurant_data.get("setupData"),
                )
                
                db.add(restaurant)
                await db.commit()
                migrated_count += 1
                logger.info(f"Migrated restaurant: {restaurant.name}")
                
            except Exception as e:
                logger.error(f"Error migrating restaurant {restaurant_data.get('name', 'Unknown')}: {e}")
                await db.rollback()
        
        logger.info(f"Successfully migrated {migrated_count} restaurants")

async def migrate_users(users_data):
    """Migrate user data"""
    async with AsyncSessionLocal() as db:
        migrated_count = 0
        for user_data in users_data:
            try:
                # Convert JSON data to database format
                user = RestaurantUser(
                    id=user_data.get("id"),
                    restaurant_id=user_data.get("restaurant_id"),
                    name=user_data.get("name"),
                    email=user_data.get("email"),
                    phone=user_data.get("phone"),
                    role=user_data.get("role"),
                    position=user_data.get("position"),
                    pin=user_data.get("pin"),
                    status=user_data.get("status", "active"),
                    hire_date=user_data.get("hireDate"),
                    performance=user_data.get("performance", 100),
                    access_level=user_data.get("accessLevel", "limited"),
                )
                
                db.add(user)
                await db.commit()
                migrated_count += 1
                logger.info(f"Migrated user: {user.name}")
                
            except Exception as e:
                logger.error(f"Error migrating user {user_data.get('name', 'Unknown')}: {e}")
                await db.rollback()
        
        logger.info(f"Successfully migrated {migrated_count} users")

async def verify_migration():
    """Verify that data was migrated successfully"""
    async with AsyncSessionLocal() as db:
        # Count restaurants
        result = await db.execute(text("SELECT COUNT(*) FROM restaurants"))
        restaurant_count = result.scalar()
        
        # Count users
        result = await db.execute(text("SELECT COUNT(*) FROM restaurant_users"))
        user_count = result.scalar()
        
        logger.info(f"Migration verification: {restaurant_count} restaurants, {user_count} users in database")

async def main():
    """Main migration function"""
    try:
        logger.info("Starting simple JSON to SQL migration...")
        
        # Create tables
        await create_tables()
        
        # Load JSON data
        json_data = await load_json_data()
        if not json_data:
            logger.warning("No JSON data found to migrate")
            return
        
        # Migrate data
        await migrate_restaurants(json_data.get("restaurants", []))
        await migrate_users(json_data.get("users", []))
        
        # Verify migration
        await verify_migration()
        
        logger.info("Migration completed successfully!")
        
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
