import React from "react";
import { useState, useEffect } from "react";
import { format, startOfWeek, endOfWeek, eachDayOfInterval, addDays, isSameDay, parseISO } from "date-fns";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ChevronLeft, ChevronRight, Calendar as CalendarIcon } from "lucide-react";
import { StaffUnavailability } from "@/types/staffAvailability";
import { getUnavailabilityForWeek, getUnavailabilityColor } from "@/services/staffAvailabilityService";
import AvailabilityLegend from "./AvailabilityLegend";

interface AvailabilityCalendarWeekViewProps {
  staffData: { id: string; name: string }[];
  onDateSelect?: (date: Date) => void;
}

const AvailabilityCalendarWeekView = ({
  staffData,
  onDateSelect,
}: AvailabilityCalendarWeekViewProps) => {
  const [currentWeekStart, setCurrentWeekStart] = useState<Date>(startOfWeek(new Date(), { weekStartsOn: 1 }));
  const [unavailability, setUnavailability] = useState<StaffUnavailability[]>([]);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);

  // Calculate the end of the week
  const currentWeekEnd = endOfWeek(currentWeekStart, { weekStartsOn: 1 });
  
  // Generate array of days for the current week
  const weekDays = eachDayOfInterval({
    start: currentWeekStart,
    end: currentWeekEnd
  });

  useEffect(() => {
    // Load unavailability data for the current week
    const weekUnavailability = getUnavailabilityForWeek(currentWeekStart);
    setUnavailability(weekUnavailability);
  }, [currentWeekStart, currentWeekEnd]);

  const handlePreviousWeek = () => {
    setCurrentWeekStart(prevWeekStart => {
      return addDays(prevWeekStart, -7);
    });
  };

  const handleNextWeek = () => {
    setCurrentWeekStart(prevWeekStart => {
      return addDays(prevWeekStart, 7);
    });
  };

  const handleDateClick = (date: Date) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Reset time to start of day
    const selectedDateOnly = new Date(date);
    selectedDateOnly.setHours(0, 0, 0, 0);
    
    // Only allow selection of today or future dates
    if (selectedDateOnly >= today) {
      setSelectedDate(date);
      onDateSelect?.(date);
    }
  };

  // Get unavailability for a specific day
  const getUnavailabilityForDay = (date: Date) => {
    const dateString = format(date, 'yyyy-MM-dd');
    return unavailability.filter(u => u.date.startsWith(dateString));
  };

  // Get staff who are unavailable on a specific day
  const getUnavailableStaffForDay = (date: Date) => {
    const dayUnavailability = getUnavailabilityForDay(date);
    
    return dayUnavailability.map(u => {
      const staff = staffData.find(s => s.id === u.staffId);
      return {
        staffId: u.staffId,
        staffName: staff?.name || 'Unknown',
        type: u.type
      };
    });
  };

  return (
    <div className="w-full">
      <div className="pb-3">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">Weekly Staff Availability</h3>
          <div className="flex items-center gap-1">
            <Button variant="outline" size="icon" onClick={handlePreviousWeek}>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <div className="w-40 text-center">
              {format(currentWeekStart, 'dd/MM/yyyy')} - {format(currentWeekEnd, 'dd/MM/yyyy')}
            </div>
            <Button variant="outline" size="icon" onClick={handleNextWeek}>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-7 gap-2">
            {weekDays.map((day) => {
              const dayUnavailability = getUnavailabilityForDay(day);
              const isSelected = selectedDate ? isSameDay(day, selectedDate) : false;
              
              // Count unavailability by type
              const countByType: Record<string, number> = {};
              dayUnavailability.forEach(u => {
                countByType[u.type] = (countByType[u.type] || 0) + 1;
              });
              
              return (
                <div 
                  key={day.toISOString()}
                  className={`p-2 border rounded-md ${isPastDate ? "cursor-not-allowed opacity-50 bg-gray-100" : "cursor-pointer"} ${
                    isSelected ? 'border-primary bg-primary/10' : 'hover:bg-accent'
                  }`}
                  onClick={() => !isPastDate onClick={() => handleDateClick(day)}onClick={() => handleDateClick(day)} handleDateClick(day)}
                >
                  <div className="text-center">
                    <div className="font-medium">{format(day, 'EEE')}</div>
                    <div className="text-sm">{format(day, 'dd/MM')}</div>
                  </div>
                  
                  {Object.entries(countByType).length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2 justify-center">
                      {Object.entries(countByType).map(([type, count]) => (
                        <Badge
                          key={type}
                          variant="outline"
                          className="text-[10px] h-4 px-1"
                          style={{ 
                            backgroundColor: getUnavailabilityColor(type as any),
                            color: '#fff',
                            borderColor: 'transparent'
                          }}
                        >
                          {count}
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
          
          <AvailabilityLegend className="mt-4" />
          
          {selectedDate && (
            <div className="mt-4 p-3 border rounded-md">
              <h4 className="font-medium flex items-center gap-2 mb-2">
                <CalendarIcon className="h-4 w-4" />
                Staff Unavailable on {format(selectedDate, 'dd/MM/yyyy')}
              </h4>
              
              <div className="space-y-2">
                {getUnavailableStaffForDay(selectedDate).map((staff) => (
                  <div key={staff.staffId} className="flex items-center gap-2">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: getUnavailabilityColor(staff.type as any) }}
                    />
                    <span>{staff.staffName}</span>
                    <Badge variant="outline" className="ml-auto">
                      {staff.type.replace('-', ' ')}
                    </Badge>
                  </div>
                ))}
                
                {getUnavailableStaffForDay(selectedDate).length === 0 && (
                  <p className="text-sm text-muted-foreground">No staff unavailable on this day</p>
                )}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </div>
  );
};

export default AvailabilityCalendarWeekView;
