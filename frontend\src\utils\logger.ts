/**
 * Enhanced Logger utility for comprehensive logging across the Promith application
 */

// Log levels with numeric values for comparison
export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error'
}

// Log level priorities for filtering
const LOG_LEVEL_PRIORITY = {
  [LogLevel.DEBUG]: 0,
  [LogLevel.INFO]: 1,
  [LogLevel.WARN]: 2,
  [LogLevel.ERROR]: 3
};

// Configuration for the logger
interface LoggerConfig {
  enabled: boolean;
  minLevel: LogLevel;
  includeTimestamp: boolean;
  includeComponent: boolean;
  logToConsole: boolean;
  logToStorage: boolean;
  maxStorageLogs: number;
  enableStackTrace: boolean;
}

// Default configuration
const defaultConfig: LoggerConfig = {
  enabled: true,
  minLevel: LogLevel.DEBUG,
  includeTimestamp: true,
  includeComponent: true,
  logToConsole: true,
  logToStorage: true,
  maxStorageLogs: 2000, // Increased for better debugging
  enableStackTrace: true // Enable for better debugging
};

// Current configuration
let config: LoggerConfig = { ...defaultConfig };

// Storage key for logs
const STORAGE_KEY = 'promith_app_logs';

// Component context for automatic component detection
let currentComponent: string | null = null;

/**
 * Set the current component context for logging
 */
const setComponent = (componentName: string): void => {
  currentComponent = componentName;
};

/**
 * Clear the current component context
 */
const clearComponent = (): void => {
  currentComponent = null;
};

/**
 * Get stack trace for debugging
 */
const getStackTrace = (): string => {
  if (!config.enableStackTrace) return '';

  try {
    const stack = new Error().stack;
    return stack ? stack.split('\n').slice(2, 4).join('\n') : '';
  } catch {
    return '';
  }
};

/**
 * Sanitize data to remove sensitive information
 */
const sanitizeData = (data: any): any => {
  if (!data || typeof data !== 'object') return data;

  const sensitiveKeys = ['password', 'pin', 'token', 'secret', 'key', 'auth'];
  const sanitized = { ...data };

  const sanitizeObject = (obj: any): any => {
    if (Array.isArray(obj)) {
      return obj.map(sanitizeObject);
    }

    if (obj && typeof obj === 'object') {
      const result: any = {};
      for (const [key, value] of Object.entries(obj)) {
        if (sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive))) {
          result[key] = '[REDACTED]';
        } else if (typeof value === 'object') {
          result[key] = sanitizeObject(value);
        } else {
          result[key] = value;
        }
      }
      return result;
    }

    return obj;
  };

  return sanitizeObject(sanitized);
};

/**
 * Format a log message with enhanced formatting
 */
const formatLogMessage = (level: LogLevel, message: string, component?: string, data?: any): string => {
  const timestamp = config.includeTimestamp ? `[${new Date().toISOString()}] ` : '';
  const comp = config.includeComponent && (component || currentComponent) ? `[${component || currentComponent}] ` : '';
  const levelStr = `[${level.toUpperCase()}] `;

  let formattedMessage = `${timestamp}${levelStr}${comp}${message}`;

  if (data) {
    const sanitizedData = sanitizeData(data);
    formattedMessage += ` | Data: ${JSON.stringify(sanitizedData, null, 2)}`;
  }

  const stackTrace = getStackTrace();
  if (stackTrace) {
    formattedMessage += `\nStack: ${stackTrace}`;
  }

  return formattedMessage;
};

/**
 * Save log to local storage with rotation and attempt file logging
 */
const saveToStorage = (level: LogLevel, message: string, component?: string, data?: any): void => {
  if (!config.logToStorage) return;

  try {
    // Get existing logs
    const existingLogsString = localStorage.getItem(STORAGE_KEY) || '[]';
    const existingLogs = JSON.parse(existingLogsString);

    // Create new log entry
    const newLog = {
      id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      level,
      message,
      component: component || currentComponent || 'Unknown',
      data: sanitizeData(data),
      url: window.location.href,
      userAgent: navigator.userAgent.substring(0, 100), // Truncated for storage
      sessionId: getSessionId()
    };

    // Add new log and maintain size limit
    const updatedLogs = [newLog, ...existingLogs].slice(0, config.maxStorageLogs);

    // Save back to storage
    localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedLogs));

    // Attempt to send to backend for persistent logging
    sendLogToBackend(newLog);
  } catch (error) {
    console.error('Logger: Failed to save log to storage:', error);
  }
};

/**
 * Get or create session ID for log tracking
 */
const getSessionId = (): string => {
  let sessionId = sessionStorage.getItem('logger_session_id');
  if (!sessionId) {
    sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    sessionStorage.setItem('logger_session_id', sessionId);
  }
  return sessionId;
};

/**
 * Send log to backend for persistent storage
 */
const sendLogToBackend = async (logEntry: any): Promise<void> => {
  try {
    // Only send error and warn logs to backend to avoid spam
    if (logEntry.level !== LogLevel.ERROR && logEntry.level !== LogLevel.WARN) {
      return;
    }

    // Get API base URL
    const apiBaseUrl = import.meta.env.VITE_API_URL || 'http://localhost:5001';

    const response = await fetch(`${apiBaseUrl}/api/logs/frontend`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...logEntry,
        source: 'frontend',
        browser: {
          userAgent: navigator.userAgent,
          language: navigator.language,
          platform: navigator.platform,
          cookieEnabled: navigator.cookieEnabled,
          onLine: navigator.onLine
        },
        screen: {
          width: screen.width,
          height: screen.height,
          colorDepth: screen.colorDepth
        },
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight
        }
      })
    });

    if (!response.ok) {
      console.error(`Logger: Backend logging failed with status ${response.status}:`, await response.text());
    }
  } catch (error) {
    // Silently fail - don't log this error to avoid infinite loops
    console.debug('Logger: Failed to send log to backend:', error);
  }
};

/**
 * Check if log level should be processed
 */
const shouldLog = (level: LogLevel): boolean => {
  if (!config.enabled) return false;
  return LOG_LEVEL_PRIORITY[level] >= LOG_LEVEL_PRIORITY[config.minLevel];
};

/**
 * Core logging function
 */
const log = (level: LogLevel, message: string, component?: string, data?: any): void => {
  if (!shouldLog(level)) return;

  const formattedMessage = formatLogMessage(level, message, component, data);

  // Log to console with appropriate method
  if (config.logToConsole) {
    switch (level) {
      case LogLevel.DEBUG:
        console.debug(formattedMessage);
        break;
      case LogLevel.INFO:
        console.info(formattedMessage);
        break;
      case LogLevel.WARN:
        console.warn(formattedMessage);
        break;
      case LogLevel.ERROR:
        console.error(formattedMessage);
        break;
    }
  }

  // Save to storage
  saveToStorage(level, message, component, data);
};

/**
 * Enhanced Logger API
 */
const logger = {
  // Basic logging methods
  debug: (message: string, component?: string, data?: any) => log(LogLevel.DEBUG, message, component, data),
  info: (message: string, component?: string, data?: any) => log(LogLevel.INFO, message, component, data),
  warn: (message: string, component?: string, data?: any) => log(LogLevel.WARN, message, component, data),
  error: (message: string, component?: string, data?: any) => log(LogLevel.ERROR, message, component, data),

  // Convenience methods for common scenarios
  userAction: (action: string, component?: string, data?: any) =>
    log(LogLevel.INFO, `User Action: ${action}`, component, data),

  apiCall: (endpoint: string, method: string, component?: string, data?: any) =>
    log(LogLevel.DEBUG, `API Call: ${method} ${endpoint}`, component, data),

  formSubmit: (formName: string, component?: string, data?: any) =>
    log(LogLevel.INFO, `Form Submit: ${formName}`, component, data),

  navigation: (from: string, to: string, component?: string) =>
    log(LogLevel.INFO, `Navigation: ${from} → ${to}`, component),

  dataOperation: (operation: string, entity: string, component?: string, data?: any) =>
    log(LogLevel.DEBUG, `Data Operation: ${operation} ${entity}`, component, data),

  validation: (field: string, result: 'success' | 'failure', component?: string, data?: any) =>
    log(LogLevel.DEBUG, `Validation: ${field} - ${result}`, component, data),

  authentication: (action: string, result: 'success' | 'failure', component?: string, data?: any) =>
    log(LogLevel.INFO, `Auth: ${action} - ${result}`, component, data),

  // Component context management
  setComponent,
  clearComponent,
  withComponent: <T>(componentName: string, fn: () => T): T => {
    const previousComponent = currentComponent;
    setComponent(componentName);
    try {
      return fn();
    } finally {
      currentComponent = previousComponent;
    }
  },

  // Storage management
  getLogs: (): any[] => {
    try {
      const logsString = localStorage.getItem(STORAGE_KEY) || '[]';
      return JSON.parse(logsString);
    } catch (error) {
      console.error('Logger: Failed to retrieve logs from storage:', error);
      return [];
    }
  },

  getLogsByLevel: (level: LogLevel): any[] => {
    return logger.getLogs().filter(log => log.level === level);
  },

  getLogsByComponent: (component: string): any[] => {
    return logger.getLogs().filter(log => log.component === component);
  },

  getRecentLogs: (count: number = 50): any[] => {
    return logger.getLogs().slice(0, count);
  },

  clearLogs: (): void => {
    try {
      localStorage.removeItem(STORAGE_KEY);
      logger.info('Logs cleared', 'Logger');
    } catch (error) {
      console.error('Logger: Failed to clear logs from storage:', error);
    }
  },

  exportLogs: (): string => {
    const logs = logger.getLogs();
    return JSON.stringify(logs, null, 2);
  },

  // Configuration
  configure: (newConfig: Partial<LoggerConfig>): void => {
    config = { ...config, ...newConfig };
    logger.info('Logger configuration updated', 'Logger', newConfig);
  },

  getConfig: (): LoggerConfig => ({ ...config }),

  // Performance logging
  time: (label: string, component?: string): void => {
    console.time(label);
    logger.debug(`Timer started: ${label}`, component);
  },

  timeEnd: (label: string, component?: string): void => {
    console.timeEnd(label);
    logger.debug(`Timer ended: ${label}`, component);
  },

  // Error handling helper
  logError: (error: Error | unknown, context: string, component?: string, additionalData?: any): void => {
    const errorData = {
      name: error instanceof Error ? error.name : 'Unknown Error',
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      context,
      ...additionalData
    };

    logger.error(`Error in ${context}`, component, errorData);
  },

  // CSP-specific logging
  logCSPViolation: (violationEvent: any, component?: string): void => {
    const cspData = {
      blockedURI: violationEvent.blockedURI,
      violatedDirective: violationEvent.violatedDirective,
      originalPolicy: violationEvent.originalPolicy,
      sourceFile: violationEvent.sourceFile,
      lineNumber: violationEvent.lineNumber,
      columnNumber: violationEvent.columnNumber,
      sample: violationEvent.sample
    };

    logger.error('CSP Violation detected', component || 'CSP', cspData);
  },

  // Registration flow specific logging
  registrationFlow: {
    stepStart: (step: string, component?: string, data?: any) =>
      logger.info(`Registration Step Started: ${step}`, component || 'Registration', data),

    stepComplete: (step: string, component?: string, data?: any) =>
      logger.info(`Registration Step Completed: ${step}`, component || 'Registration', data),

    stepError: (step: string, error: any, component?: string, data?: any) =>
      logger.error(`Registration Step Failed: ${step}`, component || 'Registration', { error, ...data }),

    credentialsGenerated: (restaurantCode: string, component?: string) =>
      logger.info('Restaurant credentials generated', component || 'Registration', { restaurantCode }),

    credentialsDisplayed: (component?: string) =>
      logger.info('Credentials displayed to user', component || 'RegistrationSuccess'),

    loginRedirect: (restaurantCode: string, component?: string) =>
      logger.info('User redirected to login', component || 'RegistrationSuccess', { restaurantCode })
  },

  // Development helpers
  group: (label: string): void => {
    if (config.logToConsole) console.group(label);
  },

  groupEnd: (): void => {
    if (config.logToConsole) console.groupEnd();
  },

  table: (data: any, component?: string): void => {
    if (config.logToConsole) console.table(data);
    logger.debug('Table data logged', component, { tableData: data });
  }
};

// Initialize logger
logger.info('Promith Logger initialized', 'Logger', {
  version: '2.0.0',
  config: logger.getConfig()
});

export default logger;
