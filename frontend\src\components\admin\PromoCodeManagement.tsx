import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { toast } from '@/components/ui/sonner';
import { Plus, Edit, Trash2, Eye, EyeOff, Calendar, Percent, PoundSterling } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { getAuthHeaders } from '@/utils/authUtils';

interface PromoCode {
  id: string;
  code: string;
  name: string;
  description?: string;
  discount_type: 'percentage' | 'fixed_amount';
  discount_value: number;
  scope: string;
  minimum_spend?: number;
  maximum_discount?: number;
  start_date: string;
  end_date: string;
  usage_limit?: number;
  usage_count: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface PromoCodeFormData {
  code: string;
  name: string;
  description: string;
  discount_type: 'percentage' | 'fixed_amount';
  discount_value: string;
  scope: string;
  minimum_spend: string;
  maximum_discount: string;
  start_date: string;
  end_date: string;
  usage_limit: string;
  is_active: boolean;
}

const PromoCodeManagement: React.FC = () => {
  const { user } = useAuth();
  const [promoCodes, setPromoCodes] = useState<PromoCode[]>([]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingPromoCode, setEditingPromoCode] = useState<PromoCode | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<PromoCodeFormData>({
    code: '',
    name: '',
    description: '',
    discount_type: 'percentage',
    discount_value: '',
    scope: 'order_total',
    minimum_spend: '',
    maximum_discount: '',
    start_date: '',
    end_date: '',
    usage_limit: '',
    is_active: true
  });

  useEffect(() => {
    fetchPromoCodes();
  }, []);

  const fetchPromoCodes = async () => {
    try {
      setIsLoading(true);

      // Try authenticated endpoint first for admin access
      let response = await fetch('/api/discounts/promo-codes', {
        headers: getAuthHeaders(user)
      });

      if (response.ok) {
        const codes = await response.json();
        console.log('✅ Raw API response from admin endpoint:', codes);
        console.log('✅ Setting promoCodes state with:', codes.length, 'codes');
        setPromoCodes(codes);
        console.log('✅ Successfully fetched promo codes from admin endpoint:', codes.length);
      } else if (response.status === 401) {
        // Fallback to public active codes endpoint
        console.log('🔄 Admin endpoint failed, trying public endpoint...');
        response = await fetch('/api/discounts/promo-codes/active');
        if (response.ok) {
          const codes = await response.json();
          console.log('✅ Raw API response from public endpoint:', codes);
          console.log('✅ Setting promoCodes state with:', codes.length, 'codes');
          setPromoCodes(codes);
          console.log('✅ Successfully fetched active promo codes from public endpoint:', codes.length);
          toast.info('Showing active promo codes only (limited access)');
        } else {
          console.error('Failed to fetch promo codes from both endpoints:', response.status, response.statusText);
          toast.error('Failed to fetch promo codes');
        }
      } else {
        console.error('Failed to fetch promo codes:', response.status, response.statusText);
        toast.error('Failed to fetch promo codes');
      }
    } catch (error) {
      console.error('Error fetching promo codes:', error);
      toast.error('Failed to fetch promo codes');
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      code: '',
      name: '',
      description: '',
      discount_type: 'percentage',
      discount_value: '',
      scope: 'order_total',
      minimum_spend: '',
      maximum_discount: '',
      start_date: '',
      end_date: '',
      usage_limit: '',
      is_active: true
    });
    setEditingPromoCode(null);
  };

  const handleCreate = () => {
    resetForm();
    setIsDialogOpen(true);
  };

  const handleEdit = (promoCode: PromoCode) => {
    setFormData({
      code: promoCode.code,
      name: promoCode.name,
      description: promoCode.description || '',
      discount_type: promoCode.discount_type,
      discount_value: promoCode.discount_value.toString(),
      scope: promoCode.scope,
      minimum_spend: promoCode.minimum_spend?.toString() || '',
      maximum_discount: promoCode.maximum_discount?.toString() || '',
      start_date: promoCode.start_date.split('T')[0],
      end_date: promoCode.end_date.split('T')[0],
      usage_limit: promoCode.usage_limit?.toString() || '',
      is_active: promoCode.is_active
    });
    setEditingPromoCode(promoCode);
    setIsDialogOpen(true);
  };

  const handleSubmit = async () => {
    try {
      // Validation
      if (!formData.code || !formData.name || !formData.discount_value) {
        toast.error('Please fill in all required fields');
        return;
      }

      const submitData = {
        code: formData.code.toUpperCase(),
        name: formData.name,
        description: formData.description || undefined,
        discount_type: formData.discount_type,
        discount_value: parseFloat(formData.discount_value),
        scope: formData.scope,
        minimum_spend: formData.minimum_spend ? parseFloat(formData.minimum_spend) : undefined,
        maximum_discount: formData.maximum_discount ? parseFloat(formData.maximum_discount) : undefined,
        start_date: new Date(formData.start_date).toISOString(),
        end_date: new Date(formData.end_date).toISOString(),
        usage_limit: formData.usage_limit ? parseInt(formData.usage_limit) : undefined,
        is_active: formData.is_active
      };

      const url = editingPromoCode 
        ? `/api/discounts/promo-codes/${editingPromoCode.id}`
        : '/api/discounts/promo-codes';
      
      const method = editingPromoCode ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: getAuthHeaders(user),
        body: JSON.stringify(submitData),
      });

      if (response.ok) {
        toast.success(editingPromoCode ? 'Promo code updated successfully' : 'Promo code created successfully');
        setIsDialogOpen(false);
        resetForm();
        fetchPromoCodes();
      } else {
        const error = await response.json();
        toast.error(error.detail || 'Failed to save promo code');
      }
    } catch (error) {
      console.error('Error saving promo code:', error);
      toast.error('Failed to save promo code');
    }
  };

  const handleDelete = async (promoCode: PromoCode) => {
    if (!confirm(`Are you sure you want to delete the promo code "${promoCode.code}"?`)) {
      return;
    }

    try {
      const response = await fetch(`/api/discounts/promo-codes/${promoCode.id}`, {
        method: 'DELETE',
        headers: getAuthHeaders(user)
      });

      if (response.ok) {
        toast.success('Promo code deleted successfully');
        fetchPromoCodes();
      } else {
        toast.error('Failed to delete promo code');
      }
    } catch (error) {
      console.error('Error deleting promo code:', error);
      toast.error('Failed to delete promo code');
    }
  };

  const toggleActive = async (promoCode: PromoCode) => {
    try {
      const response = await fetch(`/api/discounts/promo-codes/${promoCode.id}`, {
        method: 'PUT',
        headers: getAuthHeaders(user),
        body: JSON.stringify({ is_active: !promoCode.is_active }),
      });

      if (response.ok) {
        toast.success(`Promo code ${!promoCode.is_active ? 'activated' : 'deactivated'}`);
        fetchPromoCodes();
      } else {
        toast.error('Failed to update promo code status');
      }
    } catch (error) {
      console.error('Error updating promo code status:', error);
      toast.error('Failed to update promo code status');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const isExpired = (endDate: string) => {
    return new Date(endDate) < new Date();
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Promo Code Management</h2>
          <p className="text-muted-foreground">Create and manage discount codes for your restaurant</p>
        </div>
        <Button onClick={handleCreate}>
          <Plus className="w-4 h-4 mr-2" />
          Create Promo Code
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Active Promo Codes</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">Loading promo codes...</div>
          ) : promoCodes.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <p>No promo codes found. Create your first promo code to get started.</p>
              <p className="text-xs mt-2 text-gray-400">
                Debug: promoCodes.length = {promoCodes.length}, isLoading = {isLoading.toString()}
              </p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Code</TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead>Discount</TableHead>
                  <TableHead>Usage</TableHead>
                  <TableHead>Valid Period</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {promoCodes.map((promoCode) => (
                  <TableRow key={promoCode.id}>
                    <TableCell>
                      <Badge variant="outline" className="font-mono">
                        {promoCode.code}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{promoCode.name}</div>
                        {promoCode.description && (
                          <div className="text-sm text-muted-foreground">
                            {promoCode.description}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        {promoCode.discount_type === 'percentage' ? (
                          <Percent className="w-3 h-3" />
                        ) : (
                          <PoundSterling className="w-3 h-3" />
                        )}
                        <span className="font-medium">
                          {promoCode.discount_type === 'percentage' 
                            ? `${promoCode.discount_value}%`
                            : `£${promoCode.discount_value}`
                          }
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div>{promoCode.usage_count} used</div>
                        {promoCode.usage_limit && (
                          <div className="text-muted-foreground">
                            of {promoCode.usage_limit} limit
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div>{formatDate(promoCode.start_date)}</div>
                        <div className="text-muted-foreground">
                          to {formatDate(promoCode.end_date)}
                        </div>
                        {isExpired(promoCode.end_date) && (
                          <Badge variant="destructive" className="text-xs mt-1">
                            Expired
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Switch
                          checked={promoCode.is_active}
                          onCheckedChange={() => toggleActive(promoCode)}
                        />
                        {promoCode.is_active ? (
                          <Eye className="w-4 h-4 text-green-600" />
                        ) : (
                          <EyeOff className="w-4 h-4 text-gray-400" />
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEdit(promoCode)}
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(promoCode)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Create/Edit Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingPromoCode ? 'Edit Promo Code' : 'Create New Promo Code'}
            </DialogTitle>
          </DialogHeader>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="code">Code *</Label>
              <Input
                id="code"
                value={formData.code}
                onChange={(e) => setFormData(prev => ({ ...prev, code: e.target.value.toUpperCase() }))}
                placeholder="SAVE10"
                className="font-mono"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="name">Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="10% Off Sale"
              />
            </div>

            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Get 10% off your entire order"
                rows={2}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="discount_type">Discount Type *</Label>
              <Select
                value={formData.discount_type}
                onValueChange={(value: 'percentage' | 'fixed_amount') => 
                  setFormData(prev => ({ ...prev, discount_type: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="percentage">Percentage</SelectItem>
                  <SelectItem value="fixed_amount">Fixed Amount</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="discount_value">
                Discount Value * {formData.discount_type === 'percentage' ? '(%)' : '(£)'}
              </Label>
              <Input
                id="discount_value"
                type="number"
                step="0.01"
                min="0"
                max={formData.discount_type === 'percentage' ? '100' : undefined}
                value={formData.discount_value}
                onChange={(e) => setFormData(prev => ({ ...prev, discount_value: e.target.value }))}
                placeholder={formData.discount_type === 'percentage' ? '10' : '5.00'}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="minimum_spend">Minimum Spend (£)</Label>
              <Input
                id="minimum_spend"
                type="number"
                step="0.01"
                min="0"
                value={formData.minimum_spend}
                onChange={(e) => setFormData(prev => ({ ...prev, minimum_spend: e.target.value }))}
                placeholder="20.00"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="maximum_discount">Maximum Discount (£)</Label>
              <Input
                id="maximum_discount"
                type="number"
                step="0.01"
                min="0"
                value={formData.maximum_discount}
                onChange={(e) => setFormData(prev => ({ ...prev, maximum_discount: e.target.value }))}
                placeholder="50.00"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="start_date">Start Date *</Label>
              <Input
                id="start_date"
                type="date"
                value={formData.start_date}
                onChange={(e) => setFormData(prev => ({ ...prev, start_date: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="end_date">End Date *</Label>
              <Input
                id="end_date"
                type="date"
                value={formData.end_date}
                onChange={(e) => setFormData(prev => ({ ...prev, end_date: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="usage_limit">Usage Limit</Label>
              <Input
                id="usage_limit"
                type="number"
                min="1"
                value={formData.usage_limit}
                onChange={(e) => setFormData(prev => ({ ...prev, usage_limit: e.target.value }))}
                placeholder="100"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="scope">Scope</Label>
              <Select
                value={formData.scope}
                onValueChange={(value) => setFormData(prev => ({ ...prev, scope: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="order_total">Entire Order</SelectItem>
                  <SelectItem value="specific_items">Specific Items</SelectItem>
                  <SelectItem value="category">Category</SelectItem>
                  <SelectItem value="minimum_spend">Minimum Spend</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center space-x-2 md:col-span-2">
              <Switch
                id="is_active"
                checked={formData.is_active}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
              />
              <Label htmlFor="is_active">Active</Label>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSubmit}>
              {editingPromoCode ? 'Update' : 'Create'} Promo Code
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default PromoCodeManagement;
