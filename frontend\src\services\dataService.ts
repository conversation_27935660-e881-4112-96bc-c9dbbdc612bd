import logger from "@/utils/logger";
import { RestaurantRegistrationData, RestaurantSetupData } from "@/types/restaurantSetup";

export interface RestaurantData {
  id: string;
  code: string;
  name: string;
  logo: string;
  address: string;
  phone: string;
  email: string;
  vatRate: number;
  currency: string;
  isActive: boolean;
  ownerName: string;
  businessLicenseNumber: string;
  restaurantType: string;
  password: string;
  createdAt: string;
  updatedAt: string;
  setupData?: RestaurantSetupData;
  // Added missing properties
  hasData?: boolean;
  subscriptionPlan?: "basic" | "pro" | "customized";
  subscriptionStatus?: "active" | "trial" | "expired" | "cancelled";
  subscriptionExpiresAt?: string;
  customizedFeatures?: string[];
  billingInfo?: {
    monthlyPrice: number;
    currency: string;
    nextBillingDate: string;
  };
}

export interface UserData {
  id: string;
  name: string;
  email: string;
  phone: string;
  restaurant_id: string;
  restaurant_name: string;
  role: string;
  position: string;
  pin: string;
  status: string;
  hireDate: string;
  performance: number;
  accessLevel: 'full' | 'limited';
}

export interface DataStore {
  restaurants: RestaurantData[];
  users: UserData[];
}

class DataService {
  private apiBaseUrl = '/api';
  private cache: DataStore | null = null;
  private lastFetch: number = 0;
  private cacheTimeout = 5000; // 5 seconds

  constructor() {
    logger.info('DataService initialized with API backend', 'DataService');
  }

  /**
   * Load data from the backend API
   */
  async loadData(): Promise<DataStore> {
    const now = Date.now();

    // Return cached data if it's still fresh
    if (this.cache && (now - this.lastFetch) < this.cacheTimeout) {
      logger.debug('Returning cached data', 'DataService');
      return this.cache;
    }

    try {
      logger.debug('Fetching data from backend API', 'DataService');

      // Fetch restaurants and users from API
      const [restaurantsResponse, usersResponse] = await Promise.all([
        fetch(`${this.apiBaseUrl}/restaurants`),
        fetch(`${this.apiBaseUrl}/restaurants/users/all`)
      ]);

      if (!restaurantsResponse.ok) {
        throw new Error(`Failed to fetch restaurants: ${restaurantsResponse.statusText}`);
      }

      const restaurants: RestaurantData[] = await restaurantsResponse.json();
      let users: UserData[] = [];

      // Try to fetch users, but don't fail if endpoint doesn't exist yet
      try {
        if (usersResponse.ok) {
          users = await usersResponse.json();
        }
      } catch (userError) {
        logger.warn('Could not fetch users from API, using empty array', 'DataService');
      }

      const data: DataStore = { restaurants, users };

      // Cache the data
      this.cache = data;
      this.lastFetch = now;

      logger.dataOperation('load', 'restaurant data from API', 'DataService', {
        restaurants: data.restaurants.length,
        users: data.users.length
      });

      return data;
    } catch (error) {
      logger.logError(error, 'loading data from backend API', 'DataService');

      // Fallback to localStorage if API fails
      try {
        const persistentData = localStorage.getItem('persistentData');
        if (persistentData) {
          const fallbackData: DataStore = JSON.parse(persistentData);
          this.cache = fallbackData;
          logger.warn('Using localStorage fallback data', 'DataService');
          return fallbackData;
        }
      } catch (fallbackError) {
        logger.logError(fallbackError, 'localStorage fallback', 'DataService');
      }

      // Return empty data structure as final fallback
      const fallbackData: DataStore = { restaurants: [], users: [] };
      this.cache = fallbackData;
      return fallbackData;
    }
  }

  /**
   * Save data to localStorage (since we can't write to public files directly)
   * In a real application, this would make an API call to save to the server
   */
  async saveData(data: DataStore): Promise<boolean> {
    try {
      // Save to localStorage as a fallback
      localStorage.setItem('persistentData', JSON.stringify(data));

      // Update cache
      this.cache = data;
      this.lastFetch = Date.now();

      logger.dataOperation('save', 'restaurant data to localStorage', 'DataService', {
        restaurants: data.restaurants.length,
        users: data.users.length
      });

      return true;
    } catch (error) {
      logger.logError(error, 'saving data', 'DataService');
      return false;
    }
  }

  /**
   * Get all restaurants
   */
  async getRestaurants(): Promise<RestaurantData[]> {
    const data = await this.loadData();

    // Also check localStorage for any additional restaurants
    try {
      const persistentData = localStorage.getItem('persistentData');
      if (persistentData) {
        const localData: DataStore = JSON.parse(persistentData);
        // Merge with loaded data, giving priority to localStorage
        const mergedRestaurants = [...data.restaurants];

        localData.restaurants.forEach(localRestaurant => {
          const existingIndex = mergedRestaurants.findIndex(r => r.id === localRestaurant.id);
          if (existingIndex >= 0) {
            mergedRestaurants[existingIndex] = localRestaurant;
          } else {
            mergedRestaurants.push(localRestaurant);
          }
        });

        return mergedRestaurants;
      }
    } catch (error) {
      logger.logError(error, 'merging localStorage data', 'DataService');
    }

    return data.restaurants;
  }

  /**
   * Get all users
   */
  async getUsers(): Promise<UserData[]> {
    const data = await this.loadData();

    // Also check localStorage for any additional users
    try {
      const persistentData = localStorage.getItem('persistentData');
      if (persistentData) {
        const localData: DataStore = JSON.parse(persistentData);
        // Merge with loaded data, giving priority to localStorage
        const mergedUsers = [...data.users];

        localData.users.forEach(localUser => {
          const existingIndex = mergedUsers.findIndex(u => u.id === localUser.id);
          if (existingIndex >= 0) {
            mergedUsers[existingIndex] = localUser;
          } else {
            mergedUsers.push(localUser);
          }
        });

        return mergedUsers;
      }
    } catch (error) {
      logger.logError(error, 'merging localStorage users', 'DataService');
    }

    return data.users;
  }

  /**
   * Add a new restaurant using the backend API
   */
  async addRestaurant(
    registrationData: RestaurantRegistrationData,
    setupData: RestaurantSetupData,
    credentials: { restaurantCode: string; ownerPin: string }
  ): Promise<{ success: boolean; restaurant?: RestaurantData; user?: UserData }> {
    try {
      // Prepare the registration request
      const registrationRequest = {
        registrationData,
        setupData,
        credentials
      };

      logger.debug('Sending registration request to API', 'DataService', {
        restaurantCode: credentials.restaurantCode,
        restaurantName: registrationData.restaurantName
      });

      // Send to backend API
      const response = await fetch(`${this.apiBaseUrl}/restaurants/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(registrationRequest)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ detail: 'Unknown error' }));
        throw new Error(`API Error: ${errorData.detail || response.statusText}`);
      }

      const result = await response.json();

      if (result.success) {
        // Clear cache to force reload of fresh data
        this.clearCache();

        logger.dataOperation('add', 'new restaurant via API', 'DataService', {
          restaurantCode: credentials.restaurantCode,
          restaurantName: registrationData.restaurantName
        });

        return {
          success: true,
          restaurant: result.restaurant,
          user: result.user
        };
      } else {
        throw new Error(result.message || 'Registration failed');
      }
    } catch (error) {
      logger.logError(error, 'adding new restaurant via API', 'DataService');

      // Fallback to localStorage if API fails
      try {
        logger.warn('API failed, attempting localStorage fallback', 'DataService');
        return await this.addRestaurantFallback(registrationData, setupData, credentials);
      } catch (fallbackError) {
        logger.logError(fallbackError, 'localStorage fallback failed', 'DataService');
        return { success: false };
      }
    }
  }

  /**
   * Fallback method to add restaurant to localStorage if API fails
   */
  private async addRestaurantFallback(
    registrationData: RestaurantRegistrationData,
    setupData: RestaurantSetupData,
    credentials: { restaurantCode: string; ownerPin: string }
  ): Promise<{ success: boolean; restaurant?: RestaurantData; user?: UserData }> {
    const currentData = await this.loadData();

    // Generate new restaurant ID
    const restaurantId = Date.now().toString() + Math.random().toString(36).substr(2, 9);

    // Create restaurant entry
    const newRestaurant: RestaurantData = {
      id: restaurantId,
      code: credentials.restaurantCode,
      name: registrationData.restaurantName,
      logo: setupData.logoUrl || '/logos/default-restaurant.png',
      address: `${registrationData.address.street}, ${registrationData.address.city}, ${registrationData.address.zipCode}`,
      phone: registrationData.phone,
      email: registrationData.email,
      vatRate: 20,
      currency: 'GBP',
      isActive: true,
      ownerName: registrationData.ownerName,
      businessLicenseNumber: registrationData.businessLicenseNumber,
      restaurantType: registrationData.restaurantType,
      password: registrationData.password,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      setupData
    };

    // Generate new user ID
    const userId = Date.now().toString() + Math.random().toString(36).substr(2, 9);

    // Create owner user entry
    const newUser: UserData = {
      id: userId,
      name: registrationData.ownerName,
      email: registrationData.email,
      phone: registrationData.phone,
      restaurant_id: restaurantId,
      restaurant_name: registrationData.restaurantName,
      role: 'admin',
      position: 'Owner/Manager',
      pin: credentials.ownerPin,
      status: 'active',
      hireDate: new Date().toISOString().split('T')[0],
      performance: 100,
      accessLevel: 'full'
    };

    // Save to localStorage
    const persistentData: DataStore = {
      restaurants: [...currentData.restaurants, newRestaurant],
      users: [...currentData.users, newUser]
    };

    const saveSuccess = await this.saveData(persistentData);

    if (saveSuccess) {
      logger.dataOperation('add', 'new restaurant (localStorage fallback)', 'DataService', {
        restaurantCode: credentials.restaurantCode,
        restaurantName: registrationData.restaurantName
      });

      return {
        success: true,
        restaurant: newRestaurant,
        user: newUser
      };
    } else {
      throw new Error('Failed to save restaurant data to localStorage');
    }
  }

  /**
   * Find restaurant by code
   */
  async findRestaurantByCode(code: string): Promise<RestaurantData | null> {
    const restaurants = await this.getRestaurants();
    return restaurants.find(r => r.code === code) || null;
  }

  /**
   * Find user by email
   */
  async findUserByEmail(email: string): Promise<UserData | null> {
    const users = await this.getUsers();
    return users.find(u => u.email === email) || null;
  }

  /**
   * Clear cache to force reload
   */
  clearCache(): void {
    this.cache = null;
    this.lastFetch = 0;
    logger.debug('Data cache cleared', 'DataService');
  }
}

// Export singleton instance
export const dataService = new DataService();
export default dataService;
