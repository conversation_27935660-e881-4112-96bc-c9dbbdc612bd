"""
Split bill models for RestroManage database.
Corresponds to app/models/split_bills.py Pydantic models.
"""

from sqlalchemy import Column, String, Boolean, Float, Text, JSON, ForeignKey, Integer, DateTime
from sqlalchemy.orm import relationship
from .base import BaseModel, TimestampMixin, StatusMixin, SoftDeleteMixin

class SplitBill(BaseModel, TimestampMixin, StatusMixin, SoftDeleteMixin):
    """
    Split bill model for managing divided payments.
    Corresponds to SplitBill Pydantic model in app/models/split_bills.py
    """
    __tablename__ = "split_bills"
    
    # Restaurant association
    restaurant_id = Column(String(36), ForeignKey("restaurants.id"), nullable=False, index=True)
    
    # Original order association
    original_order_id = Column(String(36), ForeignKey("orders.id"), nullable=False, index=True)
    
    # Split configuration
    split_type = Column(String(20), nullable=False, index=True)
    # Types: by_items, equal_split, custom_amounts, percentage_split
    number_of_splits = Column(Integer, nullable=False)
    
    # Financial totals
    original_total = Column(Float, nullable=False)
    total_paid = Column(Float, default=0.0, nullable=False)
    remaining_balance = Column(Float, nullable=False)
    
    # Status tracking
    status = Column(String(20), default="pending", nullable=False, index=True)
    # Status: pending, partially_paid, completed, cancelled
    
    # Split details
    split_method = Column(String(50), nullable=True)  # How the split was calculated
    split_rules = Column(JSON, nullable=True)         # Rules used for splitting
    
    # Customer information
    customer_names = Column(JSON, nullable=True)      # List of customer names
    customer_contacts = Column(JSON, nullable=True)   # Contact information
    
    # Payment tracking
    payment_deadline = Column(DateTime(timezone=True), nullable=True)
    payment_reminders_sent = Column(Integer, default=0, nullable=False)
    
    # Service information
    table_id = Column(String(36), ForeignKey("tables.id"), nullable=True, index=True)
    server_id = Column(String(36), ForeignKey("staff.id"), nullable=True, index=True)
    
    # Notes and special instructions
    notes = Column(Text, nullable=True)
    special_instructions = Column(Text, nullable=True)
    
    # Relationships
    restaurant = relationship("Restaurant")
    original_order = relationship("Order", back_populates="split_bill", foreign_keys=[original_order_id])
    table = relationship("Table")
    server = relationship("Staff", foreign_keys=[server_id])
    portions = relationship("SplitPortion", back_populates="split_bill", cascade="all, delete-orphan")
    payments = relationship("SplitBillPayment", back_populates="split_bill", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<SplitBill(id={self.id}, original_order_id={self.original_order_id}, status={self.status})>"

class SplitPortion(BaseModel, TimestampMixin, StatusMixin):
    """
    Individual portion of a split bill.
    Corresponds to SplitPortion Pydantic model in app/models/split_bills.py
    """
    __tablename__ = "split_portions"
    
    # Split bill association
    split_bill_id = Column(String(36), ForeignKey("split_bills.id"), nullable=False, index=True)
    
    # Portion identification
    portion_number = Column(Integer, nullable=False)
    portion_name = Column(String(100), nullable=True)  # Optional custom name
    
    # Customer information
    customer_name = Column(String(255), nullable=True)
    customer_id = Column(String(36), nullable=True, index=True)
    customer_phone = Column(String(20), nullable=True)
    customer_email = Column(String(255), nullable=True)
    
    # Financial breakdown
    subtotal = Column(Float, nullable=False)
    tax_amount = Column(Float, default=0.0, nullable=False)
    tip_amount = Column(Float, default=0.0, nullable=False)
    discount_amount = Column(Float, default=0.0, nullable=False)
    total_amount = Column(Float, nullable=False)
    
    # Payment status
    payment_status = Column(String(20), default="pending", nullable=False, index=True)
    # Status: pending, paid, partially_paid, overdue, cancelled
    amount_paid = Column(Float, default=0.0, nullable=False)
    amount_due = Column(Float, nullable=False)
    
    # Payment method and details
    payment_method = Column(String(20), nullable=True)
    # Methods: cash, card, digital_wallet, gift_card, loyalty_points
    payment_reference = Column(String(255), nullable=True)
    payment_processor_id = Column(String(255), nullable=True)
    
    # Timing
    paid_at = Column(DateTime(timezone=True), nullable=True)
    payment_due_date = Column(DateTime(timezone=True), nullable=True)
    
    # Items included in this portion
    included_items = Column(JSON, nullable=True)  # List of order item IDs and quantities
    
    # Receipt and documentation
    receipt_number = Column(String(50), nullable=True, unique=True, index=True)
    receipt_generated = Column(Boolean, default=False, nullable=False)
    receipt_sent = Column(Boolean, default=False, nullable=False)
    
    # Notes
    customer_notes = Column(Text, nullable=True)
    internal_notes = Column(Text, nullable=True)
    
    # Relationships
    split_bill = relationship("SplitBill", back_populates="portions")
    payments = relationship("SplitBillPayment", back_populates="portion")
    
    def __repr__(self):
        return f"<SplitPortion(id={self.id}, portion_number={self.portion_number}, total_amount={self.total_amount})>"

class SplitBillPayment(BaseModel, TimestampMixin):
    """
    Payment record for split bill portions.
    Corresponds to PaymentResponse Pydantic model in app/models/split_bills.py
    """
    __tablename__ = "split_bill_payments"
    
    # Split bill and portion association
    split_bill_id = Column(String(36), ForeignKey("split_bills.id"), nullable=False, index=True)
    portion_id = Column(String(36), ForeignKey("split_portions.id"), nullable=False, index=True)
    
    # Payment identification
    payment_id = Column(String(100), nullable=False, unique=True, index=True)
    receipt_number = Column(String(50), nullable=False, unique=True, index=True)
    
    # Payment details
    payment_method = Column(String(20), nullable=False)
    amount_paid = Column(Float, nullable=False)
    tip_amount = Column(Float, default=0.0, nullable=False)
    total_amount = Column(Float, nullable=False)
    
    # Payment processing
    payment_reference = Column(String(255), nullable=True)
    processor_transaction_id = Column(String(255), nullable=True)
    processor_response = Column(JSON, nullable=True)
    
    # Status and validation
    payment_status = Column(String(20), default="completed", nullable=False, index=True)
    # Status: pending, completed, failed, refunded, cancelled
    is_successful = Column(Boolean, default=True, nullable=False)
    
    # Customer information
    customer_name = Column(String(255), nullable=True)
    customer_signature = Column(Text, nullable=True)  # Digital signature data
    
    # Timing
    processed_at = Column(DateTime(timezone=True), nullable=False, index=True)
    authorized_at = Column(DateTime(timezone=True), nullable=True)
    settled_at = Column(DateTime(timezone=True), nullable=True)
    
    # Refund information
    refund_amount = Column(Float, default=0.0, nullable=False)
    refund_reason = Column(String(255), nullable=True)
    refunded_at = Column(DateTime(timezone=True), nullable=True)
    refund_reference = Column(String(255), nullable=True)
    
    # Staff and terminal information
    processed_by = Column(String(36), ForeignKey("users.id"), nullable=True, index=True)
    terminal_id = Column(String(100), nullable=True)
    location = Column(String(255), nullable=True)
    
    # Notes and comments
    payment_notes = Column(Text, nullable=True)
    customer_notes = Column(Text, nullable=True)
    
    # Relationships
    split_bill = relationship("SplitBill", back_populates="payments")
    portion = relationship("SplitPortion", back_populates="payments")
    processed_by_user = relationship("User", foreign_keys=[processed_by])
    
    def __repr__(self):
        return f"<SplitBillPayment(id={self.id}, payment_id={self.payment_id}, amount_paid={self.amount_paid})>"
