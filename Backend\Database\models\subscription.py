"""
Subscription models for RestroManage database.
Migrated from backend/models/subscription.py
"""

from sqlalchemy import Column, String, Boolean, DateTime, JSON, ForeignKey, Integer, Numeric
from sqlalchemy.orm import relationship
from datetime import datetime
from .base import BaseModel, TimestampMixin

class SubscriptionPlan(BaseModel, TimestampMixin):
    """Subscription plan definitions with features and pricing"""
    __tablename__ = "subscription_plans"
    
    # Plan identification
    name = Column(String(255), nullable=False)
    description = Column(String(500), nullable=True)
    
    # Pricing
    price_monthly = Column(Numeric(10, 2), nullable=False)
    price_yearly = Column(Numeric(10, 2), nullable=True)
    
    # Features and configuration
    features = Column(JSON, nullable=False)  # List of feature IDs
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Relationships
    subscriptions = relationship("RestaurantSubscription", back_populates="plan")
    
    def __repr__(self):
        return f"<SubscriptionPlan(id={self.id}, name={self.name})>"

class RestaurantSubscription(BaseModel, TimestampMixin):
    """Restaurant subscription status and configuration"""
    __tablename__ = "restaurant_subscriptions"
    
    # Restaurant association
    restaurant_id = Column(String(36), nullable=False, unique=True, index=True)
    
    # Plan association
    plan_id = Column(String(36), ForeignKey("subscription_plans.id"), nullable=False, index=True)
    
    # Subscription status
    status = Column(String(20), nullable=False, default="trial", index=True)  # trial, active, expired, cancelled
    
    # Subscription timing
    starts_at = Column(DateTime(timezone=True), nullable=False, default=datetime.utcnow)
    expires_at = Column(DateTime(timezone=True), nullable=False)
    trial_ends_at = Column(DateTime(timezone=True), nullable=True)
    
    # Configuration
    custom_features = Column(JSON, default=list)  # Additional features for customized plans
    billing_cycle = Column(String(20), default="monthly")  # monthly, yearly
    auto_renew = Column(Boolean, default=True)
    
    # Metadata
    payment_method = Column(String(50), nullable=True)
    last_payment_date = Column(DateTime(timezone=True), nullable=True)
    next_payment_date = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    plan = relationship("SubscriptionPlan", back_populates="subscriptions")
    access_logs = relationship("FeatureAccessLog", back_populates="subscription")
    
    def __repr__(self):
        return f"<RestaurantSubscription(id={self.id}, restaurant_id={self.restaurant_id}, plan_id={self.plan_id})>"

class FeatureDefinition(BaseModel, TimestampMixin):
    """Available features in the system"""
    __tablename__ = "feature_definitions"
    
    # Feature identification
    name = Column(String(255), nullable=False)
    description = Column(String(500), nullable=True)
    category = Column(String(50), nullable=True, index=True)  # 'core', 'analytics', 'advanced', 'ai'
    
    # Feature configuration
    is_active = Column(Boolean, default=True, nullable=False)
    requires_setup = Column(Boolean, default=False)
    dependencies = Column(JSON, default=list)  # List of feature IDs this feature depends on
    
    # Usage tracking
    usage_count = Column(Integer, default=0)
    last_used = Column(DateTime(timezone=True), nullable=True)
    
    def __repr__(self):
        return f"<FeatureDefinition(id={self.id}, name={self.name}, category={self.category})>"

class FeatureAccessLog(BaseModel, TimestampMixin):
    """Audit log for feature access attempts"""
    __tablename__ = "feature_access_logs"
    
    # Access details
    restaurant_id = Column(String(36), nullable=False, index=True)
    subscription_id = Column(String(36), ForeignKey("restaurant_subscriptions.id"), nullable=True, index=True)
    feature_id = Column(String(100), nullable=False, index=True)
    
    # Access result
    access_granted = Column(Boolean, nullable=False, index=True)
    access_reason = Column(String(100), nullable=True)  # 'pro_plan', 'basic_plan', 'trial_expired', etc.
    
    # User context
    user_id = Column(String(36), nullable=True, index=True)
    ip_address = Column(String(45), nullable=True)  # IPv6 compatible
    user_agent = Column(String(500), nullable=True)
    
    # Request context
    endpoint = Column(String(200), nullable=True)
    request_method = Column(String(10), nullable=True)
    response_time_ms = Column(Integer, nullable=True)
    
    # Timestamp (inherited from TimestampMixin as created_at)
    access_timestamp = Column(DateTime(timezone=True), default=datetime.utcnow, nullable=False, index=True)
    
    # Relationships
    subscription = relationship("RestaurantSubscription", back_populates="access_logs")
    
    def __repr__(self):
        return f"<FeatureAccessLog(id={self.id}, restaurant_id={self.restaurant_id}, feature_id={self.feature_id}, granted={self.access_granted})>"

class SubscriptionUsageMetrics(BaseModel, TimestampMixin):
    """Track subscription usage metrics for analytics"""
    __tablename__ = "subscription_usage_metrics"
    
    # Subscription association
    restaurant_id = Column(String(36), nullable=False, index=True)
    subscription_id = Column(String(36), ForeignKey("restaurant_subscriptions.id"), nullable=False, index=True)
    
    # Metrics period
    period_start = Column(DateTime(timezone=True), nullable=False, index=True)
    period_end = Column(DateTime(timezone=True), nullable=False, index=True)
    period_type = Column(String(20), nullable=False)  # daily, weekly, monthly
    
    # Usage metrics
    total_requests = Column(Integer, default=0)
    unique_features_used = Column(Integer, default=0)
    total_users = Column(Integer, default=0)
    peak_concurrent_users = Column(Integer, default=0)
    
    # Feature usage breakdown
    feature_usage = Column(JSON, default=dict)  # {feature_id: usage_count}
    
    # Performance metrics
    average_response_time_ms = Column(Integer, nullable=True)
    error_rate_percent = Column(Numeric(5, 2), nullable=True)
    
    def __repr__(self):
        return f"<SubscriptionUsageMetrics(id={self.id}, restaurant_id={self.restaurant_id}, period={self.period_type})>"
