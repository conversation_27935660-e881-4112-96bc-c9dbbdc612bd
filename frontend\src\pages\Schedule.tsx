import { useState, useEffect } from "react";
import Layout from "@/components/layout/Layout";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "@/components/ui/sonner";
import { Calendar as CalendarIcon, Clock, Plus, Edit2, Trash2, Users } from "lucide-react";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Progress } from "@/components/ui/progress";
import { format, isSameDay } from "date-fns";
import { cn } from "@/lib/utils";
import StaffTimeEntries from "@/components/admin/StaffTimeEntries";
import ForecastBasedScheduler from "@/components/schedule/ForecastBasedScheduler";
import StaffCalendar from "@/components/staff/StaffCalendar";
import { ForecastData } from "@/components/dashboard/ForecastCard";
import { ForecastShift, StaffMember } from "@/services/forecastScheduleService";

interface Shift {
  id: string;
  staffId: string;
  staffName: string;
  role: string;
  day: string;
  date: string;
  startTime: string;
  endTime: string;
}

const Schedule = () => {
  // Define days of the week first so it can be used in getDatesForWeek
  const daysOfWeek = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"];

  // Calculate dates for each day of the week based on the selected date
  const getDatesForWeek = (selectedDate: Date) => {
    const result: { day: string; date: Date; formattedDate: string }[] = [];
    const currentDay = selectedDate.getDay(); // 0 = Sunday, 1 = Monday, etc.

    // Convert to our day indexing (0 = Monday, 6 = Sunday)
    const adjustedCurrentDay = currentDay === 0 ? 6 : currentDay - 1;

    // Find the Monday of the current week
    const monday = new Date(selectedDate);
    monday.setDate(selectedDate.getDate() - adjustedCurrentDay);

    // Create entries for each day of the week
    for (let i = 0; i < 7; i++) {
      const currentDate = new Date(monday);
      currentDate.setDate(monday.getDate() + i);

      result.push({
        day: daysOfWeek[i],
        date: currentDate,
        formattedDate: format(currentDate, "MMM d")
      });
    }

    return result;
  };

  const [date, setDate] = useState<Date>(new Date());
  const [view, setView] = useState<"day" | "week" | "month">("week");
  const [isAddShiftOpen, setIsAddShiftOpen] = useState(false);
  const [isEditShiftOpen, setIsEditShiftOpen] = useState(false);
  const [currentShift, setCurrentShift] = useState<Shift | null>(null);
  const [weekDates, setWeekDates] = useState(getDatesForWeek(new Date()));
  const [newShift, setNewShift] = useState<Omit<Shift, "id">>({
    staffId: "",
    staffName: "",
    role: "",
    day: "",
    date: format(new Date(), "yyyy-MM-dd"),
    startTime: "09:00",
    endTime: "17:00"
  });

  // Mock data for staff and shifts
  const staffMembers: StaffMember[] = [
    {
      id: "1",
      name: "Michael Rodriguez",
      role: "waiter",
      position: "Head Waiter",
      status: 'active',
      availableDays: ['mon', 'tue', 'wed', 'thu', 'fri'],
      assignedHours: 35
    },
    {
      id: "2",
      name: "Jennifer Smith",
      role: "waiter",
      position: "Waiter",
      status: 'active',
      availableDays: ['mon', 'tue', 'wed', 'sat', 'sun'],
      assignedHours: 28
    },
    {
      id: "3",
      name: "David Chen",
      role: "waiter",
      position: "Waiter",
      status: 'active',
      availableDays: ['wed', 'thu', 'fri', 'sat', 'sun'],
      assignedHours: 30
    },
    {
      id: "4",
      name: "Maria Lopez",
      role: "chef",
      position: "Head Chef",
      status: 'active',
      availableDays: ['mon', 'tue', 'wed', 'thu', 'fri'],
      assignedHours: 40
    },
    {
      id: "5",
      name: "Robert Johnson",
      role: "manager",
      position: "Floor Manager",
      status: 'active',
      availableDays: ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'],
      assignedHours: 45
    },
    {
      id: "6",
      name: "Sarah Williams",
      role: "hostess",
      position: "Hostess",
      status: 'active',
      availableDays: ['thu', 'fri', 'sat', 'sun'],
      assignedHours: 25
    },
    {
      id: "7",
      name: "James Wilson",
      role: "bartender",
      position: "Bar Manager",
      status: 'active',
      availableDays: ['wed', 'thu', 'fri', 'sat'],
      assignedHours: 32
    },
    {
      id: "8",
      name: "Emily Davis",
      role: "chef",
      position: "Sous Chef",
      status: 'active',
      availableDays: ['mon', 'tue', 'wed', 'thu', 'fri'],
      assignedHours: 38
    }
  ];

  const [shifts, setShifts] = useState<Shift[]>([
    { id: "1", staffId: "1", staffName: "Michael Rodriguez", role: "waiter", day: "Monday", date: "2023-05-15", startTime: "09:00", endTime: "17:00" },
    { id: "2", staffId: "2", staffName: "Jennifer Smith", role: "waiter", day: "Monday", date: "2023-05-15", startTime: "12:00", endTime: "20:00" },
    { id: "3", staffId: "3", staffName: "David Chen", role: "waiter", day: "Tuesday", date: "2023-05-16", startTime: "09:00", endTime: "17:00" },
    { id: "4", staffId: "4", staffName: "Maria Lopez", role: "chef", day: "Tuesday", date: "2023-05-16", startTime: "08:00", endTime: "16:00" },
    { id: "5", staffId: "5", staffName: "Robert Johnson", role: "manager", day: "Wednesday", date: "2023-05-17", startTime: "10:00", endTime: "18:00" },
    { id: "6", staffId: "6", staffName: "Sarah Williams", role: "hostess", day: "Wednesday", date: "2023-05-17", startTime: "12:00", endTime: "20:00" },
    { id: "7", staffId: "1", staffName: "Michael Rodriguez", role: "waiter", day: "Thursday", date: "2023-05-18", startTime: "09:00", endTime: "17:00" },
    { id: "8", staffId: "2", staffName: "Jennifer Smith", role: "waiter", day: "Friday", date: "2023-05-19", startTime: "12:00", endTime: "20:00" },
    { id: "9", staffId: "4", staffName: "Maria Lopez", role: "chef", day: "Saturday", date: "2023-05-20", startTime: "08:00", endTime: "16:00" },
    { id: "10", staffId: "6", staffName: "Sarah Williams", role: "hostess", day: "Saturday", date: "2023-05-20", startTime: "12:00", endTime: "20:00" },
    { id: "11", staffId: "3", staffName: "David Chen", role: "waiter", day: "Sunday", date: "2023-05-21", startTime: "10:00", endTime: "18:00" },
    { id: "12", staffId: "5", staffName: "Robert Johnson", role: "manager", day: "Sunday", date: "2023-05-21", startTime: "10:00", endTime: "18:00" }
  ]);

  const timeSlots = Array.from({ length: 13 }, (_, i) => i + 8); // 8 AM to 8 PM

  // Mock forecast data for the scheduler
  const mockForecastData: ForecastData[] = [
    {
      day: "Monday",
      actualRevenue: 0,
      projectedRevenue: 1150,
      customers: 47,
      confidence: 92,
      staffNeeded: 5,
      tableOccupancy: 65,
      peakHours: [
        { hour: 12, customers: 12 },
        { hour: 13, customers: 15 },
        { hour: 19, customers: 20 }
      ]
    },
    {
      day: "Tuesday",
      actualRevenue: 0,
      projectedRevenue: 1380,
      customers: 54,
      confidence: 90,
      staffNeeded: 6,
      tableOccupancy: 70,
      peakHours: [
        { hour: 12, customers: 14 },
        { hour: 13, customers: 16 },
        { hour: 19, customers: 24 }
      ]
    },
    {
      day: "Wednesday",
      actualRevenue: 0,
      projectedRevenue: 1520,
      customers: 63,
      confidence: 88,
      staffNeeded: 6,
      tableOccupancy: 75,
      peakHours: [
        { hour: 12, customers: 16 },
        { hour: 13, customers: 18 },
        { hour: 19, customers: 29 }
      ]
    },
    {
      day: "Thursday",
      actualRevenue: 0,
      projectedRevenue: 1750,
      customers: 72,
      confidence: 85,
      staffNeeded: 7,
      tableOccupancy: 80,
      peakHours: [
        { hour: 12, customers: 18 },
        { hour: 13, customers: 20 },
        { hour: 19, customers: 34 }
      ]
    },
    {
      day: "Friday",
      actualRevenue: 0,
      projectedRevenue: 2200,
      customers: 90,
      confidence: 82,
      staffNeeded: 8,
      tableOccupancy: 90,
      peakHours: [
        { hour: 12, customers: 22 },
        { hour: 13, customers: 25 },
        { hour: 19, customers: 43 }
      ]
    },
    {
      day: "Saturday",
      actualRevenue: 0,
      projectedRevenue: 2600,
      customers: 105,
      confidence: 80,
      staffNeeded: 9,
      tableOccupancy: 95,
      peakHours: [
        { hour: 12, customers: 26 },
        { hour: 13, customers: 28 },
        { hour: 19, customers: 51 }
      ]
    },
    {
      day: "Sunday",
      actualRevenue: 0,
      projectedRevenue: 2000,
      customers: 80,
      confidence: 84,
      staffNeeded: 7,
      tableOccupancy: 85,
      peakHours: [
        { hour: 12, customers: 20 },
        { hour: 13, customers: 22 },
        { hour: 19, customers: 38 }
      ]
    }
  ];

  const handleAddShift = () => {
    const selectedStaff = staffMembers.find(staff => staff.id === newShift.staffId);
    if (!selectedStaff) {
      toast.error("Please select a staff member");
      return;
    }

    if (!newShift.day) {
      toast.error("Please select a day of the week");
      return;
    }

    // Generate a date for the selected day (this is just for storage, we'll use the day property for display)
    // In a real app, you'd want to calculate the actual date based on the selected week
    const id = (shifts.length + 1).toString();
    const shiftToAdd = {
      ...newShift,
      id,
      staffName: selectedStaff.name,
      role: selectedStaff.role
    };

    setShifts([...shifts, shiftToAdd]);
    setIsAddShiftOpen(false);
    setNewShift({
      staffId: "",
      staffName: "",
      role: "",
      day: "",
      date: format(new Date(), "yyyy-MM-dd"),
      startTime: "09:00",
      endTime: "17:00"
    });
    toast.success("Shift added successfully");
  };

  const handleEditShift = () => {
    if (!currentShift) return;

    const updatedShifts = shifts.map(shift =>
      shift.id === currentShift.id ? currentShift : shift
    );

    setShifts(updatedShifts);
    setIsEditShiftOpen(false);
    setCurrentShift(null);
    toast.success("Shift updated successfully");
  };

  const handleDeleteShift = (id: string) => {
    // Find the shift to be deleted for the confirmation message
    const shiftToDelete = shifts.find(shift => shift.id === id);
    const staffName = shiftToDelete?.staffName || "Unknown";
    const day = shiftToDelete?.day || "Unknown";

    // Remove the shift
    const updatedShifts = shifts.filter(shift => shift.id !== id);
    setShifts(updatedShifts);

    // Show success message with details
    toast.success(`Shift for ${staffName} on ${day} deleted successfully`);
  };

  // Handle forecast-based schedule generation
  const handleScheduleGenerated = (generatedShifts: ForecastShift[]) => {
    // Convert ForecastShift to Shift format
    const newShifts = generatedShifts.map(forecastShift => ({
      id: forecastShift.id,
      staffId: forecastShift.staffId,
      staffName: forecastShift.staffName,
      role: forecastShift.role,
      day: forecastShift.day,
      date: forecastShift.date,
      startTime: forecastShift.startTime,
      endTime: forecastShift.endTime
    }));

    // Add the new shifts to the existing shifts
    // First, remove any shifts for the same dates to avoid duplicates
    const dates = [...new Set(newShifts.map(shift => shift.date))];
    const filteredShifts = shifts.filter(shift => !dates.includes(shift.date));

    // Then add the new shifts
    setShifts([...filteredShifts, ...newShifts]);

    toast.success(`Generated ${newShifts.length} shifts based on forecast data`);
  };

  // Filter shifts based on the selected view and date
  const getFilteredShifts = () => {
    const currentDate = new Date(date);
    const currentDay = format(currentDate, "EEEE");
    const currentDateStr = format(currentDate, "yyyy-MM-dd");

    switch (view) {
      case "day":
        return shifts.filter(shift => shift.day === currentDay || shift.date === currentDateStr);
      case "week":
        // For simplicity, just show all shifts
        return shifts;
      case "month":
        // For simplicity, just show all shifts
        return shifts;
      default:
        return shifts;
    }
  };

  const filteredShifts = getFilteredShifts();

  // Group shifts by day for the week view
  const shiftsByDay = daysOfWeek.map(day => ({
    day,
    shifts: filteredShifts.filter(shift => shift.day === day)
  }));

  // Helper function to calculate shift duration
  const calculateShiftDuration = (startTime: string, endTime: string) => {
    const [startHour, startMinute] = startTime.split(":").map(Number);
    const [endHour, endMinute] = endTime.split(":").map(Number);
    
    const startMinutes = startHour * 60 + startMinute;
    const endMinutes = endHour * 60 + endMinute;
    
    const durationMinutes = endMinutes - startMinutes;
    return (durationMinutes / 60).toFixed(1);
  };

  // Get target hours from localStorage or use default (40)
  const [targetHours, setTargetHours] = useState(() => {
    const savedTarget = localStorage.getItem('staffHoursTarget');
    return savedTarget ? parseInt(savedTarget) : 40;
  });

  // Update target hours when localStorage changes
  useEffect(() => {
    const handleStorageChange = () => {
      const savedTarget = localStorage.getItem('staffHoursTarget');
      if (savedTarget) {
        setTargetHours(parseInt(savedTarget));
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  // Update week dates when selected date changes
  useEffect(() => {
    setWeekDates(getDatesForWeek(date));
  }, [date]);

  // Define interface for staff hours data
  interface StaffHoursData {
    name: string;
    position: string;
    totalHours: number;
    percentOfIdeal: number;
  }

  // Calculate hours per week for each staff member
  const calculateHoursPerWeek = (): StaffHoursData[] => {
    const staffHours: Record<string, StaffHoursData> = {};

    staffMembers.forEach(staff => {
      staffHours[staff.id] = {
        name: staff.name,
        position: staff.position,
        totalHours: 0,
        percentOfIdeal: 0
      };
    });

    shifts.forEach(shift => {
      const startTime = new Date(`2000-01-01T${shift.startTime}`);
      const endTime = new Date(`2000-01-01T${shift.endTime}`);
      const hours = (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60);

      if (staffHours[shift.staffId]) {
        staffHours[shift.staffId].totalHours += hours;
      }
    });

    // Calculate percentage of ideal (using targetHours)
    Object.keys(staffHours).forEach(staffId => {
      staffHours[staffId].percentOfIdeal = (staffHours[staffId].totalHours / targetHours) * 100;
    });

    return Object.values(staffHours);
  };

  const staffHoursData = calculateHoursPerWeek();

  // Staff unavailability state
  const [staffUnavailabilities, setStaffUnavailabilities] = useState<{
    id: string;
    staffId: string;
    date: Date;
    type: 'holiday' | 'sick' | 'personal' | 'other';
    notes?: string;
  }[]>([]);

  // Handle staff unavailability changes
  const handleUnavailabilityChange = (unavailabilities) => {
    setStaffUnavailabilities(unavailabilities);
    
    // In a real app, you would save this to a database
    // For now, we'll just log it
    console.log("Staff unavailabilities updated:", unavailabilities);
  };

  // Check if a staff member is available on a specific date
  const isStaffAvailable = (staffId: string, date: Date) => {
    return !staffUnavailabilities.some(
      unavailability => 
        unavailability.staffId === staffId && 
        isSameDay(new Date(unavailability.date), date)
    );
  };

  // Filter shifts to exclude unavailable staff
  useEffect(() => {
    if (staffUnavailabilities.length > 0) {
      // This is just for demonstration - in a real app, you would filter shifts
      // when generating the schedule or show warnings for conflicts
      console.log("Some staff members are unavailable on scheduled days");
    }
  }, [staffUnavailabilities, shifts]);

  return (
    <Layout title="Staff Schedule">
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex items-center gap-2">
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-[240px] justify-start text-left font-normal",
                    !date && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {date ? format(date, "PPP") : <span>Pick a date</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={date}
                  onSelect={(newDate) => newDate && setDate(newDate)}
                  initialFocus
                />
              </PopoverContent>
            </Popover>

            <Tabs defaultValue="week" onValueChange={(value) => setView(value as "day" | "week" | "month")}>
              <TabsList>
                <TabsTrigger value="day">Day</TabsTrigger>
                <TabsTrigger value="week">Week</TabsTrigger>
                <TabsTrigger value="month">Month</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={() => setIsAddShiftOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Shift
            </Button>
          </div>
        </div>

        <Tabs defaultValue="schedule">
          <TabsList>
            <TabsTrigger value="schedule">Schedule</TabsTrigger>
            <TabsTrigger value="hours">Staff Hours</TabsTrigger>
            <TabsTrigger value="forecast">Forecast-Based</TabsTrigger>
            <TabsTrigger value="availability">Staff Availability</TabsTrigger>
            <TabsTrigger value="time-entries">Time Entries</TabsTrigger>
          </TabsList>
          
          <TabsContent value="schedule" className="space-y-4">
            {view === "day" && (
              <Card>
                <CardHeader>
                  <CardTitle>Daily Schedule - {format(date, "EEEE, MMMM d, yyyy")}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-8">
                    {filteredShifts.length === 0 ? (
                      <div className="text-center py-8 text-muted-foreground">
                        No shifts scheduled for this day
                      </div>
                    ) : (
                      filteredShifts.map((shift) => {
                        // Check if staff is available for this shift
                        const shiftDate = new Date(shift.date);
                        const isAvailable = isStaffAvailable(shift.staffId, shiftDate);
                        
                        return (
                          <div key={shift.id} className="flex items-center justify-between border-b pb-4">
                            <div className="flex items-center gap-4">
                              <div className={`p-3 rounded-full ${isAvailable ? 'bg-primary/10' : 'bg-red-100'}`}>
                                <Users className={`h-6 w-6 ${isAvailable ? 'text-primary' : 'text-red-500'}`} />
                              </div>
                              <div>
                                <div className="font-medium">{shift.staffName}</div>
                                <div className="text-sm text-muted-foreground capitalize">{shift.role}</div>
                                {!isAvailable && (
                                  <div className="text-xs text-red-500 mt-1">Staff unavailable on this day</div>
                                )}
                              </div>
                            </div>
                            <div className="flex items-center gap-4">
                              <div className="text-right">
                                <div className="font-medium flex items-center">
                                  <Clock className="mr-1 h-4 w-4 text-muted-foreground" />
                                  {shift.startTime} - {shift.endTime}
                                </div>
                                <div className="text-sm text-muted-foreground">
                                  {calculateShiftDuration(shift.startTime, shift.endTime)} hours
                                </div>
                              </div>
                              <div className="flex items-center gap-2">
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => {
                                    setCurrentShift(shift);
                                    setIsEditShiftOpen(true);
                                  }}
                                >
                                  <Edit2 className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => handleDeleteShift(shift.id)}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        );
                      })
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {view === "week" && (
              <div className="grid grid-cols-1 md:grid-cols-7 gap-4">
                {shiftsByDay.map((dayData) => (
                  <Card key={dayData.day} className="md:col-span-1">
                    <CardHeader className="p-3">
                      <CardTitle className="text-sm font-medium">{dayData.day}</CardTitle>
                    </CardHeader>
                    <CardContent className="p-3">
                      {dayData.shifts.length === 0 ? (
                        <div className="text-center py-2 text-xs text-muted-foreground">
                          No shifts
                        </div>
                      ) : (
                        <div className="space-y-2">
                          {dayData.shifts.map((shift) => {
                            // Find the date for this shift
                            const shiftDate = weekDates.find(d => d.day === shift.day)?.date || new Date();
                            const isAvailable = isStaffAvailable(shift.staffId, shiftDate);
                            
                            return (
                              <div
                                key={shift.id}
                                className={`text-xs p-2 rounded-md cursor-pointer ${
                                  isAvailable ? 'bg-primary/10 hover:bg-primary/20' : 'bg-red-100 hover:bg-red-200'
                                }`}
                                onClick={() => {
                                  setCurrentShift(shift);
                                  setIsEditShiftOpen(true);
                                }}
                              >
                                <div className="font-medium">{shift.staffName}</div>
                                <div className="text-muted-foreground flex items-center mt-1">
                                  <Clock className="mr-1 h-3 w-3" />
                                  {shift.startTime} - {shift.endTime}
                                </div>
                                {!isAvailable && (
                                  <div className="text-xs text-red-500 mt-1">Unavailable</div>
                                )}
                              </div>
                            );
                          })}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}

            {view === "month" && (
              <Card>
                <CardHeader>
                  <CardTitle>Monthly Schedule - {format(date, "MMMM yyyy")}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8 text-muted-foreground">
                    Monthly view is not implemented in this demo
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>
          
          <TabsContent value="hours">
            <Card>
              <CardHeader>
                <CardTitle>Staff Hours Overview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {staffHoursData.map((staff, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <div>
                          <div className="font-medium">{staff.name}</div>
                          <div className="text-sm text-muted-foreground">{staff.position}</div>
                        </div>
                        <div className="text-right">
                          <div className="font-medium">{staff.totalHours} hours</div>
                          <div className="text-sm text-muted-foreground">
                            {staff.percentOfIdeal.toFixed(0)}% of target ({targetHours} hours)
                          </div>
                        </div>
                      </div>
                      <Progress value={staff.percentOfIdeal} className="h-2" />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="forecast">
            <ForecastBasedScheduler 
              forecastData={mockForecastData}
              staffData={staffMembers}
              onScheduleGenerated={handleScheduleGenerated}
            />
          </TabsContent>
          
          <TabsContent value="availability">
            <StaffCalendar 
              staffData={staffMembers}
              onUnavailabilityChange={handleUnavailabilityChange}
            />
          </TabsContent>
          
          <TabsContent value="time-entries">
            <StaffTimeEntries />
          </TabsContent>
        </Tabs>

        {/* Add Shift Dialog */}
        <Dialog open={isAddShiftOpen} onOpenChange={setIsAddShiftOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add Shift</DialogTitle>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="staff">Staff Member</Label>
                <Select
                  value={newShift.staffId}
                  onValueChange={(value) => setNewShift({ ...newShift, staffId: value })}
                >
                  <SelectTrigger id="staff">
                    <SelectValue placeholder="Select staff member" />
                  </SelectTrigger>
                  <SelectContent>
                    {staffMembers.map((staff) => (
                      <SelectItem key={staff.id} value={staff.id}>
                        {staff.name} ({staff.position})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="day">Day</Label>
                <Select
                  value={newShift.day}
                  onValueChange={(value) => setNewShift({ ...newShift, day: value })}
                >
                  <SelectTrigger id="day">
                    <SelectValue placeholder="Select day" />
                  </SelectTrigger>
                  <SelectContent>
                    {daysOfWeek.map((day) => (
                      <SelectItem key={day} value={day}>
                        {day}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="start-time">Start Time</Label>
                  <Input
                    id="start-time"
                    type="time"
                    value={newShift.startTime}
                    onChange={(e) => setNewShift({ ...newShift, startTime: e.target.value })}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="end-time">End Time</Label>
                  <Input
                    id="end-time"
                    type="time"
                    value={newShift.endTime}
                    onChange={(e) => setNewShift({ ...newShift, endTime: e.target.value })}
                  />
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddShiftOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddShift}>Add Shift</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Edit Shift Dialog */}
        <Dialog open={isEditShiftOpen} onOpenChange={setIsEditShiftOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit Shift</DialogTitle>
            </DialogHeader>
            {currentShift && (
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <Label htmlFor="edit-staff">Staff Member</Label>
                  <Select
                    value={currentShift.staffId}
                    onValueChange={(value) =>
                      setCurrentShift({
                        ...currentShift,
                        staffId: value,
                        staffName: staffMembers.find((s) => s.id === value)?.name || "",
                        role: staffMembers.find((s) => s.id === value)?.role || ""
                      })
                    }
                  >
                    <SelectTrigger id="edit-staff">
                      <SelectValue placeholder="Select staff member" />
                    </SelectTrigger>
                    <SelectContent>
                      {staffMembers.map((staff) => (
                        <SelectItem key={staff.id} value={staff.id}>
                          {staff.name} ({staff.position})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="edit-day">Day</Label>
                  <Select
                    value={currentShift.day}
                    onValueChange={(value) => setCurrentShift({ ...currentShift, day: value })}
                  >
                    <SelectTrigger id="edit-day">
                      <SelectValue placeholder="Select day" />
                    </SelectTrigger>
                    <SelectContent>
                      {daysOfWeek.map((day) => (
                        <SelectItem key={day} value={day}>
                          {day}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="edit-start-time">Start Time</Label>
                    <Input
                      id="edit-start-time"
                      type="time"
                      value={currentShift.startTime}
                      onChange={(e) =>
                        setCurrentShift({ ...currentShift, startTime: e.target.value })
                      }
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="edit-end-time">End Time</Label>
                    <Input
                      id="edit-end-time"
                      type="time"
                      value={currentShift.endTime}
                      onChange={(e) =>
                        setCurrentShift({ ...currentShift, endTime: e.target.value })
                      }
                    />
                  </div>
                </div>
              </div>
            )}
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEditShiftOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleEditShift}>Save Changes</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </Layout>
  );
};

export default Schedule;