/**
 * Orders state slice for RestroManage
 * Manages active orders queue, order history, status tracking, payment processing, and customer information
 */

import { StateCreator } from 'zustand';
import { 
  OrdersState,
  Order,
  OrderItem,
  PaymentState,
  Customer,
  KitchenDisplayOrder,
  FilterState
} from '@/types/store';
import { orderApi } from '@/services/apiService';
import logger from '@/utils/logger';

export interface OrdersSlice extends OrdersState {
  // State
  activeOrders: Order[];
  orderHistory: Order[];
  ordersQueue: Order[];
  selectedOrder: Order | null;
  ordersFilters: FilterState;
  paymentStates: Map<string, PaymentState>;
  customerInfo: Map<string, Customer>;
  kitchenDisplay: KitchenDisplayOrder[];
  isOrdersLoading: boolean;
  ordersError: string | null;
  lastOrdersUpdate: Date | null;

  // Actions
  initializeOrders: (restaurantId: string) => Promise<void>;
  createOrder: (order: Omit<Order, 'id' | 'timestamp'>) => Promise<void>;
  updateOrder: (orderId: string, updates: Partial<Order>) => Promise<void>;
  cancelOrder: (orderId: string, reason: string) => Promise<void>;
  addOrderItem: (orderId: string, item: Omit<OrderItem, 'id'>) => Promise<void>;
  updateOrderItem: (orderId: string, itemId: string, updates: Partial<OrderItem>) => Promise<void>;
  removeOrderItem: (orderId: string, itemId: string) => Promise<void>;
  updateOrderStatus: (orderId: string, status: Order['status']) => Promise<void>;
  processPayment: (orderId: string, method: string, amount: number) => Promise<void>;
  setSelectedOrder: (order: Order | null) => void;
  updateCustomerInfo: (customerId: string, info: Partial<Customer>) => void;
  updateOrdersFilters: (filters: Partial<FilterState>) => void;
  refreshKitchenDisplay: () => void;
  syncOrders: () => Promise<void>;
  resetOrders: () => void;
  clearOrdersData: (restaurantId: string) => void;
}

const initialOrdersFilters: FilterState = {
  searchQuery: '',
  dateRange: {
    from: new Date(),
    to: new Date(),
  },
  categories: [],
  status: ['pending', 'preparing', 'ready'],
};

export const createOrdersSlice: StateCreator<
  OrdersSlice,
  [],
  [],
  OrdersSlice
> = (set, get) => ({
  // Initial state
  activeOrders: [],
  orderHistory: [],
  ordersQueue: [],
  selectedOrder: null,
  ordersFilters: initialOrdersFilters,
  paymentStates: new Map(),
  customerInfo: new Map(),
  kitchenDisplay: [],
  isOrdersLoading: false,
  ordersError: null,
  lastOrdersUpdate: null,

  // Actions
  initializeOrders: async (restaurantId: string) => {
    try {
      set((state) => {
        state.isOrdersLoading = true;
        state.ordersError = null;
      });

      logger.info('Initializing orders', 'OrdersSlice', { restaurantId });

      // Fetch orders from API
      const [activeOrdersData, orderHistoryData] = await Promise.allSettled([
        orderApi.getOrders(restaurantId, 'pending,preparing,ready'),
        orderApi.getOrders(restaurantId, 'served,paid,cancelled'),
      ]);

      set((state) => {
        // Set active orders
        if (activeOrdersData.status === 'fulfilled') {
          state.activeOrders = activeOrdersData.value.map(transformApiOrderToOrder);
          state.ordersQueue = state.activeOrders.filter(order => 
            order.status === 'pending' || order.status === 'preparing'
          );
        }

        // Set order history
        if (orderHistoryData.status === 'fulfilled') {
          state.orderHistory = orderHistoryData.value.map(transformApiOrderToOrder);
        }

        state.isOrdersLoading = false;
        state.lastOrdersUpdate = new Date();
      });

      // Update kitchen display
      get().refreshKitchenDisplay();

      logger.info('Orders initialized successfully', 'OrdersSlice', { 
        restaurantId,
        activeCount: get().activeOrders.length,
        historyCount: get().orderHistory.length
      });
    } catch (error) {
      set((state) => {
        state.isOrdersLoading = false;
        state.ordersError = error instanceof Error ? error.message : 'Failed to initialize orders';
      });

      logger.error('Failed to initialize orders', 'OrdersSlice', { error, restaurantId });
      throw error;
    }
  },

  createOrder: async (order: Omit<Order, 'id' | 'timestamp'>) => {
    const restaurantId = get().currentRestaurantId;
    if (!restaurantId) {
      logger.warn('Cannot create order without restaurant ID', 'OrdersSlice');
      return;
    }

    try {
      logger.info('Creating order', 'OrdersSlice', { tableId: order.tableId, itemCount: order.items.length });

      // Create order via API
      const newOrderData = await orderApi.createOrder({
        restaurant_id: restaurantId,
        table_id: order.tableId,
        customer_id: order.customerId,
        items: order.items.map(item => ({
          menu_item_id: item.menuItemId,
          quantity: item.quantity,
          price: item.price,
          modifications: item.modifications,
          notes: item.notes,
        })),
        total_amount: order.totalAmount,
        tax: order.tax,
        discount: order.discount,
        notes: order.notes,
        staff_id: order.staffId,
      });

      const newOrder: Order = {
        ...order,
        id: newOrderData.id,
        timestamp: new Date(newOrderData.created_at),
        status: 'pending',
        paymentStatus: 'pending',
      };

      set((state) => {
        state.activeOrders.push(newOrder);
        state.ordersQueue.push(newOrder);
        state.lastOrdersUpdate = new Date();
      });

      // Initialize payment state
      set((state) => {
        state.paymentStates.set(newOrder.id, {
          orderId: newOrder.id,
          status: 'idle',
          amount: newOrder.totalAmount,
        });
      });

      // Update kitchen display
      get().refreshKitchenDisplay();

      logger.info('Order created successfully', 'OrdersSlice', { orderId: newOrder.id, tableId: order.tableId });
    } catch (error) {
      logger.error('Failed to create order', 'OrdersSlice', { error, order });
      throw error;
    }
  },

  updateOrder: async (orderId: string, updates: Partial<Order>) => {
    try {
      logger.info('Updating order', 'OrdersSlice', { orderId, updates });

      // Update order via API
      await orderApi.updateOrder(orderId, updates);

      set((state) => {
        // Update in active orders
        const activeIndex = state.activeOrders.findIndex(order => order.id === orderId);
        if (activeIndex !== -1) {
          state.activeOrders[activeIndex] = { ...state.activeOrders[activeIndex], ...updates };
        }

        // Update in order history
        const historyIndex = state.orderHistory.findIndex(order => order.id === orderId);
        if (historyIndex !== -1) {
          state.orderHistory[historyIndex] = { ...state.orderHistory[historyIndex], ...updates };
        }

        // Update in queue
        const queueIndex = state.ordersQueue.findIndex(order => order.id === orderId);
        if (queueIndex !== -1) {
          state.ordersQueue[queueIndex] = { ...state.ordersQueue[queueIndex], ...updates };
        }

        // Update selected order if it's the same one
        if (state.selectedOrder?.id === orderId) {
          state.selectedOrder = { ...state.selectedOrder, ...updates };
        }

        state.lastOrdersUpdate = new Date();
      });

      // Update kitchen display if status changed
      if (updates.status) {
        get().refreshKitchenDisplay();
      }

      logger.info('Order updated successfully', 'OrdersSlice', { orderId });
    } catch (error) {
      logger.error('Failed to update order', 'OrdersSlice', { error, orderId });
      throw error;
    }
  },

  cancelOrder: async (orderId: string, reason: string) => {
    try {
      logger.info('Cancelling order', 'OrdersSlice', { orderId, reason });

      await get().updateOrder(orderId, { 
        status: 'cancelled',
        notes: `Cancelled: ${reason}`,
      });

      // Move from active to history
      set((state) => {
        const orderIndex = state.activeOrders.findIndex(order => order.id === orderId);
        if (orderIndex !== -1) {
          const cancelledOrder = state.activeOrders[orderIndex];
          state.orderHistory.unshift(cancelledOrder);
          state.activeOrders.splice(orderIndex, 1);
        }

        // Remove from queue
        state.ordersQueue = state.ordersQueue.filter(order => order.id !== orderId);
      });

      logger.info('Order cancelled successfully', 'OrdersSlice', { orderId, reason });
    } catch (error) {
      logger.error('Failed to cancel order', 'OrdersSlice', { error, orderId, reason });
      throw error;
    }
  },

  addOrderItem: async (orderId: string, item: Omit<OrderItem, 'id'>) => {
    try {
      const newItem: OrderItem = {
        ...item,
        id: `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      };

      set((state) => {
        const updateOrderItems = (orders: Order[]) => {
          const orderIndex = orders.findIndex(order => order.id === orderId);
          if (orderIndex !== -1) {
            orders[orderIndex].items.push(newItem);
            // Recalculate total
            orders[orderIndex].totalAmount = orders[orderIndex].items.reduce(
              (sum, item) => sum + (item.price * item.quantity), 0
            );
          }
        };

        updateOrderItems(state.activeOrders);
        updateOrderItems(state.ordersQueue);
        
        if (state.selectedOrder?.id === orderId) {
          state.selectedOrder.items.push(newItem);
          state.selectedOrder.totalAmount = state.selectedOrder.items.reduce(
            (sum, item) => sum + (item.price * item.quantity), 0
          );
        }

        state.lastOrdersUpdate = new Date();
      });

      logger.info('Order item added', 'OrdersSlice', { orderId, itemId: newItem.id, name: item.name });
    } catch (error) {
      logger.error('Failed to add order item', 'OrdersSlice', { error, orderId, item });
      throw error;
    }
  },

  updateOrderItem: async (orderId: string, itemId: string, updates: Partial<OrderItem>) => {
    try {
      set((state) => {
        const updateOrderItems = (orders: Order[]) => {
          const orderIndex = orders.findIndex(order => order.id === orderId);
          if (orderIndex !== -1) {
            const itemIndex = orders[orderIndex].items.findIndex(item => item.id === itemId);
            if (itemIndex !== -1) {
              orders[orderIndex].items[itemIndex] = { ...orders[orderIndex].items[itemIndex], ...updates };
              // Recalculate total
              orders[orderIndex].totalAmount = orders[orderIndex].items.reduce(
                (sum, item) => sum + (item.price * item.quantity), 0
              );
            }
          }
        };

        updateOrderItems(state.activeOrders);
        updateOrderItems(state.ordersQueue);
        
        if (state.selectedOrder?.id === orderId) {
          const itemIndex = state.selectedOrder.items.findIndex(item => item.id === itemId);
          if (itemIndex !== -1) {
            state.selectedOrder.items[itemIndex] = { ...state.selectedOrder.items[itemIndex], ...updates };
            state.selectedOrder.totalAmount = state.selectedOrder.items.reduce(
              (sum, item) => sum + (item.price * item.quantity), 0
            );
          }
        }

        state.lastOrdersUpdate = new Date();
      });

      logger.info('Order item updated', 'OrdersSlice', { orderId, itemId, updates });
    } catch (error) {
      logger.error('Failed to update order item', 'OrdersSlice', { error, orderId, itemId });
      throw error;
    }
  },

  removeOrderItem: async (orderId: string, itemId: string) => {
    try {
      set((state) => {
        const updateOrderItems = (orders: Order[]) => {
          const orderIndex = orders.findIndex(order => order.id === orderId);
          if (orderIndex !== -1) {
            orders[orderIndex].items = orders[orderIndex].items.filter(item => item.id !== itemId);
            // Recalculate total
            orders[orderIndex].totalAmount = orders[orderIndex].items.reduce(
              (sum, item) => sum + (item.price * item.quantity), 0
            );
          }
        };

        updateOrderItems(state.activeOrders);
        updateOrderItems(state.ordersQueue);
        
        if (state.selectedOrder?.id === orderId) {
          state.selectedOrder.items = state.selectedOrder.items.filter(item => item.id !== itemId);
          state.selectedOrder.totalAmount = state.selectedOrder.items.reduce(
            (sum, item) => sum + (item.price * item.quantity), 0
          );
        }

        state.lastOrdersUpdate = new Date();
      });

      logger.info('Order item removed', 'OrdersSlice', { orderId, itemId });
    } catch (error) {
      logger.error('Failed to remove order item', 'OrdersSlice', { error, orderId, itemId });
      throw error;
    }
  },

  updateOrderStatus: async (orderId: string, status: Order['status']) => {
    await get().updateOrder(orderId, { status });

    // Move to history if completed
    if (status === 'served' || status === 'paid' || status === 'cancelled') {
      set((state) => {
        const orderIndex = state.activeOrders.findIndex(order => order.id === orderId);
        if (orderIndex !== -1) {
          const completedOrder = state.activeOrders[orderIndex];
          state.orderHistory.unshift(completedOrder);
          state.activeOrders.splice(orderIndex, 1);
        }

        // Remove from queue
        state.ordersQueue = state.ordersQueue.filter(order => order.id !== orderId);
      });
    }
  },

  processPayment: async (orderId: string, method: string, amount: number) => {
    try {
      logger.info('Processing payment', 'OrdersSlice', { orderId, method, amount });

      // Update payment state to processing
      set((state) => {
        state.paymentStates.set(orderId, {
          orderId,
          status: 'processing',
          method,
          amount,
        });
      });

      // Simulate payment processing (in real app, this would call payment API)
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Update payment state to success
      set((state) => {
        state.paymentStates.set(orderId, {
          orderId,
          status: 'success',
          method,
          amount,
          transactionId: `txn_${Date.now()}`,
        });
      });

      // Update order status
      await get().updateOrder(orderId, { 
        paymentStatus: 'completed',
        paymentMethod: method,
        status: 'paid',
      });

      logger.info('Payment processed successfully', 'OrdersSlice', { orderId, method, amount });
    } catch (error) {
      // Update payment state to error
      set((state) => {
        state.paymentStates.set(orderId, {
          orderId,
          status: 'error',
          method,
          amount,
          error: error instanceof Error ? error.message : 'Payment failed',
        });
      });

      logger.error('Failed to process payment', 'OrdersSlice', { error, orderId, method, amount });
      throw error;
    }
  },

  setSelectedOrder: (order: Order | null) => {
    set((state) => {
      state.selectedOrder = order;
    });
  },

  updateCustomerInfo: (customerId: string, info: Partial<Customer>) => {
    set((state) => {
      const existing = state.customerInfo.get(customerId);
      state.customerInfo.set(customerId, { ...existing, ...info } as Customer);
    });
  },

  updateOrdersFilters: (filters: Partial<FilterState>) => {
    set((state) => {
      state.ordersFilters = { ...state.ordersFilters, ...filters };
    });
  },

  refreshKitchenDisplay: () => {
    set((state) => {
      state.kitchenDisplay = state.activeOrders
        .filter(order => order.status === 'pending' || order.status === 'preparing')
        .map(order => ({
          orderId: order.id,
          tableId: order.tableId,
          items: order.items,
          priority: order.estimatedTime && order.estimatedTime < 15 ? 'high' : 'normal',
          estimatedTime: order.estimatedTime || 30,
          status: order.status === 'pending' ? 'pending' : 'preparing',
        }))
        .sort((a, b) => {
          // Sort by priority first, then by estimated time
          if (a.priority !== b.priority) {
            return a.priority === 'high' ? -1 : 1;
          }
          return a.estimatedTime - b.estimatedTime;
        });
    });
  },

  syncOrders: async () => {
    const restaurantId = get().currentRestaurantId;
    if (!restaurantId) return;

    await get().initializeOrders(restaurantId);
  },

  resetOrders: () => {
    set((state) => {
      state.activeOrders = [];
      state.orderHistory = [];
      state.ordersQueue = [];
      state.selectedOrder = null;
      state.ordersFilters = initialOrdersFilters;
      state.paymentStates.clear();
      state.customerInfo.clear();
      state.kitchenDisplay = [];
      state.isOrdersLoading = false;
      state.ordersError = null;
      state.lastOrdersUpdate = null;
    });
  },

  clearOrdersData: (restaurantId: string) => {
    set((state) => {
      if (state.currentRestaurantId === restaurantId) {
        state.activeOrders = [];
        state.orderHistory = [];
        state.ordersQueue = [];
        state.selectedOrder = null;
        state.paymentStates.clear();
        state.customerInfo.clear();
        state.kitchenDisplay = [];
      }
    });
  },
});

// Helper function to transform API order data to Order type
function transformApiOrderToOrder(apiOrder: any): Order {
  return {
    id: apiOrder.id,
    tableId: apiOrder.table_id,
    customerId: apiOrder.customer_id,
    items: apiOrder.items?.map((item: any) => ({
      id: item.id,
      menuItemId: item.menu_item_id,
      name: item.name,
      quantity: item.quantity,
      price: item.price,
      modifications: item.modifications || [],
      notes: item.notes,
    })) || [],
    status: apiOrder.status,
    totalAmount: apiOrder.total_amount,
    tax: apiOrder.tax || 0,
    discount: apiOrder.discount || 0,
    paymentMethod: apiOrder.payment_method,
    paymentStatus: apiOrder.payment_status || 'pending',
    timestamp: new Date(apiOrder.created_at),
    estimatedTime: apiOrder.estimated_time,
    notes: apiOrder.notes,
    staffId: apiOrder.staff_id,
  };
}
