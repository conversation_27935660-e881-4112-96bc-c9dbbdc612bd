from fastapi import APIRouter, Depends, HTTPException, status
from typing import List, Optional
from datetime import datetime
from app.models.discounts import (
    PromoCode, PromoCodeCreate, PromoCodeUpdate, Campaign, CampaignCreate,
    DiscountValidationResult, PromoCodeUsage, PromoCodeValidationRequest,
    DiscountApplicationRequest, DiscountRemovalRequest
)
from app.services.discount_service import DiscountService
from app.utils.storage import get_all, get_by_id, create, update, delete, query
from app.utils.auth import get_current_active_user, check_manager_role

router = APIRouter(prefix="/discounts", tags=["Discounts"])

# Promo Code Management
@router.get("/promo-codes", response_model=List[PromoCode])
async def get_promo_codes(current_user = Depends(get_current_active_user)):
    """Get all promo codes"""
    return get_all("promo_codes")

@router.get("/promo-codes/active", response_model=List[PromoCode])
async def get_active_promo_codes():
    """Get active promo codes (public endpoint for EPOS)"""
    all_codes = get_all("promo_codes")
    now = datetime.now()

    # Filter for active codes that are currently valid
    active_codes = [
        code for code in all_codes
        if code.get("is_active", True) and
           datetime.fromisoformat(code["start_date"]) <= now <= datetime.fromisoformat(code["end_date"])
    ]

    return active_codes

@router.get("/promo-codes/{promo_code_id}", response_model=PromoCode)
async def get_promo_code(promo_code_id: str, current_user = Depends(get_current_active_user)):
    """Get a promo code by ID"""
    promo_code = get_by_id("promo_codes", promo_code_id)
    if not promo_code:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Promo code not found"
        )
    return promo_code

@router.post("/promo-codes", response_model=PromoCode)
async def create_promo_code(
    promo_code: PromoCodeCreate,
    current_user = Depends(get_current_active_user)
):
    """Create a new promo code"""
    check_manager_role(current_user)
    
    # Check if code already exists
    existing_codes = query("promo_codes", lambda x: x.get("code") == promo_code.code.upper())
    if existing_codes:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Promo code already exists"
        )
    
    promo_code_dict = promo_code.model_dump()
    promo_code_dict["code"] = promo_code_dict["code"].upper()
    promo_code_dict["usage_count"] = 0
    
    return create("promo_codes", promo_code_dict)

@router.put("/promo-codes/{promo_code_id}", response_model=PromoCode)
async def update_promo_code(
    promo_code_id: str,
    promo_code_update: PromoCodeUpdate,
    current_user = Depends(get_current_active_user)
):
    """Update a promo code"""
    check_manager_role(current_user)
    
    existing_promo_code = get_by_id("promo_codes", promo_code_id)
    if not existing_promo_code:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Promo code not found"
        )
    
    update_data = promo_code_update.model_dump(exclude_unset=True)
    if "code" in update_data:
        update_data["code"] = update_data["code"].upper()
    
    return update("promo_codes", promo_code_id, update_data)

@router.delete("/promo-codes/{promo_code_id}")
async def delete_promo_code(
    promo_code_id: str,
    current_user = Depends(get_current_active_user)
):
    """Delete a promo code"""
    check_manager_role(current_user)
    
    promo_code = get_by_id("promo_codes", promo_code_id)
    if not promo_code:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Promo code not found"
        )
    
    delete("promo_codes", promo_code_id)
    return {"message": "Promo code deleted successfully"}

# Promo Code Validation and Application
@router.post("/validate-promo-code")
async def validate_promo_code(
    request: PromoCodeValidationRequest,
    current_user = Depends(get_current_active_user)
):
    """Validate a promo code for an order"""
    try:
        result = DiscountService.validate_promo_code(
            request.code, request.order_total, request.order_items, request.customer_id
        )
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.post("/apply-discount")
async def apply_discount(
    request: DiscountApplicationRequest,
    current_user = Depends(get_current_active_user)
):
    """Apply a discount to an order"""
    try:
        result = DiscountService.apply_discount_to_order(
            request.order_id, request.promo_code, request.customer_id
        )
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.post("/remove-discount")
async def remove_discount(
    request: DiscountRemovalRequest,
    current_user = Depends(get_current_active_user)
):
    """Remove a discount from an order"""
    try:
        result = DiscountService.remove_discount_from_order(request.order_id, request.promo_code)
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

# Campaign Management
@router.get("/campaigns", response_model=List[Campaign])
async def get_campaigns(current_user = Depends(get_current_active_user)):
    """Get all campaigns"""
    return get_all("campaigns")

@router.get("/campaigns/{campaign_id}", response_model=Campaign)
async def get_campaign(campaign_id: str, current_user = Depends(get_current_active_user)):
    """Get a campaign by ID"""
    campaign = get_by_id("campaigns", campaign_id)
    if not campaign:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Campaign not found"
        )
    return campaign

@router.post("/campaigns", response_model=Campaign)
async def create_campaign(
    campaign: CampaignCreate,
    current_user = Depends(get_current_active_user)
):
    """Create a new campaign"""
    check_manager_role(current_user)
    
    campaign_dict = campaign.model_dump()
    campaign_dict["total_usage"] = 0
    campaign_dict["total_discount_given"] = 0.0
    
    return create("campaigns", campaign_dict)

@router.put("/campaigns/{campaign_id}", response_model=Campaign)
async def update_campaign(
    campaign_id: str,
    campaign_update: CampaignCreate,
    current_user = Depends(get_current_active_user)
):
    """Update a campaign"""
    check_manager_role(current_user)
    
    existing_campaign = get_by_id("campaigns", campaign_id)
    if not existing_campaign:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Campaign not found"
        )
    
    return update("campaigns", campaign_id, campaign_update.model_dump())

@router.delete("/campaigns/{campaign_id}")
async def delete_campaign(
    campaign_id: str,
    current_user = Depends(get_current_active_user)
):
    """Delete a campaign"""
    check_manager_role(current_user)
    
    campaign = get_by_id("campaigns", campaign_id)
    if not campaign:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Campaign not found"
        )
    
    delete("campaigns", campaign_id)
    return {"message": "Campaign deleted successfully"}

# Usage Analytics
@router.get("/promo-codes/{promo_code_id}/usage", response_model=List[PromoCodeUsage])
async def get_promo_code_usage(
    promo_code_id: str,
    current_user = Depends(get_current_active_user)
):
    """Get usage history for a promo code"""
    check_manager_role(current_user)
    
    usage_records = query(
        "promo_code_usage",
        lambda x: x.get("promo_code_id") == promo_code_id
    )
    return usage_records

@router.get("/analytics/discount-summary")
async def get_discount_analytics(current_user = Depends(get_current_active_user)):
    """Get discount analytics summary"""
    check_manager_role(current_user)
    
    # Get all promo codes
    promo_codes = get_all("promo_codes")
    
    # Get all usage records
    usage_records = get_all("promo_code_usage")
    
    # Calculate analytics
    total_discounts_given = sum(record.get("discount_amount", 0) for record in usage_records)
    total_usage_count = len(usage_records)
    
    # Active promo codes
    now = datetime.now()
    active_codes = [
        code for code in promo_codes
        if code.get("is_active", True) and
           datetime.fromisoformat(code["start_date"]) <= now <= datetime.fromisoformat(code["end_date"])
    ]
    
    # Most used promo codes
    code_usage = {}
    for record in usage_records:
        code_id = record.get("promo_code_id")
        if code_id:
            code_usage[code_id] = code_usage.get(code_id, 0) + 1
    
    most_used = sorted(code_usage.items(), key=lambda x: x[1], reverse=True)[:5]
    
    return {
        "total_promo_codes": len(promo_codes),
        "active_promo_codes": len(active_codes),
        "total_discounts_given": total_discounts_given,
        "total_usage_count": total_usage_count,
        "most_used_codes": most_used,
        "average_discount_per_use": total_discounts_given / total_usage_count if total_usage_count > 0 else 0
    }

# Gift Card Management
@router.get("/gift-cards")
async def get_gift_cards(current_user = Depends(get_current_active_user)):
    """Get all gift cards"""
    check_manager_role(current_user)
    return get_all("gift_cards")

@router.post("/gift-cards")
async def create_gift_card(
    request: dict,
    current_user = Depends(get_current_active_user)
):
    """Generate a new gift card"""
    check_manager_role(current_user)

    amount = request.get("amount", 0)
    if amount <= 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Gift card amount must be greater than 0"
        )

    # Generate unique gift card code
    import random
    import string
    code = "GC-" + ''.join(random.choices(string.ascii_uppercase + string.digits, k=6))

    # Ensure code is unique
    existing_cards = query("gift_cards", lambda x: x.get("code") == code)
    while existing_cards:
        code = "GC-" + ''.join(random.choices(string.ascii_uppercase + string.digits, k=6))
        existing_cards = query("gift_cards", lambda x: x.get("code") == code)

    gift_card_data = {
        "code": code,
        "amount": amount,
        "balance": amount,
        "status": "active",
        "created_at": datetime.now().isoformat(),
        "used_at": None,
        "used_by": None
    }

    return create("gift_cards", gift_card_data)

@router.post("/gift-cards/{card_id}/deactivate")
async def deactivate_gift_card(
    card_id: str,
    current_user = Depends(get_current_active_user)
):
    """Deactivate a gift card"""
    check_manager_role(current_user)

    gift_card = get_by_id("gift_cards", card_id)
    if not gift_card:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Gift card not found"
        )

    if gift_card["status"] != "active":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Gift card is not active"
        )

    updated_card = update("gift_cards", card_id, {
        "status": "deactivated",
        "used_at": datetime.now().isoformat()
    })

    return updated_card
