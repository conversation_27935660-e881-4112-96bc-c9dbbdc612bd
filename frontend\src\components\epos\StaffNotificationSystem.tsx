import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { 
  Bell, 
  ChefHat, 
  Users, 
  Table, 
  Clock, 
  CheckCircle,
  X,
  AlertTriangle,
  RotateCcw
} from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { useStaffPIN } from "@/contexts/StaffPINContext";
import { toast } from "@/components/ui/sonner";
import apiService from "@/services/apiService";
import logger from "@/utils/logger";

interface StaffNotification {
  id: string;
  type: 'new_order' | 'order_ready' | 'table_cleaning' | 'customer_seated' | 'payment_completed';
  title: string;
  message: string;
  table_number?: number;
  order_number?: string;
  priority: 'low' | 'medium' | 'high';
  target_roles: string[];
  created_at: string;
  created_by: string;
  acknowledged_by: string[];
  auto_dismiss_after?: number; // minutes
}

interface StaffNotificationSystemProps {
  isMinimized: boolean;
  onToggleMinimize: () => void;
}

const StaffNotificationSystem: React.FC<StaffNotificationSystemProps> = ({ 
  isMinimized, 
  onToggleMinimize 
}) => {
  const { currentRestaurant } = useAuth();
  const { activeStaff } = useStaffPIN();
  const [notifications, setNotifications] = useState<StaffNotification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);

  // Load notifications when component mounts
  useEffect(() => {
    if (currentRestaurant && activeStaff) {
      loadNotifications();
      
      // Set up polling for new notifications every 30 seconds
      const interval = setInterval(loadNotifications, 30000);
      return () => clearInterval(interval);
    }
  }, [currentRestaurant, activeStaff]);

  // Calculate unread count
  useEffect(() => {
    if (activeStaff) {
      const unread = notifications.filter(n => 
        !n.acknowledged_by.includes(activeStaff.id) &&
        n.target_roles.includes(activeStaff.role)
      ).length;
      setUnreadCount(unread);
    }
  }, [notifications, activeStaff]);

  const loadNotifications = async () => {
    if (!currentRestaurant || !activeStaff) return;

    try {
      // Try to load from API first, fallback to mock data
      try {
        const response = await apiService.apiRequest(
          `/mvc/notifications?restaurant_id=${currentRestaurant.id}&role=${activeStaff.role}`
        );
        
        const typedResponse = response as { success: boolean; data?: any[] };
        if (typedResponse.success && typedResponse.data) {
          setNotifications(typedResponse.data);
          logger.debug('Notifications loaded from API', 'StaffNotificationSystem', {
            count: typedResponse.data.length
          });
        } else {
          throw new Error('Invalid API response');
        }
      } catch (apiError) {
        // Fallback to mock data for demo
        const mockNotifications = generateMockNotifications();
        setNotifications(mockNotifications);
      }
    } catch (error) {
      logger.error('Failed to load notifications', 'StaffNotificationSystem', { 
        error: error.message 
      });
    }
  };

  const generateMockNotifications = (): StaffNotification[] => {
    const now = new Date();
    const notifications: StaffNotification[] = [];

    // Generate different types of notifications based on staff role
    if (activeStaff?.role === 'chef') {
      notifications.push({
        id: 'notif-1',
        type: 'new_order',
        title: 'New Order - Table 5',
        message: 'Order #ORD123: 2x Margherita Pizza, 1x Caesar Salad',
        table_number: 5,
        order_number: 'ORD123',
        priority: 'high',
        target_roles: ['chef', 'kitchen'],
        created_at: new Date(now.getTime() - 5 * 60000).toISOString(),
        created_by: 'staff_001',
        acknowledged_by: []
      });
    }

    if (activeStaff?.role === 'staff' || activeStaff?.role === 'manager') {
      notifications.push({
        id: 'notif-2',
        type: 'table_cleaning',
        title: 'Table 3 Needs Cleaning',
        message: 'Customer has left, table requires cleaning before next seating',
        table_number: 3,
        priority: 'medium',
        target_roles: ['staff', 'manager'],
        created_at: new Date(now.getTime() - 10 * 60000).toISOString(),
        created_by: 'system',
        acknowledged_by: []
      });

      notifications.push({
        id: 'notif-3',
        type: 'customer_seated',
        title: 'New Customers - Table 7',
        message: 'Party of 4 seated, allergen alert: nuts',
        table_number: 7,
        priority: 'medium',
        target_roles: ['staff', 'manager'],
        created_at: new Date(now.getTime() - 15 * 60000).toISOString(),
        created_by: 'staff_002',
        acknowledged_by: []
      });
    }

    return notifications;
  };

  const acknowledgeNotification = async (notificationId: string) => {
    if (!activeStaff) return;

    try {
      // Update locally first
      setNotifications(prev => prev.map(n => 
        n.id === notificationId 
          ? { ...n, acknowledged_by: [...n.acknowledged_by, activeStaff.id] }
          : n
      ));

      // Try to update on server
      await apiService.apiRequest(`/mvc/notifications/${notificationId}/acknowledge`, {
        method: 'PATCH',
        body: JSON.stringify({ staff_id: activeStaff.id })
      });

      logger.debug('Notification acknowledged', 'StaffNotificationSystem', { 
        notificationId,
        staffId: activeStaff.id 
      });
    } catch (error) {
      logger.warn('Failed to acknowledge notification on server', 'StaffNotificationSystem', { 
        error: error.message 
      });
    }
  };

  const dismissNotification = (notificationId: string) => {
    setNotifications(prev => prev.filter(n => n.id !== notificationId));
  };

  const getNotificationIcon = (type: StaffNotification['type']) => {
    switch (type) {
      case 'new_order': return <ChefHat className="w-4 h-4" />;
      case 'order_ready': return <CheckCircle className="w-4 h-4" />;
      case 'table_cleaning': return <RotateCcw className="w-4 h-4" />;
      case 'customer_seated': return <Users className="w-4 h-4" />;
      case 'payment_completed': return <CheckCircle className="w-4 h-4" />;
      default: return <Bell className="w-4 h-4" />;
    }
  };

  const getPriorityColor = (priority: StaffNotification['priority']) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffMinutes < 1) return 'Just now';
    if (diffMinutes < 60) return `${diffMinutes}m ago`;
    
    const diffHours = Math.floor(diffMinutes / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    
    return date.toLocaleDateString();
  };

  // Filter notifications for current staff role
  const relevantNotifications = notifications.filter(n => 
    n.target_roles.includes(activeStaff?.role || '')
  );

  if (isMinimized) {
    return (
      <div className="fixed top-4 right-4 z-50">
        <Card className="w-16 shadow-lg border-2 border-orange-200 cursor-pointer" onClick={onToggleMinimize}>
          <CardContent className="p-3 text-center">
            <div className="relative">
              <Bell className="w-6 h-6 mx-auto text-orange-600" />
              {unreadCount > 0 && (
                <Badge className="absolute -top-2 -right-2 w-5 h-5 p-0 flex items-center justify-center bg-red-500 text-white text-xs">
                  {unreadCount > 9 ? '9+' : unreadCount}
                </Badge>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="fixed top-4 right-4 z-50">
      <Card className="w-80 h-96 shadow-xl border-2 border-orange-200 flex flex-col">
        <CardHeader className="pb-2 flex-shrink-0">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg flex items-center gap-2">
              <Bell className="w-5 h-5" />
              Staff Notifications
              {unreadCount > 0 && (
                <Badge className="bg-red-500 text-white">
                  {unreadCount}
                </Badge>
              )}
            </CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggleMinimize}
              className="h-6 w-6 p-0"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="flex-1 overflow-hidden p-0">
          <ScrollArea className="h-full px-4">
            {relevantNotifications.length === 0 ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-center">
                  <Bell className="w-8 h-8 mx-auto mb-2 text-gray-300" />
                  <p className="text-gray-500 text-sm">No notifications</p>
                </div>
              </div>
            ) : (
              <div className="space-y-3 py-4">
                {relevantNotifications.map((notification) => {
                  const isUnread = !notification.acknowledged_by.includes(activeStaff?.id || '');
                  
                  return (
                    <Card 
                      key={notification.id}
                      className={`transition-all ${isUnread ? 'ring-2 ring-orange-200 bg-orange-50' : ''}`}
                    >
                      <CardContent className="p-3">
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex items-center gap-2">
                            {getNotificationIcon(notification.type)}
                            <span className="font-medium text-sm">{notification.title}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Badge className={getPriorityColor(notification.priority)}>
                              {notification.priority}
                            </Badge>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => dismissNotification(notification.id)}
                              className="h-4 w-4 p-0"
                            >
                              <X className="w-3 h-3" />
                            </Button>
                          </div>
                        </div>
                        
                        <p className="text-sm text-gray-600 mb-2">
                          {notification.message}
                        </p>
                        
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2 text-xs text-gray-500">
                            <Clock className="w-3 h-3" />
                            <span>{formatTimeAgo(notification.created_at)}</span>
                            {notification.table_number && (
                              <>
                                <Table className="w-3 h-3 ml-2" />
                                <span>Table {notification.table_number}</span>
                              </>
                            )}
                          </div>
                          
                          {isUnread && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => acknowledgeNotification(notification.id)}
                              className="h-6 px-2 text-xs"
                            >
                              Mark Read
                            </Button>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            )}
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
};

export default StaffNotificationSystem;
