import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useAuth } from "@/contexts/AuthContext";
import { Clock, LogOut, Play, Pause, Coffee } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { toast } from "@/components/ui/sonner";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  ClockStatus, 
  StaffTimeTracking, 
  updateTimeEntryFromClockAction 
} from "@/services/timeEntryService";

const EnhancedStaffTimeClock = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const [currentTime, setCurrentTime] = useState(new Date());
  
  // Initialize time tracking state
  const [timeTracking, setTimeTracking] = useState<StaffTimeTracking>({
    clockStatus: 'clocked_out',
    lastClockIn: null,
    lastClockOut: null,
    breakStart: null,
    breakEnd: null,
    totalHoursToday: 0,
    totalBreakTime: 0
  });

  // Load time tracking data from localStorage on mount
  useEffect(() => {
    const savedTracking = localStorage.getItem(`timeTracking_${user?.id}`);
    if (savedTracking) {
      setTimeTracking(JSON.parse(savedTracking));
    }
  }, [user?.id]);

  // Save time tracking data to localStorage when it changes
  useEffect(() => {
    if (user?.id) {
      localStorage.setItem(`timeTracking_${user.id}`, JSON.stringify(timeTracking));
    }
  }, [timeTracking, user?.id]);

  // Update current time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
      
      // If clocked in, update total hours
      if (timeTracking.clockStatus === 'clocked_in' && timeTracking.lastClockIn) {
        const clockInTime = new Date(timeTracking.lastClockIn);
        const hoursWorked = (currentTime.getTime() - clockInTime.getTime()) / (1000 * 60 * 60);
        
        setTimeTracking(prev => ({
          ...prev,
          totalHoursToday: parseFloat(hoursWorked.toFixed(2))
        }));
      }
      
      // If on break, update break time
      if (timeTracking.clockStatus === 'on_break' && timeTracking.breakStart) {
        const breakStartTime = new Date(timeTracking.breakStart);
        const breakTime = (currentTime.getTime() - breakStartTime.getTime()) / (1000 * 60 * 60);
        
        setTimeTracking(prev => ({
          ...prev,
          totalBreakTime: parseFloat(breakTime.toFixed(2))
        }));
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [timeTracking, currentTime]);

  // Update time entry in the service when time tracking changes
  useEffect(() => {
    if (user?.id && (timeTracking.lastClockIn || timeTracking.lastClockOut)) {
      updateTimeEntryFromClockAction(user.id, timeTracking);
    }
  }, [timeTracking, user?.id]);

  const handleClockIn = () => {
    const now = new Date().toISOString();
    setTimeTracking(prev => ({
      ...prev,
      clockStatus: 'clocked_in',
      lastClockIn: now,
      lastClockOut: null
    }));
    toast.success("You have clocked in successfully!");
  };

  const handleClockOut = () => {
    const now = new Date().toISOString();
    setTimeTracking(prev => ({
      ...prev,
      clockStatus: 'clocked_out',
      lastClockOut: now,
      totalHoursToday: prev.totalHoursToday // Keep the accumulated hours
    }));
    toast.success("You have clocked out successfully!");
  };

  const handleStartBreak = () => {
    const now = new Date().toISOString();
    setTimeTracking(prev => ({
      ...prev,
      clockStatus: 'on_break',
      breakStart: now,
      breakEnd: null
    }));
    toast.success("Your break has started!");
  };

  const handleEndBreak = () => {
    const now = new Date().toISOString();
    setTimeTracking(prev => ({
      ...prev,
      clockStatus: 'clocked_in',
      breakEnd: now,
      totalBreakTime: 0 // Reset break time after ending break
    }));
    toast.success("Your break has ended!");
  };

  const handleLogout = () => {
    // If user is clocked in, ask for confirmation
    if (timeTracking.clockStatus === 'clocked_in') {
      if (window.confirm("You are still clocked in. Do you want to clock out before logging out?")) {
        handleClockOut();
      }
    }
    logout();
    navigate('/');
  };

  // Format time display
  const formatTime = (hours: number) => {
    const h = Math.floor(hours);
    const m = Math.floor((hours - h) * 60);
    return `${h}h ${m}m`;
  };

  // Get status badge color
  const getStatusColor = (status: ClockStatus) => {
    switch (status) {
      case 'clocked_in': return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200';
      case 'clocked_out': return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200';
      case 'on_break': return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  // Get status display text
  const getStatusText = (status: ClockStatus) => {
    switch (status) {
      case 'clocked_in': return 'Clocked In';
      case 'clocked_out': return 'Clocked Out';
      case 'on_break': return 'On Break';
      default: return 'Unknown';
    }
  };

  if (!user) {
    return null;
  }

  // Default values for assigned hours and available days
  const assignedHours = user.assignedHours || 35;
  const availableDays = user.availableDays || 'Mon, Tue, Wed, Thu, Fri';

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg font-medium">Staff Time Clock</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col space-y-4">
          <div className="flex flex-col items-center justify-center p-4 bg-muted rounded-lg">
            <div className="text-sm text-muted-foreground mb-1">Current Time</div>
            <div className="text-2xl font-bold">
              {currentTime.toLocaleTimeString()}
            </div>
            <div className="text-sm text-muted-foreground mt-1">
              {currentTime.toLocaleDateString()}
            </div>
          </div>

          <div className="flex flex-col space-y-2">
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium">{user.name}</div>
                <div className="text-sm text-muted-foreground">{user.position}</div>
              </div>
              <Badge className={getStatusColor(timeTracking.clockStatus as ClockStatus)}>
                {getStatusText(timeTracking.clockStatus as ClockStatus)}
              </Badge>
            </div>
          </div>

          <Separator />

          <div className="grid grid-cols-2 gap-2 text-sm">
            <div className="text-muted-foreground">Assigned Hours:</div>
            <div className="font-medium text-right">{assignedHours}h / week</div>
            
            <div className="text-muted-foreground">Available Days:</div>
            <div className="font-medium text-right">{availableDays}</div>
            
            <div className="text-muted-foreground">Hours Today:</div>
            <div className="font-medium text-right">{formatTime(timeTracking.totalHoursToday)}</div>
            
            {timeTracking.clockStatus === 'on_break' && (
              <>
                <div className="text-muted-foreground">Break Time:</div>
                <div className="font-medium text-right">{formatTime(timeTracking.totalBreakTime)}</div>
              </>
            )}
          </div>

          <div className="grid grid-cols-2 gap-2 mt-2">
            {timeTracking.clockStatus === 'clocked_out' ? (
              <Button 
                className="w-full" 
                onClick={handleClockIn}
              >
                <Play className="mr-2 h-4 w-4" /> Clock In
              </Button>
            ) : (
              <Button 
                className="w-full" 
                variant="destructive" 
                onClick={handleClockOut}
              >
                <Pause className="mr-2 h-4 w-4" /> Clock Out
              </Button>
            )}

            {timeTracking.clockStatus === 'clocked_in' ? (
              <Button 
                className="w-full" 
                variant="outline" 
                onClick={handleStartBreak}
              >
                <Coffee className="mr-2 h-4 w-4" /> Start Break
              </Button>
            ) : timeTracking.clockStatus === 'on_break' ? (
              <Button 
                className="w-full" 
                variant="outline" 
                onClick={handleEndBreak}
              >
                <Clock className="mr-2 h-4 w-4" /> End Break
              </Button>
            ) : (
              <Button 
                className="w-full" 
                variant="outline" 
                disabled
              >
                <Coffee className="mr-2 h-4 w-4" /> Break
              </Button>
            )}
          </div>

          <Button
            variant="outline"
            className="mt-4"
            onClick={handleLogout}
          >
            <LogOut className="mr-2 h-4 w-4" /> Logout
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default EnhancedStaffTimeClock;
