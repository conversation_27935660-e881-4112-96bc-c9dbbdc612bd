<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redirecting to Staff Management</title>
    <script>
        // Try different URLs to connect to the development server
        const urls = [
            'http://localhost:3000/admin/staff',
            'http://127.0.0.1:3000/admin/staff',
            'http://*************:3000/admin/staff'
        ];

        function tryConnect(index) {
            if (index >= urls.length) {
                document.getElementById('status').innerHTML = 'Failed to connect to any URL. Please check if the server is running.';
                return;
            }

            const url = urls[index];
            document.getElementById('status').innerHTML = `Trying to connect to ${url}...`;

            fetch(url, { mode: 'no-cors' })
                .then(() => {
                    window.location.href = url;
                })
                .catch(() => {
                    setTimeout(() => tryConnect(index + 1), 1000);
                });
        }

        window.onload = function() {
            tryConnect(0);
        };
    </script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            margin: 0;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            text-align: center;
            padding: 2rem;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            max-width: 500px;
        }
        h1 {
            margin-bottom: 1rem;
        }
        #status {
            margin-top: 1rem;
            color: #666;
        }
        .manual-links {
            margin-top: 2rem;
        }
        .manual-links a {
            display: block;
            margin: 0.5rem 0;
            color: #0066cc;
            text-decoration: none;
        }
        .manual-links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Connecting to Staff Management</h1>
        <p>Attempting to connect to the development server...</p>
        <p id="status">Initializing...</p>

        <div class="manual-links">
            <p>If automatic redirection doesn't work, try these links manually:</p>
            <a href="http://localhost:3000/admin/staff">localhost:3000</a>
            <a href="http://127.0.0.1:3000/admin/staff">127.0.0.1:3000</a>
            <a href="http://*************:3000/admin/staff">*************:3000</a>
        </div>
    </div>
</body>
</html>
