from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Optional
from datetime import datetime, timedelta
import random
from app.models.analytics import SalesData, RevenueData, ForecastData, AnalyticsResponse
from app.services.analytics_service import AnalyticsService
from app.utils.auth import get_current_restaurant_id
from app.utils.storage import get_all

router = APIRouter(prefix="/analytics")

# Initialize analytics service
analytics_service = AnalyticsService()

@router.get("/sales", response_model=List[SalesData])
async def get_sales_data(
    start_date: Optional[str] = Query(None, description="Start date in YYYY-MM-DD format"),
    end_date: Optional[str] = Query(None, description="End date in YYYY-MM-DD format"),
    restaurant_id: str = Depends(get_current_restaurant_id)
):
    """
    Get sales data for a specified date range
    """
    return await analytics_service.get_sales_data(restaurant_id, start_date, end_date)

@router.get("/revenue", response_model=List[RevenueData])
async def get_revenue_data(
    period_type: str = Query("weekly", description="Period type: 'weekly' or 'monthly'"),
    restaurant_id: str = Depends(get_current_restaurant_id)
):
    """
    Get revenue data by period (weekly or monthly)
    """
    return await analytics_service.get_revenue_data(restaurant_id, period_type)

@router.get("/forecast", response_model=List[ForecastData])
async def get_forecast_data(restaurant_id: str = Depends(get_current_restaurant_id)):
    """
    Get sales and customer forecast for the next 7 days
    """
    forecast_data = await analytics_service._generate_forecast_data(restaurant_id)
    return forecast_data

@router.get("/dashboard", response_model=AnalyticsResponse)
async def get_dashboard_data(restaurant_id: str = Depends(get_current_restaurant_id)):
    """
    Get all analytics data for the dashboard
    """
    return await analytics_service.get_dashboard_metrics(restaurant_id)

@router.get("/popular-items")
async def get_popular_items(
    limit: int = Query(5, description="Number of items to return"),
    restaurant_id: str = Depends(get_current_restaurant_id)
):
    """
    Get the most popular menu items based on order history
    """
    return await analytics_service.get_popular_items(restaurant_id, limit)

@router.get("/advanced-forecast")
async def get_advanced_forecast():
    """
    Get advanced forecast data including ingredient usage, customer traffic, 
    seasonal trends, and revenue projections
    """
    today = datetime.now()
    forecast = []
    
    # Generate forecast for next 7 days
    for i in range(1, 8):
        date = today + timedelta(days=i)
        weekday = date.weekday()
        date_str = date.strftime("%A")
        
        # Base values with weekday patterns
        if weekday >= 5:  # Weekend
            base_customers = 80
            base_revenue = 2000
            base_occupancy = 85
            base_staff = 12
        else:
            base_customers = 50 + (weekday * 5)  # Gradually increase through the week
            base_revenue = 1200 + (weekday * 100)
            base_occupancy = 60 + (weekday * 5)
            base_staff = 8 + (weekday // 2)
        
        # Random variation
        random_factor = 0.9 + (random.random() * 0.2)  # 0.9 to 1.1
        
        # Generate peak hours
        peak_hours = []
        for hour in [12, 13, 19, 20]:
            if hour < 15:  # Lunch
                base_hour_customers = base_customers * 0.3 / 2  # 30% of daily customers split between 2 lunch hours
            else:  # Dinner
                base_hour_customers = base_customers * 0.5 / 2  # 50% of daily customers split between 2 dinner hours
            
            peak_hours.append({
                "hour": hour,
                "customers": int(base_hour_customers * random_factor)
            })
        
        # Generate ingredient usage forecast
        ingredients = []
        common_ingredients = [
            {"name": "Chicken", "unit": "kg", "base_quantity": 0.2, "cost_per_unit": 7},
            {"name": "Beef", "unit": "kg", "base_quantity": 0.15, "cost_per_unit": 12},
            {"name": "Pasta", "unit": "kg", "base_quantity": 0.1, "cost_per_unit": 4},
            {"name": "Rice", "unit": "kg", "base_quantity": 0.1, "cost_per_unit": 4},
            {"name": "Tomatoes", "unit": "kg", "base_quantity": 0.2, "cost_per_unit": 3.5}
        ]
        
        for ing in common_ingredients:
            # Only include some ingredients randomly to make the data look more realistic
            if random.random() > 0.2:  # 80% chance to include each ingredient
                quantity = round(ing["base_quantity"] * base_customers * random_factor, 1)
                cost = round(quantity * ing["cost_per_unit"], 2)
                
                ingredients.append({
                    "name": ing["name"],
                    "quantity": quantity,
                    "unit": ing["unit"],
                    "cost": cost
                })
        
        # Add forecast entry
        forecast.append({
            "day": date_str,
            "actualRevenue": 0,  # No actual revenue for future dates
            "projectedRevenue": round(base_revenue * random_factor, 2),
            "customers": int(base_customers * random_factor),
            "confidence": random.randint(80, 95),
            "tableOccupancy": base_occupancy,
            "staffNeeded": base_staff,
            "peakHours": peak_hours,
            "ingredientUsage": ingredients,
            "seasonalTrend": random.randint(3, 12) if weekday >= 5 else random.randint(1, 8),
            "historicalComparison": random.randint(5, 15) if weekday >= 5 else random.randint(2, 10)
        })
    
    return forecast

@router.get("/staff-scheduling")
async def get_staff_scheduling_data():
    """
    Get staff scheduling recommendations based on forecasts
    """
    # Get advanced forecast data
    forecast = await get_advanced_forecast()
    
    # Get all staff
    staff = get_all("staff")
    
    # Generate scheduling recommendations
    scheduling_data = []
    
    for day_forecast in forecast:
        day = day_forecast["day"]
        staff_needed = day_forecast["staffNeeded"]
        peak_hours = day_forecast["peakHours"]
        
        # Calculate shifts needed
        morning_shift = {"start": "07:00", "end": "15:00", "staff_needed": max(2, staff_needed // 3)}
        afternoon_shift = {"start": "15:00", "end": "23:00", "staff_needed": max(3, staff_needed * 2 // 3)}
        
        # Assign staff (simplified for demo)
        morning_staff = []
        afternoon_staff = []
        
        for i, s in enumerate(staff):
            # Simple alternating assignment for demo purposes
            if i % 2 == 0 and len(morning_staff) < morning_shift["staff_needed"]:
                morning_staff.append({
                    "id": s["id"],
                    "name": s["name"],
                    "role": s["role"],
                    "hourly_rate": s["hourly_rate"]
                })
            elif len(afternoon_staff) < afternoon_shift["staff_needed"]:
                afternoon_staff.append({
                    "id": s["id"],
                    "name": s["name"],
                    "role": s["role"],
                    "hourly_rate": s["hourly_rate"]
                })
        
        # Calculate labor costs
        morning_cost = sum(s["hourly_rate"] * 8 for s in morning_staff)
        afternoon_cost = sum(s["hourly_rate"] * 8 for s in afternoon_staff)
        
        scheduling_data.append({
            "day": day,
            "forecast": {
                "customers": day_forecast["customers"],
                "revenue": day_forecast["projectedRevenue"],
                "peak_hours": peak_hours
            },
            "shifts": [
                {
                    "name": "Morning",
                    "start": morning_shift["start"],
                    "end": morning_shift["end"],
                    "staff_needed": morning_shift["staff_needed"],
                    "assigned_staff": morning_staff,
                    "labor_cost": morning_cost
                },
                {
                    "name": "Afternoon",
                    "start": afternoon_shift["start"],
                    "end": afternoon_shift["end"],
                    "staff_needed": afternoon_shift["staff_needed"],
                    "assigned_staff": afternoon_staff,
                    "labor_cost": afternoon_cost
                }
            ],
            "total_labor_cost": morning_cost + afternoon_cost,
            "labor_cost_percentage": round(((morning_cost + afternoon_cost) / day_forecast["projectedRevenue"]) * 100, 2)
        })
    
    return scheduling_data

@router.get("/seasonal-analysis")
async def get_seasonal_analysis():
    """
    Get seasonal analysis data
    """
    # Generate seasonal data for the year
    months = ["January", "February", "March", "April", "May", "June", 
              "July", "August", "September", "October", "November", "December"]
    
    seasonal_data = []
    
    for i, month in enumerate(months):
        # Define seasonal patterns
        if i in [5, 6, 7]:  # Summer months
            season_factor = 1.3
            season_name = "Summer"
        elif i in [11, 0, 1]:  # Winter months
            season_factor = 0.9
            season_name = "Winter"
        elif i in [2, 3, 4]:  # Spring months
            season_factor = 1.1
            season_name = "Spring"
        else:  # Fall months
            season_factor = 1.0
            season_name = "Fall"
        
        # Add holiday effects
        holiday_effect = 1.0
        holiday_name = None
        
        if month == "December":
            holiday_effect = 1.4
            holiday_name = "Christmas"
        elif month == "November":
            holiday_effect = 1.2
            holiday_name = "Thanksgiving"
        elif month == "February":
            holiday_effect = 1.15
            holiday_name = "Valentine's Day"
        elif month == "July":
            holiday_effect = 1.2
            holiday_name = "Independence Day"
        
        # Base values
        base_revenue = 40000
        base_customers = 1600
        
        # Apply factors
        revenue = round(base_revenue * season_factor * holiday_effect)
        customers = round(base_customers * season_factor * holiday_effect)
        
        seasonal_data.append({
            "month": month,
            "revenue": revenue,
            "customers": customers,
            "averageCheck": round(revenue / customers, 2),
            "season": season_name,
            "seasonalFactor": round(season_factor, 2),
            "holiday": holiday_name,
            "holidayFactor": round(holiday_effect, 2) if holiday_name else None
        })
    
    return seasonal_data

@router.get("/inventory-forecast")
async def get_inventory_forecast(restaurant_id: str = Depends(get_current_restaurant_id)):
    """
    Get inventory usage forecast and reorder recommendations
    """
    return await analytics_service.get_inventory_forecast(restaurant_id)

@router.get("/revenue-projections")
async def get_revenue_projections(months: int = Query(12, description="Number of months to project")):
    """
    Get revenue projections for the specified number of months
    """
    today = datetime.now()
    projections = []
    
    # Base monthly revenue
    base_revenue = 40000
    
    for i in range(months):
        projection_date = today + timedelta(days=i*30)
        month = projection_date.month
        year = projection_date.year
        
        # Seasonal factors
        if month in [6, 7, 8]:  # Summer
            season_factor = 1.3
        elif month in [12, 1, 2]:  # Winter
            season_factor = 0.9
        elif month in [3, 4, 5]:  # Spring
            season_factor = 1.1
        else:  # Fall
            season_factor = 1.0
        
        # Growth factor (assume 5% annual growth)
        months_from_now = i
        growth_factor = 1 + (0.05 * months_from_now / 12)
        
        # Random variation
        random_factor = 0.95 + (random.random() * 0.1)  # 0.95 to 1.05
        
        # Calculate projected revenue
        projected_revenue = base_revenue * season_factor * growth_factor * random_factor
        
        # Add projection
        projections.append({
            "month": projection_date.strftime("%B %Y"),
            "projectedRevenue": round(projected_revenue, 2),
            "seasonalFactor": round(season_factor, 2),
            "growthFactor": round(growth_factor, 2),
            "confidence": random.randint(70, 95)
        })
    
    return projections

@router.get("/category-performance")
async def get_category_performance(
    days: int = Query(30, description="Number of days to analyze"),
    restaurant_id: str = Depends(get_current_restaurant_id)
):
    """
    Get performance metrics by menu category
    """
    return await analytics_service.get_category_performance(restaurant_id, days)

@router.get("/peak-hours")
async def get_peak_hours_analysis(
    days: int = Query(30, description="Number of days to analyze"),
    restaurant_id: str = Depends(get_current_restaurant_id)
):
    """
    Get peak hours analysis with category breakdown
    """
    return await analytics_service.get_peak_hours_analysis(restaurant_id, days)

@router.get("/profit-margins")
async def get_profit_margin_analysis(
    days: int = Query(30, description="Number of days to analyze"),
    restaurant_id: str = Depends(get_current_restaurant_id)
):
    """
    Get profit margin analysis by category
    """
    return await analytics_service.get_profit_margin_analysis(restaurant_id, days)
