import logger from "@/utils/logger";
import { RestaurantRegistrationData, RestaurantSetupData } from "@/types/restaurantSetup";

export interface StoredRestaurantData {
  id: string;
  restaurantCode: string;
  ownerPin: string;
  registrationData: RestaurantRegistrationData;
  setupData: RestaurantSetupData;
  isSetupComplete: boolean;
  createdAt: string;
  updatedAt: string;
  status: 'active' | 'inactive';
  logoPath?: string;
}

class FileStorageService {
  private baseUrl = `${import.meta.env.VITE_API_URL || 'http://localhost:5001'}/api/storage`;

  constructor() {
    logger.info('FileStorageService initialized', 'FileStorageService');
  }

  // Save restaurant data to file
  async saveRestaurantData(data: StoredRestaurantData): Promise<boolean> {
    try {
      logger.dataOperation('save', 'restaurant data to file', 'FileStorageService', {
        restaurantCode: data.restaurantCode,
        fileName: `restaurant_${data.restaurantCode}.json`
      });

      const response = await fetch(`${this.baseUrl}/restaurant`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`Failed to save restaurant data: ${response.statusText}`);
      }

      logger.dataOperation('save', 'restaurant data to file', 'FileStorageService', {
        result: 'success',
        restaurantCode: data.restaurantCode
      });
      return true;
    } catch (error) {
      logger.logError(error, 'saving restaurant data to file', 'FileStorageService');

      // Fallback to localStorage if file storage fails
      try {
        const fallbackKey = `restaurant_file_${data.restaurantCode}`;
        localStorage.setItem(fallbackKey, JSON.stringify(data));
        logger.warn('Used localStorage fallback for restaurant data', 'FileStorageService');
        return true;
      } catch (fallbackError) {
        logger.logError(fallbackError, 'localStorage fallback', 'FileStorageService');
        return false;
      }
    }
  }

  // Load restaurant data from file
  async loadRestaurantData(restaurantCode: string): Promise<StoredRestaurantData | null> {
    try {
      logger.dataOperation('load', 'restaurant data from file', 'FileStorageService', {
        restaurantCode,
        fileName: `restaurant_${restaurantCode}.json`
      });

      const response = await fetch(`${this.baseUrl}/restaurant/${restaurantCode}`);

      if (!response.ok) {
        if (response.status === 404) {
          logger.debug('Restaurant data file not found', 'FileStorageService', { restaurantCode });
          return null;
        }
        throw new Error(`Failed to load restaurant data: ${response.statusText}`);
      }

      const data = await response.json();
      logger.dataOperation('load', 'restaurant data from file', 'FileStorageService', {
        result: 'success',
        restaurantCode
      });
      return data;
    } catch (error) {
      logger.logError(error, 'loading restaurant data from file', 'FileStorageService');

      // Fallback to localStorage
      try {
        const fallbackKey = `restaurant_file_${restaurantCode}`;
        const stored = localStorage.getItem(fallbackKey);
        if (stored) {
          logger.debug('Used localStorage fallback for loading restaurant data', 'FileStorageService');
          return JSON.parse(stored);
        }
      } catch (fallbackError) {
        logger.logError(fallbackError, 'localStorage fallback load', 'FileStorageService');
      }

      return null;
    }
  }

  // Load all restaurant data files
  async loadAllRestaurantData(): Promise<StoredRestaurantData[]> {
    try {
      logger.dataOperation('load', 'all restaurant data files', 'FileStorageService');

      const response = await fetch(`${this.baseUrl}/restaurants`);

      if (!response.ok) {
        throw new Error(`Failed to load all restaurant data: ${response.statusText}`);
      }

      const data = await response.json();
      logger.dataOperation('load', 'all restaurant data files', 'FileStorageService', {
        result: 'success',
        count: data.length
      });
      return data;
    } catch (error) {
      logger.logError(error, 'loading all restaurant data files', 'FileStorageService');

      // Fallback to localStorage
      try {
        const restaurants: StoredRestaurantData[] = [];
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && key.startsWith('restaurant_file_')) {
            const stored = localStorage.getItem(key);
            if (stored) {
              restaurants.push(JSON.parse(stored));
            }
          }
        }
        logger.debug('Used localStorage fallback for loading all restaurant data', 'FileStorageService', {
          count: restaurants.length
        });
        return restaurants;
      } catch (fallbackError) {
        logger.logError(fallbackError, 'localStorage fallback load all', 'FileStorageService');
        return [];
      }
    }
  }

  // Save logo file
  async saveLogo(file: File, restaurantCode: string): Promise<string | null> {
    try {
      const fileName = `${restaurantCode}_${Date.now()}.${file.name.split('.').pop()}`;
      logger.dataOperation('save', 'logo file', 'FileStorageService', {
        restaurantCode,
        fileName,
        fileSize: file.size
      });

      const formData = new FormData();
      formData.append('logo', file);
      formData.append('restaurantCode', restaurantCode);
      formData.append('fileName', fileName);

      const response = await fetch(`${this.baseUrl}/logo`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Failed to save logo: ${response.statusText}`);
      }

      const result = await response.json();
      const logoPath = result.path || `/uploads/logos/${fileName}`;

      logger.dataOperation('save', 'logo file', 'FileStorageService', {
        result: 'success',
        logoPath
      });
      return logoPath;
    } catch (error) {
      logger.logError(error, 'saving logo file', 'FileStorageService');

      // Fallback to base64 storage in localStorage
      try {
        const reader = new FileReader();
        return new Promise((resolve) => {
          reader.onload = () => {
            const base64 = reader.result as string;
            const fallbackKey = `logo_${restaurantCode}`;
            localStorage.setItem(fallbackKey, base64);
            logger.warn('Used localStorage fallback for logo storage', 'FileStorageService');
            resolve(base64);
          };
          reader.onerror = () => {
            logger.logError(reader.error, 'base64 conversion fallback', 'FileStorageService');
            resolve(null);
          };
          reader.readAsDataURL(file);
        });
      } catch (fallbackError) {
        logger.logError(fallbackError, 'logo storage fallback', 'FileStorageService');
        return null;
      }
    }
  }

  // Check if file storage is available
  async isFileStorageAvailable(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/health`);
      return response.ok;
    } catch (error) {
      logger.debug('File storage not available, using localStorage fallback', 'FileStorageService');
      return false;
    }
  }
}

// Export singleton instance
export const fileStorageService = new FileStorageService();
export default fileStorageService;
