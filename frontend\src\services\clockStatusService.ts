// Storage key for clock status
const CLOCK_STATUS_KEY = 'clockStatus';

// Define clock status type
export type ClockStatus = 'clocked_in' | 'clocked_out' | 'on_break';

// Define clock status interface
export interface ClockStatusData {
  staffId: string;
  status: ClockStatus;
  lastClockIn: string | null;
  lastClockOut: string | null;
  breakStart: string | null;
  breakEnd: string | null;
}

// Get all clock statuses
export const getAllClockStatuses = (): Record<string, ClockStatusData> => {
  const clockStatusJson = localStorage.getItem(CLOCK_STATUS_KEY);
  return clockStatusJson ? JSON.parse(clockStatusJson) : {};
};

// Get clock status for a staff member
export const getClockStatus = (staffId: string): ClockStatusData | null => {
  const allStatuses = getAllClockStatuses();
  return allStatuses[staffId] || null;
};

// Save clock status for a staff member
export const saveClockStatus = (
  staffId: string, 
  status: ClockStatus,
  lastClockIn: string | null = null,
  lastClockOut: string | null = null,
  breakStart: string | null = null,
  breakEnd: string | null = null
): void => {
  const allStatuses = getAllClockStatuses();
  
  allStatuses[staffId] = {
    staffId,
    status,
    lastClockIn,
    lastClockOut,
    breakStart,
    breakEnd
  };
  
  localStorage.setItem(CLOCK_STATUS_KEY, JSON.stringify(allStatuses));
  
  // Log for debugging
  console.log(`[ClockStatusService] Saved clock status for staff ${staffId}:`, status);
};

// Update clock status for a staff member
export const updateClockStatus = (staffId: string, updates: Partial<ClockStatusData>): void => {
  const allStatuses = getAllClockStatuses();
  const currentStatus = allStatuses[staffId] || {
    staffId,
    status: 'clocked_out',
    lastClockIn: null,
    lastClockOut: null,
    breakStart: null,
    breakEnd: null
  };
  
  allStatuses[staffId] = {
    ...currentStatus,
    ...updates
  };
  
  localStorage.setItem(CLOCK_STATUS_KEY, JSON.stringify(allStatuses));
  
  // Log for debugging
  console.log(`[ClockStatusService] Updated clock status for staff ${staffId}:`, allStatuses[staffId]);
};

// Remove clock status for a staff member
export const removeClockStatus = (staffId: string): void => {
  const allStatuses = getAllClockStatuses();
  delete allStatuses[staffId];
  localStorage.setItem(CLOCK_STATUS_KEY, JSON.stringify(allStatuses));
  
  // Log for debugging
  console.log(`[ClockStatusService] Removed clock status for staff ${staffId}`);
};

// Clock in a staff member
export const clockIn = (staffId: string): void => {
  const now = new Date().toISOString();
  saveClockStatus(staffId, 'clocked_in', now, null, null, null);
  
  // Log for debugging
  console.log(`[ClockStatusService] Clocked in staff ${staffId} at ${now}`);
};

// Clock out a staff member
export const clockOut = (staffId: string): void => {
  const now = new Date().toISOString();
  const currentStatus = getClockStatus(staffId);
  
  if (currentStatus) {
    saveClockStatus(
      staffId, 
      'clocked_out', 
      currentStatus.lastClockIn, 
      now,
      currentStatus.breakStart,
      currentStatus.breakEnd
    );
  } else {
    saveClockStatus(staffId, 'clocked_out', null, now, null, null);
  }
  
  // Log for debugging
  console.log(`[ClockStatusService] Clocked out staff ${staffId} at ${now}`);
};

// Start break for a staff member
export const startBreak = (staffId: string): void => {
  const now = new Date().toISOString();
  const currentStatus = getClockStatus(staffId);
  
  if (currentStatus) {
    saveClockStatus(
      staffId, 
      'on_break', 
      currentStatus.lastClockIn, 
      currentStatus.lastClockOut,
      now,
      null
    );
  } else {
    saveClockStatus(staffId, 'on_break', null, null, now, null);
  }
  
  // Log for debugging
  console.log(`[ClockStatusService] Started break for staff ${staffId} at ${now}`);
};

// End break for a staff member
export const endBreak = (staffId: string): void => {
  const now = new Date().toISOString();
  const currentStatus = getClockStatus(staffId);
  
  if (currentStatus) {
    saveClockStatus(
      staffId, 
      'clocked_in', 
      currentStatus.lastClockIn, 
      currentStatus.lastClockOut,
      currentStatus.breakStart,
      now
    );
  } else {
    saveClockStatus(staffId, 'clocked_in', null, null, null, now);
  }
  
  // Log for debugging
  console.log(`[ClockStatusService] Ended break for staff ${staffId} at ${now}`);
};
