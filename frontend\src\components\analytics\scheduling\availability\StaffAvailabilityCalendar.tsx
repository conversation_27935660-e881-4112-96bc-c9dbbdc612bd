import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Download, Plus, Calendar, Users } from "lucide-react";
import { StaffMember } from "@/types";
import { getUnresolvedConflicts } from "@/services/staffAvailabilityService";
import AvailabilityCalendarMonthView from "./AvailabilityCalendarMonthView";
import AvailabilityCalendarWeekView from "./AvailabilityCalendarWeekView";
import TimeOffRequestForm from "./TimeOffRequestForm";
import SchedulingConflictNotification from "./SchedulingConflictNotification";
import AvailabilityLegend from "./AvailabilityLegend";

interface StaffAvailabilityCalendarProps {
  staffData: StaffMember[];
  forecastData: any[];
}

const StaffAvailabilityCalendar = ({
  staffData,
  forecastData
}: StaffAvailabilityCalendarProps) => {
  const [view, setView] = useState<"week" | "month">("month");
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);
  const [isRequestFormOpen, setIsRequestFormOpen] = useState(false);
  const [selectedStaffId, setSelectedStaffId] = useState<string>("");
  const [hasConflicts, setHasConflicts] = useState(false);

  // Check for scheduling conflicts
  useEffect(() => {
    const checkForConflicts = () => {
      const conflicts = getUnresolvedConflicts();
      setHasConflicts(conflicts.length > 0);
    };

    checkForConflicts();
    // Set up an interval to check for conflicts periodically
    const intervalId = setInterval(checkForConflicts, 30000); // Check every 30 seconds
    
    return () => clearInterval(intervalId);
  }, []);

  const handleRequestTimeOff = (staffId: string) => {
    setSelectedStaffId(staffId);
    setIsRequestFormOpen(true);
  };

  const handleExportCalendar = () => {
    // In a real application, this would generate a CSV or PDF file
    alert("Calendar exported successfully!");
  };

  // Simplified staff data for calendar components
  const simplifiedStaffData = staffData.map(staff => ({
    id: staff.id,
    name: staff.name
  }));

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="pb-3">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <CardTitle className="text-xl font-bold">Staff Availability Calendar</CardTitle>
            <div className="flex items-center gap-2">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => handleRequestTimeOff(staffData[0]?.id || "")}
              >
                <Plus className="mr-2 h-4 w-4" />
                Request Time Off
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={handleExportCalendar}
              >
                <Download className="mr-2 h-4 w-4" />
                Export
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {hasConflicts && (
              <SchedulingConflictNotification 
                onResolve={() => setHasConflicts(false)} 
              />
            )}
            
            <Tabs defaultValue={view} onValueChange={(value) => setView(value as "week" | "month")}>
              <div className="flex justify-between items-center mb-4">
                <TabsList>
                  <TabsTrigger value="week">Week</TabsTrigger>
                  <TabsTrigger value="month">Month</TabsTrigger>
                </TabsList>
              </div>
              
              <TabsContent value="week" className="space-y-4">
                <AvailabilityCalendarWeekView 
                  staffData={simplifiedStaffData}
                  onDateSelect={setSelectedDate}
                />
              </TabsContent>
              
              <TabsContent value="month" className="space-y-4">
                <AvailabilityCalendarMonthView 
                  staffData={simplifiedStaffData}
                  onDateSelect={setSelectedDate}
                />
              </TabsContent>
            </Tabs>
          </div>
        </CardContent>
      </Card>
      
      <Dialog open={isRequestFormOpen} onOpenChange={setIsRequestFormOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Request Time Off</DialogTitle>
          </DialogHeader>
          <TimeOffRequestForm 
            staffId={selectedStaffId}
            staffName={staffData.find(s => s.id === selectedStaffId)?.name || ""}
            onRequestSubmitted={() => setIsRequestFormOpen(false)}
            onCancel={() => setIsRequestFormOpen(false)}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default StaffAvailabilityCalendar;
