# File Import Implementation for Inventory

This document provides instructions on how to implement the file import functionality for the inventory tab in RestroManage.

## Overview

The implementation adds support for importing inventory data from CSV, XLSX, and PDF files. It includes:

1. A server middleware for handling file uploads and processing
2. A utility module for file validation and upload
3. A reusable ImportDialog component
4. Integration with the existing Inventory component

## Files Created

- `src/server/fileUploadMiddleware.js`: Server middleware for handling file uploads
- `src/utils/fileUpload.ts`: Utility functions for file validation and upload
- `src/pages/ImportDialog.tsx`: Reusable dialog component for file import

## Implementation Steps

### 1. Install Dependencies

```bash
npm install express multer csv-parser xlsx pdf-parse body-parser cors
```

### 2. Update Vite Configuration

Modify `vite.config.ts` to include the server middleware:

```typescript
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";
import express from "express";
import { createFileUploadMiddleware } from "./src/server/fileUploadMiddleware";

export default defineConfig(({ mode }) => ({
  server: {
    host: '127.0.0.1',
    port: 3000,
    strictPort: false,
    cors: true,
    hmr: {
      host: '127.0.0.1',
      port: 3000
    },
    // Add middleware for file uploads
    configureServer: (server) => {
      server.middlewares.use(express.json());
      server.middlewares.use(createFileUploadMiddleware());
    },
  },
  // ... rest of your config
}));
```

### 3. Modify Inventory.tsx

1. Import the ImportDialog component:
   ```typescript
   import { ImportDialog } from "./ImportDialog";
   ```

2. Replace the existing Import Dialog with the new component:
   ```tsx
   {/* Import Dialog */}
   <ImportDialog 
     isOpen={isImportOpen} 
     onClose={() => setIsImportOpen(false)} 
     inventory={inventory}
     onApplyChanges={(matchedItems) => {
       // Update inventory with matched items
       const updatedInventory = [...inventory];
       let updatedCount = 0;

       for (const result of matchedItems) {
         if (result.matchedItem) {
           const index = updatedInventory.findIndex(item => item.id === result.matchedItem.id);
           if (index !== -1) {
             const newStock = updatedInventory[index].stock + result.quantity;
             updatedInventory[index] = {
               ...updatedInventory[index],
               stock: newStock,
               isLow: newStock <= updatedInventory[index].reorderLevel
             };
             updatedCount++;
           }
         }
       }

       setInventory(updatedInventory);
       toast.success(`Updated ${updatedCount} inventory items from import`);
     }}
   />
   ```

## Testing

1. Start the development server:
   ```bash
   npm run dev
   ```

2. Navigate to the Inventory tab
3. Click the "Import" button
4. Select a CSV, XLSX, or PDF file
5. Verify that the file is processed and the data is displayed
6. Click "Apply Changes" to update the inventory

## Supported File Formats

- **CSV**: Standard comma-separated values file
- **XLSX**: Excel spreadsheet file
- **PDF**: PDF document (simplified implementation)

## Notes

- The PDF processing is currently simplified and returns sample data
- For production use, you may want to enhance the PDF parsing logic
- The file upload endpoint is available at `/api/upload`
