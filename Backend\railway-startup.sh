#!/bin/bash

# Railway startup script for RestroManage Backend
set -e

echo "🚀 Starting RestroManage Backend on Railway..."

# Wait for database to be ready
echo "⏳ Waiting for database connection..."
python -c "
import os
import time
import sys
from sqlalchemy import create_engine, text

DATABASE_URL = os.getenv('DATABASE_URL')
if not DATABASE_URL:
    print('❌ DATABASE_URL not set')
    sys.exit(1)

engine = create_engine(DATABASE_URL)
max_retries = 30
retry_count = 0

while retry_count < max_retries:
    try:
        with engine.connect() as conn:
            conn.execute(text('SELECT 1'))
        print('✅ Database connection successful')
        break
    except Exception as e:
        retry_count += 1
        print(f'⏳ Database not ready (attempt {retry_count}/{max_retries}): {e}')
        time.sleep(2)
        
if retry_count >= max_retries:
    print('❌ Database connection failed after maximum retries')
    sys.exit(1)
"

# Run database migrations
echo "🔄 Running database migrations..."
python -c "
try:
    # Try the new database structure first
    from app.database import init_database
    if init_database():
        print('✅ Database initialization completed')
    else:
        print('⚠️ Database initialization had issues, continuing...')
except ImportError:
    try:
        # Fallback to old structure
        from Database.migrations import initialize_database
        if initialize_database():
            print('✅ Database initialization completed')
        else:
            print('⚠️ Database initialization had issues, continuing...')
    except Exception as e:
        print(f'⚠️ Migration error: {e}, continuing...')
except Exception as e:
    print(f'⚠️ Migration error: {e}, continuing...')
"

# Create upload directories
echo "📁 Setting up upload directories..."
mkdir -p /app/uploads/logos /app/uploads/data /app/logs

# Set Railway-specific environment variables
export HOST=0.0.0.0
export PORT=${PORT:-8000}

echo "🌐 Starting server on $HOST:$PORT"

# Start the application with gunicorn
# Try different app module paths based on project structure
if [ -f "app/api.py" ]; then
    APP_MODULE="app.api:app"
elif [ -f "main.py" ]; then
    APP_MODULE="main:app"
else
    APP_MODULE="app:app"
fi

echo "🚀 Starting with module: $APP_MODULE"

exec gunicorn $APP_MODULE \
    --worker-class uvicorn.workers.UvicornWorker \
    --workers ${WORKERS:-4} \
    --bind $HOST:$PORT \
    --timeout 120 \
    --keep-alive 2 \
    --max-requests 1000 \
    --max-requests-jitter 100 \
    --access-logfile - \
    --error-logfile - \
    --log-level ${LOG_LEVEL:-info}
