import { createContext, useContext, ReactNode } from 'react';
import { Notification, NotificationType, NotificationPriority } from '@/types/notification';
import { useNotificationStore } from '@/hooks/useAppStore';
import { toast } from '@/components/ui/sonner';

// Define context type
interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'isRead'>) => void;
  markAsRead: (notificationId: string) => void;
  markAllAsRead: () => void;
  deleteNotification: (notificationId: string) => void;
  clearAllNotifications: () => void;
  showToast: (type: NotificationType, title: string, message: string) => void;
}

// Create context with default values
const NotificationContext = createContext<NotificationContextType>({
  notifications: [],
  unreadCount: 0,
  addNotification: () => {},
  markAsRead: () => {},
  markAllAsRead: () => {},
  deleteNotification: () => {},
  clearAllNotifications: () => {},
  showToast: () => {}
});

interface NotificationProviderProps {
  children: ReactNode;
}

export const NotificationProvider = ({ children }: NotificationProviderProps) => {
  // Use the new React Query-based notification store
  const notificationStore = useNotificationStore();

  // Add a new notification
  const addNotification = async (notification: Omit<Notification, 'id' | 'timestamp' | 'isRead'>) => {
    try {
      await notificationStore.createNotification({
        title: notification.title,
        message: notification.message,
        type: notification.type,
        priority: notification.priority,
        link: notification.link,
        relatedId: notification.relatedId,
      });

      // Optionally show a toast for high priority notifications
      if (notification.priority === 'high') {
        showToast(notification.type, notification.title, notification.message);
      }
    } catch (error) {
      console.error('Failed to create notification:', error);
      showToast('error', 'Error', 'Failed to create notification');
    }
  };

  // Mark a notification as read
  const markAsRead = async (notificationId: string) => {
    try {
      await notificationStore.markAsRead([notificationId]);
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
      showToast('error', 'Error', 'Failed to mark notification as read');
    }
  };

  // Mark all notifications as read
  const markAllAsRead = async () => {
    try {
      await notificationStore.markAllAsRead();
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
      showToast('error', 'Error', 'Failed to mark all notifications as read');
    }
  };

  // Delete a notification
  const deleteNotificationHandler = async (notificationId: string) => {
    try {
      await notificationStore.deleteNotification(notificationId);
    } catch (error) {
      console.error('Failed to delete notification:', error);
      showToast('error', 'Error', 'Failed to delete notification');
    }
  };

  // Clear all notifications
  const clearAllNotifications = async () => {
    try {
      await notificationStore.deleteAllNotifications();
    } catch (error) {
      console.error('Failed to delete all notifications:', error);
      showToast('error', 'Error', 'Failed to delete all notifications');
    }
  };

  // Show a toast notification
  const showToast = (type: NotificationType, title: string, message: string) => {
    switch (type) {
      case 'success':
        toast.success(title, { description: message });
        break;
      case 'error':
        toast.error(title, { description: message });
        break;
      case 'warning':
        toast.warning(title, { description: message });
        break;
      default:
        toast.info(title, { description: message });
        break;
    }
  };

  return (
    <NotificationContext.Provider
      value={{
        notifications: notificationStore.notifications,
        unreadCount: notificationStore.unreadCount,
        addNotification,
        markAsRead,
        markAllAsRead,
        deleteNotification: deleteNotificationHandler,
        clearAllNotifications,
        showToast
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
};

// Custom hook for using notification context
export const useNotifications = () => useContext(NotificationContext);
