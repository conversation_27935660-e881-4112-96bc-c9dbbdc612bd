import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  BarChart3, 
  Users, 
  ShoppingBasket, 
  Calendar,
  TrendingUp,
  DollarSign,
  Clock,
  FileText,
  Upload,
  Download,
  Settings,
  Plus,
  Eye,
  Edit,
  MoreHorizontal,
  Building2,
  ChefHat,
  Utensils,
  Package
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import logger from '@/utils/logger';

interface DashboardStats {
  todayRevenue: string;
  todayCustomers: number;
  inventoryItems: number;
  staffOnDuty: number;
}

interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  action: () => void;
  variant?: 'default' | 'secondary' | 'outline';
}

const Dashboard: React.FC = () => {
  const { currentRestaurant, user } = useAuth();
  const [stats, setStats] = useState<DashboardStats>({
    todayRevenue: '£3,245.80',
    todayCustomers: 142,
    inventoryItems: 245,
    staffOnDuty: 8
  });

  useEffect(() => {
    logger.setComponent('Dashboard');
    logger.info('Dashboard loaded', 'Dashboard', {
      restaurant: currentRestaurant?.name,
      user: user?.name
    });
  }, [currentRestaurant, user]);

  const quickActions: QuickAction[] = [
    {
      id: 'new-order',
      title: 'New Order',
      description: 'Create a new customer order',
      icon: <Plus className="h-5 w-5" />,
      action: () => logger.userAction('new order clicked', 'Dashboard')
    },
    {
      id: 'view-menu',
      title: 'Menu Management',
      description: 'Update menu items and pricing',
      icon: <Utensils className="h-5 w-5" />,
      action: () => logger.userAction('menu management clicked', 'Dashboard')
    },
    {
      id: 'staff-schedule',
      title: 'Staff Schedule',
      description: 'Manage staff shifts and availability',
      icon: <Calendar className="h-5 w-5" />,
      action: () => logger.userAction('staff schedule clicked', 'Dashboard')
    },
    {
      id: 'inventory',
      title: 'Inventory',
      description: 'Check stock levels and reorder',
      icon: <Package className="h-5 w-5" />,
      action: () => logger.userAction('inventory clicked', 'Dashboard')
    }
  ];

  const dataImportSections = [
    {
      title: 'Menu Data',
      description: 'Import menu items, categories, and pricing',
      formats: ['CSV', 'Excel', 'JSON'],
      icon: <Utensils className="h-5 w-5 text-orange-600" />
    },
    {
      title: 'Staff Information',
      description: 'Import employee data and schedules',
      formats: ['CSV', 'Excel'],
      icon: <Users className="h-5 w-5 text-blue-600" />
    },
    {
      title: 'Inventory Data',
      description: 'Import product catalog and stock levels',
      formats: ['CSV', 'Excel', 'PDF'],
      icon: <Package className="h-5 w-5 text-green-600" />
    },
    {
      title: 'Financial Records',
      description: 'Import transaction history and reports',
      formats: ['CSV', 'PDF', 'Excel'],
      icon: <DollarSign className="h-5 w-5 text-purple-600" />
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Building2 className="h-8 w-8 text-blue-600" />
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    {currentRestaurant?.name || 'Restaurant Dashboard'}
                  </h1>
                  <p className="text-sm text-gray-500">
                    Welcome back, {user?.name || 'User'}
                  </p>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Badge variant="outline" className="text-green-700 border-green-200">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                Online
              </Badge>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Today's Revenue</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.todayRevenue}</div>
              <p className="text-xs text-muted-foreground">
                <TrendingUp className="h-3 w-3 inline mr-1" />
                +12% from yesterday
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Customers Today</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.todayCustomers}</div>
              <p className="text-xs text-muted-foreground">
                +8% from yesterday
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Inventory Items</CardTitle>
              <ShoppingBasket className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.inventoryItems}</div>
              <p className="text-xs text-muted-foreground">
                3 items low stock
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Staff on Duty</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.staffOnDuty}</div>
              <p className="text-xs text-muted-foreground">
                2 shifts remaining
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Main Dashboard Tabs */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="quick-actions">Quick Actions</TabsTrigger>
            <TabsTrigger value="data-import">Data Import</TabsTrigger>
            <TabsTrigger value="reports">Reports</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5" />
                    Recent Activity
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-sm">New order #1234 received</span>
                      </div>
                      <span className="text-xs text-gray-500">2 min ago</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span className="text-sm">Staff member clocked in</span>
                      </div>
                      <span className="text-xs text-gray-500">5 min ago</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                        <span className="text-sm">Low stock alert: Tomatoes</span>
                      </div>
                      <span className="text-xs text-gray-500">15 min ago</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <ChefHat className="h-5 w-5" />
                    Today's Menu Highlights
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Grilled Salmon</span>
                      <Badge variant="secondary">12 sold</Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Pasta Carbonara</span>
                      <Badge variant="secondary">8 sold</Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Caesar Salad</span>
                      <Badge variant="secondary">15 sold</Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Quick Actions Tab */}
          <TabsContent value="quick-actions" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {quickActions.map((action) => (
                <Card key={action.id} className="cursor-pointer hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex flex-col items-center text-center space-y-3">
                      <div className="p-3 bg-blue-100 rounded-full">
                        {action.icon}
                      </div>
                      <div>
                        <h3 className="font-semibold text-sm">{action.title}</h3>
                        <p className="text-xs text-gray-500 mt-1">{action.description}</p>
                      </div>
                      <Button size="sm" onClick={action.action} className="w-full">
                        Get Started
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Data Import Tab */}
          <TabsContent value="data-import" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Upload className="h-5 w-5" />
                  Import Your Data
                </CardTitle>
                <p className="text-sm text-gray-600">
                  Upload your existing data to get started quickly. We support multiple formats for easy migration.
                </p>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {dataImportSections.map((section, index) => (
                    <Card key={index} className="border-dashed">
                      <CardContent className="p-6">
                        <div className="flex items-start space-x-4">
                          <div className="p-2 bg-gray-100 rounded-lg">
                            {section.icon}
                          </div>
                          <div className="flex-1">
                            <h3 className="font-semibold text-sm mb-1">{section.title}</h3>
                            <p className="text-xs text-gray-600 mb-3">{section.description}</p>
                            <div className="flex flex-wrap gap-1 mb-3">
                              {section.formats.map((format) => (
                                <Badge key={format} variant="outline" className="text-xs">
                                  {format}
                                </Badge>
                              ))}
                            </div>
                            <Button size="sm" variant="outline" className="w-full">
                              <Upload className="h-3 w-3 mr-2" />
                              Upload {section.title}
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Reports Tab */}
          <TabsContent value="reports" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Generate Reports
                </CardTitle>
                <p className="text-sm text-gray-600">
                  Create detailed reports for your restaurant operations and performance analysis.
                </p>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Button variant="outline" className="h-20 flex flex-col items-center justify-center space-y-2">
                    <BarChart3 className="h-5 w-5" />
                    <span className="text-sm">Sales Report</span>
                  </Button>
                  <Button variant="outline" className="h-20 flex flex-col items-center justify-center space-y-2">
                    <Users className="h-5 w-5" />
                    <span className="text-sm">Staff Report</span>
                  </Button>
                  <Button variant="outline" className="h-20 flex flex-col items-center justify-center space-y-2">
                    <Package className="h-5 w-5" />
                    <span className="text-sm">Inventory Report</span>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Dashboard;
