// Verification script for ForecastBasedScheduler fix
// This script can be run in the browser console to verify the fix

console.log('🔧 ForecastBasedScheduler Fix Verification');
console.log('==========================================');

// Check if we're on the correct page
const currentPath = window.location.pathname;
console.log(`Current path: ${currentPath}`);

// Function to check if the component is rendered without errors
function checkForecastSchedulerComponent() {
    console.log('\n📋 Checking ForecastBasedScheduler component...');
    
    // Look for the forecast-based tab
    const forecastTab = document.querySelector('[data-value="forecast"], [value="forecast"]');
    if (forecastTab) {
        console.log('✅ Forecast-Based tab found');
        
        // Click the tab to activate it
        forecastTab.click();
        
        setTimeout(() => {
            // Check if the component content is rendered
            const forecastContent = document.querySelector('[data-state="active"] .forecast-based-scheduler, .forecast-based-scheduler');
            const cardTitle = document.querySelector('h3, .card-title');
            
            if (cardTitle && cardTitle.textContent.includes('Forecast-Based')) {
                console.log('✅ ForecastBasedScheduler component rendered successfully');
                console.log('✅ No "forecastData is not defined" error detected');
                
                // Check for forecast data display
                const forecastDataText = document.querySelector('*');
                if (forecastDataText && forecastDataText.textContent.includes('days of forecast data available')) {
                    console.log('✅ Forecast data is being displayed correctly');
                } else {
                    console.log('⚠️ Forecast data display not found (may be expected if no data)');
                }
                
                return true;
            } else {
                console.log('❌ ForecastBasedScheduler component not found or not rendered');
                return false;
            }
        }, 500);
    } else {
        console.log('❌ Forecast-Based tab not found');
        return false;
    }
}

// Function to check for JavaScript errors
function checkForErrors() {
    console.log('\n🐛 Checking for JavaScript errors...');
    
    // Override console.error to catch errors
    const originalError = console.error;
    let errorCount = 0;
    
    console.error = function(...args) {
        errorCount++;
        if (args.some(arg => typeof arg === 'string' && arg.includes('forecastData'))) {
            console.log('❌ Found forecastData related error:', ...args);
        }
        originalError.apply(console, args);
    };
    
    setTimeout(() => {
        if (errorCount === 0) {
            console.log('✅ No JavaScript errors detected');
        } else {
            console.log(`⚠️ ${errorCount} JavaScript errors detected (check console for details)`);
        }
        
        // Restore original console.error
        console.error = originalError;
    }, 2000);
}

// Function to verify the fix
function verifyFix() {
    console.log('\n🔍 Verifying ForecastBasedScheduler fix...');
    console.log('Fix details:');
    console.log('- Added forecastData to component props destructuring');
    console.log('- Fixed TypeScript DateRange type error');
    console.log('- Removed unused imports and state variables');
    console.log('- Component should now render without ReferenceError');
    
    checkForErrors();
    
    if (currentPath.includes('schedule') || currentPath === '/') {
        checkForecastSchedulerComponent();
    } else {
        console.log('⚠️ Not on Schedule page. Navigate to /schedule to test the component.');
    }
}

// Run verification
verifyFix();

// Export functions for manual testing
window.forecastSchedulerTest = {
    checkComponent: checkForecastSchedulerComponent,
    checkErrors: checkForErrors,
    verify: verifyFix
};

console.log('\n📝 Manual testing commands:');
console.log('- forecastSchedulerTest.verify() - Run full verification');
console.log('- forecastSchedulerTest.checkComponent() - Check component rendering');
console.log('- forecastSchedulerTest.checkErrors() - Check for errors');
