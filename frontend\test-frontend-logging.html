<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend Logging Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Frontend Logging Test</h1>
    <p>This page tests the frontend logging functionality to ensure the 422 error is fixed.</p>

    <div class="test-section">
        <h2>Test Results</h2>
        <div id="results"></div>
    </div>

    <div class="test-section">
        <h2>Manual Tests</h2>
        <button onclick="testStringId()">Test String ID (Should Work)</button>
        <button onclick="testNumericId()">Test Numeric ID (Should Fail)</button>
        <button onclick="testErrorLog()">Test Error Log</button>
        <button onclick="testWarningLog()">Test Warning Log</button>
    </div>

    <div class="test-section">
        <h2>Log Details</h2>
        <pre id="logDetails"></pre>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:5001';
        const resultsDiv = document.getElementById('results');
        const logDetailsDiv = document.getElementById('logDetails');

        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-section ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            resultsDiv.appendChild(div);
        }

        function updateLogDetails(details) {
            logDetailsDiv.textContent = JSON.stringify(details, null, 2);
        }

        // Generate ID like the fixed logger
        function generateLogId() {
            return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }

        // Generate session ID
        function generateSessionId() {
            return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }

        async function sendLogToBackend(logEntry) {
            try {
                const response = await fetch(`${API_BASE_URL}/api/logs/frontend`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(logEntry)
                });

                const responseData = await response.json();
                
                if (response.ok) {
                    addResult(`✅ Log sent successfully: ${responseData.message}`, 'success');
                    updateLogDetails({ request: logEntry, response: responseData });
                } else {
                    addResult(`❌ Log failed with status ${response.status}: ${responseData.detail || 'Unknown error'}`, 'error');
                    updateLogDetails({ request: logEntry, response: responseData, status: response.status });
                }
            } catch (error) {
                addResult(`❌ Network error: ${error.message}`, 'error');
                updateLogDetails({ request: logEntry, error: error.message });
            }
        }

        async function testStringId() {
            const logEntry = {
                id: generateLogId(), // This should be a string
                timestamp: new Date().toISOString(),
                level: 'INFO',
                message: 'Test log with string ID',
                component: 'TestComponent',
                url: window.location.href,
                userAgent: navigator.userAgent,
                sessionId: generateSessionId()
            };

            addResult(`Testing with string ID: ${logEntry.id} (type: ${typeof logEntry.id})`);
            await sendLogToBackend(logEntry);
        }

        async function testNumericId() {
            const logEntry = {
                id: Date.now() + Math.random(), // This should fail - numeric ID
                timestamp: new Date().toISOString(),
                level: 'INFO',
                message: 'Test log with numeric ID (should fail)',
                component: 'TestComponent',
                url: window.location.href,
                userAgent: navigator.userAgent,
                sessionId: generateSessionId()
            };

            addResult(`Testing with numeric ID: ${logEntry.id} (type: ${typeof logEntry.id})`);
            await sendLogToBackend(logEntry);
        }

        async function testErrorLog() {
            const logEntry = {
                id: generateLogId(),
                timestamp: new Date().toISOString(),
                level: 'ERROR',
                message: 'Test error log message',
                component: 'TestComponent',
                url: window.location.href,
                userAgent: navigator.userAgent,
                sessionId: generateSessionId(),
                data: {
                    errorCode: 'TEST_ERROR',
                    details: 'This is a test error for logging validation'
                }
            };

            addResult('Testing ERROR level log (should be sent to backend)');
            await sendLogToBackend(logEntry);
        }

        async function testWarningLog() {
            const logEntry = {
                id: generateLogId(),
                timestamp: new Date().toISOString(),
                level: 'WARN',
                message: 'Test warning log message',
                component: 'TestComponent',
                url: window.location.href,
                userAgent: navigator.userAgent,
                sessionId: generateSessionId(),
                data: {
                    warningCode: 'TEST_WARNING',
                    details: 'This is a test warning for logging validation'
                }
            };

            addResult('Testing WARN level log (should be sent to backend)');
            await sendLogToBackend(logEntry);
        }

        // Run automatic test on page load
        window.addEventListener('load', () => {
            addResult('Frontend logging test page loaded');
            addResult('Click the buttons above to test different scenarios');
        });
    </script>
</body>
</html>
