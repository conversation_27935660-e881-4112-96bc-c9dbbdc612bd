#!/bin/bash

# Add the import statement
sed -i '1s/^/import { uploadFile, validateFileType } from "@\/utils\/fileUpload";\n/' src/pages/Inventory.tsx

# Add the handleFileUpload function
# Find the line after handleApplyImport function
LINE_NUM=$(grep -n "const handleApplyImport" src/pages/Inventory.tsx | cut -d: -f1)
END_LINE_NUM=$(tail -n +$LINE_NUM src/pages/Inventory.tsx | grep -n "^  };" | head -1 | cut -d: -f1)
END_LINE_NUM=$((LINE_NUM + END_LINE_NUM))
INSERT_LINE_NUM=$((END_LINE_NUM + 1))

# Insert the handleFileUpload function
sed -i "${INSERT_LINE_NUM}i\\
  // Handle file upload\\
  const handleFileUpload = async (file: File) => {\\
    if (!validateFileType(file)) {\\
      toast.error(\"Invalid file type. Only CSV, XLSX, and PDF files are allowed.\");\\
      return;\\
    }\\
\\
    setImportData(prev => ({ ...prev, processing: true }));\\
\\
    try {\\
      // Upload the file to the server\\
      const response = await uploadFile(file);\\
      \\
      if (!response.success) {\\
        throw new Error(response.error || 'Error processing file');\\
      }\\
\\
      // Process the results\\
      const processedResults = [];\\
      \\
      for (const item of response.results) {\\
        // Find matching inventory item\\
        const matchingItem = inventory.find(invItem =>\\
          invItem.name.toLowerCase().includes((item.name || '').toLowerCase())\\
        );\\
\\
        processedResults.push({\\
          name: item.name,\\
          quantity: item.quantity,\\
          unit: item.unit,\\
          matchedItem: matchingItem || null,\\
          status: matchingItem ? \"matched\" : \"not_found\"\\
        });\\
      }\\
\\
      setImportData(prev => ({\\
        ...prev,\\
        processing: false,\\
        results: processedResults\\
      }));\\
    } catch (error) {\\
      console.error(\"Error processing file:\", error);\\
      setImportData(prev => ({\\
        ...prev,\\
        processing: false,\\
        results: null\\
      }));\\
      toast.error(\"Error processing file: \" + (error as Error).message);\\
    }\\
  };" src/pages/Inventory.tsx

# Replace the Import Dialog
START_LINE_NUM=$(grep -n "{/\\* Import Dialog \\*/}" src/pages/Inventory.tsx | cut -d: -f1)
END_LINE_NUM=$(tail -n +$START_LINE_NUM src/pages/Inventory.tsx | grep -n "</Dialog>" | head -1 | cut -d: -f1)
END_LINE_NUM=$((START_LINE_NUM + END_LINE_NUM - 1))

# Create a temporary file with the new dialog content
cat > temp_dialog.txt << 'EOD'
      {/* Import Dialog */}
      <Dialog open={isImportOpen} onOpenChange={setIsImportOpen}>
        <DialogContent className="sm:max-w-[700px]">
          <DialogHeader>
            <DialogTitle>Import Inventory</DialogTitle>
            <p className="text-sm text-muted-foreground mt-1">
              Upload a file to automatically update inventory
            </p>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="fileUpload">Select File</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="fileUpload"
                  type="file"
                  accept=".csv,.xlsx,.pdf"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      // Clear previous results
                      setImportData({
                        text: "",
                        processing: false,
                        results: null
                      });
                      
                      // Handle file upload
                      handleFileUpload(file);
                    }
                  }}
                  disabled={importData.processing}
                />
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                Supported file types: CSV, XLSX, PDF
              </p>
            </div>

            {importData.processing && (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                <span className="ml-2">Processing file data...</span>
              </div>
            )}

            {importData.results && (
              <div className="border rounded-md overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Quantity</TableHead>
                      <TableHead>Unit</TableHead>
                      <TableHead>Matched Item</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {importData.results.map((result, index) => (
                      <TableRow key={index}>
                        <TableCell>{result.name || result.parsed?.name || 'Unknown'}</TableCell>
                        <TableCell>{result.quantity || result.parsed?.quantity || 'N/A'}</TableCell>
                        <TableCell>{result.unit || result.parsed?.unit || 'N/A'}</TableCell>
                        <TableCell>
                          {result.matchedItem ? (
                            <div className="text-sm">
                              <div className="font-medium">{result.matchedItem.name}</div>
                              <div className="text-xs text-muted-foreground">Current: {result.matchedItem.stock} {result.matchedItem.unit}</div>
                            </div>
                          ) : (
                            <span className="text-muted-foreground text-sm">No match found</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs ${
                            result.status === "matched" ? "bg-green-100 text-green-800" :
                            result.status === "not_found" ? "bg-yellow-100 text-yellow-800" :
                            "bg-red-100 text-red-800"
                          }`}>
                            {result.status === "matched" ? (
                              <><Check className="h-3 w-3 mr-1" /> Matched</>
                            ) : result.status === "not_found" ? (
                              <><AlertTriangle className="h-3 w-3 mr-1" /> Not Found</>
                            ) : (
                              <><X className="h-3 w-3 mr-1" /> Error</>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </div>
          <DialogFooter className="flex items-center justify-between">
            <div>
              {importData.results && (
                <div className="text-sm">
                  <span className="font-medium">
                    {importData.results.filter(r => r.status === "matched").length} of {importData.results.length} items matched
                  </span>
                </div>
              )}
            </div>
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => setIsImportOpen(false)}>
                Cancel
              </Button>
              {importData.results && (
                <Button onClick={handleApplyImport} disabled={!importData.results.some(r => r.status === "matched")}>
                  <FileText className="mr-2 h-4 w-4" />
                  Apply Changes
                </Button>
              )}
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
EOD

# Replace the dialog in the file
sed -i "${START_LINE_NUM},${END_LINE_NUM}d" src/pages/Inventory.tsx
LINE_NUM=$((START_LINE_NUM - 1))
sed -i "${LINE_NUM}r temp_dialog.txt" src/pages/Inventory.tsx

# Clean up
rm temp_dialog.txt

echo "Inventory.tsx updated successfully!"
