# Load environment variables first, before any other imports
from dotenv import load_dotenv
load_dotenv()

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from contextlib import asynccontextmanager
import os
from app.routers import menu, orders, staff, tables, inventory, analytics, auth, restaurants, file_storage, forecasting, discounts, split_bills, ai_insights, frontend_logs, health, lightweight_ai, enhanced_ai, notifications
# Import new MVC routers
from app.routers import restaurants_mvc, tables_mvc, discounts_mvc, split_bills_mvc
# Import subscription router from consolidated backend
from app.routers import subscription
from app.utils.storage import initialize_storage
from app.utils.logging_config import logger, log_startup
from app.database import init_database, cleanup_database, check_database_health

# Log application startup
log_startup("RestroManage FastAPI Backend", os.getenv("API_VERSION", "v1"))

# Lifespan event handler
@asynccontextmanager
async def lifespan(app: FastAPI):
    """Handle application startup and shutdown"""
    # Startup
    try:
        # For now, force JSON storage to ensure promo codes are available
        # TODO: Migrate promo codes from JSON to SQL database
        initialize_storage()
        logger.info("Using JSON file storage for data persistence", "Storage")

        # Initialize database for future use
        await init_database()
        logger.info("Database initialized successfully", "Database")

    except Exception as e:
        logger.error(f"Startup initialization failed: {e}", "Startup")
        # Continue with in-memory storage only
        initialize_storage()
        logger.info("Fallback to in-memory storage only", "Storage")

    yield

    # Shutdown
    try:
        await cleanup_database()
        logger.info("Database cleanup completed", "Database")
    except Exception as e:
        logger.error(f"Database cleanup failed: {e}", "Database")

# Create FastAPI app with lifespan
app = FastAPI(
    title=os.getenv("APP_NAME", "RestroManage"),
    description="RestroManage API - A comprehensive restaurant management system",
    version=os.getenv("API_VERSION", "v1"),
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan,
)

# Configure CORS
origins = [
    "http://localhost:5173",
    "http://127.0.0.1:5173",
    "http://localhost:5174",  # Previous frontend port
    "http://127.0.0.1:5174",
    "http://localhost:5175",  # Previous frontend port
    "http://127.0.0.1:5175",
    "http://localhost:5176",  # Current frontend port
    "http://127.0.0.1:5176",
    "http://localhost:3000",  # Additional common frontend ports
    "http://127.0.0.1:3000",
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD"],
    allow_headers=["*"],
    expose_headers=["*"],
    max_age=3600,  # Cache preflight requests for 1 hour
)



# Include routers
app.include_router(auth.router, prefix="/api", tags=["Authentication"])
app.include_router(restaurants.router, prefix="/api", tags=["Restaurants"])
app.include_router(menu.router, prefix="/api", tags=["Menu"])
app.include_router(orders.router, prefix="/api", tags=["Orders"])
app.include_router(staff.router, prefix="/api", tags=["Staff"])
app.include_router(tables.router, prefix="/api", tags=["Tables"])
app.include_router(inventory.router, prefix="/api", tags=["Inventory"])
app.include_router(analytics.router, prefix="/api", tags=["Analytics"])
app.include_router(forecasting.router, prefix="/api", tags=["Forecasting"])
app.include_router(file_storage.router, prefix="/api", tags=["File Storage"])
app.include_router(discounts.router, prefix="/api", tags=["Discounts"])
app.include_router(split_bills.router, prefix="/api", tags=["Split Bills"])
app.include_router(ai_insights.router, prefix="/api", tags=["AI Insights"])
app.include_router(lightweight_ai.router, prefix="/api", tags=["Lightweight AI"])
app.include_router(enhanced_ai.router, prefix="/api", tags=["Enhanced AI"])
app.include_router(notifications.router, prefix="/api", tags=["Notifications"])
app.include_router(frontend_logs.router, prefix="/api/logs", tags=["Frontend Logs"])
app.include_router(subscription.router, prefix="/api", tags=["Subscription"])
app.include_router(health.router, tags=["Health"])

# Include new MVC routers for parallel testing and gradual migration
app.include_router(restaurants_mvc.router, prefix="/api/mvc", tags=["Restaurants (MVC)"])
app.include_router(tables_mvc.router, prefix="/api/mvc", tags=["Tables (MVC)"])
app.include_router(discounts_mvc.router, prefix="/api/mvc", tags=["Discounts (MVC)"])
app.include_router(split_bills_mvc.router, prefix="/api/mvc", tags=["Split Bills (MVC)"])

# Log successful router registration
logger.info("All API routers registered successfully", "API", {
    "legacy_routers": [
        "auth", "restaurants", "menu", "orders", "staff", "tables",
        "inventory", "analytics", "forecasting", "file_storage",
        "discounts", "split_bills", "ai_insights", "lightweight_ai", "notifications", "frontend_logs", "subscription"
    ],
    "mvc_routers": [
        "restaurants_mvc", "tables_mvc", "discounts_mvc", "split_bills_mvc"
    ]
})

# Mount static files for uploads
import os
uploads_dir = "uploads"
if not os.path.exists(uploads_dir):
    os.makedirs(uploads_dir, exist_ok=True)
app.mount("/uploads", StaticFiles(directory=uploads_dir), name="uploads")

@app.get("/", tags=["Root"])
async def root():
    return {
        "message": "Welcome to RestroManage API",
        "docs": "/docs",
        "redoc": "/redoc"
    }

@app.get("/health", tags=["Health"])
async def health_check():
    """Health check endpoint with database status"""
    try:
        db_health = await check_database_health()
        return {
            "status": "healthy",
            "database": db_health,
            "api": "operational"
        }
    except Exception as e:
        return {
            "status": "degraded",
            "database": {"status": "unhealthy", "error": str(e)},
            "api": "operational"
        }
