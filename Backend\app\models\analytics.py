from pydantic import BaseModel
from typing import List

class SalesData(BaseModel):
    date: str
    sales: float
    orders: int

class RevenueData(BaseModel):
    period: str
    revenue: float

class ForecastData(BaseModel):
    day: str
    customers: int
    projectedRevenue: float

class AnalyticsResponse(BaseModel):
    daily_sales: List[SalesData]
    weekly_revenue: List[RevenueData]
    monthly_revenue: List[RevenueData]
    forecast: List[ForecastData]
