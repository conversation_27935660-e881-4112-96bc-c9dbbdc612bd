"""
Table models for RestroManage database.
Corresponds to app/models/tables.py Pydantic models.
"""

from sqlalchemy import Column, String, Boolean, Float, Text, JSON, ForeignKey, Integer, DateTime
from sqlalchemy.orm import relationship
from .base import BaseModel, TimestampMixin, StatusMixin, SoftDeleteMixin

class Table(BaseModel, TimestampMixin):
    """
    Table model for restaurant table management.
    Simplified to match existing database schema.
    """
    __tablename__ = "tables"

    # Basic information (matches existing schema)
    restaurant_id = Column(String(36), nullable=False, index=True)
    number = Column(Integer, nullable=False, index=True)
    capacity = Column(Integer, nullable=False)
    location = Column(String(100), nullable=True)
    status = Column(String(20), nullable=True)
    current_order_id = Column(String(36), nullable=True)

    # Relationships (simplified)
    # restaurant = relationship("Restaurant", back_populates="tables")
    # orders = relationship("Order", back_populates="table")
    
    def __repr__(self):
        return f"<Table(id={self.id}, number={self.number}, capacity={self.capacity}, status={self.status})>"

class TableReservation(BaseModel, TimestampMixin, StatusMixin):
    """
    Table reservation model for advance bookings.
    """
    __tablename__ = "table_reservations"
    
    # Table and restaurant association
    table_id = Column(String(36), ForeignKey("tables.id"), nullable=False, index=True)
    restaurant_id = Column(String(36), ForeignKey("restaurants.id"), nullable=False, index=True)
    
    # Reservation details
    reservation_date = Column(DateTime(timezone=True), nullable=False, index=True)
    reservation_time = Column(DateTime(timezone=True), nullable=False, index=True)
    duration_minutes = Column(Integer, default=120, nullable=False)  # Expected duration
    party_size = Column(Integer, nullable=False)
    
    # Customer information
    customer_name = Column(String(255), nullable=False)
    customer_phone = Column(String(20), nullable=False)
    customer_email = Column(String(255), nullable=True)
    customer_id = Column(String(36), nullable=True, index=True)  # For registered customers
    
    # Reservation status
    status = Column(String(20), default="confirmed", nullable=False, index=True)
    # Status: pending, confirmed, seated, completed, cancelled, no_show
    
    # Special requests
    special_requests = Column(Text, nullable=True)
    dietary_requirements = Column(JSON, nullable=True)
    occasion = Column(String(100), nullable=True)  # birthday, anniversary, etc.
    
    # Confirmation and communication
    confirmation_code = Column(String(20), nullable=True, unique=True, index=True)
    confirmation_sent = Column(Boolean, default=False, nullable=False)
    reminder_sent = Column(Boolean, default=False, nullable=False)
    
    # Arrival and seating
    arrival_time = Column(DateTime(timezone=True), nullable=True)
    seated_time = Column(DateTime(timezone=True), nullable=True)
    departure_time = Column(DateTime(timezone=True), nullable=True)
    
    # Service assignment
    assigned_server_id = Column(String(36), ForeignKey("staff.id"), nullable=True)
    
    # Deposit and payment
    deposit_required = Column(Float, nullable=True)
    deposit_paid = Column(Float, default=0.0, nullable=False)
    deposit_payment_method = Column(String(20), nullable=True)
    
    # Notes and comments
    internal_notes = Column(Text, nullable=True)
    customer_notes = Column(Text, nullable=True)
    
    # Relationships
    table = relationship("Table", back_populates="reservations")
    restaurant = relationship("Restaurant")
    assigned_server = relationship("Staff", foreign_keys=[assigned_server_id])
    
    def __repr__(self):
        return f"<TableReservation(id={self.id}, customer_name={self.customer_name}, reservation_date={self.reservation_date})>"
