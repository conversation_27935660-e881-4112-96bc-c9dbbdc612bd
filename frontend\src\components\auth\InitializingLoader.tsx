import React from 'react';
import { cn } from '@/lib/utils';

interface InitializingLoaderProps {
  message?: string;
  className?: string;
}

const InitializingLoader: React.FC<InitializingLoaderProps> = ({ 
  message = "Initializing Session...",
  className 
}) => {
  return (
    <div className={cn(
      "fixed inset-0 z-50 flex items-center justify-center bg-background",
      className
    )}>
      <div className="flex flex-col items-center space-y-6 p-8">
        {/* Logo/Brand Area */}
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
            <svg 
              className="w-6 h-6 text-primary-foreground" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" 
              />
            </svg>
          </div>
          <div>
            <h1 className="text-xl font-bold text-foreground">RestroManage</h1>
            <p className="text-sm text-muted-foreground">Restaurant Management System</p>
          </div>
        </div>

        {/* Loading Spinner */}
        <div className="relative">
          <div className="w-16 h-16 border-4 border-muted rounded-full animate-spin border-t-primary"></div>
          <div className="absolute inset-0 w-16 h-16 border-4 border-transparent rounded-full animate-pulse border-t-accent opacity-30"></div>
        </div>

        {/* Loading Message */}
        <div className="text-center space-y-2">
          <p className="text-lg font-medium text-foreground">{message}</p>
          <p className="text-sm text-muted-foreground">Please wait while we set up your session</p>
        </div>

        {/* Loading Progress Dots */}
        <div className="flex space-x-2">
          <div className="w-2 h-2 bg-primary rounded-full animate-bounce"></div>
          <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
          <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
        </div>

        {/* Additional Styling for Professional Look */}
        <div className="absolute inset-0 bg-gradient-to-br from-background via-background to-muted/10 -z-10"></div>
      </div>

      {/* Custom CSS for enhanced animations */}
      <style>{`
        @keyframes spin {
          from {
            transform: rotate(0deg);
          }
          to {
            transform: rotate(360deg);
          }
        }
        
        @keyframes pulse {
          0%, 100% {
            opacity: 0.3;
          }
          50% {
            opacity: 0.8;
          }
        }
        
        @keyframes bounce {
          0%, 100% {
            transform: translateY(0);
          }
          50% {
            transform: translateY(-8px);
          }
        }
      `}</style>
    </div>
  );
};

export default InitializingLoader;
