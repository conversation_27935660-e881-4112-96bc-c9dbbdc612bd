import { User } from '@/contexts/AuthContext';

/**
 * Get authentication headers for API requests
 */
export const getAuthHeaders = (user?: User | null): Record<string, string> => {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  if (user?.access_token) {
    headers['Authorization'] = `Bearer ${user.access_token}`;
  } else {
    // DEVELOPMENT: Provide a mock token when user is not authenticated
    // This helps bypass 401 errors during development when authentication is disabled
    headers['Authorization'] = `Bearer dev-mock-token-${Date.now()}`;
    console.log('Using development mock token for API request');
  }

  return headers;
};

/**
 * Check if user has admin role
 */
export const isAdmin = (user?: User | null): boolean => {
  return user?.role === 'admin';
};

/**
 * Check if user has manager role or higher
 */
export const isManagerOrAdmin = (user?: User | null): boolean => {
  return user?.role === 'admin' || user?.role === 'manager';
};

/**
 * Check if user token is expired
 */
export const isTokenExpired = (user?: User | null): boolean => {
  if (!user?.expires_at) return true;

  // Add 5 minute buffer before expiration
  const bufferTime = 5 * 60; // 5 minutes in seconds
  const currentTime = Date.now() / 1000;
  const expirationTime = user.expires_at - bufferTime;

  return currentTime > expirationTime;
};

/**
 * Check if token will expire soon (within 10 minutes)
 */
export const isTokenExpiringSoon = (user?: User | null): boolean => {
  if (!user?.expires_at) return true;

  const bufferTime = 10 * 60; // 10 minutes in seconds
  const currentTime = Date.now() / 1000;
  const expirationTime = user.expires_at - bufferTime;

  return currentTime > expirationTime;
};

/**
 * Get user from localStorage with token validation
 */
export const getStoredUser = (): User | null => {
  try {
    const storedUser = localStorage.getItem('user');
    if (!storedUser) return null;

    const user = JSON.parse(storedUser) as User;

    // Check if token is expired
    if (isTokenExpired(user)) {
      console.warn('Stored user token has expired, clearing session');
      localStorage.removeItem('user');
      localStorage.removeItem('isLoggedIn');
      return null;
    }

    return user;
  } catch (error) {
    console.error('Error parsing stored user:', error);
    localStorage.removeItem('user');
    localStorage.removeItem('isLoggedIn');
    return null;
  }
};

/**
 * Store user with token in localStorage
 */
export const storeUser = (user: User): void => {
  localStorage.setItem('user', JSON.stringify(user));
  localStorage.setItem('isLoggedIn', 'true');
};

/**
 * Clear user session
 */
export const clearUserSession = (): void => {
  localStorage.removeItem('user');
  localStorage.removeItem('isLoggedIn');
};
