# Architecture Overview

## 🏗️ System Architecture

RestroManage-V1 is a modern restaurant management platform built with a **microservices-inspired architecture** using React frontend and FastAPI backend.

```mermaid
graph TB
    subgraph "Frontend Layer"
        A[React App<br/>Vite Dev Server<br/>Port 5175]
        B[Vite Proxy<br/>API Routing]
    end
    
    subgraph "Backend Layer"
        C[FastAPI Server<br/>Port 5002]
        D[Authentication<br/>JWT Tokens]
        E[API Routers<br/>Modular Endpoints]
    end
    
    subgraph "Data Layer"
        F[SQLite Database<br/>Development]
        G[PostgreSQL<br/>Production]
        H[Redis Cache<br/>Sessions]
    end
    
    subgraph "External Services"
        I[AI Services<br/>Google AI/OpenAI]
        J[File Storage<br/>Local/Cloud]
    end
    
    A --> B
    B --> C
    C --> D
    C --> E
    E --> F
    E --> G
    C --> H
    E --> I
    E --> J
```

## 🔧 Technology Stack

### Frontend
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite (Fast HMR, ES modules)
- **Styling**: Tailwind CSS + shadcn/ui components
- **State Management**: Zustand
- **HTTP Client**: Fetch API with custom hooks
- **Development Server**: Vite dev server with proxy

### Backend
- **Framework**: FastAPI (Python 3.11+)
- **Architecture**: MVC Pattern with Router-Controller separation
- **Authentication**: JWT tokens with bcrypt hashing
- **Database ORM**: SQLAlchemy with async support
- **API Documentation**: Auto-generated OpenAPI/Swagger
- **CORS**: Configured for cross-origin requests

### Database
- **Development**: SQLite with async support
- **Production**: PostgreSQL
- **Caching**: Redis for sessions and performance
- **Migrations**: Alembic for database versioning

## 📁 Project Structure

```
RestroManage-V1/
├── frontend/                 # React frontend application
│   ├── src/
│   │   ├── components/      # Reusable UI components
│   │   ├── pages/          # Page components
│   │   ├── hooks/          # Custom React hooks
│   │   ├── utils/          # Utility functions
│   │   └── types/          # TypeScript type definitions
│   ├── public/             # Static assets
│   ├── vite.config.ts      # Vite configuration
│   └── package.json        # Frontend dependencies
│
├── Backend/                 # FastAPI backend application
│   ├── app/
│   │   ├── api.py          # Main FastAPI application
│   │   ├── routers/        # API route handlers
│   │   ├── models/         # Pydantic models
│   │   ├── controllers/    # Business logic (MVC)
│   │   ├── utils/          # Utility functions
│   │   └── config.py       # Configuration settings
│   ├── main.py             # Application entry point
│   └── requirements.txt    # Python dependencies
│
├── docs/                   # Documentation
└── docker-compose.yml      # Container orchestration
```

## 🔄 Request Flow

### 1. Frontend Request
```
User Action → React Component → API Hook → Vite Proxy
```

### 2. Proxy Routing
```
Vite Proxy (5175) → FastAPI Server (5002)
```

### 3. Backend Processing
```
FastAPI Router → Controller → Database/Service → Response
```

### 4. Response Flow
```
FastAPI Response → Vite Proxy → React Component → UI Update
```

## 🔐 Authentication Flow

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant B as Backend
    participant D as Database
    
    U->>F: Login Request
    F->>B: POST /api/auth/login
    B->>D: Validate Credentials
    D-->>B: User Data
    B-->>F: JWT Token
    F->>F: Store Token
    
    Note over F,B: Subsequent Requests
    F->>B: API Request + Bearer Token
    B->>B: Validate JWT
    B-->>F: Protected Resource
```

## 📊 Data Flow Architecture

### Restaurant-Centric Design
All data is organized around restaurant entities:

```
Restaurant (Root Entity)
├── Users & Staff
├── Menu Items
├── Orders & Transactions
├── Tables & Reservations
├── Inventory
├── Analytics & Reports
└── Notifications
```

### API Design Patterns
- **RESTful Endpoints**: Standard HTTP methods (GET, POST, PUT, DELETE)
- **Resource-Based URLs**: `/api/restaurants/{id}/orders`
- **Consistent Response Format**: Standardized JSON responses
- **Error Handling**: HTTP status codes with detailed error messages

## 🔧 Configuration Management

### Environment Variables
```bash
# Backend Configuration
PORT=5002                    # Server port
DATABASE_URL=sqlite:///...   # Database connection
SECRET_KEY=...              # JWT signing key
REDIS_URL=redis://...       # Cache connection

# Frontend Configuration
VITE_API_URL=http://localhost:5002  # Backend URL
```

### Development vs Production
- **Development**: SQLite, local file storage, debug logging
- **Production**: PostgreSQL, cloud storage, structured logging

## 🚀 Deployment Architecture

### Development
```
Local Machine
├── Frontend (Vite Dev Server) - Port 5175
├── Backend (FastAPI) - Port 5002
├── SQLite Database
└── Local File Storage
```

### Production (Docker)
```
Container Orchestration
├── Frontend Container (Nginx) - Port 80/443
├── Backend Container (Gunicorn) - Port 8000
├── PostgreSQL Container - Port 5432
├── Redis Container - Port 6379
└── Shared Volumes for Storage
```

## 🔍 Monitoring & Observability

### Health Checks
- **Basic**: `/health` - Simple status check
- **Detailed**: `/health/detailed` - Full dependency check
- **Readiness**: `/health/ready` - Kubernetes readiness probe
- **Liveness**: `/health/live` - Kubernetes liveness probe

### Logging
- **Structured Logging**: JSON format for production
- **Log Levels**: DEBUG, INFO, WARNING, ERROR, CRITICAL
- **Request Tracing**: Unique request IDs for tracking

## 🔧 Development Workflow

### Local Development
1. **Backend**: `cd Backend && python main.py`
2. **Frontend**: `cd frontend && npm run dev`
3. **Database**: Auto-created SQLite file
4. **Proxy**: Vite handles API routing

### Testing Strategy
- **Unit Tests**: Individual component testing
- **Integration Tests**: API endpoint testing
- **E2E Tests**: Full user workflow testing

## 📈 Scalability Considerations

### Horizontal Scaling
- **Stateless Backend**: JWT tokens, no server sessions
- **Database Connection Pooling**: Efficient resource usage
- **Caching Strategy**: Redis for frequently accessed data

### Performance Optimization
- **Frontend**: Code splitting, lazy loading, asset optimization
- **Backend**: Async operations, database indexing, query optimization
- **Caching**: API response caching, static asset caching

---

**Next Steps**: 
- Review [Development Setup](./development-setup.md) for local environment
- Check [Proxy Configuration](./proxy-configuration.md) for connection setup
- See [API Documentation](./api-documentation.md) for endpoint details
