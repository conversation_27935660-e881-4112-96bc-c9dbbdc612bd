import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  ChefHat, 
  BarChart3, 
  Calendar, 
  Users, 
  ShoppingBasket, 
  Settings, 
  Star, 
  Clock, 
  Table, 
  ClipboardList, 
  Bell, 
  Shield, 
  Smartphone 
} from "lucide-react";
import MarketingLayout from "@/components/marketing/MarketingLayout";

const Features = () => {
  return (
    <MarketingLayout>
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">Powerful Features for Modern Restaurants</h1>
            <p className="text-xl mb-8">
              Discover all the tools you need to streamline operations, increase efficiency, and grow your restaurant business.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/register">
                <Button size="lg" className="bg-white text-blue-600 hover:bg-white/90 w-full sm:w-auto">
                  Start Free Trial
                </Button>
              </Link>
              <Link to="/demo">
                <Button size="lg" variant="outline" className="text-white border-white hover:bg-white/10 w-full sm:w-auto">
                  Request Demo
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Main Features */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold mb-4">Core Features</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Everything you need to manage your restaurant efficiently
            </p>
          </div>

          <div className="space-y-24">
            {/* Dashboard Feature */}
            <FeatureRow
              title="Comprehensive Dashboard"
              description="Get a real-time overview of your restaurant's performance with our intuitive dashboard. Monitor sales, customer traffic, inventory levels, and staff performance at a glance."
              image="/images/dashboard-feature.png"
              fallbackImage="https://placehold.co/600x400/3b82f6/ffffff?text=Dashboard+Feature"
              features={[
                "Real-time sales tracking",
                "Revenue forecasting",
                "Staff performance metrics",
                "Inventory alerts",
                "Customer feedback summary"
              ]}
              icon={<BarChart3 size={24} className="text-blue-600" />}
              reverse={false}
            />

            {/* Staff Management Feature */}
            <FeatureRow
              title="Staff Management"
              description="Simplify staff scheduling, track performance, and manage your team efficiently. Reduce scheduling conflicts and ensure optimal staffing levels at all times."
              image="/images/staff-feature.png"
              fallbackImage="https://placehold.co/600x400/3b82f6/ffffff?text=Staff+Management+Feature"
              features={[
                "Drag-and-drop scheduling",
                "Performance tracking",
                "Time and attendance",
                "Shift swapping",
                "Automated notifications"
              ]}
              icon={<Users size={24} className="text-blue-600" />}
              reverse={true}
            />

            {/* Inventory Management Feature */}
            <FeatureRow
              title="Inventory Control"
              description="Keep track of your inventory in real-time, reduce waste, and optimize your ordering process. Get alerts when items are running low and generate purchase orders with a single click."
              image="/images/inventory-feature.png"
              fallbackImage="https://placehold.co/600x400/3b82f6/ffffff?text=Inventory+Feature"
              features={[
                "Real-time stock tracking",
                "Automatic reorder alerts",
                "Supplier management",
                "Waste tracking",
                "Inventory valuation reports"
              ]}
              icon={<ShoppingBasket size={24} className="text-blue-600" />}
              reverse={false}
            />

            {/* Table Management Feature */}
            <FeatureRow
              title="Table Management"
              description="Optimize your seating arrangements, track table turnover, and improve the dining experience for your customers. Visualize your restaurant floor plan and manage reservations efficiently."
              image="/images/table-feature.png"
              fallbackImage="https://placehold.co/600x400/3b82f6/ffffff?text=Table+Management+Feature"
              features={[
                "Interactive floor plan",
                "Table turnover tracking",
                "Reservation management",
                "Wait time estimation",
                "Server assignment"
              ]}
              icon={<Table size={24} className="text-blue-600" />}
              reverse={true}
            />
          </div>
        </div>
      </section>

      {/* Additional Features Grid */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold mb-4">Additional Features</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Explore more powerful tools to enhance your restaurant management
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <FeatureCard 
              icon={<Calendar size={24} className="text-blue-600" />}
              title="Reservation System"
              description="Manage table bookings, send confirmations, and reduce no-shows with automated reminders."
            />
            <FeatureCard 
              icon={<ClipboardList size={24} className="text-blue-600" />}
              title="Reporting & Analytics"
              description="Generate detailed reports on sales, inventory, staff performance, and customer behavior."
            />
            <FeatureCard 
              icon={<Star size={24} className="text-blue-600" />}
              title="Customer Feedback"
              description="Collect and analyze customer reviews to improve your service quality and menu offerings."
            />
            <FeatureCard 
              icon={<Bell size={24} className="text-blue-600" />}
              title="Notifications & Alerts"
              description="Stay informed with real-time alerts for inventory levels, reservations, and staff schedule changes."
            />
            <FeatureCard 
              icon={<Shield size={24} className="text-blue-600" />}
              title="Secure Data Storage"
              description="Keep your restaurant data safe with enterprise-grade security and regular backups."
            />
            <FeatureCard 
              icon={<Smartphone size={24} className="text-blue-600" />}
              title="Mobile Access"
              description="Manage your restaurant from anywhere with our mobile-responsive design and dedicated app."
            />
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-blue-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">Ready to Experience These Features?</h2>
          <p className="text-xl text-white/80 max-w-3xl mx-auto mb-8">
            Start your free trial today and see how Promith can transform your restaurant operations.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/register">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-white/90 w-full sm:w-auto">
                Start Free Trial
              </Button>
            </Link>
            <Link to="/demo">
              <Button size="lg" variant="outline" className="text-white border-white hover:bg-white/10 w-full sm:w-auto">
                Request Demo
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </MarketingLayout>
  );
};

// Helper Components
const FeatureCard = ({ icon, title, description }: { icon: React.ReactNode, title: string, description: string }) => {
  return (
    <div className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow">
      <div className="bg-blue-50 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
        {icon}
      </div>
      <h3 className="text-xl font-semibold mb-2">{title}</h3>
      <p className="text-gray-600">{description}</p>
    </div>
  );
};

const FeatureRow = ({ 
  title, 
  description, 
  image, 
  fallbackImage,
  features, 
  icon, 
  reverse 
}: { 
  title: string, 
  description: string, 
  image: string, 
  fallbackImage: string,
  features: string[], 
  icon: React.ReactNode, 
  reverse: boolean 
}) => {
  return (
    <div className={`flex flex-col ${reverse ? 'lg:flex-row-reverse' : 'lg:flex-row'} items-center gap-12`}>
      <div className="lg:w-1/2">
        <img 
          src={image} 
          alt={title} 
          className="rounded-lg shadow-xl w-full"
          onError={(e) => {
            e.currentTarget.src = fallbackImage;
          }}
        />
      </div>
      <div className="lg:w-1/2 space-y-6">
        <div className="flex items-center gap-3">
          <div className="bg-blue-50 w-10 h-10 rounded-lg flex items-center justify-center">
            {icon}
          </div>
          <h3 className="text-2xl font-bold">{title}</h3>
        </div>
        <p className="text-lg text-gray-700">{description}</p>
        <ul className="space-y-3">
          {features.map((feature, index) => (
            <li key={index} className="flex items-start">
              <svg className="h-6 w-6 text-green-500 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <span className="text-gray-700">{feature}</span>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default Features;
