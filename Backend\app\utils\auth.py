from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Optional
import os
from jose import jwt
from passlib.context import Crypt<PERSON>ontext
from dotenv import load_dotenv
from fastapi import Depends, HTTPException, status, Header, Query
from fastapi.security import OAuth2PasswordBearer
from app.utils.storage import get_by_id, query
import logging

# Load environment variables
load_dotenv()

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT settings
SECRET_KEY = os.getenv("SECRET_KEY", "09d25e094faa6ca2556c818166b7a9563b93f7099f6f0f4caa6cf63b88e8d3e7")
ALGORITHM = os.getenv("ALGORITHM", "HS256")
ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))
REFRESH_TOKEN_EXPIRE_DAYS = int(os.getenv("REFRESH_TOKEN_EXPIRE_DAYS", "7"))

# OAuth2 scheme
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/auth/token")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against a hash"""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """Hash a password"""
    return pwd_context.hash(password)

def authenticate_user(username_or_email: str, password: str):
    """Authenticate a user by username or email"""
    # Helper function to check if input is email format
    def is_email(input_str: str) -> bool:
        import re
        return re.match(r'^[^\s@]+@[^\s@]+\.[^\s@]+$', input_str) is not None

    # Try to find user by email or username
    if is_email(username_or_email):
        # Search by email
        users = query("users", {"email": username_or_email})
    else:
        # Search by username
        users = query("users", {"username": username_or_email})

    if not users:
        return None

    user = users[0]
    if not verify_password(password, user.get("password_hash", "")):
        return None
    return user

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """Create an access token"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def create_refresh_token(data: dict) -> str:
    """Create a refresh token"""
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

async def get_current_user(token: str = Depends(oauth2_scheme)):
    """Get the current user from the token"""
    from app.utils.logging_config import logger

    # DEVELOPMENT: Check for mock development tokens
    if token and token.startswith("dev-mock-token-"):
        logger.info("Using development mock token bypass", "Auth")
        # Return a mock admin user for development
        mock_user = {
            "id": "dev-admin",
            "name": "Development Admin",
            "email": "<EMAIL>",
            "role": "admin",
            "restaurant_id": "1",
            "is_active": True
        }
        return mock_user

    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        user_id: str = payload.get("sub")
        if user_id is None:
            logger.warning("Token validation failed: missing user ID", "Auth")
            raise credentials_exception

        # Check token expiration
        exp = payload.get("exp")
        if exp and datetime.utcnow().timestamp() > exp:
            logger.warning("Token validation failed: token expired", "Auth", {"user_id": user_id})
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token has expired",
                headers={"WWW-Authenticate": "Bearer"},
            )

    except jwt.ExpiredSignatureError:
        logger.warning("Token validation failed: expired signature", "Auth")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token has expired",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except jwt.JWTError as e:
        logger.warning("Token validation failed: JWT error", "Auth", {"error": str(e)})
        raise credentials_exception

    user = get_by_id("users", user_id)
    if user is None:
        logger.warning("Token validation failed: user not found", "Auth", {"user_id": user_id})
        raise credentials_exception

    logger.debug("Token validation successful", "Auth", {"user_id": user_id})
    return user

async def get_current_active_user(current_user = Depends(get_current_user)):
    """Get the current active user"""
    if not current_user.get("is_active", False):
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user

def check_admin_role(user):
    """Check if the user has admin role"""
    if user.get("role") != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    return True

def check_manager_role(user):
    """Check if the user has manager or admin role"""
    if user.get("role") not in ["admin", "manager"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    return True

# Restaurant ID utilities for multi-tenant support
logger = logging.getLogger(__name__)

async def get_current_restaurant_id(
    restaurant_id: Optional[str] = Query(None, description="Restaurant ID for multi-tenant filtering"),
    x_restaurant_id: Optional[str] = Header(None, description="Restaurant ID from header")
) -> str:
    """
    Get the current restaurant ID from query parameter or header.
    For production, this should be extracted from JWT token.
    """
    # Priority: Query parameter > Header > Default
    current_restaurant_id = restaurant_id or x_restaurant_id

    if not current_restaurant_id:
        # For development, use a default restaurant ID
        # In production, this should raise an authentication error
        logger.warning("No restaurant ID provided, using default for development")
        return "4b41fcac-d638-43f1-b10b-0530d86fa781"  # Default restaurant ID from DB

    return current_restaurant_id

async def get_optional_restaurant_id(
    restaurant_id: Optional[str] = Query(None, description="Restaurant ID for filtering"),
    x_restaurant_id: Optional[str] = Header(None, description="Restaurant ID from header")
) -> Optional[str]:
    """
    Get restaurant ID optionally (for endpoints that can work without it)
    """
    return restaurant_id or x_restaurant_id

def validate_restaurant_access(restaurant_id: str, user_restaurant_id: str) -> bool:
    """
    Validate that user has access to the specified restaurant.
    In production, this should check user permissions.
    """
    # For development, allow access to any restaurant
    # In production, implement proper access control
    return True
