# Deployment Guide

## 🚀 Production Deployment

### Docker Deployment (Recommended)

#### Prerequisites
- Docker 20.10+
- Docker Compose 2.0+
- Domain name (for production)
- SSL certificate (Let's Encrypt recommended)

#### Quick Start
```bash
# Clone repository
git clone https://github.com/prathamsurti/RestroManage-V1.git
cd RestroManage-V1

# Configure environment
cp Backend/.env.example Backend/.env
# Edit Backend/.env with production values

# Build and start services
docker-compose up -d

# Check status
docker-compose ps
```

#### Production Environment Variables
```env
# Backend/.env
ENVIRONMENT=production
DATABASE_URL=****************************************/restro_manage
REDIS_URL=redis://redis:6379/0
SECRET_KEY=your-super-secure-production-secret-key-32-chars-minimum
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Security
DEBUG=false
LOG_LEVEL=INFO
ENABLE_SQL_LOGGING=false

# Performance
WORKERS=4
MAX_REQUESTS=1000
TIMEOUT=120
```

### Railway Deployment

#### Setup
```bash
# Install Railway CLI
npm install -g @railway/cli

# Login to Railway
railway login

# Create new project
railway init

# Deploy backend
cd Backend
railway up

# Deploy frontend
cd ../frontend
railway up
```

#### Railway Configuration
```json
// railway.json
{
  "build": {
    "builder": "DOCKERFILE",
    "dockerfilePath": "Dockerfile.railway"
  },
  "deploy": {
    "startCommand": "./railway-startup.sh",
    "healthcheckPath": "/health",
    "healthcheckTimeout": 300
  }
}
```

### Manual Server Deployment

#### Server Requirements
- Ubuntu 20.04+ or CentOS 8+
- Python 3.11+
- Node.js 18+
- PostgreSQL 13+
- Redis 6+
- Nginx 1.18+

#### Backend Setup
```bash
# Install system dependencies
sudo apt update
sudo apt install python3.11 python3.11-venv postgresql redis-server nginx

# Create application user
sudo useradd -m -s /bin/bash restro
sudo su - restro

# Clone and setup application
git clone https://github.com/prathamsurti/RestroManage-V1.git
cd RestroManage-V1/Backend

# Create virtual environment
python3.11 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
pip install gunicorn

# Configure environment
cp .env.example .env
# Edit .env with production values

# Run database migrations
python -c "from app.database import init_database; init_database()"
```

#### Frontend Setup
```bash
# Install Node.js dependencies
cd ../frontend
npm install

# Build for production
npm run build

# Copy build files to web server
sudo cp -r dist/* /var/www/html/
```

#### Nginx Configuration
```nginx
# /etc/nginx/sites-available/restro-manage
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;
    
    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
    
    # Frontend
    location / {
        root /var/www/html;
        try_files $uri $uri/ /index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # Backend API
    location /api/ {
        proxy_pass http://127.0.0.1:8000/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Health check
    location /health {
        proxy_pass http://127.0.0.1:8000/health;
        proxy_set_header Host $host;
    }
}
```

#### Systemd Service
```ini
# /etc/systemd/system/restro-backend.service
[Unit]
Description=RestroManage Backend
After=network.target postgresql.service redis.service

[Service]
Type=exec
User=restro
Group=restro
WorkingDirectory=/home/<USER>/RestroManage-V1/Backend
Environment=PATH=/home/<USER>/RestroManage-V1/Backend/venv/bin
ExecStart=/home/<USER>/RestroManage-V1/Backend/venv/bin/gunicorn app.api:app \
    --worker-class uvicorn.workers.UvicornWorker \
    --workers 4 \
    --bind 127.0.0.1:8000 \
    --timeout 120 \
    --keep-alive 2 \
    --max-requests 1000 \
    --access-logfile /var/log/restro/access.log \
    --error-logfile /var/log/restro/error.log
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

## 🔧 Configuration Management

### Environment-Specific Configs

#### Development
```env
ENVIRONMENT=development
DATABASE_URL=sqlite+aiosqlite:///./restro_manage.db
DEBUG=true
LOG_LEVEL=DEBUG
ALLOWED_ORIGINS=http://localhost:5175,http://localhost:3000
```

#### Staging
```env
ENVIRONMENT=staging
DATABASE_URL=**************************************/restro_staging
DEBUG=false
LOG_LEVEL=INFO
ALLOWED_ORIGINS=https://staging.yourdomain.com
```

#### Production
```env
ENVIRONMENT=production
DATABASE_URL=***********************************/restro_production
DEBUG=false
LOG_LEVEL=WARNING
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
```

### SSL/TLS Setup

#### Let's Encrypt (Free SSL)
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 📊 Monitoring & Logging

### Application Monitoring
```python
# Backend monitoring setup
from prometheus_client import Counter, Histogram, generate_latest

REQUEST_COUNT = Counter('http_requests_total', 'Total HTTP requests', ['method', 'endpoint'])
REQUEST_DURATION = Histogram('http_request_duration_seconds', 'HTTP request duration')

@app.middleware("http")
async def monitor_requests(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    duration = time.time() - start_time
    
    REQUEST_COUNT.labels(method=request.method, endpoint=request.url.path).inc()
    REQUEST_DURATION.observe(duration)
    
    return response
```

### Log Management
```bash
# Centralized logging with rsyslog
# /etc/rsyslog.d/restro.conf
$template RestroFormat,"%timestamp% %hostname% %syslogtag% %msg%\n"
local0.*    /var/log/restro/application.log;RestroFormat
& stop
```

### Health Checks
```bash
# Automated health monitoring
#!/bin/bash
# /usr/local/bin/health-check.sh

BACKEND_URL="http://localhost:8000/health"
FRONTEND_URL="https://yourdomain.com"

# Check backend
if ! curl -f $BACKEND_URL > /dev/null 2>&1; then
    echo "Backend health check failed" | logger -t restro-monitor
    systemctl restart restro-backend
fi

# Check frontend
if ! curl -f $FRONTEND_URL > /dev/null 2>&1; then
    echo "Frontend health check failed" | logger -t restro-monitor
    systemctl restart nginx
fi
```

## 🔄 CI/CD Pipeline

### GitHub Actions
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Test Backend
        run: |
          cd Backend
          python -m pytest
      - name: Test Frontend
        run: |
          cd frontend
          npm test

  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Deploy to Railway
        run: |
          railway deploy
```

## 📋 Deployment Checklist

### Pre-Deployment
- [ ] **Environment Variables**: All production values set
- [ ] **Database**: PostgreSQL configured and accessible
- [ ] **SSL Certificate**: Valid and properly configured
- [ ] **Domain**: DNS pointing to server
- [ ] **Firewall**: Ports 80, 443 open
- [ ] **Backup Strategy**: Database backup configured

### Post-Deployment
- [ ] **Health Checks**: All endpoints responding
- [ ] **SSL**: HTTPS working correctly
- [ ] **Performance**: Response times acceptable
- [ ] **Monitoring**: Logs and metrics collecting
- [ ] **Security**: Security headers configured
- [ ] **Backup**: First backup completed

### Rollback Plan
```bash
# Quick rollback procedure
# 1. Stop current services
sudo systemctl stop restro-backend nginx

# 2. Restore previous version
git checkout previous-stable-tag
docker-compose down
docker-compose up -d

# 3. Restore database if needed
psql restro_production < backup.sql

# 4. Restart services
sudo systemctl start restro-backend nginx
```

---

**Next Steps**:
- Set up monitoring with [Troubleshooting Guide](./troubleshooting-guide.md)
- Configure development environment with [Development Setup](./development-setup.md)
- Review API endpoints in [API Documentation](./api-documentation.md)
