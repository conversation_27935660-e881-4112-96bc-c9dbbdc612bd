"""
Base models and mixins for RestroManage application.
Provides common functionality and validation for all models.
"""

from pydantic import BaseModel as PydanticBaseModel, Field, field_validator
from typing import Optional, Any, Dict, List
from datetime import datetime
from enum import Enum
import uuid

class BaseModel(PydanticBaseModel):
    """Base model with common configuration"""
    
    class Config:
        # Allow population by field name and alias
        populate_by_name = True
        # Use enum values instead of enum names
        use_enum_values = True
        # Validate assignment
        validate_assignment = True
        # Allow extra fields for flexibility
        extra = "forbid"
        # JSON schema extra
        json_schema_extra = {
            "example": {}
        }

class IDMixin(BaseModel):
    """Mixin for models that have an ID field"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Unique identifier")

class TimestampMixin(BaseModel):
    """Mixin for models that have timestamp fields"""
    created_at: datetime = Field(default_factory=datetime.now, description="Creation timestamp")
    updated_at: datetime = Field(default_factory=datetime.now, description="Last update timestamp")

class SoftDeleteMixin(BaseModel):
    """Mixin for models that support soft deletion"""
    is_deleted: bool = Field(default=False, description="Soft deletion flag")
    deleted_at: Optional[datetime] = Field(default=None, description="Deletion timestamp")

class StatusMixin(BaseModel):
    """Mixin for models that have a status field"""
    is_active: bool = Field(default=True, description="Active status flag")

class MetadataMixin(BaseModel):
    """Mixin for models that have metadata"""
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="Additional metadata")
    tags: Optional[List[str]] = Field(default=None, description="Tags for categorization")

class AuditMixin(BaseModel):
    """Mixin for models that need audit trail"""
    created_by: Optional[str] = Field(default=None, description="User who created this record")
    updated_by: Optional[str] = Field(default=None, description="User who last updated this record")
    version: int = Field(default=1, description="Version number for optimistic locking")

class LocationMixin(BaseModel):
    """Mixin for models that have location information"""
    latitude: Optional[float] = Field(default=None, ge=-90, le=90, description="Latitude coordinate")
    longitude: Optional[float] = Field(default=None, ge=-180, le=180, description="Longitude coordinate")
    address: Optional[str] = Field(default=None, description="Physical address")
    city: Optional[str] = Field(default=None, description="City")
    country: Optional[str] = Field(default=None, description="Country")
    postal_code: Optional[str] = Field(default=None, description="Postal/ZIP code")

class ContactMixin(BaseModel):
    """Mixin for models that have contact information"""
    email: Optional[str] = Field(default=None, description="Email address")
    phone: Optional[str] = Field(default=None, description="Phone number")
    website: Optional[str] = Field(default=None, description="Website URL")

class PricingMixin(BaseModel):
    """Mixin for models that have pricing information"""
    currency: str = Field(default="GBP", description="Currency code")
    tax_rate: Optional[float] = Field(default=0.20, ge=0, le=1, description="Tax rate (0-1)")
    
    @field_validator('currency')
    @classmethod
    def validate_currency(cls, v):
        valid_currencies = ['GBP', 'USD', 'EUR', 'CAD', 'AUD']
        if v not in valid_currencies:
            raise ValueError(f'Currency must be one of {valid_currencies}')
        return v

class FullModel(IDMixin, TimestampMixin, StatusMixin, AuditMixin):
    """Full model with all common mixins"""
    pass

# Common Enums
class Priority(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"

class Status(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    PENDING = "pending"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    ARCHIVED = "archived"

class OperatingStatus(str, Enum):
    OPEN = "open"
    CLOSED = "closed"
    BUSY = "busy"
    MAINTENANCE = "maintenance"

# Validation Helpers
class ValidationHelpers:
    """Common validation helpers"""
    
    @staticmethod
    def validate_email(email: str) -> str:
        """Validate email format"""
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(pattern, email):
            raise ValueError('Invalid email format')
        return email.lower()
    
    @staticmethod
    def validate_phone(phone: str) -> str:
        """Validate phone number format"""
        import re
        # Remove all non-digit characters
        digits_only = re.sub(r'\D', '', phone)
        if len(digits_only) < 10:
            raise ValueError('Phone number must have at least 10 digits')
        return phone
    
    @staticmethod
    def validate_positive_number(value: float, field_name: str = "value") -> float:
        """Validate that a number is positive"""
        if value < 0:
            raise ValueError(f'{field_name} must be positive')
        return value
    
    @staticmethod
    def validate_percentage(value: float, field_name: str = "percentage") -> float:
        """Validate that a value is a valid percentage (0-100)"""
        if not 0 <= value <= 100:
            raise ValueError(f'{field_name} must be between 0 and 100')
        return value
    
    @staticmethod
    def validate_non_empty_string(value: str, field_name: str = "field") -> str:
        """Validate that a string is not empty"""
        if not value or not value.strip():
            raise ValueError(f'{field_name} cannot be empty')
        return value.strip()

# Response Models
class SuccessResponse(BaseModel):
    """Standard success response"""
    success: bool = True
    message: str = "Operation completed successfully"
    data: Optional[Any] = None

class ErrorResponse(BaseModel):
    """Standard error response"""
    success: bool = False
    error: str
    details: Optional[Dict[str, Any]] = None
    error_code: Optional[str] = None

class PaginatedResponse(BaseModel):
    """Paginated response wrapper"""
    items: List[Any]
    total: int
    page: int = 1
    per_page: int = 10
    pages: int
    has_next: bool
    has_prev: bool

class ListResponse(BaseModel):
    """List response wrapper"""
    items: List[Any]
    total: int
    filters_applied: Optional[Dict[str, Any]] = None

# Request Models
class PaginationParams(BaseModel):
    """Pagination parameters"""
    page: int = Field(default=1, ge=1, description="Page number")
    per_page: int = Field(default=10, ge=1, le=100, description="Items per page")
    
class SortParams(BaseModel):
    """Sorting parameters"""
    sort_by: Optional[str] = Field(default=None, description="Field to sort by")
    sort_order: Optional[str] = Field(default="asc", description="Sort order (asc/desc)")
    
    @field_validator('sort_order')
    @classmethod
    def validate_sort_order(cls, v):
        if v not in ['asc', 'desc']:
            raise ValueError('Sort order must be "asc" or "desc"')
        return v

class FilterParams(BaseModel):
    """Base filtering parameters"""
    search: Optional[str] = Field(default=None, description="Search query")
    status: Optional[str] = Field(default=None, description="Status filter")
    created_after: Optional[datetime] = Field(default=None, description="Created after date")
    created_before: Optional[datetime] = Field(default=None, description="Created before date")

class BulkOperation(BaseModel):
    """Bulk operation request"""
    ids: List[str] = Field(description="List of IDs to operate on")
    operation: str = Field(description="Operation to perform")
    parameters: Optional[Dict[str, Any]] = Field(default=None, description="Operation parameters")

class BulkOperationResult(BaseModel):
    """Bulk operation result"""
    success_count: int
    error_count: int
    total_count: int
    errors: Optional[List[Dict[str, Any]]] = None
    results: Optional[List[Any]] = None

# Health Check Models
class HealthCheck(BaseModel):
    """Health check response"""
    status: str = "healthy"
    timestamp: datetime = Field(default_factory=datetime.now)
    version: str = "1.0.0"
    services: Dict[str, str] = Field(default_factory=dict)
    uptime: Optional[float] = None

class ServiceStatus(BaseModel):
    """Individual service status"""
    name: str
    status: str  # healthy, unhealthy, degraded
    response_time: Optional[float] = None
    last_check: datetime = Field(default_factory=datetime.now)
    details: Optional[Dict[str, Any]] = None
