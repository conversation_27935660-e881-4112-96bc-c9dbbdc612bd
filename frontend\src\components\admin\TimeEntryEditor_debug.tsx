import { useState, useEffect } from "react";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, Clock, Save, X } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { toast } from "@/components/ui/sonner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  TimeEntry,
  getAllTimeEntries,
  updateTimeEntry,
  deleteTimeEntry
} from "@/services/timeEntryService";

interface TimeEntryEditorProps {
  staffData: any[];
}

const TimeEntryEditor = ({ staffData }: TimeEntryEditorProps) => {
  const [timeEntries, setTimeEntries] = useState<TimeEntry[]>([]);
  const [selectedStaffId, setSelectedStaffId] = useState<string>("");
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [currentEntry, setCurrentEntry] = useState<TimeEntry | null>(null);
  const [editedEntry, setEditedEntry] = useState<Partial<TimeEntry>>({});

  // Load time entries when component mounts or when staff/date selection changes
  useEffect(() => {
    try {
      const entries = getAllTimeEntries();
      console.log('Time entries loaded:', entries);

      // Filter entries based on selected staff and date
      const filteredEntries = entries.filter(entry => {
        const matchesStaff = selectedStaffId ? entry.staffId === selectedStaffId : true;
        const matchesDate = selectedDate
          ? entry.date === format(selectedDate, 'yyyy-MM-dd')
          : true;

        return matchesStaff && matchesDate;
      });

      setTimeEntries(filteredEntries);
      console.log('Filtered entries:', filteredEntries);
    } catch (error) {
      console.error('Error loading time entries:', error);
    }
  }, [selectedStaffId, selectedDate]);

  // Handle edit button click
  const handleEditEntry = (entry: TimeEntry) => {
    setCurrentEntry(entry);
    setEditedEntry({
      clockIn: entry.clockIn,
      clockOut: entry.clockOut,
      breakTime: entry.breakTime,
      totalHours: entry.totalHours
    });
    setIsEditDialogOpen(true);
  };

  // Calculate total hours based on clock in/out and break time
  const calculateTotalHours = (clockIn: string | null, clockOut: string | null, breakMinutes: number) => {
    if (!clockIn || !clockOut) return 0;

    const [inHours, inMinutes] = clockIn.split(':').map(Number);
    const [outHours, outMinutes] = clockOut.split(':').map(Number);

    const totalMinutes = (outHours * 60 + outMinutes) - (inHours * 60 + inMinutes) - breakMinutes;
    return Math.max(0, totalMinutes / 60);
  };

  // Handle time input changes
  const handleTimeChange = (field: 'clockIn' | 'clockOut', value: string) => {
    setEditedEntry(prev => {
      const updated = { ...prev, [field]: value };

      // Recalculate total hours if both clock in and out are present
      if (updated.clockIn && updated.clockOut) {
        updated.totalHours = calculateTotalHours(
          updated.clockIn,
          updated.clockOut,
          updated.breakTime || 0
        );
      }

      return updated;
    });
  };

  // Handle break time change
  const handleBreakTimeChange = (value: string) => {
    const breakMinutes = parseInt(value) || 0;

    setEditedEntry(prev => {
      const updated = { ...prev, breakTime: breakMinutes };

      // Recalculate total hours if both clock in and out are present
      if (updated.clockIn && updated.clockOut) {
        updated.totalHours = calculateTotalHours(
          updated.clockIn,
          updated.clockOut,
          breakMinutes
        );
      }

      return updated;
    });
  };

  // Handle save changes
  const handleSaveChanges = () => {
    if (!currentEntry) return;

    const updatedEntry: TimeEntry = {
      ...currentEntry,
      clockIn: editedEntry.clockIn || currentEntry.clockIn,
      clockOut: editedEntry.clockOut || currentEntry.clockOut,
      breakTime: editedEntry.breakTime !== undefined ? editedEntry.breakTime : currentEntry.breakTime,
      totalHours: editedEntry.totalHours !== undefined ? editedEntry.totalHours : currentEntry.totalHours
    };

    updateTimeEntry(updatedEntry);

    // Update local state
    setTimeEntries(prev =>
      prev.map(entry => entry.id === updatedEntry.id ? updatedEntry : entry)
    );

    setIsEditDialogOpen(false);
    toast.success("Time entry updated successfully");
  };

  // Handle delete entry
  const handleDeleteEntry = (entryId: string) => {
    if (window.confirm("Are you sure you want to delete this time entry?")) {
      deleteTimeEntry(entryId);

      // Update local state
      setTimeEntries(prev => prev.filter(entry => entry.id !== entryId));

      toast.success("Time entry deleted successfully");
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Get staff name by ID
  const getStaffName = (staffId: string) => {
    const staff = staffData.find(s => s.id === staffId);
    return staff ? staff.name : "Unknown Staff";
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Time Entry Management</CardTitle>
        <CardDescription>
          Edit staff clock in/out times and manage time entries
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="w-full sm:w-1/2">
            <Label htmlFor="staff-select">Staff Member</Label>
            <Select value={selectedStaffId} onValueChange={setSelectedStaffId}>
              <SelectTrigger id="staff-select">
                <SelectValue placeholder="All Staff Members" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Staff Members</SelectItem>
                {staffData.map(staff => (
                  <SelectItem key={staff.id} value={staff.id}>
                    {staff.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="w-full sm:w-1/2">
            <Label>Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !selectedDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {selectedDate ? format(selectedDate, 'PPP') : <span>Pick a date</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={selectedDate}
                  onSelect={setSelectedDate}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>

        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Date</TableHead>
              <TableHead>Staff</TableHead>
              <TableHead>Clock In</TableHead>
              <TableHead>Clock Out</TableHead>
              <TableHead>Break</TableHead>
              <TableHead>Hours</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {timeEntries.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-6 text-muted-foreground">
                  No time entries found for the selected criteria
                </TableCell>
              </TableRow>
            ) : (
              timeEntries.map((entry) => (
                <TableRow key={entry.id}>
                  <TableCell>{formatDate(entry.date)}</TableCell>
                  <TableCell>{getStaffName(entry.staffId)}</TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Clock className="h-3 w-3 mr-1 text-muted-foreground" />
                      {entry.clockIn || '--:--'}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Clock className="h-3 w-3 mr-1 text-muted-foreground" />
                      {entry.clockOut || '--:--'}
                    </div>
                  </TableCell>
                  <TableCell>{entry.breakTime} min</TableCell>
                  <TableCell>{entry.totalHours.toFixed(1)}h</TableCell>
                  <TableCell className="text-right">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditEntry(entry)}
                    >
                      Edit
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-red-500 hover:text-red-700"
                      onClick={() => handleDeleteEntry(entry.id)}
                    >
                      Delete
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>

        {/* Edit Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Edit Time Entry</DialogTitle>
              <DialogDescription>
                Adjust clock in/out times and break duration
              </DialogDescription>
            </DialogHeader>

            {currentEntry && (
              <div className="grid gap-4 py-4">
                <div>
                  <Label htmlFor="staff-name" className="text-muted-foreground text-sm">Staff Member</Label>
                  <div id="staff-name" className="font-medium">{getStaffName(currentEntry.staffId)}</div>
                </div>

                <div>
                  <Label htmlFor="entry-date" className="text-muted-foreground text-sm">Date</Label>
                  <div id="entry-date" className="font-medium">{formatDate(currentEntry.date)}</div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="clock-in">Clock In Time</Label>
                    <Input
                      id="clock-in"
                      type="time"
                      value={editedEntry.clockIn || ''}
                      onChange={(e) => handleTimeChange('clockIn', e.target.value)}
                    />
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="clock-out">Clock Out Time</Label>
                    <Input
                      id="clock-out"
                      type="time"
                      value={editedEntry.clockOut || ''}
                      onChange={(e) => handleTimeChange('clockOut', e.target.value)}
                    />
                  </div>
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="break-time">Break Duration (minutes)</Label>
                  <Input
                    id="break-time"
                    type="number"
                    min="0"
                    value={editedEntry.breakTime?.toString() || '0'}
                    onChange={(e) => handleBreakTimeChange(e.target.value)}
                  />
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="total-hours" className="text-muted-foreground text-sm">Total Hours</Label>
                  <div id="total-hours" className="font-medium">
                    {editedEntry.totalHours !== undefined
                      ? editedEntry.totalHours.toFixed(1)
                      : currentEntry.totalHours.toFixed(1)}h
                  </div>
                </div>
              </div>
            )}

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                <X className="h-4 w-4 mr-2" /> Cancel
              </Button>
              <Button onClick={handleSaveChanges}>
                <Save className="h-4 w-4 mr-2" /> Save Changes
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
};

export default TimeEntryEditor;
