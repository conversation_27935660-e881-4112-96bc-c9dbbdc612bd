import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";

interface StaffMetrics {
  sales: number;
  tablesTurned: number;
  customerRating: number;
}

interface StaffPerformanceDetailsProps {
  name: string;
  performance: number;
  metrics?: StaffMetrics;
}

const StaffPerformanceDetails = ({ name, performance, metrics }: StaffPerformanceDetailsProps) => {
  // Determine color based on performance
  const getColor = () => {
    if (performance >= 85) return "bg-green-500";
    if (performance >= 70) return "bg-yellow-500";
    return "bg-red-500";
  };

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg font-medium">Performance Details</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <div className="flex justify-between mb-1">
            <span className="text-sm font-medium">Overall Performance</span>
            <span className="text-sm font-medium">{performance}%</span>
          </div>
          <Progress value={performance} className="h-2" indicatorClassName={getColor()} />
        </div>

        {metrics && (
          <div className="grid grid-cols-3 gap-4 pt-2">
            <div className="bg-muted p-3 rounded-lg text-center">
              <p className="text-sm text-muted-foreground">Sales</p>
              <p className="text-lg font-semibold">${metrics.sales}</p>
            </div>
            <div className="bg-muted p-3 rounded-lg text-center">
              <p className="text-sm text-muted-foreground">Tables</p>
              <p className="text-lg font-semibold">{metrics.tablesTurned}</p>
            </div>
            <div className="bg-muted p-3 rounded-lg text-center">
              <p className="text-sm text-muted-foreground">Rating</p>
              <p className="text-lg font-semibold">{metrics.customerRating}/5</p>
            </div>
          </div>
        )}

        <div className="pt-2">
          <h4 className="text-sm font-medium mb-2">Performance Breakdown</h4>
          <div className="space-y-3">
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-xs">Customer Service</span>
                <span className="text-xs">{Math.min(100, performance + 5)}%</span>
              </div>
              <Progress 
                value={Math.min(100, performance + 5)} 
                className="h-1.5" 
                indicatorClassName={performance + 5 >= 85 ? "bg-green-500" : performance + 5 >= 70 ? "bg-yellow-500" : "bg-red-500"} 
              />
            </div>
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-xs">Speed</span>
                <span className="text-xs">{Math.max(0, performance - 10)}%</span>
              </div>
              <Progress 
                value={Math.max(0, performance - 10)} 
                className="h-1.5" 
                indicatorClassName={performance - 10 >= 85 ? "bg-green-500" : performance - 10 >= 70 ? "bg-yellow-500" : "bg-red-500"} 
              />
            </div>
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-xs">Accuracy</span>
                <span className="text-xs">{Math.min(100, performance + 2)}%</span>
              </div>
              <Progress 
                value={Math.min(100, performance + 2)} 
                className="h-1.5" 
                indicatorClassName={performance + 2 >= 85 ? "bg-green-500" : performance + 2 >= 70 ? "bg-yellow-500" : "bg-red-500"} 
              />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default StaffPerformanceDetails;
