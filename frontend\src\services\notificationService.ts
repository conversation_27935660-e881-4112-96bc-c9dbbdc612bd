import { Notification, NotificationType, NotificationPriority } from "@/types/notification";
import { apiRequest } from "./apiService";
import logger from "@/utils/logger";

// Storage key for notifications (fallback for offline mode)
const NOTIFICATIONS_KEY = 'notifications';

// Get all notifications from localStorage
export const getAllNotifications = (): Notification[] => {
  const notificationsJson = localStorage.getItem(NOTIFICATIONS_KEY);
  return notificationsJson ? JSON.parse(notificationsJson) : [];
};

// Add a new notification
export const addNotification = (notification: Omit<Notification, 'id' | 'timestamp' | 'isRead'>): Notification => {
  const allNotifications = getAllNotifications();
  
  // Create a new notification with generated ID and timestamp
  const newNotification: Notification = {
    ...notification,
    id: Date.now().toString(),
    timestamp: new Date().toISOString(),
    isRead: false
  };
  
  // Add to the beginning of the array (newest first)
  allNotifications.unshift(newNotification);
  
  // Save to localStorage
  localStorage.setItem(NOTIFICATIONS_KEY, JSON.stringify(allNotifications));
  
  return newNotification;
};

// Mark a notification as read
export const markNotificationAsRead = (notificationId: string): void => {
  const allNotifications = getAllNotifications();
  const index = allNotifications.findIndex(n => n.id === notificationId);
  
  if (index !== -1) {
    allNotifications[index].isRead = true;
    localStorage.setItem(NOTIFICATIONS_KEY, JSON.stringify(allNotifications));
  }
};

// Mark all notifications as read
export const markAllNotificationsAsRead = (): void => {
  const allNotifications = getAllNotifications();
  
  const updatedNotifications = allNotifications.map(notification => ({
    ...notification,
    isRead: true
  }));
  
  localStorage.setItem(NOTIFICATIONS_KEY, JSON.stringify(updatedNotifications));
};

// Delete a notification
export const deleteNotification = (notificationId: string): void => {
  const allNotifications = getAllNotifications();
  const filteredNotifications = allNotifications.filter(n => n.id !== notificationId);
  localStorage.setItem(NOTIFICATIONS_KEY, JSON.stringify(filteredNotifications));
};

// Delete all notifications
export const deleteAllNotifications = (): void => {
  localStorage.setItem(NOTIFICATIONS_KEY, JSON.stringify([]));
};

// Get unread notification count
export const getUnreadNotificationCount = (): number => {
  const allNotifications = getAllNotifications();
  return allNotifications.filter(n => !n.isRead).length;
};

// API-based notification functions
export const notificationApi = {
  // Get all notifications from API
  getNotifications: async (restaurantId: string, filters?: {
    type?: NotificationType;
    priority?: NotificationPriority;
    is_read?: boolean;
    limit?: number;
    offset?: number;
  }): Promise<Notification[]> => {
    try {
      logger.debug('Fetching notifications from API', 'NotificationService', { restaurantId, filters });

      const params = new URLSearchParams();
      if (filters?.type) params.append('type', filters.type);
      if (filters?.priority) params.append('priority', filters.priority);
      if (filters?.is_read !== undefined) params.append('is_read', filters.is_read.toString());
      if (filters?.limit) params.append('limit', filters.limit.toString());
      if (filters?.offset) params.append('offset', filters.offset.toString());
      params.append('restaurant_id', restaurantId);

      const queryString = params.toString();
      const url = `/notifications${queryString ? `?${queryString}` : ''}`;

      const response = await apiRequest(url);

      // Transform API response to match frontend interface
      const notifications = response.map((notification: any) => ({
        id: notification.id,
        title: notification.title,
        message: notification.message,
        type: notification.type,
        priority: notification.priority,
        isRead: notification.is_read,
        timestamp: notification.created_at,
        link: notification.link,
        relatedId: notification.related_id,
      }));

      logger.debug(`Retrieved ${notifications.length} notifications from API`, 'NotificationService');
      return notifications;

    } catch (error) {
      logger.error('Failed to fetch notifications from API', 'NotificationService', {
        error: error.message,
        errorName: error.name,
        restaurantId,
        endpoint: '/notifications',
        filters,
        fallbackAction: 'Using localStorage data'
      });
      // Fallback to localStorage
      return getAllNotifications();
    }
  },

  // Create notification via API
  createNotification: async (
    restaurantId: string,
    notification: Omit<Notification, 'id' | 'timestamp' | 'isRead'>
  ): Promise<Notification> => {
    try {
      logger.debug('Creating notification via API', 'NotificationService', { restaurantId, notification });

      const response = await apiRequest('/notifications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Restaurant-ID': restaurantId,
        },
        body: JSON.stringify({
          title: notification.title,
          message: notification.message,
          type: notification.type,
          priority: notification.priority,
          link: notification.link,
          related_id: notification.relatedId,
        }),
      });

      // Transform API response to match frontend interface
      const createdNotification = {
        id: response.id,
        title: response.title,
        message: response.message,
        type: response.type,
        priority: response.priority,
        isRead: response.is_read,
        timestamp: response.created_at,
        link: response.link,
        relatedId: response.related_id,
      };

      logger.info('Notification created successfully via API', 'NotificationService', { id: createdNotification.id });
      return createdNotification;

    } catch (error) {
      logger.error('Failed to create notification via API', 'NotificationService', { error, restaurantId });
      // Fallback to localStorage
      return addNotification(notification);
    }
  },

  // Mark notifications as read via API
  markAsRead: async (restaurantId: string, notificationIds: string[]): Promise<number> => {
    try {
      logger.debug('Marking notifications as read via API', 'NotificationService', { restaurantId, notificationIds });

      const response = await apiRequest('/notifications/mark-read', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'X-Restaurant-ID': restaurantId,
        },
        body: JSON.stringify({
          notification_ids: notificationIds,
        }),
      });

      logger.info(`Marked ${response.updated_count} notifications as read via API`, 'NotificationService');
      return response.updated_count;

    } catch (error) {
      logger.error('Failed to mark notifications as read via API', 'NotificationService', { error, restaurantId });
      // Fallback to localStorage
      notificationIds.forEach(id => markNotificationAsRead(id));
      return notificationIds.length;
    }
  },

  // Mark all notifications as read via API
  markAllAsRead: async (restaurantId: string): Promise<number> => {
    try {
      logger.debug('Marking all notifications as read via API', 'NotificationService', { restaurantId });

      const response = await apiRequest('/notifications/mark-all-read', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'X-Restaurant-ID': restaurantId,
        },
      });

      logger.info(`Marked all ${response.updated_count} notifications as read via API`, 'NotificationService');
      return response.updated_count;

    } catch (error) {
      logger.error('Failed to mark all notifications as read via API', 'NotificationService', { error, restaurantId });
      // Fallback to localStorage
      markAllNotificationsAsRead();
      return 0;
    }
  },

  // Delete notification via API
  deleteNotification: async (restaurantId: string, notificationId: string): Promise<boolean> => {
    try {
      logger.debug('Deleting notification via API', 'NotificationService', { restaurantId, notificationId });

      await apiRequest(`/notifications/${notificationId}`, {
        method: 'DELETE',
        headers: {
          'X-Restaurant-ID': restaurantId,
        },
      });

      logger.info('Notification deleted successfully via API', 'NotificationService', { notificationId });
      return true;

    } catch (error) {
      logger.error('Failed to delete notification via API', 'NotificationService', { error, restaurantId, notificationId });
      // Fallback to localStorage
      deleteNotification(notificationId);
      return false;
    }
  },

  // Delete all notifications via API
  deleteAllNotifications: async (restaurantId: string): Promise<number> => {
    try {
      logger.debug('Deleting all notifications via API', 'NotificationService', { restaurantId });

      const response = await apiRequest('/notifications', {
        method: 'DELETE',
        headers: {
          'X-Restaurant-ID': restaurantId,
        },
      });

      logger.info('All notifications deleted successfully via API', 'NotificationService');
      return response.deleted_count || 0;

    } catch (error) {
      logger.error('Failed to delete all notifications via API', 'NotificationService', { error, restaurantId });
      // Fallback to localStorage
      deleteAllNotifications();
      return 0;
    }
  },

  // Get notification statistics via API
  getNotificationStats: async (restaurantId: string): Promise<{
    total_count: number;
    unread_count: number;
    by_type: Record<string, number>;
    by_priority: Record<string, number>;
  }> => {
    try {
      logger.debug('Fetching notification stats via API', 'NotificationService', { restaurantId });

      const response = await apiRequest(`/notifications/stats?restaurant_id=${restaurantId}`);

      logger.debug('Retrieved notification stats from API', 'NotificationService', response);
      return response;

    } catch (error) {
      logger.error('Failed to fetch notification stats via API', 'NotificationService', {
        error: error.message,
        errorName: error.name,
        restaurantId,
        endpoint: '/notifications/stats',
        fallbackAction: 'Calculating from localStorage'
      });
      // Fallback to localStorage calculation
      const notifications = getAllNotifications();
      const unreadCount = notifications.filter(n => !n.isRead).length;

      return {
        total_count: notifications.length,
        unread_count: unreadCount,
        by_type: {},
        by_priority: {},
      };
    }
  },
};

// Initialize mock notifications for testing
export const initializeMockNotifications = (): void => {
  // Only initialize if no notifications exist yet
  if (getAllNotifications().length === 0) {
    const mockNotifications: Omit<Notification, 'id' | 'timestamp' | 'isRead'>[] = [
      {
        title: 'Low inventory alert',
        message: 'Tomatoes are running low. Current stock: 5 units',
        type: 'inventory',
        priority: 'high',
        relatedId: 'inv-123'
      },
      {
        title: 'New reservation',
        message: 'New reservation for Smith party of 4 at 7:00 PM',
        type: 'reservation',
        priority: 'medium',
        link: '/reservations',
        relatedId: 'res-456'
      },
      {
        title: 'Staff schedule updated',
        message: 'The staff schedule for next week has been updated',
        type: 'staff',
        priority: 'low',
        link: '/staff'
      },
      {
        title: 'System update',
        message: 'The system will be updated tonight at 2:00 AM',
        type: 'info',
        priority: 'medium'
      }
    ];

    // Add each mock notification
    mockNotifications.forEach(notification => {
      addNotification(notification);
    });

    console.log('Initialized mock notifications');
  }
};
