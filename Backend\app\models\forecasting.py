from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime, date
from enum import Enum

class ForecastType(str, Enum):
    SALES = "sales"
    CUSTOMER_TRAFFIC = "customer_traffic"
    INVENTORY_DEMAND = "inventory_demand"
    STAFF_SCHEDULING = "staff_scheduling"
    SEASONAL_TRENDS = "seasonal_trends"

class ForecastPeriod(str, Enum):
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"

class TrendDirection(str, Enum):
    INCREASING = "increasing"
    DECREASING = "decreasing"
    STABLE = "stable"

class ConfidenceLevel(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"

class ForecastDataPoint(BaseModel):
    date: date
    value: float
    confidence_interval_lower: float
    confidence_interval_upper: float
    confidence_level: ConfidenceLevel

class TrendAnalysis(BaseModel):
    direction: TrendDirection
    strength: float  # 0-1 scale
    description: str
    change_percentage: float

class SeasonalPattern(BaseModel):
    pattern_type: str  # "weekly", "monthly", "holiday", "event"
    peak_periods: List[str]
    low_periods: List[str]
    impact_factor: float  # multiplier effect

class ForecastMetrics(BaseModel):
    accuracy: float  # 0-1 scale
    mean_absolute_error: float
    mean_squared_error: float
    r_squared: float
    model_type: str

class SalesForecast(BaseModel):
    forecast_type: ForecastType = ForecastType.SALES
    period: ForecastPeriod
    predictions: List[ForecastDataPoint]
    trend_analysis: TrendAnalysis
    seasonal_patterns: List[SeasonalPattern]
    metrics: ForecastMetrics
    historical_comparison: Dict[str, float]
    generated_at: datetime = Field(default_factory=datetime.now)

class CustomerTrafficForecast(BaseModel):
    forecast_type: ForecastType = ForecastType.CUSTOMER_TRAFFIC
    period: ForecastPeriod
    predictions: List[ForecastDataPoint]
    peak_hours: List[str]
    low_hours: List[str]
    average_daily_customers: float
    trend_analysis: TrendAnalysis
    metrics: ForecastMetrics
    generated_at: datetime = Field(default_factory=datetime.now)

class InventoryDemandItem(BaseModel):
    item_id: str
    item_name: str
    predicted_usage: float
    unit: str
    reorder_recommendation: bool
    recommended_quantity: float
    confidence_level: ConfidenceLevel

class InventoryDemandForecast(BaseModel):
    forecast_type: ForecastType = ForecastType.INVENTORY_DEMAND
    period: ForecastPeriod
    items: List[InventoryDemandItem]
    total_cost_prediction: float
    critical_items: List[str]
    trend_analysis: TrendAnalysis
    metrics: ForecastMetrics
    generated_at: datetime = Field(default_factory=datetime.now)

class StaffScheduleRecommendation(BaseModel):
    date: date
    hour: int
    recommended_staff_count: int
    position_breakdown: Dict[str, int]  # {"front_of_house": 3, "back_of_house": 2}
    confidence_level: ConfidenceLevel

class StaffSchedulingForecast(BaseModel):
    forecast_type: ForecastType = ForecastType.STAFF_SCHEDULING
    period: ForecastPeriod
    recommendations: List[StaffScheduleRecommendation]
    peak_hours: List[str]
    optimal_staff_levels: Dict[str, int]
    cost_optimization: Dict[str, float]
    trend_analysis: TrendAnalysis
    metrics: ForecastMetrics
    generated_at: datetime = Field(default_factory=datetime.now)

class SeasonalEvent(BaseModel):
    event_name: str
    date_range: Dict[str, str]  # {"start": "2024-12-20", "end": "2024-12-25"}
    impact_multiplier: float
    affected_categories: List[str]

class SeasonalTrendsForecast(BaseModel):
    forecast_type: ForecastType = ForecastType.SEASONAL_TRENDS
    period: ForecastPeriod
    upcoming_events: List[SeasonalEvent]
    seasonal_patterns: List[SeasonalPattern]
    revenue_impact: Dict[str, float]
    menu_recommendations: List[str]
    marketing_opportunities: List[str]
    trend_analysis: TrendAnalysis
    generated_at: datetime = Field(default_factory=datetime.now)

class ComprehensiveForecast(BaseModel):
    sales_forecast: SalesForecast
    customer_traffic_forecast: CustomerTrafficForecast
    inventory_demand_forecast: InventoryDemandForecast
    staff_scheduling_forecast: StaffSchedulingForecast
    seasonal_trends_forecast: SeasonalTrendsForecast
    summary: Dict[str, Any]
    recommendations: List[str]
    generated_at: datetime = Field(default_factory=datetime.now)

class ForecastRequest(BaseModel):
    forecast_types: List[ForecastType]
    period: ForecastPeriod
    days_ahead: int = 30
    include_confidence_intervals: bool = True
    include_seasonal_analysis: bool = True
