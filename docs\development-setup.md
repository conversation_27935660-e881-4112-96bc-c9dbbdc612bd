# Development Setup Guide

## 🚀 Quick Start

### Prerequisites
- **Node.js**: 18.0.0 or higher
- **Python**: 3.11 or higher
- **Git**: Latest version
- **VS Code**: Recommended IDE

### 1. Clone Repository
```bash
git clone https://github.com/prathamsurti/RestroManage-V1.git
cd RestroManage-V1
```

### 2. Backend Setup
```bash
# Navigate to backend directory
cd Backend

# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Set environment variables (optional)
export PORT=5001  # Use 5001 to match frontend proxy
export ENVIRONMENT=development

# Start backend server
python main.py
```

**Backend should start on**: `http://localhost:5001` (or 5002 if PORT not set)

### 3. Frontend Setup
```bash
# Open new terminal, navigate to frontend
cd frontend

# Install dependencies
npm install

# Start development server
npm run dev
```

**Frontend should start on**: `http://localhost:5175`

### 4. Verify Setup
```bash
# Test backend directly
curl http://localhost:5001/health

# Test through frontend proxy
curl http://localhost:5175/health

# Test API endpoint
curl http://localhost:5175/api/restaurants
```

## 🔧 Configuration Files

### Backend Configuration (`Backend/.env`)
Create this file for custom settings:
```env
# Server Configuration
PORT=5001
HOST=0.0.0.0
RELOAD=true
ENVIRONMENT=development

# Database
DATABASE_URL=sqlite+aiosqlite:///./restro_manage.db

# Security
SECRET_KEY=your-development-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Origins
ALLOWED_ORIGINS=http://localhost:5175,http://localhost:5173,http://localhost:3000

# Logging
LOG_LEVEL=DEBUG
ENABLE_SQL_LOGGING=true

# AI Services (optional)
GOOGLE_AI_API_KEY=your-google-ai-key
OPENAI_API_KEY=your-openai-key
```

### Frontend Configuration (`frontend/.env`)
```env
# API Configuration
VITE_API_URL=http://localhost:5001

# Environment
VITE_ENVIRONMENT=development
```

## 📁 Project Structure Deep Dive

### Backend Structure
```
Backend/
├── app/
│   ├── api.py              # Main FastAPI application
│   ├── config.py           # Configuration settings
│   ├── routers/            # API route handlers
│   │   ├── auth.py         # Authentication endpoints
│   │   ├── restaurants.py  # Restaurant management
│   │   ├── notifications.py # Notification system
│   │   ├── menu.py         # Menu management
│   │   ├── orders.py       # Order processing
│   │   └── health.py       # Health check endpoints
│   ├── models/             # Pydantic data models
│   ├── controllers/        # Business logic (MVC pattern)
│   ├── utils/              # Utility functions
│   │   ├── auth.py         # Authentication utilities
│   │   ├── storage.py      # Data storage utilities
│   │   └── logging_config.py # Logging configuration
│   └── database/           # Database configuration
├── main.py                 # Application entry point
├── requirements.txt        # Python dependencies
└── alembic/               # Database migrations
```

### Frontend Structure
```
frontend/
├── src/
│   ├── components/         # Reusable UI components
│   │   ├── ui/            # shadcn/ui components
│   │   ├── layout/        # Layout components
│   │   └── forms/         # Form components
│   ├── pages/             # Page components
│   │   ├── auth/          # Authentication pages
│   │   ├── dashboard/     # Dashboard pages
│   │   └── management/    # Management pages
│   ├── hooks/             # Custom React hooks
│   │   ├── useApi.ts      # API interaction hooks
│   │   └── useAuth.ts     # Authentication hooks
│   ├── utils/             # Utility functions
│   ├── types/             # TypeScript type definitions
│   └── stores/            # Zustand state stores
├── public/                # Static assets
├── vite.config.ts         # Vite configuration
├── tailwind.config.ts     # Tailwind CSS configuration
└── package.json           # Frontend dependencies
```

## 🔧 Development Tools

### VS Code Extensions (Recommended)
```json
{
  "recommendations": [
    "ms-python.python",
    "ms-python.black-formatter",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-typescript-next",
    "ms-vscode.vscode-json"
  ]
}
```

### VS Code Settings (`.vscode/settings.json`)
```json
{
  "python.defaultInterpreterPath": "./Backend/venv/bin/python",
  "python.formatting.provider": "black",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.organizeImports": true
  },
  "typescript.preferences.importModuleSpecifier": "relative"
}
```

## 🐛 Debugging Setup

### Backend Debugging
```python
# In main.py, add debug configuration
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.api:app",
        host="0.0.0.0",
        port=5001,
        reload=True,
        debug=True,  # Enable debug mode
        log_level="debug"
    )
```

### Frontend Debugging
```typescript
// In vite.config.ts
export default defineConfig({
  server: {
    port: 5175,
    proxy: {
      "/api": {
        target: "http://localhost:5001",
        changeOrigin: true,
        secure: false,
        configure: (proxy, options) => {
          proxy.on('error', (err, req, res) => {
            console.log('proxy error', err);
          });
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('Sending Request:', req.method, req.url);
          });
        }
      }
    }
  }
});
```

## 🧪 Testing Setup

### Backend Testing
```bash
# Install test dependencies
pip install pytest pytest-asyncio httpx

# Run tests
pytest Backend/tests/

# Run with coverage
pytest --cov=app Backend/tests/
```

### Frontend Testing
```bash
# Install test dependencies
npm install --save-dev @testing-library/react @testing-library/jest-dom vitest

# Run tests
npm run test

# Run with coverage
npm run test:coverage
```

## 🔍 Common Development Issues

### Port Conflicts
```bash
# Check what's running on ports
netstat -an | findstr :5001
netstat -an | findstr :5175

# Kill processes if needed
# Windows:
taskkill /F /PID <process-id>
# Linux/Mac:
kill -9 <process-id>
```

### Database Issues
```bash
# Reset database
rm Backend/restro_manage.db

# Run migrations
cd Backend
python -c "from app.database import init_database; init_database()"
```

### Dependency Issues
```bash
# Backend: Clear and reinstall
pip freeze > temp.txt
pip uninstall -r temp.txt -y
pip install -r requirements.txt

# Frontend: Clear and reinstall
rm -rf node_modules package-lock.json
npm install
```

## 🚀 Production Build

### Backend Production
```bash
# Install production dependencies
pip install gunicorn

# Run with gunicorn
gunicorn app.api:app --worker-class uvicorn.workers.UvicornWorker --workers 4 --bind 0.0.0.0:8000
```

### Frontend Production
```bash
# Build for production
npm run build

# Preview production build
npm run preview
```

## 📋 Development Checklist

- [ ] **Environment Setup**: Python 3.11+, Node.js 18+
- [ ] **Backend Running**: `http://localhost:5001/health` responds
- [ ] **Frontend Running**: `http://localhost:5175` loads
- [ ] **Proxy Working**: `http://localhost:5175/health` responds
- [ ] **Database Connected**: No database errors in backend logs
- [ ] **API Endpoints**: `/api/restaurants` returns data
- [ ] **Authentication**: Login/logout functionality works
- [ ] **Hot Reload**: Changes reflect immediately

---

**Next Steps**:
- Review [Proxy Configuration](./proxy-configuration.md) for connection details
- Check [Troubleshooting Guide](./troubleshooting-guide.md) for common issues
- See [API Documentation](./api-documentation.md) for endpoint usage
