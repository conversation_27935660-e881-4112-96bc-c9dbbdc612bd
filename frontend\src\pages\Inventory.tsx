import InventoryForecastDashboard from "@/components/inventory/InventoryForecastDashboard";
import { uploadFile, validateFileType } from "@/utils/fileUpload";

import { useState } from "react";
import Layout from "@/components/layout/Layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "@/components/ui/sonner";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Search,
  Plus,
  Filter,
  ArrowUpDown,
  AlertTriangle,
  Save,
  X,
  FileUp,
  FileText,
  Check,
  Loader2,
  Printer,
  Calendar,
  BarChart
} from "lucide-react";
import ForecastedInventory from "@/components/inventory/ForecastedInventory";
import { ForecastData } from "@/components/dashboard/ForecastCard";
import { AllergenSelector, AllergenBadgeList } from "@/components/allergens";

interface InventoryItem {
  id: string;
  name: string;
  category: string;
  stock: number;
  unit: string;
  reorderLevel: number;
  cost: number;
  expiryDate?: string;
  allergens?: string[];
  isLow: boolean;
  isExpiring: boolean;
}

interface WasteItem {
  id: string;
  inventoryItemId: string;
  date: string;
  quantity: number;
  reason: "expired" | "damaged" | "spoiled" | "other";
  notes?: string;
  cost: number;
}

// Mock inventory data
const inventoryData: InventoryItem[] = [
    {
      id: "1",
      name: "Tomatoes",
      category: "Vegetables",
      stock: 3,
      unit: "kg",
      reorderLevel: 10,
      cost: 3.99,
      allergens: [],
      isLow: true,
      isExpiring: false
    },
    {
      id: "2",
      name: "Chicken Breast",
      category: "Meat",
      stock: 7,
      unit: "kg",
      reorderLevel: 8,
      cost: 12.50,
      expiryDate: "May 2, 2025",
      allergens: [],
      isLow: true,
      isExpiring: true
    },
    {
      id: "3",
      name: "Olive Oil",
      category: "Grocery",
      stock: 2,
      unit: "liters",
      reorderLevel: 4,
      cost: 18.75,
      allergens: [],
      isLow: true,
      isExpiring: false
    },
    {
      id: "4",
      name: "Rice",
      category: "Grocery",
      stock: 25,
      unit: "kg",
      reorderLevel: 10,
      cost: 2.50,
      allergens: [],
      isLow: false,
      isExpiring: false
    },
    {
      id: "5",
      name: "Onions",
      category: "Vegetables",
      stock: 15,
      unit: "kg",
      reorderLevel: 8,
      cost: 1.99,
      allergens: [],
      isLow: false,
      isExpiring: false
    },
    {
      id: "6",
      name: "Salmon Fillet",
      category: "Seafood",
      stock: 5,
      unit: "kg",
      reorderLevel: 4,
      cost: 22.99,
      expiryDate: "May 5, 2025",
      allergens: ["fish"],
      isLow: false,
      isExpiring: true
    },
    {
      id: "7",
      name: "Heavy Cream",
      category: "Dairy",
      stock: 6,
      unit: "liters",
      reorderLevel: 5,
      cost: 5.75,
      expiryDate: "May 8, 2025",
      allergens: ["dairy"],
      isLow: false,
      isExpiring: true
    },
    {
      id: "8",
      name: "Flour",
      category: "Bakery",
      stock: 20,
      unit: "kg",
      reorderLevel: 5,
      cost: 2.25,
      allergens: ["gluten"],
      isLow: false,
      isExpiring: false
    },
    {
      id: "9",
      name: "Eggs",
      category: "Dairy",
      stock: 50,
      unit: "units",
      reorderLevel: 20,
      cost: 0.30,
      expiryDate: "May 10, 2025",
      allergens: ["eggs"],
      isLow: false,
      isExpiring: false
    },
    {
      id: "10",
      name: "Almonds",
      category: "Nuts",
      stock: 3,
      unit: "kg",
      reorderLevel: 2,
      cost: 12.00,
      allergens: ["nuts"],
      isLow: false,
      isExpiring: false
    },
    {
      id: "11",
      name: "Soy Sauce",
      category: "Condiments",
      stock: 8,
      unit: "liters",
      reorderLevel: 3,
      cost: 4.50,
      allergens: ["soy", "gluten"],
      isLow: false,
      isExpiring: false
    },
    {
      id: "12",
      name: "Shrimp",
      category: "Seafood",
      stock: 4,
      unit: "kg",
      reorderLevel: 2,
      cost: 18.99,
      expiryDate: "May 3, 2025",
      allergens: ["shellfish"],
      isLow: false,
      isExpiring: true
    }
  ];

// Mock waste data
const wasteData: WasteItem[] = [
  {
    id: "1",
    inventoryItemId: "2", // Chicken Breast
    date: "May 1, 2025",
    quantity: 0.5,
    reason: "expired",
    notes: "Past expiration date",
    cost: 6.25 // Half of the original cost
  },
  {
    id: "2",
    inventoryItemId: "6", // Salmon Fillet
    date: "May 2, 2025",
    quantity: 0.3,
    reason: "spoiled",
    notes: "Not stored properly",
    cost: 6.90 // 30% of the original cost
  },
  {
    id: "3",
    inventoryItemId: "7", // Heavy Cream
    date: "May 3, 2025",
    quantity: 1,
    reason: "expired",
    notes: "Past expiration date",
    cost: 5.75
  }
];

// Mock forecast data for inventory planning
const mockForecastData: ForecastData[] = [
  {
    day: "Monday",
    actualRevenue: 0,
    projectedRevenue: 1150,
    customers: 47,
    confidence: 92,
    staffNeeded: 5,
    tableOccupancy: 65,
    peakHours: [
      { hour: 12, customers: 12 },
      { hour: 13, customers: 15 },
      { hour: 19, customers: 20 }
    ]
  },
  {
    day: "Tuesday",
    actualRevenue: 0,
    projectedRevenue: 1380,
    customers: 54,
    confidence: 90,
    staffNeeded: 6,
    tableOccupancy: 70,
    peakHours: [
      { hour: 12, customers: 14 },
      { hour: 13, customers: 16 },
      { hour: 19, customers: 24 }
    ]
  },
  {
    day: "Wednesday",
    actualRevenue: 0,
    projectedRevenue: 1520,
    customers: 63,
    confidence: 88,
    staffNeeded: 6,
    tableOccupancy: 75,
    peakHours: [
      { hour: 12, customers: 16 },
      { hour: 13, customers: 18 },
      { hour: 19, customers: 29 }
    ]
  },
  {
    day: "Thursday",
    actualRevenue: 0,
    projectedRevenue: 1750,
    customers: 72,
    confidence: 85,
    staffNeeded: 7,
    tableOccupancy: 80,
    peakHours: [
      { hour: 12, customers: 18 },
      { hour: 13, customers: 20 },
      { hour: 19, customers: 34 }
    ]
  },
  {
    day: "Friday",
    actualRevenue: 0,
    projectedRevenue: 2200,
    customers: 90,
    confidence: 82,
    staffNeeded: 8,
    tableOccupancy: 90,
    peakHours: [
      { hour: 12, customers: 22 },
      { hour: 13, customers: 25 },
      { hour: 19, customers: 43 }
    ]
  },
  {
    day: "Saturday",
    actualRevenue: 0,
    projectedRevenue: 2600,
    customers: 105,
    confidence: 80,
    staffNeeded: 9,
    tableOccupancy: 95,
    peakHours: [
      { hour: 12, customers: 26 },
      { hour: 13, customers: 28 },
      { hour: 19, customers: 51 }
    ]
  },
  {
    day: "Sunday",
    actualRevenue: 0,
    projectedRevenue: 2000,
    customers: 80,
    confidence: 84,
    staffNeeded: 7,
    tableOccupancy: 85,
    peakHours: [
      { hour: 12, customers: 20 },
      { hour: 13, customers: 22 },
      { hour: 19, customers: 38 }
    ]
  }
];

const Inventory = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [isAddItemOpen, setIsAddItemOpen] = useState(false);
  const [isAddWasteOpen, setIsAddWasteOpen] = useState(false);
  const [isUpdateStockOpen, setIsUpdateStockOpen] = useState(false);
  const [isImportOpen, setIsImportOpen] = useState(false);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [filters, setFilters] = useState({
    categories: [] as string[],
    stockStatus: [] as string[],
    minStock: "",
    maxStock: "",
    minCost: "",
    maxCost: ""
  });
  const [sorting, setSorting] = useState({
    column: "name",
    direction: "asc" as "asc" | "desc"
  });
  const [inventory, setInventory] = useState<InventoryItem[]>(inventoryData);
  const [wasteItems, setWasteItems] = useState<WasteItem[]>(wasteData);
  const [currentItem, setCurrentItem] = useState<InventoryItem | null>(null);
  const [stockUpdate, setStockUpdate] = useState({
    quantity: 0,
    reason: "delivery",
    invoiceNumber: "",
    date: new Date().toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' }),
    notes: ""
  });
  const [newItem, setNewItem] = useState<Omit<InventoryItem, "id" | "isLow" | "isExpiring">>({
    name: "",
    category: "",
    stock: 0,
    unit: "kg",
    reorderLevel: 0,
    cost: 0,
    expiryDate: undefined,
    allergens: []
  });
  const [newWaste, setNewWaste] = useState<Omit<WasteItem, "id" | "cost">>({
    inventoryItemId: "",
    date: new Date().toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' }),
    quantity: 0,
    reason: "expired",
    notes: ""
  });
  const [importData, setImportData] = useState({
    text: "",
    processing: false,
    results: null as any
  });
  const [isPrintLabelOpen, setIsPrintLabelOpen] = useState(false);
  const [labelData, setLabelData] = useState({
    quantity: 1,
    unit: "",
    expiryType: "standard", // standard, defrosted, opened
    customDays: 0,
    notes: "",
    printDate: true,
    printTime: false
  });

  // Categories
  const categories = [
    "Vegetables",
    "Fruits",
    "Meat",
    "Seafood",
    "Dairy",
    "Bakery",
    "Grocery",
    "Beverages",
    "Spices",
    "Other"
  ];

  // Units
  const units = [
    "kg",
    "g",
    "liters",
    "ml",
    "units",
    "boxes",
    "bottles",
    "cans",
    "packages"
  ];

  // Add new inventory item
  const handleAddItem = () => {
    if (!newItem.name || !newItem.category) {
      toast.error("Please fill in all required fields");
      return;
    }

    const id = (inventory.length + 1).toString();
    const isLow = newItem.stock <= newItem.reorderLevel;
    const isExpiring = !!newItem.expiryDate; // Simple check, in a real app would check date proximity

    const itemToAdd: InventoryItem = {
      ...newItem,
      id,
      isLow,
      isExpiring
    };

    setInventory([...inventory, itemToAdd]);
    setIsAddItemOpen(false);
    setNewItem({
      name: "",
      category: "",
      stock: 0,
      unit: "kg",
      reorderLevel: 0,
      cost: 0,
      expiryDate: undefined,
      allergens: []
    });
    toast.success("Inventory item added successfully");
  };

  // Add new waste item
  const handleAddWaste = () => {
    if (!newWaste.inventoryItemId || newWaste.quantity <= 0) {
      toast.error("Please fill in all required fields");
      return;
    }

    const id = (wasteItems.length + 1).toString();
    const inventoryItem = inventory.find(item => item.id === newWaste.inventoryItemId);

    if (!inventoryItem) {
      toast.error("Selected inventory item not found");
      return;
    }

    // Calculate cost based on the quantity and the item's cost
    const cost = inventoryItem.cost * newWaste.quantity;

    const wasteToAdd: WasteItem = {
      ...newWaste,
      id,
      cost
    };

    // Update waste items
    setWasteItems([...wasteItems, wasteToAdd]);

    // Update inventory by reducing the stock
    const updatedInventory = inventory.map(item =>
      item.id === newWaste.inventoryItemId
        ? { ...item, stock: Math.max(0, item.stock - newWaste.quantity) }
        : item
    );
    setInventory(updatedInventory);

    setIsAddWasteOpen(false);
    setNewWaste({
      inventoryItemId: "",
      date: new Date().toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' }),
      quantity: 0,
      reason: "expired",
      notes: ""
    });
    toast.success("Waste item recorded successfully");
  };

  // Update stock for existing item
  const handleUpdateStock = () => {
    if (!currentItem || stockUpdate.quantity <= 0) {
      toast.error("Please enter a valid quantity");
      return;
    }

    // Update inventory by increasing the stock
    const updatedInventory = inventory.map(item => {
      if (item.id === currentItem.id) {
        const newStock = item.stock + stockUpdate.quantity;
        // Check if the item is still low on stock after update
        const isLow = newStock <= item.reorderLevel;
        return { ...item, stock: newStock, isLow };
      }
      return item;
    });

    setInventory(updatedInventory);
    setIsUpdateStockOpen(false);

    // Reset the form
    setStockUpdate({
      quantity: 0,
      reason: "delivery",
      invoiceNumber: "",
      date: new Date().toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' }),
      notes: ""
    });

    toast.success(`Stock updated for ${currentItem.name}`);
  };

  // Process imported invoice data
  const handleImportData = () => {
    if (!importData.text.trim()) {
      toast.error("Please enter invoice data");
      return;
    }

    setImportData(prev => ({ ...prev, processing: true }));

    // Simulate processing the invoice text
    setTimeout(() => {
      try {
        // This is a simplified example of parsing invoice data
        // In a real app, you would use a more sophisticated parser
        const lines = importData.text.split('\n');
        const results = [];

        for (const line of lines) {
          // Skip empty lines
          if (!line.trim()) continue;

          // Try to extract item name, quantity, and unit
          const match = line.match(/([a-zA-Z\s]+)\s+(\d+(?:\.\d+)?)\s+([a-zA-Z]+)/);
          if (match) {
            const [, name, quantity, unit] = match;

            // Find matching inventory item
            const matchingItem = inventory.find(item =>
              item.name.toLowerCase().includes(name.trim().toLowerCase())
            );

            results.push({
              text: line,
              parsed: {
                name: name.trim(),
                quantity: parseFloat(quantity),
                unit
              },
              matchedItem: matchingItem || null,
              status: matchingItem ? "matched" : "not_found"
            });
          } else {
            results.push({
              text: line,
              parsed: null,
              matchedItem: null,
              status: "parse_error"
            });
          }
        }

        setImportData(prev => ({
          ...prev,
          processing: false,
          results
        }));

      } catch (error) {
        console.error("Error parsing invoice:", error);
        setImportData(prev => ({
          ...prev,
          processing: false,
          results: null
        }));
        toast.error("Error processing invoice data");
      }
    }, 1000);
  };

  // Apply imported data to inventory
  const handleApplyImport = () => {
    if (!importData.results || importData.results.length === 0) {
      toast.error("No valid data to import");
      return;
    }

    // Update inventory with matched items
    const updatedInventory = [...inventory];
    let updatedCount = 0;

    for (const result of importData.results) {
      if (result.status === "matched" && result.matchedItem && result.parsed) {
        const index = updatedInventory.findIndex(item => item.id === result.matchedItem.id);
        if (index !== -1) {
          const newStock = updatedInventory[index].stock + result.parsed.quantity;
          updatedInventory[index] = {
            ...updatedInventory[index],
            stock: newStock,
            isLow: newStock <= updatedInventory[index].reorderLevel
          };
          updatedCount++;
        }
      }
    }

    setInventory(updatedInventory);
    setIsImportOpen(false);
    setImportData({
      text: "",
      processing: false,
      results: null
    });

    toast.success(`Updated ${updatedCount} inventory items from invoice`);
  };
  // Handle file upload
  const handleFileUpload = async (file: File) => {
    if (!validateFileType(file)) {
      toast.error("Invalid file type. Only CSV, XLSX, and PDF files are allowed.");
      return;
    }

    setImportData(prev => ({ ...prev, processing: true }));

    try {
      // Upload the file to the server
      const response = await uploadFile(file);

      if (!response.success) {
        throw new Error(response.error || 'Error processing file');
      }

      // Process the results
      const processedResults = [];

      for (const item of response.results) {
        // Find matching inventory item
        const matchingItem = inventory.find(invItem =>
          invItem.name.toLowerCase().includes((item.name || '').toLowerCase())
        );

        processedResults.push({
          name: item.name,
          quantity: item.quantity,
          unit: item.unit,
          matchedItem: matchingItem || null,
          status: matchingItem ? "matched" : "not_found"
        });
      }

      setImportData(prev => ({
        ...prev,
        processing: false,
        results: processedResults
      }));
    } catch (error) {
      console.error("Error processing file:", error);
      setImportData(prev => ({
        ...prev,
        processing: false,
        results: null
      }));
      toast.error("Error processing file: " + (error as Error).message);
    }
  };

  // Calculate expiry date based on type
  const calculateExpiryDate = (type: string, customDays: number) => {
    const today = new Date();
    let daysToAdd = 0;

    switch (type) {
      case "defrosted":
        daysToAdd = 3; // 3 days for defrosted meat
        break;
      case "opened":
        daysToAdd = 7; // 7 days for opened items
        break;
      case "custom":
        daysToAdd = customDays;
        break;
      default:
        // Use the item's standard expiry date if available
        if (currentItem?.expiryDate) {
          return currentItem.expiryDate;
        }
        // Default to 14 days if no expiry date is set
        daysToAdd = 14;
    }

    const expiryDate = new Date(today);
    expiryDate.setDate(today.getDate() + daysToAdd);

    return expiryDate.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Handle printing label and updating expiration
  const handlePrintLabel = () => {
    if (!currentItem) {
      toast.error("No item selected");
      return;
    }

    // Calculate expiry date
    const expiryDate = calculateExpiryDate(
      labelData.expiryType === "custom" ? "custom" : labelData.expiryType,
      labelData.customDays
    );

    // Prepare label data
    const labelToPrint = {
      itemName: currentItem.name,
      category: currentItem.category,
      quantity: labelData.quantity,
      unit: labelData.unit || currentItem.unit,
      expiryDate: expiryDate,
      prepDate: new Date().toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      }),
      prepTime: labelData.printTime ?
        new Date().toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit'
        }) : null,
      notes: labelData.notes
    };

    // In a real app, this would send the label to a printer
    // For this demo, we'll just show a success message and log the label
    console.log("Printing label:", labelToPrint);

    // Always update the inventory item with the new expiry date and status
    const updatedInventory = inventory.map(item => {
      if (item.id === currentItem.id) {
        // If we're printing a label for a portion of the stock, we need to:
        // 1. Reduce the main stock by the labeled quantity
        // 2. Mark the item as expiring with the new date

        // Calculate remaining stock after labeling
        const remainingStock = Math.max(0, item.stock - labelData.quantity);

        // Update the item with new expiry date and reduced stock
        return {
          ...item,
          stock: remainingStock,
          expiryDate: expiryDate,
          isExpiring: true // Always mark as expiring when a label is printed
        };
      }
      return item;
    });

    // Update the inventory state
    setInventory(updatedInventory);

    // If we're tracking a specific portion with a different expiry date,
    // we should add a new inventory item for this portion
    if (labelData.expiryType !== "standard" && labelData.quantity > 0 && labelData.quantity < currentItem.stock) {
      // Create a new inventory item for the labeled portion
      const newItemId = (inventory.length + 1).toString();
      const labeledItem: InventoryItem = {
        ...currentItem,
        id: newItemId,
        name: `${currentItem.name} (${labelData.expiryType === "defrosted" ? "Defrosted" :
               labelData.expiryType === "opened" ? "Opened" : "Prepared"})`,
        stock: labelData.quantity,
        unit: labelData.unit || currentItem.unit,
        expiryDate: expiryDate,
        isExpiring: true
      };

      // Add the new item to inventory
      setInventory(prev => [...prev, labeledItem]);

      toast.success(`Created new inventory entry for ${labelData.quantity} ${labeledItem.unit} of ${currentItem.name}`);
    }

    setIsPrintLabelOpen(false);

    // Reset label data
    setLabelData({
      quantity: 1,
      unit: "",
      expiryType: "standard",
      customDays: 0,
      notes: "",
      printDate: true,
      printTime: false
    });

    toast.success(`Label for ${currentItem.name} sent to printer`);
  };

  // Get unique categories from inventory
  const getUniqueCategories = () => {
    const categories = new Set<string>();
    inventory.forEach(item => categories.add(item.category));
    return Array.from(categories).sort();
  };

  // Apply filters and sorting to inventory
  const filterInventory = (items: InventoryItem[], tab: string) => {
    let filtered = [...items];

    // Filter by tab
    if (tab === "low") {
      filtered = filtered.filter(item => item.isLow);
    } else if (tab === "expiring") {
      filtered = filtered.filter(item => item.isExpiring);
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        item =>
          item.name.toLowerCase().includes(query) ||
          item.category.toLowerCase().includes(query)
      );
    }

    // Apply advanced filters
    if (filters.categories.length > 0) {
      filtered = filtered.filter(item => filters.categories.includes(item.category));
    }

    if (filters.stockStatus.length > 0) {
      filtered = filtered.filter(item => {
        if (filters.stockStatus.includes('low') && item.isLow) return true;
        if (filters.stockStatus.includes('normal') && !item.isLow) return true;
        if (filters.stockStatus.includes('expiring') && item.isExpiring) return true;
        return false;
      });
    }

    if (filters.minStock) {
      const minStock = parseFloat(filters.minStock);
      if (!isNaN(minStock)) {
        filtered = filtered.filter(item => item.stock >= minStock);
      }
    }

    if (filters.maxStock) {
      const maxStock = parseFloat(filters.maxStock);
      if (!isNaN(maxStock)) {
        filtered = filtered.filter(item => item.stock <= maxStock);
      }
    }

    if (filters.minCost) {
      const minCost = parseFloat(filters.minCost);
      if (!isNaN(minCost)) {
        filtered = filtered.filter(item => item.cost >= minCost);
      }
    }

    if (filters.maxCost) {
      const maxCost = parseFloat(filters.maxCost);
      if (!isNaN(maxCost)) {
        filtered = filtered.filter(item => item.cost <= maxCost);
      }
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let valueA, valueB;

      // Determine which column to sort by
      switch (sorting.column) {
        case 'name':
          valueA = a.name.toLowerCase();
          valueB = b.name.toLowerCase();
          break;
        case 'category':
          valueA = a.category.toLowerCase();
          valueB = b.category.toLowerCase();
          break;
        case 'stock':
          valueA = a.stock;
          valueB = b.stock;
          break;
        case 'cost':
          valueA = a.cost;
          valueB = b.cost;
          break;
        default:
          valueA = a.name.toLowerCase();
          valueB = b.name.toLowerCase();
      }

      // Apply sort direction
      if (sorting.direction === 'asc') {
        return valueA > valueB ? 1 : valueA < valueB ? -1 : 0;
      } else {
        return valueA < valueB ? 1 : valueA > valueB ? -1 : 0;
      }
    });

    return filtered;
  };

  // Function to get active filter summary
  const getActiveFilterSummary = () => {
    const activeFilters = [];

    if (filters.categories.length > 0) {
      activeFilters.push(`Categories: ${filters.categories.join(', ')}`);
    }

    if (filters.stockStatus.length > 0) {
      activeFilters.push(`Status: ${filters.stockStatus.map(s =>
        s === 'low' ? 'Low Stock' :
        s === 'normal' ? 'Normal' :
        'Expiring Soon'
      ).join(', ')}`);
    }

    if (filters.minStock || filters.maxStock) {
      activeFilters.push(`Stock: ${filters.minStock || '0'} - ${filters.maxStock || '∞'}`);
    }

    if (filters.minCost || filters.maxCost) {
      activeFilters.push(`Cost: £${filters.minCost || '0'} - £${filters.maxCost || '∞'}`);
    }

    return activeFilters;
  };

  const renderInventoryTable = (items: InventoryItem[]) => {
    const activeFilters = getActiveFilterSummary();

    return (
      <div className="rounded-md border">
        {activeFilters.length > 0 && (
          <div className="bg-muted/50 p-2 border-b flex flex-wrap gap-2 items-center">
            <span className="text-sm font-medium">Active Filters:</span>
            {activeFilters.map((filter, index) => (
              <span key={index} className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">
                {filter}
              </span>
            ))}
            <Button
              variant="ghost"
              size="sm"
              className="ml-auto h-7 text-xs"
              onClick={() => {
                setFilters({
                  categories: [],
                  stockStatus: [],
                  minStock: "",
                  maxStock: "",
                  minCost: "",
                  maxCost: ""
                });
              }}
            >
              Clear All
            </Button>
          </div>
        )}
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[300px]">
                <button
                  className="flex items-center hover:text-primary"
                  onClick={() => setSorting({
                    column: 'name',
                    direction: sorting.column === 'name' && sorting.direction === 'asc' ? 'desc' : 'asc'
                  })}
                >
                  Name
                  <ArrowUpDown className={`ml-1 h-3 w-3 ${sorting.column === 'name' ? 'text-primary' : ''}`} />
                  {sorting.column === 'name' && (
                    <span className="ml-1 text-xs text-primary">{sorting.direction === 'asc' ? '↑' : '↓'}</span>
                  )}
                </button>
              </TableHead>
              <TableHead>
                <button
                  className="flex items-center hover:text-primary"
                  onClick={() => setSorting({
                    column: 'category',
                    direction: sorting.column === 'category' && sorting.direction === 'asc' ? 'desc' : 'asc'
                  })}
                >
                  Category
                  <ArrowUpDown className={`ml-1 h-3 w-3 ${sorting.column === 'category' ? 'text-primary' : ''}`} />
                  {sorting.column === 'category' && (
                    <span className="ml-1 text-xs text-primary">{sorting.direction === 'asc' ? '↑' : '↓'}</span>
                  )}
                </button>
              </TableHead>
              <TableHead>Allergens</TableHead>
              <TableHead className="text-center">
                <button
                  className="flex items-center justify-center mx-auto hover:text-primary"
                  onClick={() => setSorting({
                    column: 'stock',
                    direction: sorting.column === 'stock' && sorting.direction === 'asc' ? 'desc' : 'asc'
                  })}
                >
                  Stock
                  <ArrowUpDown className={`ml-1 h-3 w-3 ${sorting.column === 'stock' ? 'text-primary' : ''}`} />
                  {sorting.column === 'stock' && (
                    <span className="ml-1 text-xs text-primary">{sorting.direction === 'asc' ? '↑' : '↓'}</span>
                  )}
                </button>
              </TableHead>
              <TableHead>Unit</TableHead>
              <TableHead className="text-right">
                <button
                  className="flex items-center justify-end ml-auto hover:text-primary"
                  onClick={() => setSorting({
                    column: 'cost',
                    direction: sorting.column === 'cost' && sorting.direction === 'asc' ? 'desc' : 'asc'
                  })}
                >
                  Cost
                  <ArrowUpDown className={`ml-1 h-3 w-3 ${sorting.column === 'cost' ? 'text-primary' : ''}`} />
                  {sorting.column === 'cost' && (
                    <span className="ml-1 text-xs text-primary">{sorting.direction === 'asc' ? '↑' : '↓'}</span>
                  )}
                </button>
              </TableHead>
              <TableHead className="text-center">Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {items.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-6 text-muted-foreground">
                  No inventory items found
                </TableCell>
              </TableRow>
            ) : (
              items.map((item) => (
                <TableRow key={item.id}>
                  <TableCell className="font-medium">{item.name}</TableCell>
                  <TableCell>{item.category}</TableCell>
                  <TableCell>
                    <AllergenBadgeList
                      allergens={item.allergens || []}
                      size="sm"
                      maxDisplay={3}
                    />
                  </TableCell>
                  <TableCell className="text-center">
                    <div className="flex items-center justify-center gap-2">
                      <span
                        className={item.stock <= item.reorderLevel ? "text-restaurant-secondary font-medium" : ""}
                      >
                        {item.stock}
                      </span>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6"
                        onClick={() => {
                          setCurrentItem(item);
                          setIsUpdateStockOpen(true);
                        }}
                        title="Update Stock"
                      >
                        <Plus className="h-3 w-3" />
                      </Button>
                    </div>
                  </TableCell>
                  <TableCell>{item.unit}</TableCell>
                  <TableCell className="text-right">£{item.cost.toFixed(2)}</TableCell>
                  <TableCell>
                    <div className="flex justify-center items-center gap-1">
                      {item.isLow && (
                        <div className="bg-restaurant-secondary/10 text-restaurant-secondary text-xs px-2 py-1 rounded-full flex items-center">
                          <AlertTriangle className="h-3 w-3 mr-1" /> Low
                        </div>
                      )}
                      {item.isExpiring && (
                        <div className="bg-restaurant-warning/10 text-restaurant-warning text-xs px-2 py-1 rounded-full">
                          Expires {item.expiryDate}
                        </div>
                      )}
                      {!item.isLow && !item.isExpiring && (
                        <div className="bg-restaurant-success/10 text-restaurant-success text-xs px-2 py-1 rounded-full">
                          Good
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex justify-end items-center gap-1">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8"
                        onClick={() => {
                          setCurrentItem(item);
                          setLabelData(prev => ({
                            ...prev,
                            unit: item.unit
                          }));
                          setIsPrintLabelOpen(true);
                        }}
                        title="Print Label"
                      >
                        <Printer className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    );
  };

  const renderWasteTable = () => {
    return (
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[200px]">
                <div className="flex items-center">
                  Item <ArrowUpDown className="ml-1 h-3 w-3" />
                </div>
              </TableHead>
              <TableHead>Date</TableHead>
              <TableHead className="text-center">Quantity</TableHead>
              <TableHead>Reason</TableHead>
              <TableHead className="text-right">Cost</TableHead>
              <TableHead>Notes</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {wasteItems.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-6 text-muted-foreground">
                  No waste items recorded
                </TableCell>
              </TableRow>
            ) : (
              wasteItems.map((waste) => {
                const inventoryItem = inventory.find(item => item.id === waste.inventoryItemId);
                return (
                  <TableRow key={waste.id}>
                    <TableCell className="font-medium">
                      {inventoryItem ? inventoryItem.name : "Unknown Item"}
                    </TableCell>
                    <TableCell>{waste.date}</TableCell>
                    <TableCell className="text-center">
                      {waste.quantity} {inventoryItem ? inventoryItem.unit : ""}
                    </TableCell>
                    <TableCell>
                      <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs ${
                        waste.reason === "expired" ? "bg-restaurant-warning/10 text-restaurant-warning" :
                        waste.reason === "damaged" ? "bg-restaurant-secondary/10 text-restaurant-secondary" :
                        waste.reason === "spoiled" ? "bg-red-100 text-red-800" :
                        "bg-gray-100 text-gray-800"
                      }`}>
                        {waste.reason.charAt(0).toUpperCase() + waste.reason.slice(1)}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">£{waste.cost.toFixed(2)}</TableCell>
                    <TableCell className="text-sm text-muted-foreground">
                      {waste.notes || "-"}
                    </TableCell>
                  </TableRow>
                );
              })
            )}
          </TableBody>
        </Table>
      </div>
    );
  };

  return (
    <Layout title="Inventory Management">
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row justify-between gap-4">
          <div className="relative max-w-md">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search inventory..."
              className="pl-8 w-full"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsFilterOpen(true)}
              className={filters.categories.length > 0 || filters.stockStatus.length > 0 ||
                filters.minStock || filters.maxStock || filters.minCost || filters.maxCost ?
                "bg-primary/10 border-primary/20" : ""}
            >
              <Filter className="h-4 w-4 mr-1" />
              Filter {(filters.categories.length > 0 || filters.stockStatus.length > 0 ||
                filters.minStock || filters.maxStock || filters.minCost || filters.maxCost) &&
                `(${[
                  filters.categories.length > 0 ? `${filters.categories.length} categories` : null,
                  filters.stockStatus.length > 0 ? `${filters.stockStatus.length} statuses` : null,
                  filters.minStock || filters.maxStock ? 'stock range' : null,
                  filters.minCost || filters.maxCost ? 'cost range' : null
                ].filter(Boolean).length})`}
            </Button>
            <Button size="sm" onClick={() => setIsAddItemOpen(true)}>
              <Plus className="h-4 w-4 mr-1" /> Add Item
            </Button>
            <Button size="sm" variant="secondary" onClick={() => setIsAddWasteOpen(true)}>
              <AlertTriangle className="h-4 w-4 mr-1" /> Record Waste
            </Button>
            <Button size="sm" variant="outline" onClick={() => setIsImportOpen(true)}>
              <FileUp className="h-4 w-4 mr-1" /> Import
            </Button>
          </div>
        </div>

        <Card>
          <CardContent className="pt-6">
            <Tabs defaultValue="all">
              <TabsList className="mb-4">
                <TabsTrigger value="all">All Items</TabsTrigger>
                <TabsTrigger value="low">Low Stock</TabsTrigger>
                <TabsTrigger value="expiring">Expiring Soon</TabsTrigger>
                <TabsTrigger value="waste">Waste Log</TabsTrigger>
                <TabsTrigger value="forecast">
                  <BarChart className="h-4 w-4 mr-2" />
                  Forecasted Usage
                </TabsTrigger>
              </TabsList>
              <TabsContent value="all">
                {renderInventoryTable(filterInventory(inventory, "all"))}
              </TabsContent>
              <TabsContent value="low">
                {renderInventoryTable(filterInventory(inventory, "low"))}
              </TabsContent>
              <TabsContent value="expiring">
                {renderInventoryTable(filterInventory(inventory, "expiring"))}
              </TabsContent>
              <TabsContent value="waste">
                {renderWasteTable()}
              </TabsContent>

              <TabsContent value="forecast">
                <ForecastedInventory
                  inventoryData={inventory}
                  forecastData={mockForecastData}
                  onGenerateOrder={(items) => {
                    console.log("Order generated:", items);
                    toast.success(`Order generated for ${items.length} items`);
                  }}
                />
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>

      {/* Add Item Dialog */}
      <Dialog open={isAddItemOpen} onOpenChange={setIsAddItemOpen}>
        <DialogContent className="sm:max-w-[550px] max-h-[90vh] flex flex-col">
          <DialogHeader>
            <DialogTitle>Add Inventory Item</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4 overflow-y-auto flex-1 pr-2">
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="name">Name *</Label>
                <Input
                  id="name"
                  value={newItem.name}
                  onChange={(e) => setNewItem({ ...newItem, name: e.target.value })}
                  placeholder="e.g., Tomatoes"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="category">Category *</Label>
                <Select
                  value={newItem.category}
                  onValueChange={(value) => setNewItem({ ...newItem, category: value })}
                >
                  <SelectTrigger id="category">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map(category => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="stock">Current Stock *</Label>
                <Input
                  id="stock"
                  type="number"
                  min="0"
                  value={newItem.stock}
                  onChange={(e) => setNewItem({ ...newItem, stock: parseInt(e.target.value) })}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="unit">Unit</Label>
                <Select
                  value={newItem.unit}
                  onValueChange={(value) => setNewItem({ ...newItem, unit: value })}
                >
                  <SelectTrigger id="unit">
                    <SelectValue placeholder="Select unit" />
                  </SelectTrigger>
                  <SelectContent>
                    {units.map(unit => (
                      <SelectItem key={unit} value={unit}>
                        {unit}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="reorderLevel">Reorder Level</Label>
                <Input
                  id="reorderLevel"
                  type="number"
                  min="0"
                  value={newItem.reorderLevel}
                  onChange={(e) => setNewItem({ ...newItem, reorderLevel: parseInt(e.target.value) })}
                  placeholder="Minimum stock level"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="cost">Cost (£)</Label>
                <Input
                  id="cost"
                  type="number"
                  min="0"
                  step="0.01"
                  value={newItem.cost}
                  onChange={(e) => setNewItem({ ...newItem, cost: parseFloat(e.target.value) })}
                  placeholder="Cost per unit"
                />
              </div>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="expiryDate">Expiry Date (optional)</Label>
              <Input
                id="expiryDate"
                type="text"
                value={newItem.expiryDate || ""}
                onChange={(e) => setNewItem({ ...newItem, expiryDate: e.target.value || undefined })}
                placeholder="e.g., May 15, 2025"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="allergens">Allergens</Label>
              <AllergenSelector
                selectedAllergens={newItem.allergens || []}
                onChange={(allergens) => setNewItem({ ...newItem, allergens })}
                placeholder="Select allergens if any"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddItemOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddItem}>
              Add Item
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Update Stock Dialog */}
      <Dialog open={isUpdateStockOpen} onOpenChange={setIsUpdateStockOpen}>
        <DialogContent className="sm:max-w-[550px] max-h-[90vh] flex flex-col">
          <DialogHeader>
            <DialogTitle>Update Stock</DialogTitle>
            {currentItem && (
              <p className="text-sm text-muted-foreground mt-1">
                {currentItem.name} - Current stock: {currentItem.stock} {currentItem.unit}
              </p>
            )}
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="quantity">Quantity to Add *</Label>
                <Input
                  id="quantity"
                  type="number"
                  min="0.1"
                  step="0.1"
                  value={stockUpdate.quantity}
                  onChange={(e) => setStockUpdate({ ...stockUpdate, quantity: parseFloat(e.target.value) })}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="date">Date *</Label>
                <Input
                  id="date"
                  type="text"
                  value={stockUpdate.date}
                  onChange={(e) => setStockUpdate({ ...stockUpdate, date: e.target.value })}
                  placeholder="e.g., May 15, 2025"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="reason">Reason</Label>
                <Select
                  value={stockUpdate.reason}
                  onValueChange={(value) => setStockUpdate({ ...stockUpdate, reason: value })}
                >
                  <SelectTrigger id="reason">
                    <SelectValue placeholder="Select reason" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="delivery">Delivery</SelectItem>
                    <SelectItem value="return">Return</SelectItem>
                    <SelectItem value="adjustment">Inventory Adjustment</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="invoiceNumber">Invoice Number</Label>
                <Input
                  id="invoiceNumber"
                  value={stockUpdate.invoiceNumber}
                  onChange={(e) => setStockUpdate({ ...stockUpdate, invoiceNumber: e.target.value })}
                  placeholder="e.g., INV-12345"
                />
              </div>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="notes">Notes</Label>
              <Input
                id="notes"
                value={stockUpdate.notes}
                onChange={(e) => setStockUpdate({ ...stockUpdate, notes: e.target.value })}
                placeholder="Additional details"
              />
            </div>
            {currentItem && stockUpdate.quantity > 0 && (
              <div className="bg-muted/50 p-3 rounded-md border text-sm">
                <p className="font-medium">Preview:</p>
                <p>New stock level will be: <span className="font-medium">{currentItem.stock + stockUpdate.quantity} {currentItem.unit}</span></p>
                <p>Status will be: <span className={`font-medium ${(currentItem.stock + stockUpdate.quantity) <= currentItem.reorderLevel ? "text-restaurant-secondary" : "text-restaurant-success"}`}>
                  {(currentItem.stock + stockUpdate.quantity) <= currentItem.reorderLevel ? "Low Stock" : "In Stock"}
                </span></p>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsUpdateStockOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateStock}>
              Update Stock
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Waste Dialog */}
      <Dialog open={isAddWasteOpen} onOpenChange={setIsAddWasteOpen}>
        <DialogContent className="sm:max-w-[550px] max-h-[90vh] flex flex-col">
          <DialogHeader>
            <DialogTitle>Record Waste Item</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4 overflow-y-auto flex-1 pr-2">
            <div className="grid gap-2">
              <Label htmlFor="inventoryItem">Inventory Item *</Label>
              <Select
                value={newWaste.inventoryItemId}
                onValueChange={(value) => setNewWaste({ ...newWaste, inventoryItemId: value })}
              >
                <SelectTrigger id="inventoryItem">
                  <SelectValue placeholder="Select inventory item" />
                </SelectTrigger>
                <SelectContent>
                  {inventory.map(item => (
                    <SelectItem key={item.id} value={item.id}>
                      {item.name} ({item.stock} {item.unit} available)
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="date">Date *</Label>
                <Input
                  id="date"
                  type="text"
                  value={newWaste.date}
                  onChange={(e) => setNewWaste({ ...newWaste, date: e.target.value })}
                  placeholder="e.g., May 15, 2025"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="quantity">Quantity *</Label>
                <Input
                  id="quantity"
                  type="number"
                  min="0.1"
                  step="0.1"
                  value={newWaste.quantity}
                  onChange={(e) => setNewWaste({ ...newWaste, quantity: parseFloat(e.target.value) })}
                />
              </div>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="reason">Reason *</Label>
              <Select
                value={newWaste.reason}
                onValueChange={(value: "expired" | "damaged" | "spoiled" | "other") =>
                  setNewWaste({ ...newWaste, reason: value })
                }
              >
                <SelectTrigger id="reason">
                  <SelectValue placeholder="Select reason" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="expired">Expired</SelectItem>
                  <SelectItem value="damaged">Damaged</SelectItem>
                  <SelectItem value="spoiled">Spoiled</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="notes">Notes (optional)</Label>
              <Input
                id="notes"
                value={newWaste.notes || ""}
                onChange={(e) => setNewWaste({ ...newWaste, notes: e.target.value })}
                placeholder="Additional details about the waste"
              />
            </div>
            {newWaste.inventoryItemId && newWaste.quantity > 0 && (
              <div className="text-sm text-muted-foreground">
                {(() => {
                  const item = inventory.find(i => i.id === newWaste.inventoryItemId);
                  if (item) {
                    const cost = item.cost * newWaste.quantity;
                    return (
                      <div className="p-2 border rounded bg-muted/50">
                        <p>Estimated cost: <span className="font-medium">£{cost.toFixed(2)}</span></p>
                        <p>New stock after recording: <span className="font-medium">{Math.max(0, item.stock - newWaste.quantity)} {item.unit}</span></p>
                      </div>
                    );
                  }
                  return null;
                })()}
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddWasteOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddWaste} variant="secondary">
              Record Waste
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

            {/* Import Dialog */}
      <Dialog open={isImportOpen} onOpenChange={setIsImportOpen}>
        <DialogContent className="sm:max-w-[700px]">
          <DialogHeader>
            <DialogTitle>Import Inventory</DialogTitle>
            <p className="text-sm text-muted-foreground mt-1">
              Upload a file to automatically update inventory
            </p>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="fileUpload">Select File</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="fileUpload"
                  type="file"
                  accept=".csv,.xlsx,.pdf"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      // Clear previous results
                      setImportData({
                        text: "",
                        processing: false,
                        results: null
                      });

                      // Handle file upload
                      handleFileUpload(file);
                    }
                  }}
                  disabled={importData.processing}
                />
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                Supported file types: CSV, XLSX, PDF
              </p>
            </div>

            {importData.processing && (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                <span className="ml-2">Processing file data...</span>
              </div>
            )}

            {importData.results && (
              <div className="border rounded-md overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Quantity</TableHead>
                      <TableHead>Unit</TableHead>
                      <TableHead>Matched Item</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {importData.results.map((result, index) => (
                      <TableRow key={index}>
                        <TableCell>{result.name || result.parsed?.name || 'Unknown'}</TableCell>
                        <TableCell>{result.quantity || result.parsed?.quantity || 'N/A'}</TableCell>
                        <TableCell>{result.unit || result.parsed?.unit || 'N/A'}</TableCell>
                        <TableCell>
                          {result.matchedItem ? (
                            <div className="text-sm">
                              <div className="font-medium">{result.matchedItem.name}</div>
                              <div className="text-xs text-muted-foreground">Current: {result.matchedItem.stock} {result.matchedItem.unit}</div>
                            </div>
                          ) : (
                            <span className="text-muted-foreground text-sm">No match found</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="inline-flex items-center px-2 py-1 rounded-full text-xs">
                            {result.status === "matched" ? (
                              <><Check className="h-3 w-3 mr-1" /> Matched</>
                            ) : result.status === "not_found" ? (
                              <><AlertTriangle className="h-3 w-3 mr-1" /> Not Found</>
                            ) : (
                              <><X className="h-3 w-3 mr-1" /> Error</>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </div>
          <DialogFooter className="flex items-center justify-between">
            <div>
              {importData.results && (
                <div className="text-sm">
                  <span className="font-medium">
                    {importData.results.filter(r => r.status === "matched").length} of {importData.results.length} items matched
                  </span>
                </div>
              )}
            </div>
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => setIsImportOpen(false)}>
                Cancel
              </Button>
              {importData.results && (
                <Button onClick={handleApplyImport} disabled={!importData.results.some(r => r.status === "matched")}>
                  <FileText className="mr-2 h-4 w-4" />
                  Apply Changes
                </Button>
              )}
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Print Label Dialog */}
      <Dialog open={isPrintLabelOpen} onOpenChange={setIsPrintLabelOpen}>
        <DialogContent className="sm:max-w-[550px]">
          <DialogHeader>
            <DialogTitle>Print Label</DialogTitle>
            {currentItem && (
              <p className="text-sm text-muted-foreground mt-1">
                {currentItem.name} - {currentItem.category}
              </p>
            )}
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="quantity">Quantity</Label>
                <Input
                  id="quantity"
                  type="number"
                  min="0.1"
                  step="0.1"
                  value={labelData.quantity}
                  onChange={(e) => setLabelData({ ...labelData, quantity: parseFloat(e.target.value) })}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="unit">Unit</Label>
                <Input
                  id="unit"
                  value={labelData.unit}
                  onChange={(e) => setLabelData({ ...labelData, unit: e.target.value })}
                  placeholder={currentItem?.unit || "e.g., kg, liters"}
                />
              </div>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="expiryType">Expiry Type</Label>
              <Select
                value={labelData.expiryType}
                onValueChange={(value) => setLabelData({ ...labelData, expiryType: value })}
              >
                <SelectTrigger id="expiryType">
                  <SelectValue placeholder="Select expiry type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="standard">Standard (Use item's expiry date)</SelectItem>
                  <SelectItem value="defrosted">Defrosted (3 days)</SelectItem>
                  <SelectItem value="opened">Opened (7 days)</SelectItem>
                  <SelectItem value="custom">Custom</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {labelData.expiryType === "custom" && (
              <div className="grid gap-2">
                <Label htmlFor="customDays">Days until expiry</Label>
                <Input
                  id="customDays"
                  type="number"
                  min="1"
                  value={labelData.customDays}
                  onChange={(e) => setLabelData({ ...labelData, customDays: parseInt(e.target.value) })}
                />
              </div>
            )}

            <div className="grid gap-2">
              <Label htmlFor="notes">Notes (Optional)</Label>
              <Input
                id="notes"
                value={labelData.notes}
                onChange={(e) => setLabelData({ ...labelData, notes: e.target.value })}
                placeholder="e.g., Handle with care, Keep refrigerated"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="printDate"
                  checked={labelData.printDate}
                  onChange={(e) => setLabelData({ ...labelData, printDate: e.target.checked })}
                  className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                />
                <Label htmlFor="printDate" className="text-sm font-normal">Print preparation date</Label>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="printTime"
                  checked={labelData.printTime}
                  onChange={(e) => setLabelData({ ...labelData, printTime: e.target.checked })}
                  className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                />
                <Label htmlFor="printTime" className="text-sm font-normal">Print preparation time</Label>
              </div>
            </div>

            {/* Preview */}
            <div className="mt-2 border rounded-md p-4 bg-muted/30">
              <h3 className="text-sm font-medium mb-2">Label Preview</h3>
              <div className="bg-card border rounded-md p-3 text-sm">
                <div className="font-bold text-card-foreground">{currentItem?.name}</div>
                <div className="text-card-foreground">Qty: {labelData.quantity} {labelData.unit || currentItem?.unit}</div>
                <div className="text-restaurant-warning font-medium">
                  Expires: {calculateExpiryDate(
                    labelData.expiryType === "custom" ? "custom" : labelData.expiryType,
                    labelData.customDays
                  )}
                </div>
                {labelData.printDate && (
                  <div className="text-xs text-muted-foreground">
                    Prep: {new Date().toLocaleDateString('en-US', {
                      month: 'short',
                      day: 'numeric',
                      year: 'numeric'
                    })}
                    {labelData.printTime && ` ${new Date().toLocaleTimeString('en-US', {
                      hour: '2-digit',
                      minute: '2-digit'
                    })}`}
                  </div>
                )}
                {labelData.notes && (
                  <div className="text-xs mt-1 italic">{labelData.notes}</div>
                )}
              </div>

              {/* Stock update preview */}
              {currentItem && labelData.quantity > 0 && (
                <div className="mt-3 text-xs text-muted-foreground">
                  <div className="font-medium text-sm mb-1">Inventory Updates:</div>
                  <ul className="list-disc pl-4 space-y-1">
                    <li>
                      Main stock will be reduced by {labelData.quantity} {labelData.unit || currentItem.unit}
                      <span className="font-medium"> ({currentItem.stock} → {Math.max(0, currentItem.stock - labelData.quantity)})</span>
                    </li>
                    {labelData.expiryType !== "standard" && labelData.quantity > 0 && labelData.quantity < currentItem.stock && (
                      <li>
                        New inventory item will be created for {labelData.quantity} {labelData.unit || currentItem.unit} of
                        <span className="font-medium"> {currentItem.name} ({labelData.expiryType === "defrosted" ? "Defrosted" :
                          labelData.expiryType === "opened" ? "Opened" : "Prepared"})</span>
                      </li>
                    )}
                    <li>
                      Expiration date will be set to <span className="font-medium">{calculateExpiryDate(
                        labelData.expiryType === "custom" ? "custom" : labelData.expiryType,
                        labelData.customDays
                      )}</span>
                    </li>
                  </ul>
                </div>
              )}
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsPrintLabelOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handlePrintLabel}>
              <Printer className="mr-2 h-4 w-4" /> Print Label
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Filter Dialog */}
      <Dialog open={isFilterOpen} onOpenChange={setIsFilterOpen}>
        <DialogContent className="sm:max-w-[550px]">
          <DialogHeader>
            <DialogTitle>Filter Inventory</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            {/* Categories */}
            <div className="grid gap-2">
              <Label>Categories</Label>
              <div className="grid grid-cols-2 gap-2">
                {getUniqueCategories().map(category => (
                  <div key={category} className="flex items-center space-x-2">
                    <Checkbox
                      id={`category-${category}`}
                      checked={filters.categories.includes(category)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setFilters(prev => ({
                            ...prev,
                            categories: [...prev.categories, category]
                          }));
                        } else {
                          setFilters(prev => ({
                            ...prev,
                            categories: prev.categories.filter(c => c !== category)
                          }));
                        }
                      }}
                    />
                    <label
                      htmlFor={`category-${category}`}
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      {category}
                    </label>
                  </div>
                ))}
              </div>
            </div>

            {/* Stock Status */}
            <div className="grid gap-2">
              <Label>Stock Status</Label>
              <div className="grid grid-cols-3 gap-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="status-low"
                    checked={filters.stockStatus.includes('low')}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setFilters(prev => ({
                          ...prev,
                          stockStatus: [...prev.stockStatus, 'low']
                        }));
                      } else {
                        setFilters(prev => ({
                          ...prev,
                          stockStatus: prev.stockStatus.filter(s => s !== 'low')
                        }));
                      }
                    }}
                  />
                  <label
                    htmlFor="status-low"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Low Stock
                  </label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="status-normal"
                    checked={filters.stockStatus.includes('normal')}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setFilters(prev => ({
                          ...prev,
                          stockStatus: [...prev.stockStatus, 'normal']
                        }));
                      } else {
                        setFilters(prev => ({
                          ...prev,
                          stockStatus: prev.stockStatus.filter(s => s !== 'normal')
                        }));
                      }
                    }}
                  />
                  <label
                    htmlFor="status-normal"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Normal
                  </label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="status-expiring"
                    checked={filters.stockStatus.includes('expiring')}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setFilters(prev => ({
                          ...prev,
                          stockStatus: [...prev.stockStatus, 'expiring']
                        }));
                      } else {
                        setFilters(prev => ({
                          ...prev,
                          stockStatus: prev.stockStatus.filter(s => s !== 'expiring')
                        }));
                      }
                    }}
                  />
                  <label
                    htmlFor="status-expiring"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Expiring Soon
                  </label>
                </div>
              </div>
            </div>

            {/* Stock Range */}
            <div className="grid gap-2">
              <Label>Stock Range</Label>
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="minStock" className="text-xs">Min Stock</Label>
                  <Input
                    id="minStock"
                    type="number"
                    placeholder="Min"
                    value={filters.minStock}
                    onChange={(e) => setFilters(prev => ({ ...prev, minStock: e.target.value }))}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="maxStock" className="text-xs">Max Stock</Label>
                  <Input
                    id="maxStock"
                    type="number"
                    placeholder="Max"
                    value={filters.maxStock}
                    onChange={(e) => setFilters(prev => ({ ...prev, maxStock: e.target.value }))}
                  />
                </div>
              </div>
            </div>

            {/* Cost Range */}
            <div className="grid gap-2">
              <Label>Cost Range (£)</Label>
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="minCost" className="text-xs">Min Cost</Label>
                  <Input
                    id="minCost"
                    type="number"
                    placeholder="Min"
                    value={filters.minCost}
                    onChange={(e) => setFilters(prev => ({ ...prev, minCost: e.target.value }))}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="maxCost" className="text-xs">Max Cost</Label>
                  <Input
                    id="maxCost"
                    type="number"
                    placeholder="Max"
                    value={filters.maxCost}
                    onChange={(e) => setFilters(prev => ({ ...prev, maxCost: e.target.value }))}
                  />
                </div>
              </div>
            </div>
          </div>
          <DialogFooter className="flex justify-between">
            <Button
              variant="outline"
              onClick={() => {
                setFilters({
                  categories: [],
                  stockStatus: [],
                  minStock: "",
                  maxStock: "",
                  minCost: "",
                  maxCost: ""
                });
              }}
            >
              Reset Filters
            </Button>
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => setIsFilterOpen(false)}>
                Cancel
              </Button>
              <Button onClick={() => setIsFilterOpen(false)}>
                Apply Filters
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Layout>
  );
};

export default Inventory;

