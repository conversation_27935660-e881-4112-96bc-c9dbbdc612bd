# RestroManage State Management System

This document provides a comprehensive guide to the state management system implemented for RestroManage-v0, covering all tab states with restaurant ID isolation, persistence, and efficient re-rendering.

## Overview

The state management system combines:
- **React Query** for server state management and caching
- **Custom hooks** for simplified state access
- **Restaurant ID isolation** for multi-tenant architecture
- **Comprehensive error handling** and loading states
- **Data synchronization** patterns

## Architecture

### Core Components

1. **React Query Integration** (`/hooks/useStoreQueries.ts`)
   - Server state management
   - Automatic caching and invalidation
   - Background refetching
   - Optimistic updates

2. **Simplified Store Hooks** (`/hooks/useAppStore.ts`)
   - Tab-specific state management
   - Local state for UI preferences
   - Integration with React Query

3. **Error Handling System** (`/hooks/useErrorHandling.ts`)
   - Comprehensive error classification
   - Retry mechanisms
   - Loading state management
   - Network status monitoring

4. **UI Components** (`/components/common/ErrorBoundary.tsx`)
   - Error boundaries
   - Loading components
   - Network status indicators
   - Data status displays

## Usage Guide

### Dashboard State Management

```tsx
import { useDashboardStore } from '@/hooks/useAppStore';
import { TabLoadingWrapper } from '@/components/common/ErrorBoundary';

const Dashboard = () => {
  const {
    metrics,
    recentOrders,
    quickStats,
    alerts,
    isLoading,
    error,
    refreshMetrics,
    addAlert,
    toggleRealTime,
    isRealTimeEnabled,
  } = useDashboardStore();

  return (
    <TabLoadingWrapper
      isLoading={isLoading}
      error={error}
      onRetry={refreshMetrics}
      context="Dashboard"
    >
      {/* Dashboard content */}
      <div className="grid grid-cols-4 gap-4">
        {quickStats.map(stat => (
          <StatCard key={stat.id} {...stat} />
        ))}
      </div>
      
      {/* Real-time toggle */}
      <button onClick={toggleRealTime}>
        Real-time: {isRealTimeEnabled ? 'ON' : 'OFF'}
      </button>
      
      {/* Add alert */}
      <button onClick={() => addAlert({
        type: 'info',
        title: 'Test Alert',
        message: 'This is a test alert',
        priority: 'medium',
      })}>
        Add Alert
      </button>
    </TabLoadingWrapper>
  );
};
```

### Analytics State Management

```tsx
import { useAnalyticsStore } from '@/hooks/useAppStore';

const Analytics = () => {
  const {
    data,
    filters,
    chartConfigs,
    isLoading,
    error,
    refreshData,
    updateFilters,
    updateChartConfig,
    exportData,
  } = useAnalyticsStore();

  const handleDateRangeChange = (dateRange) => {
    updateFilters({ dateRange });
  };

  const handleExport = async () => {
    try {
      await exportData('csv');
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  return (
    <TabLoadingWrapper
      isLoading={isLoading}
      error={error}
      onRetry={refreshData}
      context="Analytics"
    >
      {/* Date range selector */}
      <DateRangeSelector onChange={handleDateRangeChange} />
      
      {/* Charts */}
      {chartConfigs.map(config => (
        <Chart
          key={config.id}
          data={data.salesData}
          config={config}
          onConfigChange={(updates) => updateChartConfig(config.id, updates)}
        />
      ))}
      
      {/* Export button */}
      <button onClick={handleExport}>Export CSV</button>
    </TabLoadingWrapper>
  );
};
```

### Staff State Management

```tsx
import { useStaffStore } from '@/hooks/useAppStore';

const Staff = () => {
  const {
    members,
    activeStaff,
    selectedMember,
    isLoading,
    isCreating,
    addStaffMember,
    setSelectedMember,
    refreshData,
  } = useStaffStore();

  const handleAddStaff = async () => {
    try {
      await addStaffMember({
        name: 'New Staff Member',
        email: '<EMAIL>',
        role: 'waiter',
        position: 'Waiter',
      });
    } catch (error) {
      console.error('Failed to add staff:', error);
    }
  };

  return (
    <TabLoadingWrapper
      isLoading={isLoading}
      error={error}
      onRetry={refreshData}
      context="Staff"
    >
      {/* Staff list */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <h3>All Staff ({members.length})</h3>
          {members.map(member => (
            <StaffCard
              key={member.id}
              member={member}
              onClick={() => setSelectedMember(member)}
            />
          ))}
        </div>
        
        <div>
          <h3>Active Staff ({activeStaff.length})</h3>
          {activeStaff.map(member => (
            <StaffCard key={member.id} member={member} />
          ))}
        </div>
      </div>
      
      {/* Add staff button */}
      <button onClick={handleAddStaff} disabled={isCreating}>
        {isCreating ? 'Adding...' : 'Add Staff'}
      </button>
    </TabLoadingWrapper>
  );
};
```

### Orders State Management

```tsx
import { useOrdersStore } from '@/hooks/useAppStore';

const Orders = () => {
  const {
    activeOrders,
    ordersQueue,
    kitchenDisplay,
    selectedOrder,
    isLoading,
    isCreating,
    createOrder,
    updateOrder,
    setSelectedOrder,
    refreshData,
  } = useOrdersStore();

  const handleCreateOrder = async () => {
    try {
      await createOrder({
        tableId: 'table_1',
        items: [
          {
            menuItemId: 'item_1',
            name: 'Test Item',
            quantity: 1,
            price: 10.00,
            modifications: [],
          }
        ],
        totalAmount: 10.00,
        tax: 2.00,
        discount: 0,
        staffId: 'staff_1',
      });
    } catch (error) {
      console.error('Failed to create order:', error);
    }
  };

  const handleUpdateOrderStatus = async (orderId, status) => {
    try {
      await updateOrder(orderId, { status });
    } catch (error) {
      console.error('Failed to update order:', error);
    }
  };

  return (
    <TabLoadingWrapper
      isLoading={isLoading}
      error={error}
      onRetry={refreshData}
      context="Orders"
    >
      {/* Orders grid */}
      <div className="grid grid-cols-3 gap-4">
        <div>
          <h3>Active Orders ({activeOrders.length})</h3>
          {activeOrders.map(order => (
            <OrderCard
              key={order.id}
              order={order}
              onClick={() => setSelectedOrder(order)}
              onStatusChange={(status) => handleUpdateOrderStatus(order.id, status)}
            />
          ))}
        </div>
        
        <div>
          <h3>Queue ({ordersQueue.length})</h3>
          {ordersQueue.map(order => (
            <OrderCard key={order.id} order={order} />
          ))}
        </div>
        
        <div>
          <h3>Kitchen Display ({kitchenDisplay.length})</h3>
          {kitchenDisplay.map(order => (
            <KitchenOrderCard key={order.orderId} order={order} />
          ))}
        </div>
      </div>
      
      {/* Create order button */}
      <button onClick={handleCreateOrder} disabled={isCreating}>
        {isCreating ? 'Creating...' : 'Create Order'}
      </button>
    </TabLoadingWrapper>
  );
};
```

### Settings State Management

```tsx
import { useSettingsStore } from '@/hooks/useAppStore';

const Settings = () => {
  const {
    restaurantSettings,
    userPreferences,
    isDirty,
    isLoading,
    isSaving,
    updateUserPreferences,
    updateRestaurantSettings,
    validateSettings,
    refreshData,
  } = useSettingsStore();

  const handleSaveSettings = async () => {
    try {
      const errors = validateSettings();
      if (errors.length > 0) {
        console.error('Validation errors:', errors);
        return;
      }
      
      await updateRestaurantSettings(restaurantSettings);
    } catch (error) {
      console.error('Failed to save settings:', error);
    }
  };

  const handleThemeChange = () => {
    const themes = ['light', 'dark', 'system'];
    const currentIndex = themes.indexOf(userPreferences.theme);
    const nextTheme = themes[(currentIndex + 1) % themes.length];
    updateUserPreferences({ theme: nextTheme });
  };

  return (
    <TabLoadingWrapper
      isLoading={isLoading}
      error={error}
      onRetry={refreshData}
      context="Settings"
    >
      {/* Restaurant settings */}
      <div className="space-y-4">
        <h3>Restaurant Settings</h3>
        <input
          value={restaurantSettings.name || ''}
          onChange={(e) => updateRestaurantSettings({ name: e.target.value })}
          placeholder="Restaurant Name"
        />
        
        {/* User preferences */}
        <h3>User Preferences</h3>
        <button onClick={handleThemeChange}>
          Theme: {userPreferences.theme}
        </button>
        
        {/* Save button */}
        {isDirty && (
          <button onClick={handleSaveSettings} disabled={isSaving}>
            {isSaving ? 'Saving...' : 'Save Changes'}
          </button>
        )}
      </div>
    </TabLoadingWrapper>
  );
};
```

## Error Handling

### Using Error Boundaries

```tsx
import { ErrorBoundary } from '@/components/common/ErrorBoundary';

const App = () => {
  return (
    <ErrorBoundary context="Application">
      <Dashboard />
    </ErrorBoundary>
  );
};
```

### Custom Error Handling

```tsx
import { useErrorHandling } from '@/hooks/useErrorHandling';

const MyComponent = () => {
  const { handleAsyncOperation, addError } = useErrorHandling();

  const performAction = async () => {
    try {
      await handleAsyncOperation(
        () => apiCall(),
        'MyComponent',
        { maxRetries: 3, retryDelay: 1000 }
      );
    } catch (error) {
      // Error is automatically handled and logged
    }
  };

  return <button onClick={performAction}>Perform Action</button>;
};
```

## Data Synchronization

### Global Sync

```tsx
import { useAppStore } from '@/hooks/useAppStore';

const SyncButton = () => {
  const { syncWithBackend, lastSyncTime } = useAppStore();

  const handleSync = async () => {
    try {
      await syncWithBackend();
    } catch (error) {
      console.error('Sync failed:', error);
    }
  };

  return (
    <div>
      <button onClick={handleSync}>Sync All Data</button>
      {lastSyncTime && (
        <span>Last sync: {lastSyncTime.toLocaleTimeString()}</span>
      )}
    </div>
  );
};
```

### Component-Specific Sync

```tsx
import { useDataSync } from '@/hooks/useErrorHandling';

const MyComponent = () => {
  const { syncData, syncStatus } = useDataSync();

  const handleRefresh = async () => {
    await syncData(['dashboard', 'orders'], 'MyComponent');
  };

  return (
    <div>
      <button onClick={handleRefresh} disabled={syncStatus.isSync}>
        {syncStatus.isSync ? 'Syncing...' : 'Refresh'}
      </button>
    </div>
  );
};
```

## Best Practices

1. **Always use TabLoadingWrapper** for consistent loading and error states
2. **Handle errors gracefully** with try-catch blocks and user feedback
3. **Use optimistic updates** for better user experience
4. **Implement proper loading states** for all async operations
5. **Validate data** before sending to the server
6. **Use restaurant ID isolation** for multi-tenant data
7. **Cache data appropriately** with React Query
8. **Provide retry mechanisms** for failed operations
9. **Monitor network status** and adapt UI accordingly
10. **Log errors comprehensively** for debugging

## Performance Considerations

- React Query automatically handles caching and background refetching
- Use selective re-rendering with proper dependency arrays
- Implement pagination for large datasets
- Use optimistic updates for immediate feedback
- Debounce user inputs for search and filters
- Lazy load components and data when possible

## Testing

```tsx
import { renderHook } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useDashboardStore } from '@/hooks/useAppStore';

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return ({ children }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

test('dashboard store loads data correctly', async () => {
  const { result } = renderHook(() => useDashboardStore(), {
    wrapper: createWrapper(),
  });

  expect(result.current.isLoading).toBe(true);
  // Add more assertions...
});
```

This state management system provides a robust, scalable foundation for RestroManage-v0 with proper error handling, loading states, and data synchronization across all tabs.
