#!/usr/bin/env python3
"""
Database migration script to add missing columns to the orders table.
This script adds the columns required by the Order Pydantic model.
"""

import sqlite3
import sys
from pathlib import Path

def migrate_orders_table():
    """Add missing columns to the orders table"""
    
    # Database file path
    db_path = Path("restro_manage.db")
    
    if not db_path.exists():
        print("❌ Database file not found: restro_manage.db")
        return False
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔄 Starting orders table migration...")
        
        # Check current table structure
        cursor.execute("PRAGMA table_info(orders)")
        existing_columns = {row[1] for row in cursor.fetchall()}
        print(f"📋 Current columns: {sorted(existing_columns)}")
        
        # Define new columns to add
        new_columns = {
            'subtotal': 'FLOAT DEFAULT 0.0',
            'payment_status': 'VARCHAR(20) DEFAULT "unpaid"',
            'tax_amount': 'FLOAT DEFAULT 0.0',
            'tip_amount': 'FLOAT DEFAULT 0.0',
            'discount_amount': 'FLOAT DEFAULT 0.0',
            'applied_discounts': 'TEXT',  # JSON string
            'is_split_bill': 'BOOLEAN DEFAULT 0',
            'split_bill_id': 'VARCHAR'
        }
        
        # Add missing columns
        columns_added = 0
        for column_name, column_def in new_columns.items():
            if column_name not in existing_columns:
                try:
                    sql = f"ALTER TABLE orders ADD COLUMN {column_name} {column_def}"
                    cursor.execute(sql)
                    print(f"✅ Added column: {column_name}")
                    columns_added += 1
                except sqlite3.Error as e:
                    print(f"❌ Failed to add column {column_name}: {e}")
                    return False
            else:
                print(f"⏭️  Column already exists: {column_name}")
        
        # Commit changes
        conn.commit()
        
        # Verify the migration
        cursor.execute("PRAGMA table_info(orders)")
        final_columns = {row[1] for row in cursor.fetchall()}
        print(f"📋 Final columns: {sorted(final_columns)}")
        
        print(f"✅ Migration completed successfully! Added {columns_added} columns.")
        
        return True
        
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    print("🔧 Orders Table Migration Script")
    print("=" * 50)
    
    success = migrate_orders_table()
    
    if success:
        print("\n🎉 Migration completed successfully!")
        sys.exit(0)
    else:
        print("\n💥 Migration failed!")
        sys.exit(1)
