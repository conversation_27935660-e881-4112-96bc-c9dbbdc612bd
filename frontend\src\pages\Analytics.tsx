import logger from "@/utils/logger";
import { useState, useEffect } from "react";
import Layout from "@/components/layout/Layout";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "@/components/ui/sonner";
import DateRangeSelector from "@/components/dashboard/DateRangeSelector";
import StatCard from "@/components/dashboard/StatCard";
import CustomerFeedbackCard from "@/components/dashboard/CustomerFeedbackCard";
import ForecastCard, { ForecastData } from "@/components/dashboard/ForecastCard";
import PopularItemsCard from "@/components/dashboard/PopularItemsCard";
import SalesPredictionCard from "@/components/dashboard/SalesPredictionCard";
import IngredientForecastCard from "@/components/dashboard/IngredientForecastCard";
import CustomerTrafficCard from "@/components/dashboard/CustomerTrafficCard";
import SeasonalTrendCard from "@/components/dashboard/SeasonalTrendCard";
import RevenueProjectionCard from "@/components/dashboard/RevenueProjectionCard";
import StaffScheduleAnalytics from "@/components/analytics/scheduling/StaffScheduleAnalytics";
import ReportsSection from "@/components/analytics/ReportsSection";
import CategoryPerformanceCard from "@/components/analytics/CategoryPerformanceCard";
import PeakHoursAnalysisCard from "@/components/analytics/PeakHoursAnalysisCard";
import { TrendingUp, DollarSign, Users, ShoppingBag, AlertCircle, Loader2 } from "lucide-react";
import { analyticsApi, testConnection, type SalesData, type PopularItem, type RevenueData, type ForecastData as ApiForecastData, type AnalyticsResponse } from "@/services/api";
const mockReviews = [
  {
    id: "1",
    customerName: "John Smith",
    rating: 5,
    comment: "Excellent food and service! Will definitely come back.",
    date: "2023-06-15"
  },
  {
    id: "2",
    customerName: "Sarah Johnson",
    rating: 4,
    comment: "Great atmosphere and delicious food. Service was a bit slow.",
    date: "2023-06-14"
  },
  {
    id: "3",
    customerName: "Michael Brown",
    rating: 5,
    comment: "The best Italian food in town! Highly recommend the pasta dishes.",
    date: "2023-06-12"
  },
  {
    id: "4",
    customerName: "Emily Davis",
    rating: 3,
    comment: "Food was good but portions were small for the price.",
    date: "2023-06-10"
  },
  {
    id: "5",
    customerName: "David Wilson",
    rating: 4,
    comment: "Lovely ambiance and great wine selection. Will return!",
    date: "2023-06-08"
  }
];

// Mock popular menu items
const mockMenuItems = [
  {
    id: "1",
    name: "Margherita Pizza",
    category: "Pizza",
    price: 12.99,
    orderCount: 145,
    percentageOfSales: 18,
    trend: 5
  },
  {
    id: "2",
    name: "Spaghetti Carbonara",
    category: "Pasta",
    price: 14.99,
    orderCount: 120,
    percentageOfSales: 15,
    trend: 8
  },
  {
    id: "3",
    name: "Chicken Parmesan",
    category: "Main Course",
    price: 16.99,
    orderCount: 95,
    percentageOfSales: 12,
    trend: -3
  },
  {
    id: "4",
    name: "Tiramisu",
    category: "Dessert",
    price: 7.99,
    orderCount: 85,
    percentageOfSales: 10,
    trend: 12
  },
  {
    id: "5",
    name: "Caesar Salad",
    category: "Salad",
    price: 9.99,
    orderCount: 75,
    percentageOfSales: 9,
    trend: -2
  }
];


const Analytics = () => {
  // Initialize component logging
  logger.setComponent("Analytics");
  logger.info("Component initialized", "Analytics");

  // Transform SalesData to ForecastData format
  const transformSalesDataToForecast = (salesData: SalesData[]): ForecastData[] => {
    return salesData.map(sale => ({
      day: sale.date,
      actualRevenue: sale.sales,
      projectedRevenue: sale.sales * 1.05, // Add 5% projection
      customers: sale.orders * 2.5 // Estimate customers from orders
    }));
  };

  // State management
  const [dateRange, setDateRange] = useState<{ from: Date; to: Date }>({
    from: new Date(new Date().setDate(new Date().getDate() - 7)),
    to: new Date(),
  });

  // API data state
  const [isLoading, setIsLoading] = useState(true);
  const [isBackendConnected, setIsBackendConnected] = useState(false);
  const [apiSalesData, setApiSalesData] = useState<SalesData[]>([]);
  const [apiPopularItems, setApiPopularItems] = useState<PopularItem[]>([]);
  const [apiRevenueData, setApiRevenueData] = useState<RevenueData[]>([]);
  const [apiForecastData, setApiForecastData] = useState<ApiForecastData[]>([]);
  const [dashboardData, setDashboardData] = useState<AnalyticsResponse | null>(null);
  const [categoryPerformance, setCategoryPerformance] = useState<any[]>([]);
  const [peakHoursData, setPeakHoursData] = useState<any[]>([]);
  const [profitMarginData, setProfitMarginData] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);

  // Get restaurant ID from localStorage or use default for development
  const restaurantId = localStorage.getItem('restaurant_id') || "4b41fcac-d638-43f1-b10b-0530d86fa781";

  // API data fetching
  const fetchAnalyticsData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Test backend connection first
      const isConnected = await testConnection();
      setIsBackendConnected(isConnected);

      if (!isConnected) {
        logger.warn("Backend connection failed, using fallback data", "Analytics");
        setError("Backend unavailable - using cached data");
        return;
      }

      // Fetch all analytics data in parallel
      const [sales, popular, revenue, forecast, dashboard, categoryPerf, peakHours, profitMargins] = await Promise.allSettled([
        analyticsApi.getSalesData(
          dateRange.from.toISOString().split('T')[0],
          dateRange.to.toISOString().split('T')[0],
          restaurantId
        ),
        analyticsApi.getPopularItems(5, restaurantId),
        analyticsApi.getRevenueData('weekly', restaurantId),
        analyticsApi.getForecastData(restaurantId),
        analyticsApi.getDashboardData(restaurantId),
        analyticsApi.getCategoryPerformance(restaurantId, 30),
        analyticsApi.getPeakHoursAnalysis(restaurantId, 30),
        analyticsApi.getProfitMarginAnalysis(restaurantId, 30)
      ]);

      // Handle results
      if (sales.status === 'fulfilled') {
        setApiSalesData(sales.value);
        logger.info("Sales data loaded", "Analytics", { count: sales.value.length, data: sales.value });
      } else {
        logger.error("Sales data failed to load", "Analytics", { error: sales.reason });
      }

      if (popular.status === 'fulfilled') {
        setApiPopularItems(popular.value);
        logger.info("Popular items loaded", "Analytics", { count: popular.value.length, data: popular.value });
      } else {
        logger.error("Popular items failed to load", "Analytics", { error: popular.reason });
      }

      if (revenue.status === 'fulfilled') {
        setApiRevenueData(revenue.value);
        logger.info("Revenue data loaded", "Analytics", { count: revenue.value.length });
      }

      if (forecast.status === 'fulfilled') {
        setApiForecastData(forecast.value);
        logger.info("Forecast data loaded", "Analytics", { count: forecast.value.length });
      }

      if (dashboard.status === 'fulfilled') {
        setDashboardData(dashboard.value);
        logger.info("Dashboard data loaded", "Analytics");
      }

      if (categoryPerf.status === 'fulfilled') {
        setCategoryPerformance(categoryPerf.value);
        logger.info("Category performance loaded", "Analytics", { count: categoryPerf.value.length });
      }

      if (peakHours.status === 'fulfilled') {
        setPeakHoursData(peakHours.value);
        logger.info("Peak hours data loaded", "Analytics", { count: peakHours.value.length });
      }

      if (profitMargins.status === 'fulfilled') {
        setProfitMarginData(profitMargins.value);
        logger.info("Profit margin data loaded", "Analytics", { count: profitMargins.value.length });
      }

      // Check for any errors
      const errors = [sales, popular, revenue, forecast, dashboard, categoryPerf, peakHours, profitMargins]
        .filter(result => result.status === 'rejected')
        .map(result => (result as PromiseRejectedResult).reason);

      if (errors.length > 0) {
        logger.error("Some analytics data failed to load", "Analytics", { errors });
        setError(`Failed to load some data: ${errors.length} errors`);
      }

    } catch (error) {
      logger.error("Failed to fetch analytics data", "Analytics", { error });
      setError("Failed to load analytics data");
      toast.error("Failed to load analytics data");
    } finally {
      setIsLoading(false);
    }
  };

  // Load data on component mount and when date range changes
  useEffect(() => {
    fetchAnalyticsData();
  }, [dateRange, restaurantId]);

  // Sales data with correct ForecastData structure (fallback data)
  const salesData: ForecastData[] = [
    {
      day: "Monday",
      actualRevenue: 1200,
      projectedRevenue: 1100,
      customers: 45
    },
    {
      day: "Tuesday",
      actualRevenue: 1350,
      projectedRevenue: 1300,
      customers: 52
    },
    {
      day: "Wednesday",
      actualRevenue: 1500,
      projectedRevenue: 1450,
      customers: 61
    },
    {
      day: "Thursday",
      actualRevenue: 1700,
      projectedRevenue: 1600,
      customers: 70
    },
    {
      day: "Friday",
      actualRevenue: 2100,
      projectedRevenue: 2000,
      customers: 85
    },
    {
      day: "Saturday",
      actualRevenue: 2400,
      projectedRevenue: 2500,
      customers: 98
    },
    {
      day: "Sunday",
      actualRevenue: 1900,
      projectedRevenue: 1800,
      customers: 76
    }
  ];

  // Future forecast with correct ForecastData structure
  const forecastData: ForecastData[] = [
    {
      day: "Next Monday",
      actualRevenue: 0,
      projectedRevenue: 1150,
      customers: 47,
      confidence: 85,
      tableOccupancy: 65,
      staffNeeded: 8,
      peakHours: [
        { hour: 12, customers: 15 },
        { hour: 13, customers: 18 },
        { hour: 19, customers: 22 },
        { hour: 20, customers: 25 }
      ],
      ingredientUsage: [
        { name: "Chicken", quantity: 12, unit: "kg", cost: 85 },
        { name: "Beef", quantity: 8, unit: "kg", cost: 95 },
        { name: "Pasta", quantity: 6, unit: "kg", cost: 25 },
        { name: "Tomatoes", quantity: 10, unit: "kg", cost: 35 }
      ],
      seasonalTrend: 5.2,
      historicalComparison: 8.5,
      trafficByHour: [
        { hour: 11, traffic: 5 },
        { hour: 12, traffic: 15 },
        { hour: 13, traffic: 18 },
        { hour: 14, traffic: 10 },
        { hour: 18, traffic: 18 },
        { hour: 19, traffic: 22 },
        { hour: 20, traffic: 25 },
        { hour: 21, traffic: 15 }
      ]
    },
    {
      day: "Next Tuesday",
      actualRevenue: 0,
      projectedRevenue: 1380,
      customers: 54,
      confidence: 82,
      tableOccupancy: 70,
      staffNeeded: 9,
      peakHours: [
        { hour: 12, customers: 18 },
        { hour: 13, customers: 20 },
        { hour: 19, customers: 25 },
        { hour: 20, customers: 28 }
      ],
      ingredientUsage: [
        { name: "Chicken", quantity: 14, unit: "kg", cost: 98 },
        { name: "Beef", quantity: 9, unit: "kg", cost: 105 },
        { name: "Pasta", quantity: 7, unit: "kg", cost: 28 },
        { name: "Tomatoes", quantity: 12, unit: "kg", cost: 42 }
      ],
      seasonalTrend: 6.5,
      historicalComparison: 9.2,
      trafficByHour: [
        { hour: 11, traffic: 6 },
        { hour: 12, traffic: 18 },
        { hour: 13, traffic: 20 },
        { hour: 14, traffic: 12 },
        { hour: 18, traffic: 20 },
        { hour: 19, traffic: 25 },
        { hour: 20, traffic: 28 },
        { hour: 21, traffic: 18 }
      ]
    },
    {
      day: "Next Wednesday",
      actualRevenue: 0,
      projectedRevenue: 1520,
      customers: 63,
      confidence: 88,
      tableOccupancy: 75,
      staffNeeded: 10,
      peakHours: [
        { hour: 12, customers: 20 },
        { hour: 13, customers: 22 },
        { hour: 19, customers: 28 },
        { hour: 20, customers: 30 }
      ],
      ingredientUsage: [
        { name: "Chicken", quantity: 16, unit: "kg", cost: 112 },
        { name: "Beef", quantity: 10, unit: "kg", cost: 120 },
        { name: "Pasta", quantity: 8, unit: "kg", cost: 32 },
        { name: "Tomatoes", quantity: 14, unit: "kg", cost: 49 }
      ],
      seasonalTrend: 7.8,
      historicalComparison: 10.5,
      trafficByHour: [
        { hour: 11, traffic: 8 },
        { hour: 12, traffic: 20 },
        { hour: 13, traffic: 22 },
        { hour: 14, traffic: 15 },
        { hour: 18, traffic: 25 },
        { hour: 19, traffic: 28 },
        { hour: 20, traffic: 30 },
        { hour: 21, traffic: 20 }
      ]
    },
    {
      day: "Next Thursday",
      actualRevenue: 0,
      projectedRevenue: 1750,
      customers: 72,
      confidence: 86,
      tableOccupancy: 80,
      staffNeeded: 12,
      peakHours: [
        { hour: 12, customers: 22 },
        { hour: 13, customers: 25 },
        { hour: 19, customers: 32 },
        { hour: 20, customers: 35 }
      ],
      ingredientUsage: [
        { name: "Chicken", quantity: 18, unit: "kg", cost: 126 },
        { name: "Beef", quantity: 12, unit: "kg", cost: 144 },
        { name: "Pasta", quantity: 10, unit: "kg", cost: 40 },
        { name: "Tomatoes", quantity: 16, unit: "kg", cost: 56 }
      ],
      seasonalTrend: 8.2,
      historicalComparison: 12.0,
      trafficByHour: [
        { hour: 11, traffic: 10 },
        { hour: 12, traffic: 22 },
        { hour: 13, traffic: 25 },
        { hour: 14, traffic: 18 },
        { hour: 18, traffic: 28 },
        { hour: 19, traffic: 32 },
        { hour: 20, traffic: 35 },
        { hour: 21, traffic: 22 }
      ]
    },
    {
      day: "Next Friday",
      actualRevenue: 0,
      projectedRevenue: 2200,
      customers: 90,
      confidence: 90,
      tableOccupancy: 95,
      staffNeeded: 15,
      peakHours: [
        { hour: 12, customers: 28 },
        { hour: 13, customers: 32 },
        { hour: 19, customers: 42 },
        { hour: 20, customers: 45 }
      ],
      ingredientUsage: [
        { name: "Chicken", quantity: 22, unit: "kg", cost: 154 },
        { name: "Beef", quantity: 15, unit: "kg", cost: 180 },
        { name: "Pasta", quantity: 12, unit: "kg", cost: 48 },
        { name: "Rice", quantity: 8, unit: "kg", cost: 32 },
        { name: "Tomatoes", quantity: 20, unit: "kg", cost: 70 }
      ],
      seasonalTrend: 9.5,
      historicalComparison: 14.2,
      trafficByHour: [
        { hour: 11, traffic: 12 },
        { hour: 12, traffic: 28 },
        { hour: 13, traffic: 32 },
        { hour: 14, traffic: 22 },
        { hour: 18, traffic: 38 },
        { hour: 19, traffic: 42 },
        { hour: 20, traffic: 45 },
        { hour: 21, traffic: 30 },
        { hour: 22, traffic: 18 }
      ]
    },
    {
      day: "Next Saturday",
      actualRevenue: 0,
      projectedRevenue: 2600,
      customers: 105,
      confidence: 92,
      tableOccupancy: 100,
      staffNeeded: 18,
      peakHours: [
        { hour: 12, customers: 32 },
        { hour: 13, customers: 38 },
        { hour: 19, customers: 48 },
        { hour: 20, customers: 52 }
      ],
      ingredientUsage: [
        { name: "Chicken", quantity: 26, unit: "kg", cost: 182 },
        { name: "Beef", quantity: 18, unit: "kg", cost: 216 },
        { name: "Pasta", quantity: 14, unit: "kg", cost: 56 },
        { name: "Rice", quantity: 10, unit: "kg", cost: 40 },
        { name: "Tomatoes", quantity: 24, unit: "kg", cost: 84 }
      ],
      seasonalTrend: 10.8,
      historicalComparison: 16.5,
      trafficByHour: [
        { hour: 11, traffic: 15 },
        { hour: 12, traffic: 32 },
        { hour: 13, traffic: 38 },
        { hour: 14, traffic: 25 },
        { hour: 18, traffic: 42 },
        { hour: 19, traffic: 48 },
        { hour: 20, traffic: 52 },
        { hour: 21, traffic: 35 },
        { hour: 22, traffic: 22 }
      ]
    },
    {
      day: "Next Sunday",
      actualRevenue: 0,
      projectedRevenue: 2000,
      customers: 80,
      confidence: 88,
      tableOccupancy: 85,
      staffNeeded: 14,
      peakHours: [
        { hour: 12, customers: 25 },
        { hour: 13, customers: 30 },
        { hour: 19, customers: 35 },
        { hour: 20, customers: 38 }
      ],
      ingredientUsage: [
        { name: "Chicken", quantity: 20, unit: "kg", cost: 140 },
        { name: "Beef", quantity: 14, unit: "kg", cost: 168 },
        { name: "Pasta", quantity: 11, unit: "kg", cost: 44 },
        { name: "Tomatoes", quantity: 18, unit: "kg", cost: 63 }
      ],
      seasonalTrend: 8.5,
      historicalComparison: 12.8,
      trafficByHour: [
        { hour: 11, traffic: 12 },
        { hour: 12, traffic: 25 },
        { hour: 13, traffic: 30 },
        { hour: 14, traffic: 20 },
        { hour: 18, traffic: 32 },
        { hour: 19, traffic: 35 },
        { hour: 20, traffic: 38 },
        { hour: 21, traffic: 25 }
      ]
    }
  ];

  // Generate 90-day forecast data for the SalesPredictionCard
  const generate90DayForecast = (): ForecastData[] => {
    const data: ForecastData[] = [];
    const startDate = new Date();
    const daysOfWeek = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];

    // Base values
    const baseRevenue = 1500;
    const baseCustomers = 60;

    // Generate data for 90 days
    for (let i = 0; i < 90; i++) {
      const currentDate = new Date(startDate);
      currentDate.setDate(startDate.getDate() + i);

      const dayOfWeek = daysOfWeek[currentDate.getDay()];
      const isWeekend = currentDate.getDay() === 0 || currentDate.getDay() === 6;

      // Add some randomness and weekend boost
      const dayFactor = isWeekend ? 1.5 : 1.0;
      const randomFactor = 0.9 + Math.random() * 0.3; // Random between 0.9 and 1.2

      // Add seasonal trend (higher in summer months)
      const month = currentDate.getMonth();
      const seasonalFactor = month >= 5 && month <= 7 ? 1.2 : 1.0; // Summer boost

      const projectedRevenue = Math.round(baseRevenue * dayFactor * randomFactor * seasonalFactor);
      const customers = Math.round(baseCustomers * dayFactor * randomFactor * seasonalFactor);

      // Format the date as "Month Day"
      const formattedDate = currentDate.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric'
      });

      data.push({
        day: formattedDate,
        actualRevenue: 0, // No actual revenue for future dates
        projectedRevenue,
        customers,
        confidence: Math.round(85 + Math.random() * 10),
        seasonalTrend: month >= 5 && month <= 7 ? 8.5 : 5.2,
        historicalComparison: month >= 5 && month <= 7 ? 12.0 : 8.0
      });
    }

    return data;
  };

  // Generate the 90-day forecast data
  const ninetyDayForecast = generate90DayForecast();

  const handleDateRangeChange = (range: { from: Date; to: Date }) => {
    setDateRange(range);
    logger.userAction("date range changed", "Analytics", { range });
    // Data will be refetched automatically due to useEffect dependency
  };

  // Loading component
  const LoadingSpinner = () => (
    <div className="flex items-center justify-center p-8">
      <Loader2 className="h-8 w-8 animate-spin" />
      <span className="ml-2">Loading analytics data...</span>
    </div>
  );

  // Error component
  const ErrorAlert = ({ message }: { message: string }) => (
    <div className="flex items-center p-4 bg-red-50 border border-red-200 rounded-lg">
      <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
      <span className="text-red-700">{message}</span>
      <button
        onClick={fetchAnalyticsData}
        className="ml-auto px-3 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200"
      >
        Retry
      </button>
    </div>
  );

  // Backend status indicator
  const BackendStatus = () => (
    <div className={`flex items-center text-sm ${isBackendConnected ? 'text-green-600' : 'text-red-600'}`}>
      <div className={`w-2 h-2 rounded-full mr-2 ${isBackendConnected ? 'bg-green-500' : 'bg-red-500'}`} />
      {isBackendConnected ? 'Backend Connected' : 'Backend Offline'}
    </div>
  );

  return (
    <Layout title="Analytics" requiredRoles={["admin"]}>
      <div className="flex flex-col gap-4">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold">Analytics Dashboard</h2>
            <BackendStatus />
            {/* Debug info */}
            {import.meta.env.DEV && (
              <div className="mt-2 text-xs text-muted-foreground">
                Debug: Restaurant ID: {restaurantId} | Backend: {isBackendConnected ? '✅' : '❌'} |
                Sales: {apiSalesData.length} | Popular: {apiPopularItems.length} |
                Categories: {categoryPerformance.length} | Peak Hours: {peakHoursData.length} |
                Loading: {isLoading ? 'Yes' : 'No'} | Error: {error || 'None'}
              </div>
            )}
          </div>
          <DateRangeSelector onRangeChange={handleDateRangeChange} />
        </div>

        {/* Error Alert */}
        {error && <ErrorAlert message={error} />}

        {/* Loading State */}
        {isLoading && <LoadingSpinner />}

        <Tabs defaultValue="overview" className="w-full">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="categories">Category Analytics</TabsTrigger>
            <TabsTrigger value="staff">Staff Analytics</TabsTrigger>
            <TabsTrigger value="inventory">Inventory</TabsTrigger>
            <TabsTrigger value="reports">Reports</TabsTrigger>
            <TabsTrigger value="forecasts">Forecasts</TabsTrigger>
            <TabsTrigger value="advanced-forecast">Advanced Forecast</TabsTrigger>
          </TabsList>
          <TabsContent value="overview" className="space-y-4">
            {!isLoading && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <StatCard
                  title="Total Revenue"
                  value={`£${(apiSalesData.length > 0 ? apiSalesData.reduce((sum, day) => sum + day.sales, 0) : salesData.reduce((sum, day) => sum + day.actualRevenue, 0)).toLocaleString()}`}
                  icon={<span className="font-bold text-lg">£</span>}
                  trend={{ value: 12, isPositive: true }}
                  className="bg-card"
                />
                <StatCard
                  title="Total Orders"
                  value={(apiSalesData.length > 0 ? apiSalesData.reduce((sum, day) => sum + day.orders, 0) : salesData.reduce((sum, day) => sum + day.customers, 0)).toLocaleString()}
                  icon={<ShoppingBag className="h-5 w-5" />}
                  trend={{ value: 8, isPositive: true }}
                  className="bg-card"
                />
                <StatCard
                  title="Popular Items"
                  value={(apiPopularItems.length > 0 ? apiPopularItems : mockMenuItems).length.toString()}
                  icon={<TrendingUp className="h-5 w-5" />}
                  trend={{ value: 3, isPositive: true }}
                  className="bg-card"
                />
                <StatCard
                  title="Average Daily Sales"
                  value={`£${(apiSalesData.length > 0 ? apiSalesData.length > 0 ? Math.round(apiSalesData.reduce((sum, day) => sum + day.sales, 0) / apiSalesData.length).toLocaleString() : '0' : salesData.length > 0 ? Math.round(salesData.reduce((sum, day) => sum + day.actualRevenue, 0) / salesData.length).toLocaleString() : '0')}`}
                  icon={<DollarSign className="h-5 w-5" />}
                  trend={{ value: 5, isPositive: true }}
                  className="bg-card"
                />
              </div>
            )}

            {!isLoading && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <ForecastCard data={apiSalesData.length > 0 ? transformSalesDataToForecast(apiSalesData) : salesData} title="Sales Performance" />
                <PopularItemsCard items={apiPopularItems.length > 0 ? apiPopularItems : mockMenuItems} />
              </div>
            )}

            {!isLoading && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <CustomerFeedbackCard reviews={mockReviews} />
              </div>
            )}
          </TabsContent>

          <TabsContent value="categories" className="space-y-4">
            {!isLoading && (
              <div className="grid grid-cols-1 gap-6">
                <CategoryPerformanceCard
                  data={profitMarginData.length > 0 ? profitMarginData : categoryPerformance}
                  title="Category Performance & Profitability"
                />

                <PeakHoursAnalysisCard
                  data={peakHoursData}
                  title="Peak Hours & Category Preferences"
                />

                {categoryPerformance.length > 0 && (
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    <Card>
                      <CardHeader>
                        <CardTitle>Category Insights</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          {categoryPerformance.slice(0, 5).map((category, index) => (
                            <div key={index} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                              <div>
                                <div className="font-medium">{category.category}</div>
                                <div className="text-sm text-muted-foreground">
                                  {category.totalQuantity} items sold • {category.uniqueItems} unique items
                                </div>
                              </div>
                              <div className="text-right">
                                <div className="font-bold text-green-600">
                                  £{category.totalRevenue?.toFixed(2) || '0.00'}
                                </div>
                                <div className="text-sm text-muted-foreground">
                                  £{category.avgOrderValue?.toFixed(2) || '0.00'} avg order
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle>Performance Metrics</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div className="text-center p-3 bg-muted rounded-lg">
                              <div className="text-2xl font-bold text-blue-600">
                                {categoryPerformance.length}
                              </div>
                              <div className="text-sm text-muted-foreground">Active Categories</div>
                            </div>
                            <div className="text-center p-3 bg-muted rounded-lg">
                              <div className="text-2xl font-bold text-green-600">
                                £{categoryPerformance.reduce((sum, cat) => sum + (cat.totalRevenue || 0), 0).toFixed(0)}
                              </div>
                              <div className="text-sm text-muted-foreground">Total Revenue</div>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <h4 className="text-sm font-medium">Top Performing Categories</h4>
                            {categoryPerformance
                              .sort((a, b) => (b.totalRevenue || 0) - (a.totalRevenue || 0))
                              .slice(0, 3)
                              .map((category, index) => (
                                <div key={index} className="flex items-center justify-between text-sm">
                                  <span>{category.category}</span>
                                  <span className="font-medium text-green-600">
                                    £{category.totalRevenue?.toFixed(0) || '0'}
                                  </span>
                                </div>
                              ))}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                )}
              </div>
            )}

            {isLoading && (
              <div className="flex items-center justify-center py-12">
                <Loader2 className="h-8 w-8 animate-spin" />
                <span className="ml-2">Loading category analytics...</span>
              </div>
            )}

            {!isLoading && categoryPerformance.length === 0 && peakHoursData.length === 0 && (
              <div className="text-center py-12">
                <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">No Category Data Available</h3>
                <p className="text-muted-foreground">
                  Category analytics will appear here once you have order data with menu categories.
                </p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="staff" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <StatCard
                title="Upselling Success Rate"
                value="68%"
                icon={<TrendingUp className="h-5 w-5" />}
                trend={{ value: 12, isPositive: true }}
                className="bg-card"
              />
              <StatCard
                title="Average Upsell Value"
                value="£8.50"
                icon={<span className="font-bold text-lg">£</span>}
                trend={{ value: 5, isPositive: true }}
                className="bg-card"
              />
              <StatCard
                title="Staff Performance Score"
                value="4.2/5"
                icon={<Users className="h-5 w-5" />}
                trend={{ value: 3, isPositive: true }}
                className="bg-card"
              />
              <StatCard
                title="Customer Satisfaction"
                value="92%"
                icon={<TrendingUp className="h-5 w-5" />}
                trend={{ value: 8, isPositive: true }}
                className="bg-card"
              />
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>Upselling Performance by Staff</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {[
                      { name: "Sarah Johnson", upsells: 45, rate: "72%", value: "£385" },
                      { name: "Mike Chen", upsells: 38, rate: "65%", value: "£320" },
                      { name: "Emma Davis", upsells: 52, rate: "78%", value: "£445" },
                      { name: "James Wilson", upsells: 31, rate: "58%", value: "£265" }
                    ].map((staff, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <div className="font-medium">{staff.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {staff.upsells} successful upsells
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-bold text-green-600">{staff.rate}</div>
                          <div className="text-sm text-muted-foreground">{staff.value}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Top Upselling Items</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {[
                      { item: "Extra Cheese", count: 156, revenue: "£390" },
                      { item: "Garlic Bread", count: 134, revenue: "£469" },
                      { item: "Dessert Upgrade", count: 98, revenue: "£588" },
                      { item: "Wine Pairing", count: 87, revenue: "£695" },
                      { item: "Premium Toppings", count: 76, revenue: "£456" }
                    ].map((item, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <div className="font-medium">{item.item}</div>
                          <div className="text-sm text-muted-foreground">
                            {item.count} times sold
                          </div>
                        </div>
                        <div className="font-bold text-green-600">{item.revenue}</div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="inventory" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {[
                { category: "Vegetables", items: 45, lowStock: 3, value: "£1,250" },
                { category: "Meat & Poultry", items: 28, lowStock: 1, value: "£2,890" },
                { category: "Dairy Products", items: 32, lowStock: 5, value: "£890" },
                { category: "Beverages", items: 67, lowStock: 2, value: "£1,450" },
                { category: "Dry Goods", items: 89, lowStock: 8, value: "£750" },
                { category: "Frozen Items", items: 34, lowStock: 0, value: "£1,120" }
              ].map((category, index) => (
                <Card
                  key={index}
                  className="cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => {
                    toast.info(`Viewing detailed inventory for ${category.category}`);
                    // Here you would typically navigate to detailed inventory view
                  }}
                >
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg">{category.category}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Total Items:</span>
                        <span className="font-medium">{category.items}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Low Stock:</span>
                        <span className={`font-medium ${category.lowStock > 0 ? 'text-red-600' : 'text-green-600'}`}>
                          {category.lowStock}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Total Value:</span>
                        <span className="font-bold">{category.value}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="reports" className="space-y-4">
            <ReportsSection />
          </TabsContent>

          <TabsContent value="forecasts" className="space-y-4">
            {!isLoading && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <ForecastCard data={apiForecastData.length > 0 ? transformSalesDataToForecast(apiSalesData) : forecastData} title="Future Forecast" />
                <PopularItemsCard items={apiPopularItems.length > 0 ? apiPopularItems : mockMenuItems} />
              </div>
            )}
          </TabsContent>

          <TabsContent value="advanced-forecast" className="space-y-6">
            <div className="grid grid-cols-1 gap-6">
              <SalesPredictionCard data={ninetyDayForecast} title="Sales Predictions (30/60/90 Days)" />

              <IngredientForecastCard data={forecastData} title="Ingredient Usage Forecast" />

              <CustomerTrafficCard data={forecastData} title="Customer Traffic Predictions" />

              <SeasonalTrendCard data={forecastData} title="Seasonal Trend Analysis" />

              <RevenueProjectionCard data={forecastData} title="Revenue Projections" />
            </div>
          </TabsContent>
          
        </Tabs>
      </div>
    </Layout>
  );
};

export default Analytics;
