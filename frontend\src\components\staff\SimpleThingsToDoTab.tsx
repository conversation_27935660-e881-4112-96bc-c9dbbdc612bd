import logger from "@/utils/logger";
import React, { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardT<PERSON>le, CardDescription } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>board<PERSON>ist, <PERSON>rush, Scissors, RefreshCw, CheckCircle, RefreshCcw } from "lucide-react";
import { toast } from "@/components/ui/sonner";
import { Badge } from "@/components/ui/badge";
import { Task, getAllTasks, markTaskAsCompleted } from "@/services/taskService";

interface SimpleThingsToDoTabProps {
  staffData: any[];
}

const SimpleThingsToDoTab: React.FC<SimpleThingsToDoTabProps> = ({ staffData }) => {
  const [recentTasks, setRecentTasks] = useState<Task[]>([]);
  const [filteredTasks, setFilteredTasks] = useState<Task[]>([]);
  const [activeFilter, setActiveFilter] = useState<string>("all");

  useEffect(() => {
    // Load recent tasks on component mount
    loadRecentTasks();
  }, []);

  const loadRecentTasks = () => {
    const allTasks = getAllTasks();
    // Get 3 random pending tasks
    const pendingTasks = allTasks.filter(task => task.status === "pending");
    const randomTasks = pendingTasks.sort(() => 0.5 - Math.random()).slice(0, 3);
    setRecentTasks(randomTasks);

    // Initialize filtered tasks with all tasks
    setFilteredTasks(allTasks);
  };

  const handleFilterTasks = (filter: string) => {
    const allTasks = getAllTasks();
    setActiveFilter(filter);

    if (filter === "all") {
      setFilteredTasks(allTasks);
    } else if (filter === "cleaning") {
      setFilteredTasks(allTasks.filter(task => task.category === "cleaning"));
    } else if (filter === "cutting") {
      setFilteredTasks(allTasks.filter(task => task.category === "cutting"));
    } else if (filter === "refilling") {
      setFilteredTasks(allTasks.filter(task => task.category === "refilling"));
    }
  };

  const handleCompleteTask = (taskId: string) => {
    // Get the current user ID (assuming the first staff member for demo purposes)
    const currentUserId = staffData[0]?.id;
    const staffName = staffData[0]?.name || "Unknown staff";

    logger.userAction(`task completion: ${taskId}`, "SimpleThingsToDoTab", { userId: currentUserId, staffName });

    // Find the task before update
    const taskBeforeUpdate = recentTasks.find(task => task.id === taskId);
    logger.debug('Task before update', 'SimpleThingsToDoTab', { task: taskBeforeUpdate });

    // Mark the task as completed with the current user ID
    const updatedTask = markTaskAsCompleted(taskId, currentUserId);

    if (updatedTask) {
      logger.dataOperation('update', 'task', 'SimpleThingsToDoTab', { taskId: updatedTask.id });

      // Update local state with the task returned from the service
      setRecentTasks(recentTasks.map(task => {
        if (task.id === taskId) {
          return updatedTask;
        }
        return task;
      }));
    } else {
      logger.error('Failed to update task', 'SimpleThingsToDoTab');
      toast.error('Failed to mark task as completed');
    }

    // Force a refresh of the tasks from localStorage
    setTimeout(() => {
      logger.dataOperation('refresh', 'tasks from localStorage', 'SimpleThingsToDoTab');
      loadRecentTasks();
    }, 500);

    // Show success message with staff name
    toast.success(`Task marked as completed by ${staffName}`);
  };
  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Things to Do</CardTitle>
          <CardDescription>
            Manage tasks for staff members
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card className={activeFilter === "all" ? "border-primary" : ""}>
              <CardHeader className="pb-2">
                <div className="flex items-center gap-2">
                  <div className={`${activeFilter === "all" ? "bg-primary/20" : "bg-blue-100"} p-2 rounded-full`}>
                    <ClipboardList className={`h-5 w-5 ${activeFilter === "all" ? "text-primary" : "text-blue-600"}`} />
                  </div>
                  <CardTitle className="text-lg">All Tasks</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">View and manage all staff tasks</p>
                <Button
                  className="mt-4 w-full"
                  variant={activeFilter === "all" ? "default" : "outline"}
                  onClick={() => handleFilterTasks("all")}
                >
                  View Tasks
                </Button>
              </CardContent>
            </Card>

            <Card className={activeFilter === "cleaning" ? "border-primary" : ""}>
              <CardHeader className="pb-2">
                <div className="flex items-center gap-2">
                  <div className={`${activeFilter === "cleaning" ? "bg-primary/20" : "bg-green-100"} p-2 rounded-full`}>
                    <Brush className={`h-5 w-5 ${activeFilter === "cleaning" ? "text-primary" : "text-green-600"}`} />
                  </div>
                  <CardTitle className="text-lg">Cleaning Tasks</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">Manage cleaning and maintenance tasks</p>
                <Button
                  className="mt-4 w-full"
                  variant={activeFilter === "cleaning" ? "default" : "outline"}
                  onClick={() => handleFilterTasks("cleaning")}
                >
                  View Tasks
                </Button>
              </CardContent>
            </Card>

            <Card className={activeFilter === "cutting" ? "border-primary" : ""}>
              <CardHeader className="pb-2">
                <div className="flex items-center gap-2">
                  <div className={`${activeFilter === "cutting" ? "bg-primary/20" : "bg-purple-100"} p-2 rounded-full`}>
                    <Scissors className={`h-5 w-5 ${activeFilter === "cutting" ? "text-primary" : "text-purple-600"}`} />
                  </div>
                  <CardTitle className="text-lg">Cutting Tasks</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">Manage food preparation tasks</p>
                <Button
                  className="mt-4 w-full"
                  variant={activeFilter === "cutting" ? "default" : "outline"}
                  onClick={() => handleFilterTasks("cutting")}
                >
                  View Tasks
                </Button>
              </CardContent>
            </Card>

            <Card className={activeFilter === "refilling" ? "border-primary" : ""}>
              <CardHeader className="pb-2">
                <div className="flex items-center gap-2">
                  <div className={`${activeFilter === "refilling" ? "bg-primary/20" : "bg-amber-100"} p-2 rounded-full`}>
                    <RefreshCw className={`h-5 w-5 ${activeFilter === "refilling" ? "text-primary" : "text-amber-600"}`} />
                  </div>
                  <CardTitle className="text-lg">Refilling Tasks</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">Manage inventory refilling tasks</p>
                <Button
                  className="mt-4 w-full"
                  variant={activeFilter === "refilling" ? "default" : "outline"}
                  onClick={() => handleFilterTasks("refilling")}
                >
                  View Tasks
                </Button>
              </CardContent>
            </Card>
          </div>

          <div className="mt-8">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium">
                {activeFilter === "all" ? "All Tasks" :
                 activeFilter === "cleaning" ? "Cleaning Tasks" :
                 activeFilter === "cutting" ? "Cutting Tasks" :
                 activeFilter === "refilling" ? "Refilling Tasks" : "Tasks"}
              </h3>
              <Button
                variant="outline"
                size="sm"
                onClick={loadRecentTasks}
                className="flex items-center gap-1"
              >
                <RefreshCcw className="h-3.5 w-3.5" /> Refresh
              </Button>
            </div>

            <div className="space-y-4">
              {activeFilter === "all" && (
                <div className="mb-4">
                  <h4 className="text-sm font-medium mb-2">Recent Tasks</h4>
                  <div className="space-y-3">
                    {recentTasks.length === 0 ? (
                      <div className="text-center py-4 text-muted-foreground text-sm">
                        No pending tasks found
                      </div>
                    ) : (
                      recentTasks.map((task) => {
                        const assignedStaff = task.assignedTo ? staffData.find(staff => staff.id === task.assignedTo) : null;
                        const completedByStaff = task.completedBy ? staffData.find(staff => staff.id === task.completedBy) : null;

                        return (
                          <div key={task.id} className="border rounded-md p-3">
                            <div className="flex justify-between items-start">
                              <div>
                                <div className="flex items-center gap-2">
                                  <h4 className="font-medium">{task.title}</h4>
                                  {task.status === "completed" && (
                                    <Badge variant="default" className="bg-green-500">Completed</Badge>
                                  )}
                                </div>
                                <p className="text-sm text-muted-foreground mt-1">{task.description}</p>
                                {assignedStaff && (
                                  <p className="text-xs text-muted-foreground mt-2">Assigned to: {assignedStaff.name}</p>
                                )}
                                {task.status === "completed" && completedByStaff && (
                                  <p className="text-xs text-green-600 mt-1">Completed by: {completedByStaff.name}</p>
                                )}
                              </div>
                              {task.status !== "completed" && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleCompleteTask(task.id)}
                                >
                                  <CheckCircle className="h-4 w-4 mr-2" /> Complete
                                </Button>
                              )}
                            </div>
                          </div>
                        );
                      })
                    )}
                  </div>
                </div>
              )}

              <div>
                <h4 className="text-sm font-medium mb-2">
                  {activeFilter === "all" ? "All Tasks" :
                   activeFilter === "cleaning" ? "Cleaning Tasks" :
                   activeFilter === "cutting" ? "Cutting Tasks" :
                   activeFilter === "refilling" ? "Refilling Tasks" : "Tasks"}
                </h4>
                <div className="space-y-3">
                  {filteredTasks.length === 0 ? (
                    <div className="text-center py-4 text-muted-foreground text-sm">
                      No tasks found
                    </div>
                  ) : (
                    filteredTasks.map((task) => {
                      const assignedStaff = task.assignedTo ? staffData.find(staff => staff.id === task.assignedTo) : null;
                      const completedByStaff = task.completedBy ? staffData.find(staff => staff.id === task.completedBy) : null;

                      return (
                        <div key={task.id} className="border rounded-md p-3">
                          <div className="flex justify-between items-start">
                            <div>
                              <div className="flex items-center gap-2">
                                <h4 className="font-medium">{task.title}</h4>
                                {task.status === "completed" ? (
                                  <Badge variant="default" className="bg-green-500">Completed</Badge>
                                ) : (
                                  <Badge variant="outline" className="border-amber-500 text-amber-500">Pending</Badge>
                                )}
                              </div>
                              <p className="text-sm text-muted-foreground mt-1">{task.description}</p>
                              <div className="flex flex-wrap gap-x-4 gap-y-1 mt-2">
                                <p className="text-xs text-muted-foreground">Category: {task.category || "General"}</p>
                                <p className="text-xs text-muted-foreground">Priority: {task.priority || "Medium"}</p>
                                {task.dueDate && (
                                  <p className="text-xs text-muted-foreground">Due: {new Date(task.dueDate).toLocaleDateString()}</p>
                                )}
                              </div>
                              {assignedStaff && (
                                <p className="text-xs text-muted-foreground mt-1">Assigned to: {assignedStaff.name}</p>
                              )}
                              {task.status === "completed" && completedByStaff && (
                                <p className="text-xs text-green-600 mt-1">Completed by: {completedByStaff.name}</p>
                              )}
                            </div>
                            {task.status !== "completed" && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleCompleteTask(task.id)}
                              >
                                <CheckCircle className="h-4 w-4 mr-2" /> Complete
                              </Button>
                            )}
                          </div>
                        </div>
                      );
                    })
                  )}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SimpleThingsToDoTab;
