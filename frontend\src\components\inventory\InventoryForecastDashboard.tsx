import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from "recharts";
import {
  TrendingUp,
  AlertTriangle,
  ShoppingCart,
  Package,
  DollarSign,
  Download
} from "lucide-react";
import { WeeklyInventoryForecast } from "@/types/inventoryForecast";
import { calculateInventoryForecast, getMockForecastData } from "@/services/inventoryForecastService";

interface InventoryForecastDashboardProps {
  currentInventory?: any[];
}

const InventoryForecastDashboard: React.FC<InventoryForecastDashboardProps> = ({
  currentInventory = []
}) => {
  const [forecastData, setForecastData] = useState<WeeklyInventoryForecast | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<"all" | "food" | "beverages" | "supplies">("all");

  useEffect(() => {
    const mockForecast = getMockForecastData();
    const forecast = calculateInventoryForecast(mockForecast, currentInventory);
    setForecastData(forecast);
  }, [currentInventory]);

  if (!forecastData) {
    return <div>Loading forecast data...</div>;
  }

  const getAlertBadgeVariant = (level: string) => {
    switch (level) {
      case "critical": return "destructive";
      case "reorder-soon": return "secondary";
      case "sufficient": return "default";
      default: return "outline";
    }
  };

  const filteredItems = selectedCategory === "all" 
    ? forecastData.items 
    : forecastData.items.filter(item => item.category === selectedCategory);

  const categoryData = [
    { name: "Food", value: forecastData.categoryForecasts.food.totalRecommendedPurchase, color: "#8884d8" },
    { name: "Beverages", value: forecastData.categoryForecasts.beverages.totalRecommendedPurchase, color: "#82ca9d" },
    { name: "Supplies", value: forecastData.categoryForecasts.supplies.totalRecommendedPurchase, color: "#ffc658" },
  ];

  return (
    <div className="space-y-6">
      {/* Header Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Weekly Revenue Forecast</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">£{forecastData.totalPredictedRevenue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              {forecastData.totalPredictedCustomers} predicted customers
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Items Needing Reorder</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {forecastData.items.filter(item => item.reorderQuantity > 0).length}
            </div>
            <p className="text-xs text-muted-foreground">
              {forecastData.items.filter(item => item.alertLevel === "critical").length} critical
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Purchase Needed</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              £{forecastData.items.reduce((sum, item) => sum + item.totalCostRecommended, 0).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              Across {forecastData.recommendedOrders.length} suppliers
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Forecast Confidence</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round(forecastData.items.reduce((sum, item) => sum + item.forecastConfidence, 0) / forecastData.items.length)}%
            </div>
            <p className="text-xs text-muted-foreground">
              Based on historical data
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Category Breakdown Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Purchase Recommendations by Category</CardTitle>
          <CardDescription>Recommended spending for upcoming week</CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={categoryData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, value }) => `${name}: £${value.toFixed(0)}`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {categoryData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip formatter={(value) => [`£${value}`, "Recommended Purchase"]} />
            </PieChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Detailed Items */}
      <Card>
        <CardHeader>
          <CardTitle>Inventory Items Forecast</CardTitle>
          <CardDescription>Detailed predictions for each inventory item</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredItems.slice(0, 10).map((item) => (
              <div key={item.id} className="border rounded-lg p-4 space-y-3">
                <div className="flex justify-between items-start">
                  <div>
                    <h4 className="font-medium">{item.name}</h4>
                    <p className="text-sm text-muted-foreground capitalize">{item.category}</p>
                  </div>
                  <Badge variant={getAlertBadgeVariant(item.alertLevel)}>
                    {item.alertLevel.replace("-", " ")}
                  </Badge>
                </div>
                
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <p className="text-muted-foreground">Current Stock</p>
                    <p className="font-medium">{item.currentStock} {item.unit}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Weekly Usage</p>
                    <p className="font-medium">{item.predictedUsageWeekly} {item.unit}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Days Until Stockout</p>
                    <p className="font-medium">{item.daysUntilStockout} days</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Reorder Quantity</p>
                    <p className="font-medium">{item.reorderQuantity} {item.unit}</p>
                  </div>
                </div>
                
                {item.reorderQuantity > 0 && (
                  <div className="bg-muted p-3 rounded">
                    <p className="text-sm">
                      <strong>Recommendation:</strong> Order {item.reorderQuantity} {item.unit} 
                      for £{item.totalCostRecommended.toFixed(2)} 
                      (Confidence: {item.forecastConfidence}%)
                    </p>
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default InventoryForecastDashboard;
