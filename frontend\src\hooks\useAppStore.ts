/**
 * Simplified application store hooks that integrate React Query with local state management
 * Provides a clean interface for all tab state management needs
 */

import { useState, useCallback, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import {
  useDashboardData,
  useAnalyticsData,
  useStaffData,
  useOrdersData,
  useRestaurantSettings,
  useNotificationsData,
  useNotificationStats,
  useUnreadNotifications,
  useCreateOrder,
  useUpdateOrder,
  useCreateStaffMember,
  useUpdateRestaurantSettings,
  useCreateNotification,
  useMarkNotificationsAsRead,
  useMarkAllNotificationsAsRead,
  useDeleteNotification,
  useDeleteAllNotifications,
  useStoreSync
} from './useStoreQueries';
import logger from '@/utils/logger';

// Dashboard Store Hook
export const useDashboardStore = () => {
  const { currentRestaurant } = useAuth();
  const [isRealTimeEnabled, setIsRealTimeEnabled] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(30000);
  const [alerts, setAlerts] = useState<any[]>([]);

  const { data: dashboardData, isLoading, error, refetch } = useDashboardData();

  const addAlert = useCallback((alert: any) => {
    const newAlert = {
      ...alert,
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      isRead: false,
    };
    setAlerts(prev => [newAlert, ...prev]);
  }, []);

  const removeAlert = useCallback((alertId: string) => {
    setAlerts(prev => prev.filter(alert => alert.id !== alertId));
  }, []);

  const clearAlerts = useCallback(() => {
    setAlerts([]);
  }, []);

  const toggleRealTime = useCallback(() => {
    setIsRealTimeEnabled(prev => !prev);
  }, []);

  return {
    // Data
    metrics: dashboardData?.metrics || {},
    recentOrders: dashboardData?.recentOrders || [],
    quickStats: dashboardData?.quickStats || [],
    alerts,
    isRealTimeEnabled,
    refreshInterval,
    
    // Loading states
    isLoading,
    error,
    
    // Actions
    refreshMetrics: refetch,
    addAlert,
    removeAlert,
    clearAlerts,
    toggleRealTime,
    setRefreshInterval,
  };
};

// Analytics Store Hook
export const useAnalyticsStore = () => {
  const [filters, setFilters] = useState({
    dateRange: {
      from: new Date(new Date().setDate(new Date().getDate() - 30)),
      to: new Date(),
    },
    period: 'daily',
    categories: [],
    metrics: ['revenue', 'orders', 'customers'],
    compareWithPrevious: false,
  });

  const [chartConfigs, setChartConfigs] = useState([
    { id: 'sales-chart', type: 'line', dataKey: 'sales', color: '#3b82f6', visible: true },
    { id: 'revenue-chart', type: 'bar', dataKey: 'revenue', color: '#10b981', visible: true },
    { id: 'customers-chart', type: 'area', dataKey: 'customers', color: '#f59e0b', visible: true },
  ]);

  const { data: analyticsData, isLoading, error, refetch } = useAnalyticsData(filters.dateRange, filters.period);

  const updateFilters = useCallback((newFilters: any) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  const updateChartConfig = useCallback((chartId: string, config: any) => {
    setChartConfigs(prev => 
      prev.map(chart => 
        chart.id === chartId ? { ...chart, ...config } : chart
      )
    );
  }, []);

  const exportData = useCallback(async (format: string) => {
    try {
      logger.info('Exporting analytics data', 'useAnalyticsStore', { format });
      
      const exportData = {
        filters,
        data: analyticsData,
        exported_at: new Date().toISOString(),
      };

      // Simple CSV export
      if (format.toLowerCase() === 'csv') {
        const csvContent = convertToCSV(exportData);
        downloadFile(csvContent, 'analytics-data.csv', 'text/csv');
      }
    } catch (error) {
      logger.error('Failed to export analytics data', 'useAnalyticsStore', { error, format });
      throw error;
    }
  }, [analyticsData, filters]);

  return {
    // Data
    data: analyticsData || {},
    filters,
    chartConfigs,
    
    // Loading states
    isLoading,
    error,
    
    // Actions
    refreshData: refetch,
    updateFilters,
    updateChartConfig,
    exportData,
  };
};

// Staff Store Hook
export const useStaffStore = () => {
  const [selectedMember, setSelectedMember] = useState<any>(null);
  const [scheduleView, setScheduleView] = useState<'daily' | 'weekly' | 'monthly'>('weekly');
  const [filters, setFilters] = useState({
    searchQuery: '',
    dateRange: { from: new Date(), to: new Date(new Date().setDate(new Date().getDate() + 7)) },
    categories: [],
    status: [],
  });

  const { data: staffMembers = [], isLoading, error, refetch } = useStaffData();
  const createStaffMutation = useCreateStaffMember();

  const activeStaff = staffMembers.filter((member: any) => 
    member.status === 'active' || member.status === 'on-break'
  );

  const addStaffMember = useCallback(async (memberData: any) => {
    try {
      await createStaffMutation.mutateAsync(memberData);
      logger.info('Staff member added successfully', 'useStaffStore', { name: memberData.name });
    } catch (error) {
      logger.error('Failed to add staff member', 'useStaffStore', { error });
      throw error;
    }
  }, [createStaffMutation]);

  const updateFilters = useCallback((newFilters: any) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  return {
    // Data
    members: staffMembers,
    activeStaff,
    selectedMember,
    scheduleView,
    filters,
    
    // Loading states
    isLoading,
    error,
    isCreating: createStaffMutation.isPending,
    
    // Actions
    refreshData: refetch,
    addStaffMember,
    setSelectedMember,
    setScheduleView,
    updateFilters,
  };
};

// Orders Store Hook
export const useOrdersStore = () => {
  const [selectedOrder, setSelectedOrder] = useState<any>(null);
  const [filters, setFilters] = useState({
    searchQuery: '',
    dateRange: { from: new Date(), to: new Date() },
    categories: [],
    status: ['pending', 'preparing', 'ready'],
  });

  const { data: activeOrders = [], isLoading: isLoadingActive, refetch: refetchActive } = useOrdersData('pending,preparing,ready');
  const { data: orderHistory = [], isLoading: isLoadingHistory, refetch: refetchHistory } = useOrdersData('served,paid,cancelled');
  
  const createOrderMutation = useCreateOrder();
  const updateOrderMutation = useUpdateOrder();

  const ordersQueue = activeOrders.filter((order: any) => 
    order.status === 'pending' || order.status === 'preparing'
  );

  const kitchenDisplay = activeOrders
    .filter((order: any) => order.status === 'pending' || order.status === 'preparing')
    .map((order: any) => ({
      orderId: order.id,
      tableId: order.tableId,
      items: order.items,
      priority: order.estimatedTime && order.estimatedTime < 15 ? 'high' : 'normal',
      estimatedTime: order.estimatedTime || 30,
      status: order.status === 'pending' ? 'pending' : 'preparing',
    }))
    .sort((a: any, b: any) => {
      if (a.priority !== b.priority) {
        return a.priority === 'high' ? -1 : 1;
      }
      return a.estimatedTime - b.estimatedTime;
    });

  const createOrder = useCallback(async (orderData: any) => {
    try {
      await createOrderMutation.mutateAsync(orderData);
      logger.info('Order created successfully', 'useOrdersStore', { tableId: orderData.tableId });
    } catch (error) {
      logger.error('Failed to create order', 'useOrdersStore', { error });
      throw error;
    }
  }, [createOrderMutation]);

  const updateOrder = useCallback(async (orderId: string, updates: any) => {
    try {
      await updateOrderMutation.mutateAsync({ orderId, updates });
      logger.info('Order updated successfully', 'useOrdersStore', { orderId });
    } catch (error) {
      logger.error('Failed to update order', 'useOrdersStore', { error });
      throw error;
    }
  }, [updateOrderMutation]);

  const updateFilters = useCallback((newFilters: any) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  return {
    // Data
    activeOrders,
    orderHistory,
    ordersQueue,
    kitchenDisplay,
    selectedOrder,
    filters,
    
    // Loading states
    isLoading: isLoadingActive || isLoadingHistory,
    isCreating: createOrderMutation.isPending,
    isUpdating: updateOrderMutation.isPending,
    
    // Actions
    refreshData: () => {
      refetchActive();
      refetchHistory();
    },
    createOrder,
    updateOrder,
    setSelectedOrder,
    updateFilters,
  };
};

// Settings Store Hook
export const useSettingsStore = () => {
  const [userPreferences, setUserPreferences] = useState({
    theme: 'system' as 'light' | 'dark' | 'system',
    language: 'en',
    notifications: {
      email: true,
      push: true,
      sms: false,
      inApp: true,
      types: ['orders', 'inventory', 'staff', 'alerts'],
    },
    dashboard: {
      layout: 'default',
      widgets: ['metrics', 'recent-orders', 'alerts', 'quick-stats'],
      refreshInterval: 30000,
      defaultView: 'overview',
    },
  });

  const [isDirty, setIsDirty] = useState(false);
  const [validationErrors, setValidationErrors] = useState<any[]>([]);

  const { data: restaurantSettings, isLoading, error, refetch } = useRestaurantSettings();
  const updateSettingsMutation = useUpdateRestaurantSettings();

  const updateUserPreferences = useCallback((updates: any) => {
    setUserPreferences(prev => ({ ...prev, ...updates }));
    setIsDirty(true);
    
    // Save to localStorage immediately
    const { currentRestaurant } = useAuth();
    if (currentRestaurant?.id) {
      localStorage.setItem(`userPreferences_${currentRestaurant.id}`, JSON.stringify({ ...userPreferences, ...updates }));
    }
  }, [userPreferences]);

  const updateRestaurantSettings = useCallback(async (updates: any) => {
    try {
      await updateSettingsMutation.mutateAsync(updates);
      setIsDirty(false);
      setValidationErrors([]);
      logger.info('Restaurant settings updated successfully', 'useSettingsStore');
    } catch (error) {
      logger.error('Failed to update restaurant settings', 'useSettingsStore', { error });
      throw error;
    }
  }, [updateSettingsMutation]);

  const validateSettings = useCallback(() => {
    const errors: any[] = [];
    
    if (!restaurantSettings?.name?.trim()) {
      errors.push({ field: 'name', message: 'Restaurant name is required', code: 'REQUIRED' });
    }
    
    if (!restaurantSettings?.email?.trim()) {
      errors.push({ field: 'email', message: 'Email is required', code: 'REQUIRED' });
    }
    
    setValidationErrors(errors);
    return errors;
  }, [restaurantSettings]);

  return {
    // Data
    restaurantSettings: restaurantSettings || {},
    userPreferences,
    isDirty,
    validationErrors,
    
    // Loading states
    isLoading,
    error,
    isSaving: updateSettingsMutation.isPending,
    
    // Actions
    refreshData: refetch,
    updateUserPreferences,
    updateRestaurantSettings,
    validateSettings,
  };
};

// Global Store Hook
export const useAppStore = () => {
  const { currentRestaurant } = useAuth();
  const { syncAllData } = useStoreSync();
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null);

  const syncWithBackend = useCallback(async () => {
    try {
      await syncAllData();
      setLastSyncTime(new Date());
      logger.info('Backend sync completed', 'useAppStore');
    } catch (error) {
      logger.error('Backend sync failed', 'useAppStore', { error });
      throw error;
    }
  }, [syncAllData]);

  return {
    currentRestaurantId: currentRestaurant?.id || null,
    lastSyncTime,
    syncWithBackend,
  };
};

// Notification Store Hook
export const useNotificationStore = () => {
  const { currentRestaurant } = useAuth();
  const [filters, setFilters] = useState({
    type: undefined as string | undefined,
    priority: undefined as string | undefined,
    is_read: undefined as boolean | undefined,
    limit: 50,
  });

  // Queries
  const {
    data: notifications = [],
    isLoading: notificationsLoading,
    error: notificationsError,
    refetch: refetchNotifications
  } = useNotificationsData(filters);

  const {
    data: notificationStats,
    isLoading: statsLoading,
    error: statsError,
    refetch: refetchStats
  } = useNotificationStats();

  const {
    data: unreadNotifications = [],
    isLoading: unreadLoading,
    error: unreadError,
    refetch: refetchUnread
  } = useUnreadNotifications();

  // Mutations
  const createNotificationMutation = useCreateNotification();
  const markAsReadMutation = useMarkNotificationsAsRead();
  const markAllAsReadMutation = useMarkAllNotificationsAsRead();
  const deleteNotificationMutation = useDeleteNotification();
  const deleteAllNotificationsMutation = useDeleteAllNotifications();

  // Actions
  const createNotification = useCallback(async (notificationData: {
    title: string;
    message: string;
    type?: string;
    priority?: string;
    link?: string;
    relatedId?: string;
  }) => {
    try {
      logger.info('Creating notification', 'useNotificationStore', { notificationData });
      await createNotificationMutation.mutateAsync(notificationData);
      logger.info('Notification created successfully', 'useNotificationStore');
    } catch (error) {
      logger.error('Failed to create notification', 'useNotificationStore', { error });
      throw error;
    }
  }, [createNotificationMutation]);

  const markAsRead = useCallback(async (notificationIds: string[]) => {
    try {
      logger.info('Marking notifications as read', 'useNotificationStore', { notificationIds });
      await markAsReadMutation.mutateAsync(notificationIds);
      logger.info('Notifications marked as read successfully', 'useNotificationStore');
    } catch (error) {
      logger.error('Failed to mark notifications as read', 'useNotificationStore', { error });
      throw error;
    }
  }, [markAsReadMutation]);

  const markAllAsRead = useCallback(async () => {
    try {
      logger.info('Marking all notifications as read', 'useNotificationStore');
      await markAllAsReadMutation.mutateAsync();
      logger.info('All notifications marked as read successfully', 'useNotificationStore');
    } catch (error) {
      logger.error('Failed to mark all notifications as read', 'useNotificationStore', { error });
      throw error;
    }
  }, [markAllAsReadMutation]);

  const deleteNotification = useCallback(async (notificationId: string) => {
    try {
      logger.info('Deleting notification', 'useNotificationStore', { notificationId });
      await deleteNotificationMutation.mutateAsync(notificationId);
      logger.info('Notification deleted successfully', 'useNotificationStore');
    } catch (error) {
      logger.error('Failed to delete notification', 'useNotificationStore', { error });
      throw error;
    }
  }, [deleteNotificationMutation]);

  const deleteAllNotifications = useCallback(async () => {
    try {
      logger.info('Deleting all notifications', 'useNotificationStore');
      await deleteAllNotificationsMutation.mutateAsync();
      logger.info('All notifications deleted successfully', 'useNotificationStore');
    } catch (error) {
      logger.error('Failed to delete all notifications', 'useNotificationStore', { error });
      throw error;
    }
  }, [deleteAllNotificationsMutation]);

  const updateFilters = useCallback((newFilters: any) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    logger.debug('Updated notification filters', 'useNotificationStore', { newFilters });
  }, []);

  const refreshNotifications = useCallback(async () => {
    try {
      logger.info('Refreshing notifications', 'useNotificationStore');
      await Promise.all([
        refetchNotifications(),
        refetchStats(),
        refetchUnread(),
      ]);
      logger.info('Notifications refreshed successfully', 'useNotificationStore');
    } catch (error) {
      logger.error('Failed to refresh notifications', 'useNotificationStore', { error });
      throw error;
    }
  }, [refetchNotifications, refetchStats, refetchUnread]);

  // Computed values
  const unreadCount = notificationStats?.unread_count || 0;
  const totalCount = notificationStats?.total_count || 0;
  const isLoading = notificationsLoading || statsLoading || unreadLoading;
  const error = notificationsError || statsError || unreadError;

  const isCreating = createNotificationMutation.isPending;
  const isUpdating = markAsReadMutation.isPending || markAllAsReadMutation.isPending;
  const isDeleting = deleteNotificationMutation.isPending || deleteAllNotificationsMutation.isPending;

  return {
    // Data
    notifications,
    unreadNotifications,
    notificationStats,
    unreadCount,
    totalCount,
    filters,

    // Loading states
    isLoading,
    isCreating,
    isUpdating,
    isDeleting,
    error,

    // Actions
    createNotification,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    deleteAllNotifications,
    updateFilters,
    refreshNotifications,
  };
};

// Helper functions
function convertToCSV(data: any): string {
  const headers = ['Date', 'Sales', 'Revenue', 'Orders', 'Customers'];
  const rows = data.data?.salesData?.map((item: any) => [
    item.date,
    item.sales,
    item.revenue || 0,
    item.orders,
    item.customers || 0,
  ]) || [];

  return [headers, ...rows].map(row => row.join(',')).join('\n');
}

function downloadFile(content: string, filename: string, mimeType: string): void {
  const blob = new Blob([content], { type: mimeType });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
}
