// Define notification types

export type NotificationType = 
  | 'info' 
  | 'success' 
  | 'warning' 
  | 'error' 
  | 'inventory' 
  | 'staff' 
  | 'order' 
  | 'reservation';

export type NotificationPriority = 'low' | 'medium' | 'high';

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: NotificationType;
  priority: NotificationPriority;
  isRead: boolean;
  timestamp: string; // ISO string
  link?: string; // Optional link to navigate to
  relatedId?: string; // Optional ID of related entity (order, reservation, etc.)
}
