import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { ChefHat, BarChart3, Calendar, Users, ShoppingBasket, Settings, Star } from "lucide-react";

const Home = () => {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <header className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white">
        <div className="container mx-auto px-4 py-16 md:py-24">
          <nav className="flex justify-between items-center mb-16">
            <div className="flex items-center gap-2">
              <ChefHat size={30} className="text-white" />
              <span className="text-xl font-bold">Promith</span>
            </div>
            <div className="hidden md:flex items-center gap-6">
              <Link to="/" className="text-white hover:text-white/80">Home</Link>
              <Link to="/features" className="text-white hover:text-white/80">Features</Link>
              <Link to="/pricing" className="text-white hover:text-white/80">Pricing</Link>
              <Link to="/contact" className="text-white hover:text-white/80">Contact</Link>
            </div>
            <div className="flex items-center gap-4">
              <Link to="/login">
                <Button variant="outline" className="text-white border-white hover:bg-white hover:text-blue-600">
                  Login
                </Button>
              </Link>
              <Link to="/register">
                <Button className="bg-white text-blue-600 hover:bg-white/90">
                  Get Started
                </Button>
              </Link>
            </div>
          </nav>

          <div className="flex flex-col md:flex-row items-center gap-12">
            <div className="md:w-1/2 space-y-6">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight">
                Streamline Your Restaurant Management
              </h1>
              <p className="text-xl text-white/80">
                All-in-one platform to manage staff, inventory, analytics, and more. Boost efficiency and grow your restaurant business.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 pt-4">
                <Link to="/register">
                  <Button size="lg" className="bg-white text-blue-600 hover:bg-white/90 w-full sm:w-auto">
                    Start Free Trial
                  </Button>
                </Link>
                <Link to="/demo">
                  <Button size="lg" variant="outline" className="text-white border-white hover:bg-white/10 w-full sm:w-auto">
                    Request Demo
                  </Button>
                </Link>
              </div>
            </div>
            <div className="md:w-1/2">
              <img 
                src="/images/dashboard-preview.png" 
                alt="Promith Dashboard" 
                className="rounded-lg shadow-2xl"
                onError={(e) => {
                  e.currentTarget.src = "https://placehold.co/600x400/3b82f6/ffffff?text=Promith+Dashboard";
                }}
              />
            </div>
          </div>
        </div>
      </header>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Powerful Features for Modern Restaurants</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Everything you need to manage your restaurant efficiently in one place
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <FeatureCard 
              icon={<BarChart3 size={24} className="text-blue-600" />}
              title="Real-time Analytics"
              description="Track sales, customer trends, and performance metrics with intuitive dashboards and reports."
            />
            <FeatureCard 
              icon={<Users size={24} className="text-blue-600" />}
              title="Staff Management"
              description="Schedule shifts, track performance, and manage your team efficiently."
            />
            <FeatureCard 
              icon={<ShoppingBasket size={24} className="text-blue-600" />}
              title="Inventory Control"
              description="Monitor stock levels, track usage, and get alerts when items need to be reordered."
            />
            <FeatureCard 
              icon={<Calendar size={24} className="text-blue-600" />}
              title="Reservation System"
              description="Manage table bookings, send confirmations, and reduce no-shows."
            />
            <FeatureCard 
              icon={<Star size={24} className="text-blue-600" />}
              title="Customer Feedback"
              description="Collect and analyze customer reviews to improve your service quality."
            />
            <FeatureCard 
              icon={<Settings size={24} className="text-blue-600" />}
              title="Customizable Settings"
              description="Tailor the system to your restaurant's specific needs and workflows."
            />
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Trusted by Restaurant Owners</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              See what our customers have to say about Promith
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <TestimonialCard 
              quote="Promith has transformed how we run our restaurant. Staff scheduling is now a breeze, and the analytics help us make better business decisions."
              author="Michael Rodriguez"
              position="Owner, The Gourmet Kitchen"
            />
            <TestimonialCard 
              quote="The inventory management feature alone has saved us thousands of dollars by reducing waste and optimizing our ordering process."
              author="Jennifer Smith"
              position="Manager, Coastal Flavors"
            />
            <TestimonialCard 
              quote="As a new restaurant owner, this platform has been invaluable. It's like having a business consultant and operations manager in one tool."
              author="David Chen"
              position="Owner, Fusion Bistro"
            />
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Simple, Transparent Pricing</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Choose the plan that fits your restaurant's needs
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            <PricingCard 
              title="Starter"
              price="$49"
              description="Perfect for small cafes and food trucks"
              features={[
                "Staff management",
                "Basic analytics",
                "Inventory tracking",
                "Email support",
                "1 location"
              ]}
              buttonText="Start Free Trial"
              buttonLink="/register"
              highlighted={false}
            />
            <PricingCard 
              title="Professional"
              price="$99"
              description="Ideal for established restaurants"
              features={[
                "Everything in Starter",
                "Advanced analytics",
                "Reservation system",
                "Customer feedback tools",
                "Priority support",
                "3 locations"
              ]}
              buttonText="Start Free Trial"
              buttonLink="/register"
              highlighted={true}
            />
            <PricingCard 
              title="Enterprise"
              price="$199"
              description="For restaurant groups and chains"
              features={[
                "Everything in Professional",
                "Custom reporting",
                "API access",
                "Dedicated account manager",
                "Staff training",
                "Unlimited locations"
              ]}
              buttonText="Contact Sales"
              buttonLink="/contact"
              highlighted={false}
            />
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-blue-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">Ready to Transform Your Restaurant?</h2>
          <p className="text-xl text-white/80 max-w-3xl mx-auto mb-8">
            Join thousands of restaurant owners who have streamlined their operations with Promith
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/register">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-white/90 w-full sm:w-auto">
                Start Free Trial
              </Button>
            </Link>
            <Link to="/demo">
              <Button size="lg" variant="outline" className="text-white border-white hover:bg-white/10 w-full sm:w-auto">
                Request Demo
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center gap-2 mb-4">
                <ChefHat size={24} className="text-blue-400" />
                <span className="text-lg font-bold">Promith</span>
              </div>
              <p className="text-gray-400">
                The all-in-one restaurant management platform that helps you streamline operations and grow your business.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">Product</h3>
              <ul className="space-y-2">
                <li><Link to="/features" className="text-gray-400 hover:text-white">Features</Link></li>
                <li><Link to="/pricing" className="text-gray-400 hover:text-white">Pricing</Link></li>
                <li><Link to="/demo" className="text-gray-400 hover:text-white">Request Demo</Link></li>
                <li><Link to="/login" className="text-gray-400 hover:text-white">Login</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">Company</h3>
              <ul className="space-y-2">
                <li><Link to="/about" className="text-gray-400 hover:text-white">About Us</Link></li>
                <li><Link to="/contact" className="text-gray-400 hover:text-white">Contact</Link></li>
                <li><Link to="/careers" className="text-gray-400 hover:text-white">Careers</Link></li>
                <li><Link to="/blog" className="text-gray-400 hover:text-white">Blog</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">Legal</h3>
              <ul className="space-y-2">
                <li><Link to="/terms" className="text-gray-400 hover:text-white">Terms of Service</Link></li>
                <li><Link to="/privacy" className="text-gray-400 hover:text-white">Privacy Policy</Link></li>
                <li><Link to="/cookies" className="text-gray-400 hover:text-white">Cookie Policy</Link></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-12 pt-8 text-center text-gray-400">
            <p>&copy; {new Date().getFullYear()} Promith. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

// Helper Components
const FeatureCard = ({ icon, title, description }: { icon: React.ReactNode, title: string, description: string }) => {
  return (
    <div className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow">
      <div className="bg-blue-50 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
        {icon}
      </div>
      <h3 className="text-xl font-semibold mb-2">{title}</h3>
      <p className="text-gray-600">{description}</p>
    </div>
  );
};

const TestimonialCard = ({ quote, author, position }: { quote: string, author: string, position: string }) => {
  return (
    <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100">
      <div className="flex mb-4">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star key={star} size={16} className="text-yellow-400 fill-yellow-400" />
        ))}
      </div>
      <p className="text-gray-700 mb-4">"{quote}"</p>
      <div>
        <p className="font-semibold">{author}</p>
        <p className="text-gray-500 text-sm">{position}</p>
      </div>
    </div>
  );
};

const PricingCard = ({ 
  title, 
  price, 
  description, 
  features, 
  buttonText, 
  buttonLink, 
  highlighted 
}: { 
  title: string, 
  price: string, 
  description: string, 
  features: string[], 
  buttonText: string, 
  buttonLink: string, 
  highlighted: boolean 
}) => {
  return (
    <div className={`rounded-lg overflow-hidden ${highlighted ? 'ring-2 ring-blue-600 shadow-lg scale-105' : 'border border-gray-200 shadow'}`}>
      <div className={`p-6 ${highlighted ? 'bg-blue-600 text-white' : 'bg-white'}`}>
        <h3 className="text-xl font-bold mb-1">{title}</h3>
        <div className="flex items-baseline mb-2">
          <span className="text-3xl font-bold">{price}</span>
          <span className={`ml-1 text-sm ${highlighted ? 'text-white/80' : 'text-gray-500'}`}>/month</span>
        </div>
        <p className={highlighted ? 'text-white/80' : 'text-gray-600'}>{description}</p>
      </div>
      <div className="p-6 bg-card">
        <ul className="space-y-3 mb-6">
          {features.map((feature, index) => (
            <li key={index} className="flex items-center">
              <svg className="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <span className="text-card-foreground">{feature}</span>
            </li>
          ))}
        </ul>
        <Link to={buttonLink}>
          <Button 
            className={`w-full ${highlighted ? 'bg-blue-600 hover:bg-blue-700' : 'bg-gray-800 hover:bg-gray-900'}`}
          >
            {buttonText}
          </Button>
        </Link>
      </div>
    </div>
  );
};

export default Home;
