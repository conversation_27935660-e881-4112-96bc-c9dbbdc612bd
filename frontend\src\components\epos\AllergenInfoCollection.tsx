import React, { useState } from "react";
import { <PERSON><PERSON>, Di<PERSON><PERSON>ontent, Di<PERSON><PERSON>eader, Di<PERSON>Title, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { AlertTriangle } from "lucide-react";

interface AllergenInfoCollectionProps {
  isOpen: boolean;
  tableNumber: number;
  onConfirm: (hasAllergens: boolean) => void;
  onBack?: () => void;
  onClose?: () => void;
}

const AllergenInfoCollection: React.FC<AllergenInfoCollectionProps> = ({
  isOpen,
  tableNumber,
  onConfirm,
  onBack,
  onClose
}) => {
  const handleOpenChange = (open: boolean) => {
    if (!open && onClose) {
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-md [&>button]:hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="w-5 h-5 text-orange-500" />
            Allergen Information - Table {tableNumber}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            Are there any food allergies or dietary restrictions for this table?
          </p>

          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
            <div className="flex items-start gap-2">
              <AlertTriangle className="w-4 h-4 text-orange-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-orange-800">
                <p className="font-medium">Important:</p>
                <p>Please inform us of any allergies or dietary restrictions to ensure safe food preparation.</p>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="flex flex-col sm:flex-row gap-2">
          {onBack && (
            <Button variant="outline" onClick={onBack}>
              Back
            </Button>
          )}
          <Button
            variant="outline"
            className="flex-1"
            onClick={() => onConfirm(false)}
          >
            No Allergies
          </Button>
          <Button
            className="flex-1 bg-orange-600 hover:bg-orange-700"
            onClick={() => onConfirm(true)}
          >
            Yes, Has Allergies
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AllergenInfoCollection;
